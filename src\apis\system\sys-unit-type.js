import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysUnitType = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types', data, params})
export const updateSysUnitType = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types', data, params})
export const deleteSysUnitType = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types', params})
export const listSysUnitType = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types', params})
export const listSysUnitTypeSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types/selections', params})
export const pageSysUnitType = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types/page', params})
export const getSysUnitType = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_unit_types/' + id})
