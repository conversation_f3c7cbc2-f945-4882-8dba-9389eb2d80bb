import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysPrivilegeFunction = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions', data, params})
export const updateSysPrivilegeFunction = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions', data, params})
export const deleteSysPrivilegeFunction = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions', params})
export const listSysPrivilegeFunction = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions', params})
export const listSysPrivilegeFunctionSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions/selections', params})
export const pageSysPrivilegeFunction = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions/page', params})
export const getSysPrivilegeFunction = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_functions/' + id})
