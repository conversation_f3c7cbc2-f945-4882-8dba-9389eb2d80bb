<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    class="info-modal"
    @close="handleClose"
  >
    <div class="modal-content">
      <div class="info-list">
        <div 
          v-for="(item, index) in infoList" 
          :key="index" 
          class="info-item"
        >
          <span class="info-label">{{ item.label }}：</span>
          <span class="info-value">{{ item.value }}</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="modal-footer">
        <el-button 
          v-if="showCancelButton"
          @click="handleCancel"
          class="cancel-btn"
        >
          {{ cancelButtonText }}
        </el-button>
        <el-button 
          v-if="showConfirmButton"
          type="primary" 
          @click="handleConfirm"
          class="confirm-btn"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'InfoModal',
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: '提示'
    },
    // 弹窗宽度
    width: {
      type: String,
      default: '500px'
    },
    // 信息列表，格式：[{label: '标签', value: '值'}]
    infoList: {
      type: Array,
      default: () => []
    },
    // 是否显示取消按钮
    showCancelButton: {
      type: Boolean,
      default: true
    },
    // 是否显示确认按钮
    showConfirmButton: {
      type: Boolean,
      default: false
    },
    // 取消按钮文本
    cancelButtonText: {
      type: String,
      default: '关闭'
    },
    // 确认按钮文本
    confirmButtonText: {
      type: String,
      default: '确定'
    }
  },
  data() {
    return {
      visible: false
    }
  },
  methods: {
    // 显示弹窗
    show() {
      this.visible = true
    },
    // 隐藏弹窗
    hide() {
      this.visible = false
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
      this.hide()
    },
    // 取消按钮点击
    handleCancel() {
      this.$emit('cancel')
      this.hide()
    },
    // 确认按钮点击
    handleConfirm() {
      this.$emit('confirm')
      this.hide()
    }
  }
}
</script>

<style scoped lang="scss">
.info-modal {
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    .el-dialog__header {
      padding: 20px 24px 0;
      border-bottom: none;
      
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 20px;
        
        .el-dialog__close {
          font-size: 18px;
          color: #909399;
          
          &:hover {
            color: #409eff;
          }
        }
      }
    }
    
    .el-dialog__body {
      padding: 20px 24px;
    }
    
    .el-dialog__footer {
      padding: 0 24px 20px;
      border-top: none;
    }
  }
  
  .modal-content {
    .info-list {
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        line-height: 1.5;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-label {
          flex-shrink: 0;
          width: 80px;
          font-weight: 600;
          color: #606266;
          text-align: left;
        }
        
        .info-value {
          flex: 1;
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }
  
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .cancel-btn {
      border: 1px solid #dcdfe6;
      background-color: #ffffff;
      color: #606266;
      
      &:hover {
        border-color: #c0c4cc;
        color: #606266;
      }
    }
    
    .confirm-btn {
      background-color: #409eff;
      border-color: #409eff;
      
      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
    }
  }
}
</style> 