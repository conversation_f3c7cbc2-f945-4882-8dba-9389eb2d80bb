<template>
  <div class="ltw-page-container">
    <el-tabs v-model="queryParam.accessRoleType" @tab-click="handleClick" style="position: relative">
      <el-tab-pane :label="$t(item.name)" :name="item.code" v-for="item in tabsData" :key="item.id"> </el-tab-pane>
    </el-tabs>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
        </div>
      </div>
      <el-table :data="pageData.records" stripe row-key="id" ref="tableRef">
        <el-table-column header-align="left" align="left" prop="code" :label="$t('编码')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="name" :label="$t('名称')"></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="acquisitionType"
          :label="$t('采集类型')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="updatable" :label="$t('标签')">
          <template #default="scope">
            <el-link :underline="false" type="primary" @click="getTags(scope.row)" v-if="scope.row.tagAmount > 0">{{
              scope.row.tagAmount
            }}</el-link>
            <el-link :underline="false" type="primary" v-else>0</el-link>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('创建者')"
          prop="ownerEmpName"
          width="120"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('工作组')"
          prop="accessRoleName"
          v-if="queryParam.accessRoleType === 'workgroup'"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('部门')"
          prop="accessRoleName"
          v-if="queryParam.accessRoleType === 'dept'"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('公司')"
          prop="accessRoleName"
          v-if="queryParam.accessRoleType === 'org'"
        ></el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="300">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
              <!--              <el-tooltip-->
              <!--                  effect="dark"-->
              <!--                  :content="$t(dataOperatePermission['copy']?.name)"-->
              <!--                  placement="top"-->
              <!--                  :enterable="false"-->
              <!--              >-->
              <!--                <el-button-->
              <!--                    :type="dataOperatePermission['copy'].buttonStyleType"-->
              <!--                    @click="executeButtonMethod(dataOperatePermission['copy'], scope.row)"-->
              <!--                >-->
              <!--                  <ltw-icon :icon-code="dataOperatePermission['copy'].buttonIconCode"></ltw-icon>-->
              <!--                </el-button>-->
              <!--              </el-tooltip>-->
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <!-- <tag-list ref="TagList" /> -->
    <el-dialog v-model="tagCardVisible">
      <bs-tag-group-panel
        :continuous-units="continuousUnits"
        :edit-tags="false"
        :timeEdit="false"
        :data="tagsData"
        @tagClose="tagClose"
        @tagSave="tagSave"
        :classificationTag="true"
      ></bs-tag-group-panel>
    </el-dialog>
    <tag-classification-form
      ref="formRef"
      :status="dialogStatus"
      :formReadonly="formReadonly"
      :treeTagGroupList="treeTagGroupList"
      v-model="formData"
      @save="refresh"
    ></tag-classification-form>
  </div>
  <!-- share -->
  <share-dialog
    :accessRoleType="queryParam.accessRoleType"
    type="tag"
    @share-confirm="query"
    ref="shareRef"
  ></share-dialog>
</template>

<script>
import {
  saveBsTagClassification,
  updateBsTagClassification,
  deleteBsTagClassification,
  pageBsTagClassification,
  getBsTagClassification,
  getTagList,
  share,
  pageQuery,
  publish
} from '@/apis/data-collect/bs-tag-classification'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { getCurrentUser } from '@/apis/base'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'

import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import TagList from '@/pages/dataCollect/dialog/TagList'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import ShareDialog from '@/pages/dataCollect/dialog/ShareDialog.vue'
import TagClassificationForm from '@/pages/dataCollect/dialog/TagClassificationForm'
import { cloneDeep } from 'lodash'

const DEFAULT_FORM_DATA = {
  code: '',
  name: '',
  acquisitionType: '',
  description: '',
  isParent: false,
  tagList: [],
  childTagGroupList: []
}
export default {
  name: 'BsTagClassification',
  components: { TagList, BsTagGroupPanel, ShareDialog, TagClassificationForm },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: cloneDeep(DEFAULT_FORM_DATA),

      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},
      continuousUnits: [],
      tagsData: [],
      tagCardVisible: false,
      tabsData: [],
      currentUser: '',
      shareData: {
        shareIsKeep: true,
        updatable: true
      },
      treeTagGroupList: []
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.getAccessRoleType()
    this.getCurrentUser()
    this.treeListTagGroup()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    getAccessRoleType() {
      listSysDictionary({ typeCode: 'dm_dataset_access_role_type' }).then(res => {
        this.tabsData = res.data || []
        this.queryParam.accessRoleType = res.data && res.data[0].code
        this.refresh()
      })
    },
    getCurrentUser() {
      getCurrentUser().then(res => {
        this.currentUser = res.data?.empId
      })
    },
    treeListTagGroup() {
      treeListBsTagGroup().then(res => {
        this.treeTagGroupList = res.data
      })
    },
    handleClick(tab) {
      this.$nextTick(() => {
        this.refresh()
      })
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.queryParam.current = 1
      this.query()
    },
    query() {
      pageQuery(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    getGroupArray(list, attr) {
      const map = new Map()
      list.forEach((item, index, arr) => {
        if (!map.has(item[attr])) {
          map.set(
            item[attr],
            arr.filter(a => a[attr] == item[attr])
          )
        }
      })
      return Array.from(map).map(item => [...item[1]])
    },
    getTags(row) {
      this.tagsData = []
      getTagList(row.id).then(res => {
        let tagList = res.data
          .map(item => item.tagList)
          .flat(Infinity)
          .map(tag => {
            return {
              ...tag,
              groupId: tag.isParent ? `${tag.childCode}-${tag.childName}` : tag.groupId,
              groupName: tag.isParent ? tag.childName : tag.groupName,
              groupNameCn: tag.isParent ? tag.childName : tag.groupNameCn,
              groupCode: tag.isParent ? tag.childCode : tag.groupCode
            }
          })
        const tagGroupMap = this.groupBy(tagList, 'groupId')
        this.tagsData = this.setCheckedTag(tagGroupMap, row)
        this.tagCardVisible = true
      })
    },
    groupBy(array, prop) {
      return array.reduce((cur, pre) => {
        let key = pre[prop]
        if (!cur[key]) {
          cur[key] = []
        }
        cur[key].push(pre)
        return cur
      }, {})
    },
    getParentNode(tree, childId) {
      let parentInfo
      for (let node of tree) {
        // 如果当前节点就是目标节点的父节点，直接返回当前节点
        if (node.children && node.children.some(child => child.id === childId)) {
          return node.nameCn
        }
        // 否则继续遍历当前节点的子节点
        if (node.children) {
          parentInfo = this.getParentNode(node.children, childId)
          if (parentInfo !== null) {
            return parentInfo
          }
        }
      }
      return null
    },
    setCheckedTag(tagGroupMap, row) {
      let groupArr = [
        {
          nameCn: row.name,
          id: row.id,
          children: []
        }
      ]
      for (let item in tagGroupMap) {
        groupArr[0].children.push({
          nameCn: tagGroupMap[item][0].groupNameCn,
          id: item,
          tagList: tagGroupMap[item],
          asLeaf: true
        })
      }
      return groupArr
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.$refs.formRef.show({
        editTags: true,
        timeEdit: true
      })
    },
    initChildTagGroupList(data) {
      const tempMap = {}
      data.tagList.forEach(tag => {
        const key = `${tag.childCode}-${tag.childName}`
        if (!tempMap[key]) {
          tempMap[key] = {
            code: tag.childCode,
            name: tag.childName,
            tagList: []
          }
        }
        tempMap[key].tagList.push(tag)
      })
      data.childTagGroupList = Object.values(tempMap) || []
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      if (row.ownerEmpId === this.currentUser || row.updatable === true) {
        getBsTagClassification(row.id).then(res => {
          this.$nextTick(function () {
            this.initChildTagGroupList(res.data)
            this.formData = res.data
          })
          this.$refs.formRef.show()
        })
      } else {
        showToast('当前用户无编辑该数据权限', 'warning')
        return
      }
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getBsTagClassification(row.id).then(res => {
        this.$nextTick(function () {
          this.initChildTagGroupList(res.data)
          this.formData = res.data
        })
        this.$refs.formRef.show()
      })
    },
    share(row) {
      this.$refs.shareRef.show({
        title: '分享tag分组',
        type: 'tag',
        form: {
          id: row.id,
          shareIsKeep: true,
          updatable: true
        }
      })
    },
    publish(row) {
      publish(row.id).then(() => {
        this.query()
        showToast(this.$t('发布成功', 'success'))
      })
    },
    shareConfirm(data) {
      share(data).then(() => {
        this.query()
        showToast(this.$t('分享成功', 'success'))
      })
    },
    copy(row) {
      let transfer = document.createElement('input')
      document.body.appendChild(transfer)
      transfer.value = row.id
      transfer.focus()
      transfer.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
      }
      transfer.blur()
      showToast('复制成功', 'success')
      document.body.removeChild(transfer)
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      if (row.ownerEmpId === this.currentUser || row.updatable === true) {
        this.remove({ id: row.id })
      } else {
        showToast('当前用户无删除该数据权限', 'warning')
        return
      }
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteBsTagClassification(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    tagSave() {},
    tagClose() {}
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
</style>
