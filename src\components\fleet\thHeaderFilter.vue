<template>
  <!-- <divstyle="display: inline-block"> -->
  <el-popover
    placement="bottom"
    :width="width"
    trigger="click"
    ref="popoverRef"
    @hide="tableFilter"
  >
    <div v-if="filterType === 'text'">
      <el-input
        v-model="conditions.text"
        size="small"
        clearable
        :placeholder="$t('请输入')"
        @click.stop="getInputClick"
      />
    </div>
    <div v-else-if="filterType === 'date'">
      <el-date-picker
        v-model="conditions.date"
        type="date"
        clearable
        size="small"
        placeholder="开始时间"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
      />
    </div>
    <div v-else-if="filterType === 'dateRange'">
      <el-date-picker
        v-model="conditions.dateRange"
        type="daterange"
        :start-placeholder="$t('开始日期')"
        :end-placeholder="$t('结束日期')"
        range-separator="To"
        size="small"
      />
    </div>
    <div v-else-if="filterType === 'dateTime'">
      <el-date-picker
        v-model="conditions.dateTime"
        type="datetime"
        clearable
        size="small"
        placeholder="开始时间"
        value-format="yyyy-MM-dd"
      />
    </div>
    <div v-else-if="filterType === 'dateTimeRange'">
      <el-date-picker
        v-model="conditions.dateTimeRange"
        type="datetimerange"
        :start-placeholder="$t('开始日期')"
        :end-placeholder="$t('结束日期')"
        range-separator="To"
      />
    </div>
    <div v-else>
      <el-select
        v-model="conditions.select"
        :placeholder="$t('请选择')"
        :multiple="isMulti"
        filterable
        clearable
        collapse-tags
        collapse-tags-tooltip
        :teleported="false"
      >
        <el-option
          v-for="(item, index) in customArrList"
          :key="index"
          :label="item.name || item.code"
          :value="isId ? item.id : item.code"
        />
      </el-select>
    </div>
    <div v-if="['select','input'].includes(filterType)">
      <el-button
        type="primary"
        size="small"
        @click="confirm"
        style="margin-top: 5px"
        >{{ $t('筛选') }}</el-button
      >
      <el-button size="small" @click="reset" style="margin-top: 5px">{{
        $t('重置')
      }}</el-button>
    </div>
    <template #reference>
      <template class="table-header" :class="labelColor">
        {{ $t(fieldName) }}
        <ltw-icon
          icon-code="el-icon-search"
          v-if="filterType === 'text'"
        ></ltw-icon>
        <ltw-icon icon-code="el-icon-arrow-down" v-else></ltw-icon>
      </template>
    </template>
  </el-popover>
</template>

<script>
import ClickOutside from '@/directives/clickOutside'
const defaultForm = {}
export default {
  name: 'thHeaderFilter',
  data() {
    return {
      value: '',
      visible: false,
      conditions: {
        select: undefined
      },
      lastTarget: undefined,
      currentTarget: undefined
    }
  },
  props: {
    width:{
      type:Number,
      default:200
    },
    column: {
      type: [String, Object],
      default: null
    },
    fieldName: {
      type: String,
      defalut: ''
    },
    filterType: {
      type: String,
      defalut: 'text'
    },
    customArrList: {
      type: Array,
      defalut: []
    },
    isMulti: {
      type: Boolean,
      default: false
    },
    SelectorData: {
      type: [Array, Boolean, String, Object],
      default: []
    },
    isId: {
      type: Boolean,
      default: false
    },
    headerName: {
      type: String,
      default: ''
    },
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'tableFilter',
    'resetFilter',
    'update:modelValue',
  ],
  watch: {},
  computed: {
    labelColor() {
      switch (this.filterType) {
        case 'text':
          if (this.conditions.text) {
            return 'highLight'
          }
          return ''
        case 'date':
          if (this.conditions.date) {
            return 'highLight'
          }
          return ''
        case 'dateRange':
          if (this.conditions.dateRange) {
            return 'highLight'
          }
          return ''
        case 'dateTime':
          if (this.conditions.dateTime) {
            return 'highLight'
          }
          return ''
        case 'dateTimeRange':
          if (this.conditions.dateTimeRange) {
            return 'highLight'
          }
          return ''
        case 'select':
          if (this.conditions.select?.length > 0) {
            return 'highLight'
          }
          if (this.conditions.select === undefined) {
            return ''
          }
          return ''
      }
      return ''
    }
  },
  created() {},
  directives: {
    ClickOutside
  },
  methods: {
    getInputClick(event) {
      this.visible = true
    },
    showPopover() {
      this.visible = true
    },
    reset() {
      switch (this.filterType) {
        case 'text':
          this.conditions.text = undefined
          break
        case 'date':
          this.conditions.date = undefined
          break
        case 'dateRange':
          this.conditions.dateRange = undefined
          break
        case 'dateTime':
          this.conditions.dateTime = undefined
          break
        case 'dateTimeRange':
          this.conditions.dateTimeRange = undefined
          break
        case 'select':
          this.conditions.select = undefined
          break
      }
      this.$refs.popoverRef.hide()
      this.tableFilter()
    },
    confirm() {
      this.$refs.popoverRef.hide()
      this.tableFilter()
    },
    tableFilter() {
      this.$emit('tableFilter', {
        column: this.column,
        filterType: this.filterType,
        fieldName: this.fieldName,
        conditions: this.conditions
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-header {
  cursor: pointer;
  display: inline-flex;
  & > span:nth-child(1) {
    width: 40px;
    line-height: 23px;
  }
}
.highLight {
  color: #409eff;
}
</style>
