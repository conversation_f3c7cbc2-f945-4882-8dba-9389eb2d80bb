<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    fullscreen
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="choose-fleet-version"
  >
    <el-scrollbar>
      <FleetModelManagement ref="FleetModelManagement" :opt-type="optType" :version="version"></FleetModelManagement>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('选择') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { showToast } from '@/plugins/util'
import FleetModelManagement from '@/pages/fleet/FleetModelManagement.vue'

const defaultform = {}
export default {
  name: 'ChooseFleetVersion',
  emits: ['reload'],
  components: { FleetModelManagement },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      version: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.optType = row.type
      switch (row.type) {
        case 'choose':
          this.dialogTitle = this.$t('选择车型版本')
          this.version = row.version
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      let currentData = this.$refs.FleetModelManagement.getCurrentData()
      if (currentData.versionId) {
        this.$emit('reload', currentData)
        this.cancel()
      } else {
        showToast('请先选择车型版本', 'warning')
      }
    }
  }
}
</script>
<style lang="scss">
.choose-fleet-version {
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    flex-grow: 1;
    overflow: auto;
  }
}
</style>

<style scoped lang="scss">
.dialog-footer {
  display: block;
  width: 100%;
  text-align: center;
}
</style>
