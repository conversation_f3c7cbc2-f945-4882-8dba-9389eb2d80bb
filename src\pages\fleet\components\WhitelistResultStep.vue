<template>
  <div class="whitelist-result-step">
    <!-- 完成信息 -->
    <div class="completion-header">
      <div class="completion-message">
        <el-icon class="success-icon"><Check /></el-icon>
        <span class="message-text">白名单创建完成，文件名如下，点击预览:</span>
      </div>
      <div class="file-actions">
        <div class="file-name-preview" @click="handlePreviewFile">
          {{ resultData.fileName }}
        </div>
        <el-button
          type="primary"
          size="small"
          @click="handleDownloadFile(resultData.fileId)"
          class="download-btn"
        >
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </div>

    <!-- 统计信息和搜索框 -->
    <div class="statistics-search-section">
      <!-- 统计信息 -->
      <div class="statistics-section">
        <div class="stat-item">
          <el-icon class="stat-icon"><Document /></el-icon>
          <span class="stat-label">Topic</span>
          <span class="stat-value">{{ resultData.topicSum }}</span>
        </div>
        <div class="divider"></div>
        <div class="stat-item">
          <el-icon class="stat-icon"><Document /></el-icon>
          <span class="stat-label">总数据大小</span>
          <span class="stat-value">{{ formatFileSize(resultData.dataSize) }}</span>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <ltw-input
          v-model="searchKeyword"
          placeholder="search"
          class="search-input"
          @input="handleSearch"
        >
          <template #append>
            <el-icon class="search-icon"><Search /></el-icon>
          </template>
        </ltw-input>
      </div>
    </div>

      <div  class="topic-table-section">
          <el-table
            :data="filteredTopicList"
            style="width: 100%"
            height="100%"
          >

            
            <!-- Topic Path -->
            <el-table-column prop="topic" label="Topic Path" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="topic-path">{{ row.topic }}</div>
              </template>
            </el-table-column>
            
            <!-- Size -->
            <el-table-column prop="size" label="Size" width="80" align="center">
              <template #default="{ row }">
                <span class="size-value">{{ formatSize(row.size) }}</span>
              </template>
            </el-table-column>
            
            <!-- Freq(Hz) -->
            <el-table-column prop="frequency" label="Freq(Hz)" width="80" align="center">
              <template #default="{ row }">
                <span class="frequency-value">{{ row.frequency || 0 }}</span>
              </template>
            </el-table-column>
            
            <!-- Minibag(0 B/s) -->
            <el-table-column label="Minibag(0 B/s)" width="110" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.minibag"
                  disabled
                  @change="handleMinibagChange(row)"
                />
              </template>
            </el-table-column>
            
            <!-- Latched Port -->
            <el-table-column label="Latched Port" width="100" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.latched"
                  disabled
                  @change="handleLatchedChange(row)"
                />
              </template>
            </el-table-column>
            
            <!-- 数据质检 -->
            <el-table-column label="数据质检" width="200" align="left">
              <template #default="{ row }">
                <div class="data-quality-display">
                  <el-switch
                    :model-value="(row.dataCheck && row.dataCheck.enabled) || false"
                    disabled
                  />
                  <div
                    v-if="row.dataCheck && row.dataCheck.enabled"
                    class="data-quality-config"
                    :style="{
                      'padding-left': '8px',
                      'padding-right': '8px',
                      'background': getDataQualityStyle(row.dataCheck).background,
                      'overflow': 'hidden',
                      'border-radius': '2px',
                      'outline': getDataQualityStyle(row.dataCheck).outline,
                      'outline-offset': '-1px',
                      'justify-content': 'center',
                      'align-items': 'center',
                      'gap': '4px',
                      'display': 'inline-flex',
                      'margin-left': '8px'
                    }"
                  >
                    <div :style="{
                      'text-align': 'center',
                      'color': getDataQualityStyle(row.dataCheck).color,
                      'font-size': '12px',
                      'font-family': 'Inter',
                      'font-weight': '400',
                      'line-height': '20px',
                      'word-wrap': 'break-word'
                    }">
                      {{ formatDataQualityConfig(row.dataCheck) }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Document, Search, Download } from '@element-plus/icons-vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'

export default {
  name: 'WhitelistResultStep',
  components: {
    Check,
    Document,
    Search,
    Download
  },
  props: {
    resultData: {
      type: Object,
      required: true,
      default: () => ({
        dataSize: 0,
        fileAddress: '',
        fileId: '',
        fileName: '',
        remark: '',
        topicInfoList: [],
        topicSum: 0,

      })
    }
  },
  emits: ['preview-file'],
  setup(props, { emit }) {
    // 搜索关键词
    const searchKeyword = ref('')

    // 过滤后的Topic列表
    const filteredTopicList = computed(() => {
      if (!searchKeyword.value.trim()) {
        return props.resultData.topicInfoList || []
      }
      
      const keyword = searchKeyword.value.toLowerCase()
      return (props.resultData.topicInfoList || []).filter(topic => 
        topic.topic?.toLowerCase().includes(keyword) ||
        topic.interfaceName?.toLowerCase().includes(keyword)
      )
    })

    // 处理搜索
    const handleSearch = () => {
      // 搜索逻辑已在computed中处理
    }

    // 处理文件预览
    const handlePreviewFile = () => {
      if (props.resultData.fileAddress) {
        emit('preview-file', {
          fileName: props.resultData.fileName,
          fileAddress: props.resultData.fileAddress,
          fileId: props.resultData.fileId
        })
      } else {
        ElMessage.warning('文件地址不可用')
      }
    }
   
    const  downloadUrl = ref(`${GLB_CONFIG.devUrl.fileServer} + '/'`) 
    // 处理文件下载
    const handleDownloadFile = (id) => {
      const url = downloadUrl.value + id + '?token=' + util.getToken()
      window.open(url)
    }

    // 格式化文件大小（用于表格中的Size列）
    const formatSize = (bytes) => {
      if (!bytes || bytes === 0) return '0'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(0))
    }

    // 格式化文件大小（用于统计信息）
    const formatFileSize = (bytes) => {
      if (!bytes || bytes === 0) return '0 B'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 格式化数据质检配置显示
    const formatDataQualityConfig = (dataQuality) => {
      if (!dataQuality || !dataQuality.enabled) {
        return '未启用'
      }

      // 兼容不同的字段名
      const minFreq = dataQuality.minFreq || dataQuality.minFrequency
      const maxFreq = dataQuality.maxFreq || dataQuality.maxFrequency
      const errLevel = dataQuality.errLevel || dataQuality.errorLevel

      // // 如果有任何值为空或未定义，显示"配置中"
      // if (minFreq === undefined || minFreq === null ||
      //     maxFreq === undefined || maxFreq === null ||
      //     !errLevel) {
      //   return '暂无信息'
      // }

      // 格式化错误等级显示
      const levelMap = {
        'Critical': 'critical',
        'Warning': 'warning',
        'Info': 'info'
      }

      const displayLevel = levelMap[errLevel] || errLevel.toLowerCase()
      return `${minFreq} - ${maxFreq}，${displayLevel}`
    }

    // 获取数据质检配置的样式
    const getDataQualityStyle = (dataQuality) => {
      if (!dataQuality || !dataQuality.enabled) {
        return {
          background: '#F5F5F5',
          outline: '1px #DDDDDD solid',
          color: '#999999'
        }
      }

      // 兼容不同的字段名
      const minFreq = dataQuality.minFreq || dataQuality.minFrequency
      const maxFreq = dataQuality.maxFreq || dataQuality.maxFrequency
      const errLevel = dataQuality.errLevel || dataQuality.errorLevel

      if (minFreq === undefined || minFreq === null ||
          maxFreq === undefined || maxFreq === null ||
          !errLevel) {
        return {
          background: '#FFF7E6',
          outline: '1px #FFD591 solid',
          color: '#FA8C16'
        }
      }

      // 根据错误等级设置不同颜色
      const styleMap = {
        'Critical': {
          background: '#FFF2F0',
          outline: '1px #FFCCC7 solid',
          color: '#FF4D4F'
        },
        'Warning': {
          background: '#FFFBE6',
          outline: '1px #FFE58F solid',
          color: '#FAAD14'
        },
        'Info': {
          background: '#F5F5FF',
          outline: '1px #DDDDFF solid',
          color: '#5755FF'
        }
      }

      return  styleMap['Info']
    }

    // 监听resultData变化
    watch(() => props.resultData, (newData) => {
      console.log('Result data updated:', newData)
    }, { deep: true })

    return {
      searchKeyword,
      filteredTopicList,
      handleSearch,
      handlePreviewFile,
      handleDownloadFile,
      formatFileSize,
      formatSize,
      formatDataQualityConfig,
      getDataQualityStyle
    }
  }
}
</script>

<style lang="scss" scoped>
.whitelist-result-step {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  
  .completion-header {
    padding: 16px;
    border-top: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .completion-message {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .success-icon {
        color: #67C23A;
        font-size: 20px;
      }
      
      .message-text {
        color: #4E5256;
        font-size: 12px;
        font-weight: 700;
        line-height: 24px;
      }
    }
    
    .file-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .file-name-preview {
      color: #5755FF;
      font-size: 12px;
      font-weight: 700;
      text-decoration: underline;
      line-height: 12px;
      cursor: pointer;

      &:hover {
        color: #3f3cff;
      }
    }

    .download-btn {
      font-size: 12px;
      height: 24px;
      padding: 0 8px;

      .el-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
  
  .statistics-search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .statistics-section {
    display: flex;
    align-items: center;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .stat-icon {
        color: #232628;
        font-size: 16px;
      }

      .stat-label {
        color: #4E5256;
        font-size: 12px;
        font-weight: 400;
        line-height: 24px;
      }

      .stat-value {
        color: #5755FF;
        font-size: 12px;
        font-weight: 700;
        line-height: 24px;
      }
    }

    .divider {
      width: 1px;
      height: 13px;
      background: #D0D4D8;
      border-radius: 1px;
    }
  }

  .search-section {
    .search-input {
      width: 390px;

    //   :deep(.el-input__wrapper) {
    //     border-radius: 2px;
    //     border: 1px solid #D0D4D8;
    //   }

    //   :deep(.el-input__inner) {
    //     color: #B2B9C0;
    //     font-size: 12px;
    //     line-height: 12px;
    //   }

    //   .search-icon {
    //     color: #B2B9C0;
    //     font-size: 12px;
    //   }
    }
  }
  
  .topic-table-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .result-table {
      :deep(.el-table__header) {
        background: #FAFAFC;
        
        th {
          background: #FAFAFC !important;
          border-bottom: 1px solid #EBEEF5;
          
          .cell {
            color: #909399;
            font-size: 12px;
            font-weight: 400;
            line-height: 23px;
          }
        }
      }
      
      :deep(.el-table__body) {
        tr {
          &:hover {
            background: #F1F5FA !important;
          }
          
          td {
            border-bottom: 1px solid #EBEEF5;
            
            .cell {
              color: #303133;
              font-size: 12px;
              font-weight: 400;
              line-height: 23px;
            }
          }
        }
      }
      
      .topic-checkbox {
        :deep(.el-checkbox__input) {
          .el-checkbox__inner {
            background: #5755FF;
            border-color: #5755FF;
            border-radius: 2px;
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
  
  // 数据质检显示样式
  .data-quality-display {
    display: flex;
    align-items: center;
    gap: 8px;

    .data-quality-config {
      font-size: 12px;
      line-height: 20px;
      white-space: nowrap;
    }
  }

  .remark-section {
    margin-top: 16px;
  }
}
</style>
