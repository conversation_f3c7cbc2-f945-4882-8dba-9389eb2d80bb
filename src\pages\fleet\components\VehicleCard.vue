<template>
  <el-card class="vehicle-card" :class="row.statusName === 'Working' ? 'danger' : 'success'">
    <div class="card-header">
      <span class="tags">
        <span class="title">{{ row.vin }}</span>
        <span class="title external-vin" v-if="row.externalVin">{{ row.externalVin }}</span></span
      >
      <el-tag :type="row.statusName === 'Working' ? 'danger' : 'success'">{{ row.statusName }}</el-tag>
    </div>
    <div class="card-content">
      <div class="vehicle-info">
        <div class="vehicle-item-list">
          <div class="vehicle-item title">
            <el-tooltip :enterable="false" :content="row.variant">
              <div class="model-item model-title ellipsis" v-text="(row.variant || 'N/A') + '(' + (row.version || 'N/A') + ')'"></div>
            </el-tooltip>
          </div>
          <div class="vehicle-item vehicle-type">
            <el-tooltip :enterable="false" :content="row.brand">
              <div v-if="row.brand" class="ellipsis" v-text="row.brand"></div>
            </el-tooltip>
            <el-tooltip :enterable="false" :content="row.model">
              <div v-if="row.model" class="ellipsis" v-text="row.model"></div>
            </el-tooltip>
            <el-tooltip :enterable="false" :content="row.vehicleTypeName">
              <div v-if="row.vehicleTypeName" class="ellipsis" v-text="row.vehicleTypeName"></div>
            </el-tooltip>
          </div>
          <div class="vehicle-item">
            <el-tooltip :enterable="false" :content="row.projectName">
              <div class="model-item model-title ellipsis project-name" v-text="row.projectName"></div>
            </el-tooltip>
          </div>
        </div>
        <!--                <el-descriptions :column="1" border>
                          <el-descriptions-item :label="'车型版本'">
                            <el-tooltip
                              effect="dark"
                              :content="(row.variant || 'N/A') + '(' + (row.version || 'N/A') + ')'"
                              placement="top"
                              :enterable="false"
                            >
                              {{ (row.variant || 'N/A') + '(' + (row.version || 'N/A') + ')' }}
                            </el-tooltip>
                          </el-descriptions-item>
                          <el-descriptions-item :label="'车辆用途'">{{ row.useTypeName }}</el-descriptions-item>
                          <el-descriptions-item :label="'车型类型'">{{ row.vehicleTypeName }}</el-descriptions-item>
                          <el-descriptions-item :label="'责任人'">
                            <el-tooltip effect="dark" :content="row.keeperEmpName" placement="top" :enterable="false"
                              ><span style="cursor: pointer">{{ row.keeperEmpName }}</span></el-tooltip
                            >
                          </el-descriptions-item>
                          <el-descriptions-item :label="'车端设备'">
                            <span v-if="row.tagEquipBrand && row.tagEquipCode">
                              <el-tooltip effect="dark" :content="$t('解绑')" placement="top" :enterable="false">
                                <el-link type="primary" :underline="false" @click="unbindBsTagEquipment(row)">
                                  <ltw-icon icon-code="el-icon-unlock"></ltw-icon>
                                </el-link>
                              </el-tooltip>
                              &nbsp;
                              <el-tooltip
                                effect="dark"
                                :content="row.tagEquipBrand + '-' + row.tagEquipCode"
                                placement="top"
                                :enterable="false"
                              >
                                <span style="cursor: pointer">{{ row.tagEquipBrand + '-' + row.tagEquipCode }}</span>
                              </el-tooltip>
                            </span>

                            <el-popover v-else placement="right" width="400" trigger="click">
                              <bs-tag-equipment-selection
                                :data="equipmentList"
                                :auto-load="false"
                                v-model="row.tagEquipId"
                                @change="handleEquipmentChange($event, row)"
                                :ref="'equipmentSelectionRef' + row.id"
                              ></bs-tag-equipment-selection>
                              <template #reference>
                                <el-link type="primary" :underline="false" @click="listEquipment">
                                  <ltw-icon icon-code="el-icon-connection"></ltw-icon>
                                </el-link>
                              </template>
                            </el-popover>
                          </el-descriptions-item>
                          <el-descriptions-item :label="'传感器'">
                            <el-tag @click="getModalityList(row)" v-if="!row.modalityAbnormal" type="success">{{ $t('正常') }}</el-tag>
                            <el-tag @click="getModalityList(row)" v-else type="danger">{{ $t('异常') }}</el-tag>
                          </el-descriptions-item>
                        </el-descriptions>-->
        <div class="vehicle-img">
          <el-image
            :src="
              row.photoList?.length
                ? downloadUrl + row?.photoList[0]?.id + token
                : row.vehicleType === 'sedan'
                ? require('@/assets/images/default-sedan.png')
                : require('@/assets/images/default-suv.png')
            "
            :preview-src-list="[
              row.photoList && row.photoList.length
                ? downloadUrl + row.photoList[0].id + token
                : row.vehicleType === 'sedan'
                ? require('@/assets/images/default-sedan.png')
                : require('@/assets/images/default-suv.png')
            ]"
            fit="scale-down"
          >
            <template #error>
              <div class="image-slot">
                <ltw-icon icon-code="el-icon-picture"></ltw-icon>
              </div>
            </template>
          </el-image>
        </div>
      </div>
    </div>
    <div class="card-tail">
      <div class="keeper-emp-name-sw-version">
        <div class="keeper-emp-name" v-if="row.keeperEmpName">
          <el-tooltip :enterable="false" :content="row.keeperEmpName">
            <div class="model-item model-title ellipsis" v-text="row.keeperEmpName"></div>
          </el-tooltip>
        </div>
        <div class="software_version">
          <template v-for="sw in softwareVersionList">
            <span :class="getClassByModule(sw)">{{sw.module}}: {{sw.version}}</span>
          </template>
<!--          <span class="aio"></span>-->
<!--          <span class="aio getk">GETK: P3</span>-->
<!--          <span class="aio ralo">RALO: 1.0.0</span>-->
        </div>
      </div>
      <div class="vehicle-img-btn">
        <el-dropdown @command="handleCommand" class="batch-operate-btn">
          <el-button class="button" text>
            <ltw-icon icon-code="el-icon-more"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="commandObj('view', row)">
                <ltw-icon icon-code="el-icon-view"></ltw-icon>
                {{ $t('详情') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType === 'map'" :command="commandObj('position', row)">
                <ltw-icon icon-code="el-icon-location"></ltw-icon>
                {{ $t('定位') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType === 'map'" :command="commandObj('trajectory', row)">
                <ltw-icon icon-code="el-icon-guide"></ltw-icon>
                {{ $t('轨迹') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('edit', row)">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                {{ $t('编辑') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('remove', row)">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                {{ $t('删除') }}
              </el-dropdown-item>
              <!--                  <el-dropdown-item :command="commandObj('detail', row)">-->
              <!--                    <ltw-icon icon-code="el-icon-view"></ltw-icon>-->
              <!--                    {{ $t('详情') }}-->
              <!--                  </el-dropdown-item>-->
              <!--                  <el-dropdown-item :command="commandObj('fleetVersion', row)">-->
              <!--                    {{ $t('软件版本') }}-->
              <!--                  </el-dropdown-item>-->
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('calibration', row)">
                <ltw-icon icon-code="el-icon-aim"></ltw-icon>
                {{ $t('标定参数') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('competitors', row)">
                <ltw-icon icon-code="el-icon-aim"></ltw-icon>
                {{ $t('对手件') }}
              </el-dropdown-item>
<!--              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('dbc', row)">-->
<!--                <ltw-icon icon-code="el-icon-Setting"></ltw-icon>-->
<!--                {{ $t('dbc') }}-->
<!--              </el-dropdown-item>-->
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('fleetInsurance', row)">
                <ltw-icon icon-code="el-icon-document"></ltw-icon>
                {{ $t('保险') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

    </div>
  </el-card>
  <calibration-config @reload="reloadForm" ref="CalibrationConfig"/>
<!--  <dbc-config @reload="reloadForm" ref="DbcConfig"/>-->
  <modality-list @reload="reloadForm" ref="ModalityList" class="modality-issue"/>
  <vehicle-statistic-detail ref="VehicleStatisticDetail" class="vehicle-statistic-detail"/>
  <AddCalibration ref="AddCalibration"/>
  <!--  <AddFleetVersion ref="AddFleetVersion" />-->
  <OptFleetInsurance ref="OptFleetInsurance"/>
</template>

<script>
import util from '@/plugins/util'
import AddCalibration from '@/pages/fleet/dialog/AddCalibration.vue'
import BsTagEquipmentSelection from '@/components/basic/BsTagEquipmentSelection.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import VehicleStatisticDetail from '@/pages/fleet/dialog/VehicleStatisticDetail.vue'
// import AddFleetVersion from '@/pages/fleet/dialog/AddFleetVersion.vue'
import CalibrationConfig from '@/pages/fleet/dialog/CalibrationConfig.vue'
// import DbcConfig from '@/pages/fleet/dialog/VehicleDbcConfig.vue'
import OptFleetInsurance from '@/pages/fleet/dialog/OptFleetInsurance.vue'
import ModalityList from '@/pages/fleet/dialog/ModalityList'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { bindBsTagEquipment, unbindBsTagEquipment, listBsTagEquipment } from '@/apis/data-collect/bs-tag-equipment'
// import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import {
  ElButton,
  ElTooltip,
  ElTag,
  ElLink,
  ElDescriptions,
  ElDescriptionsItem,
  ElCard,
  ElPopover,
  ElSelect,
  ElOption,
  ElImage
} from 'element-plus'
import { showToast, showConfirmToast } from '@/plugins/util'
import { i18n } from '@/plugins/lang'
import { getBsVehicle, deleteBsVehicle, changeVehicleStatus } from '@/apis/fleet/bs-vehicle'
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  name: 'VehicleCard',
  data() {
    return {
      equipmentList: [],
      $t: i18n.global.t,
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken(),
      row: {},
      softwareVersionList:[],
      options: [],
      visible: false
      // vehicleStatuses: [
      // {
      //   code: 'free',
      //   name: 'Free'
      // },
      // {
      //   code: 'working',
      //   name: 'Working'
      // },
      // {
      //   code: 'repairing',
      //   name: 'repairing'
      // }
      // ]
    }
  },
  emits: ['reload', 'get-vehicle-position'],
  props: {
    type: {
      type: String,
      required: false,
      default: ''
    },
    pageType: {
      type: String,
      required: false,
      default: ''
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    vehicleStatuses: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    customFunctionList: {
      type: Array,
      default: []
    }
  },
  watch: {
    item: {
      handler(val) {
        this.row = val
        if (val.vehicleSoftwareVersions !== undefined && val.vehicleSoftwareVersions.length > 0) {
          this.softwareVersionList = val.vehicleSoftwareVersions.filter(version =>
              version.type === 'tool' && (version.module === 'aio' || version.module === 'getk' || version.module === 'raloctl') && version.version !== '' && version.version !== undefined
          );
        }
      },
      deep: true,
      immediate: true
    }
  },
  // computed: {
  //   row() {
  //     return this.item
  //     // get() {
  //     //   return this.item
  //     // },
  //     // set(val) {
  //     //   this.row = val
  //     // }
  //   }
  // },
  components: {
    ElButton,
    ElTag,
    ElLink,
    ElDescriptions,
    ElDescriptionsItem,
    ElCard,
    ElTooltip,
    ElPopover,
    ElSelect,
    ElOption,
    BsTagEquipmentSelection,
    LtwIcon,
    VehicleStatisticDetail,
    CalibrationConfig,
    // DbcConfig,
    // AddFleetVersion,
    ModalityList,
    AddCalibration,
    OptFleetInsurance,
    ElImage
  },
  mounted() {},
  created() {
    // this.listStatus()
  },
  methods: {
    getClassByModule(sw){
      if (sw.module==='aio'){
        return 'aio'
      }else if (sw.module ==='getk'){
        return 'aio getk'
      }else if (sw.module ==='raloctl'){
        return 'aio ralo'
      }
      return ''
    },
    // getFleetVersion(row) {
    //   this.$refs.AddFleetVersion.show({
    //     type: 'view',
    //     vin: row.vin
    //   })
    // },
    getCalibrationConfig(row) {
      this.$refs.CalibrationConfig.show({
        type: 'view',
        vin: row.vin
      })
      // this.modalityQuery()
    },
    // getDbcConfig(row) {
    //   this.$refs.DbcConfig.show({
    //     type: 'view',
    //     data: row
    //   })
    // },
    getOptFleetInsurance(row) {
      // if (row.insuranceId) {
      this.$refs.OptFleetInsurance.show({
        type: 'view',
        vin: row.vin
      })
      // } else {
      //   showToast('该车辆暂无保险', 'warning')
      // }
      // this.modalityQuery()
    },
    // modalityQuery() {
    // },
    getModalityList(row) {
      if (this.checkInlineButton('modalityIssue')) {
        this.$refs.ModalityList.show({
          type: 'view',
          vehicleId: row.id,
          variant: row.variant
        })
      }
    },
    getVehicleDetail(row) {
      if (row.variantVersionId) {
        this.$router.push({
          name: 'vehicleVariantVersion',
          query: {
            variantVersionId: row.variantVersionId
          }
        })
      }
    },
    viewVehicleDetail(id) {
      const {buttonName, buttonCode} = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/ViewFleetManagement',
        query: {
          id,
          btn: encodeURIComponent(JSON.stringify({buttonName, buttonCode}))
        }
      })
    },
    editVehicleDetail(id) {
      const {buttonName, buttonCode} = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          btn: encodeURIComponent(JSON.stringify({buttonName, buttonCode})),
          sourceType: 'installRecord'
        }
      })
    },
    editVehicleCompetitor(id) {
      const {buttonName, buttonCode} = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          btn: encodeURIComponent(JSON.stringify({buttonName, buttonCode})),
          sourceType: 'competitor'
        }
      })
    },
    deleteVehicleDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteBsVehicle({id}).then(() => {
          // this.query()
          this.$emit('reload')
        })
      })
    },
    query() {
      this.$emit('reload')
    },
    getVehicleStatisticDetail(id) {
      const {buttonName, buttonCode} = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          btn: encodeURIComponent(JSON.stringify({buttonName, buttonCode}))
        }
      })
      // this.$refs.VehicleStatisticDetail.show({
      //   type: 'view',
      //   id: id
      // })
    },
    getBsVehicle(id) {
      getBsVehicle(id).then(res => {
        this.row = res.data
      })
    },
    listEquipment() {
      if (!(this.equipmentList && this.equipmentList.length)) {
        listBsTagEquipment().then(res => {
          this.equipmentList = res.data
        })
      }
    },
    handleEquipmentChange({node}, row) {
      this.bindBsTagEquipment({tagEquipId: node.id, id: row.id})
    },
    bindBsTagEquipment(row) {
      let postData = {
        tagEquipId: row.tagEquipId,
        vehicleId: row.id
      }
      bindBsTagEquipment(postData).then(() => {
        showToast('绑定成功')
        this.getBsVehicle(row.id)
      })
    },
    confirmVehicleStatus() {
      let postData = {
        status: this.row.status,
        id: this.row.id
      }
      // console.log(postData)
      // this.row.statusVisible = false
      changeVehicleStatus(postData).then(() => {
        this.visible = false
        showToast('修改成功')
        this.getBsVehicle(this.row.id)
      })
    },
    checkVehicleStatus(status, item) {
      if (status === 'free') {
        if (['distributed', 'working', 'suspending'].includes(item.code)) {
          return true
        }
      }
      if (status === 'distributed') {
        return true
      }
      if (status === 'working' && item.code !== 'suspending') {
        return true
      }
      if (status === 'repairing' && item.code !== 'free') {
        return true
      }
      if (status === 'suspending' && item.code !== 'working') {
        return true
      }
      if (status === 'calibrating' && item.code !== 'free') {
        return true
      }
      return false
    },
    // showVehicleStatus() {
    //   this.row.statusVisible = true
    // },
    unbindBsTagEquipment(row) {
      showConfirmToast({
        message: BASE_CONSTANT.UNBIND_CONFIRM_MSG
      }).then(() => {
        let postData = {
          tagEquipId: row.tagEquipId
        }
        unbindBsTagEquipment(postData).then(() => {
          showToast('解绑成功')
          this.getBsVehicle(row.id)
        })
      })
    },
    checkType(index) {
      let type
      switch (index % 5) {
        case 1:
          type = 'success'
          break
        case 2:
          type = 'info'
          break
        case 3:
          type = 'warning'
          break
        case 4:
          type = 'danger'
          break
      }
      return type
    },
    reloadForm() {
      this.getBsVehicle(this.row.id)
    },
    checkInlineButton(type) {
      return ~this.inlineFunctionList.findIndex(val => val.buttonCode === type)
    },
    handleCommand(obj) {
      if (obj.type === 'remove') {
        this.deleteVehicleDetail(obj.row.id)
      } else if (obj.type === 'view') {
        this.viewVehicleDetail(obj.row.id)
      } else if (obj.type === 'edit') {
        this.editVehicleDetail(obj.row.id)
        // } else if (obj.type === 'detail') {
        //   this.getVehicleStatisticDetail(obj.row.id)
      } else if (obj.type === 'fleetVersion') {
        this.getFleetVersion(obj.row)
      } else if (obj.type === 'calibration') {
        this.getCalibrationConfig(obj.row)
      } else if (obj.type === 'competitors') {
        this.editVehicleCompetitor(obj.row.id)
      // } else if (obj.type === 'dbc') {
      //   this.getDbcConfig(obj.row)
      } else if (obj.type === 'fleetInsurance') {
        this.getOptFleetInsurance(obj.row)
      } else if (obj.type === 'position' || obj.type === 'trajectory') {
        this.getVehiclePosition(obj)
      }
    },
    commandObj(type, row) {
      return {
        type,
        row
      }
    },
    getBtnList(btnList, code) {
      return btnList.find(val => val.buttonCode === code) || {}
    },
    getVehiclePosition(row) {
      this.$emit('get-vehicle-position', row)
    }
  }
}
</script>

<style scoped lang="scss">
.vehicle-card {
  min-width: 300px;
  // min-width: 360px;
  overflow: hidden !important;
  margin-bottom: 10px;

  &.danger {
    border-left: 2px solid #f56c6c;
  }

  &.success {
    border-left: 2px solid #67c23a;
  }

  :deep(.variant-detail) {
    .title {
      margin-bottom: 10px;
    }

    .content {
      margin-bottom: 15px;
    }
  }

  // width: 360px;
  :deep(.el-card__header) {
    padding: 4px 10px;
  }

  :deep(.el-card__body) {
    padding: 4px 10px 4px 10px;
  }

  .el-tag {
    cursor: pointer;
  }

  .card-tail {
    display: flex;
    justify-content: space-between;

    .software_version {
      display: flex;
      justify-content: flex-start;
      .aio {
        margin-right: 8px;
        font-size: 12px;
        color: rgb(7, 55, 99);

        &.getk {
          color: rgb(56, 119, 47);
        }

        &.ralo {
          color: rgb(19, 80, 107);
        }
      }
    }

    .vehicle-img-btn {
      //position: absolute;
      //right: 0;
      //bottom: -6px;
      //text-align: right;

      .el-link:not(:last-child) {
        margin-right: 10px;
      }
    }

    //.tags {
    //  display: flex;
    //
    //  .title {
    //    margin-right: 10px;
    //  }
    //}

    .keeper-emp-name-sw-version {
      display: flex;
      align-items: center;
      font-style: italic;
      font-size: 12px;
      .keeper-emp-name{
        margin-right: 35px;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;

    .tags {
      display: flex;

      .title {
        margin-right: 10px;
      }
    }

    .title {
      font-size: 12px;
      //font-size: 18px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;
      //color: rgb(16, 41, 106);
      //font-weight: 600;
      &.external-vin {
        //background: #1b4a7f;
        border: 0px solid rgb(16, 41, 106);
        background-color: #c6e2ff;
        color: #000;
      }
    }
  }

  .card-content {
    .vehicle-info {
      display: flex;

      .vehicle-item-list {
        width: 50%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        padding-top: 10px;

        .vehicle-item {
          display: flex;
          //line-height: 40px;
          font-size: 14px;

          .ellipsis {
            margin-right: 10px;
          }

          &.title {
            font-weight: 600;
            font-size: 16px;
          }

          .project-name {
            color: orange;
          }

          &.vehicle-type {
            font-style: italic;
          }

          //&.keeper-emp-name {
          //  font-style: italic;
          //  font-size: 12px;
          //}
        }
      }

      //.el-descriptions {
      //  width: 50%;
      //
      //  :deep(.el-descriptions__body) {
      //    width: 100%;
      //    overflow: hidden;
      //  }
      //
      //  :deep(.el-descriptions__table) {
      //    width: 100%;
      //    table-layout: fixed;
      //  }
      //
      //  :deep(.el-descriptions__cell) {
      //    font-size: 12px;
      //    font-weight: 400;
      //    padding: 0 11px;
      //    overflow: hidden;
      //    text-overflow: ellipsis;
      //    white-space: nowrap;
      //  }
      //
      //  :deep(.el-descriptions__cell.el-descriptions__label) {
      //    width: 71px;
      //  }
      //
      //  :deep(.el-descriptions__cell.el-descriptions__content) {
      //    width: calc(100% - 71px);
      //  }
      //
      //  // width: 50%;
      //}

      .vehicle-img {
        overflow: hidden;
        width: 50%;
        padding-left: 10px;
        padding-top: 10px;
        position: relative;
        //padding-bottom: 16px;

        img,
        .el-image {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 120px;
          width: 100%;
          font-size: 30px;
          // background: var(--el-fill-color-light);
          color: var(--el-text-color-secondary);

          .image-slot {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          }
        }
      }
    }

    .vehicle-opt {
      // padding-right: 10px;
      text-align: right;

      .el-link + .el-link {
        margin-left: 10px;
      }
    }
  }
}
</style>
