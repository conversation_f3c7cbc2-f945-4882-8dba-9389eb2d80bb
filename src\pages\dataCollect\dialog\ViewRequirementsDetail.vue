<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="ViewRequirementsDetail"
  >
    <requirements-form :item="form" ref="RequirementsForm" />
    <template #footer>
      <span class="dialog-footer">
        <el-button
          v-if="dialogStatus === 'publish'"
          type="primary"
          @click="submit"
          id="submit"
          >{{ $t('发布') }}</el-button
        >
        <el-button
          v-if="dialogStatus === 'resolve'"
          type="primary"
          @click="resolve"
          id="resolve"
          >{{ $t('接收') }}</el-button
        >
        <el-button
          v-if="dialogStatus === 'reject'"
          type="primary"
          @click="reject"
          id="reject"
          >{{ $t('拒绝') }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { i18n } from '@/plugins/lang'
import RequirementsForm from '@/pages/dataCollect/components/RequirementsForm.vue'
import { showToast } from '@/plugins/util'
import {
  publishDaq,
  acceptDaq,
  rejectDaq
} from '@/apis/data-collect/vt-daq-req'
import { getDaqReq } from '@/apis/data-collect/vt-daq-req'
const defaultform = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform)
    }
  },
  components: { RequirementsForm },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = i18n.global.t('采集需求')
      this.getDaqReq(row.id)
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.form = { ...defaultform }
    },
    submit() {
      publishDaq(this.form.id).then(res => {
        showToast('发布成功')
        this.cancel()
      })
    },
    resolve() {
      acceptDaq(this.form.id).then(res => {
        showToast('接受成功')
        this.cancel()
      })
    },
    reject() {
      rejectDaq(this.form.id).then(res => {
        showToast('拒绝成功')
        this.cancel()
      })
    },
    getDaqReq(id) {
      getDaqReq(id).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        res.data.dateRange = [
          res.data.expectedStartTime,
          res.data.expectedEndTime
        ]
        this.tagList = res.data.tagList
        this.form = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
