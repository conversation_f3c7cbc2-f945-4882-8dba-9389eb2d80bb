<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    draggable
    destroy-on-close
  >
    <el-form
      :model="formData"
      :rules="formRules"
      require-asterisk-position="right"
      ref="formRef"
      label-width="80px"
      label-position="top"
    >
      <el-form-item :label="$t('类型')" prop="type">
        <el-select
          v-model="formData.type"
          :disabled="formReadonly"
          filterable
          :placeholder="$t('请选择') + $t('信号类型')"
          clearable
          style="width: 100%"
          @change="onTypeChange"
          @visible-change="onTypeVisibleChange"
        >
          <el-option
            v-for="item in dbcTypeList"
            :disabled="item.disabled"
            :key="item.code"
            :label="item.name + '（' + item.code + '）'"
            :value="item.code"
          />
          <div slot="dropdown" class="custom-dropdown" v-if="dialogStatus !== 'edit'">
            <div class="dropdown-footer">
              <div v-if="!isAdding">
                <span style="margin-left: 5px">
                  <el-button type="text" size="small" @click.stop="onAddOption">自定义</el-button>
                </span>
              </div>
              <div v-else>
                <div slot="dropdown" class="custom-dropdown">
                  <div class="dropdown-footer">
                    <div v-if="!isAdding">
                      <el-button type="text" size="small" @click.stop="onAddOption">自定义</el-button>
                    </div>
                    <div v-else>
                      <div style="display: flex; justify-content: center; margin: 12px 0">
                        <el-card style="width: 90%">
                          <el-form
                            ref="customTypeFormRef"
                            :model="customTypeForm"
                            :rules="customTypeRules"
                            label-width="0"
                            class="custom-type-form"
                            require-asterisk-position="right"
                            label-position="top"
                          >
                            <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 12px">
                              <el-form-item :label="$t('名称')" prop="customTypeName">
                                <ltw-input
                                  style="width: 240px"
                                  v-model="customTypeForm.customTypeName"
                                  size="small"
                                  :placeholder="$t('名称')"
                                  class="option-input"
                                />
                              </el-form-item>
                              <el-form-item :label="$t('编码')" prop="customType">
                                <ltw-input
                                  style="width: 240px"
                                  v-model="customTypeForm.customType"
                                  size="small"
                                  :placeholder="$t('编码')"
                                  class="option-input"
                                />
                              </el-form-item>
                            </div>
                          </el-form>
                          <div style="text-align: right">
                            <el-button type="primary" size="small" @click.stop="onConfirm">确定</el-button>
                            <el-button size="small" @click.stop="onCancel">取消</el-button>
                          </div>
                        </el-card>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('车型')" prop="variantId">
        <el-select
          id="variantId"
          v-model="formData.variantId"
          :disabled="formReadonly"
          filterable
          :placeholder="$t('请选择') + $t('车型')"
          clearable
          @change="changeVariant"
          style="width: 100%"
        >
          <el-option
            v-for="item in vehicleVariantList"
            :key="item.id"
            :label="item.code + '(' + item.useTypeName + ')'"
            :value="item.id"
          >
            <div>{{ item.code }}({{ item.useTypeName }})</div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('传感器')" prop="modality">
        <el-select
          :value-key="'modality'"
          v-model="formData.modality"
          :disabled="formReadonly"
          filterable
          :placeholder="$t('请选择') + $t('传感器')"
          clearable
          style="width: 100%"
        >
          <el-option v-for="item in dbcModalityList" :key="item.id" :label="item.code" :value="item.code">
            <div>{{ item.code }}</div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('选择dbc文件')" prop="dbcFileId">
        <el-button size="small" plain v-if="!formReadonly" @click="chooseDbcFile()" type="primary">
          选择文件
        </el-button>
        <el-alert
          type="info"
          :closable="false"
          style="line-height: 1.2; margin-top: 10px; width: 100%; position: relative"
        >
          <template #title>
            <div style="display: flex; align-items: center">
              <el-icon><Files /></el-icon>
              <span style="margin-left: 4px; color: #409eff">{{ formData.dbcFilePath || '暂无文件' }}</span>
            </div>
          </template>
          <el-button
            v-if="formData.dbcFilePath"
            type="text"
            size="small"
            :disabled="formReadonly"
            @click="deleteDbcFilePath"
            style="position: absolute; top: 4px; right: 4px; color: #f56c6c"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </el-alert>
      </el-form-item>

      <el-form-item :label="$t('message')" prop="messageId">
        <!-- <el-select
          :value-key="'dbcId'"
          v-model="formData.messageId"
          :disabled="formReadonly"
          filterable
          :placeholder="$t('请选择') + $t('Message')"
          clearable
          ref="messageSelectRef"
          style="width: 100%"
        >
          <el-option v-for="item in dbcMessageList" :key="item.id" :label="item.name" :value="item.id">
            <div>{{ item.name }}</div>
          </el-option>
        </el-select> -->
        <ltw-input :limit-size="100" v-model="formData.messageId" :disabled="formReadonly" />
      </el-form-item>

      <el-form-item :label="$t('signal')" prop="signalName">
        <ltw-input :limit-size="100" v-model="formData.signalName" :disabled="formReadonly" />
      </el-form-item>

      <el-form-item :label="$t('单位')" prop="unit">
        <ltw-input :limit-size="36" v-model="formData.unit" :disabled="formReadonly" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
    <DbcFileDrawer ref="DbcFileDrawerRef" @selected-dbc-file="onDbcFileSelected" />
  </el-dialog>
</template>

<script>
import DbcFileDrawer from '../components/DbcFileDrawer.vue'
import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import { listFtmDbcMessage } from '@/apis/fleet/ftm-dbc'
import {
  pageFtmDbcSignalLibraryType,
  saveFtmDbcSignalLibrary,
  updateFtmDbcSignalLibrary,
  getFtmDbcSignalLibrary
} from '@/apis/fleet/ftm-dbc-signal-library'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { querySensorListGroupByType } from '@/apis/fleet/ftm-sensor'
const defaultform = {}
export default {
  name: 'AddFtmDbcSignalLibrary',
  emits: ['reload'],
  components: { DbcFileDrawer },
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      formData: Object.assign({}, defaultform),
      formRules: {
        type: [{ required: true, message: this.$t('请选择类型'), trigger: 'change' }],
        variantId: [{ required: true, message: this.$t('请选择车型'), trigger: 'change' }],
        dbcFileId: [{ required: true, message: this.$t('请选择dbc附件信息'), trigger: 'blur' }],
        // messageId: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if (!value) {
        //         callback(new Error('请选择 message'))
        //       } else if (!this.formData.dbcFilePath) {
        //         callback(new Error('请先选择 DBC 文件'))
        //       } else {
        //         callback()
        //       }
        //     },
        //     trigger: 'change'
        //   }
        // ],
        messageId: [{ required: true, message: this.$t('请输入message'), trigger: 'blur' }],
        modality: [{ required: true, message: this.$t('请选择传感器'), trigger: 'change' }],
        unit: [{ required: false, message: this.$t('请输入单位'), trigger: 'blur' }],
        signalName: [{ required: true, message: this.$t('请输入signal'), trigger: 'blur' }]
      },
      dbcTypeList: [],
      vehicleVariantList: [],
      dbcMessageList: [],
      dbcModalityList: [],
      isAdding: false,
      customTypeForm: {
        customTypeName: '',
        customType: ''
      },
      customTypeRules: {
        customTypeName: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        customType: [{ required: true, message: '请输入类型编码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    listdbcType() {
      listSysDictionary({ typeCode: 'dbc_signal_library_type' }).then(res => {
        if (res.data.length === 0) {
          this.dbcTypeList = [
            {
              code: '__placeholder__',
              name: this.$t('暂无信号类型'),
              disabled: true
            }
          ]
        } else {
          this.dbcTypeList = res.data
        }
      })
    },
    onAddOption() {
      this.isAdding = true
    },
    onConfirm() {
      this.$refs.customTypeFormRef.validate(valid => {
        if (valid) {
          const { customType, customTypeName } = this.customTypeForm
          this.dbcTypeList.push({ code: customType, name: customTypeName })
          this.formData.type = customType
          this.formData.typeName = customTypeName
          this.customTypeForm.customType = ''
          this.customTypeForm.customTypeName = ''
          this.isAdding = false
          this.dbcTypeList = this.dbcTypeList.filter(item => item.code !== '__placeholder__')
        }
      })
    },
    onCancel() {
      this.customTypeForm.customType = ''
      this.customTypeForm.customTypeName = ''
      this.isAdding = false
    },
    onTypeChange(val) {
      const selected = this.dbcTypeList.find(item => item.code === val)
      this.formData.typeName = selected ? selected.name : ''
      this.formData.type = selected ? selected.code : ''
    },
    onTypeVisibleChange(visible) {
      if (!visible) return
      this.isAdding = false
    },
    listModality() {
      querySensorListGroupByType().then(res => {
        const canItem = res.data.find(item => item.code === 'CAN')
        this.dbcModalityList = canItem ? canItem.modalityVOS : []
      })
    },
    listVehicleVariant() {
      listFtmVehicleVariant().then(res => {
        this.vehicleVariantList = res.data
      })
    },
    show(row) {
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增')
          this.formData = {}
          this.dialogVisible = true
          break
        case 'view':
          this.dialogTitle = this.$t('查看详情')
          getFtmDbcSignalLibrary(row.data.id).then(res => {
            this.$nextTick(function () {
              this.formData = res.data
              this.formData.type = res.data.type
              // this.getFtmDbcMessageList(this.formData.dbcFileId)
            })
            this.dialogVisible = true
          })
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑')
          getFtmDbcSignalLibrary(row.data.id).then(res => {
            this.$nextTick(function () {
              this.formData = res.data
              this.formData.type = res.data.type
            })
            this.dialogVisible = true
          })
          break
      }
    },
    chooseDbcFile() {
      this.$refs.DbcFileDrawerRef.show()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {
      if (!this.formReadonly) {
        this.listdbcType()
        this.listModality()
      }
      this.listVehicleVariant()
    },
    initForm(id) {
      if (this.visible) {
      }
      this.$refs.formRef.resetFields()
      this.formData = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const selectedVariant = this.vehicleVariantList.find(item => item.id === this.formData.variantId)
        // const selectedMessage = this.dbcMessageList.find(item => item.id === this.formData.messageId)
        const data = {
          ...this.formData,
          variantName: selectedVariant ? selectedVariant.code : ''
          // messageId: selectedMessage ? selectedMessage.name : ''
        }
        if (this.dialogStatus === 'add') {
          saveFtmDbcSignalLibrary(data).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
            this.this.formData.dbcFilePath = []
            this.formData.dbcFileId = []
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmDbcSignalLibrary(data).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
            this.this.formData.dbcFilePath = []
            this.formData.dbcFileId = []
          })
        }
      })
    },
    onDbcFileSelected(row) {
      debugger
      // this.formData.dbcFilePath = row.fileName
      this.formData.dbcFilePath = row.filePath
      this.formData.dbcFileId = row.id
      // this.getFtmDbcMessageList(row.id)
      // this.formData.messageId = ''
      this.$refs.formRef.validateField('dbcFileId')
    },
    deleteDbcFilePath() {
      this.formData.dbcFilePath = ''
      this.formData.dbcFileId = ''
      this.$refs.formRef.validateField('dbcFileId')
      // this.formData.messageId = ''
      // this.dbcMessageList = []
    }
    // getFtmDbcMessageList(dbcFileId) {
    //   if (!dbcFileId) return
    //   listFtmDbcMessage({ dbcId: dbcFileId }).then(res => {
    //     this.dbcMessageList = res.data
    //   })
    // }
  }
}
</script>

<style scoped lang="scss"></style>
