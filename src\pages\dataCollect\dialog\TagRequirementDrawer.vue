<template>
  <el-drawer
    custom-class="req-drawer-container"
    :title="$t('标签选择')"
    v-model="dialogVisible"
    direction="rtl"
    size="80%"
    append-to-body
    @close="dialogClosed"
  >
    <div class="drawer-body left">
      <div class="title">需求选择</div>
      <el-card class="requirement-list">
        <el-tag
          class="item"
          :type="isChecked(item) ? 'primary' : 'info'"
          :effect="isChecked(item) ? 'dark' : 'light'"
          v-for="item in requirementSelection"
          :key="item.id"
          @click="getRequirementTags(item)"
          >{{ item.name }}
        </el-tag>
      </el-card>
    </div>
    <div class="drawer-body">
      <el-divider direction="vertical" style="height: 100%" />
    </div>
    <div class="drawer-body right">
      <div class="title">已选标签</div>
      <el-scrollbar class="selected-container" v-loading="tagLoading">
        <tag-list :tagList="checkedTagList" @close="handleClose"></tag-list>
        <!----<div v-show="checkedTagList?.length > 0" style="margin-bottom: 20px">
          <el-card
            v-for="req in checkedTagList"
            :key="req.id"
            shadow="always"
            class="bs-tag-group-card"
            style="margin-bottom: 10px"
          >
            <template #header>
              <div class="bs-tag-group-card-header">
                <span>{{ req.name }}</span>
              </div>
            </template>
            <div class="bs-tag-group-card-body">
              <template v-for="group in req.children" :key="group.id">
                <el-card v-if="group.tagList?.length" style="margin-bottom: 10px">
                  <template #header>
                    <div class="bs-tag-group-card-header">
                      <span>{{ group.name }}</span>
                    </div>
                  </template>
                  <template v-for="(tag, tagIndex) in group.tagList" :key="tag.id">
                    <el-tag
                      :closable="true"
                      :type="checkTagType(tag)"
                      @close="handleClose(tagIndex, group.tagList)"
                      style="margin: 0 5px"
                    >
                      {{ tag.name }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-tag>
                  </template>
                </el-card>
              </template>
            </div>
          </el-card>
        </div>-->
        <!--        <el-empty v-show="checkedTagList?.length == 0" description="暂无选择标签"></el-empty>-->
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm" id="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-drawer>
</template>
<script>
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import BsTagClassificationSelection from '@/pages/dataCollect/components/BsTagClassificationSelection.vue'
import { listDmRequirementsPublishedSelection, getRequirementTags } from '@/apis/data-collect/daq-task-record'
import LtwIcon from '@/components/base/LtwIcon.vue'
import TagList from '../components/TagList.vue'

export default {
  name: 'RequirementTagDrawer',
  emits: ['reload'],
  props: {},
  components: {
    LtwIcon,
    BsTagGroupPanel,
    BsTagClassificationSelection,
    TagList
  },
  data() {
    return {
      dialogVisible: false,
      checkedTagList: [],
      requirementSelection: [],
      tagLoading: false
    }
  },
  created() {},
  methods: {
    show(row) {
      this.checkedTagList = JSON.parse(JSON.stringify(row.dataList || []))

      this.listDmRequirementsPublishedSelection()
      this.dialogVisible = true
    },
    listDmRequirementsPublishedSelection() {
      listDmRequirementsPublishedSelection().then(res => {
        res.data.forEach(val => {
          val.requirementCode = val.code
        })
        this.requirementSelection = res.data
      })
    },
    getRequirementTags(item) {
      if (this.isChecked(item)) {
        let reqIndex = this.checkedTagList.findIndex(val => val.id === item.id)
        this.checkedTagList.splice(reqIndex, 1)
      } else {
        this.tagLoading = true
        getRequirementTags({ reqCode: item.code }).then(res => {
          item.children = this.formatList(res.data)
          this.checkedTagList.push(item)
          this.tagLoading = false
        })
      }
    },
    formatList(list) {
      let children = []
      list.forEach(tag => {
        let groupIndex = children.findIndex(child => child.id === tag.tagGroupId)
        if (~groupIndex) {
          children[groupIndex].tagList.push({
            description: tag.description,
            classificationId: tag.reqId,
            classificationName: tag.name,
            id: tag.tagId,
            name: tag.tagName,
            code: tag.tagCode,
            nameCn: tag.tagName,
            type: tag.tagType,
            tagSamples: tag.tagSamples,
            followingDuration: tag.followingDuration,
            previousDuration: tag.previousDuration,
            supportTrigger: tag.supportTrigger,
            supportVoice: tag.supportVoice
          })
        } else {
          children.push({
            code: tag.tagGroupCode,
            id: tag.tagGroupId,
            name: tag.tagGroupName,
            nameCn: tag.tagGroupName,
            tagList: [
              {
                description: tag.description,
                classificationId: tag.reqId,
                classificationName: tag.name,
                id: tag.tagId,
                name: tag.tagName,
                nameCn: tag.tagName,
                code: tag.tagCode,
                type: tag.tagType,
                tagSamples: tag.tagSamples,
                followingDuration: tag.followingDuration,
                previousDuration: tag.previousDuration,
                supportTrigger: tag.supportTrigger,
                supportVoice: tag.supportVoice
              }
            ]
          })
        }
      })
      return children
    },
    isChecked(item) {
      if (this.checkedTagList?.length) {
        return ~this.checkedTagList.findIndex(val => val.code === item.code)
      }
    },

    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    handleClose(index, tagList) {
      tagList.splice(index, 1)
      this.$emit('requirementTagClose')
    },
    confirm() {
      this.dialogVisible = false
      // let checkedTagList = this.checkedTagList.map(req => {
      //   if (req.children?.length) {
      //     req.tagList = req.children.map(group => group.tagList).flat(Infinity)
      //   }
      //   return req
      // })
      // this.initForm()
      this.$emit('reload', this.checkedTagList)
    },
    initForm() {
      this.requirementSelection = []
    },
    dialogClosed() {
      this.checkedTagList = []
    }
  }
}
</script>
<style lang="scss">
.req-drawer-container {
  .el-drawer__header {
    margin-bottom: 0;
  }

  .el-drawer__body {
    height: calc(100% - 125px);
    padding-right: 10px;
    display: flex;
    flex-direction: row;

    .drawer-body {
      height: 100%;
      //overflow-y: auto;
    }

    .left {
      width: 500px;
    }

    .title {
      margin-bottom: 10px;
    }

    .requirement-list {
      .item {
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
      }
    }

    .right {
      width: calc(100% - 500px);

      .selected-container {
        height: calc(100% - 31px);
        //padding-top: 15px;
      }
    }
  }
}
</style>
