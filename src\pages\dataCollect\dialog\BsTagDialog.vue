<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="150px">
      <el-form-item :label="$t('编码')" prop="code" v-if="dialogStatus !== 'add'">
        <ltw-input v-model="formData.code" :disabled="dialogStatus !== 'add'" id="code"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="formData.name" :disabled="formReadonly" id="name"></ltw-input>
      </el-form-item>
      <!--      <el-form-item :label="$t('中文名称')" prop="nameCn">-->
      <!--        <ltw-input-->
      <!--          v-model="formData.nameCn"-->
      <!--          :disabled="true"-->
      <!--          id="nameCn"-->
      <!--        ></ltw-input>-->
      <!--      </el-form-item>-->
      <el-form-item :label="$t('描述')" prop="description">
        <ltw-input v-model="formData.description" :disabled="formReadonly" id="description"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('版本')" prop="version">
        <ltw-input v-model="formData.version" :disabled="formReadonly" id="description"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('类型')" prop="type">
        <dictionary-selection
          @change="bsTagTypeChange"
          ref="tagTypeSelectionRef"
          v-model="formData.type"
          dictionary-type="bs_tag_type"
          :disabled="formReadonly"
          id="type"
          size="small"
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('采集类型')" prop="acquisitionType">
        <dictionary-selection
          v-model="formData.acquisitionType"
          clearable
          dictionaryType="data_acquisition_type"
          :placeholder="$t('请选择采集类型')"
          :disabled="formReadonly"
          filterable
          size="small"
        />
      </el-form-item>
      <el-form-item :label="$t('使用场景')" prop="useCase">
        <dictionary-selection
          v-model="formData.useCase"
          clearable
          dictionaryType="tag_use_case"
          :placeholder="$t('请选择使用场景')"
          :disabled="formReadonly"
          filterable
          size="small"
        />
      </el-form-item>
      <el-form-item :label="$t('互斥的')" prop="mutuallyExclusive" v-if="continuous">
        <el-switch v-model="formData.mutuallyExclusive" :disabled="formReadonly" id="mutuallyExclusive"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('前置时长')" prop="previousDuration" v-if="!continuous">
        <el-input-number
          v-model="previousDurationSeconds"
          placeholder=""
          :min="0"
          :max="9999"
          :precision="0"
          controls-position="right"
          class="tag-duration-input"
          :disabled="formReadonly"
          id="previousDuration"
          @keydown="handleKeyDown"
        ></el-input-number>
        <unit-selection
          :data="unitList"
          :clearable="false"
          v-model="previousDurationUnit"
          default-selection="first"
          ref="previousDurationUnitSelection"
          :disabled="!continuous || formReadonly"
          :auto-load="false"
          id="unitList"
        ></unit-selection>
      </el-form-item>
      <el-form-item :label="$t('后置时长')" prop="followingDuration" v-if="!continuous">
        <el-input-number
          v-model="followingDurationSeconds"
          placeholder=""
          :min="0"
          :max="9999"
          :precision="0"
          controls-position="right"
          class="tag-duration-input"
          :disabled="formReadonly"
          id="followingDuration"
          @keydown="handleKeyDown"
        ></el-input-number>
        <unit-selection
          :data="unitList"
          :clearable="false"
          v-model="followingDurationUnit"
          default-selection="first"
          ref="followingDurationUnitSelection"
          :disabled="!continuous || formReadonly"
          :auto-load="false"
          id="unitList"
        ></unit-selection>
      </el-form-item>
      <el-form-item :label="$t('最小持续时长')" prop="minContinuousSeconds" v-if="continuous">
        <el-input-number
          v-model="minDuration"
          placeholder=""
          :min="0"
          :max="9999"
          :precision="0"
          controls-position="right"
          class="tag-duration-input"
          :disabled="formReadonly"
          id="minDuration"
          @keydown="handleKeyDown"
        ></el-input-number>
        <unit-selection
          :data="unitList"
          :clearable="false"
          v-model="minDurationUnit"
          default-selection="first"
          ref="minDurationUnitSelection"
          :disabled="formReadonly"
          :auto-load="false"
          id="unitList"
        ></unit-selection>
      </el-form-item>
      <el-form-item :label="$t('最大持续时长')" prop="maxContinuousSeconds" v-if="continuous">
        <el-input-number
          v-model="maxDuration"
          placeholder=""
          :min="0"
          :max="9999"
          :precision="0"
          controls-position="right"
          class="tag-duration-input"
          :disabled="formReadonly"
          id="maxDuration"
          @keydown="handleKeyDown"
        ></el-input-number>
        <unit-selection
          :data="unitList"
          :clearable="false"
          v-model="maxDurationUnit"
          default-selection="last"
          ref="maxDurationUnitSelection"
          :disabled="formReadonly"
          :auto-load="false"
          id="unitList"
        ></unit-selection>
      </el-form-item>
      <el-form-item :label="$t('是否有效')" prop="enabled" id="enabled">
        <el-switch v-model="formData.enabled" :disabled="formReadonly"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('是否支持语音')" prop="supportVoice" id="supportVoice">
        <el-switch v-model="formData.supportVoice" :disabled="formReadonly"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('是否触发录制')" prop="supportTrigger" id="supportTrigger">
        <el-switch v-model="formData.supportTrigger" :disabled="formReadonly || tagTypeSupportTrigger"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('是否POI')" prop="poi" id="poi">
        <el-switch v-model="formData.poi" :disabled="formReadonly"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('来源和准确率')" prop="sourceAccuracy" id="sourceAccuracy">
        <div class="aList">
          <el-row class="F-row">
            <el-col :span="10">
              <div class="f-title">{{ $t('来源') }}</div>
            </el-col>
            <el-col :span="10">
              <div class="f-title">{{ $t('准确率') }}</div>
            </el-col>
            <el-col :span="4" v-if="indexCount > 1 && dialogStatus !== 'view'">
              <div class="f-title" style="margin-left: 50px">操作</div>
            </el-col>
          </el-row>
          <el-row class="F-row" v-for="(item, index) in aList" :key="index">
            <el-col :span="10">
              <el-input
                v-model="item.source"
                style="width: 150px"
                :disabled="dialogStatus === 'view'"
                size="small"
              ></el-input>
            </el-col>
            <el-col :span="10">
              <el-input-number
                style="width: 50%"
                size="small"
                v-model="item.accuracy"
                :max="100"
                :min="0"
                :precision="2"
                :step="1"
                :disabled="dialogStatus === 'view'"
                controls-position="right"
                @keydown="handleKeyDown"
              />
              %
            </el-col>
            <el-col :span="4" v-if="dialogStatus !== 'view' && indexCount > 1">
              <el-button link type="primary" style="margin-left: 48px" size="small" @click="remove(item.index)">
                {{ $t('删除') }}
              </el-button>
            </el-col>
          </el-row>
          <el-button v-if="dialogStatus !== 'view'" style="width: 100%; margin-top: 5px" size="small" @click="addItem">
            {{ $t('新增') }}
            <el-icon class="el-icon--right">
              <CirclePlus />
            </el-icon>
          </el-button>
        </div>
      </el-form-item>
      <el-form-item :label="$t('用途')" prop="usage">
        <dictionary-selection
          v-model="formData.usage"
          dictionary-type="bs_tag_usage"
          :disabled="formReadonly"
          id="usage"
          size="small"
          clearable
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('关联类型')" prop="relatedType">
        <el-row class="button-group-container">
          <dictionary-selection
            v-model="formData.relatedType"
            dictionary-type="tag_related_type"
            :disabled="formReadonly"
            size="small"
            clearable
            @change="handleRelatedTypeChange"
          ></dictionary-selection>
          <el-button type="primary" size="small" @click="chooseTag" :disabled="formReadonly">{{ $t('选择标签') }} </el-button>
        </el-row>
        <el-row class="tag-container">
          <el-tag v-for="item in tagList" :key="item.code" :closable="!formReadonly"  @close="handleClose(item)">{{ item.name }}</el-tag>
        </el-row>
        <bs-tag-group-drawer
          :drawer-visible="tagDistributeDrawerVisible"
          :row-tag-list="rowTagList"
          :force-reload="true"
          @drawer-click="confirmDistributeTags"
          v-show="formData.relatedType === TAG_RELATED_TYPE.COMBINATION"
          ref="tagDrawerRef"
        ></bs-tag-group-drawer>
        <radio-bs-tag-group-drawer
          :drawer-visible="singleTagDistributeDrawerVisible"
          :row-tag-list="singleRowList"
          :tag-id="tagId"
          :force-reload="true"
          @drawer-click="confirmDistributeSingleTags"
          v-show="formData.relatedType === TAG_RELATED_TYPE.EXTEND"
          ref="radioTagDrawerRef"
        >
        </radio-bs-tag-group-drawer>
      </el-form-item>
      <el-form-item :label="$t('优先级')" prop="priority">
        <dictionary-selection
          v-model="formData.priority"
          dictionary-type="tag_priority_level"
          :disabled="formReadonly"
          id="priority"
          size="small"
        ></dictionary-selection>
      </el-form-item>
      <el-form-item :label="$t('匹配tag')" prop="mappingNewTagList">
        <el-row>
          <el-button type="primary" size="small" @click="mappingNewTag" v-if="checkBtnVisible('mappingNewTag') && editFlag">{{
            $t('匹配新标签')
          }}</el-button>
        </el-row>
        <el-row class="tag-container">
          <el-tag v-for="item in formData.mappingNewTagList" :key="item.code">{{ item.name }}</el-tag>
        </el-row>
      </el-form-item>
      <el-form-item :label="$t('关联示例图')">
          <LtwCarousel
            v-if="formData.dmImageSampleList?.length"
            class="sample-carousel"
            :data-list="formData.dmImageSampleList"
          >
            <template #default="{ data }">
              <div class="sample-carousel-item">
                <BsTagImageSampleCard
                  v-for="item in data"
                  :img-info="item"
                  :key="item"
                  class="sample-card"
                ></BsTagImageSampleCard>
              </div>
            </template>
          </LtwCarousel>
          <span v-else>-</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="warning" @click="edit" v-if="dialogStatus === 'view' && editFlag" id="edit">{{
          $t('编辑')
        }}</el-button>
        <el-button @click="dialogClosed" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="save" id="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
  <mapping-new-tag ref="mappingTagRef" @confirm="mappingTagConfirm"></mapping-new-tag>
</template>
<script>

import LtwCarousel from '@/components/base/LtwCarousel.vue'
import BsTagImageSampleCard from '../components/BsTagImageSampleCard.vue'
import { saveBsTag, updateBsTag } from '@/apis/data-collect/bs-tag'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { BS_TAG_TYPE, TAG_RELATED_TYPE } from '@/plugins/constants/data-dictionary'
import UnitSelection from '../../../components/system/UnitSelection.vue'
import { listSysUnit } from '@/apis/system/sys-unit'
import { ElMessage } from 'element-plus'
import { RemoveFilled, CirclePlus } from '@element-plus/icons-vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import { showToast } from '@/plugins/util'
import RadioBsTagGroupDrawer from '@/components/basic/RadioBsTagGroupDrawer.vue'
import MappingNewTag from '@/pages/dataCollect/components/MappingNewTag.vue'

const defaultFormData = {
  enabled: true,
  type: 'transient',
  mutuallyExclusive: true,
  supportVoice: false,
  supportTrigger: false
}

export default {
  components: {
    MappingNewTag,
    RadioBsTagGroupDrawer,
    BsTagGroupDrawer,
    DictionarySelection,
    UnitSelection,
    LtwCarousel,
    BsTagImageSampleCard,
  },
  name: 'BsTagDialog',
  emits: ['save'],
  props: {
    dataOperatePermission: {
      type: Object,
      default: {}
    }
  },
  data() {
    const checkMaxContinuousSeconds = (rule, value, callback) => {
      if (this.formData.minContinuousSeconds >= this.formData.maxContinuousSeconds) {
        callback(new Error('最大持续时间应大于最小持续时间'))
      } else {
        callback()
      }
    }
    return {
      tagTypeSupportTrigger: false,
      TAG_RELATED_TYPE: TAG_RELATED_TYPE,
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        groupId: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        tagStage: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        mutuallyExclusive: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        type: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        version: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        acquisitionType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        useCase: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        sortNum: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        enabled: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }],
        minContinuousSeconds: [],
        maxContinuousSeconds: [{ validator: checkMaxContinuousSeconds }]
      },
      dialogTitle: '',
      dialogStatus: '',
      minDurationUnit: '',
      maxDurationUnit: '',
      minDuration: 0,
      maxDuration: 9999,
      previousDurationUnit: '',
      followingDurationUnit: '',
      previousDurationSeconds: 0,
      followingDurationSeconds: 9999,
      unitList: [],
      RemoveFilled: RemoveFilled,
      CirclePlus: CirclePlus,
      indexCount: 1,
      aList: [],
      rowTagList: [],
      tagDistributeDrawerVisible: false,
      singleTagDistributeDrawerVisible: false,
      singleRowList: [],
      tagList: [],
      tagId: '1',
      editFlag: false,
      sampleImgList: [],
      imgFileIdList: [],
      defaultImgFileIdList: []
    }
  },
  computed: {
    BS_TAG_EQUIP_TYPE() {
      return BS_TAG_EQUIP_TYPE
    },
    formReadonly() {
      return this.dialogStatus === 'view'
    },
    continuous() {
      if (this.formData.type !== BS_TAG_TYPE.CONTINUOUS) {
        this.formData.mutuallyExclusive = false
      }
      return this.formData.type === BS_TAG_TYPE.CONTINUOUS
    }
  },
  created() {
    this.listUnit()
  },
  methods: {
    bsTagTypeChange(node) {
      if (BS_TAG_TYPE.CONTINUOUS === node.value) {
        this.tagTypeSupportTrigger = true
      } else {
        this.tagTypeSupportTrigger = false
      }
    },
    handleClose(item){
      const index = this.tagList.findIndex(obj => obj.id === item.id)
      if(index!== -1){
         this.tagList.splice(index, 1)
      }
      if(this.formData.relatedType !== TAG_RELATED_TYPE.COMBINATION){
          this.tagId = ''
       }
    },
    handleKeyDown(event) {
      if (event.key === 'e') {
        event.preventDefault()
      }
    },
    dialogOpened() {
      // this.$refs.formRef?.resetFields()
    },
    dialogClosed() {
      this.indexCount = 1
      this.aList = []
      this.rowTagList = []
      this.singleRowList = []
      this.tagList = []
      this.sampleImgList = []
      this.imgFileIdList = []
      this.defaultImgFileIdList = []
      this.dialogVisible = false
      this.initForm()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      if (this.continuous) {
        //持续
        this.minDuration = 0
        this.maxDuration = 9999
        this.minDurationUnit = ''
        this.maxDurationUnit = ''
        this.$nextTick(() => {
          this.$refs.minDurationUnitSelection?.initialize()
          this.$refs.maxDurationUnitSelection?.initialize()
        })
      } else {
        //瞬时
        this.previousDurationSeconds = 5
        this.followingDurationSeconds = 10
        this.previousDurationUnit = ''
        this.followingDurationUnit = ''
        this.$nextTick(() => {
          this.$refs.previousDurationUnitSelection?.initialize()
          this.$refs.followingDurationUnitSelection?.initialize()
        })
      }
      this.formData = Object.assign({}, defaultFormData)
    },
    add(defaultData) {
      this.dialogTitle = this.$t('增加')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.$nextTick(() => {
        // this.initForm()
        this.formData = Object.assign(this.formData, defaultData)
      })
    },
    save() {
      if (this.continuous) {
        this.formData.minContinuousSeconds =
          this.$refs.minDurationUnitSelection.getNode(this.minDurationUnit).conversion * this.minDuration
        this.formData.maxContinuousSeconds =
          this.$refs.maxDurationUnitSelection.getNode(this.maxDurationUnit).conversion * this.maxDuration
      } else {
        this.formData.previousDuration =
          this.$refs.previousDurationUnitSelection.getNode(this.previousDurationUnit).conversion *
          this.previousDurationSeconds
        this.formData.followingDuration =
          this.$refs.followingDurationUnitSelection.getNode(this.followingDurationUnit).conversion *
          this.followingDurationSeconds
      }
      this.formData.nameCn = this.formData.name
      let list = []
      this.aList.forEach(item => {
        let acc = item.accuracy ? item.accuracy : ''
        if (item.source) {
          list.push(item.source + ':' + acc)
        } else {
          ElMessage.error(this.$t('请输入') + this.$t('来源'))
          list = false
        }
      })
      if (!list) {
        return false
      }
      this.formData.sourceAccuracy = list.toString()
      this.formData.nameCn = this.formData.name
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        this.formData.relatedTagList = this.tagList
        if (this.formData.relatedType === TAG_RELATED_TYPE.COMBINATION && this.tagList?.length < 2) {
          return showToast('组合关联类型需要选择两个及以上的标签', 'warning')
        }
        // 处理示例图，匹配没有修改的示例图，增加新的示例图
        const remainSampleImgList = this.sampleImgList.filter(item => {
          return this.imgFileIdList.includes(item.fileId)
        })
         const newSampleImgList =
           (Array.isArray(this.imgFileIdList) ? this.imgFileIdList : [])
         .filter(fileId => {
      return !remainSampleImgList.some(item => item.fileId === fileId)
    })
    .map(fileId => {
      return {
        fileId: fileId,
        relatedType: 'tag',
        sourceType: 'DATA_PLATFORM'
      }
    }) || []

        this.formData.dmImageSampleList = [...remainSampleImgList, ...newSampleImgList]

        if (this.dialogStatus === 'add') {
          saveBsTag(this.formData).then(res => {
            this.dialogVisible = false
            this.$emit('save', res.data)
          })
        }
        if (this.dialogStatus === 'edit') {
          updateBsTag(this.formData).then(res => {
            this.dialogVisible = false
            this.$emit('save', this.formData)
          })
        }
      })
    },
    edit() {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
    },
    view(row, editFlag) {
      this.editFlag = editFlag
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      this.formData = row
      // 示例图字段处理
      this.formData.dmImageSampleList.forEach(val => {
        val.sourceTypeName = val.sourceType?.toLowerCase().split('_').join(' ')
      })
      this.sampleImgList = [...this.formData.dmImageSampleList]
      this.imgFileIdList = this.formData.dmImageSampleList.map(item => {
        return item.fileId
      })
      this.defaultImgFileIdList = this.formData.dmImageSampleList.map(item => {
        return item.id
      })
      // 两两分组
      this.formData.dmImageSampleList = this.formData.dmImageSampleList.reduce((acc, cur, index) => {
        if (index % 2 === 0) {
          acc.push([cur])
        } else {
          acc[acc.length - 1].push(cur)
        }
        return acc
      }, [])
      this.aList = []
      if (row.sourceAccuracy) {
        const splitArr = row.sourceAccuracy.split(',')
        for (let i = 0; i < splitArr.length; i++) {
          if (splitArr[i].split(':')[0]) {
            this.aList.push({
              index: i,
              source: splitArr[i].split(':')[0],
              accuracy: splitArr[i].split(':')[1]
            })
          }
        }
        this.indexCount = splitArr.length
      }
      if (this.continuous) {
        this.formData.minContinuousSeconds ??= 0
        this.formData.maxContinuousSeconds ??= 0
        let minUnitNode = this.calculateOptimalNode(this.formData.minContinuousSeconds)
        let maxUnitNode = this.calculateOptimalNode(this.formData.maxContinuousSeconds)
        this.minDurationUnit = minUnitNode.id
        this.maxDurationUnit = maxUnitNode.id
        this.minDuration = this.formData.minContinuousSeconds / minUnitNode.conversion
        this.maxDuration = this.formData.maxContinuousSeconds / maxUnitNode.conversion
        this.tagTypeSupportTrigger = true
      } else {
        this.formData.previousDuration ??= 0
        this.formData.followingDuration ??= 0
        let previousDurationUnitNode = this.calculateOptimalNode(this.formData.previousDuration)
        let followingDurationUnitNode = this.calculateOptimalNode(this.formData.followingDuration)
        this.previousDurationUnit = previousDurationUnitNode.id
        this.followingDurationUnit = followingDurationUnitNode.id
        this.previousDurationSeconds = this.formData.previousDuration / previousDurationUnitNode.conversion
        this.followingDurationSeconds = this.formData.followingDuration / followingDurationUnitNode.conversion
        this.tagTypeSupportTrigger = false
      }
      this.tagList = row.relatedTagList
      this.tagId = row.relatedTagList?.[0]?.id || ''
      this.dialogVisible = true
    },
    calculateOptimalNode(value) {
      if (value === 0) {
        return this.unitList[0]
      }
      for (let i = this.unitList.length - 1; i >= 0; i--) {
        let node = this.unitList[i]
        if (value % node.conversion === 0) {
          return node
        }
      }
    },
    listUnit() {
      listSysUnit({ typeCode: 'tag_duration', enabled: true }).then(res => {
        this.unitList = res.data
      })
    },
    remove(index) {
      this.aList = this.aList.filter(x => x.index !== index)
    },
    addItem() {
      this.aList.push({
        index: this.indexCount
      })
      this.indexCount = this.indexCount + 1
    },
    handleRelatedTypeChange(val) {
      this.tagList = []
      this.singleRowList = this.rowTagList = this.tagList || []
      this.tagId = ''
      this.tagDistributeDrawerVisible = false
    },
    chooseTag() {
      if (!this.formData.relatedType) return showToast('请先选择关联类型', 'warning')
      if (this.formData.relatedType === TAG_RELATED_TYPE.COMBINATION) {
        this.rowTagList = this.tagList
        this.tagDistributeDrawerVisible = true
        // this.$nextTick(()=>{
        //   this.$refs.tagDrawerRef.refreshTagGroup()
        // })


      } else {
        this.singleRowList = this.tagList
        this.singleTagDistributeDrawerVisible = true
        // this.$nextTick(()=>{
        //   this.$refs.radioTagDrawerRef.refreshTagGroup()
        // })

      }
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      this.tagDistributeDrawerVisible = false
    },
    confirmDistributeSingleTags(data) {
      this.tagId = data?.tagItem?.id
      if (data?.tagItem?.id) {
        this.tagList = [data?.tagItem]
      }
      this.singleTagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    checkBtnVisible(code) {
      return Object.keys(this.dataOperatePermission).includes(code)
    },
    mappingNewTag() {
      this.$refs.mappingTagRef.show(this.formData.mappingNewTagList, this.formData.mappingNewTag)
    },
    mappingTagConfirm(formData) {
      this.dialogStatus = 'edit'
      this.formData.mappingNewTagList = formData.mappingNewTagList
      this.formData.mappingNewTag = formData.mappingNewTag
    }
  }
}
</script>
<style lang="scss" scoped>
.tag-duration-input {
  width: 200px;
}

.unit-selection {
  width: 200px;
  margin-left: 15px;
}

.aList {
  width: 100%;
  padding: 0 10px;
}

.f-title {
  font-size: 12px;
}

.button-group-container {
  width: 100%;

  .el-button {
    margin-left: 10px;
  }
}
.el-row {
  width: 100%;
}
.tag-container {
  margin-top: 10px;

  .el-tag {
    margin: 0 3px;
  }
}
.sample-carousel {
  height: 316px;
  .sample-carousel-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .sample-card {
      width: 49%;
    }
  }
}
</style>