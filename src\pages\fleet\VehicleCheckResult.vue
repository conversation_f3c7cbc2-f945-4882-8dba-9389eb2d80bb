<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="container">
        <div class="left-container">
          <el-card>
            <div class="ltw-toolbar">
              <div>
                <el-button @click="filterClick" id="filter-button">{{ $t('筛选') }}</el-button>
              </div>
            </div>
            <el-table
              :highlight-current-row="true"
              :data="displayTableData"
              :row-key="getRowKeys"
              :max-height="maxTableHeight"
              style="overflow: auto"
              @row-click="handleRowClick"
              ref="tableRef"
              class="table-container"
              :data-size="pageData.records?.length"
              :load-more-total="pageData.records?.length"
              :load-more-selector="'table tr'"
              :reload="!unloading"
              v-load-more="handelLoadmore"
            >
              <el-table-column header-align="left" align="left" label="车辆" prop="vin" />
              <el-table-column header-align="left" align="left" label="数据包" prop="measurementStr" />
              <el-table-column header-align="left" align="left" label="状态">
                <template #default="scope">
                  <div v-if="isAllStatusTrue(scope.row.checkStatusList)">
                    <!--                    <el-tag effect="dark" type="success">正常</el-tag>-->
                    <el-link class="status-icon" :underline="false" type="success">
                      <ltw-icon icon-code="el-icon-success-filled"></ltw-icon>
                    </el-link>
                  </div>
                  <div v-else>
                    <el-tag
                      style="margin-right: 5px"
                      effect="dark"
                      type="danger"
                      v-for="item in getFailedStatusItems(scope.row.checkStatusList)"
                      :key="item.type"
                    >
                      {{ item.type }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
        <div class="right-container">
          <template v-if="itemData && itemData.length > 0">
            <el-card v-for="(tableData, index) in itemData" :key="index" style="margin-bottom: 10px">
              <el-tag effect="dark" :type="tableData.status ? 'success' : 'danger'" style="margin-right: 5px">
                {{ tableData.type }}
              </el-tag>
              <!--              <el-tag effect="dark" :type="tableData.status ? 'success' : 'danger'"-->
              <!--                >{{ tableData.status ? '正常' : '异常' }}-->
              <!--              </el-tag>-->
              <el-table :data="tableData.details" :max-height="maxTableHeight">
                <el-table-column header-align="center" align="center" type="index" width="55"></el-table-column>
                <el-table-column prop="name" label="检查项名称" width="150px"></el-table-column>
                <el-table-column prop="startTime" label="开始时间" width="120px"></el-table-column>
                <el-table-column prop="endTime" label="结束时间" width="120px"></el-table-column>
                <el-table-column prop="actualFrameNum" label="ActualFrameNum" width="120px"></el-table-column>
                <el-table-column prop="countFrameNum" label="countFrameNum" width="120px"></el-table-column>
                <el-table-column prop="delay" label="delay"></el-table-column>
                <el-table-column prop="frequency" label="frequency"></el-table-column>
                <el-table-column prop="lackRateStandard" label="lackRateStandard" width="120px"></el-table-column>
                <el-table-column prop="continueLackNum" label="continueLackNum" width="125px"></el-table-column>
                <el-table-column prop="lossRate" label="lossRate" width="230px"></el-table-column>
                <el-table-column align="left" label="状态" prop="status" fixed="right">
                  <template #default="scope">
                    <span v-if="scope.row.status === '' || scope.row.status === null"></span>

                    <el-link v-else-if="scope.row.status" class="status-icon" :underline="false" type="success">
                      <ltw-icon icon-code="el-icon-success-filled"></ltw-icon>
                    </el-link>
                    <el-link v-else class="status-icon" :underline="false" type="danger">
                      <ltw-icon icon-code="el-icon-circle-close-filled"></ltw-icon>
                    </el-link>
                    <!--                    <el-tag v-else effect="dark" :type="scope.row.status ? 'success' : 'danger'"-->
                    <!--                      >{{ scope.row.status ? '正常' : '异常' }}-->
                    <!--                    </el-tag>-->
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </template>
        </div>
      </div>
    </el-card>
    <check-result-filter v-model="queryParam" @filter="refresh" ref="filterRef"></check-result-filter>
  </div>
</template>

<script>
import util, { checkTagType, checkFileSize, numUtils, debounce, getLocale } from '@/plugins/util'
import { getRawDataInfo } from '@/apis/data-collect/raw-data-bag'
import { queryCheckResult, getCheckDetail } from '@/apis/data-collect/vehicle_check_data'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import LoadMore from '@/directives/loadMore'
import CheckResultFilter from '@/pages/fleet/dialog/CheckResultFilter.vue'

const defaultFormData = {}
export default {
  components: {
    CheckResultFilter
  },
  name: 'VehicleCheckResult',
  data() {
    return {
      numUtils: numUtils,
      checkFileSize: checkFileSize,
      checkTagType: checkTagType,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      locale: getLocale(),
      //选择标签
      tabTypes: [],
      enableRadioData: [
        { label: true, nameCn: '有效数据', name: 'valid data' },
        { label: false, nameCn: '无效数据', name: 'invalid data' }
      ],
      maxTableHeight: window.innerHeight - 275,
      //page
      pageData: {
        pages: 0,
        total: 0,
        records: []
      },
      // itemData: {
      //   details: [],
      //   status:"",
      //   type:" ",
      // },
      itemData: [],
      checkStatusList: [],
      recordsList: [],
      queryParam: {
        current: 1,
        size: 20
      },
      unloading: false, //loading pannel
      isloading: true, //懒加载
      typeList: [],
      scenarioList: [],
      scenarioProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        emitPath: false,
        multiple: false
      },
      vehicleList: [],
      vehicleInfo: Object.assign({}, defaultFormData),
      fileTypeList: [],
      measureTime: undefined,
      taskCode: undefined,
      modalities: undefined,
      modalityList: [],
      taskCodeList: [],
      typeCodeList: [],
      rowTagList: [],
      tagDistributeDrawerVisible: false,
      tagList: [],
      currentStartIndex: 0,
      currentEndIndex: 20,
      initPageLength: null,
      //filter
      filterDialogVisible: false,
      //view
      detailDialogTitle: '',
      dialogStatus: '',
      detailData: Object.assign({}, defaultFormData),
      datasetSelections: [],
      allChecked: false,
      nodeId: '',
      selectRow: null,
      selectedRowData: {}
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    const isCollapseMenu = this.$route?.query?.isCollapseMenu,
      showBackButton = this.$route?.query?.showBackButton,
      vin = this.$route?.query?.vin
    if (vin) {
      this.queryParam.vin = vin
    }
    if (isCollapseMenu === '1') {
      this.$store.commit('setCollapsed', isCollapseMenu === '1' ? true : false)
    }
    if (showBackButton === '1') {
      this.$store.commit('setBackButton', showBackButton === '1' ? true : false)
    }
    this.query()
    window.onresize = debounce(this.getTableHeight)
    window.onresize = debounce(this.getLeftTableHeight)
    this.getTableHeight()
  },
  mounted() {},
  computed: {
    displayTableData() {
      return this.pageData?.records?.filter((item, index) => {
        if (index < this.currentStartIndex || index > this.currentEndIndex) {
          return false
        } else {
          return true
        }
      })
    }
  },
  methods: {
    isAllStatusTrue(checkStatusList) {
      return checkStatusList.every(item => item.status === true)
    },
    getFailedStatusItems(checkStatusList) {
      return checkStatusList.filter(item => item.status === false)
    },
    handleRowClick(row) {
      this.id = JSON.parse(JSON.stringify(row.id))
      this.selectRow = row
      this.selectedRowData = JSON.parse(JSON.stringify(row))
      this.queryItem()
    },
    setCurrentRow(row) {
      if (row) {
        this.$refs.tableRef.setCurrentRow(row, true)
      } else {
        this.$refs.tableRef.setCurrentRow()
      }
    },
    queryItem() {
      getCheckDetail({ id: this.id }).then(res => {
        this.itemData = res.data
      })
    },
    getTableHeight() {
      let tableH = 275
      this.maxTableHeight = window.innerHeight - tableH
    },
    getRowKeys(row) {
      return row.id
    },
    handelLoadmore(currentStartIndex, currentEndIndex) {
      this.currentStartIndex = currentStartIndex
      this.currentEndIndex = currentEndIndex
      if (
        currentEndIndex > this.pageData?.records?.length - this.initPageLength &&
        this.isloading === true &&
        this.pageData.hasNextPage
      ) {
        this.isloading === false
        this.queryParam.current += 1
        this.query()
      }
    },
    //enable data
    handleEnableChange(label) {
      this.refresh()
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.unloading = false
      this.queryParam.current = 1
      this.queryParam.size = 20
      this.queryParam.idList = undefined
      this.query()
    },
    priviewTags(tagsArray) {
      let tagList = []
      if (tagsArray.tags) {
        tagsArray.tags.map(val => {
          let index = tagList.findIndex(item => item.tagId === val.tagId)
          if (!~index) {
            tagList.push(val)
          }
        })
      }
      this.tagGroupList = tagList
      return this.tagGroupList
    },
    query() {
      queryCheckResult(this.queryParam, {}, this.unloading).then(res => {
        if (this.unloading === false) {
          util.loadingShow()
        }
        let pageData = res.data.records || []
        pageData.forEach(item => {
          item.tags = this.priviewTags(item)
        })
        this.pageData.records = this.queryParam.current === 1 ? pageData : this.pageData.records.concat(pageData)
        this.pageData.pages = res.data.pages
        this.pageData.total = res.data.total
        if (this.pageData.records.length > 0) {
          this.$refs.tableRef.setCurrentRow(this.displayTableData[0])
          this.handleRowClick(this.displayTableData[0])
        } else {
          this.itemData = []
        }
        this.pageData.hasNextPage = res.data.hasNextPage
        this.recordsList = JSON.stringify(this.pageData.records)
        this.initPageLength = this.pageData?.records?.length < 20 ? this.pageData?.records?.length / 2 : 10
        this.$nextTick(() => {
          if (this.unloading === false) {
            util.loadingHide()
            this.unloading = true
          }
        })
        this.isloading = true
      })
    },
    //rawdata view
    dataDetailView(row) {
      getRawDataInfo(row.id).then(res => {
        this.detailDialogTitle = row.name
        this.dialogStatus = 'view'
        this.detailData = res.data
        this.$refs.detailRef.show()
      })
    },
    filterClick() {
      this.$refs.filterRef.show()
    },
    //filter
    handleMeasureTimeChange(val) {
      this.queryParam.measureTime = val
    },
    measureTimeFormat(e) {
      e.stopPropagation()
      if (this.queryParam.startTime) {
        this.queryParam.measureTime = []
        this.queryParam.measureTime.push(this.queryParam.startTime, this.queryParam.endTime)
      }
    },
    measureTimeConfirm() {
      this.queryParam.startTime = this.queryParam.measureTime && this.queryParam.measureTime[0]
      this.queryParam.endTime = this.queryParam.measureTime && this.queryParam.measureTime[1]
      delete this.queryParam.measureTime
      this.$refs.measureTimeRef.hide()
      this.refresh()
    },
    listVehicle() {
      listBsVehicle().then(res => {
        this.vehicleList = res.data
        if (this.vehicleList && this.vehicleList.length > 0) {
          this.vehicleMap = {}
          this.vehicleList.forEach(item => {
            this.vehicleMap[item.id] = item
          })
        }
      })
    },
    vinConfirm() {
      this.$refs.vehcileRef.hide()
      this.pageData = Object({}, defaultFormData)
      this.unloading = false
      this.refresh()
    },
    getVehicle(vin) {
      listBsVehicle({ vin: vin }).then(res => {
        this.vehicleInfo = res.data[0]
      })
    }
  },
  directives: {
    LoadMore
  }
}
</script>
<style>

.popover-container > .el-form {
  max-height: 300px;
  overflow: auto;
}

.popover-container > .popover-footer {
  display: flex;
  justify-content: flex-end;
}

.popover-container > .popover-footer > .clear-button {
  color: #606266;
}

.measurement-popover {
  position: relative;
}

.table-tag > .el-tag__content {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.cascader-container > .el-cascader-menu__list {
  max-height: 300px;
  overflow: auto;
}

.cascader-container > .el-cascader-panel {
  max-height: 300px;
  overflow: auto;
}

.tag-popover {
  max-height: 400px;
  overflow-y: auto;
}

.tag-popover::-webkit-scrollbar {
  width: 5px;
  height: 0;
}

.tag-popover::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.tag-popover::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}

.table-name {
  display: inline-block;
}

.data-count {
  margin-left: 5px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: 32px;
  white-space: nowrap;
}

.el-table.is-scrolling-left th.el-table-fixed-column--left {
  background-color: #f2f3f4;
}

.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background-color: #f2f3f4;
}
</style>
<style lang="scss" scoped>
.container {
  display: flex;
}

.left-container {
  width: 30%;
  margin-right: 10px;
  // flex: 2;
  // padding: 20px;
}

.right-container {
  width: 70%;
  // flex: 3;
  // padding: 20px;
}

.el-card {
  // max-height: calc(100vh - 140px);
  :deep(.el-card__body) {
    height: 100%;

    .ltw-toolbar {
      display: flex;
      justify-content: space-between;
      color: #606266;
      font-size: 14px;

      .button-left {
        align-items: center;
        font-size: 12px;

        .data-count {
          display: flex;

          .statistic-container {
            height: 32px;
            display: flex;
            align-items: center;
            border-right: 1px solid #ebeef5;
            padding-right: 10px;
            margin-right: 10px;

            .icon {
              font-size: 40px;
              color: #409eff;
              margin-right: 10px;
              margin-top: 5px;
            }

            .bag-icon {
              color: #e6a23c;
            }

            .time-icon {
              color: #f56e6e;
            }

            .size-icon {
              color: #67c23a;
            }

            .count {
              font-weight: 500;
              font-size: 14px;
              color: #909399;

              div {
                display: flex;
                justify-content: flex-end;
              }

              div:nth-child(2) {
                color: #409eff;
                font-weight: 600;
              }
            }
          }
        }
      }

      .button-right {
        display: flex;
      }
    }

    .el-table {
      overflow: auto;

      .select-form {
        max-height: 300px;
        overflow: auto;
      }

      .table-header {
        cursor: pointer;

        & > .el-button {
          border: none;
          background: none;
          padding-left: 5px;
          height: 23px;
        }

        & > span:nth-child(1) {
          width: 40px;
          line-height: 23px;
        }

        & > span > .el-button {
          border: none;
          background: none;
          padding: 0;
          height: 23px;
          padding-left: 5px;
        }
      }

      .active {
        color: #409eff;
      }

      .default {
        color: #606266;
      }

      .table-tag {
        margin-right: 1px;
        width: 22%;
        cursor: pointer;
      }
    }
  }

  .status-icon {
    font-size: 18px;
  }
}
</style>
