import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDiskDeliveryRecord = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records',
    data,
    params
})
export const updateFtmDiskDeliveryRecord = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records',
    data,
    params
})
export const deleteFtmDiskDeliveryRecord = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records',
    params
})
export const listFtmDiskDeliveryRecord = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records',
    params
})
export const listFtmDiskDeliveryRecordSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/selections',
    params
})
export const pageFtmDiskDeliveryRecord = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/page',
    params
})
export const getFtmDiskDeliveryRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/' + id})
