<template>
  <el-table :data="formData.childTagGroupList" highlight-current-row ref="tabRef" lazy max-height="200">
    <el-table-column :label="$t('编码')" prop="code">
      <template #default="scope">
        <el-input v-model="scope.row.code" :disabled="formReadonly" maxlength="32"></el-input>
      </template>
    </el-table-column>
    <el-table-column :label="$t('名称')" prop="name">
      <template #default="scope">
        <el-input v-model="scope.row.name" :disabled="formReadonly" maxlength="32"></el-input>
      </template>
    </el-table-column>
    <el-table-column :label="$t('标签数量')" prop="tagList">
      <template #default="scope">
        <el-link @click="getTags(scope.row)" style="margin: 0 20px" type="primary" :underline="false"
          >{{ (scope.row.tagList && scope.row.tagList.length) || 0 }}
        </el-link>
        <el-button @click="chooseChildTags(scope.row)" type="primary" id="tag" :disabled="formReadonly">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
        </el-button>
      </template>
    </el-table-column>
    <el-table-column :label="$t('操作')">
      <template #default="scope">
        <el-button-group>
          <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
            <el-button type="danger" :disabled="formReadonly" @click="handleDeleteChild(scope.row)">
              <ltw-icon icon-code="el-icon-delete"></ltw-icon>
            </el-button>
          </el-tooltip>
        </el-button-group>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog v-model="tagCardVisible">
    <bs-tag-group-panel
      :continuous-units="continuousUnits"
      :edit-tags="editTags"
      :timeEdit="timeEdit"
      :data="tagsData"
      @tagClose="tagClose"
      @tagSave="tagSave"
      :classificationTag="true"
    ></bs-tag-group-panel>
    <!-- <tag-group-panel :data="tagsData"></tag-group-panel> -->
  </el-dialog>
  <bs-tag-group-drawer
    @drawerClick="confirmDistributeTags"
    :enabled="true"
    :drawerVisible="tagChooseDrawerVisible"
    :rowTagList="rowTagList"
  ></bs-tag-group-drawer>
</template>

<script>
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'

export default {
  name: 'TagChildTable',
  components: {
    BsTagGroupDrawer,
    BsTagGroupPanel
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    formReadonly: {
      type: Boolean,
      default: false
    },
    treeTagGroupList: {
      type: Array,
      required: true
    },
    status: {
      type: String,
      default: 'view'
    }
  },

  data() {
    return {
      currentChild: null,
      tagCardVisible: false,
      tagChooseDrawerVisible: false,
      rowTagList: [],
      continuousUnits: [],
      tagsData: [],
      editTags: false,
      timeEdit: false,
      acquisitionTypeList: []
    }
  },
  methods: {
    tagSave(dataList) {
      let arr = []
      if (dataList?.length > 0) {
        arr = dataList
          .map(item => {
            return item.children
          })
          .flat(Infinity)
          .map(tag => {
            return tag.tagList
          })
          .flat(Infinity)
        arr.map((val, index) => {
          val.sortNum = index
        })
        this.currentChild.tagList = arr
      }
      this.tagCardVisible = false
    },
    tagClose() {
      this.tagCardVisible = false
    },
    chooseChildTags(child) {
      this.tagChooseDrawerVisible = true
      this.currentChild = child
      this.rowTagList = child.tagList || []
    },
    getTags(child) {
      this.currentChild = child
      if (this.status === 'view') {
        this.editTags = false
        this.timeEdit = false
      } else {
        this.editTags = true
        this.timeEdit = true
      }
      this.tagsData = this.setCheckedTag(child.tagList)
      this.tagCardVisible = true
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.currentChild.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      this.tagChooseDrawerVisible = false
      //找到标签组的父节点
      this.currentChild?.tagList?.length > 0 && this.getTags(this.currentChild)
    },
    setCheckedTag(tagList) {
      let groupArr = []
      let tagObj = this.groupBy(tagList, 'groupId')
      for (let item in tagObj) {
        let parentGroupName = this.getParentNode(this.treeTagGroupList, item)
        let index = groupArr.findIndex(group => {
          return group.nameCn === parentGroupName
        })
        if (index < 0) {
          groupArr.push({
            nameCn: parentGroupName,
            asLeaf: false,
            children: [
              {
                nameCn: tagObj[item][0].groupNameCn,
                id: item,
                tagList: tagObj[item],
                asLeaf: true
              }
            ]
          })
        } else {
          groupArr[index].children.push({
            nameCn: tagObj[item][0].groupNameCn,
            id: item,
            tagList: tagObj[item],
            asLeaf: true
          })
        }
      }
      return groupArr
    },
    groupBy(array, prop) {
      return array.reduce((cur, pre) => {
        let key = pre[prop]
        if (!cur[key]) {
          cur[key] = []
        }
        cur[key].push(pre)
        return cur
      }, {})
    },
    getParentNode(tree, childId) {
      let parentInfo
      for (let node of tree) {
        // 如果当前节点就是目标节点的父节点，直接返回当前节点
        if (node.children && node.children.some(child => child.id === childId)) {
          return node.nameCn
        }
        // 否则继续遍历当前节点的子节点
        if (node.children) {
          parentInfo = this.getParentNode(node.children, childId)
          if (parentInfo !== null) {
            return parentInfo
          }
        }
      }
      return null
    },
    saveCheckedTagList(group) {
      //遍历查询选中的标签
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.currentChild.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    // 删除子标签分类
    handleDeleteChild(row) {
      this.$confirm('确认删除该子标签分类吗？', {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        this.formData.childTagGroupList = this.formData.childTagGroupList.filter(item => item !== row)
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
