import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysRoleCustomActor = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors', data, params})
export const updateSysRoleCustomActor = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors', data, params})
export const deleteSysRoleCustomActor = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors', params})
export const listSysRoleCustomActor = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors', params})
export const listSysRoleCustomActorSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors/selections', params})
export const pageSysRoleCustomActor = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors/page', params})
export const getSysRoleCustomActor = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors/' + id})
export const assignApplications = (data = [], params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors/applications', data, params})
export const assignMenus = (data = [], params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_custom_actors/menus', data, params})
