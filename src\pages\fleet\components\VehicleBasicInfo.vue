<template>
  <div class="vehicle-basic-info">
    <!-- 编辑按钮 -->
    <div class="edit-button-container">
      <el-button
        :type="isEditing ? 'success' : 'warning'"
        size="small"
        @click="toggleEditMode"
        class="edit-button"
      >
        <ltw-icon :icon-code="isEditing ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>{{ isEditing ? '保存' : '编辑' }}
      </el-button>
      <el-button
        v-if="isEditing"
        type="info"
        size="small"
        @click="cancelEditMode"
        class="cancel-button"
      >
        <ltw-icon icon-code="el-icon-close"></ltw-icon>取消
      </el-button>
    </div>

    <el-form
      ref="vehicleForm"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      class="form-container"
    >
      <!-- 基本信息区域 -->
      <div class="form-section">
        <el-form-item label="博世编号：" prop="vin" class="form-row-item">
          <ltw-input v-if="isEditing" v-model="formData.vin" placeholder="请输入车架号" />
          <span v-else class="form-value-display">{{ vehicle.vin || '-' }}</span>
        </el-form-item>
        <el-form-item label="负责人：" prop="keeperEmpName" class="form-row-item">
             <employee-selection v-if="isEditing"
                        style="width: 100%"
                        v-model="formData.keeperEmpId"
                        id="keeperEmpId"
                        @change="changeKeeperEmp"
                    />

          <span v-else class="form-value-display">{{ vehicle.keeperEmpName || '-' }}</span>
        </el-form-item>
        <el-form-item label="出厂车架号：" prop="factoryVin" class="form-row-item">
          <ltw-input v-if="isEditing" v-model="formData.factoryVin" placeholder="请输入出厂车架号" />
          <span v-else class="form-value-display">{{ vehicle.factoryVin || '-' }}</span>
        </el-form-item>
      </div>

      <!-- 车辆参数区域 -->
      <div class="form-section">
        <el-form-item label="外部编号：" prop="externalVin" class="form-row-item">
          <ltw-input v-if="isEditing" v-model="formData.externalVin" placeholder="请输入外部编号" />
          <span v-else class="form-value-display">{{ vehicle.externalVin || '-' }}</span>
        </el-form-item>
        <el-form-item label="车牌：" prop="license" class="form-row-item">
          <ltw-input v-if="isEditing" v-model="formData.license" placeholder="请输入车牌" />
          <span v-else class="form-value-display">{{ vehicle.license || '-' }}</span>
        </el-form-item>
        <el-form-item label="CAN通信矩阵：" prop="canMatrix" class="form-row-item">
          <ltw-input v-if="isEditing" v-model="formData.canMatrix" placeholder="请输入CAN通信矩阵" />
          <span v-else class="form-value-display">{{ vehicle.canMatrix || '-' }}</span>
        </el-form-item>
      </div>

      <!-- 车辆详情区域 -->
      <div class="form-section">
        <el-form-item label="轴距(mm)：" prop="wheelBase" class="form-row-item">
          <el-input v-if="isEditing" v-model="formData.wheelBase" placeholder="请输入轴距" />
          <span v-else class="form-value-display">{{ vehicle.wheelBase || '-' }}</span>
        </el-form-item>
        <el-form-item label="车长(mm)：" prop="vehicleLength" class="form-row-item">
          <el-input v-if="isEditing" v-model="formData.vehicleLength" placeholder="请输入车长" />
          <span v-else class="form-value-display">{{ vehicle.vehicleLength || '-' }}</span>
        </el-form-item>
        <el-form-item label="车宽(mm)：" prop="vehicleWidth" class="form-row-item">
          <el-input v-if="isEditing" v-model="formData.vehicleWidth" placeholder="请输入车宽" />
          <span v-else class="form-value-display">{{ vehicle.vehicleWidth || '-' }}</span>
        </el-form-item>
        <el-form-item label="车高(mm)：" prop="vehicleHeight" class="form-row-item">
          <el-input v-if="isEditing" v-model="formData.vehicleHeight" placeholder="请输入车高" />
          <span v-else class="form-value-display">{{ vehicle.vehicleHeight || '-' }}</span>
        </el-form-item>
      </div>

      <!-- 照片区域 -->
      <div class="form-section">
        <el-form-item label="照片：" prop="photo" class="form-row-item photo-form-item">
          <!-- 编辑模式 -->
          <div v-if="isEditing" class="photo-container">
            <upload-file
              ref="uploadImage"
              source-type="vehicle_image"
              :limit="1"
              accept=".jpg,.jpeg,.png,.gif"
              :source-id="vehicle.id"
              v-model="formData.photo"
              id="photo"
              :disabled="false"
            />
          </div>
          <!-- 查看模式 -->
          <div v-else class="photo-container">
            <div v-if="vehicle.photoList && vehicle.photoList.length" class="photo-display">
               <upload-file
              ref="uploadImage"
              source-type="vehicle_image"
              :limit="1"
              accept=".jpg,.jpeg,.png,.gif"
              :source-id="vehicle.id"
              v-model="formData.photo"
              id="photo"
              :disabled="true"
            />
            </div>
            <div v-else class="no-photo">
              <div class="photo-placeholder">
                <ltw-icon icon-code="el-icon-picture" class="placeholder-icon"></ltw-icon>
                <span class="placeholder-text">暂无照片</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import util, { showToast } from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import { updateBsVehicle } from '@/apis/fleet/bs-vehicle'
import { createNumberValidationRules } from '@/plugins/util'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
export default {
  name: 'VehicleBasicInfo',
  components: {
    LtwIcon,
    UploadFile,
    EmployeeSelection
  },
  props: {
    vehicle: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      isEditing: false,
      formData: {
        vin: '',
        keeperEmpName: '',
        factoryVin: '',
        externalVin: '',
        license: '',
        canMatrix: '',
        wheelBase: '',
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        photo: []
      },
      formRules: {
        vin: [
          { required: true, message: '请输入车架号', trigger: 'blur' },
          { min: 1, max: 50, message: '车架号长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        keeperEmpName: [
          { required: true, message: '请输入负责人', trigger: 'blur' },
          { min: 1, max: 20, message: '负责人长度在 1 到 20 个字符', trigger: 'blur' }
        ],
         wheelBase:createNumberValidationRules(4,0,false),
        vehicleLength:createNumberValidationRules(5,0,false),
        vehicleWidth:createNumberValidationRules(5,0,false),
        vehicleHeight:createNumberValidationRules(5,0,false)
      }
    }
  },
  watch: {
    vehicle: {
      handler(newVal) {
        if (newVal) {
          this.initFormData()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initFormData() {
      this.formData = {...this.vehicle}
    },
    changeKeeperEmp(e) {
      this.formData.keeperEmpName = e.node.name
    },

    toggleEditMode() {
      if (this.isEditing) {
        // 保存逻辑
        this.saveChanges()
      } else {
        // 进入查看模式
        this.initFormData()
        this.isEditing = true
      }
    },

    cancelEditMode() {
      // 取消编辑，恢复原始数据
      this.isEditing = false
      this.initFormData()
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.vehicleForm) {
          this.$refs.vehicleForm.clearValidate()
        }
      })
    },

    saveChanges() {
      this.$refs.vehicleForm.validate((valid) => {
        if (valid) {
          // 准备提交数据
          const postData = {
            id: this.vehicle.id,
            ...this.formData
          }
          updateBsVehicle(postData).then(res => {
            this.isEditing = false
            this.$refs.vehicleForm.clearValidate()
            showToast('保存成功')
            this.$emit('update-vehicle', res.data)
          }).catch(error => {
            console.error('保存失败:', error)
          })
        } else {
          return false
        }
      })
    },

    cancelEdit() {
      this.isEditing = false
      this.initFormData()
      this.$refs.vehicleForm.clearValidate()
    },

    getPhotoUrl(photo) {
      if (photo && photo.id) {
        return GLB_CONFIG.devUrl.fileServer + '/' + photo.id + '?token=' + util.getToken()
      }
      return ''
    },

    previewPhoto(photo) {
      // 预览照片逻辑
      if (photo && photo.id) {
        const url = this.getPhotoUrl(photo)
        // 这里可以添加图片预览逻辑，比如打开模态框
        console.log('Preview photo:', url)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-basic-info {
  position: relative;
  height: calc(100vh - 300px); // 减去抽屉头部和其他元素的高度
  display: flex;
  flex-direction: column;
  padding-top: 0; // 移除顶部padding
}

.edit-button-container {
  position: sticky;
  top: 0; // 相对于tab pane顶部固定
  z-index: 100;
  background: white;
  padding:0 20px 0 20px;
  margin: 0 -20px 0 -20px; // 只抵消左右padding，移除底部margin
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0; // 防止按钮容器被压缩

  .edit-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .cancel-button {
    padding: 6px 12px;
    font-size: 12px;
    background: #909399;
    border-color: #909399;
    color: white;

    &:hover {
      background: #73767A;
      border-color: #73767A;
    }
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1; // 占据剩余空间
  overflow-y: auto; // 允许垂直滚动
  padding: 20px 20px 40px 20px; // 添加内边距
  margin: 0 -20px; // 抵消父容器的padding
}

.form-section {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #E4E7ED;
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.form-row-item {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;

  :deep(.el-form-item__label) {
    color: #4E5256;
    font-size: 12px;
    font-family: 'Bosch Office Sans', sans-serif;
    font-weight: 400;
    line-height: 32px;
    min-width: 140px;
    padding-right: 12px;
    text-align: left;
    flex-shrink: 0;
  }

  :deep(.el-form-item__content) {
    margin-left: 0 !important;
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 32px;
  }

  :deep(.el-form-item__error) {
    position: static;
    margin-top: 4px;
    padding-left: 0;
  }

  :deep(.el-input) {
    width: 100%;
  }

  &.photo-form-item {
    :deep(.el-form-item__label) {
      line-height: 24px;
      align-self: flex-start;
      padding-top: 4px;
    }

    :deep(.el-form-item__content) {
      align-items: flex-start;
    }
  }
}

.form-value-display {
  color: #232628;
  font-size: 12px;
  font-family: 'Bosch Sans Global', sans-serif;
  font-weight: 400;
  line-height: 32px;
  text-align: left;
  display: flex;
  align-items: center;
  min-height: 32px;
  width: 100%;
}

.form-row-full {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.form-label {
  color: #4E5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 23px;
  min-width: 80px;
}

.photo-container {
  margin-top: 8px;
  width: 100%;
}

.photo-display {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.photo-image {
  width: 120px;
  height: 120px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #409EFF;
  }
}

.no-photo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  border: 2px dashed #DCDFE6;
  border-radius: 4px;
  background: #FAFAFA;
  width: 250px;
}

.photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #C0C4CC;

  .placeholder-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .placeholder-text {
    font-size: 12px;
  }
}

/* 全局表单元素32px高度样式 */
:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-select) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

:deep(.el-date-editor) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

:deep(.el-cascader) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
</style>
