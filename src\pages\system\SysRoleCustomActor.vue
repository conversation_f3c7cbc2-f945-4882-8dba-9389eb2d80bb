<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-tool-container">
                    <org-selection v-model="queryParam.tenantId"></org-selection>
                </div>
                <div class="ltw-search-container ltw-tool-container">
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh"
                              >
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>{{item.name}}
                    </el-button>
                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                                                <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                {{ item.name }}</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records" 
                      @selection-change="handleSelectionChange"  ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                <el-table-column header-align="left" align="left" prop="name" label="名称"></el-table-column>
                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                <el-table-column header-align="left" align="left" prop="sortNum" label="展示顺序"></el-table-column>
                <el-table-column header-align="left" align="left" prop="enabled" label="状态">
                    <template #default="scope">
                        <el-switch v-model="scope.row.enabled"
                                   @change="changeStatus(scope.row)"></el-switch>
                    </template>
                </el-table-column>
                <!--                            <el-table-column header-align="left" align="left" prop="tenantId" label="租户id"></el-table-column>-->
                <el-table-column header-align="left" align="left" label="操作" width="240">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name"
                                        placement="top" :enterable="false"
                            >
                                 <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon></el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="名称" prop="name">
                    <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="编码" prop="code">
                    <ltw-input v-model="formData.code" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="展示顺序" prop="sortNum">
                    <ltw-input v-model="formData.sortNum" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="启用状态" prop="enabled">
                    <el-switch v-model="formData.enabled" :disabled="formReadonly"></el-switch>
                </el-form-item>
                <el-form-item label="所属机构" prop="tenantId">
                    <org-selection v-model="formData.tenantId" :disabled="formReadonly"></org-selection>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
        <privilege-assignment v-model="privilegeAssignmentVisible" :role-id="currentSelectedRoleId" :role-type="currentRoleType"></privilege-assignment>
    </div>
</template>

<script>
    import {
        saveSysRoleCustomActor,
        updateSysRoleCustomActor,
        deleteSysRoleCustomActor,
        pageSysRoleCustomActor,
        getSysRoleCustomActor
    } from '@/apis/system/sys-role-custom-actor'
    import OrgSelection from "@/components/system/OrgSelection";
    import PrivilegeAssignment from "@/components/system/PrivilegeAssignment";
    import ROLE_TYPE from "@/plugins/constants/role-type"

    

const defaultFormData = {}
    export default {
        name: "SysRoleCustomActor",
        components: {
            OrgSelection,PrivilegeAssignment
        },
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({}, defaultFormData),
                formRules: {
                    code: [
                        {required: true, message: '请输入角色编码', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '请输入角色名称', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData: [],
                applications: [],
                privilegeAssignmentVisible:false,
                currentSelectedRoleId:'',
                currentRoleType:ROLE_TYPE.CUSTOM_ACTOR,
                currentUser:{}
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList

            }
            this.currentUser = this.$store.state.permission.currentUser
            this.queryParam.tenantId = this.currentUser.currentTenantId
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(funcName, row) {
                this[funcName](row)
            },
            refresh() {
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysRoleCustomActor(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加自定义角色'
                this.dialogStatus = 'add'
                this.dialogVisible = true
                this.formData.tenantId = this.currentUser.currentTenantId
                if (this.pageData.total) {
                    let lastSorNum = this.pageData.total;
                    if (lastSorNum) {
                        this.formData.sortNum = lastSorNum + 1
                    }
                }
            },
            save() {
                if (this.dialogStatus === 'add') {
                    saveSysRoleCustomActor(this.formData).then(
                        () => {
                            this.dialogVisible = false
                            this.query()
                        }
                    )
                }
                if (this.dialogStatus === 'edit') {
                    updateSysRoleCustomActor(this.formData).then(
                        () => {
                            this.dialogVisible = false
                            this.query()
                        }
                    )
                }
            },
            edit({id}) {
                this.dialogTitle = '修改自定义角色'
                this.dialogStatus = 'edit'
                getSysRoleCustomActor(id).then(
                    res => {
                        this.dialogVisible = true
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                    }
                )
            },
            handleCommand(command) {
                if (this.selectedData.length === 0) {
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if (command === 'batchRemove') {
                    this.batchRemove()
                }
            },
            singleRemove({id}) {
                this.remove({id})
            },
            batchRemove() {
                let idList = [];
                this.selectedData.forEach(ele => {
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param) {
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysRoleCustomActor(param).then(
                        () => {
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    })
                })
            },
            view({id}) {
                this.dialogTitle = '查看自定义角色'
                this.dialogStatus = 'view'
                getSysRoleCustomActor(id).then(
                    res => {
                        this.dialogVisible = true
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                    }
                )
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value) {
                this.selectedData = value
            },
            handleRowClick(row) {
                this.$refs.privilegeRef.load(row.id);
            },
            changeStatus(row) {
                updateSysRoleCustomActor({
                    id: row.id,
                    enabled: row.enabled
                }).catch(() => {
                    row.enabled = !row.enabled
                })
            },
            initForm() {
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({}, defaultFormData)
            },
            privilegeAssign(row){
                this.currentSelectedRoleId = row.id
                this.privilegeAssignmentVisible = true
            }
        }
    }
</script>