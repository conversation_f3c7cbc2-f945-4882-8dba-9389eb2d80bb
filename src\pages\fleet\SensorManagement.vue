<template>
  <div class="sensor-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-section">
        <div class="breadcrumb-nav">
          <el-button type="text" class="back-btn">
            <i class="el-icon-arrow-left"></i>
            Back
          </el-button>
          <div class="divider"></div>
          <div class="stage-tag">
            <span class="tag">Staging</span>
          </div>
          <div class="divider"></div>
        </div>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>Page</el-breadcrumb-item>
          <el-breadcrumb-item>Page</el-breadcrumb-item>
          <el-breadcrumb-item class="current">Current Page</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <!-- 页面标题 -->
      <div class="page-title-section">
        <div class="title-content">
          <i class="title-icon"></i>
          <h1 class="page-title">传感器管理</h1>
          <div class="title-tag">
            <span class="tag">车辆配置</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <div class="search-input-group">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入名称查找"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button icon="el-icon-search" @click="handleSearch"></el-button>
            </template>
          </el-input>
          <el-button class="filter-btn" icon="el-icon-setting"></el-button>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleConfirm">确认</el-button>
          <el-button :disabled="true" class="reset-btn">重置</el-button>
        </div>
      </div>
      <div class="divider-line"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 顶部选项卡和统计 -->
      <div class="content-header">
        <!-- 主选项卡 -->
        <div class="main-tabs">
          <el-tabs v-model="activeMainTab" class="main-tab-container">
            <el-tab-pane label="Modality" name="modality"></el-tab-pane>
            <el-tab-pane label="Sensor" name="sensor"></el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 控制栏 -->
      <div class="control-bar">
        <div class="left-controls">
          <!-- 显示模式选择 -->
          <div class="display-mode">
            <span class="label">展示</span>
            <el-select v-model="displayMode" class="mode-select">
              <el-option label="卡片" value="card"></el-option>
              <el-option label="列表" value="list"></el-option>
            </el-select>
          </div>
          <div class="control-divider"></div>
          
          <!-- 排序选择 -->
          <div class="sort-control">
            <el-select v-model="sortBy" class="sort-select">
              <el-option label="By Date" value="date"></el-option>
              <el-option label="By Name" value="name"></el-option>
            </el-select>
            <el-button class="sort-direction-btn" icon="el-icon-sort"></el-button>
          </div>
          <div class="control-divider"></div>
          
          <!-- 统计信息 -->
          <div class="statistics">
            <i class="stats-icon el-icon-data-line"></i>
            <span class="stats-label">车辆总数</span>
            <span class="stats-value">{{ totalCount }}</span>
          </div>
        </div>
      </div>

      <!-- 内容卡片 -->
      <el-card class="content-card" shadow="hover">
        <!-- 传感器类型筛选标签 -->
        <div class="sensor-filter-tags">
          <el-tag
            v-for="sensorType in sensorTypes"
            :key="sensorType.value"
            :type="selectedSensorTypes.includes(sensorType.value) ? 'primary' : 'info'"
            :effect="selectedSensorTypes.includes(sensorType.value) ? 'dark' : 'plain'"
            class="sensor-tag"
            @click="toggleSensorType(sensorType.value)"
          >
            {{ sensorType.label }}
          </el-tag>
        </div>

        <!-- 传感器内容区域 -->
        <div class="sensor-content">
          <!-- 传感器子选项卡 -->
          <div class="sensor-tabs">
            <el-tabs v-model="activeSensorTab" class="sensor-tab-container">
              <el-tab-pane
                v-for="tab in sensorTabs"
                :key="tab.name"
                :label="tab.label"
                :name="tab.name"
              >
                <template #label>
                  <span class="tab-label">{{ tab.label }}</span>
                  <el-button
                    v-if="tab.name === activeSensorTab"
                    type="primary"
                    size="mini"
                    icon="el-icon-plus"
                    class="add-btn"
                    @click="handleAddSensor"
                  ></el-button>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 传感器卡片网格 -->
          <div class="sensor-grid">
            <div
              v-for="sensor in filteredSensors"
              :key="sensor.id"
              class="sensor-card"
            >
              <el-card class="sensor-item" shadow="hover">
                <div class="sensor-card-content">
                  <div class="sensor-image">
                    <img :src="sensor.image || defaultImage" :alt="sensor.name" />
                  </div>
                  <div class="sensor-info">
                    <div class="sensor-header">
                      <h3 class="sensor-name">{{ sensor.type }} {{ sensor.brand }}</h3>
                    </div>
                    <div class="sensor-details">
                      <div class="detail-item">
                        <span class="detail-label">型号规格</span>
                        <span class="detail-value">{{ sensor.specification }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SensorManagement',
  data() {
    return {
      // 搜索相关
      searchKeyword: '',
      
      // 选项卡相关
      activeMainTab: 'sensor',
      activeSensorTab: 'camera',
      
      // 显示控制
      displayMode: 'card',
      sortBy: 'date',
      
      // 传感器类型筛选
      selectedSensorTypes: ['Lidar', 'Radar', 'Camera'],
      sensorTypes: [
        { label: '全部', value: 'all' },
        { label: 'Lidar', value: 'lidar' },
        { label: 'Radar', value: 'radar' },
        { label: 'CAN', value: 'can' },
        { label: 'Camera', value: 'camera' },
        { label: 'ECU', value: 'ecu' },
        { label: 'USS', value: 'uss' }
      ],
      
      // 传感器子选项卡
      sensorTabs: [
        { name: 'camera', label: 'Camera' }
      ],
      
      // 传感器数据
      sensorList: [
        {
          id: 1,
          type: 'Camera',
          brand: '艾利光',
          specification: 'ISX031 100°200W',
          image: 'https://placehold.co/146x107',
          category: 'camera'
        },
        {
          id: 2,
          type: 'Camera',
          brand: '艾利光',
          specification: 'ISX031 100°200W',
          image: 'https://placehold.co/146x107',
          category: 'camera'
        },
        {
          id: 3,
          type: 'Camera',
          brand: '艾利光',
          specification: 'ISX031 100°200W',
          image: 'https://placehold.co/146x107',
          category: 'camera'
        },
        {
          id: 4,
          type: 'Camera',
          brand: '艾利光',
          specification: 'ISX031 100°200W',
          image: 'https://placehold.co/146x107',
          category: 'camera'
        }
      ],
      
      // 默认图片
      defaultImage: 'https://placehold.co/146x107'
    }
  },
  
  computed: {
    // 统计总数
    totalCount() {
      return this.filteredSensors.length
    },
    
    // 过滤后的传感器列表
    filteredSensors() {
      let filtered = this.sensorList
      
      // 根据当前选项卡过滤
      if (this.activeSensorTab !== 'all') {
        filtered = filtered.filter(sensor => sensor.category === this.activeSensorTab)
      }
      
      // 根据搜索关键词过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(sensor => 
          sensor.type.toLowerCase().includes(keyword) ||
          sensor.brand.toLowerCase().includes(keyword) ||
          sensor.specification.toLowerCase().includes(keyword)
        )
      }
      
      return filtered
    }
  },
  
  methods: {
    // 搜索处理
    handleSearch() {
      console.log('搜索关键词:', this.searchKeyword)
      // 这里可以调用API进行搜索
    },
    
    // 确认按钮处理
    handleConfirm() {
      this.$message.success('操作确认')
    },
    
    // 切换传感器类型
    toggleSensorType(type) {
      if (type === 'all') {
        this.selectedSensorTypes = ['all']
      } else {
        const index = this.selectedSensorTypes.indexOf(type)
        if (index > -1) {
          this.selectedSensorTypes.splice(index, 1)
        } else {
          this.selectedSensorTypes.push(type)
        }
        // 移除"全部"选项
        const allIndex = this.selectedSensorTypes.indexOf('all')
        if (allIndex > -1) {
          this.selectedSensorTypes.splice(allIndex, 1)
        }
      }
    },
    
    // 添加传感器
    handleAddSensor() {
      this.$message.info('添加传感器功能')
    }
  }
}
</script>
