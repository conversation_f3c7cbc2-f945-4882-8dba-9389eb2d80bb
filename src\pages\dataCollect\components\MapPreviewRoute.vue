<template>
  <div class="map-selection-container">
    <div class="left" :class="leftHidden && 'narrow'">
      <el-divider content-position="left" class="title">路线规划</el-divider>
      <div class="route-point-list">
        <el-scrollbar>
          <div
            v-if="routePointList?.length"
            class="route-point-item"
            v-for="(routePoint, index) in routePointList"
            :key="routePoint.id"
          >
            <div class="route-point-label" v-if="routePoint.type === 'start'">
              <el-tag>起点</el-tag>
            </div>
            <div class="route-point-label" v-else-if="routePoint.type === 'end'">
              <el-tag type="success">终点</el-tag>
            </div>
            <div class="route-point-label" v-else>
              <el-tag type="warning">途经点</el-tag>
            </div>
            <div class="route-point-name">{{ routePoint.title }}</div>
            <el-popconfirm width="250" :title="$t('是否确认删除该数据')" @confirm="removeRoutePoint(index)">
              <template #reference>
                <el-link :underline="false" v-show="index > 0 && index < routePointList?.length - 1" type="danger">
                  <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                </el-link>
              </template>
            </el-popconfirm>
          </div>
          <el-empty v-else />
        </el-scrollbar>
      </div>
      <div class="footer" v-if="!disabled">
        <el-button type="primary" size="small" :disabled="!routePointList?.length" @click="confirmClearMap"
          >{{ $t('清除') }}
        </el-button>
        <el-button type="primary" size="small" :disabled="!routePointList?.length" @click="generateRoutes"
          >{{ $t('生成路线') }}
        </el-button>
        <el-button type="primary" size="small" :disabled="!planRouteList?.length" @click="confirmRoute"
          >{{ $t('确认路线') }}
        </el-button>
      </div>
    </div>
    <div class="middle" :class="{ 'left-narrow': leftHidden, 'right-narrow': rightHidden }">
      <el-button size="small" class="toggle toggle-left" @click="toggleLeft" :disabled="disabled">
        <ltw-icon :class="leftHidden && 'narrow'" icon-code="el-icon-d-arrow-left"></ltw-icon>
      </el-button>
      <el-button size="small" class="toggle toggle-right" @click="toggleRight">
        <ltw-icon :class="rightHidden && 'narrow'" icon-code="el-icon-d-arrow-right"></ltw-icon>
      </el-button>
      <div class="opt-list" v-if="recommendList?.length">
        <div
          class="opt-btn"
          v-for="item in geoTypes"
          :key="item.value"
          :class="{ active: geoType === item.value }"
          @click="changeGeoType(item.value)"
        >
          <ltw-icon :icon-code="getIconCode(item.value)"></ltw-icon>
          {{ item.name }}
        </div>
      </div>

      <el-button class="bird-overview-btn" @click="getBirderOverview">回到总览</el-button>
      <div class="opt-list status-list">
        <div class="opt-btn">
          <div class="geo-type">
            <el-image :src="require('@/assets/images/point.png')" fit="fill"></el-image>
          </div>
          参考点
        </div>
      </div>
      <base-map
        ref="baseMap"
        @change-bounds="changeBounds"
        @zoom-changed="zoomChanged"
        @click-marker="clickMarker"
        @click-route="clickRoute"
        @right-click-map="rightClickMap"
        @adjust-complete="adjustComplete"
        @click-editor-marker="clickEditorMarker"
        @cluster-changed="clusterChanged"
      ></base-map>
      <!--      <div class="map-overview" :id="mapId" ref="mapContainerRef"></div>-->
      <div class="tool-button-container" v-if="!leftHidden">
        <div class="search-container">
          <el-select
            v-if="!disabled"
            size="small"
            clearable
            v-model="queryParam.key"
            remote
            :remote-method="positioningByAddress"
            :loading="queryLoading"
            :placeholder="$t('示例:39.90812,116.397484/苏州市现代大道')"
            filterable
            @change="selectSuggestionHandle"
          >
            <el-option v-for="item in suggestionList" :key="item.id" :label="item.title" :value="item.id">
              <span style="float: left">{{ item.title }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                {{
                  (item.province || '') +
                  (item.city ? ',' + item.city : '') +
                  (item.district ? ',' + item.district : '')
                }}
              </span>
            </el-option>
          </el-select>
          <el-button size="small" @click="search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </div>
        <div class="intersection-button">
          <el-checkbox v-model="showRoad" @change="handleDisplayRoadChange">{{ $t('真值轨迹') }}</el-checkbox>
        </div>
        <el-tag class="zoom-tag">{{ $t('缩放等级') }}：{{ zoom.toFixed(2) }}</el-tag>
      </div>
    </div>

    <div class="right" :class="rightHidden && 'narrow'">
      <el-link type="primary" class="load-routes-by-req" @click="loadRoutesByReq">载入需求路线</el-link>
      <el-divider content-position="left" class="title">路线列表</el-divider>
      <div class="route-point-list">
        <el-scrollbar>
          <div
            v-if="routeList?.length"
            class="route-item"
            :class="{ active: currentRouteKey === route.id }"
            @click="getRouteCenter(route)"
            v-for="(route, index) in routeList"
            :key="route.id"
          >
            <div class="route-name">
              <el-tooltip effect="dark" :content="route.detail.name" placement="top">
                <div class="title-name">{{ route.detail.name }}</div>
              </el-tooltip>
              <el-popconfirm width="250" :title="$t('是否确认删除该数据')" @confirm="removeRoute(route)">
                <template #reference>
                  <el-link class="close-icon" v-if="!disabled" :underline="false" type="danger">
                    <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                  </el-link>
                </template>
              </el-popconfirm>
            </div>

            <div class="route-point-label">
              <el-tag type="success">起点</el-tag>
              <el-tooltip effect="dark" :content="route.markers[0].title" placement="top">
                <div class="route-point-name">{{ route.markers[0].title }}</div>
              </el-tooltip>
            </div>
            <div class="route-point-label">
              <el-tag type="warning">途经点</el-tag>
              <div class="route-point-name pass">
                <div class="pass-num" @click.stop="togglePassPoints(route)">
                  <ltw-icon v-if="route.markers.length - 2 > 0" icon-code="el-icon-arrow-down"></ltw-icon>
                  <el-link type="primary" :underline="false">{{ route.markers.length - 2 }}</el-link>
                </div>
                <div class="pass-points" :class="{ 'toggle-pass-points': route.togglePassPointsFlag }">
                  <div
                    class="pass-point"
                    v-for="point in route.markers.slice(1, route.markers?.length - 1)"
                    :key="point.id"
                  >
                    <el-tooltip effect="dark" :content="point.title" placement="top">
                      <div
                        class="route-point-name"
                        :class="{ active: currentPassMarkerId === point.id }"
                        @click.stop="goPassCenter(point, route.id)"
                      >
                        {{ point.title }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div class="route-point-label">
              <el-tag type="danger">终点</el-tag>
              <el-tooltip effect="dark" :content="route.markers[route.markers?.length - 1].title" placement="top">
                <div class="route-point-name">{{ route.markers[route.markers?.length - 1].title }}</div>
              </el-tooltip>
            </div>
          </div>

          <el-empty v-else />
        </el-scrollbar>
      </div>
      <div class="footer" v-if="!disabled">
        <el-button size="small" type="primary" @click="save">{{ $t('保存') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { latLngPoint, isLatitudeLongitude, searchSuggestionAPI, drivingSearch } from '@/plugins/map/TxMap'
import util, { getUuid, showConfirmToast, showToast } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { listMapResult } from '@/apis/data-collect/map_results'
import md5 from 'js-md5'
import { jsonp } from 'vue-jsonp'
import MarkerInfoWindow from '@/pages/dataCollect/dialog/MarkerInfoWindow.vue'
import { h, render } from 'vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { ElMessageBox } from 'element-plus'
import { getFilesPreview } from '@/apis/base/file'
import { getRecommendItemListRequirement, getRequirementRecommends } from '@/apis/data-collect/vt-daq-task'
import BaseMap from '@/components/map/BaseMap.vue'

const CONST_POINT = 'point'
export default {
  name: 'MapPreviewRoute',
  components: {
    LtwInput,
    LtwIcon,
    BaseMap
  },
  props: {
    propDisabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propDisabled: {
      handler(val) {
        this.disabled = val
      },
      deep: true,
      immediate: true
    }
  },
  emits: ['reload'],
  data() {
    return {
      OVER_LAY_TYPE,
      mapId: 'map',
      queryParam: {},
      bounds: [],
      showRoad: false,
      roadList: [],
      queryLoading: false,
      suggestionList: [],
      // windowinfo传值
      currentMarker: {},
      currentPassMarkerId: '',
      routePointList: [],
      // clearRoutePoints: [],
      planRouteList: [],
      routeList: [],
      allRouteList: [],
      currentRouteKey: '',
      selectedStyleName: 'selectedStyle',
      unSelectedStyleName: 'unSelectedStyle',
      routeMode: '',
      leftHidden: false,
      rightHidden: false,
      disabled: false,
      zoom: 18,
      clipDot: [],
      geoType: CONST_POINT,
      geoTypes: [
        {
          name: '点',
          value: CONST_POINT
        },
        {
          name: '线',
          value: 'line'
        },
        {
          name: '面',
          value: 'area'
        }
        // {
        //   name: '聚合',
        //   value: 'cluster'
        // }
      ],
      recommendList: [],
      clusterBubbleList: [],
      requirementIdList: []
    }
  },
  methods: {
    async show(row) {
      await this.$refs.baseMap.show({ centerMarker: false })
      const globalInfoWindow = this.$refs.baseMap.createInfoWindow(this.createInfoWindow())
      globalInfoWindow?.close()
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      this.zoom = globalMap.getZoom()
      if (row?.data?.checkedRoutes?.length) {
        const _this = this
        getFilesPreview({
          path: row?.data?.checkedRoutes[0].attachment,
          token: util.getToken()
        }).then(async res => {
          const files = await _this.readFile(res)
          const checkedRoutes = JSON.parse(files)
          this.allRouteList = JSON.parse(JSON.stringify(checkedRoutes))
          this.drawRouteListLines(this.allRouteList)
          this.setRouteChecked(JSON.parse(JSON.stringify(checkedRoutes)))
          this.getRouteCenter(checkedRoutes[0])
        })
        // this.getFilesPreview(row?.data?.checkedRoutes)
      }
      this.requirementIdList = row.data.requirementIdList
      if (row.data.requirementIdList?.length) {
        this.getRecommendItemListRequirement(row.data.requirementIdList)
      }
    },
    // getFilesPreview(checkedRoutes) {
    //   const _this = this
    //   // if (row.checkedRoutes?.length) {
    //   //   row.routes.push(...row.checkedRoutes)
    //   // }
    //   // if (row?.routes?.length) {
    //   //   const promises = row?.routes.map(val =>
    //   getFilesPreview({
    //     path: checkedRoutes[0].attachment,
    //     token: util.getToken()
    //   }).then(res => {
    //     let checkedRoutes = _this.readFile(res)
    //     this.allRouteList = checkedRoutes
    //     this.drawRouteListLines(this.allRouteList)
    //     this.setRouteChecked(checkedRoutes)
    //     this.getRouteCenter(checkedRoutes[0])
    //   })
    //   // )
    //   // Promise.all(promises).then(res => {
    //   //   let routes, checkedRoutes
    //   //   if (row.checkedRoutes?.length) {
    //   //     checkedRoutes = JSON.parse(res[res?.length - 1])
    //   //     routes = res.map(val => JSON.parse(val)).flat(2)
    //   //   } else {
    //   //     routes = res.map(val => JSON.parse(val)).flat(2)
    //   //   }
    //   //   this.allRouteList = routes
    //   //   if (this.allRouteList?.length) {
    //   //     this.drawRouteListLines(this.allRouteList)
    //   //     this.leftHidden = true
    //   //   }
    //   //   if (checkedRoutes?.length) {
    //   //     this.setRouteChecked(checkedRoutes)
    //   //     this.getRouteCenter(checkedRoutes[0])
    //   //   } else {
    //   //     if (routes?.length) {
    //   //       this.routeList = routes
    //   //       this.getRouteCenter(routes[0])
    //   //     }
    //   //   }
    //   // })
    //   // } else {
    //   //   this.leftHidden = false
    //   // }
    // },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = event => {
          // 获取文件的字节数据
          resolve(event.target.result)
        }
        reader.onerror = event => {
          reject(error)
        }
        reader.readAsText(file) // 使用readAsText方法读取文件内容
      })
    },
    getRouteCenter(item) {
      // 重置途经点高亮
      this.currentPassMarkerId = ''
      const globalRouteListPolylineLayer = this.$refs.baseMap.getGlobalMapObj('globalRouteListPolylineLayer')
      const globalMultiLabel = this.$refs.baseMap.getGlobalMapObj('globalMultiLabel')
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')
      this.$refs.baseMap.clearObjects(globalMultiLabel, globalMarker)
      let routeLines = globalRouteListPolylineLayer.getGeometries()

      this.currentRouteKey = item.id === this.currentRouteKey ? '' : item.id

      routeLines.forEach(val => {
        if (val.id === item.id) {
          if (val.styleId === this.selectedStyleName) {
            val.styleId = 'style_' + val.id
          } else {
            val.styleId = this.selectedStyleName
            this.setRoutePointMarkers(item.markers)

            //置于屏幕中心
            if (val.paths?.length) {
              this.$refs.baseMap.showFitView(
                val.paths.map(path => {
                  return { position: path }
                })
              )
            }
          }
        } else {
          val.styleId = val.styleId === this.selectedStyleName ? 'style_' + val.id : val.styleId
        }
      })
      globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    toggleLeft() {
      this.leftHidden = !this.leftHidden
    },
    toggleRight() {
      this.rightHidden = !this.rightHidden
    },
    setRouteChecked(checkedRoutes) {
      const globalRouteListPolylineLayer = this.$refs.baseMap.getGlobalMapObj('globalRouteListPolylineLayer')
      this.routeList = checkedRoutes
      //切换高亮
      let routeLines = globalRouteListPolylineLayer?.getGeometries() || []
      routeLines.forEach(val => {
        if (!~checkedRoutes.findIndex(checkedRoute => checkedRoute.id === val.id)) {
          val.styleId = this.unSelectedStyleName
        }
      })
      globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    drawRouteListLines(routes) {
      const _this = this
      const routeLines = routes.map((val, index) => {
        return {
          id: val.id,
          color: '#409eff',
          paths: val.detail.polyline,
          styles: {
            arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
          }
        }
      })
      const options = {
        styles: {
          borderWidth: 2,
          borderColor: '#a8a8a8',
          showArrow: true,
          lineCap: 'round',
          arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        }
      }
      this.$refs.baseMap.createPolylineLayer(routeLines, options, 'route-list-polyline-layer', 1)
    },
    clickRoute(e) {
      const route = this.routeList.find(val => val.id === e.geometry.id)
      if (route) {
        this.removeRoute(route)
      } else {
        const route = this.allRouteList.find(val => val.id === e.geometry.id)
        if (route) {
          this.routeList.push(route)
          //设置列表高亮
          this.getRouteCenter(route)
        } else {
          // 不在路线列表中-刚生成路线未确认时
          this.handleRoutePlanClick(e)
        }
      }
    },
    clickMarker(evt) {
      if (evt.geometry.properties.type === 'recommend') {
        this.$refs.baseMap.showFitView([evt.geometry])
      }
    },
    removeRoute(route) {
      this.currentRouteKey = ''
      const globalRouteListPolylineLayer = this.$refs.baseMap.getGlobalMapObj('globalRouteListPolylineLayer')
      const globalMultiLabel = this.$refs.baseMap.getGlobalMapObj('globalMultiLabel')
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')
      this.$refs.baseMap.clearObjects(globalMultiLabel, globalMarker)
      //切换高亮
      let routeLines = globalRouteListPolylineLayer?.getGeometries()

      routeLines.forEach(val => {
        if (val.id === route.id) {
          val.styleId = val.styleId === this.unSelectedStyleName ? this.selectedStyleName : this.unSelectedStyleName
        } else {
          val.styleId = val.styleId === this.selectedStyleName ? 'style_' + val.id : val.styleId
        }
      })

      const deleteIndex = this.routeList.findIndex(val => val.id === route.id)
      this.routeList.splice(deleteIndex, 1)
      globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    setRoutePointMarkers(routePointList) {
      const markers = routePointList.map((val, index) => {
        let point = {
          id: 'routePoint-' + val.id,
          style: {
            backgroundColor: '#e6a23c'
          },
          position: latLngPoint({ lat: val.lat, lng: val.lng }),
          content: index
        }
        if (index === 0) {
          point.style = {
            backgroundColor: '#e6a23c',
            width: 20, // 默认宽度
            height: 30, // 默认高度
            anchor: { x: 10, y: 30 }, // 默认锚点
            enableRelativeScale: false,
            src: require('@/assets/images/mapIcon/start.png')
          }
        } else if (index === routePointList?.length - 1) {
          point.style = {
            backgroundColor: '#e6a23c',
            width: 20, // 默认宽度
            height: 30, // 默认高度
            anchor: { x: 10, y: 30 }, // 默认锚点
            enableRelativeScale: false,
            src: require('@/assets/images/mapIcon/end.png')
          }
        }
        return point
      })
      // 开始点结束点
      this.$refs.baseMap.initMarkers([markers[0], markers[markers?.length - 1]])
      // 途经点
      this.$refs.baseMap.initMultiLabel(markers.slice(1, markers?.length - 1))
    },
    async handleDisplayRoadChange(val) {
      const globalRoadLine = this.$refs.baseMap.getGlobalMapObj('globalRoadLine')
      await this.$refs.baseMap.clearObjects(globalRoadLine)
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      if (val) {
        let zoom = globalMap.getZoom()
        if (zoom < 13) {
          this.showRoad = false
          showToast('地图缩放等级>13的情况下支持查看真值，请放大地图后再次点击真值轨迹按钮', 'warning')
        } else {
          this.drawerRoadLine()
        }
      } else {
        this.$refs.baseMap.clearObjects(globalRoadLine)
      }
    },
    drawerRoadLine() {
      listMapResult({
        targetCrs: 1,
        intersectingArea: { type: 'Polygon', userData: { crs: 1 }, coordinates: [this.bounds] }
      }).then(res => {
        this.roadList = res.data
        this.extractedCoordinates()
        const options = {
          styles: {
            color: '#ec4c4c',
            width: 5,
            lineCap: 'round',
            showArrow: true,
            arrowOptions: { width: 8, height: 5, space: 30 }
          }
        }
        this.$refs.baseMap.createPolylineLayer(this.roadList, options, 'roadLine', 1, 'globalRoadLine')
      })
    },
    async rightClickMap(e) {
      if (!this.disabled) {
        const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')

        globalInfoWindow?.close()
        this.currentRouteKey && this.getRouteCenter({ id: this.currentRouteKey })
        await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.NONE)

        const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
        this.$refs.baseMap.removeGeo(globalMapEditorMarker.getGeometries().map(val => val.id))

        await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
        this.$refs.baseMap.setActiveOverlay(OVER_LAY_TYPE.DRAGMARKER)
        let markerList = [
          {
            id: 0, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            style: {
              width: 17,
              height: 25,
              anchor: { x: 17, y: 50 },
              src: 'data:image/png;base64,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'
            },
            position: e.latLng
          }
        ]
        this.$refs.baseMap.addGeo(markerList, 'globalMapEditorMarker')
      }
    },
    createInfoWindow() {
      const _this = this
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      let instance = h(MarkerInfoWindow, {
        item: _this.currentMarker,
        onSetPoint: val => {
          this.setPoint(val)
        }
      })
      let infoWindow = new TMap.InfoWindow({
        map: globalMap,
        enableCustom: true,
        position: latLngPoint({ lat: 31.380155339677, lng: 121.27259505835 }),
        offset: { y: -50, x: 0 },
        content: '<div id="ref-card"></div>'
      })
      let card = document.querySelector('#ref-card')
      render(instance, card)
      return infoWindow
    },
    setPoint(val) {
      if (this.leftHidden) {
        this.leftHidden = false
      }
      const index = this.routePointList.findIndex(point => val.id === point.id)
      if (!~index) {
        const startIndex = this.routePointList.findIndex(val => val.type === 'start')
        const endIndex = this.routePointList.findIndex(val => val.type === 'end')
        if (val.type === 'start') {
          if (~startIndex) {
            this.routePointList[startIndex].type = 'pass'
          }
          this.routePointList.unshift(val)
        } else if (val.type === 'end') {
          if (~endIndex) {
            this.routePointList[endIndex].type = 'pass'
          }
          this.routePointList.push(val)
        } else {
          if (!~endIndex) {
            this.routePointList.push(val)
          } else {
            this.routePointList.splice(-1, 0, val)
          }
        }
        const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
        const ids = globalMapEditorMarker
          .getGeometries()
          .filter(marker => marker.id !== val.id)
          .map(marker => marker.id)
        this.$refs.baseMap.removeGeo(globalMapEditorMarker, ids)
      } else {
        showToast(this.$t('该点位已添加'), 'warning')
      }
    },
    clickEditorMarker(e) {
      this.openInfoWindow(e)
    },
    adjustComplete(e) {
      this.dragEnd(e)
    },

    async dragEnd(event) {
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.setPosition(
        latLngPoint({
          lat: event?.position?.lat,
          lng: event?.position?.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: event?.position?.lat,
        lng: event?.position?.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      globalInfoWindow?.open()
    },
    async openInfoWindow(evt) {
      this.$refs.baseMap.goCenter([evt.geometry.position.lng, evt.geometry.position.lat])
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.setPosition(
        latLngPoint({
          lat: evt.geometry.position.lat,
          lng: evt.geometry.position.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: evt.geometry.position.lat,
        lng: evt.geometry.position.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      globalInfoWindow?.open()
    },
    async positioningByAddress(key) {
      if (!key) return
      const isLatLng = isLatitudeLongitude(key)
      let suggestionList = []
      if (isLatLng) {
        suggestionList = [
          {
            title: key,
            id: key
          }
        ]
      } else {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(key)
        this.queryLoading = false
      }
      this.suggestionList = suggestionList
    },
    selectSuggestionHandle(key) {
      if (!key) return
      const item = this.suggestionList.find(val => val.id === key)
      // this.queryParam.keyword = item?.title
      // this.queryParam.location = item?.location
      this.queryParam.keyword = (item?.province || '') + (item?.city || '') + (item?.district || '') + item?.title
      this.search()
    },
    async search() {
      if (!this.queryParam.key) return
      const isLatLng = isLatitudeLongitude(this.queryParam.key)
      let suggestionList = []
      if (!isLatLng) {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(this.queryParam.keyword)
        this.queryLoading = false
      } else {
        suggestionList = [{ location: latLngPoint(this.queryParam.key.split(',')) }]
      }
      let markerList = suggestionList.map((val, index) => {
        // const item = this.convertorItem(val)
        return {
          id: val.id, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          style: {
            width: 34,
            height: 50,
            anchor: { x: 17, y: 50 },
            enableRelativeScale: true,
            relativeScaleOptions: {
              scaleZoom: 17, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
              minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
              maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
            },
            src: 'data:image/png;base64,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'
          },

          position: val.location //点标记坐标位置
        }
      })

      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.NONE)
      const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
      this.$refs.baseMap.removeGeo(globalMapEditorMarker.getGeometries().map(val => val.id))
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
      this.$refs.baseMap.addGeo(markerList)
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.close()
      this.$refs.baseMap.showFitView(markerList)
    },
    getGeoCoder(param) {
      let sig = md5(
        `/ws/geocoder/v1?callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&location=${param.location}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
      )
      // sig = encodeURI(sig) //url化一下
      let getData = {
        callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
        callbackName: 'jsonpCallback', // 设置callback 参数的值
        key: GLB_CONFIG.TxMapKey,
        location: param.location,
        output: 'jsonp',
        sig
      }
      //签名失败的解决办法 https://lbs.qq.com/faq/serverFaq/webServiceKey
      return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
    },
    async getLocationName(item) {
      // this.currentVehicle = row
      if (item.lat && item.lng) {
        let res = await this.getGeoCoder({
          location: item.lat + ',' + item.lng
        })
        if (res.status === 0) {
          item.title = res.result?.formatted_addresses?.recommend
          item.id = res.request_id
          return item
        } else {
          showToast(res.message, 'warning')
        }
      }
    },
    extractedCoordinates() {
      this.roadList.forEach(item => {
        item.paths =
          item.trajectoryGeom?.coordinates?.map(geo => {
            const [longitude, latitude] = geo
            return [latitude, longitude]
          }) || []
      })
    },
    drawRoutePlanLines(routes) {
      const _this = this
      const routeLines = routes.map((val, index) => {
        return {
          id: index + 1,
          color: 'rgb(159, 206, 255)',
          paths: val.polyline,
          styles: {
            arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
          }
        }
      })
      const options = {
        styles: {
          borderWidth: 2,
          borderColor: '#a8a8a8',
          showArrow: true,
          arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        }
      }
      const globalRoutePlanPolylineLayer = this.$refs.baseMap.createPolylineLayer(
        routeLines,
        options,
        'route-plan-polyline-layer',
        2,
        'globalRoutePlanPolylineLayer'
      )

      //设置第一条线为高亮
      let planRoutes = globalRoutePlanPolylineLayer.getGeometries()
      planRoutes[0].styleId = _this.selectedStyleName
      globalRoutePlanPolylineLayer.setGeometries(planRoutes)
      // globalRoutePlanPolylineLayer.on('click', _this.handleRoutePlanClick)
    },
    handleRoutePlanClick(e) {
      const globalRoutePlanPolylineLayer = this.$refs.baseMap.getGlobalMapObj('globalRoutePlanPolylineLayer')
      //切换高亮
      let routeLines = globalRoutePlanPolylineLayer.getGeometries()
      const chooseIndex = routeLines.findIndex(val => val.id === e.geometry.id)
      const highlightIndex = routeLines.findIndex(val => val.styleId === this.selectedStyleName)
      if (chooseIndex !== highlightIndex) {
        let chooseStyle = routeLines[chooseIndex].styleId
        let chooseRank = routeLines[chooseIndex].rank
        routeLines[chooseIndex].styleId = routeLines[highlightIndex].styleId
        routeLines[chooseIndex].rank = routeLines[highlightIndex].rank
        routeLines[highlightIndex].styleId = chooseStyle
        routeLines[highlightIndex].rank = chooseRank
        globalRoutePlanPolylineLayer.setGeometries(routeLines)
      }
    },
    confirmClearMap() {
      showConfirmToast({
        message: this.$t('确认删除已选路线规划?')
      }).then(() => {
        this.clearPlanMap()
      })
    },
    clearMap() {
      // globalMarkerCluster ???
      this.$refs.baseMap.clearObjects(
        'globalRecommendMarker',
        'globalRecommendPolylineLayer',
        'globalRecommendPolygonLayer'
      )
      this.activeEditorMode = null
      this.polygonSelected = false
      this.clusterBubbleList.forEach(item => {
        item.destroy()
      })
      this.clusterBubbleList = []
    },
    clusterChanged(e) {
      if (this.geoType === 'cluster') {
        const _this = this
        let markerGeometries = []
        // 销毁旧聚合簇生成的覆盖物
        this.clearMap()
        // this.$refs.baseMap.clearObjects('globalMarker', 'globalPolylineLayer', 'globalPolygonLayer')
        const globalMarkerCluster = this.$refs.baseMap.getGlobalMapObj('globalRecommendMarkerCluster')
        const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
        // 根据新的聚合簇数组生成新的覆盖物和点标记图层
        const clusters = globalMarkerCluster.getClusters()
        clusters.forEach(function (item) {
          if (item.geometries.length > 1) {
            const ClusterBubble = initClusterBubble(globalMap)
            let clusterBubble = new ClusterBubble({
              map: globalMap,
              position: item.center,
              content: item.geometries.length
            })
            clusterBubble.on('click', () => {
              globalMap.fitBounds(item.bounds, {
                padding: 100 // 自适应边距
              })
            })
            _this.clusterBubbleList.push(clusterBubble)
          } else {
            markerGeometries.push(item.geometries[0])
          }
        })

        if (markerGeometries?.length) {
          // 创建点标记图层
          const markers = this.recommendList
            .filter(val => ~markerGeometries.findIndex(geo => geo.id === val.id))
            .map((val, index) => {
              return {
                id: val.id,
                style: {
                  width: 20,
                  height: 20,
                  anchor: { x: 10, y: 10 },
                  src: require('@/assets/images/point.png')
                },
                position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]]),
                properties: { type: 'recommend' }
              }
            })
          this.$refs.baseMap.initMarkers(markers, 'globalRecommendMarker')
          // const globalInfoWindow = this.$refs.baseMap.createInfoWindow()
          // globalInfoWindow.close() //初始关闭信息窗关闭
          // this.drawInfoWindow(globalMarker)
        }
      }
    },
    getRecommendItemListRequirement(requirementIdList) {
      const promises = requirementIdList?.map(val =>
        getRecommendItemListRequirement({
          targetCrs: 1,
          requirementId: val
        }).then(res => res)
      )
      Promise.all(promises || []).then(result => {
        this.recommendList = result.map(val => val.data || []).flat(2)
        this.drawMap()
        // this.$refs.MapPreviewRoute.show({
        //   type: this.dialogStatus,
        //   data: {
        //     routes: routes,
        //     checkedRoutes: data,
        //     requirementIdList: this.form.requirementIdList
        //   }
        //   // address: 'minio://arena-platform/requirement_recommend_attachment/21654469c31d4a45abf75e829f2a5860.'
        // })
        // if (data?.length) {
        //   const taskFile = data.find(val => val.type === 'ROUTE')
        //   this.taskFileId = taskFile?.id
        // }
      })
      // getRecommendItemListRequirement(postData).then(res => {
      //   if (!res.data.length) {
      //     showToast('该需求暂无推荐项', 'warning')
      //     this.recommendList = []
      //     return
      //   }
      //   this.recommendList = res.data
      //   this.drawMap(true)
      // })
    },
    drawPoints(showFitView) {
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalRecommendMarker')
      this.$refs.baseMap.clearObjects(globalMarker)
      // 自定义点位样式
      const markers = this.recommendList.map((val, index) => {
        return {
          id: val.id,
          style: {
            width: 40,
            height: 40,
            anchor: { x: 20, y: 20 },
            src: require('@/assets/images/point.png')
          },
          position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]]),
          properties: { type: 'recommend' }
        }
      })
      // 仅搜索区域时
      if (showFitView) {
        this.$refs.baseMap.showFitView(markers)
      }
      // if (this.disabled) {
      this.$refs.baseMap.initMarkers(markers, 'globalRecommendMarker')
    },
    drawCluster(showFitView) {
      const markers = this.recommendList.map((val, index) => {
        return {
          id: val.id,
          style: {
            width: 20,
            height: 20,
            anchor: { x: 10, y: 10 },
            src: require('@/assets/images/point.png')
          },
          position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]])
        }
      })
      // 仅搜索区域时
      if (showFitView) {
        this.$refs.baseMap.showFitView(markers)
      }
      this.$refs.baseMap.initClusterMarkers(markers, 'globalRecommendMarkerCluster')
    },
    drawLines(showFitView) {
      const lines = this.recommendList
        .filter(val => val.line?.coordinates?.length)
        .map((val, index) => {
          return {
            id: val.id,
            // color: '#409eff',
            paths: val.line.coordinates.map(coord => [coord[1], coord[0]]),
            styles: {
              arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
            },
            position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]])
          }
        })
      const options = {
        // styles: {
        //   borderWidth: 2,
        //   borderColor: '#a8a8a8',
        //   showArrow: true,
        //   arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        // }
      }
      // 仅搜索区域时
      if (showFitView) {
        this.$refs.baseMap.showFitView(lines)
      }
      this.$refs.baseMap.createPolylineLayer(lines, options, 'poi-polyline-layer', 2, 'globalRecommendPolylineLayer')
    },
    drawAreas(showFitView) {
      const areas = this.recommendList
        .filter(val => val.polygon?.coordinates?.length)
        .map((val, index) => {
          return {
            id: val.id,
            paths: val.polygon.coordinates[0].map(coord => latLngPoint([coord[1], coord[0]])),
            position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]])
          }
        })
      const options = {}
      // 仅搜索区域时
      if (showFitView) {
        this.$refs.baseMap.showFitView(areas)
      }
      this.$refs.baseMap.createPolygonLayer(areas, options, 'poi-polygon-layer', 2, 'globalRecommendPolygonLayer')
    },
    clearPlanMap() {
      this.planRouteList = []
      this.routePointList = []
      // this.clearRoutePoints = []
      this.$refs.baseMap.clearObjects('globalRoutePlanPolylineLayer', 'globalMultiLabel', 'globalMarker')

      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.close()

      //取消运动路线
      this.currentRouteKey && this.getRouteCenter({ id: this.currentRouteKey })

      // 清除拖拽marker
      const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
      this.$refs.baseMap.removeGeo(globalMapEditorMarker?.getGeometries()?.map(val => val.id))
      // this.clearMarkers()
    },
    goPassCenter(e, routeId) {
      if (this.currentRouteKey === routeId) {
        if (this.currentPassMarkerId === e.id) {
          this.currentPassMarkerId = ''
        } else {
          this.currentPassMarkerId = e.id
        }
        const globalMultiLabel = this.$refs.baseMap.getGlobalMapObj('globalMultiLabel')
        let passPoints = globalMultiLabel.getGeometries()
        const clickedPassPoints = passPoints.map((val, index) => {
          return {
            id: val.id,
            styleId:
              this.currentPassMarkerId && val.id === 'routePoint-' + this.currentPassMarkerId
                ? 'selectedStyle'
                : val.id,
            position: val.position,
            content: val.content
          }
        })
        globalMultiLabel.setGeometries(clickedPassPoints)

        if (e.lat && e.lng) {
          this.$refs.baseMap.goCenter([e.lng, e.lat])
        }
      } else {
        showToast('请先选择对应路线', 'warning')
      }
    },
    zoomChanged(e) {
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      this.zoom = globalMap.getZoom()
      if (this.zoom <= 15 && this.geoType !== CONST_POINT) {
        this.changeGeoType(CONST_POINT)
      }
    },
    changeBounds(bounds) {
      this.bounds = bounds
      if (this.showRoad) {
        this.handleDisplayRoadChange(true)
      }
    },

    removeRoutePoint(index) {
      this.routePointList.splice(index, 1)
    },
    async generateRoutes() {
      let globalDriving = this.$refs.baseMap.getGlobalMapObj('globalDriving')
      if (!globalDriving) {
        globalDriving = this.$refs.baseMap.initDriving({
          mp: true,
          noStep: true,
          policy: 'PICKUP,NAV_POINT_FIRST'
        })
      }
      // 校验路径完整性
      let routeCheckList = this.routePointList.filter(val => val.type === 'start' || val.type === 'end')
      if (routeCheckList?.length > 1) {
        this.$refs.baseMap.clearObjects('globalRoutePlanPolylineLayer', 'globalMultiLabel', 'globalMarker')
        this.setRoutePointMarkers(this.routePointList)
        const startPoint = this.routePointList[0]
        const endPoint = this.routePointList[this.routePointList?.length - 1]
        const waypoints = this.routePointList.slice(1, this.routePointList?.length - 1)
        const routes = await drivingSearch(globalDriving, {
          from: latLngPoint({ lat: startPoint.lat, lng: startPoint.lng }),
          to: latLngPoint({
            lat: endPoint.lat,
            lng: endPoint.lng
          }),
          waypoints: waypoints.map(val => latLngPoint({ lat: val.lat, lng: val.lng })),
          servicesk: GLB_CONFIG.TxMapSecretKey
        })
        this.planRouteList = routes.result.routes
        this.drawRoutePlanLines(routes.result.routes)
        const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
        globalInfoWindow?.close()
      } else {
        showToast('请至少选择起点和终点', 'warning')
      }
    },
    confirmRoute() {
      const _this = this,
        queryParamItem = this.suggestionList.find(val => val.id === _this.queryParam.key)
      if (this.planRouteList?.length) {
        const globalRoutePlanPolylineLayer = this.$refs.baseMap.getGlobalMapObj('globalRoutePlanPolylineLayer')
        let routeLines = globalRoutePlanPolylineLayer.getGeometries()
        const highlightIndex = routeLines.findIndex(val => val.styleId === _this.selectedStyleName)
        ElMessageBox.prompt('', _this.$t('路线名称'), {
          confirmButtonText: _this.$t('确认'),
          cancelButtonText: _this.$t('取消'),
          inputValue: queryParamItem?.title,
          inputValidator(val) {
            if (!val) {
              return '请输入路线名称'
            }
          }
        }).then(async ({ value }) => {
          this.planRouteList[highlightIndex].name = value
          const route = { markers: this.routePointList, detail: this.planRouteList[highlightIndex], id: getUuid() }

          this.routeList.push(route)
          this.allRouteList.push(route)
          this.clearPlanMap()
          this.$refs.baseMap.clearObjects('globalRouteListPolylineLayer')
          this.drawRouteListLines(this.routeList)
        })
      } else {
        showToast(this.$t('请先选择路线'), 'warning')
      }
    },
    async save() {
      // if (this.routeList?.length) {
      this.$emit('reload', this.routeList)
      // } else {
      //   showToast(this.$t('请先选择路线'), 'warning')
      // }
    },
    togglePassPoints(route) {
      if (route.markers?.length > 2) {
        route.togglePassPointsFlag = !route.togglePassPointsFlag
      }
    },
    getIconCode(geoType) {
      let iconCode = ''
      switch (geoType) {
        case CONST_POINT:
          iconCode = 'el-icon-location'
          break
        case 'line':
          iconCode = 'el-icon-top-right'
          break
        case 'area':
          iconCode = 'el-icon-help'
          break
        case 'cluster':
          iconCode = 'el-icon-connection'
          break
      }
      return iconCode
    },
    changeGeoType(val) {
      this.geoType = val
      this.drawMap()
    },
    drawMap(showFitView) {
      this.clearMap()
      this.$refs.baseMap.clearObjects('globalRecommendMarkerCluster')
      switch (this.geoType) {
        case CONST_POINT:
          this.drawPoints(showFitView)
          break
        case 'line':
          this.drawLines(showFitView)
          break
        case 'area':
          this.drawAreas(showFitView)
          break
        case 'cluster':
          this.drawCluster(showFitView)
          break
      }
    },
    loadRoutesByReq() {
      const promises = this.requirementIdList?.map(val =>
        getRequirementRecommends({
          requirementId: val,
          type: 'ROUTE'
        }).then(res => res)
      )
      Promise.all(promises || []).then(result => {
        // 会获取到两个文件，取第一个文件
        const routes = result.map(val => (val.data?.length ? val.data[0] : [])).flat(2)
        this.getFilesPreview(routes)
        // this.$refs.MapPreviewRoute.show({
        //   type: this.dialogStatus,
        //   data: {
        //     routes: routes,
        //     checkedRoutes: data,
        //     requirementIdList: this.form.requirementIdList
        //   }
        //   // address: 'minio://arena-platform/requirement_recommend_attachment/21654469c31d4a45abf75e829f2a5860.'
        // })
        // if (data?.length) {
        //   const taskFile = data.find(val => val.type === 'ROUTE')
        //   this.taskFileId = taskFile?.id
        // }
      })
    },
    getFilesPreview(routes) {
      const _this = this
      // if (row.checkedRoutes?.length) {
      //   row.routes.push(...row.checkedRoutes)
      // }
      if (routes?.length) {
        const promises = routes.map(val =>
          getFilesPreview({
            path: val.attachment,
            token: util.getToken()
          }).then(res => _this.readFile(res))
        )
        Promise.all(promises).then(res => {
          let routes,
            // checkedRoutes,
            loadRouteList = []
          // if (row.checkedRoutes?.length) {
          //   checkedRoutes = JSON.parse(res[res?.length - 1])
          //   routes = res.map(val => JSON.parse(val)).flat(2)
          // } else {
          routes = res
            .map(val => {
              return JSON.parse(val)
            })
            .flat(2)
          // }
          routes?.forEach(val => {
            const index = this.routeList.findIndex(route => route.id === val.id)
            if (!~index) {
              loadRouteList.push(val)
            }
          })
          if (loadRouteList?.length) {
            const routeList = JSON.parse(JSON.stringify(this.routeList))
            this.allRouteList = JSON.parse(JSON.stringify(routeList.concat(loadRouteList)))
            this.routeList = JSON.parse(JSON.stringify(routeList.concat(loadRouteList)))
            this.clearPlanMap()
            this.$refs.baseMap.clearObjects('globalRouteListPolylineLayer')
            this.drawRouteListLines(this.allRouteList)
            this.leftHidden = true
            if (this.currentRouteKey) {
              this.getRouteCenter({ id: this.currentRouteKey })
            }
            // if (checkedRoutes?.length) {
            //   this.setRouteChecked(checkedRoutes)
            //   this.getRouteCenter(checkedRoutes[0])
            // } else {
            //   if (routes?.length) {
            // this.routeList = routes
            //     this.getRouteCenter(routes[0])
            //   }
            // }
          } else {
            showToast('该需求暂无新路线可供加载', 'warning')
          }
        })
      } else {
        this.leftHidden = false
      }
    },
    getBirderOverview() {
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalRecommendMarker')
      this.$refs.baseMap.showFitView(globalMarker.geometries)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/var.scss';

.map-selection-container {
  display: flex;
  font-size: 0;
  height: 100%;
  width: 100%;
  //height: calc(100vh - $footer-height - $header-height - var(--el-main-padding));

  //.left {
  //  border: 1px solid #eee;
  //  height: 100%;
  //  width: 25%;
  //}

  .middle {
    position: relative;
    border: 1px solid #eee;
    height: 100%;
    width: 45%;
    transition: all 0.3s;

    &.left-narrow,
    &.right-narrow {
      width: 75%;

      .ltw-icon {
        transition: all 0.3s;

        &.narrow {
          transform: rotateZ(180deg);
        }
      }
    }

    &.left-narrow.right-narrow {
      width: 100%;
    }

    .toggle {
      z-index: 1001;
      position: absolute;
      top: 4px;

      &.toggle-left {
        left: 10px;
      }

      &.toggle-right {
        right: 10px;
      }
    }

    .tool-button-container {
      max-width: calc(100% - 60px);
      display: flex;
      height: 65px;
      justify-content: center;
      position: absolute;
      //width: 570px;
      padding: 20px;
      z-index: 1001;
      top: 30px;
      left: 30px;
      background: white;
      box-shadow: -4px 0 10px 0 rgba(0, 0, 0, 0.05);

      .search-container {
        //width: 290px;
        display: flex;
        margin: 0 5px;
      }

      .intersection-button {
        display: flex;
        align-items: center;

        .el-radio-group {
          .el-radio {
            margin-right: 8px;
          }
        }
      }
      .zoom-tag{
        margin-left: 4px;
      }
    }

    .map-overview {
      user-select: none;
      height: 100%;
      width: 100%;
    }

    .bird-overview-btn {
      position: absolute;
      z-index: 2;
      right: 10px;
      bottom: 52px;
      width: 78px;
      font-size: 12px;
    }

    .opt-list {
      position: absolute;
      z-index: 2;
      right: 10px;
      top: 104px;
      background: #fff;
      padding: 5px 0;
      border-radius: 3px;

      &.status-list {
        top: auto;
        bottom: 10px;

        .opt-btn {
          pointer-events: none;
        }
      }

      .opt-btn {
        padding: 5px 10px;
        display: flex;
        text-align: left;
        font-size: 12px;
        cursor: pointer;

        .ltw-icon {
          display: inline-block;
          margin-right: 4px;
        }

        &.active,
        &:hover {
          background-color: rgb(235.9, 245.3, 255);
          color: #409eff;
        }

        .btn-text {
          display: flex;
        }

        .geo-type {
          height: 16px;
          width: 16px;
          margin-right: 6px;

          .el-image {
            height: 100%;
            width: 100%;
          }

          &.area {
            background: rgba(236, 99, 10, 0.62);
            border-radius: 4px;
          }

          &.point {
            height: 20px;
          }
        }

        //&:not(:first-child) {
        //  margin-top: 12px;
        //}
      }
    }
  }

  .left,
  .right {
    transition: all 0.3s;
    height: 100%;
    padding: 10px;
    background: #fff;
    box-sizing: border-box;
    overflow: hidden;
    //z-index: 1001;
    box-shadow: -4px 0 10px 0 rgba(0, 0, 0, 0.05);
    //min-width: 350px;
    &.narrow {
      width: 0;
      padding: 0;
    }

    :deep(.el-divider__text) {
      white-space: nowrap;
    }

    .route-point-list {
      width: 100%;
      height: calc(100% - 24px - 50px);

      .route-point-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
        border: 1px solid #e4e7ed;
        color: #606266;
        font-size: 14px;
        line-height: 20px;
        padding: 14px 20px;

        .route-point-label {
          width: 60px;
          margin-right: 10px;
        }

        .el-link {
          font-size: 24px;
          margin-left: 10px;
        }
      }

      .route-item {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
        border: 1px solid #e4e7ed;
        color: #606266;
        font-size: 14px;
        line-height: 20px;
        padding: 14px 20px;
        cursor: pointer;

        &.active {
          border-color: rgb(159.5, 206.5, 255);
          background-color: rgb(235.9, 245.3, 255);
          color: #409eff;
        }

        .route-name {
          display: flex;
          justify-content: space-between;
          font-size: 16px;
          font-weight: bold;
          flex-direction: row;
          width: 100%;

          .title-name {
            width: calc(100% - 24px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
          }

          .close-icon {
            width: 24px;
          }

          .el-link {
            font-size: 24px;
            //position
          }
        }

        .route-point-label {
          width: 100%;
          font-size: 12px;
          display: flex;
          margin-top: 10px;

          .el-tag {
            margin-right: 10px;
            width: 60px;
          }

          .route-point-name {
            //display: flex;
            //flex-direction: column;
            width: calc(100% - 60px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;

            &.pass {
              background: #f5f5f5;
              border-radius: 4px;
              padding: 0 8px;
            }

            &.active {
              color: #409eff;
            }

            .ltw-icon {
              margin-right: 4px;
            }

            .pass-num {
              display: flex;
              align-items: center;
              flex-direction: row;
            }

            .pass-points {
              height: 0;
              transition: all 0.3s;

              &.toggle-pass-points {
                height: auto;
              }

              .route-point-name {
                width: 100%;
                padding: 2px 0;
                color: #606266;

                &:hover {
                  color: #909399;
                }

                &.active {
                  color: #409eff;
                }
              }
            }
          }
        }
      }
    }

    .footer {
      text-align: right;
    }
  }

  .left {
    width: 30%;
    //min-width: 240px;
  }

  .right {
    position: relative;
    width: 25%;

    .load-routes-by-req {
      position: absolute;
      top: 4px;
      right: 14px;
    }

    //min-width: 180px;
  }
}
</style>
