<template>
  <div class="bs-tag-group-panel" id="bs-tag-group-panel">
    <VueDraggable v-model="dragData" :animation="300" :disabled="!timeEdit">
      <template v-for="item in dragData" :key="item">
        <bs-tag-group-card
            :tag-group="item"
            :edit-tags="editTags"
            :timeEdit="timeEdit"
            :attributes="attributes"
            :model="model"
            :continuous-units="continuousUnits"
            :classificationTag="classificationTag"
            ref="bsTagGroupCardRef"
            v-if="checkGroupVisible(item)"
            @tag-click="handleTagClick"
        ></bs-tag-group-card>
      </template>
    </VueDraggable>
    <div class="dialog-footer" v-if="editTags">
      <el-button @click="tagClose" id="cancel">{{ $t('取消') }}</el-button>
      <el-button type="primary" @click="tagSave" id="save">{{ $t('保存') }}</el-button>
    </div>
  </div>
</template>
<script>
import { VueDraggable } from 'vue-draggable-plus'
import BsTagGroupCard from '@/components/dataCollect/BsTagGroupCard'
import { ElButton } from 'element-plus'
import TAG_ATTRIBUTES from '@/plugins/constants/tag-attributes.js'

export default {
  name: 'BsTagGroupPanel',
  components: { BsTagGroupCard, ElButton, VueDraggable },
  emits: ['tag-click', 'tag-save', 'tag-close'],
  props: {
    editTags: {
      type: Boolean,
      default: false
    },
    model: {
      type: String,
      default: 'priview'
    },
    data: {
      type: Array,
      default: () => []
    },
    continuousUnits: {
      type: Array,
      default: () => []
    },
    classificationTag: {
      type: Boolean,
      default: false
    },
    timeEdit: {
      type: Boolean,
      default: true
    },
    deleteTags: {
      type: Boolean,
      default: false
    },
    buttonUnDisabled: {
      type: Boolean,
      default: false
    },
    attributes: {
      type: Array,
      default: () => [TAG_ATTRIBUTES.MIN, TAG_ATTRIBUTES.MIN_DURATION]
    }
  },
  watch: {
    // 监听 props 变化并同步到本地副本
    data: {
      handler(newVal) {
        this.dragData = JSON.parse(JSON.stringify(newVal))
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      dragData: []
    }
  },
  created() {},
  methods: {
    checkGroupVisible(group) {
      if (this.classificationTag) {
        return true
      }
      if ((!group.children || group.children.length === 0) && (!group.tagList || group.tagList.length === 0)) {
        return false
      }
      if (this.model === 'priview' || this.model === 'simulation') {
        return group.checkedAll || group.isIndeterminate
      }
      return true
    },
    handleTagClick(param) {
      this.$emit('tag-click', param)
    },
    tagSave() {
      this.$emit('tag-save', this.dragData)
    },
    tagClose() {
      this.$emit('tag-close')
    }
  }
}
</script>
<style lang="scss" scoped>
.bs-tag-group-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dialog-footer {
  padding: var(--el-dialog-padding-primary);
  padding-top: 10px;
  text-align: right;
  box-sizing: border-box;
}

// .ltw-toolbar{
//     display: flex;
//     flex-direction: row;
//     justify-content: flex-end;
// }
</style>
