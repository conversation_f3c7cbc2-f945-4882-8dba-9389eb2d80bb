<template>
  <div class="fleet-model-mangement">
    <el-card shadow="hover" class="fleet-model">
      <ltw-input
        id="queryKey"
        :placeholder="$t('请输入关键字')"
        v-model="queryParam.key"
        clearable
        class="search-key"
        @clear="refresh"
      >
        <template #append>
          <el-button id="refresh" @click="refresh">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </template>
      </ltw-input>
      <el-card class="card-body" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-radio-group @change="filterPageData" v-model="queryParam.useType">
              <el-radio-button v-for="item in useTypeList" :label="item.code" :key="item.code"
                >{{ item.name }}
              </el-radio-button>
            </el-radio-group>
            <el-dropdown @command="handleCommand" class="batch-operate-btn">
              <el-button class="button" text>
                <ltw-icon icon-code="el-icon-more"></ltw-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="add" id="add">
                    <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                    {{ $t('新增') }}
                  </el-dropdown-item>
                  <el-dropdown-item command="edit" id="edit">
                    <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                    {{ $t('编辑') }}
                  </el-dropdown-item>
                  <el-dropdown-item command="batchRemove" id="batchRemove">
                    <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                    {{ $t('删除') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <el-scrollbar>
          <div class="fleet-model-list">
            <el-card
              class="fleet-model-item"
              :class="{ active: currentModel.id === item.id }"
              shadow="hover"
              v-for="item in pageData"
              :key="item.id"
              @click="getFleetVersion(item)"
            >
              <el-tooltip :enterable="false" :content="item.code">
                <div class="model-item model-title ellipsis" v-text="item.code"></div>
              </el-tooltip>
              <div class="model-item">
                <el-tooltip :enterable="false" :content="item.brand">
                  <div v-if="item.brand" class="ellipsis" v-text="item.brand"></div>
                </el-tooltip>
                <el-tooltip :enterable="false" :content="item.vehicleModel">
                  <div v-if="item.vehicleModel" class="ellipsis" v-text="item.vehicleModel"></div>
                </el-tooltip>
                <el-tooltip :enterable="false" :content="item.vehicleTypeName">
                  <div v-if="item.vehicleTypeName" class="ellipsis" v-text="item.vehicleTypeName"></div>
                </el-tooltip>
              </div>
              <!--              <div class="model-item ellipsis" v-text="item.vehicleModel + '-' + item.vehicleTypeName"></div>-->
              <div class="model-item ellipsis" v-text="item.projectName"></div>
              <el-link
                class="model-view"
                :type="currentModel.id === item.id ? 'warning' : 'primary'"
                @click="getModelDetail(item)"
              >
                <ltw-icon icon-code="el-icon-view"></ltw-icon>
              </el-link>
              <!--              <el-link class="model-view" :type="currentModel.id === item.id ? 'warning' : 'primary'" @click="getModelDetail(item)">详情</el-link>-->
            </el-card>
          </div>
        </el-scrollbar>
      </el-card>
    </el-card>
    <el-card shadow="hover" class="fleet-version">
      <el-scrollbar>
        <el-breadcrumb v-if="currentModel.id" separator="/">
          <el-breadcrumb-item>{{ currentModel.useTypeName }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentModel.code }}</el-breadcrumb-item>
          <el-breadcrumb-item v-if="getTypeNmae(type)">{{ getTypeNmae(type) }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="card-opt">
          <el-button v-if="type === 'view' && optType !== 'choose'" type="primary" text @click="addFleetVersion">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          </el-button>
          <el-button v-if="type === 'view' && optType !== 'choose'" type="warning" text @click="editFleetVersion">
            <ltw-icon icon-code="el-icon-edit"></ltw-icon>
          </el-button>
          <el-button v-if="type === 'view' && optType !== 'choose'" type="danger" text @click="deleteFleetVersion">
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
          </el-button>
          <el-button v-if="type === 'view' && optType !== 'choose'" type="info" text @click="copyFleetVersion">
            <ltw-icon icon-code="el-icon-copy-document"></ltw-icon>
          </el-button>
        </div>
        <div class="empty-content" v-if="type === '' && !fleetVersionList?.length">
          <el-empty description="暂无数据"></el-empty>
          <el-button @click="addFleetVersion()" v-if="currentModel.id" type="primary">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('新增车型版本') }}
          </el-button>
        </div>
        <div class="fleet-version-content" v-else>
          <div class="version-list" v-if="type === 'view'">
            <div
              class="version-item"
              :class="{ active: item.id === formData.id }"
              @click="changeVersion(item)"
              v-for="(item, index) in fleetVersionList"
              :key="index"
            >
              <div>
                <ltw-icon icon-code="el-icon-link"></ltw-icon>
              </div>

              {{ item.version }}
            </div>
          </div>
          <div class="step-content" v-if="type">
            <el-steps v-if="type === 'add'" simple :active="activeStep" process-status="finish" finish-status="success">
              <el-step :title="$t('版本信息')" />
              <el-step :title="$t('传感器信息')" />
            </el-steps>
            <el-tabs v-else v-model="activeStep">
              <el-tab-pane :label="$t('版本信息')" :name="0"></el-tab-pane>
              <el-tab-pane :label="$t('传感器信息')" :name="1"></el-tab-pane>
            </el-tabs>
          </div>
          <div class="version-detail">
            <div class="step1" v-show="activeStep === 0">
              <el-form
                :model="formData"
                :rules="formRules"
                ref="formRef"
                label-width="180px"
                :hide-required-asterisk="type === 'view'"
              >
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-form-item :label="$t('版本')" prop="version">
                      <ltw-input v-if="type !== 'view'" :limitSize="10" v-model="formData.version" id="version" />
                      <el-tag v-else>{{ formData.version }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('采集数据包类型')" prop="rawDataBagType">
                      <el-select
                        v-if="type !== 'view'"
                        multiple
                        collapse-tags
                        style="width: 100%"
                        v-model="formData.rawDataBagType"
                        :placeholder="$t('请选择')"
                        clearable
                      >
                        <el-option
                          v-for="item in rawDataBagTypeList"
                          :key="item.code"
                          :label="$t(item.name)"
                          :value="item.code"
                        />
                      </el-select>
                      <div v-else>
                        <el-tag v-for="(item, index) in formData.rawDataBagTypeNameList" :key="index"
                          >{{ item }}
                        </el-tag>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="$t('抽帧有效运行gap值')"
                      prop="frameEffectiveGap"
                      :rules="[
                        {
                          required: queryParam.useType === 'daq',
                          message: $t('请输入'),
                          trigger: 'change'
                        }
                      ]"
                    >
                      <ltw-input v-if="type !== 'view'" v-model="formData.frameEffectiveGap" id="frameEffectiveGap">
                        <template #append>
                          <span>%</span>
                        </template>
                      </ltw-input>
                      <el-tag v-else>{{ formData.frameEffectiveGap }}%</el-tag>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-tabs v-model="activeTab" class="template-tabs">
                  <el-tab-pane :label="$t('车端数据落盘模板')" name="vehicleEndDataTemplate"></el-tab-pane>
                  <el-tab-pane :label="$t('云端数据存储模板')" name="cloudDataTemplate"></el-tab-pane>
                </el-tabs>
                <Codemirror
                  v-model:value="formData[activeTab]"
                  :options="cmOptions"
                  @change="onChange"
                  @blur="onBlur"
                  @focus="onFocus"
                  @scroll="onScroll"
                />
              </el-form>
            </div>
            <div class="step1" v-show="activeStep === 1">
              <div class="header-title">
                <!-- {{ $t('传感器') }} -->
                <el-button size="small" id="addModality" type="primary" @click="addModality">
                  <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                  {{ $t('新增') }}
                </el-button>
              </div>
              <el-table :row-class-name="setClassName" :data="formData.versionMappingModalityVOS" ref="tableRef">
                <el-table-column type="expand" fixed="left">
                  <template #default="props">
                    <el-form class="expand-form" ref="formRef" label-width="140px">
                      <el-row :gutter="10">
                        <el-col :span="8">
                          <el-form-item :label="$t('分辨率')">
                            <el-tag>{{ props.row.cameraVO?.resolution }}</el-tag>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="$t('畸变模型')">
                            <el-tag>{{ props.row.cameraVO?.distortionModel }}</el-tag>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="$t('相邻两行曝光间隔')">
                            <el-tag>{{ props.row.cameraVO?.exposureInterval }}us</el-tag>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="$t('水平视角')">
                            <el-tag>{{ props.row.cameraVO?.hfov }}°</el-tag>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="$t('垂直视角')">
                            <el-tag>{{ props.row.cameraVO?.vfov }}°</el-tag>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="$t('备注')">
                            <el-tag>{{ props.row.cameraVO?.remark }}</el-tag>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </template>
                </el-table-column>
                <el-table-column type="index" width="50" fixed="left" />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('编码')"
                  prop="modality"
                  show-overflow-tooltip
                  width="150"
                  fixed="left"
                >
                  <!-- <template #default="scope">
																		  <el-link
																			type="primary"
																			@click="viewModality(scope.row)"
																			:underline="false"
																			>{{ scope.row.modality }}</el-link
																		  >
																		</template> -->
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('传感器')"
                  show-overflow-tooltip
                  width="130"
                  prop="modalityName"
                />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('传感器类型')"
                  prop="sensorTypeName"
                  width="100"
                />
                <el-table-column header-align="left" align="left" :label="$t('供应商')" prop="supplierName" />
                <el-table-column header-align="left" align="left" :label="$t('型号')" prop="model" />
                <el-table-column header-align="left" align="left" :label="$t('规格')" prop="specification" />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('安装') + $t('位置')"
                  prop="installPositionName"
                />
                <el-table-column header-align="left" align="left" :label="$t('指导位置x') + '(mm)'" prop="positionX" />
                <el-table-column header-align="left" align="left" :label="$t('指导位置y') + '(mm)'" prop="positionY" />
                <el-table-column header-align="left" align="left" :label="$t('指导位置z') + '(mm)'" prop="positionZ" />
                <el-table-column header-align="left" align="left" :label="$t('距地高度') + '(mm)'" prop="height" />
                <el-table-column header-align="left" align="left" :label="$t('图片中坐标')" prop="imageCoordinate" />
                <el-table-column header-align="left" align="left" :label="$t('msop')" prop="msop" />
                <el-table-column header-align="left" align="left" :label="$t('difop')" prop="difop" />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('帧同步偏移值' + '(ms)')"
                  prop="pcdJpgOffset"
                />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('间隔差的基准值') + '(ms)'"
                  prop="intervalDif"
                />
                <el-table-column
                  header-align="left"
                  align="left"
                  :label="$t('备注')"
                  show-overflow-tooltip
                  prop="remark"
                />
                <el-table-column header-align="left" align="left" fixed="right" :label="$t('操作')" width="170">
                  <template #default="scope">
                    <el-button-group>
                      <el-tooltip
                        effect="dark"
                        :content="$t('编辑')"
                        placement="top"
                        :enterable="false"
                        v-if="!scope.row.editing"
                      >
                        <el-button type="warning" @click="editModality(scope.row)">
                          <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                        <el-button type="danger" @click="deleteModality(scope.row)">
                          <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip effect="dark" :content="$t('详情')" placement="top" :enterable="false">
                        <el-button type="primary" @click="viewModality(scope.row)">
                          <ltw-icon icon-code="el-icon-view"></ltw-icon>
                        </el-button>
                      </el-tooltip>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="dialog-footer">
              <!-- <el-button
														@click="type = ''"
														v-if="type === 'view'"
														id="view-cancel"
														>{{ $t('关闭') }}</el-button
													  >
													  <template v-else> -->
              <!-- <template> -->
              <el-button v-show="activeStep === 0 && type !== 'view'" @click="cancel()" id="cancel"
                >{{ $t('取消') }}
              </el-button>
              <el-button v-show="activeStep === 0 && type === 'add'" type="primary" @click="next" id="next"
                >{{ $t('下一步') }}
              </el-button>
              <el-button
                v-show="activeStep === 1 && type === 'add'"
                type="primary"
                @click="previousStep"
                id="previousStep"
                >{{ $t('上一步') }}
              </el-button>
              <el-button
                v-show="activeStep === 0 && (type === 'edit' || type === 'copy')"
                type="primary"
                @click="next()"
                >{{ $t('保存') }}
              </el-button>
              <el-button v-show="activeStep === 1 && type === 'add'" type="primary" @click="cancel('reload')"
                >{{ $t('完成') }}
              </el-button>
              <!-- </template> -->
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-card>
  </div>
  <AddFleetModel ref="AddFleetModel" @reload="query" />
  <AddModality ref="AddModality" @reload="reloadModalityList"></AddModality>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import { deleteFtmVehicleVariant, listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import {
  saveFtmVehicleVariantVersion,
  deleteFtmVehicleVariantVersion,
  updateFtmVehicleVariantVersion,
  getFtmVehicleVariantVersion,
  listFtmVehicleVariantVersion,
  copyFtmVehicleVariantVersion
} from '@/apis/fleet/ftm-vehicle-variant-version'
import {
  listFtmVariantVersionMappingModality,
  deleteFtmVariantVersionMappingModality
} from '@/apis/fleet/ftm-variant-version-mapping-modality'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import AddFleetModel from '@/pages/fleet/dialog/AddFleetModel.vue'
import AddModality from '@/pages/fleet/dialog/AddModality.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'

export default {
  components: {
    LtwIcon,
    AddFleetModel,
    AddModality,
    Codemirror
  },
  name: 'FleetModelManagement',
  data() {
    const checkFrameEffectiveGap = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback()
      }
      let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      if (Number(value) > 100) {
        callback(new Error(this.$t('请勿输入超过100的数字')))
      } else if (!reg.test(value)) {
        callback(new Error(this.$t('请输入整数或一位小数')))
      } else {
        callback()
      }
    }
    return {
      selectedData: [],
      currentOrg: {},
      pageData: {
        // total: 0
      },
      queryParam: {
        // current: 1,
        // size: 10
      },
      useTypeList: [],
      currentModel: {},
      type: '',
      versionPageData: [],
      activeStep: 0,
      //新增车型版本
      formData: {},
      rawDataBagTypeList: [],
      cmOptions: {},
      activeTab: 'vehicleEndDataTemplate',
      formRules: {
        variantId: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        version: [{ required: true, message: this.$t('请输入'), trigger: 'change' }],
        rawDataBagType: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        // lidarTimestampAngle: [
        //   {
        //     validator: validateThreeFloatValidity,
        //     trigger: 'change'
        //   }
        // ],
        // syncFrameOffsetForward: [
        //   {
        //     validator: validateThreeFloatValidity,
        //     trigger: 'change'
        //   }
        // ],
        // syncFrameOffsetBack: [
        //   {
        //     validator: validateThreeFloatValidity,
        //     trigger: 'change'
        //   }
        // ],
        frameEffectiveGap: [
          {
            validator: checkFrameEffectiveGap,
            // pattern: /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,1})))$/,
            required: false,
            trigger: 'change'
          }
        ]
      },
      fleetVersionList: [],
      backupFormData: {},
      modelCheckAll: false
    }
  },
  props: {
    optType: String,
    version: String
  },
  watch: {
    type(val) {
      setTimeout(() => {
        this.cmOptions = {
          mode: 'application/json', // Language mode text/yaml、text/javascript
          theme: 'dracula', // Theme
          // readOnly: 'nocursor'
          indentUnit: 4, // 缩进多少个空格
          tabSize: 4, // 制表符宽度
          // lineNumbers: true, // 是否显示行号
          lineWrapping: true, // 是否默认换行
          // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
          readOnly: val === 'view', // 禁止用户编辑编辑器内容
          // line: true,
          smartIndent: true // 智能缩进
        }
      })
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.getUseType()
  },
  computed: {
    modelIsIndeterminate() {
      return this.selectedData?.length > 0 && this.selectedData?.length < this.pageData.records?.length
    }
  },
  methods: {
    getUseType() {
      listSysDictionary({ typeCode: 'use_type' }).then(res => {
        this.useTypeList = res.data
        if (res.data?.length) {
          this.queryParam.useType = res.data[0].code

          this.query()
        }
      })
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      // this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      listFtmVehicleVariant(this.queryParam).then(res => {
        this.pageData = res.data
        if (res.data?.length) {
          this.getFleetVersion(res.data[0])
        } else {
          this.currentModel = {}
          this.cancel('reload')
        }
      })
    },
    add() {
      this.$refs.AddFleetModel.show({
        type: 'add',
        useType: this.queryParam.useType
      })
    },
    edit(row) {
      this.$refs.AddFleetModel.show({
        type: 'edit',
        id: row.id
      })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleVariant(param).then(() => {
          this.currentModel = {}
          this.query()
        })
      })
    },
    view(row) {
      this.$refs.AddFleetModel.show({ type: 'view', id: row.id })
    },
    handleCommand(command) {
      // if (this.selectedData.length === 0) {
      //   this.$message.warning({
      //     message: '请先选择数据再执行批量操作',
      //     type: 'warning'
      //   })
      //   return
      // }
      if (command === 'add') {
        return this.add()
      }
      if (this.currentModel.id) {
        if (command === 'batchRemove') {
          // if (this.selectedData.length === 0) {
          //   return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
          // }
          this.singleRemove(this.currentModel)
        } else if (command === 'edit') {
          this.edit(this.currentModel)
        }
      } else {
        this.$message.warning({
          message: BASE_CONSTANT.BATCH_OPERATION_WARNING,
          type: 'warning'
        })
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    filterPageData() {
      this.query()
    },
    getFleetVersion(row) {
      this.currentModel = row
      this.cancel('reload')
      this.listRawDataTypeList()
    },
    listFtmVehicleVariantVersion() {
      if (this.currentModel.id) {
        listFtmVehicleVariantVersion({
          variantId: this.currentModel.id
        }).then(res => {
          this.fleetVersionList = res.data
          if (res.data?.length) {
            this.type = 'view'
            this.activeStep = 0
            this.activeTab = 'vehicleEndDataTemplate'
            if (!this.formData.id) {
              let form = res.data[res.data?.length - 1]
              if (form.rawDataBagType) {
                form.rawDataBagType = form.rawDataBagType?.split(',')
              }
              form.frameEffectiveGap = parseFloat(parseFloat(form.frameEffectiveGap * 100).toFixed(1))
              this.formData = form
            }
            this.getFtmVehicleVariantVersion()
          } else {
            this.type = ''
            this.backupFormData = {}
          }
        })
      } else {
        this.fleetVersionList = []
        this.type = ''
        this.backupFormData = {}
      }
    },
    listRawDataTypeList() {
      if (!this.rawDataBagTypeList?.length) {
        listSysDictionary({ typeCode: 'raw_data_bag_type' }).then(res => {
          this.rawDataBagTypeList = res.data
          // if (this.formData.id) {
          //   this.formData.rawDataBagTypeNameList = this.formData.rawDataBagType
          //     .split(',')
          //     .map(val => this.getDicName(val, this.rawDataBagTypeList))
          // }
        })
      }
    },
    getTypeNmae(type) {
      let typeName = ''
      switch (type) {
        case 'add':
          typeName = '新增车型版本'
          break
        case 'edit':
          typeName = '编辑车型版本'
          break
        case 'view':
          typeName = '查看车型版本'
          break
        case 'copy':
          typeName = '复制车型版本'
          break
      }
      return typeName
    },
    onChange() {},
    onBlur() {
      // console.log(this.formData[this.activeTab])
      // try {
      //   this.formData[this.activeTab] = JSON.stringify(
      //     JSON.parse(this.formData[this.activeTab] || '{}'),
      //     null,
      //     '\t'
      //   )
      // } catch (error) {
      //   console.log(error)
      // }
    },
    onFocus() {},
    onScroll() {},
    cancel(val) {
      this.$refs.formRef?.resetFields()
      this.formData = {}
      if (val === 'reload') {
        this.listFtmVehicleVariantVersion()
      } else {
        if (this.fleetVersionList?.length) {
          this.type = 'view'
        } else {
          //兼容五车型版本时，新增时取消的情况
          this.type = ''
        }
        this.activeStep = 0
        this.activeTab = 'vehicleEndDataTemplate'
        this.formData = JSON.parse(JSON.stringify(this.backupFormData))
      }
    },
    previousStep() {
      this.activeStep = 0
    },
    next() {
      if (this.type !== 'view') {
        this.$refs.formRef.validate(valid => {
          if (!valid) return
          let postData = {
            ...this.formData,
            rawDataBagType: this.formData.rawDataBagType.join(','),
            variantId: this.currentModel.id,
            frameEffectiveGap: parseFloat(parseFloat(this.formData.frameEffectiveGap / 100).toFixed(3))
          }
          if (this.type === 'copy') {
            this.copyFtmVehicleVariantVersion()
          } else if (this.type === 'add') {
            //防止出现新增多个车型版本的时候，上一个新增的id数据在新增第二个版本时带给后台
            if (this.formData.id) {
              updateFtmVehicleVariantVersion(postData).then(() => {
                this.activeStep++
                showToast('保存成功')
              })
            } else {
              saveFtmVehicleVariantVersion(postData).then(res => {
                this.activeStep++
                if (this.rawDataBagTypeList?.length) {
                  res.data.rawDataBagType = res.data.rawDataBagType?.split(',')
                  res.data.rawDataBagTypeNameList = res.data.rawDataBagType.map(val =>
                    this.getDicName(val, this.rawDataBagTypeList)
                  )
                }
                res.data.frameEffectiveGap = parseFloat(parseFloat(res.data.frameEffectiveGap * 100).toFixed(1))
                this.formData = res.data
                showToast('保存成功')
              })
            }
          } else if (this.type === 'edit') {
            updateFtmVehicleVariantVersion(postData).then(() => {
              if (this.type === 'add') {
                this.activeStep++
              }
              showToast('保存成功')
              this.cancel()
              this.listFtmVehicleVariantVersion()
            })
          }
        })
      } else {
        this.activeStep++
      }
    },
    reloadModalityList(variantVersionId) {
      listFtmVariantVersionMappingModality({
        variantVersionId: variantVersionId
      }).then(res => {
        this.formData.versionMappingModalityVOS = res.data
      })
    },
    deleteModality(row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVariantVersionMappingModality({ id: row.id }).then(() => {
          this.reloadModalityList(this.formData.id)
        })
      })
    },
    addModality() {
      this.$refs.AddModality.show({
        type: 'add',
        data: {
          variantVersionId: this.formData.id,
          variant: this.formData.code,
          useType: this.queryParam.useType
        }
      })
    },
    viewModality(row) {
      this.$refs.AddModality.show({
        type: 'view',
        data: row
      })
    },
    editModality(row) {
      this.$refs.AddModality.show({
        type: 'edit',
        data: {
          ...row,
          useType: this.queryParam.useType
        }
      })
    },
    changeVersion(row) {
      this.formData.id = row.id
      this.getFtmVehicleVariantVersion()
    },
    getFtmVehicleVariantVersion() {
      getFtmVehicleVariantVersion(this.formData.id).then(res => {
        if (this.rawDataBagTypeList?.length) {
          res.data.rawDataBagType = res.data.rawDataBagType?.split(',')
          res.data.rawDataBagTypeNameList = res.data.rawDataBagType.map(val =>
            this.getDicName(val, this.rawDataBagTypeList)
          )
        }
        res.data.frameEffectiveGap = parseFloat(parseFloat(res.data.frameEffectiveGap * 100).toFixed(1))
        this.formData = res.data
        this.backupFormData = JSON.parse(JSON.stringify(this.formData))
      })
    },
    getDicName(item, list) {
      const dicItem = list.find(val => item === val.code)
      return dicItem.name
    },
    addFleetVersion() {
      // this.currentModel = {}
      this.activeStep = 0
      this.formData = {
        rawDataBagType: [],
        vehicleEndDataTemplate: '{\n\t\n\n\n\n\n\n\n\n\n}',
        cloudDataTemplate: '{\n\t\n\n\n\n\n\n\n\n\n}'
      }
      this.type = 'add'
    },
    editFleetVersion() {
      this.type = 'edit'
    },
    copyFleetVersion() {
      this.activeStep = 0
      this.type = 'copy'
    },
    deleteFleetVersion() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleVariantVersion({ id: this.formData.id }).then(() => {
          this.type = ''
          if (this.formData.id) {
            this.$refs.formRef.resetFields()
          }
          this.formData = {}
          this.listFtmVehicleVariantVersion()
        })
      })
    },
    setClassName({ row }) {
      // 通过自己的逻辑返回一个class或者空
      return row.sensorType !== 'camera' ? 'no-expand' : ''
    },
    changeModelCheck(e, row) {
      if (e.target.tagName !== 'INPUT') return
      // console.log(e.target._modelValue)
      if (e.target._modelValue === false) {
        this.selectedData.push(row)
      } else {
        const index = this.selectedData.findIndex(val => val.id === row.id)
        this.selectedData.splice(index, 1)
      }
      this.modelCheckAll = this.selectedData?.length === this.pageData.records?.length
      // this.modelIsIndeterminate =
      //   this.selectedData?.length > 0 &&
      //   this.selectedData?.length < this.pageData.records?.length
    },
    handleModelCheckAllChange(val) {
      this.selectedData = val ? this.pageData.records : []
      this.pageData.records.forEach(item => (item.checked = val))
      // this.modelIsIndeterminate = false
    },
    getCurrentData() {
      return {
        modelId: this.currentModel.id,
        versionId: this.formData.id,
        version: this.formData.version,
        code: this.formData.code
      }
    },
    copyFtmVehicleVariantVersion() {
      let postData = {
        id: this.formData.id,
        rawDataBagType: this.formData.rawDataBagType.join(','),
        // syncFrameOffsetForward: this.formData.syncFrameOffsetForward,
        // frameEffectiveGap: this.formData.frameEffectiveGap,
        frameEffectiveGap: parseFloat(parseFloat(this.formData.frameEffectiveGap / 100).toFixed(3)),
        // syncFrameOffsetBack: this.formData.syncFrameOffsetBack,
        // lidarTimestampAngle: this.formData.lidarTimestampAngle,
        vehicleEndDataTemplate: this.formData.vehicleEndDataTemplate,
        cloudDataTemplate: this.formData.cloudDataTemplate,
        version: this.formData.version
      }
      copyFtmVehicleVariantVersion(postData).then(() => {
        showToast('复制成功')
        this.cancel('reload')
      })
    },
    getModelDetail(row) {
      this.$refs.AddFleetModel.show({
        type: 'view',
        id: row.id
      })
    }
    // getFormData(item) {
    //   this.getFtmVehicleVariantVersion(item)
    //   // this.formData = item
    // }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/var.scss';

.fleet-model-mangement {
  display: flex;
  height: calc(100vh - $header-height - $footer-height - var(--el-main-padding));

  .ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .fleet-model {
    width: 35%;
    margin-right: 10px;

    .search-key {
      width: auto;
      margin-bottom: 10px;
    }

    & > :deep(.el-card__body) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-radio-group {
        border-radius: 24px;

        .el-radio-button > :deep(.el-radio-button__inner) {
          border: none;

          &:hover {
            color: rgb(16, 41, 106);
          }
        }

        .el-radio-button.is-active > :deep(.el-radio-button__inner) {
          background-color: rgb(16, 41, 106);
          border-color: rgb(16, 41, 106);
          border-radius: 24px;

          &:hover {
            color: white;
          }
        }
      }
    }

    .card-body {
      flex-grow: 1;

      & > :deep(.el-card__body) {
        padding: 20px 10px 0 20px;
        height: calc(100% - 69px);
      }

      .fleet-model-list {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        // margin-top: 10px;
        // .model-check-all {
        //   position: absolute;
        //   left: 0;
        //   top: -30px;
        // }
        .fleet-model-item {
          width: calc(50% - 10px);
          border-radius: 10px;
          position: relative;
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: pointer;
          background: #f5f5f5;
          transition: all 0.5s;
          color: rgb(16, 41, 106);

          .model-view {
            position: absolute;
            right: 10px;
            bottom: 10px;
          }

          :hover {
            // color: white;
            // background: rgba(16, 41, 106, 0.3);
          }

          // & > :deep(.el-card__body) {
          //   // padding: 10px 0 0 0;
          //   padding: 0;
          // }
          &.active {
            background: rgb(16, 41, 106);
            color: white;

            .model-view {
              color: #fff;

              &:hover::after {
                border-color: #fff;
              }
            }
          }

          .model-item {
            font-weight: 600;
            font-size: 12px;
            margin-bottom: 10px;
            display: flex;

            .ellipsis {
              margin-right: 10px;
            }

            &:last-child {
              margin-bottom: 0;
            }

            // justify-content: space-between;
            // line-height: 30px;
            // &.item-center {
            //   justify-content: center;
            //   background: #000;
            //   border-radius: 10px;
            //   color: white;
            //   font-size: 16px;
            // }
            &.model-title {
              font-weight: 600;
              font-size: 20px;
            }
          }
        }

        @media screen and (max-width: 1600px) {
          .fleet-model-item {
            width: 100%;
          }
        }
      }
    }
  }

  .fleet-version {
    width: 65%;
    position: relative;

    .card-opt {
      position: absolute;
      right: 0;
      top: 0;

      .el-button + .el-button {
        margin-left: 0;
      }
    }

    .empty-content {
      text-align: center;
    }

    :deep(.el-card__body) {
      height: 100%;
    }

    .fleet-version-content {
      .version-list {
        margin: 30px 0 20px 0;
        display: flex;

        .version-item {
          flex-shrink: 0;
          min-width: 60px;
          text-align: center;
          padding: 10px;
          cursor: pointer;
          margin-right: 20px;
          // color: #409eff;
          border-radius: 16px;
          // background: #ecf5ff;
          // border-color: #d9ecff;
          transition: all 0.3s;

          &:hover {
            // background: #409eff;
            // color: white;
          }

          &.active {
            background: rgb(16, 41, 106);
            color: white;
          }
        }
      }

      .version-detail {
        .el-form {
          padding: 0 10px;
        }

        .el-tag {
          margin-right: 10px;
        }

        .dialog-footer {
          margin-top: 10px;
          text-align: center;
        }

        .expand-form {
          width: 800px;
        }

        // .codemirror-container{
        //   min-height: 200px;
        // }

        :deep(.no-expand) .el-table__expand-column .cell {
          display: none;
        }
      }

      .step-content {
        margin: 10px 0;
        // padding: 0 15%;
      }
    }
  }
}
</style>
