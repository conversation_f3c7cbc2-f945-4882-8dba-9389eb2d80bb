import { httpDelete, httpGet, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

const baseURL = GLB_CONFIG.devUrl.fileServer
const serverURL = GLB_CONFIG.devUrl.serviceSiteRootUrl
export const uploadFile = (data = {}, params = {}) => httpPost({ url: '', data, params, baseURL })
export const uploadCalibrationZipFile = (data = {}, params = {}) =>
  httpPost({
    url: '/fleet/ftm_calibration_parameters/uploadCalibrationZipFile',
    data,
    params,
    baseURL
  })
export const uploadFtmPakringLotData = (data = {}, params = {}) =>
  httpPost({
    url: serverURL + '/fleet/ftm_parking_lots/importSlotEntrance',
    data,
    params
  })
export const uploadWorkReport = (data = {}, params = {}) =>
  httpPost({
    url: serverURL + '/ftm/ftm_daily_work_reports/importWorkReportExcel',
    data,
    params
  })
export const uploadShadowModeVehicleCaliParam = (data = {}, params = {}) =>
  httpPost({
    url: serverURL + '/ftm/ftm_shadow_calibrations/importShadowVehicleCaliParamZip',
    data,
    params
  })
export const deleteFile = id => httpDelete({ url: '/' + id, baseURL })
export const downloadFile = id => httpGet({ url: '/' + id, baseURL })
export const getFileList = params => httpGet({ url: '/', params, baseURL: baseURL + '/list' })
export const getFilePreview = params => httpGet({ url: '/data/extracted/measurement/preview', params, baseURL })

// dongliang写在全局的
export const getFilesPreview = params =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/files/preview',
    params,
    responseType: 'blob'
  })
