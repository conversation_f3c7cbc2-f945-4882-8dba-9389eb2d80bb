<template>
  <div class="vehicle-card-enhanced">
    <el-card class="card" :class="{ 'selected': selected }" shadow="never" @click="handleCardClick">
      <!-- 车辆图片区域 -->
      <div class="vehicle-image-section">
        <!-- 能源方式图标 -->
        <!-- <div v-if="getEnergyIconName()" class="energy-icon-container">
          <svg class="energy-icon" aria-hidden="true">
            <use :xlink:href="`#svg-${getEnergyIconName()}`" />
          </svg>
        </div> -->

        <el-image
          :src="vehicleImage"
          fit="cover"
          class="vehicle-image"
        >
          <template #error>
            <div class="image-placeholder">
              <ltw-icon icon-code="el-icon-picture" class="placeholder-icon"></ltw-icon>
            </div>
          </template>
        </el-image>

      </div>

      <!-- 车辆信息区域 -->
      <div class="vehicle-info-section">
        <!-- 主要信息 -->
        <div class="main-info">
          <h3 class="vehicle-title" :title="getVehicleTitle()">{{ getVehicleTitle() }}</h3>
          <div class="vehicle-subtitle">
            <span class="brand-model">{{ getBrandModel() }}</span>
            <span class="type-info">{{ getTypeInfo() }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'


export default {
  name: 'VehicleCardEnhanced',
  components: {
    LtwIcon
  },
  props: {
    vehicle: {
      type: Object,
      required: true,
      default: () => ({})
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['view', 'edit', 'delete', 'detail'],
  computed: {
    vehicleImage() {
      if (this.vehicle.fileList?.length) {
        return GLB_CONFIG.devUrl.fileServer + '/' + this.vehicle.fileList[0].id + '?token=' + util.getToken()
      }
      // 根据车型返回默认图片
      return this.vehicle.vehicleType === 'sedan'
        ? require('@/assets/images/sedan.png')
        : require('@/assets/images/suv-new.png')
    }
  },
  methods: {
    getVehicleTitle() {
      return this.vehicle.code || 'Unknown Vehicle'
    },
    getBrandModel() {
      const brand = this.vehicle.vehicleModel || ''
      const model = this.vehicle.vehicleTypeName || ''
      return brand&&model? `${brand} | ${model}` : 'Unkown'
    },
    getTypeInfo() {
      return this.vehicle.energyTypeName || ''
    },

    // 获取能源类型
    getEnergyType() {
      return this.vehicle.energyTypeName || ''
    },

    // 获取能源方式图标名称
    getEnergyIconName() {
      const energyType = this.getEnergyType()
      if (!energyType) return null

      // 根据能源类型返回对应的SVG图标ID
      const iconMap = {
        '纯电': 'battery-electrical',
        '插混': 'hybrids',
        '燃油': 'petrol',
      }

      if (iconMap[energyType]) {
        return iconMap[energyType]
      }

      return null
    },

    handleCardClick() {
      this.$emit('detail', this.vehicle)
    }
  }
}
</script>

<style lang="scss" scoped>
// 卡片宽度完全依赖父容器
.vehicle-card-enhanced {
  width: 100%;

  .card {
    border: calc(1px * var(--scale-factor, 1)) solid #eff1f2;
    border-radius: calc(2px * var(--scale-factor, 1));
    transition: all 0.3s ease;
    cursor: pointer;   
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);

    &:hover {
      box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.18), 0px 4px 8px rgba(0, 0, 0, 0.08);
      border-color: #d0d4d8;
    }

    // 选中状态样式
    &.selected {
      border: calc(1px * var(--scale-factor, 1)) solid #5755FF;
      
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);

      &:hover {
        border-color: #5755FF;
        box-shadow: 0px 12px 28px rgba(87, 85, 255, 0.4), 0px 6px 12px rgba(87, 85, 255, 0.2);
      }
    }

    :deep(.el-card__body) {
      padding: 0;
    }
  }
  
  .vehicle-image-section {
    position: relative;
    height: calc(160px * var(--scale-factor, 1));
    overflow: hidden;
    background: #E7ECF4;

    // 能源方式图标
    .energy-icon-container {
      position: absolute;
      top: 8px;
      left: 8px;
      z-index: 10;
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      .energy-icon {
        width: 20px;
        height: 20px;
        fill: currentColor;
      }
    }

    .vehicle-image {
      width: 100%;
      height: 100%;

      .image-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #f8f9fa;
        color: #8a9097;

        .placeholder-icon {
          font-size: calc(32px * var(--scale-factor, 1));
        }
      }
    }
    
    .status-indicators {
      position: absolute;
      top: 12px;
      left: 12px;
      display: flex;
      gap: 6px;
      
      .status-dot,
      .action-dot,
      .warning-dot,
      .info-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 0.8);
      }
      
      .status-dot {
        &.status-working {
          background-color: #f56c6c;
        }
        &.status-free {
          background-color: #67c23a;
        }
        &.status-maintenance {
          background-color: #e6a23c;
        }
        &.status-offline {
          background-color: #909399;
        }
        &.status-unknown {
          background-color: #d3d3d3;
        }
      }
      
      .action-dot {
        background-color: #5755ff;
      }
      
      .warning-dot {
        background-color: #ff9500;
      }
      
      .info-dot {
        background-color: #00c4cc;
      }
    }
  }
  
  .vehicle-info-section {
    padding: calc(16px * var(--scale-factor, 1));

    .main-info {
      margin-bottom: calc(12px * var(--scale-factor, 1));
      
      .vehicle-title {
        font-size: 14px;
        font-weight: 600;
        color: #5755FF;
        margin: 0 0 4px 0;
        line-height: 1.4;
        // 文本省略号效果
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default; // 显示默认光标以便hover显示tooltip
      }
      
      .vehicle-subtitle {
        display: flex;
        flex-direction: column;
        gap: 2px;
        
        .brand-model {
          font-size: 12px;
          color: #8a9097;
          font-weight: 500;
        }

        .type-info {
          font-size: 12px;
          color: #8a9097;
        }
      }
    }
    
    .detail-info {
      margin-bottom: 16px;
      
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 11px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-label {
          color: #8a9097;
          flex-shrink: 0;
        }
        
        .info-value {
          color: #4e5256;
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 60%;
          
          &.vin-code {
            font-family: monospace;
            font-size: 10px;
          }
        }
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      
      .action-btn {
        font-size: 11px;
        padding: 4px 8px;
        border-radius: 2px;
        border: 1px solid #d0d4d8;
        background: white;
        color: #4e5256;
        
        &:hover {
          border-color: #5755ff;
          color: #5755ff;
        }
        
        &.view-btn:hover {
          background: #f5f5ff;
        }
        
        &.edit-btn:hover {
          background: #f0f9ff;
        }
        
        &.more-btn {
          padding: 4px 6px;
          
          &:hover {
            background: #f8f9fa;
          }
        }
      }
      
      .more-actions {
        margin-left: auto;
      }
    }
  }
}
</style>
