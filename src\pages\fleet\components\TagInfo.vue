<template>
  <div class="tag-info">
    <el-card
      v-for="item in parkingLotFieldItems"
      :key="item"
      shadow="always"
      class="bs-tag-group-card"
      style="margin-bottom: 10px"
    >
      <template #header>
        <div class="bs-tag-group-card-header">
          <span>{{ item.name }}</span>
        </div>
      </template>
      <div class="bs-tag-group-card-body">
        <template v-for="(tag, tagIndex) in item.itemList" :key="tag">
          <el-tag @click="chooseTag(tag)" :effect="isChecked(tag) ? 'dark' : 'light'">
            {{ tag.itemName }}
            <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
          </el-tag>
        </template>
      </div>
    </el-card>
    <div class="footer">
      <el-button @click="cancel">{{ $t('取消') }}</el-button>
      <el-button v-if="!formReadonly" id="submit" type="primary" @click="submit">{{ $t('保存') }}</el-button>
      <el-button v-if="dialogStatus === 'map' && formReadonly" type="warning" @click="edit"
        >{{ $t('编辑') }}
      </el-button>
    </div>
  </div>
</template>
<script>
import { listParkingLotFieldWithItems } from '@/apis/fleet/parking-lot-management'

const defaultform = {}
export default {
  name: 'BaseInfo',
  emits: ['reload', 'save-info', 'cancel-btn', 'update_map_status_edit'],
  components: {},
  props: {
    cardInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dialogStatus: {
      type: String,
      default: () => {
        return ''
      }
    },
    mapStatusEdit: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  watch: {
    cardInfo: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
        const form = JSON.parse(JSON.stringify(this.form))
        delete form.fileIdList
        this.backupForm = JSON.stringify(form)
      },
      immediate: true
    },
    mapStatusEdit: {
      handler(val) {
        this.mapStatusEditFlag = val
      },
      immediate: true
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view' || (this.dialogStatus === 'map' && !this.mapStatusEditFlag)
    }
  },
  data() {
    return {
      form: Object.assign({}, defaultform),
      parkingLotFieldItems: [],
      mapStatusEditFlag: false,
      backupForm: []
    }
  },
  created() {
    this.listParkingLotFieldWithItems() // 环境类型
  },
  mounted() {
    this.backupForm = JSON.stringify(this.form)
  },
  methods: {
    listParkingLotFieldWithItems() {
      if (!this.parkingLotFieldItems?.length) {
        listParkingLotFieldWithItems().then(res => {
          this.parkingLotFieldItems = res.data
        })
      }
    },
    submit() {
      const postData = { ...this.form }
      this.$emit('save-info', postData)
    },
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'warning'
      // return 'info'
    },
    chooseTag(row) {
      if (this.formReadonly) return
      this.form.parkingLotItems = this.form.parkingLotItems || []
      const index = this.form.parkingLotItems?.findIndex(val => val.itemCode === row.itemCode)
      if (!~index) {
        this.form.parkingLotItems.push(row)
      } else {
        this.form.parkingLotItems.splice(index, 1)
      }
    },
    isChecked(item) {
      if (this.form.parkingLotItems?.length) {
        return ~this.form.parkingLotItems.findIndex(val => val.itemCode === item.itemCode)
      }
    },
    cancel() {
      if (this.mapStatusEditFlag) {
        this.mapStatusEditFlag = false
        this.$emit('update_map_status_edit', this.mapStatusEditFlag)
        this.form.parkingLotItems = JSON.parse(JSON.stringify(this.cardInfo.parkingLotItems))
      } else {
        this.$emit('cancel-btn')
      }
    },
    edit() {
      this.mapStatusEditFlag = true
      this.$emit('update_map_status_edit', this.mapStatusEditFlag)
    },
    checkFormChanged() {
      if (this.formReadonly) {
        return false
      } else {
        return this.backupForm !== JSON.stringify(this.form)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tag-info {
  .bs-tag-group-card-body {
    .el-tag {
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      color: #5755ff;
      background: rgb(238, 238, 255);
      border-color: rgb(171, 170, 255);

      &.el-tag--dark {
        background: #5755ff;
        color: #fff;
        border-color: #5755ff;
      }
    }
  }

  .footer {
    text-align: right;
  }
}
</style>
