/**
 * v-load-more 表格自定义指令加载
 * load-more-total 数据总行数
 * load-more-selector 数据行样式
 */

export default {
  updated: function (el, binding, vnode, oldVnode) {
    setTimeout(() => {
      const dataSize = vnode.props['load-more-total']
      const oldDataSize = oldVnode.props['load-more-total']

      if (dataSize === oldDataSize) {
        return
      }
      // 设置默认溢出显示数量
      var spillDataNum = 1

      // 设置隐藏函数
      var timeout = false
      let setRowDisableNone = function (topNum, showRowNum, binding) {
        if (timeout) {
          window.clearTimeout(timeout)
        }
        timeout = setTimeout(() => {
          binding.value.call(null, topNum, topNum + showRowNum + spillDataNum)
        })
      }

      const loadMoreSelector = vnode.props['load-more-selector']
      const waitTime = vnode.props['wait-time']
      let selectRow = el.querySelector(loadMoreSelector)
      const rowHeight = selectRow?.clientHeight
      let showRowNum = Math.ceil(el.clientHeight / rowHeight)
      let i = 1
      let clearTimeout
      function customSetInterval(func, times) {
        clearTimeout = setTimeout(() => {
          func()
          customSetInterval(func, times)
        }, times)
      }
      function turnTop() {
        selectRow = el.querySelector(loadMoreSelector)
        if (selectRow && selectRow.style) {
          selectRow.style.cssText = `opacity: 1;transition: all 0.3s;transformOrigin: center top;overflow: hidden;`
          selectRow.style.height = 0
          selectRow.style.opacity = 0
        }
        if (i >= dataSize) {
          i = 0
        }
        // new Promise((resolve, reject) => {
        setTimeout(() => {
          // selectRow.style.height = rowHeight + 'px'
          selectRow?.remove()
          setRowDisableNone(i, showRowNum, binding)
          i++
          // resolve()
        }, 300)
        // })
      }
      // turnTop()
      customSetInterval(turnTop, waitTime)
      el.addEventListener('mouseover', function () {
        window.clearTimeout(clearTimeout)
      })
      el.addEventListener('mouseleave', function () {
        customSetInterval(turnTop, waitTime)
      })
    })
  }
}
