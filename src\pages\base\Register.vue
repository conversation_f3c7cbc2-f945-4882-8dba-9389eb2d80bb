<template>
  <el-container :class="loginTheme === 'dark' ? 'dark' : 'light'">
    <ltw-header
      @switchSkin="switchSkin"
      @reload="goHome"
      :showTime="false"
      :logoSrc="logoSrc"
    >
      <template #title>
        <span class="page-name">{{ $t('用户注册') }}</span>
      </template>
    </ltw-header>
    <!-- <el-header class="ltw-header">
      <div class="ltw-header-left">
        <div class="title-container">
          <img @click="goHome()" class="logo" :src="logoSrc" v-if="logoSrc" />
          <span>{{ $t('WAVE3') }} </span>
          <span class="page-name">{{ $t('用户注册') }}</span>
        </div>
      </div>
      <div class="ltw-header-center">
        <slot name="center"> </slot>
      </div>
      <div class="ltw-header-right">
        <slot name="right">
          <div class="login-list">
            <el-link @click="forgetPassword" :underline="false">{{
              $t('忘记密码')
            }}</el-link>
            <el-divider direction="vertical" />
            <el-link @click="login" :underline="false">{{
              $t('登录')
            }}</el-link>
          </div>
          <el-dropdown @command="changeLang" trigger="click">
            <span class="dropDown"> CN/EN </span>
            <template #dropdown>
              <el-dropdown-menu class="menu">
                <el-dropdown-item
                  v-for="item in locales"
                  :key="item"
                  :command="item"
                  :class="{ active: locale === item }"
                >
                  {{ languageLabels[item] }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </el-header> -->
    <el-main>
      <el-card class="box-card">
        <el-form
          :model="form"
          :rules="formRules"
          ref="formRef"
          label-width="80px"
        >
          <el-form-item :label="$t('姓名')" prop="userName">
            <ltw-input id="userName" v-model="form.userName"></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('账号')" prop="loginName">
            <ltw-input
              id="loginName"
              v-model="form.loginName"
              placeholder="请使用您的NT账号"
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('邮箱')" prop="email">
            <ltw-input
              id="email"
              text-type="description"
              v-model="form.email"
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('密码')" prop="password">
            <ltw-input
              id="password"
              type="password"
              :placeholder="$t('请输入')"
              v-model="form.password"
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('确认密码')" prop="confirmPwd">
            <ltw-input
              id="confirmPwd"
              type="password"
              :placeholder="$t('请输入')"
              v-model="form.confirmPwd"
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('组织架构')" prop="orgIds">
            <el-cascader
              placeholder="可输入名称搜索"
              :options="orgList"
              :props="props"
              v-model="form.orgIds"
              :show-all-levels="false"
              filterable
              clearable
              popper-class="org-popper-class"
              class="org-selection"
              ref="orgSelection"
            >
            </el-cascader>
            <!-- <org-selection v-model="form.orgIds"></org-selection> -->
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button id="goHome" @click="goHome">{{ $t('返回') }}</el-button>
          <el-button id="submit" type="primary" @click="submit">{{
            $t('保存')
          }}</el-button>
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>

<script>
import LtwHeader from '@/components/base/LtwHeader'
import util from '@/plugins/util'
import { setLocale, showToast } from '@/plugins/util'
import { useI18n } from 'vue-i18n'
import OrgSelection from '@/components/system/OrgSelection'
import { registerUser } from '@/apis/base/index'
import { getOrgList } from '@/apis/base/index'

export default {
  name: 'ForgetPassword',
  components: {
    OrgSelection,
    LtwHeader
  },
  data() {
    const validateconfirmPwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else {
        if (value.length < 8) {
          callback(new Error('新密码至少8位以上'))
        } else {
          callback()
        }
      }
    }
    const validatepassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认密码'))
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      logoSrc: require('@/assets/images/login/logo-dark.png'),
      locales: ['zh', 'en'],
      languageLabels: {
        zh: '简体中文',
        en: 'English'
      },
      locale: '',
      form: {},
      formRules: {
        userName: [
          {
            required: true,
            message: this.$t('请输入名称'),
            trigger: 'blur'
          }
        ],
        loginName: [
          {
            required: true,
            message: this.$t('请输入账号'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            type: 'email',
            required: true,
            message: this.$t('请输入正确的邮箱'),
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            validator: validateconfirmPwd,
            trigger: 'blur'
          },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
            message:
              '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为 8 - 30位',
            trigger: 'change'
          }
        ],
        confirmPwd: [
          {
            required: true,
            validator: validatepassword,
            trigger: 'change'
          }
        ],
        orgIds: [
          {
            required: true,
            message: this.$t('请选择组织架构'),
            trigger: 'blur'
          }
        ]
      },
      orgList: [],
      props: {
        checkStrictly: true,
        value: 'id',
        label: 'name',
        children: 'children',
        leaf: 'asLeaf',
        emitPath: false,
        expandTrigger: 'hover'
      },
      loginTheme: ''
    }
  },
  created() {
    this.locale = useI18n().locale
    this.getOrgList()
  },
  methods: {
    goHome() {
      this.$router.push('/login')
    },
    changeLang(command) {
      setLocale(command)
    },
    resetForm() {
      this.$refs.formRef.resetFields()
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        let data = util.encrypt(JSON.stringify(postData))
        registerUser(data).then(() => {
          showToast(
            '注册成功，管理员审核通过后即可正常登陆使用系统，请关注您的注册邮箱!'
          )
          setTimeout(() => {
            this.login()
          }, 1000)
        })
      })
    },
    getOrgList() {
      getOrgList().then(res => {
        this.orgList = res.data
      })
    },
    switchSkin(loginTheme) {
      this.loginTheme = loginTheme
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item.active) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}
.el-container {
  height: 100%;
  flex-direction: column;
  .page-name {
    font-size: 14px;
    position: relative;
    letter-spacing: 1px;
    white-space: nowrap;
    padding-left: 10px;

    &::before {
      position: absolute;
      content: '';
      display: block;
      height: 20px;
      width: 1px;
      background: #bfbfbf;
      top: calc(50% - 10px);
      left: 0;
    }
  }
  .dropdown-divided {
    flex-direction: column;
    padding: 0;
    &::before {
      width: 100%;
    }
    span {
      padding: 0 17px;
    }
  }
  .el-dropdown {
    cursor: pointer;
  }
  .el-main {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: calc(100% - 60px);
    padding-top: 140px;
  }
  .box-card {
    display: inline-block;
    padding-top: 30px;
    // margin-top: -15%;
    width: 600px;
    // height: 400px;
    display: flex;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    :deep(.el-card__body) {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .el-steps {
      width: 100%;
      margin: 40px 0;
      .el-step {
        cursor: pointer;
      }
    }
    .el-form {
      width: 500px;
      :deep(.el-input-group__append) button.el-button {
        background-color: var(--el-button-bg-color, var(--el-fill-color-blank));
        color: var(--el-button-text-color, var(--el-text-color-regular));
      }
    }
    .finished {
      text-align: center;
      color: #505050;
      font-size: 14px;
      .dialog-footer {
        margin-top: 20px;
      }
    }
    .dialog-footer {
      text-align: center;
    }
  }
}
</style>
