import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmShadowCalibration = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations',
    data,
    params
})
export const updateFtmShadowCalibration = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations',
    data,
    params
})
export const deleteFtmShadowCalibration = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations',
    params
})
export const listFtmShadowCalibration = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations',
    params
})
export const listFtmShadowCalibrationSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations/selections',
    params
})
export const pageFtmShadowCalibration = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations/page',
    params
})
export const getFtmShadowCalibration = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_calibrations/' + id})
