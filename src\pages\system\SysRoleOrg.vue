<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-tool-container">
          <el-select
            v-model="queryParam.asTenant"
            placeholder="请选择"
            clearable
            @change="refresh"
          >
            <el-option
              v-for="item in asTenantOptionList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            placeholder="请输入名称"
            v-model="queryParam.name"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item.buttonCode)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ item.name }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              批量操作
              <ltw-icon
                icon-code="el-icon-arrow-down"
                class="el-icon--right"
              ></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        row-key="id"
        @selection-change="handleSelectionChange"
        :tree-pros="{ children: 'children', hasChildren: '!asLeaf' }"
        default-expand-all
        ref="tableRef"
      >
        <!--                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>-->
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="名称"
          width="300px"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="abbreviation"
          label="缩写"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="code"
          label="编码"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="innerCode"
          label="内部编码"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="socialCreditCode"
          label="统一社会信用代码"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="tagList"
          label="标签"
        >
          <template #default="scope">
            <div class="tag-container">
              <el-tag
                type="success"
                :key="tag.id"
                v-for="tag in scope.row.tagList"
                >{{ tag.tagName }}</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="address"
          label="地址"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="enabled"
          label="是否租户"
          width="80"
        >
          <template #default="scope">
            <el-switch
              v-model="scope.row.asTenant"
              :disabled="true"
            ></el-switch>
          </template>
        </el-table-column>

        <!--                <el-table-column header-align="left" align="left" prop="cantonCode" label="行政区"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="phone" label="机构电话"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="category" label="机构类别"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="postcode" label="邮编"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="mnemonicCode" label="助记码"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="parentId" label="父级单元id"></el-table-column>-->
        <!--                <el-table-column header-align="left" align="left" prop="status" label="状态"></el-table-column>-->
        <el-table-column
          header-align="left"
          align="left"
          prop="sortNum"
          label="顺序"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          label="操作"
          width="180"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="item.name"
                placement="top"
                :enterable="false"
              >
                <el-button
                  :type="item.buttonStyleType"
                  @click="executeButtonMethod(item.buttonCode, scope.row)"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      ></el-pagination>
    </el-card>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <ltw-input
            v-model="formData.name"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="缩写" prop="abbreviation">
          <ltw-input
            v-model="formData.abbreviation"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <ltw-input
            v-model="formData.code"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="内部编码" prop="innerCode">
          <ltw-input
            v-model="formData.innerCode"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="socialCreditCode">
          <ltw-input
            v-model="formData.socialCreditCode"
            :disabled="formReadonly"
          ></ltw-input> </el-form-item
        ><el-form-item label="标签" prop="tagList">
          <!--                    <el-tag type="success" v-for="item in formData.tagList" :closable="!formReadonly">{{item.name}}</el-tag>-->
          <!--                    <el-button v-if="tagSelectVisible" size="small" @click="showInput" icon="el-icon-plus">-->
          <!--                        添加标签</el-button>-->
          <el-select
            v-model="formData.tagIdList"
            multiple
            placeholder="请选择"
            clearable
            filterable
            allow-create
            default-first-option
            @change="handleTagSelectChange"
            :disabled="formReadonly"
          >
            <el-option
              v-for="item in tagList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父级机构" prop="parentId">
          <el-cascader
            v-model="formData.parentId"
            :props="parentSelectionProps"
            placeholder="请选择父机构"
            :options="parentOrgSelections"
            filterable
            :show-all-levels="false"
            @change="handleParentOrgChange"
            clearable
            :disabled="formReadonly"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="是否租户" prop="enabled">
          <el-switch
            v-model="formData.asTenant"
            :disabled="formReadonly"
          ></el-switch>
        </el-form-item>
        <!--                <el-form-item label="行政区" prop="cantonCode">-->
        <!--                    <ltw-input v-model="formData.cantonCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="机构电话" prop="phone">-->
        <!--                    <ltw-input v-model="formData.phone" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="机构类别" prop="category">-->
        <!--                    <ltw-input v-model="formData.category" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="邮编" prop="postcode">-->
        <!--                    <ltw-input v-model="formData.postcode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="地址" prop="address">-->
        <!--                    <ltw-input v-model="formData.address" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="助记码" prop="mnemonicCode">-->
        <!--                    <ltw-input v-model="formData.mnemonicCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="父级单元id" prop="parentId">-->
        <!--                    <ltw-input v-model="formData.parentId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="编码层级链" prop="codeLink">-->
        <!--                    <ltw-input v-model="formData.codeLink" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="状态" prop="status">-->
        <!--                    <ltw-input v-model="formData.status" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <el-form-item label="顺序" prop="sortNum">
          <el-input-number
            v-model="formData.sortNum"
            :disabled="formReadonly"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >关 闭</el-button
          >
          <template v-else>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveSysRoleOrg,
  updateSysRoleOrg,
  deleteSysRoleOrg,
  treePageSysRoleOrg,
  treeListSysRoleOrg,
  getSysRoleOrg
} from '@/apis/system/sys-role-org'
import { listSysTag } from '@/apis/system/sys-tag'
import BILL_CODE from '@/plugins/constants/bill-code'
import BASE_CONSTANT from '../../plugins/constants/base-constant'

const defaultFormData = {
  asTenant: true,
  sortNum: 1
}
export default {
  name: 'SysRoleOrg',
  data() {
    return {
      asTenantOptionList: [
        {
          label: '租户机构',
          value: true
        },
        {
          label: '非租户机构',
          value: false
        }
      ],
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        fuzzyQuery: true,
        asTenant: true,
        withTag: true
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      parentSelectionProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        disabled: 'disabled',
        emitPath: false
      },
      tagList: [],
      tagMap: {},
      tagHasChanged: false,
      parentOrgSelections: []
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
    this.getTagList()
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      treePageSysRoleOrg(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加组织机构'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.queryParentOrg()
    },
    save() {
      this.formData.tagList = []
      if (this.tagHasChanged) {
        if (this.formData.tagIdList) {
          this.formData.tagIdList.forEach(tagId => {
            if (this.tagMap[tagId]) {
              this.formData.tagList.push({
                tagId: tagId
              })
            } else {
              this.formData.tagList.push({
                tagName: tagId,
                billCode: BILL_CODE.APPLICATION
              })
            }
          })
        }
      }
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveSysRoleOrg(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateSysRoleOrg(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = '修改组织机构'
      this.dialogStatus = 'edit'
      getSysRoleOrg(row.id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          this.formData = res.data
          this.setTagIdList()
          this.queryParentOrg({ currentSelectedId: this.formData.id })
        })
      })
    },
    setTagIdList() {
      this.formData.tagIdList = []
      if (this.formData.tagList && this.formData.tagList.length > 0) {
        this.formData.tagList.forEach(tag => {
          this.formData.tagIdList.push(tag.tagId)
        })
      }
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysRoleOrg(param).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    view(row) {
      this.dialogTitle = '查看组织机构'
      this.dialogStatus = 'view'
      getSysRoleOrg(row.id).then(res => {
        this.dialogVisible = true
        this.queryParentOrg()
        this.$nextTick(function () {
          this.formData = res.data
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    dialogOpened() {},
    handleSelectionChange(value) {
      this.selectedData = value
    },
    getTagList() {
      listSysTag({ billCode: BILL_CODE.ORG }).then(res => {
        this.tagList = res.data
        if (this.tagList && this.tagList.length > 0) {
          this.tagList.forEach(tag => {
            this.tagMap[tag.id] = tag
          })
        }
      })
    },
    handleParentOrgChange(val) {
      if (!val) {
        this.formData.parentId = BASE_CONSTANT.TREE_ROOT_PARENT
      }
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    queryParentOrg(param) {
      treeListSysRoleOrg(param).then(res => {
        this.parentOrgSelections = res.data
      })
    },
    handleTagSelectChange() {
      this.tagHasChanged = true
    }
  }
}
</script>
