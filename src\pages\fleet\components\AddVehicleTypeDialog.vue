<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑车型' : '新增车型'"
    width="800px"
    :before-close="handleClose"
    class="add-vehicle-type-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      class="vehicle-form"
    >
      <el-row :gutter="20">
        <!-- 左侧：基本信息 -->
        <el-col :span="12">
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            
            <el-form-item label="车型编码" prop="code">
              <ltw-input v-model="formData.code" placeholder="请输入车辆编码" />
            </el-form-item>
            
            <el-form-item label="供应商" prop="supplierId">
              <el-select v-model="formData.supplierId" placeholder="请选择供应商" style="width: 100%">
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="车辆型号" prop="vehicleModel">
              <ltw-input v-model="formData.vehicleModel" placeholder="请输入车辆型号" />
            </el-form-item>
            
            <el-form-item label="类型" prop="vehicleType">
              <el-radio-group v-model="formData.vehicleType">
                <el-radio-button v-for="item in vehicleTypeOptions" :key="item.code" :label="item.code">
                  {{ item.name }}
                </el-radio-button>
                <!-- <el-radio-button label="SUV">SUV</el-radio-button>
                <el-radio-button label="Sedan">Sedan</el-radio-button>
                <el-radio-button label="Other">Other</el-radio-button> -->
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="能源方式" prop="energyType">
              <el-radio-group v-model="formData.energyType">
                <el-radio-button v-for="item in energyTypeOptions" :key="item.code" :label="item.code">
                  {{ item.name }}
                </el-radio-button>
                <!-- <el-radio-button label="1">纯电</el-radio-button>
                <el-radio-button label="2">燃油</el-radio-button>
                <el-radio-button label="3">混动</el-radio-button> -->
              </el-radio-group>
            </el-form-item>
            <el-form-item label="装车方案" prop="projectName">
              <ltw-input v-model="formData.projectName" placeholder="请输入装车方案" />
            </el-form-item>
          </div>
        </el-col>
        
        <!-- 右侧：车身信息 -->
        <el-col :span="12">
          <div class="form-section">
            <h4 class="section-title">车身信息</h4>
            
            <el-form-item label="轴距 (mm)" prop="wheelBase">
              <el-input v-model="formData.wheelBase" placeholder=""  />
            </el-form-item>
            
            <el-form-item label="车长 (mm)" prop="vehicleLength">
              <el-input v-model="formData.vehicleLength" placeholder="" />
            </el-form-item>
            
            <el-form-item label="车宽 (mm)" prop="vehicleWidth">
              <el-input v-model="formData.vehicleWidth" placeholder="" />
            </el-form-item>
            
            <el-form-item label="车高 (mm)" prop="vehicleHeight">
              <el-input v-model="formData.vehicleHeight" placeholder="" />
            </el-form-item>
            
            <el-form-item label="描述" prop="description">
              <el-input 
                v-model="formData.description" 
                type="textarea" 
                :rows="2"
                maxlength="100"
                show-word-limit
                placeholder="请输入车型描述"
              />
            </el-form-item>
          </div>
        </el-col>
      </el-row>
      
      <!-- 图片上传区域 -->
      <el-row>
        <el-col :span="24">
          <div class="form-section">
            <h4 class="section-title">图片</h4>
            <el-form-item prop="images">
              <upload-file
                ref="uploadImage"
                source-type="vehicle_variant_attachment"
                :limit="4"
                accept=".jpg,.jpeg,.png,.gif"
                :source-id="formData.id"
                v-model="formData.fileIdList"
                list-type="picture-card"
                :disabled="false"
              />
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { saveFtmVehicleVariant, updateFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { createNumberValidationRules } from '@/plugins/util'

export default {
  name: 'AddVehicleTypeDialog',
  components: {
    UploadFile
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'submit'],
  data() {
    return {
      loading: false,
      supplierOptions: [], // 供应商选项列表
      vehicleTypeOptions: [], // 车型类型选项
      energyTypeOptions: [], // 能源类型选项
      formData: {
        code: '',
        supplierId: '',
        vehicleModel: '',
        vehicleType: '',
        energyType: '',
        wheelBase: '',
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        description: '',
        fileIdList: [],
        projectName:''
      },
      formRules: {
        code: [
          { required: true, message: '请输入车型编号', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '请选择厂商', trigger: 'change' }
        ],
        vehicleModel: [
          { required: true, message: '请输入型号', trigger: 'blur' }
        ],
        vehicleType: [
          { required: true, message: '请选择车型类型', trigger: 'change' }
        ],
        energyType: [
          { required: true, message: '请选择能源方式', trigger: 'change' }
        ],
        wheelBase:createNumberValidationRules(4,0,false),
        vehicleLength:createNumberValidationRules(5,0,false),
        vehicleWidth:createNumberValidationRules(5,0,false),
        vehicleHeight:createNumberValidationRules(5,0,false)
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },

  async mounted(){
    await Promise.all([
      this.getSupplyName(),
      this.getVihicleType(),
      this.getEnergyType()
    ])

    if (this.isEdit && this.editData) {
      this.fillFormWithEditData()
    }
  },
  methods: {
    async getSupplyName(){
      try {
        const res = await listSysRoleOrg({tagCode: 'vehicle_brand_supplier'})
        this.supplierOptions = res.data || []
      } catch (error) {
        console.error('加载供应商数据失败:', error)
       // this.$message.error('加载供应商数据失败')
      }
    },
    async getVihicleType(){
      try {
        const res = await listSysDictionary({typeCode: 'variant_type'})
        this.vehicleTypeOptions = res.data || []
        if(!this.isEdit && res.data && res.data.length){
          this.formData.vehicleType = this.vehicleTypeOptions[0].code
        }
      } catch (error) {
        console.error('加载车型类型数据失败:', error)
        //this.$message.error('加载车型类型数据失败')
      }
    },
    async getEnergyType(){
      try {
        const res = await listSysDictionary({typeCode: 'energy_type'})
        this.energyTypeOptions = res.data || []
        if(!this.isEdit && res.data && res.data.length){
          this.formData.energyType = this.energyTypeOptions[0].code
        }
      } catch (error) {
        console.error('加载能源类型数据失败:', error)
        //this.$message.error('加载能源类型数据失败')
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    
    resetForm() {
      this.$refs.formRef?.resetFields()
      this.formData = {
        code: '',
        supplierId: '',
        vehicleModel: '',
        vehicleType: this.vehicleTypeOptions.length > 0 ? this.vehicleTypeOptions[0].code : '',
        energyType: this.energyTypeOptions.length > 0 ? this.energyTypeOptions[0].code : '',
        wheelBase: '',
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        description: '',
        projectName:'',
        fileIdList: []
      }
    },

    fillFormWithEditData() {
      if (this.editData) {
        this.formData = {
          ...this.editData,
          // 确保有ID用于UploadFile组件的source-id
          id: this.editData.id || ''
        }

        // UploadFile组件会根据source-id自动加载图片
      }
    },

    
    async handleSubmit() {
      try {
        await this.$refs.formRef.validate()

        // 额外校验vehicleType和energyType
        if (!this.formData.vehicleType || this.formData.vehicleType.trim() === '') {
          this.$message.error('请选择车型类型')
          return
        }

        if (!this.formData.energyType || this.formData.energyType.trim() === '') {
          this.$message.error('请选择能源方式')
          return
        }

        this.loading = true

        // 图片数据由UploadFile组件自动处理

        // 获取供应商中文名称
        const selectedSupplier = this.supplierOptions.find(supplier => supplier.id === this.formData.supplierId)
        const supplierName = selectedSupplier ? selectedSupplier.name : ''

        const postData = {
          ...this.formData,
          supplierName: supplierName // 添加供应商中文名称
        }

        if(this.isEdit){
         await  updateFtmVehicleVariant(postData)
        }else{
         await  saveFtmVehicleVariant(postData)
        }
        // 提交数据
        this.$emit('submit')
      //  this.$message.success(this.isEdit ? '车型编辑成功' : '车型添加成功')
        this.handleClose()
        
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-vehicle-type-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .vehicle-form {
    .form-section {
      margin-bottom: 20px;
      
      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;
      }
    }
    
    :deep(.el-form-item) {
      margin-bottom: 16px;
      
      .el-form-item__label {
        font-size: 12px;
        color: #606266;
      }
    }
    
    :deep(.el-radio-group) {
      .el-radio-button {
        .el-radio-button__inner {
          font-size: 12px;
          padding: 8px 12px;
          border-radius: 2px;
        }
        
        &.is-active .el-radio-button__inner {
          background-color: #5755ff;
          border-color: #5755ff;
        }
      }
    }
    
    .image-uploader {
      :deep(.el-upload--picture-card) {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        
        &:hover {
          border-color: #5755ff;
        }
      }
      
      :deep(.el-upload-list--picture-card) {
        .el-upload-list__item {
          width: 100px;
          height: 100px;
          margin: 0 8px 8px 0;
        }
      }
      
      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        text-align: center;
        line-height: 100px;
      }
    }
  }
  
  .dialog-footer {
    text-align: right;
    
    .el-button {
      font-size: 12px;
      padding: 7px 15px;
      border-radius: 2px;
      
      &.el-button--primary {
        background-color: #5755ff;
        border-color: #5755ff;
        
        &:hover {
          background-color: #4644dd;
          border-color: #4644dd;
        }
      }
    }
  }
}

.image-preview-dialog {
  :deep(.el-dialog__body) {
    text-align: center;
    
    img {
      max-width: 100%;
      max-height: 500px;
    }
  }
}
</style>
