<template>
  <div>
    <div v-show="tagList?.length > 0" style="margin-bottom: 20px">
      <el-card
        v-for="item in tagList"
        :key="item"
        shadow="always"
        class="bs-tag-group-card"
        style="margin-bottom: 10px"
      >
        <template #header>
          <div class="bs-tag-group-card-header">
            <span>{{ item.name }}</span>
          </div>
        </template>
        <div class="bs-tag-group-card-body">
          <template v-if="item?.children?.length">
            <function-scene-list
              :tagList="item.children"
              :checkedTagList="checkedTagList"
              :closeable="closeable"
              @close="handleClose"
            />
          </template>
          <template v-else>
            <template v-for="(tag, tagIndex) in item.items" :key="tag">
              <el-tag
                @click="chooseTag(tag)"
                :closable="closeable"
                :type="checkTagType(tag)"
                :effect="isChecked(tag) ? 'dark' : 'light'"
                @close="handleClose(tagIndex, item.items)"
              >
                <ltw-icon class="form-component" icon-code="svg-input" v-if="tag.componentType === 'input'"></ltw-icon>
                <ltw-icon class="form-component" icon-code="svg-select" v-if="tag.componentType === 'select'"></ltw-icon>
                <ltw-icon class="form-component" icon-code="svg-textarea" v-if="tag.componentType === 'textarea'"></ltw-icon>
                {{ tag.name }}
              </el-tag>
            </template>
            <!--            </el-card>-->
            <!--        </template>-->
          </template>
        </div>
      </el-card>
    </div>
    <el-empty v-show="tagList?.length == 0" description="暂无选择标签"></el-empty>
  </div>
</template>

<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import FunctionSceneList from '@/pages/dataCollect/components/FunctionSceneList.vue'

export default {
  components: {
    LtwIcon,
    FunctionSceneList
  },
  props: {
    tagList: {
      type: Array,
      default: []
    },
    checkedTagList: {
      type: Array,
      default: []
    },
    closeable: {
      type: Boolean,
      default: true
    },
    choosenable: {
      type: Boolean,
      default: false
    }
  },
  // computed: {
  //   checkedTagList() {
  //     return this.propsCheckedTagList
  //   }
  // },
  methods: {
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'primary'
    },
    handleClose(index, tagList) {
      this.$emit('close', index, tagList)
    },
    chooseTag(row) {
      if (!this.choosenable) return
      const index = this.checkedTagList.findIndex(val => val.id === row.id)
      if (!~index) {
        this.checkedTagList.push(row)
      } else {
        this.checkedTagList.splice(index, 1)
      }
    },
    isChecked(item) {
      if (this.checkedTagList?.length) {
        return ~this.checkedTagList.findIndex(val => val.code === item.code)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tag {
  margin: 0 5px 5px 0;
  cursor: pointer;

  .form-component{
    color: orange;
  }
}

:deep(.el-card__header, .el-card__body) {
  padding: 10px;
}
</style>
