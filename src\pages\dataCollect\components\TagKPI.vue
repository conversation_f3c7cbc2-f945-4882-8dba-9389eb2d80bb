<template>
  <div v-if="data.length > 0">
    <div v-for="item in data" :key="item.id">
      <bs-tag-group-card
        :tag-group="item"
        :time-edit="editTags"
        :continuous-units="continuousUnits"
        ref="bsTagGroupCardRef"
        @tag-click="handleTagClick"
        :classificationTag="true"
      ></bs-tag-group-card>
    </div>
  </div>
  <el-empty v-else description="暂无选择标签"></el-empty>
</template>

<script>
import bsTagGroupCard from '@/components/dataCollect/BsTagGroupCard.vue'

export default {
  components: { bsTagGroupCard },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    editTags: {
      type: Boolean,
      default: false
    },
    continuousUnits: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    data: {
      handler(newval, old) {
        this.kpiData = newval
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      kpiData: ''
    }
  },
  methods: {
    // checkGroupVisible(group) {
    //   if (!(group.children?.length || group.tagList?.length)) {
    //     return false
    //   }
    //   return true
    // },
    handleTagClick(param) {
      this.$emit('tag-click', param)
    }
  }
}
</script>
