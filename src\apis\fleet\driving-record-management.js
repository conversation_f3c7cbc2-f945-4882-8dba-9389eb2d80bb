import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveTaskDailyReports = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_task_daily_reports',
    data,
    params
  })
export const updateTaskDailyReports = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_task_daily_reports',
    data,
    params
  })
export const deleteTaskDailyReports = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_task_daily_reports',
    params
  })
export const pageDaqTaskRecordWithDailyReport = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/pageDaqTaskRecordWithDailyReport',
    data,
    params
  })
export const pageFtmTaskDailyReportDayGroup = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/pageFtmTaskDailyReportDayGroup',
    data,
    params
  })
export const exportWebTaskDailyReportGroupExcel = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/exportWebTaskDailyReportGroupExcel',
    responseType: 'blob',
    data,
    params
  })
export const exportWebTaskDailyReportExcel = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/exportWebTaskDailyReportExcel',
    responseType: 'blob',
    data,
    params
  })
export const statisticsDaqTaskDailyRecordVO = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/statisticsDaqTaskDailyRecordVO',
    data,
    params
  })

export const getTaskDailyReport = id =>
  httpGet({ url: `${GLB_CONFIG.devUrl.serviceSiteRootUrl}/ftm/ftm_task_daily_reports/${id}` })

export const getTreeDailyReportCategoryData = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_task_daily_reports/treeDailyReportCategoryData',
    params
  })
