import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsTag = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags', data, params})
export const updateBsTag = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags', data, params})
export const deleteBsTag = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags', params})
export const listBsTag = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags', params})
export const listBsTagSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags/selections', params})
export const pageBsTag = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags/page', params})
export const getBsTag = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags/' + id})
export const reorderBsTag = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tags/reorder', data, params})

