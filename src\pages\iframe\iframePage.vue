<template>
  <div v-loading="loading">
    <iframe :src="url" class="container" frameborder="0" sandbox="allow-scripts allow-forms allow-popups allow-top-navigation allow-same-origin" @load="onIframeLoad"></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return{
        loading:true,
        url: this.$route.meta.url
    }
  },
  watch: {
    $route(to, from) {
      // 当路由变化时，执行相应的更新逻辑
      this.url = this.$route?.meta?.url
    }
  },
  methods:{
   onIframeLoad(){
    this.loading = false
   }
  }
}
</script>

<style scope>
.container {
  height: 610px;
  width: 100%;
}
</style>
