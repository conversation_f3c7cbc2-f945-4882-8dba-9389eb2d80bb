<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :destroy-on-close="true"
  >

    <el-form
        :inline="true"
        class="form-record"
        :model="formDataRecord"
        :rules="formRulesRecord"
        ref="formRef"
        label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('标题')" prop="title">
            <ltw-input
                v-model="formDataRecord.title"
            ></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('生效日期')" prop="startDate">
            <el-date-picker
                v-model="formDataRecord.startDate"
                type="date"
                :disabled-date="disabledStartDate"
                :placeholder="$t('开始日期')"
                value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('失效日期')" prop="endDate">
            <el-date-picker
                v-model="formDataRecord.endDate"
                type="date"
                :disabled-date="disabledEndDate"
                :placeholder="$t('结束日期')"
                value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item :label="$t('文件')" prop="fileId">
          <div class="dialog-body">
            <upload-file
                ref="uploadImage"
                source-type="competitor_import_file"
                :limit="1"
                listType="text"
                accept=".xlsx"
                id="file"
                tip="limit 1 file"
                :drag="true"
                v-model="formDataRecord.fileId"
                @returnFile="assignFile"
            />
          </div>
        </el-form-item>
      </el-row>
     <el-row>
       <el-col :span="24">
         <el-form-item :label="$t('描述')" prop="description" style="width: 100%" >
           <ltw-input
               :rows="4"
               v-model="formDataRecord.description"
               textType="description"
               type="textarea"
           ></ltw-input>
         </el-form-item>
       </el-col>

     </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" id="view-cancel">{{ $t('关闭') }}</el-button>
        <el-button type="primary" @click="submit" id="view-submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'
import {showToast} from "@/plugins/util";
import {importSpareReport} from "@/apis/fleet/ftm-spare-part";

const defaultFormData = {}
export default {
  name: 'ImportParkingLot',
  emits: ['reload', 'cancel'],
  data() {
    return {
      formRulesRecord: {
        title: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        fileId: [
          {
            required: true,
            message: this.$t('请上传'),
            trigger: 'change'
          }
        ]
      },
      //新增车型版本
      formDataRecord: {
        description:"",
        fileId:[],
        endDate:"",
        startDate:"",
        title:""
      },
      file:{},
      dialogStatus: '',
      objectValue: {},
      dialogVisible: false,
      dialogTitle: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    UploadFile
  },
  created() {},
  methods: {
    assignFile(file) {
      this.file = file
    },
    disabledEndDate(val) {
      if (this.formDataRecord.startDate) {
        return new Date(val) < new Date(this.formDataRecord.startDate).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.formDataRecord.endDate) {
        return new Date(val) > new Date(this.formDataRecord.endDate).getTime()
      }
    },
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('导入对手件')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    submit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          let formData = new FormData();
          formData.append('file', this.file);
          formData.append('startDate', this.formDataRecord.startDate);
          formData.append('endDate', this.formDataRecord.endDate);
          formData.append('description', this.formDataRecord.description);
          formData.append('title', this.formDataRecord.title);
          formData.append('fileId', this.formDataRecord.fileId);
          importSpareReport(formData).then(res => {
            showToast("导入成功", 'success')
            this.dialogVisible = false
          })
        }
      })
    },
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    }
  }
}
</script>

