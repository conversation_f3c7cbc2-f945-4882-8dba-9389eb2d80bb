<!-- <dictionary-type-selection
v-model="form.dateType"
dictionaryType="test-type"
:collapse-tags="true"
:collapse-tags-tooltip="false"
:placeholder="$t('请选择')"
/> -->

<template>
  <el-select
    v-model="selectValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :multiple="multipleOptional"
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    :size="size"
    @change="handleChange"
  >
    <el-option
      v-for="item in list"
      :key="item.code"
      :label="item[modelName]"
      :value="item[modelCode]"
      :disabled="item[modelCode] === selectDisabled"
      id="dictionary-type-selection"
    ></el-option>
  </el-select>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { sysDictionaryTypes } from '@/apis/system/sys-dictionary'
import { ElSelect, ElOption } from 'element-plus'
export default {
  name: 'DictionaryTypeSelection',
  props: {
    size: String,
    selectDisabled: String,
    dictionaryType: String,
    modelValue: [String, Number, Array],
    disabled: Boolean,
    placeholder: {
      type: String,
      default: i18n.global.t('请选择')
    },
    clearable: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: true
    },
    collapseTagsTooltip: {
      type: Boolean,
      default: false
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    modelCode: {
      type: String,
      default: 'code'
    },
    modelName: {
      type: String,
      default: 'name'
    },
    modelOptions: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      $t: i18n.global.t,
      list: [],
      dataMap: [],
      queryParam: {},
      multipleOptional: false
    }
  },
  components: {
    ElSelect,
    ElOption
  },
  computed: {
    selectValue: {
      get() {
        if (this.multipleOptional) {
          return this.formatNumToArray(this.modelValue)
        } else {
          return this.modelValue
        }
      },
      set(val) {
        if (this.list?.length) {
          if (this.multipleOptional && val.length) {
            val = val.reduce((prev, cur) => Number(prev) + Number(cur))
          }
          this.$emit('update:modelValue', val)
        }
      }
    }
  },
  watch: {
    modelOptions(val) {
      if (val && val.length > 0) {
        this.list = this.modelOptions
        this.query()
        // let map = {}
        // val.forEach(item => {
        //   map[item[this.modelCode]] = JSON.parse(JSON.stringify(item))
        //   let modelNameKeyList = this.modelName.split(','),
        //     nameList = []
        //   for (let i = 0, iLen = modelNameKeyList?.length; i < iLen; i++) {
        //     if (item[modelNameKeyList[i]]) {
        //       nameList.push(item[modelNameKeyList[i]])
        //     }
        //   }
        //   item[this.modelName] = nameList.join('-')
        // })
        // this.dataMap = map
      }
    }
  },
  created() {
    if (this.autoLoad) {
      this.queryParam.typeCode = this.dictionaryType
      this.query()
    }
  },
  methods: {
    async query() {
      if (this.autoLoad) {
        let res = await this.sysDictionaryTypes()
        this.list = res.data.dictionaryList
        this.multipleOptional = res.data.multipleOptional
      }
      let dataMap = {}
      this.list.forEach(item => {
        dataMap[item[this.modelCode]] = JSON.parse(JSON.stringify(item))
        // 多个name拼接情况
        let modelNameKeyList = this.modelName.split(','),
          nameList = []
        for (let i = 0, iLen = modelNameKeyList?.length; i < iLen; i++) {
          if (item[modelNameKeyList[i]]) {
            nameList.push(item[modelNameKeyList[i]])
          }
        }
        item[this.modelName] = nameList.join('-')
      })
      this.dataMap = dataMap
    },
    sysDictionaryTypes() {
      return new Promise((resolve, reject) => {
        sysDictionaryTypes(this.queryParam)
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    formatNumToArray(modelValue) {
      let model = modelValue.toString(),
        selectValue = []
      for (let i = 0, iLen = model.length; i < iLen; i++) {
        let numValue = model[i] * Math.pow(10, iLen - 1 - i)
        if (numValue) {
          selectValue.push(numValue.toString())
        }
      }
      return selectValue
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      // let item = this.list.find(val => val.code === value)
      this.$emit('change', this.dataMap[value])
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped></style>
