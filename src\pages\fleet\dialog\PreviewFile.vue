<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="40%" @close="dialogClosed" @open="dialogOpened">
    <template #header>
      <div class="dialog-header">
        <div class="dialog-title" v-text="dialogTitle"></div>
        <el-tag :type="form.status === 'draft' ? 'warning' : 'success'">{{ form.statusName }}</el-tag>
      </div>
    </template>
    <Codemirror
      v-model:value="form.code"
      :options="cmOptions"
      @change="onChange"
      @blur="onBlur"
      @focus="onFocus"
      @scroll="onScroll"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" id="cancel">{{ $t('关闭') }}</el-button>
        <el-button v-if="form?.btn?.buttonCode" type="primary" @click="revoke">{{
          $t(form?.btn?.buttonName)
        }}</el-button>
        <el-button v-else type="primary" @click="publish" id="submit">{{ $t('发布') }}</el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script>
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import {
  previewFtmVehicleInstallationRecord,
  publishFtmVehicleInstallationRecord,
  revokeFtmVehicleInstallationRecord
} from '@/apis/fleet/ftm-vehicle-installation-record'
import { showToast, showConfirmToast } from '@/plugins/util.js'

const defaultform = {}
export default {
  name: 'PreviewFile',
  emits: ['reload'],
  components: { Codemirror },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      cmOptions: {},
      form: Object.assign({}, defaultform)
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        // case 'add':
        //   this.form.orgId = row.orgId
        //   this.dialogTitle = this.$t('新增司机')
        //   break
        // case 'edit':
        //   this.dialogTitle = this.$t('编辑司机')
        //   this.previewFtmVehicleInstallationRecord(row)
        //   break
        case 'view':
          this.dialogTitle = '配置信息'
          // this.dialogTitle = row.vin + (row.version && ' - ' + row.version)
          this.form = row
          // this.form.status = row.status
          this.previewFtmVehicleInstallationRecord(row)
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      // this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveSysDriver(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateSysDriver(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    publish() {
      showConfirmToast({
        message: this.$t('请确认是否发布该装车记录')
      }).then(() => { 
        publishFtmVehicleInstallationRecord({ id: this.form.id,vin: this.form.vin }).then(() => {
          showToast('已发布成功')
          this.cancel()
        })
      })
    },
    revoke() {
      showConfirmToast({
        message: this.$t('请确认是否撤回该装车记录')
      }).then(() => {
        revokeFtmVehicleInstallationRecord({ id: this.form.id }).then(() => {
          showToast('已撤回成功')
          this.cancel()
        })
      })
    },
    previewFtmVehicleInstallationRecord(row) {
      previewFtmVehicleInstallationRecord({
        id: row.id,
        vin: row.vin
      }).then(res => {
        this.form.code = res.data
        setTimeout(() => {
          this.cmOptions = {
            mode: 'application/json', // Language mode text/yaml、text/javascript
            theme: 'dracula', // Theme
            // readOnly: 'nocursor'
            indentUnit: 4, // 缩进多少个空格
            tabSize: 4, // 制表符宽度
            // lineNumbers: true, // 是否显示行号
            lineWrapping: true, // 是否默认换行
            // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
            readOnly: this.dialogStatus === 'view', // 禁止用户编辑编辑器内容
            // line: true,
            smartIndent: true // 智能缩进
          }
        })
      })
    },
    onChange() {},
    onBlur() {},
    onFocus() {},
    onScroll() {}
  }
}
</script>

<style scoped lang="scss">
.codemirror-container.height-auto {
  height: auto;
}

.dialog-header {
  display: flex;

  .dialog-title {
    margin-right: 10px;
  }
}
</style>
