<template>
  <el-select
    v-model="selectValue"
    :placeholder="$t('请选择')"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
  >
    <el-option
      v-for="item in list"
      :key="item.code"
      :label="item.name"
      :value="item.code"
      id="dictionary-selection"
    ></el-option>
  </el-select>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { ElSelect, ElOption } from 'element-plus'
export default {
  name: 'DictionarySelection',
  props: {
    dictionaryType: String,
    modelValue: [String, Number, Array],
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      $t: i18n.global.t,
      list: [],
      queryParam: {},
      map: {}
    }
  },
  components: {
    ElSelect,
    ElOption
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  mounted() {
    this.queryParam.typeCode = this.dictionaryType
    this.query()
  },
  methods: {
    query() {
      listSysDictionary(this.queryParam).then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          this.map = {}
          this.list.forEach(item => {
            this.map[item.code] = item
          })
        }
      })
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      let node = this.map[value]
      // this.$emit('change',value,node)
      this.$emit('change', {
        value,
        node
      })
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped></style>
