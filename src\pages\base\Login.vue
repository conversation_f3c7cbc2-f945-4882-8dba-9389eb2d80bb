<template>
  <div class="main" :class="loginTheme === 'dark' ? 'dark' : 'light'">
    <div class="top-line" v-if="loginTheme === 'light'"></div>
    <ltw-header :showTime="false" :theme-type="loginTheme" @switchSkin="switchSkin"> </ltw-header>
    <div class="login-container">
      <div class="slogan" v-if="loginTheme === 'light'">
        <div class="slogan-title">{{ $t('科技成就生活之美') }}</div>
        <div class="slogan-sec-title">
          {{ $t('博世为你的美好生活，再添一份自在') }}
        </div>
      </div>
      <div class="form-container">
        <el-form size="large" ref="loginFormRef" :model="loginFormData" :rules="loginRules" class="login-form">
          <div class="title">
            {{ $t('欢迎登录') }}<span v-if="loginTheme === 'light'" class="project-name">ARENA</span>
          </div>
          <el-form-item prop="loginName">
            <ltw-input
              v-focus
              id="loginName"
              :placeholder="$t('用户名')"
              v-model="loginFormData.loginName"
              type="text"
              @focus="isLoginName = true"
              @blur="isLoginName = false"
            ></ltw-input>
          </el-form-item>
          <el-form-item prop="password" @keyup.enter="handleLogin">
            <ltw-input
              id="password"
              :placeholder="$t('密码')"
              v-model="loginFormData.password"
              type="password"
              @focus="isPassword = true"
              @blur="isPassword = false"
            >
            </ltw-input>
            <el-link
              v-if="loginTheme === 'light'"
              class="forget-pwd"
              @click="forgetPwd()"
              type="primary"
              :underline="false"
            >
              {{ $t('忘记密码') + '？' }}
            </el-link>
          </el-form-item>

          <div class="login-btn" v-if="loginTheme === 'dark'">
            <el-link id="forgetPwd" @click="forgetPwd()" type="info" :underline="false">{{ $t('忘记密码') }} </el-link>
            <el-link id="register" @click="register()" type="primary" :underline="false">{{ $t('快速注册') }} </el-link>
          </div>
          <el-button id="handleLogin" :loading="loading" type="primary" style="width: 100%" @click="handleLogin">
            <span v-if="!loading">{{ $t('登 录') }}</span>
            <span v-else>{{ $t('请等待...') }}</span>
          </el-button>
        </el-form>
        <!-- <el-divider style="width: 85%; margin: 10px 0" /> -->
        <div class="register" v-if="loginTheme === 'light'">
          {{ $t('还没有账号') + '？' }}
          <el-link @click="register()" type="primary" :underline="false">
            {{ $t('去') + $t('注册') }}
          </el-link>
        </div>
      </div>
    </div>
    <div class="ltw-footer">Copyright © BOSCH - XC-DX/PJ-W3-PMT <span v-text="year"></span></div>
  </div>
</template>

<script>
import LtwHeader from '@/components/base/LtwHeader'
import { login } from '@/apis/base/index'
import util from '@/plugins/util'

export default {
  name: 'login',
  components: {
    LtwHeader
  },
  data() {
    return {
      isLoginName: false,
      isPassword: false,
      loginFormData: {},
      loginRules: {
        loginName: [
          {
            required: true,
            trigger: 'change',
            message: this.$t('用户名') + this.$t('不能为空')
          }
        ],
        password: [
          {
            required: true,
            trigger: 'change',
            message: this.$t('密码') + this.$t('不能为空')
          }
        ]
      },
      loading: false,
      loginTheme: 'light',
      year: ''
    }
  },
  directives: {
    focus: {
      // 指令的定义
      mounted(el) {
        let dom = el.querySelector('input') || el.querySelector('textarea')
        dom.focus()
      }
    }
  },
  created() {
    this.year = new Date().getFullYear()
    // this.loginTheme = localStorage.getItem('login-theme') || 'light'
  },
  methods: {
    handleLogin() {
      this.$refs.loginFormRef.validate(valid => {
        if (valid) {
          this.loading = true
          let data = util.encrypt(JSON.stringify(this.loginFormData))
          let params = {
            auth_type: process.env.NODE_ENV === 'development' ? 'shiro' : ''
          }
          login(data, params)
            .then(res => {
              let token = res.data
              util.setToken(token)
              this.$router.push(decodeURIComponent(this.$route?.query?.redirect || '/'))
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    register() {
      this.$router.push('/register')
    },
    forgetPwd() {
      this.$router.push('/forgetPassword')
    },
    switchSkin(loginTheme) {
      this.loginTheme = loginTheme
    }
  }
}
</script>

<style scoped lang="scss">
.main {
  background-image: url('@/assets/images/login/login-banner-light.png');
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.dark {
    background-image: url('@/assets/images/login/login-banner-dark.png');

    .login-container {
      .form-container {
        background: rgba(31, 58, 173, 0.12);

        .login-form {
          margin-bottom: 0;

          .title {
            font-size: 30px;
            font-family: SourceHanSansCN;
            font-weight: 500;
            color: #ffffff;
            padding-bottom: 45px;
          }

          #handleLogin {
            background: linear-gradient(90deg, #fec800, #fd8300);
            box-shadow: 0 1px 12px 0 rgba(0, 0, 0, 0.15);
            border-radius: 30px;
            border: none;
          }

          :deep(.el-input__wrapper) {
            background: rgba(0, 0, 0, 0);
            border-radius: 30px;

            .el-input__inner {
              color: white;
            }

            &.is-focus {
              box-shadow: 0 0 0 1px #ee9200 inset;
            }
          }

          .login-btn {
            #register {
              color: rgba(238, 146, 0, 1);
            }
          }
        }
      }
    }

    .ltw-footer {
      background: rgba(255, 255, 255, 0);
    }

    // .top-line{
    //   display: none;
    // }
  }

  .top-line {
    background-image: url('@/assets/images/login/top-line.png');
    background-size: 100% 100%;
    width: 100%;
    height: 4px;
  }

  .ltw-footer {
    width: 100%;
    line-height: 36px;
    color: #6d7477;
    // font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.4);
    text-align: center;
  }

  // background: linear-gradient(rgba(65, 105, 225, 1), rgba(100, 149, 237, 0.3));
  .login-container {
    position: relative;
    width: 100%;
    height: 100%;
    // box-shadow: 2px 2px 3px rgba(65, 105, 225, 0.5);
    display: flex;
    flex-direction: column;
    // align-items: center;
    justify-content: center;
    // background: #ffffff;
    // border-radius: 10px;
    .slogan {
      max-width: 50%;
      position: absolute;
      bottom: 60%;
      left: 12%;
      font-family: PingFang-SC-Bold, PingFang-SC;

      .slogan-title {
        font-size: 40px;
        font-weight: bold;
        line-height: 56px;
        color: #10306a;
        margin-bottom: 15px;
      }

      .slogan-sec-title {
        font-size: 24px;
        font-weight: 500;
        color: #1e497d;
        line-height: 33px;
      }
    }

    .form-container {
      margin-left: 64%;
      padding-bottom: 70px;
      width: 22%;
      min-width: 300px;
      background: #fff;
      transition: all 0.3s;
      // height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      :deep(.el-input__inner) {
        transition: all 0.3s;
      }

      .login-btn {
        // padding: 0 10px;
        // width: 80%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 22px;
      }

      .login-form {
        border-radius: 6px;
        padding: 15px 0;
        margin-bottom: 20px;
        width: 80%;

        .el-form-item__content {
          position: relative;

          .el-link {
            position: absolute;
            right: 10px;
          }
        }

        #handleLogin {
          transition: all 0.3s;
        }

        .title {
          transition: all 0.3s;
          padding: 15px 0;
          text-align: center;
          // display: flex;
          // justify-content: center;
          font-size: 24px;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: #232628;
          line-height: 33px;

          .project-name {
            margin-left: 10px;
          }
        }
      }
    }

    .register {
      font-size: 14px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #101213;
      line-height: 20px;

      .el-link {
        line-height: 20px;
        font-size: 14px;
      }
    }
  }
}
</style>
