<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="80%"
    @close="dialogClosed"
    @open="dialogOpened"
    :destroy-on-close="true"
    draggable
    class="collect-record-dialog"
  >
    <div class="ltw-toolbar">
      <div class="left-button">
        <el-select-v2
          v-if="queryParam.type !== 'INTERSECTION'"
          v-model="queryParam.locationId"
          clearable
          :options="parkingList"
          @change="refresh"
        />
        <!--        <el-select v-model="queryParam.locationId" clearable filterable @change="refresh" popper-class="locationId">-->
        <!--          <el-option v-for="item in parkingList" :key="item.id" :label="item.name" :value="item.id"></el-option>-->
        <!--        </el-select>-->
        <el-date-picker
          v-model="measureTime"
          type="datetimerange"
          range-separator="To"
          :start-placeholder="$t('开始日期')"
          :end-placeholder="$t('结束日期')"
          value-format="YYYY-MM-DD hh:mm:ss"
          @change="refresh"
          clearable
          @clear="measureTime = undefined"
        />
      </div>

      <div class="ltw-tool-container button-group">
        <el-button
          :id="item.buttonIconCode"
          :type="item.buttonStyleType"
          :key="item.id"
          v-for="item in outlineFunctionList"
          @click="executeButtonMethod(item)"
        >
          <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
          {{ $t(item.name) }}
        </el-button>
        <el-dropdown
          @command="handleCommand"
          class="batch-operate-btn"
          v-if="batchingFunctionList && batchingFunctionList.length > 0"
        >
          <el-button id="batchOperateBtn" type="primary">
            {{ $t('批量操作') }}
            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :key="item.id"
                v-for="item in batchingFunctionList"
                :command="item.buttonCode"
                :id="item.buttonCode"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                {{ $t(item.name) }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <el-table :data="pageData.records" stripe @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
      <el-table-column
        header-align="left"
        align="left"
        type="selection"
        width="55"
        reserve-selection
        fixed="left"
      ></el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        fixed="left"
        prop="sourceType"
        :label="$t('来源')"
        :width="130"
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-tag type="success">
            <ltw-icon :icon-code="'svg-' + scope.row.sourceType"></ltw-icon>
            {{ scope.row.sourceTypeName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="taskCode"
        :label="$t('编号')"
        :width="170"
        show-overflow-tooltip
        fixed="left"
      >
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="acquisitionRecordView(scope.row)"
            >{{ scope.row.code }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="executingDate"
        :label="$t('任务执行日期')"
        width="120"
        fixed="left"
      >
        <template #default="scope">
          <el-tag type="success">{{ scope.row.executingDate }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="locationName"
        :label="$t('地点')"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column header-align="left" align="left" prop="locationType" :label="$t('地点类型')" width="110">
        <template #default="scope">
          {{ getDicName(scope.row.locationType, locationTypeList) }}
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" prop="acquisitionType" :label="$t('采集类型')" width="110">
      </el-table-column>
      <!--      <el-table-column header-align="left" align="left" prop="dataSize" :label="$t('大小')" width="60">-->
      <!--        <template #default="scope">-->
      <!--          {{ checkFileSize(scope.row.dataSize) }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column header-align="left" align="left" prop="dataAmount" :label="$t('数据量')" width="74">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column header-align="left" align="left" prop="tagRecordCount" :label="$t('标签数量')" width="90">-->
      <!--      </el-table-column>-->
      <el-table-column header-align="left" align="left" prop="extractAmount" :label="$t('采集时间')" width="320">
        <template #default="scope">
          <template v-if="scope.row.startTime">
            <el-tag>{{ scope.row.startTime }}</el-tag>
            <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
            <el-tag v-if="scope.row.endTime">{{ scope.row.endTime }}</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" prop="duration" :label="$t('采集时长')" width="104">
        <template #default="scope">
          <el-tooltip
            effect="dark"
            :content="formatSecToDate(scope.row.duration || 0).time"
            placement="top"
            :enterable="false"
          >
            <el-tag v-if="formatSecToDate(scope.row.duration || 0).time"
              >{{ formatSecToDate(scope.row.duration || 0).time }}
            </el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" prop="duration" :label="$t('描述')" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.remark }}
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" prop="editable" :label="$t('是否有数据绑定采集记录')">
        <template #default="scope">
          <span v-if="scope.row.editable">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>
      <el-table-column header-align="left" align="left" :label="$t('动态场景属性')" min-width="130">
        <template #default="scope">
          <el-button type="primary" size="small" @click="showTaskExtension(scope.row.items)" v-if="scope.row.items">
            <ltw-icon icon-code="el-icon-InfoFilled"></ltw-icon>
            Parking Kpi
          </el-button>
        </template>
      </el-table-column>

      <el-table-column
        header-align="left"
        align="left"
        :label="$t('操作')"
        min-width="160"
        fixed="right"
        v-if="inlineFunctionList?.length"
      >
        <template #default="scope">
          <el-button-group>
            <el-tooltip
              :key="item.id"
              v-for="item in inlineFunctionList"
              effect="dark"
              :content="$t(item.name)"
              placement="top"
              :enterable="false"
            >
              <el-button
                :id="item.buttonIconCode"
                :type="item.buttonStyleType"
                @click="executeButtonMethod(item, scope.row)"
                :disabled="item.buttonCode === 'acquisitionRecordEdit' && !scope.row.editable"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.current"
      :page-sizes="[10, 15, 20, 30]"
      :page-size="queryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageData.total"
    >
    </el-pagination>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">{{ $t('取消') }}</el-button>
      </span>
    </template>
    <add-collect-record-dialog ref="AddCollectRecordDialog" @reload="query"></add-collect-record-dialog>
    <BatchAddOrderCollectRecordDialog
      ref="BatchAddOrderCollectRecordDialog"
      @reload="query"
    ></BatchAddOrderCollectRecordDialog>
  </el-dialog>
  <el-dialog title="查看动态参数" v-model="taskExtensionShow" width="600px" append-to-body :draggable="true">
    <Codemirror v-if="taskExtensionShow" v-model:value="taskExtension" border :options="cmOptions"></Codemirror>
  </el-dialog>
</template>
<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast, checkFileSize, dateUtils } from '@/plugins/util'
import { listMdParkingLotsSimple } from '@/apis/fleet/parking-lot-management'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import {
  pageDAQTaskRecords,
  deleteDAQTaskRecords,
  saveStaTaskRecordData
} from '@/apis/data-collect/acquisition-task-record'
import AddCollectRecordDialog from '@/pages/dataCollect/dialog/AddOrderCollectRecordDialog.vue'
import BatchAddOrderCollectRecordDialog from '@/pages/dataCollect/dialog/BatchAddOrderCollectRecordDialog.vue'

import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'

export default {
  emits: ['reload'],
  data() {
    return {
      taskExtension: [],
      taskExtensionShow: false,
      cmOptions: {
        mode: 'application/json', // Language mode text/yaml、text/javascript
        theme: 'dracula', // Theme
        indentUnit: 4, // 缩进多少个空格
        tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否默认换行
        // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
        // line: true,
        smartIndent: true // 智能缩进
      },
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      checkFileSize: checkFileSize,
      selectedData: [],
      measureTime: [],
      unitList: [
        {
          code: 'b',
          name: 'B'
        },
        {
          code: 'kb',
          name: 'KB'
        },
        {
          code: 'm',
          name: 'M'
        },
        {
          code: 'g',
          name: 'G'
        },
        {
          code: 't',
          name: 'T'
        }
      ],
      taskDetail: '',
      formatSecToDate: dateUtils.formatSecToDate,
      locationTypeList: [],
      parkingList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    AddCollectRecordDialog,
    BatchAddOrderCollectRecordDialog,
    Codemirror
  },

  methods: {
    showTaskExtension(items) {
      this.taskExtensionShow = true
      this.taskExtension = JSON.stringify(Array.isArray(items) ? items : [], null, '\t')
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      if (row.data.type === 'INTERSECTION') {
        this.queryParam.locationId = row.data.locationId
        this.queryParam.type = row.data.type
        this.dialogTitle = row.data.locationName + ' ' + this.$t('采集记录')
      } else {
        this.taskDetail = row.data
        this.queryParam.taskCode = row.data.taskCode
        this.dialogTitle = this.queryParam.taskCode + ' ' + this.$t('采集记录')
      }
      switch (row.type) {
        case 'edit':
          this.query()
          this.initButton(row.data.btnPermission)
          break
        case 'view':
          this.query()
          break
      }
      this.getParkingType()
      this.getParkingList()
    },
    initButton(btnPermission) {
      this.batchingFunctionList = btnPermission.batchingFunctionList.filter(
        val => val.buttonCode === 'acquisitionRecordBatchRemove' || val.buttonCode === 'acquisitionRecordBatchAdd'
      )
      this.inlineFunctionList = btnPermission.inlineFunctionList.filter(val => {
        return (
          val.buttonCode === 'dataStatistic' ||
          val.buttonCode === 'acquisitionRecordEdit' ||
          val.buttonCode === 'acquisitionRecordSingleRemove'
        )
      })
      this.outlineFunctionList = btnPermission.outlineFunctionList.filter(
        val => val.buttonCode === 'acquisitionRecordAdd'
      )
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    acquisitionRecordAdd() {
      this.$refs.AddCollectRecordDialog.show({
        type: 'add',
        data: this.taskDetail
      })
    },
    acquisitionRecordEdit(row) {
      this.$refs.AddCollectRecordDialog.show({
        type: 'edit',
        data: {
          id: row.id
        }
      })
    },
    acquisitionRecordView(row) {
      this.$refs.AddCollectRecordDialog.show({
        type: 'view',
        data: {
          id: row.id
        }
      })
    },

    handleCommand(command) {
      if (command === 'acquisitionRecordBatchAdd') {
        this.acquisitionRecordBatchAdd()
      } else {
        if (this.selectedData.length === 0) {
          return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
        }
        if (command === 'batchRemove') {
          this.batchRemove()
        }
      }
    },
    getParkingType() {
      if (!this.locationTypeList?.length) {
        listSysDictionary({
          typeCode: 'location_type'
        }).then(res => {
          this.locationTypeList = res.data
        })
      }
    },
    getDicName(code, dicList) {
      for (let i = 0, iLen = dicList?.length; i < iLen; i++) {
        if (dicList[i].code === code) {
          return dicList[i].name
        }
      }
    },
    acquisitionRecordSingleRemove(row) {
      this.remove({ id: row.id })
    },
    acquisitionRecordBatchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    acquisitionRecordBatchAdd() {
      this.$refs.BatchAddOrderCollectRecordDialog.show({
        type: 'add',
        data: this.taskDetail
      })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDAQTaskRecords(param).then(() => {
          this.$refs.tableRef.clearSelection()
          this.query()
        })
      })
    },
    dataStatistic(row) {
      saveStaTaskRecordData(row.id).then(res => {
        this.query()
        return showToast('车辆该日数据统计成功')
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {},
    query() {
      pageDAQTaskRecords(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    refresh() {
      ;[this.queryParam.startTime, this.queryParam.endTime] = this.measureTime?.length
        ? [this.measureTime[0], this.measureTime[1]]
        : [undefined, undefined]
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    getParkingList() {
      if (!(this.parkingList && this.parkingList?.length)) {
        listMdParkingLotsSimple().then(res => {
          res.data.forEach(val => {
            val.label = val.name
            val.value = val.id
          })
          this.parkingList = res.data
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.ltw-toolbar {
  display: flex;

  .left-button {
    display: flex;
    align-items: center;

    & > :deep(.search-input) {
      width: 200px;
      margin-right: 5px;
    }

    :deep(.el-date-editor) {
      width: 300px;
      margin: 0 10px;
    }
  }
}
</style>
<style lang="scss">
.collect-record-dialog {
  min-width: 800px;
}
</style>
