import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVariantVersionMappingModality = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys', data, params})
export const updateFtmVariantVersionMappingModality = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys', data, params})
export const deleteFtmVariantVersionMappingModality = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys', params})
export const listFtmVariantVersionMappingModality = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys', params})
export const listFtmVariantVersionMappingModalitySelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys/selections', params})
export const pageFtmVariantVersionMappingModality = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys/page', params})
export const getFtmVariantVersionMappingModality = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_variant_version_mapping_modalitys/' + id})
