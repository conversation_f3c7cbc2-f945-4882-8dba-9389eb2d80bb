<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('有效日期')" prop="activationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.activationDate"
              :disabled-date="disabledStartDate"
              type="date"
              :placeholder="$t('有效日期')"
              value-format="YYYY-MM-DD"
            />
            <el-tag v-else><span v-text="form.activationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('失效日期')" prop="deactivationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.deactivationDate"
              :disabled-date="disabledEndDate"
              type="date"
              :placeholder="$t('失效日期')"
              value-format="YYYY-MM-DD"
            />
            <el-tag v-else><span v-text="form.deactivationDate || '-'"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('标定日期')" prop="calibrationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.calibrationDate"
              type="date"
              :placeholder="$t('标定日期')"
              value-format="YYYY-MM-DD"
            />
            <el-tag v-else><span v-text="form.calibrationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('标定工具版本')" prop="toolVersion">
            <el-input-number
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.toolVersion"
              :precision="1.0"
              :step="0.1"
            />
            <el-tag v-else><span v-text="form.toolVersion"></span></el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="12">
        <el-form-item
          :label="$t('是否有效')"
          id="enable"
          prop="enable"
          v-if="dialogStatus === 'edit' || dialogStatus === 'view'"
        >
          <el-switch
            v-if="dialogStatus === 'edit' || dialogStatus === 'view'"
            v-model="form.enable"
            :disabled="formReadonly"
            inline-prompt
            :active-text="$t('是')"
            :inactive-text="$t('否')"
            id="enable"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
          <el-tag v-else><span v-text="form.enable"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-form-item :label="$t('文件')" :prop="'fileId'" v-if="dialogStatus === 'add' || dialogStatus === 'edit'">
        <upload-file
          :disabled="dialogStatus !== 'add'"
          ref="uploadImage"
          source-type="dbc_file"
          :limit="1"
          listType="text"
          :source-id="form.id"
          v-model="form.fileId"
        />
      </el-form-item>
    </el-form>
    <div class="sensor-files" v-if="dialogStatus === 'view'">
      <el-tabs v-model="activeName">
        <el-tab-pane label="JSON" name="JsonFile">
          <div class="json-content">
            <el-button v-if="jsonData?.length" type="primary" plain @click="downloadJson()" class="download-btn">
              <ltw-icon icon-code="svg-download"></ltw-icon>
              {{ $t('下载') }}
            </el-button>
            <div class="jsonTxt">
              <el-scrollbar height="500px">
                <json-editor-vue
                  border
                  v-if="jsonData?.length"
                  style="margin-top: 10px"
                  :currentMode="jsonMode"
                  :language="jsonLang"
                  v-model="jsonData"
                  :modeList="modeList"
                  @scroll="onScroll"
                ></json-editor-vue>
              </el-scrollbar>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="传感器文件" name="sensorFile">
          <div class="sensor-content">
            <el-button v-if="sensorList?.length" type="primary" plain @click="downloadFiles()" class="download-btn">
              <ltw-icon icon-code="svg-download"></ltw-icon>
              {{ $t('下载') }}
            </el-button>
            <el-tabs :style="{ left: elTabHeaderWidth + 'px' }" v-model="fileType" class="file-type-tabs">
              <el-tab-pane label="JSON" name="JSON"></el-tab-pane>
              <el-tab-pane label="YAML" name="YAML"></el-tab-pane>
            </el-tabs>
            <el-tabs tab-position="left" v-model="sensorModel" class="sensor-list-tabs">
              <template v-for="(item, index) in sensorList" :key="index">
                <el-tab-pane lazy :label="item.modalityCode" :name="item.modalityCode">
                  <div class="border-card-content" v-if="sensorModel === item.modalityCode">
                    <div class="text">
                      <json-editor-vue
                        v-if="fileType === 'JSON'"
                        style="margin-top: 10px"
                        :currentMode="jsonMode"
                        :language="jsonLang"
                        v-model="item[fileType === 'JSON' ? 'jsonContent' : 'content']"
                        :modeList="modeList"
                      ></json-editor-vue>
                      <el-scrollbar height="800px">
                        <div
                          v-for="(value, key) in item[fileType === 'JSON' ? 'jsonContent' : 'content']"
                          :key="key"
                        >
                          <div class="divider-style">
                            <el-divider>
                              <el-icon>
                                <star-filled />
                              </el-icon>
                              {{ key }}
                            </el-divider>
                          </div>
                          <Codemirror
                            v-if="fileType === 'YAML'"
                            v-model:value="item.content[key]"
                            border
                            :options="cmOptions"
                          ></Codemirror>
                          <!--                          <div-->
                          <!--                            v-html="trans(value)"-->
                          <!--                            style="margin-left: 20px"-->
                          <!--                          ></div>-->
                        </div>
                      </el-scrollbar>
                    </div>
                  </div>
                </el-tab-pane>
              </template>
            </el-tabs>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="cancel">{{ $t('取消') }}</el-button>
      <el-button v-if="dialogStatus === 'add' || dialogStatus === 'edit'" id="submit" type="primary" @click="submit"
        >{{ $t('保存') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { showToast, downloadTxt } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { i18n } from '@/plugins/lang'
import UploadFile from '@/components/system/UploadFile.vue'
import util, { getLocale } from '@/plugins/util'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElSelect
} from 'element-plus'
import {
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  getFtmCalibrationRecords,
  getFtmCalibrationResults
} from '@/apis/fleet/ftm-calibration-records'
import Codemirror from 'codemirror-editor-vue3'

const defaultFormData = { enable: true }
export default {
  name: 'AddDbcConfig',
  emits: ['reload', 'cancel'],
  data() {
    return {
      activeName: 'JsonFile',
      objectValue: {},
      contentValue: '',
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      $t: i18n.global.t,
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      form: Object.assign({}, defaultFormData),
      formRules: {},
      fileType: 'JSON',
      sensorModel: '',
      sensorList: [],
      jsonData: [],
      jsonLang: getLocale() === 'en' ? 'en' : 'zh-CN',
      jsonMode: 'view',
      modeList: ['view'],
      elTabHeaderWidth: 0,
      cmOptions: {
        // mode: "text/javascript", // Language mode
        // readOnly: "nocursor",
        mode: 'application/json', // Language mode text/yaml、text/javascript
        theme: 'dracula', // Theme
        // readOnly: 'nocursor'
        indentUnit: 4, // 缩进多少个空格
        tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否默认换行
        // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
        // line: true,
        smartIndent: true // 智能缩进
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    Codemirror,
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElSelect,
    UploadFile
  },
  created() {},
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      let formRules = {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        calibrationDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        toolVersion: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        fileId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
      switch (row.type) {
        case 'add':
          this.formRules = formRules
          // this.form.vehicleId = row.vehicleId
          this.form.vin = row.vin
          this.dialogTitle = i18n.global.t('新增') + ' ' + i18n.global.t('dbc')
          break
        case 'edit':
          this.formRules = formRules
          this.dialogTitle = i18n.global.t('编辑') + ' ' + i18n.global.t('dbc')
          this.form = JSON.parse(JSON.stringify(row.data))
          break
        case 'view':
          this.formRules = []
          this.dialogTitle = i18n.global.t('dbc') + ' ' + i18n.global.t('详情')
          this.form = JSON.parse(JSON.stringify(row.data))
          this.removeArr()
          this.getJsonData()
          this.getFtmCalibrationResults()
          break
      }
    },
    getFtmCalibrationResults() {
      getFtmCalibrationResults({ recordId: this.form.id }).then(res => {
        res.data.forEach(val => {
          val.jsonContent = JSON.parse(val.jsonContent || '{}')
          val.content = JSON.parse(val.content || '{}')
        })
        this.sensorList = res.data
        if (res.data?.length) {
          this.sensorModel = res.data[0].modalityCode
        }
        setTimeout(() => {
          let elTabHeaderDom = document.querySelector('.sensor-list-tabs .el-tabs__header.is-left')
          this.elTabHeaderWidth = elTabHeaderDom.clientWidth
        })
      })
    },
    getFtmCalibrationRecords(row) {
      getFtmCalibrationRecords(row.id).then(res => {
        // res.data.fileId = [res.data.fileId]
        this.form = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    submitCancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    dialogOpened() {},
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          fileId: this.form.fileId?.length && this.form.fileId[0]
        }
        if (this.dialogStatus === 'add') {
          saveFtmCalibrationRecords(postData).then(() => {
            // this.cancelCalibration()
            this.submitCancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmCalibrationRecords(postData).then(() => {
            // this.cancelCalibration()
            this.submitCancel()
          })
        }
      })
    },
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    },
    downloadJson() {
      showToast(this.$t('开始下载'), 'success')
      downloadTxt('vin_data.json', JSON.stringify(this.jsonData))
      this.jsonDialogVisible = false
    },
    downloadFiles() {
      let item = this.sensorList.find(val => val.modalityCode === this.sensorModel)
      let postData = {
        id: item.id,
        token: util.getToken()
      }
      if (this.fileType === 'JSON') {
        // downloadModalityJson(postData)
        window.open(
          GLB_CONFIG.devUrl.serviceSiteRootUrl +
            '/ftm/ftm_calibration_results/modality/json?' +
            Object.keys(postData)
              .map(key => `${key}=${postData[key]}`)
              .join('&')
        )
      } else {
        // downloadModalityYaml(postData)
        window.open(
          GLB_CONFIG.devUrl.serviceSiteRootUrl +
            '/ftm/ftm_calibration_results/modality/yaml?' +
            Object.keys(postData)
              .map(key => `${key}=${postData[key]}`)
              .join('&')
        )
      }
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return new Date(val) < new Date(this.form.activationDate).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.form.deactivationDate) {
        return new Date(val) > new Date(this.form.deactivationDate).getTime()
      }
    },
    trans(value) {
      // alert(value);
      return value.replace(/\n/g, '<br>')
    },
    sensorChange() {
      this.fileType = 'JSON'
    },
    getJsonData() {
      if (this.form.content !== undefined) {
        this.jsonData.push(JSON.parse(this.form.content))
      }
    },
    removeArr() {
      this.jsonData.splice(0, this.jsonData.length)
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.add-form {
  padding: 10px 0;

  .form-footer {
    text-align: right;
  }
}

.sensor-files {
  .json-content {
    position: relative;
    margin-top: 36px;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
    }
  }

  .sensor-content {
    position: relative;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    .file-type-tabs {
      // position: absolute;
      // top: -10px;
      position: absolute;
      top: -40px;

      :deep(.el-tabs__header) {
        margin: 0;
      }
    }

    .sensor-list-tabs {
      display: flex;
      margin-top: 50px;
      // overflow: visible;
      :deep(.el-tabs__header) {
        margin-right: 0;
        border-right: none;
      }

      :deep(.el-tabs__content) {
        // overflow: visible;
        flex-grow: 1;
        padding: 0;
        .divider-style{
          padding: 0 10px;
        }

        .el-tab-pane,
        .el-tab-pane > .border-card-content,
        .el-tab-pane > .border-card-content > .text,
        .el-tab-pane > .border-card-content > .text > .container {
          height: 100%;
        }

        .el-tab-pane > .border-card-content > .text {
          width: 100%;
        }

        .el-tab-pane > .border-card-content > .text > .container .jsoneditor {
          border: thin solid #dcdfe6;
        }

        .el-tab-pane > .border-card-content > .text > .container {
          margin: 0 !important;

          .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
            height: 100%;
            margin: 0;
            padding: 0;
          }

          .jsoneditor-menu,
          .jsoneditor-navigation-bar {
            display: none;
          }
        }
      }

      .border-card-content {
        position: relative;

        :deep(.jsoneditor-poweredBy) {
          display: none;
        }
      }
    }
  }
}
</style>
