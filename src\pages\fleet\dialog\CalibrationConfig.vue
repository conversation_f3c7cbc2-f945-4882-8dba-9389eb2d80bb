<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    class="calibration-dialog"
    destroy-on-close
  >
    <el-button
      class="add-calibration"
      type="primary"
      @click="addCalibrationParams"
    >
      {{ $t('添加') }}
    </el-button>
    <el-timeline>
      <template v-for="(item, index) in list" :key="index">
        <el-timeline-item
          :center="false"
          placement="top"
          :timestamp="item.activationDate"
        >
          <template v-slot:dot v-if="item.sourceType">
            <ltw-icon v-if="item.sourceType==='VEHICLE'" icon-code="svg-car-cloud-upload"></ltw-icon>
            <ltw-icon v-if="item.sourceType==='CLOUD'" icon-code="svg-cloud-upload"></ltw-icon>
          </template>
          <el-card
            @mouseup.prevent="cancelScroll($event, item, index)"
            @mousedown="scrollDown($event, item, index)"
          >
            <calibration-form
              @reload="reload"
              :index="index"
              :item="item"
              :ref="'CalibrationForm' + index"
            ></calibration-form>
          </el-card>
        </el-timeline-item>
      </template>
    </el-timeline>
<!--    <el-slider-->
<!--      v-if="sliderVisible"-->
<!--      class="custom-slider"-->
<!--      vertical-->
<!--      :min="sliderMin"-->
<!--      :max="sliderMax"-->
<!--      :height="sliderHeight + 'px'"-->
<!--      v-model="sliderValue"-->
<!--      :marks="marks"-->
<!--      :style="{ top: offsetTop + 'px' }"-->
<!--    />-->
    <template #footer>
      <el-button @click="cancel">{{ $t('关闭') }}</el-button>
    </template>
    <AddCalibrationConfig
      ref="AddCalibrationConfig"
      @reload="listFtmCalibrationRecords"
      @cancel="cancelData"
    ></AddCalibrationConfig>
  </el-dialog>
</template>

<script>
import CalibrationForm from '@/pages/fleet/components/CalibrationForm'
import { i18n } from '@/plugins/lang'
import AddCalibrationConfig from '@/pages/fleet/dialog/AddCalibrationConfig'
import { dateUtils, showConfirmToast, showToast } from '@/plugins/util.js'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import Drag from '@/directives/drag-timeline'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElTimeline,
  ElTimelineItem,
  ElSlider,
  ElSelect
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  listFtmCalibrationRecords,
  deleteFtmCalibrationRecords,
  updateFtmCalibrationRecordsBatch
} from '@/apis/fleet/ftm-calibration-records'
const defaultFormData = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      $t: i18n.global.t,
      //参数标定
      calibrationVisible: false,
      pageData: {
        records: [],
        total: 0
      },
      calibrationParamsQueryParam: {
        current: 1,
        size: 10
      },
      formVisible: false,
      form: Object.assign({}, defaultFormData),
      calibrationParamsEditFormVisible: false,
      expandKeys: [],

      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      currentRow: '',
      currentRowIndex: '',
      options: [],
      typeList: [],

      //为了上传图片的参数
      variant: '',
      list: [],
      sliderValue: 0,
      marks: {
        // 0: '0°C',
        // 8: '8°C',
        // 37: '37°C',
        // 50: {
        //   style: {
        //     color: '#1989FA'
        //   },
        //   label: '50%'
        // }
      },
      sliderVisible: false,
      sliderMin: 0,
      sliderMax: 100,
      sliderHeight: 300,
      offsetTop: 0,
      vehicleId: '',
      vin: '',
      draggable: false,
      edit: false,
      activeItem: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  directives: {
    Drag
  },
  components: {
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElTimeline,
    ElTimelineItem,
    ElSlider,
    ElSelect,
    LtwInput,
    DictionarySelection,
    EmployeeSelection,
    LtwIcon,
    AddCalibrationConfig,
    CalibrationForm
  },
  created() {
    // let list = [
    //   {
    //     calibrationDate: '2023-4-12',
    //     date: '2023-4-12',
    //     disabledDate: '2023-4-15'
    //   },
    //   {
    //     calibrationDate: '2023-3-12',
    //     date: '2023-3-12',
    //     disabledDate: '2023-3-15'
    //   },
    //   {
    //     calibrationDate: '2023-2-12',
    //     date: '2023-2-12',
    //     disabledDate: '2023/2/15'
    //   },
    //   {
    //     calibrationDate: '2023-1-12',
    //     date: '2023-1-12',
    //     disabledDate: '2023/1/15'
    //   }
    // ]
    // this.list = list.map(val => {
    //   val.draggable = false
    //   return val
    // })
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      // this.dialogStatus = row.type
      this.dialogTitle = i18n.global.t('标定记录')
      // this.vehicleId = row.id
      this.vin = row.vin
      // this.variant = row.variant
      this.listFtmCalibrationRecords()
      // this.listFtmCalibrationVariantMappingModalityVO({ variant: row.variant })
      // this.listSysDictionary('calibration_parameter_type')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      // this.$emit('reload')
    },
    dialogOpened() {},
    initForm() {},
    cancelCalibration() {
      this.formVisible = false
    },

    getDiffDay(date_1, date_2) {
      // 计算两个日期之间的差值
      let totalDays, diffDate
      let myDate_1 = new Date(date_1)
      let myDate_2 = new Date(date_2)
      // 将两个日期都转换为毫秒格式，然后做差
      diffDate = myDate_1 - myDate_2 // 取相差毫秒数的绝对值

      totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整

      return totalDays // 相差的天数
    },
    scrollDown(event, item, index) {
      this.activeItem = { index, item: JSON.parse(JSON.stringify(item)) }
      if (this.edit) {
        return
      }
      this.marks = {}
      this.sliderMin = 0
      this.sliderValue = this.sliderValue || item.sliderValue
      if (item.disabledDays) {
        this.marks[item.sliderValue + item.disabledDays] =
          '失效日期:' + this.list[index].deactivationDate
      }
      if (index === 0) {
        // this.sliderValue = _this.getDiffDay(
        //   arr[index].date,
        //   arr[index + 1].date
        // )
        // 总天数,按大的日期来算
        this.sliderMax =
          item.dragDays + (item.disabledDays > 0 ? item.disabledDays : 0)
      } else if (index === this.list?.length - 1) {
        this.sliderMax = item.dragDays
        // this.sliderMin -= 5
      } else {
        // this.sliderValue = _this.getDiffDay(
        //   arr[index].date,
        //   arr[index + 1].date
        // )
        // 最大值
        this.sliderMax = item.dragDays
      }
    },
    cancelScroll(event, item, index) {
      // if (item.edit) {
      //   return
      // }
      // this.sliderValue = 0
      // item.draggable = false
      this.sliderVisible = false
      if (this.draggable) {
        this.draggable = false
        // let postData = {}
        // this.updateFtmCalibrationRecordsBatch(postData)

        this.$refs.AddCalibrationConfig.show({
          type: 'edit',
          data: item
          // id: item.id
        })
      }
      // if (!this.sliderVisible) {
      //   item.draggable = false
      // }
    },
    cancelDirective(event, item, index) {
      // if (item.edit) {
      //   return
      // }
      //按住滑动到其他时间线区域，不进行注销拖动指令
      if (!this.sliderVisible) {
        item.draggable = false
      }
    },
    registerDirective(event, item, index) {
      if (this.edit) {
        return
      }
      //按住滑动到其他时间线区域，不进行注册拖动指令
      if (!this.sliderVisible) {
        // 非拖拽时间
        if (!item.draggable) {
          // 当前dragger是false时
          this.list = this.formatCalibrationList(
            JSON.parse(JSON.stringify(this.list))
          )
          this.list[index].draggable = true
          // item.draggable = true
          // this.sliderValue = 0
        }
      }
    },
    dragFn(moveDays, dragLength, offsetTop) {
      // 判断拖动
      if (moveDays) {
        if (!this.draggable) {
          this.draggable = true
        }
        if (!this.sliderVisible) {
          this.sliderVisible = true
        }
      }
      if (moveDays === '') {
        this.offsetTop = offsetTop
        this.sliderHeight = dragLength
      } else {
        let index = this.list.findIndex(val => val.draggable)
        // if (moveDays) {
        if (this.list?.length <= 1) {
          this.sliderValue = this.list[index].sliderValue - moveDays
          this.list[index].activationDate = dateUtils.parseTime(
            new Date(this.list[index].defaultDate) -
              moveDays * 24 * 60 * 60 * 1000,
            '{y}-{m}-{d}'
          )
          // 更新自己deactivationDate
          if (
            new Date(this.list[index].deactivationDate) <
            new Date(this.list[index].activationDate)
          ) {
            this.list[index].deactivationDate = this.list[index].activationDate
          }
        } else {
          if (index === 0) {
            if (this.list[index].sliderValue - moveDays > 0) {
              this.sliderValue = this.list[index].sliderValue - moveDays
              this.list[index].activationDate = dateUtils.parseTime(
                new Date(this.list[index].defaultDate) -
                  moveDays * 24 * 60 * 60 * 1000,
                '{y}-{m}-{d}'
              )
              // 更新自己deactivationDate
              if (
                new Date(this.list[index].deactivationDate) <
                new Date(this.list[index].activationDate)
              ) {
                this.list[index].deactivationDate =
                  this.list[index].activationDate
              }
              // 更新下一个deactivationDate
              if (
                new Date(this.list[index].activationDate) <
                new Date(this.list[index + 1].deactivationDate)
              ) {
                this.list[index + 1].deactivationDate =
                  this.list[index].activationDate
              }
            }
          } else if (index === this.list?.length - 1) {
            if (
              this.list[index].sliderValue - moveDays <
              this.list[index].dragDays
            ) {
              this.sliderValue = this.list[index].sliderValue - moveDays
              this.list[index].activationDate = dateUtils.parseTime(
                new Date(this.list[index].defaultDate) -
                  moveDays * 24 * 60 * 60 * 1000,
                '{y}-{m}-{d}'
              )
              // 更新自己deactivationDate
              if (
                new Date(this.list[index].deactivationDate) <
                new Date(this.list[index].activationDate)
              ) {
                this.list[index].deactivationDate =
                  this.list[index].activationDate
              }
            }
          } else {
            if (
              this.list[index].sliderValue - moveDays > 0 &&
              this.list[index].sliderValue - moveDays <
                this.list[index].dragDays
            ) {
              this.sliderValue = this.list[index].sliderValue - moveDays
              this.list[index].activationDate = dateUtils.parseTime(
                new Date(this.list[index].defaultDate) -
                  moveDays * 24 * 60 * 60 * 1000,
                '{y}-{m}-{d}'
              )
              // 更新自己deactivationDate
              if (
                new Date(this.list[index].deactivationDate) <
                new Date(this.list[index].activationDate)
              ) {
                this.list[index].deactivationDate =
                  this.list[index].activationDate
              }
              // 更新下一个deactivationDate
              if (
                new Date(this.list[index].activationDate) <
                new Date(this.list[index + 1].deactivationDate)
              ) {
                this.list[index + 1].deactivationDate =
                  this.list[index].activationDate
              }
            }
          }
        }
        // }
        // debounce(()=>{

        // })
      }
    },
    updateFtmCalibrationRecordsBatch(postData) {
      this.list.forEach(val => {
        val.fileId = val.fileId?.length && val.fileId[0]
      })
      updateFtmCalibrationRecordsBatch(this.list).then(res => {
        showToast('日期修改已生效')
        // this.listFtmCalibrationRecords()
      })
    },

    // //参数标定
    listFtmCalibrationRecords() {
      this.list = []
      listFtmCalibrationRecords({
        vin: this.vin,
        enable: true
      }).then(res => {
        this.list = res.data.map(val => {
          val.draggable = false
          return val
        })
        this.edit = false
      })
    },
    formatCalibrationList(list) {
      let _this = this
      list.forEach((val, index, arr) => {
        val.draggable = false
        val.defaultDate = val.activationDate
        // 额外的失效天数
        // if (
        //   Date.parse(arr[index].deactivationDate) &&
        //   Date.parse(arr[index].deactivationDate) >
        //     Date.parse(arr[index].activationDate)
        // ) {
        let isDisabledDays =
          Date.parse(arr[index].deactivationDate) &&
          Date.parse(arr[index].deactivationDate) >
            Date.parse(arr[index].activationDate)
        if (isDisabledDays) {
          val.disabledDays = _this.getDiffDay(
            arr[index].deactivationDate,
            arr[index].activationDate
          )
        }
        //  else {
        //   arr[index].deactivationDate = ''
        // }

        // if (Date.parse(arr[index].deactivationDate)) {
        //   if (
        //     Date.parse(arr[index].deactivationDate) >
        //     Date.parse(arr[index].activationDate)
        //   ) {
        //     val.disabledDays = _this.getDiffDay(
        //       arr[index].deactivationDate,
        //       arr[index].activationDate
        //     )
        //   } else {
        //     arr[index].deactivationDate = arr[index].activationDate
        //     val.disabledDays = 0
        //   }
        // }
        if (list?.length === 1) {
          val.dragDays = 50
          // 当前天数
          val.sliderValue = 0
        } else {
          if (index === 0) {
            // 一个item的拖拽天数
            val.dragDays =
              _this.getDiffDay(
                arr[index].activationDate,
                arr[index + 1].activationDate
              ) > 50
                ? _this.getDiffDay(
                    arr[index].activationDate,
                    arr[index + 1].activationDate
                  )
                : 50
            // 当前天数
            val.sliderValue = _this.getDiffDay(
              arr[index].activationDate,
              arr[index + 1].activationDate
            )
            // val.dragDays = _this.getDiffDay(arr[index].date, arr[index + 1].date)
          } else if (index === list?.length - 1) {
            // 一个item的拖拽天数
            val.dragDays =
              _this.getDiffDay(
                arr[index - 1].activationDate,
                arr[index].activationDate
              ) > 50
                ? _this.getDiffDay(
                    arr[index - 1].activationDate,
                    arr[index].activationDate
                  )
                : 50
            val.sliderValue =
              50 -
              _this.getDiffDay(
                arr[index - 1].activationDate,
                arr[index].activationDate
              )
          } else {
            // 当前天数
            val.sliderValue = _this.getDiffDay(
              arr[index].activationDate,
              arr[index + 1].activationDate
            )
            // 一个item的拖拽天数
            val.dragDays = _this.getDiffDay(
              arr[index - 1].activationDate,
              arr[index + 1].activationDate
            )
          }
        }
      })
      return list
    },
    addCalibrationParams() {
      // this.list = [{ edit: true, draggable: false, vin: this.vin }]
      if (~this.list.findIndex(val => val.edit === true)) {
        return showToast('请先保存标定参数记录', 'warning')
      } else {
        // if (this.list?.length) {
        // this.list.unshift({ abc: 123, vin: this.vin })
        this.list.unshift({ edit: true, draggable: false, vin: this.vin, dialogStatus: 'add' })
        // this.list = this.formatCalibrationList([
        //   { edit: true, draggable: false, vin: this.vin }
        // ])

        // } else {
        //   this.list.splice(0, 1, {
        //     edit: false,
        //     draggable: false,
        //     vin: this.vin
        //   })
        //   setTimeout(() => {
        //     this.list[0].edit = true
        //   }, 1000)
        // let list = [{ edit: true, draggable: false, vin: this.vin }]
        // this.list = this.formatCalibrationList(list)
        // this.cancelDirective({}, list[0])
        // }
        // 编辑状态禁止拖拽
        this.edit = true
      }

      // if (this.list?.length) {
      //   this.list.unshift({ edit: true, draggable: false, vin: this.vin })
      // } else {
      //   this.list = [{ edit: true, draggable: false, vin: this.vin }]
      // }
      // // 编辑状态禁止拖拽
      // this.edit = true
      // this.$refs.AddCalibrationConfig.show({
      //   type: 'add',
      //   // vehicleId: this.vehicleId,
      //   vin: this.vin
      // })
    },
    editItem(item) {
      // this.form = item
      item.edit = true
      // this.$refs.AddCalibrationConfig.show({
      //   type: 'edit',
      //   id: item.id
      // })
    },
    reload(obj) {
      if (obj?.type === 'delete') {
        if (obj.form.id) {
          return this.listFtmCalibrationRecords()
        } else {
          this.list.splice(obj.index, 1)
        }
      } else if (obj?.type === 'add') {
        return this.listFtmCalibrationRecords()
      } else if (obj?.type === 'edit' || obj?.type === 'cancel') {
        this.list.splice(obj.index, 1, obj.form)
      } else if (obj?.type === 'view') {
        this.$refs.AddCalibrationConfig.show({
          type: 'view',
          data: obj.form
        })
        return
      }
      this.edit = obj.form.edit
      if (obj.form.edit) {
        this.cancelDirective({}, obj.form)
      } else {
        this.registerDirective({}, obj.form, obj.index)
      }
    },
    cancelData() {
      this.list.splice(this.activeItem.index, 1, this.activeItem.item)
    }
  }
}
</script>
<style lang="scss">
.calibration-dialog {
  .el-dialog__body {
    position: relative;
  }
}
</style>
<style scoped lang="scss">
.custom-slider {
  position: absolute;
  left: 46px;
  // top: 0;
  display: block;
  z-index: 2;
}
.add-calibration {
  margin-left: 68px;
}
.el-timeline-item {
  :deep(.el-timeline-item__wrapper) {
    width: 100%;
  }
  .form-item {
    line-height: 32px;
    color: #303133;
    // color: #606266;
  }
  .file-row {
    display: flex;
    .form-item {
      white-space: nowrap;
    }
  }
  .footer {
    position: absolute;
    bottom: 8px;
    right: 20px;
    text-align: right;
    .el-link {
      margin-left: 6px;
    }
  }
}
</style>
