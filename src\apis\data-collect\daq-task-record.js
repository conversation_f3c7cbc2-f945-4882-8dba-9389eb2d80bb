import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqTaskRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    data,
    params
  })
export const updateDaqTaskRecord = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    data,
    params
  })
export const deleteDaqTaskRecord = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    params
  })
export const listDaqTaskRecord = (params = {}, fullLoading) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records',
    params,
    fullLoading
  })
export const listDaqTaskRecordSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/selections',
    params
  })
export const listDmRequirementsPublishedSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_requirements/published',
    params
  })
export const getRequirementTags = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_requirement_tags',
    params,
    unloading
  })
export const pageDaqTaskRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/page',
    params
  })
export const getDaqTaskRecordDashboardVO = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/getDaqTaskRecordDashboardVO',
    params
  })
export const getDaqTaskRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_records/' + id })
export const listTaskFunction = () => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions' })
export const taskFunctionGroups = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/queryFunctionGroupTree',
    params
  })
