<template>
  <div class="fleets-management-new">
    <!-- 主内容区域 -->
    <div class="main-content-container" :class="{ 'drawer-open': showDetailDrawer }">
      <el-card shadow="never" class="main-card" :class="{ 'with-drawer': showDetailDrawer }">
        <!-- 筛选和操作栏 -->
        <div class="filter-action-bar">
          <div class="filter-section">
            <!-- 显示选项 -->
            <div class="display-options">
              <ltw-icon icon-code="el-icon-van"></ltw-icon>
              <span class="display-label">{{ $t('车型总数') }}</span>
              <span class="display-count">{{ filteredVehicles.length }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <div class="search-container">
              <ltw-input
                v-model="searchKeyword"
                :placeholder="$t('请输入车型编码/型号查找')"
                clearable
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #append>
                  <el-button @click="handleSearch">
                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                  </el-button>
                </template>
              </ltw-input>
            </div>
            <el-button type="primary" @click="addNewVehicle" class="add-button" size="small">
              <ltw-icon icon-code="el-icon-plus" class="button-icon"></ltw-icon>
              {{ $t('新增车型') }}
            </el-button>
          </div>
        </div>
        <div
          class="box-with-drawer"
          :class="{ 'with-drawer': showDetailDrawer }"
          :style="showDetailDrawer ? { marginRight: (drawerWidth + 20) + 'px' } : {}"
        >
          <div class="main-box">
            <!-- 品牌筛选标签 -->
            <div class="brand-filter-section">
              <div class="filter-tags">
                <div class="filter-tag" :class="{ active: selectedBrands.length === 0 }" @click="selectAllBrands">
                  {{ $t('全部') }}
                </div>
                <div
                  v-for="brand in brandList"
                  :key="brand.code"
                  class="filter-tag"
                  :class="{ active: selectedBrands.includes(brand.code) }"
                  @click="toggleBrand(brand.code)"
                >
                  {{ brand.name }}
                </div>
              </div>
            </div>

            <!-- 车辆卡片展示区域 -->
            <div class="vehicles-content">
              <div v-if="loading" class="loading-container">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else-if="filteredVehicles.length === 0" class="empty-container">
                <el-empty :description="$t('暂无数据')" />
              </div>
              <div v-else class="vehicles-by-brand" @click.self="clearSelection">
                <!-- 按品牌分组显示 -->
                <div v-for="brandGroup in vehiclesByBrand" :key="brandGroup.brandName" class="brand-group">
                  <!-- 品牌标签 -->
                  <div class="brand-label">
                    <span class="brand-name">{{ brandGroup.brandName }}</span>
                  </div>

                  <!-- 该品牌下的车辆卡片 - 使用flex布局 -->
                  <div class="vehicles-flex-container" :class="{ 'with-drawer': showDetailDrawer }">
                    <div
                      v-for="vehicle in brandGroup.vehicles"
                      :key="vehicle.id"
                      class="vehicle-flex-item"
                    >
                      <vehicle-card-new
                        :vehicle="vehicle"
                        :selected="selectedVehicleId === vehicle.id"
                        @detail="handleVehicleDetail"
                        @menu-click="handleVehicleMenu"
                        class="vehicle-card-item"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 抽屉容器 -->
          <div v-if="showDetailDrawer" class="drawer-container" :style="{ width: drawerWidth + 'px' }">
            <!-- 拖拽手柄 -->
            <div
              class="drawer-resize-handle"
              @mousedown="startDrag"
            ></div>
            <div class="drawer-header">
              <div class="drawer-title">
                <span>{{selectedVehicle.code}}</span>
              </div>
              <div class="drawer-close" @click="closeDetailDrawer">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M15 5L5 15M5 5L15 15" stroke="#666" stroke-width="1.5" stroke-linecap="round" />
                </svg>
              </div>
            </div>
            <div class="drawer-content">
              <vehicle-detail-drawer
                ref="vehicleDetailDrawer"
                v-if="showDetailDrawer"
                v-model:visible="showDetailDrawer"
                :vehicle-id="selectedVehicleId"
                @edit="handleEditVehicleSubmit"
                @delete="handleDeleteFromDrawer"
                @save="handleSaveInfo"
                :is-embedded="true"
              />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 新增车型对话框 -->
    <add-vehicle-type-dialog v-if="showAddDialog" v-model:visible="showAddDialog" @submit="handleAddVehicleSubmit" />
  </div>
</template>

<script>
import VehicleCardNew from './components/VehicleCardNew.vue'
import AddVehicleTypeDialog from './components/AddVehicleTypeDialog.vue'
import VehicleDetailDrawer from './components/VehicleDetailDrawer.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
export default {
  components: {
    VehicleCardNew,
    AddVehicleTypeDialog,
    VehicleDetailDrawer,
    LtwIcon,
    LtwInput
  },
  name: 'FleetsManagementNew',
  data() {
    return {
      loading: true,
      searchKeyword: '',
      selectedBrands: [], // 选中的品牌
      vehicleList: [], // 所有车辆数据
      showAddDialog: false, // 显示新增对话框
      showDetailDrawer: false, // 显示详情抽屉
      selectedVehicleId: null, // 选中的车辆ID
      selectedVehicle:null,
      brandList:[],
      // 抽屉拖拽相关
      drawerWidth: 740, // 抽屉宽度
      minDrawerWidth: 740, // 最小抽屉宽度
      isDragging: false, // 是否正在拖拽
      dragStartX: 0, // 拖拽开始时的X坐标
      dragStartWidth: 0 // 拖拽开始时的抽屉宽度
    }
  },
  computed: {
    // 车辆列表（现在筛选逻辑在后端处理）
    filteredVehicles() {
      return this.vehicleList
    },

    // 按品牌分组的车辆数据
    vehiclesByBrand() {
      if (!this.vehicleList || this.vehicleList.length === 0) {
        return []
      }

      // 按supplierName分组
      const brandGroups = {}
      this.vehicleList.forEach(vehicle => {
        const brandName = vehicle.supplierName
        // 只有当supplierName有值时才进行分组
        if (brandName) {
          if (!brandGroups[brandName]) {
            brandGroups[brandName] = {
              brandName,
              vehicles: []
            }
          }
          brandGroups[brandName].vehicles.push(vehicle)
        }
      })

      // 转换为数组并排序
      return Object.values(brandGroups).sort((a, b) => {
        return a.brandName.localeCompare(b.brandName)
      })
    }
  },

  created() {
    this.loadData()
  },

  beforeDestroy() {
    // 清理拖拽事件监听
    if (this.isDragging) {
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
      document.body.classList.remove('drawer-dragging')
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        await this.loadVehicles()
      } finally {
        this.loading = false
      }
    },
    // 加载车辆数据
    async loadVehicles() {
      try {
        const res = await listFtmVehicleVariant() // 加载所有数据
        this.vehicleList = res.data || []
        this.brandList  = this.getbrandList()
      } catch (error) {
        console.error('加载车辆数据失败:', error)
      }
    },
    getbrandList() {
      if (!this.vehicleList || this.vehicleList.length === 0) {
        return []
      }

      // 从车辆数据中提取唯一的品牌信息
      const brandMap = new Map()
      this.vehicleList.forEach(vehicle => {
        const supplierId = vehicle.supplierId
        const supplierName = vehicle.supplierName
        
        // 只有当supplierId和supplierName都有值时才添加到品牌列表
        if (supplierId && supplierName) {
          if (!brandMap.has(supplierId)) {
            brandMap.set(supplierId, {
              code: supplierId,
              name: supplierName
            })
          }
        }
      })

      // 转换为数组并排序
      return Array.from(brandMap.values()).sort((a, b) => {
        return a.name.localeCompare(b.name)
      })
    },

    // 带参数加载车辆数据
    async loadVehiclesWithParams() {
      this.loading = true
      try {
        const params = {}

        // 添加搜索关键词参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.key = this.searchKeyword.trim()
        }

        // 添加品牌筛选参数
        if (this.selectedBrands.length > 0) {
          params.supplierIdList = this.selectedBrands.join(',')
        }

        const res = await listFtmVehicleVariant(params)
        this.vehicleList = res.data || []
        
      } catch (error) {
        console.error('加载车辆数据失败:', error)
        //this.$message.error('加载车辆数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null
      // 执行搜索
      this.loadVehiclesWithParams()
    },
    // 选择全部品牌
    selectAllBrands() {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      // 重置品牌选择
      this.selectedBrands = []
      this.loadVehiclesWithParams()
    },
    // 切换品牌选择
    toggleBrand(brandCode) {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      // 切换品牌选择
      const index = this.selectedBrands.indexOf(brandCode)
      if (index > -1) {
        this.selectedBrands.splice(index, 1)
      } else {
        this.selectedBrands.push(brandCode)
      }
      this.loadVehiclesWithParams()
    },
    // 添加新车型
    addNewVehicle() {
      this.showAddDialog = true
    },

    // 处理车型详情点击
    handleVehicleDetail(vehicle) {
      this.selectedVehicleId = vehicle.id
      this.selectedVehicle = vehicle

      // 重置抽屉状态
      this.resetDrawerState()

      this.showDetailDrawer = true
    },

    // 重置抽屉状态
    resetDrawerState() {
      // 等待下一个tick确保抽屉组件已经渲染
      this.$nextTick(() => {
        if (this.$refs.vehicleDetailDrawer) {
          // 重置到查看模式
          this.ensureViewMode()
          // 重置tab到第一个
          this.resetToFirstTab()
        }
      })
    },

    // 确保抽屉进入查看模式
    ensureViewMode() {
      if (this.$refs.vehicleDetailDrawer) {
        // 如果正在编辑模式，取消编辑
        if (this.$refs.vehicleDetailDrawer.isGlobalEditing) {
          this.$refs.vehicleDetailDrawer.cancelGlobalEdit()
        }
        // 清除表单校验
        this.clearFormValidation()
      }
    },

    // 重置到第一个tab
    resetToFirstTab() {
      if (this.$refs.vehicleDetailDrawer) {
        this.$refs.vehicleDetailDrawer.activeTab = 'detail'
      }
    },

    // 清除表单校验
    clearFormValidation() {
      if (this.$refs.vehicleDetailDrawer) {
        this.$nextTick(() => {
          // 清除基础信息表单校验
          if (this.$refs.vehicleDetailDrawer.$refs.basicInfoForm) {
            this.$refs.vehicleDetailDrawer.$refs.basicInfoForm.clearValidate()
          }
          // 清除车身信息表单校验
          if (this.$refs.vehicleDetailDrawer.$refs.bodyInfoForm) {
            this.$refs.vehicleDetailDrawer.$refs.bodyInfoForm.clearValidate()
          }
        })
      }
    },

    // 关闭详情抽屉
    closeDetailDrawer() {
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      // 重置抽屉宽度
      this.drawerWidth = this.minDrawerWidth
    },

    // 处理编辑车辆提交
    handleEditVehicleSubmit() {
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.$message.success('车型保存成功')
      this.loadVehiclesWithParams()
    },
    handleSaveInfo(){
      this.loadVehiclesWithParams()
    },

    // 清除选中状态
    clearSelection() {
      this.selectedVehicleId = null
    },

    // 从抽屉中删除车辆
    handleDeleteFromDrawer() {
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.loadVehiclesWithParams()
    },

    // 处理新增车型提交
    handleAddVehicleSubmit() {
      this.$message.success('车型保存成功')
      this.loadVehiclesWithParams()
    },

    // 处理车型菜单点击
    handleVehicleMenu(vehicle) {
      // 可以在这里添加菜单相关的逻辑，比如显示下拉菜单
      // 暂时也打开详情抽屉
      this.selectedVehicleId = vehicle.id
      this.showDetailDrawer = true
    },

    // 抽屉拖拽相关方法
    startDrag(event) {
      this.isDragging = true
      this.dragStartX = event.clientX
      this.dragStartWidth = this.drawerWidth

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)

      // 防止文本选择和添加拖拽样式
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'ew-resize'
      document.body.classList.add('drawer-dragging')
      event.preventDefault()
      event.stopPropagation()
    },

    onDrag(event) {
      if (!this.isDragging) return

      // 计算新宽度（向左拖拽增加宽度，向右拖拽减少宽度）
      const deltaX = event.clientX - this.dragStartX
      const newWidth = this.dragStartWidth - deltaX // 注意这里是减法，向左拖动(deltaX为负)会增加宽度

      // 限制最小宽度和最大宽度（不超过窗口宽度的80%）
      const maxWidth = window.innerWidth * 0.8
      if (newWidth >= this.minDrawerWidth && newWidth <= maxWidth) {
        this.drawerWidth = newWidth
      }
    },

    stopDrag() {
      this.isDragging = false

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)

      // 恢复样式
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
      document.body.classList.remove('drawer-dragging')
    }
  }
}
</script>

<style lang="scss" scoped>
.fleets-management-new {
  background-color: #f5f7fa;
  height: calc(100vh - 122px);
  font-family: 'Bosch Sans Global', sans-serif;
  display: flex;
  gap: 20px;
  overflow: hidden;
  position: relative;

  .main-content-container {
    width: 100%;
    height: 100%;
    .box-with-drawer {
      display: flex;
      flex: 1;
      height: 0;
      gap: 16px;
      width: 100%;
      &.with-drawer {
        transition: margin-right 0.3s ease; // 添加过渡动画
      }
    }

    .main-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      flex:1;
    }
  }

  .main-card {
    border-radius: 8px;
    box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
    border: 1px solid #eff1f2;
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__header) {
      padding: 20px;
      border-bottom: 1px solid #eff1f2;
      background-color: #fafbfc;
    }

    :deep(.el-card__body) {
      padding: 0;
      display: flex;
      height: 100%;
      flex-direction: column;
    }
  }

  // 搜索区域

  .search-container {
    display: flex;

    .search-input {
      width: 300px;
      height: 32px;
    }
  }

  // 筛选和操作栏
  .filter-action-bar {
    padding: 20px 16px 16px;
    border-bottom: 1px solid #eff1f2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;

    .filter-section {
      display: flex;
      align-items: center;

      .display-options {
        display: flex;
        align-items: center;
        height: 32px;

        .display-label {
          color: #4e5256;
          font-size: 14px;
          font-weight: 400;
          line-height: 32px;
          margin-left: 2px;
        }
        .display-count {
          color: blue;
          font-size: 14px;
          font-weight: 400;
          line-height: 32px;
          padding-left: 6px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;
      .add-button {
        background: #5755ff;
        border: 1px solid #5755ff;
        border-radius: 2px;
        color: white;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        padding: 7px 15px;
        height:32px;

        .button-icon {
          margin-right: 4px;
        }

        &:hover {
          background: #4644dd;
          border-color: #4644dd;
        }
      }
    }
  }

  // 品牌筛选区域
  .brand-filter-section {
    padding: 14px 20px;
    border-bottom: 1px solid #eff1f2;
    background-color: white;

    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .filter-tag {
        padding: 7px 15px;
        background: #f5f5ff;
        border: 1px solid #ddddff;
        border-radius: 2px;
        color: #5755ff;
        font-size: 14px;
        font-weight: 400;
        line-height: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 24px;

        &:hover {
          background: #e8f1ff;
          border-color: #b3d4ff;
        }

        &.active {
          background: #5755ff;
          border-color: #5755ff;
          color: white;
        }
      }
    }
  }

  // 车辆内容区域
  .vehicles-content {
    padding: 20px;
    background-color: white;
    flex: 1;
    overflow: auto;

    .loading-container {
      padding: 40px;
      text-align: center;
    }

    .empty-container {
      padding: 60px 0;
      text-align: center;
    }

    // 按品牌分组显示
    .vehicles-by-brand {
      .brand-group {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        // 品牌标签
        .brand-label {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          padding: 8px 12px;
          width: 100%;
          border-bottom: 1px solid #eff1f2;

          .brand-name {
            color: #495057;
            font-size: 16px;
            font-weight: 500;
            margin-right: 8px;
          }

          .brand-icon {
            color: #6c757d;
            font-size: 14px;
          }
        }
      }
    }

    // 车辆列表容器样式 - Flex布局
    .vehicles-flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      // 抽屉关闭时 - 超大屏默认5列布局
      .vehicle-flex-item {
        flex: 0 0 calc(20% - 16px); // 5列：20% 宽度，减去gap
        min-width: 230px; // 设置最小宽度
        margin-bottom: 20px;

        .vehicle-card-item {
          width: 100%;
          transition: transform 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }
        }
      }

      // 抽屉打开时 - 调整布局
      &.with-drawer {
        gap: 16px;

        .vehicle-flex-item {
          flex: 0 0 calc(33.333% - 11px); // 3列：33.333% 宽度，减去gap
          margin-bottom: 16px;
        }
      }

      // 响应式布局
      @media (max-width: 1600px) {
        .vehicle-flex-item {
          flex: 0 0 calc(25% - 15px); // 4列：25% 宽度，减去gap
        }

        &.with-drawer {
          .vehicle-flex-item {
            flex: 0 0 calc(50% - 8px); // 2列：50% 宽度，减去gap
          }
        }
      }

      @media (max-width: 1200px) {
        .vehicle-flex-item {
          flex: 0 0 calc(33.333% - 14px); // 3列
        }

        &.with-drawer {
          .vehicle-flex-item {
            flex: 0 0 calc(100% - 0px); // 1列
          }
        }
      }

      @media (max-width: 768px) {
        .vehicle-flex-item {
          flex: 0 0 calc(50% - 10px); // 2列
        }

        &.with-drawer {
          .vehicle-flex-item {
            flex: 0 0 calc(100% - 0px); // 1列
          }
        }
      }

      @media (max-width: 480px) {
        .vehicle-flex-item {
          flex: 0 0 calc(100% - 0px); // 1列
        }
      }
    }
  }
}

// 抽屉打开时的布局调整已通过响应式配置处理

// 抽屉容器样式
.drawer-container {
  width: 740px;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
  border: 1px solid #eff1f2;
  z-index: 10;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease;
  position: relative;
  min-width: 740px; // 设置最小宽度

  // 拖拽手柄样式
  .drawer-resize-handle {
    position: absolute;
    left: 0; // 贴着左边缘
    top: 0;
    width: 10px; // 进一步增加宽度
    height: 100%;
    cursor: ew-resize;
    z-index: 1000; // 提高z-index确保在最上层

    &:hover {
      background: rgba(64, 158, 255, 0.3);
      border-right-color: rgba(64, 158, 255, 0.5);
    }

    &:active {
      background: rgba(64, 158, 255, 0.5);
    }

  }

  .drawer-header {
    width: 100%;
    height: 50px;
    padding: 20px 20px 20px 30px; // 左侧增加10px为拖拽手柄留出空间
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    position: relative; // 确保不会覆盖拖拽手柄
    z-index: 1; // 低于拖拽手柄的z-index

    .drawer-title {
      color: #232628;
      font-size: 18px;
      font-family: 'Bosch Sans', sans-serif;
      font-weight: 600;
      line-height: 18px;
    }

    .drawer-close {
      width: 20px;
      height: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f0f0f0;
      }
    }
  }

  .drawer-content {
    flex: 1;
    padding: 0;
    margin-left: 10px; // 为拖拽手柄留出空间
    position: relative;
    z-index: 1; // 确保低于拖拽手柄
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 拖拽时的全局样式 */
body.drawer-dragging {
  user-select: none !important;
  cursor: ew-resize !important;

  * {
    user-select: none !important;
    pointer-events: none !important;
  }

  .drawer-resize-handle {
    pointer-events: auto !important;
  }
}
</style>
