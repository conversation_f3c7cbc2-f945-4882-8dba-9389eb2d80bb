<template>
  <div class="map-selection-container intersection">
    <div class="map-content">
      <div class="query-param top">
        <div class="label">
          <el-button size="default" type="primary" @click="openQueryParams()" id="tag" :disabled="disabled">
            筛选
            <ltw-icon :class="{ visible: filterForm }" icon-code="el-icon-d-arrow-right"></ltw-icon>
          </el-button>
        </div>
        <div class="form" :class="{ visible: filterForm }">
          <el-checkbox
            class="intersection-check"
            v-model="queryParam.intersectionChecked"
            label="全选"
            @change="changeCheckAll"
          />
          <el-checkbox class="intersection-check" v-model="queryParam.hdComplete" label="HD完整性" />
          <el-cascader
            class="canton-select"
            clearable
            filterable
            popper-class="canton-list"
            v-model="queryParam.cantonCode"
            :options="cantonCodeList"
            :props="props"
          />
          <el-select
            v-model="queryParam.supplierCode"
            placeholder="请选择供应商"
            class="supplier-code"
            clearable
            filterable
          >
            <el-option v-for="item in supplierList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>

          <el-select
            v-model="queryParam.intersectionCollected"
            placeholder="请选择"
            class="supplier-code"
            clearable
            filterable
          >
            <el-option
              v-for="item in intersectionCollectedList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
          <ltw-input
            class="intersection-name"
            :placeholder="$t('请输入关键字')"
            v-model="queryParam.intersectionName"
            clearable
            id="input"
          >
            <template #append>
              <el-button @click="search" id="el-icon-search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
      </div>
      <base-map ref="baseMap" @click-area="clickArea" @click-label="clickLabel" @zoom-changed="zoomChanged"></base-map>
    </div>
    <el-card class="choosed-intersection">
      <template #header>已选路口(共{{ intersectionList?.length || 0 }})</template>
      <div class="body">
        <el-scrollbar class="selected-container">
          <el-card
            class="intersection-item"
            :class="{ active: currentMarkerId === item.id }"
            v-for="(item, index) in intersectionList"
            :key="item.id"
            @click="getMapCenter(item, index)"
            >{{ item.name }}
          </el-card>
          <el-empty v-show="!intersectionList?.length" description="暂无选择路口"></el-empty>
        </el-scrollbar>
      </div>
      <div class="footer" v-if="!disabled">
        <el-button size="small" @click="clearIntersection()">清除</el-button>
        <el-button size="small" type="primary" @click="submitIntersection()">确认</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { latLngPoint } from '@/plugins/map/TxMap'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { showToast } from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { getIntersectionList, daqTaskRecommends, updateDaqTaskRecommends } from '@/apis/data-collect/vt-daq-task'
import BaseMap from '@/components/map/BaseMap.vue'
import { getSysCantonTree } from '@/apis/system/sys-canton'

let baseForm = {
  crs: 1
}
export default {
  name: 'MapPoi',
  components: {
    LtwInput,
    LtwIcon,
    BaseMap
  },
  props: {
    propDisabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propDisabled: {
      handler(val) {
        this.disabled = val
      },
      deep: true,
      immediate: true
    },
    markersBoardList: {
      handler(val) {
        let obj = {}
        val?.forEach(val => {
          val.itemName = val.itemName || '无tag名'
          if (obj[val.itemName]) {
            obj[val.itemName]++
          } else {
            obj[val.itemName] = 1
          }
        })
        this.formatPoiObj = obj
      },
      deep: true,
      immediate: true
    }
  },
  emits: ['reload', 'show-task-record'],
  data() {
    return {
      supplierList: [],
      cantonCodeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
        // checkStrictly: true
      },
      intersectionCollectedList: [
        {
          name: '全部',
          code: ''
        },
        {
          name: '已采集',
          code: true
        },
        {
          name: '未采集',
          code: false
        }
      ],
      queryParam: {
        cantonCode: [],
        supplierCode: '',
        intersectionCollected: '',
        intersectionName: '',
        intersectionChecked: false,
        hdComplete: false
      },
      form: {},
      intersectionList: [],
      allIntersectionList: [],
      preIntersectionList: [],
      currentMarkerId: '',
      selectedStyle: 'selectedStyle',
      collectedStyle: 'collectedStyle',
      disabled: false,
      filterForm: false
    }
  },
  methods: {
    async show(row) {
      await this.$refs.baseMap.show()
      this.form = {
        type: row.data.type,
        taskId: row.data.taskId,
        taskCode: row.data.taskCode,
        id: row.data.id
      }
      if (row?.data?.recommendItems?.length) {
        const postData = {
          ...baseForm,
          idList: row.data.recommendItems?.map(val => val.itemKey)
        }
        this.getIntersectionList(postData, true)
      }
    },
    changeCheckAll(e) {
      this.currentMarkerId = ''
      const globalPolygonLayer = this.$refs.baseMap.getGlobalMapObj('globalPolygonLayer')
      let areas = globalPolygonLayer.getGeometries()
      if (e) {
        this.preIntersectionList = JSON.parse(JSON.stringify(this.intersectionList))
      } else {
        this.intersectionList = JSON.parse(JSON.stringify(this.preIntersectionList))
      }
      this.allIntersectionList.forEach(val => {
        const index = this.intersectionList.findIndex(choose => choose.id === val.id)
        const areaIndex = areas.findIndex(area => area.id === val.id)
        if (!~index) {
          if (e) {
            this.intersectionList.push(val)
          }
          areas[areaIndex].styleId = e ? this.selectedStyle : val.daqTaskCount ? this.collectedStyle : `style_${val.id}`
        }
      })
      globalPolygonLayer.setGeometries(areas)
    },
    openQueryParams() {
      this.filterForm = !this.filterForm
      this.listSupplier()
      this.getSysCantonTree()
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    listSupplier() {
      if (!this.supplierList?.length) {
        listSysRoleOrg({ tagCodeList: 'org_supplier' }).then(res => {
          this.supplierList = res.data
        })
      }
    },
    search() {
      this.currentMarkerId = ''
      this.queryParam.intersectionChecked = false
      if (this.queryParam.cantonCode?.length || this.queryParam.intersectionName || this.queryParam.supplierCode) {
        const postData = {
          ...baseForm,
          intersectionName: this.queryParam.intersectionName,
          supplierCode: this.queryParam.supplierCode,
          collected: this.queryParam.intersectionCollected,
          cantonCode: this.queryParam.cantonCode?.length
            ? this.queryParam.cantonCode[this.queryParam.cantonCode?.length - 1]
            : ''
        }
        if (this.hdComplete) {
          postData.hdComplete = this.hdComplete
        }
        this.getIntersectionList(postData)
      } else {
        showToast(this.$t('请先选择区域、供应商或者关键字'), 'warning')
      }
    },
    getIntersectionList(postData, choose) {
      this.clearMap()
      getIntersectionList(postData).then(async res => {
        if (!res.data.length) {
          showToast('暂无路口', 'warning')
          return
        }

        if (choose) {
          this.intersectionList = res.data
        }
        this.allIntersectionList = JSON.parse(JSON.stringify(res.data))

        this.drawIntersection(res.data)
      })
    },
    drawIntersection(data) {
      let labels = []
      const areas = data
        .filter(val => val.geomJson?.coordinates[0]?.length)
        .map(val => {
          const intersectionIndex = this.intersectionList.findIndex(intersection => intersection.id === val.id)
          const obj = {
            id: val.id,
            styleId: !~intersectionIndex ? (val.daqTaskCount ? this.collectedStyle : '') : this.selectedStyle,
            position: latLngPoint([val.geomCenterPointJson.coordinates[1], val.geomCenterPointJson.coordinates[0]]),
            paths: val.geomJson.coordinates[0].map(coord => latLngPoint([coord[1], coord[0], coord[2]]))
          }
          if (val.daqTaskCount) {
            labels.push({
              ...obj,
              content: val.daqTaskCount
            })
          }
          return obj
        })
      const options = {
        color: 'rgba(245, 108, 108, 0.7)', // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: 'rgba(245, 108, 108, 1)' // 边线颜色
      }
      this.$refs.baseMap.showFitView(areas)
      this.$refs.baseMap.createPolygonLayer(areas, options, 'intersection-polygon-layer', 2)
      this.$refs.baseMap.initMultiLabel(labels)
    },
    getMapCenter(item) {
      this.currentMarkerId = item.id
      this.$refs.baseMap.goCenter(item.geomCenterPointJson.coordinates)
    },
    clickArea(e) {
      if (!this.disabled) {
        const index = this.intersectionList.findIndex(val => val.id === e.geometry.id)
        if (~index) {
          this.toggleArea(this.intersectionList[index])
          this.intersectionList.splice(index, 1)
        } else {
          const intersection = this.allIntersectionList.find(val => val.id === e.geometry.id)
          if (intersection) {
            this.intersectionList.unshift(intersection)
            //设置列表高亮
            this.toggleArea(intersection)
          }
        }
      }
    },
    clickLabel(e) {
      const item = this.allIntersectionList.find(val => val.id === e.geometry.id)
      const postData = {
        type: this.dialogStatus,
        data: {
          locationId: item.id,
          type: 'INTERSECTION',
          locationName: item.name
        }
      }
      this.$emit('show-task-record', postData)
    },
    toggleArea(area) {
      this.currentRouteKey = ''
      const globalPolygonLayer = this.$refs.baseMap.getGlobalMapObj('globalPolygonLayer')
      //切换高亮
      let areas = globalPolygonLayer.getGeometries()

      areas.forEach(val => {
        if (val.id === area.id) {
          val.styleId =
            val.styleId === this.selectedStyle
              ? area.daqTaskCount
                ? this.collectedStyle
                : `style_${area.id}`
              : this.selectedStyle
        }
        return ''
      })
      globalPolygonLayer.setGeometries(areas)
    },
    zoomChanged(e) {
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      const globalMultiLabel = this.$refs.baseMap.getGlobalMapObj('globalMultiLabel')
      if (globalMap.getZoom() >= 18) {
        globalMultiLabel?.setVisible(true)
      } else {
        globalMultiLabel?.setVisible(false)
      }
    },
    clearMap() {
      this.$refs.baseMap.clearObjects('globalMultiLabel', 'globalPolygonLayer')
    },

    async clearIntersection() {
      this.intersectionChecked = false
      this.intersectionList = []

      this.changeCheckAll(false)
    },
    submitIntersection() {
      const intersectionRecommendList = this.intersectionList.map(val => {
        return {
          itemType: 'INTERSECTION',
          itemKey: val.id,
          itemName: val.name,
          longitude: val.geomCenterPointJson.coordinates[0],
          latitude: val.geomCenterPointJson.coordinates[1]
        }
      })
      const postData = {
        ...this.form,
        recommendItems: intersectionRecommendList
      }
      if (this.form?.id) {
        updateDaqTaskRecommends(postData).then(res => {
          showToast('保存成功')
          // this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      } else {
        daqTaskRecommends(postData).then(res => {
          this.form.id = res.data.id
          showToast('保存成功')
          // this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.map-selection-container.intersection {
  width: 100%;
  height: 100%;
  display: flex;

  .map-content {
    position: relative;
    height: 100%;
    width: calc(100% - 200px);

    .map-overview {
      user-select: none;
      height: 100%;
      width: 100%;
      z-index: 0;
    }

    .query-param {
      width: 100%;
      display: flex;
      z-index: 2;
      position: absolute;
      left: 0;

      &.top {
        top: 0;
      }

      &.tools {
        bottom: 0;
      }

      .label {
        display: flex;
        align-items: center;
        white-space: nowrap;

        .ltw-icon {
          transition: all 0.3s;
        }

        .visible {
          transform: rotateZ(180deg);
        }
      }

      .form {
        width: calc(100% - 74px);
        padding-left: 10px;
        display: flex;
        align-items: center;
        transform-origin: 0 50%;
        transform: scaleX(0);
        transition: all 0.3s;
        background: #fff;

        .search-container {
          white-space: nowrap;
          width: 125px;
        }

        &.visible {
          transform: scaleX(1);
        }

        .intersection-check {
          margin-left: 6px;
          margin-right: 10px;
        }

        .intersection-name {
          width: 200px;
        }

        .supplier-code {
          width: 200px;
        }
      }
    }
  }

  .choosed-intersection {
    width: 200px;
    margin-left: 10px;

    :deep(.el-card__header) {
      background: rgb(239, 241, 242);
    }

    :deep(.el-card__body) {
      height: 100%;
    }

    .body {
      height: calc(100% - 88px);

      .total {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
      }

      .list {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }

      .num {
        color: rgb(25, 173, 253);
      }
    }

    .intersection-item {
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        border-color: #fab6b6;
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: end;
    align-items: center;

    .intersection-num {
      white-space: nowrap;
      margin-right: 10px;
    }
  }
}
</style>
