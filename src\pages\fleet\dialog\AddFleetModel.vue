<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="700px"
    @close="dialogClosed"
    @open="dialogOpened"
    draggable
  >
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      label-width="140px"
      :hide-required-asterisk="formReadonly"
      class="add-fleet-model"
      label-position="top"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item :label="$t('编码')" prop="code">
            <ltw-input v-model="form.code" :disabled="formReadonly" id="code"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('名称')" prop="name">
            <ltw-input v-model="form.name" :disabled="formReadonly" id="name"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车型类型')" prop="vehicleType">
            <dictionary-type-selection
              v-model="form.vehicleType"
              :disabled="formReadonly"
              clearable
              dictionaryType="variant_type"
              :placeholder="$t('请选择')"
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车辆用途')" prop="useType">
            <dictionary-type-selection
              v-model="form.useType"
              :disabled="formReadonly"
              clearable
              dictionaryType="use_type"
              :placeholder="$t('请选择')"
              filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierId">
            <supplier-selection
              v-model="form.supplierId"
              :disabled="formReadonly"
              clearable
              @change="changeSupplierName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('装车方案')" prop="projectId">
            <el-select
              v-model="form.projectId"
              filterable
              :placeholder="$t('请选择') + $t('装车方案')"
              clearable
              :disabled="formReadonly"
              id="type"
            >
              <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
            <el-form-item :label="$t('车辆') + $t('品牌')" prop="brand">
              <ltw-input
                v-model="form.brand"
                :disabled="formReadonly"
                id="brand"
              ></ltw-input>
            </el-form-item>
          </el-col> -->
        <el-col :span="12">
          <el-form-item :label="$t('车辆') + $t('型号')" prop="vehicleModel">
            <ltw-input v-model="form.vehicleModel" :disabled="formReadonly" id="vehicleModel"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('轴距') + '(' + $t('毫米') + ')'" prop="wheelBase">
            <ltw-input
              v-model="form.wheelBase"
              :disabled="formReadonly"
              id="wheelBase"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('采集车辆宽度（不考虑反光镜）')" prop="excludeRearviewMirrorWidth">
            <ltw-input
              v-model="form.excludeRearviewMirrorWidth"
              :disabled="formReadonly"
              id="excludeRearviewMirrorWidth"
            >
              <template #append>
                <span>米</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('采集车辆宽度（考虑反光镜）')" prop="includeRearviewMirrorWidth">
            <ltw-input
              v-model="form.includeRearviewMirrorWidth"
              :disabled="formReadonly"
              id="includeRearviewMirrorWidth"
            >
              <template #append>
                <span>米</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车辆质量重心到车头的距离')" prop="toHeadstockDistance">
            <ltw-input
              v-model="form.toHeadstockDistance"
              :disabled="formReadonly"
              id="toHeadstockDistance"
            >
              <template #append>
                <span>米</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车辆质量重心到车尾的距离')" prop="toTailstockDistance">
            <ltw-input
              v-model="form.toTailstockDistance"
              :disabled="formReadonly"
              id="toTailstockDistance"
            >
              <template #append>
                <span>米</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('后轴中心到车辆几何中心的距离')" prop="egoCenterShiftDistance">
            <ltw-input
              v-model="form.egoCenterShiftDistance"
              :disabled="formReadonly"
              id="egoCenterShiftDistance"
            >
              <template #append>
                <span>米</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('描述')" prop="description">
            <ltw-input
              text-type="description"
              id="description"
              v-model="form.description"
              :disabled="formReadonly"
              type="textarea"
            ></ltw-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
          <el-button
            :type="item.buttonStyleType"
            @click="executeButtonMethod(currentButton)"
            v-if="currentButton && currentButton.name"
            >{{ $t(currentButton.name) }}</el-button
          >
        </template>
        <template v-else>
          <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
          <el-button type="primary" @click="submit">{{ $t('保 存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { validateFourDecimalValidity, validateTwoDigitInteger, isPositiveNum, isInteger } from '@/plugins/util'
import { listFtmInstallationProjectSelection } from '@/apis/fleet/ftm-installation-project'
import { getFtmVehicleVariant, saveFtmVehicleVariant, updateFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection.vue'
import SupplierSelection from '@/components/system/SupplierSelection.vue'

const defaultform = {}
export default {
  name: 'AddFleetModel',
  emits: ['reload'],
  components: { DictionaryTypeSelection, SupplierSelection },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        name: [{ required: true, message: this.$t('请输入名称'), trigger: 'change' }],
        code: [{ required: true, message: this.$t('请输入编码'), trigger: 'change' }],
        vehicleModel: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('车辆') + this.$t('型号'),
            trigger: 'change'
          }
        ],
        vehicleType: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('车型类型'),
            trigger: 'change'
          }
        ],
        useType: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('车辆用途'),
            trigger: 'change'
          }
        ],
        supplierId: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('供应商'),
            trigger: 'change'
          }
        ],
        projectId: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('装车方案'),
            trigger: 'change'
          }
        ],
        brand: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('车辆') + this.$t('品牌'),
            trigger: 'change'
          }
        ],
        model: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('车辆') + this.$t('型号'),
            trigger: 'change'
          }
        ],
        excludeRearviewMirrorWidth: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        includeRearviewMirrorWidth: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        toHeadstockDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        toTailstockDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        egoCenterShiftDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        wheelBase: [
          {
            validator: isInteger,
            trigger: 'change'
          },
          {
            validator: isPositiveNum,
            trigger: 'change'
          }
        ]
      },
      projectList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增车型')
          this.form.useType = row.useType
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑车型')
          this.getFtmVehicleVariant(row)
          break
        case 'view':
          this.dialogTitle = this.$t('车型详情')
          this.getFtmVehicleVariant(row)
          break
      }
      this.listFtmInstallationProjectSelection()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          wheelBase: this.form.wheelBase || 0,
          excludeRearviewMirrorWidth: this.form.excludeRearviewMirrorWidth || 0,
          includeRearviewMirrorWidth: this.form.includeRearviewMirrorWidth || 0,
          toHeadstockDistance: this.form.toHeadstockDistance || 0,
          toTailstockDistance: this.form.toTailstockDistance || 0,
          egoCenterShiftDistance: this.form.egoCenterShiftDistance || 0
        }
        if (this.dialogStatus === 'add') {
          saveFtmVehicleVariant(postData).then(() => {
            this.cancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmVehicleVariant(postData).then(() => {
            this.cancel()
          })
        }
      })
    },
    getFtmVehicleVariant(row) {
      getFtmVehicleVariant(row.id).then(res => {
        this.form = res.data
      })
    },
    changeSensorType(e) {
      this.form.sensorTypeName = e.node?.name
    },
    changeSupplierName(e) {
      this.form.supplierName = e.node?.name
    },
    listFtmInstallationProjectSelection() {
      if (!this.projectList?.length) {
        listFtmInstallationProjectSelection().then(res => {
          this.projectList = res.data
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.add-fleet-model {
  padding: 0 20px;

  :deep(.el-form-item__label) {
    line-height: inherit;
  }
}
</style>
