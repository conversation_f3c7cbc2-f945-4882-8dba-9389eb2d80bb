<template>
  <!-- <el-aside> -->
  <div class="aside">
    <div class="menu-list">
      <div class="back-div">
        <el-link class="back-btn" v-if="showBackButton" :underline="false" @click="goBack">
          {{ $t('返回') }}
        </el-link>
      </div>

      <el-scrollbar>
        <el-menu
          class="el-menu-vertical-demo"
          :class="{ 'el-menu-en': getLocale === 'en' }"
          :uniqueOpened="true"
          :default-active="defaultActiveMenu"
          :collapse="collapsed"
          router
          ref="menuRef"
        >
          <template v-for="menu in menuList" :key="menu.id">
            <el-sub-menu :index="menu.pageUrl ? menu.pageUrl : menu.id" v-if="!menu.asLeaf" :id="menu.name">
              <template #title>
                <ltw-icon :icon-code="menu.iconCode"></ltw-icon>
                <span>{{ $t(menu.name) }}</span>
              </template>
              <el-menu-item :index="child.pageUrl" v-for="child in menu.children" :key="child.id" :id="child.code">
                <template #title>
                  <ltw-icon :icon-code="child.iconCode"></ltw-icon>

                  <span>{{ $t(child.name) }}</span>
                </template>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item :index="menu.pageUrl" v-if="menu.asLeaf">
              <template #title>
                <ltw-icon :icon-code="menu.iconCode"></ltw-icon>
                <span>{{ $t(menu.name) }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
    <div class="toggle-button" :class="{ collapsed: collapsed }" @click="toggle">
      <ltw-icon icon-code="el-icon-expand"></ltw-icon>
    </div>
  </div>
  <!-- </el-aside> -->
</template>

<script>
import { treeListSysPrivilegeMenuOfCurrentUser } from '@/apis/system/sys-privilege-menu'
import LtwIcon from '@/components/base/LtwIcon'
import { getLocale } from '@/plugins/util'

export default {
  components: { LtwIcon },
  name: 'LtwMenuAside',
  props: {
    moduleCode: String,
    moduleId: String,
    applicationId: String
  },
  data() {
    return {
      menuList: [],
      collapsed: false,
      showBackButton: false,
      defaultActiveMenu: '',
      getLocale: getLocale()
    }
  },
  // computed: {
  //   getAsideWidth() {
  //     // return this.collapsed ? '64px' : '201px'
  //     return this.collapsed ? '64px' : getLocale() === 'zh' ? '200px' : '280px'
  //   }
  // },
  created() {
    // let routerFunctionMap =
    //   this.$store.state.permission.routerFunctionMap[
    //     this.$router.currentRoute.value.path
    //   ]
    // if (routerFunctionMap) {
    //   this.batchingFunctionList = routerFunctionMap.batchingFunctionList
    //   this.inlineFunctionList = routerFunctionMap.inlineFunctionList
    //   this.outlineFunctionList = routerFunctionMap.outlineFunctionList
    // }
  },
  watch: {
    $route(to) {
      if (to.path !== this.defaultActiveMenu) {
        this.defaultActiveMenu = to.path
      }
    },
    '$store.state.collapsed': {
      handler(val) {
        this.collapsed = val
      }
    },
    '$store.state.showBackButton': {
      handler(val) {
        this.showBackButton = val
      }
    }
  },
  methods: {
    load(queryParam, changeModule) {
      this.getMenuList(queryParam, changeModule)
    },
    goHome() {
      this.findDefaultActiveMenu(this.menuList)
      this.$router.push(this.defaultActiveMenu)
    },
    refresh() {
      let queryParam = {
        moduleCode: this.moduleCode,
        moduleId: this.moduleId,
        applicationId: this.applicationId
      }

      this.getMenuList(queryParam)
      if (this.menuList && this.menuList.length > 0 && !this.menuList[0].asLeaf) {
        this.$refs.menuRef.open(this.menuList[0].id)
      }
    },
    getMenuList(queryParam) {
      treeListSysPrivilegeMenuOfCurrentUser(queryParam).then(res => {
        this.menuList = res.data
        this.$nextTick(function () {
          //
          // if (this.$router.currentRoute.value.path === '/home') {
          //     this.findDefaultActiveMenu(this.menuList)
          //     this.$router.push(this.defaultActiveMenu)
          // } else {
          //     this.defaultActiveMenu = this.$router.currentRoute.value.path
          // }
          let menuFlag = this.checkRoutePathInMenuList(this.$router.currentRoute.value.path, this.menuList)
          let routerFlag = this.checkRoutePathInMenuList(
            this.$router.currentRoute.value.path,
            this.$router.options.routes
          )

          if (routerFlag || menuFlag) {
            this.defaultActiveMenu = this.$router.currentRoute.value.path
          } else {
            this.findDefaultActiveMenu(this.menuList)
            this.$router.push(this.defaultActiveMenu)
          }

          if (this.menuList && this.menuList.length > 0) {
            if (this.menuList[0].asLeaf) {
              this.$refs.menuRef.open(this.menuList[0].id)
            }
          } else {
            this.$router.push('/empty')
          }
        })
      })
    },
    checkRoutePathInMenuList(path, menuList) {
      let result = false
      for (let i = 0; i < menuList.length; i++) {
        let menu = menuList[i]
        if (!menu.children?.length) {
          if (path === (menu.pageUrl || menu.path)) {
            return true
          }
        } else {
          result = this.checkRoutePathInMenuList(path, menu.children)
          if (result) {
            return true
          }
        }
      }

      return result
    },
    toggle() {
      this.collapsed = !this.collapsed
      this.$store.commit('setCollapsed', this.collapsed)
    },
    findDefaultActiveMenu(list) {
      for (let i = 0; i < list.length; i++) {
        let menu = list[i]
        if (menu.asLeaf) {
          if (menu.pageUrl) {
            this.defaultActiveMenu = menu.pageUrl
            return
          }
        } else {
          this.findDefaultActiveMenu(menu.children)
          if (this.defaultActiveMenu) {
            return
          }
        }
      }
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>
<style scoped lang="scss">
.aside {
  .menu-list {
    position: relative;
    height: calc(100% - 24px);

    .back-div {
      text-align: center;
      .back-btn{
        padding: 0 24px;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        width: 100%;
        font-size: 16px;
      }
    }

    .el-menu-vertical-demo:not(.el-menu--collapse) {
      width: 200px;
    }

    .el-menu-vertical-demo.el-menu-en:not(.el-menu--collapse) {
      width: 280px;
    }

    .el-menu {
      border-right: none;
    }

    .el-sub-menu,
    .el-menu-item {
      .ltw-icon {
        :deep(.svg-icon) {
          font-size: 18px;
          margin-right: 5px;
        }
      }

      &:hover,
      &.is-active {
        color: #1d5e99;
        background: rgba(230, 243, 255, 1);
      }
    }

    .el-sub-menu {
      :deep(.el-sub-menu__title) {
        &:hover {
          background: rgba(230, 243, 255, 1);
        }
      }
    }
  }

  .toggle-button {
    width: 100%;
    font-size: 16px;
    color: #000000;
    line-height: 24px;
    padding-right: 10px;
    text-align: right;
    cursor: pointer;

    :deep(.el-icon) {
      transition: all 0.3s;
      transform: rotate(180deg);
    }

    &.collapsed {
      :deep(.el-icon) {
        transform: rotate(0);
      }
    }
  }
}

.dark {
  .aside {
    background: rgba(15, 23, 59, 1);

    .toggle-button {
      color: #fff;
    }

    .menu-list {
      .el-sub-menu,
      .el-menu-item {
        background: rgba(15, 23, 59, 1);
        color: #fff;
      }

      .el-sub-menu,
      .el-menu-item {
        &:hover,
        &.is-active {
          color: #fff;
          background: rgba(24, 40, 100, 1);
        }
      }

      .el-sub-menu {
        :deep(.el-sub-menu__title) {
          color: #fff;

          &:hover {
            background: rgba(24, 40, 100, 1);
          }
        }
      }
    }
  }
}
</style>
