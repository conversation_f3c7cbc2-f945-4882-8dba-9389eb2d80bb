<template>
    <div  class="ltw-page-container">
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container">
                    <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                            <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </el-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button :type="item.buttonStyleType" :key="item.id" v-for="item in outlineFunctionList" @click="executeButtonMethod(item)">
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                        {{$t(item.name)}}
                    </el-button>
                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            {{$t('批量操作')}}
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList"  :command="item.buttonCode">
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                    {{$t(item.name)}}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records" stripe border @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
<!--                                <el-table-column header-align="center" align="center" prop="deliveryRecordId" :label="$t('寄送记录id')"></el-table-column>-->
                <el-table-column header-align="center" align="center" prop="transportMode" min-width="110" :label="$t('运输方式 ')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="diskCode" :label="$t('磁盘号')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="diskSerialNumber" min-width="175" :label="$t('磁盘序列号')"></el-table-column>
<!--                <el-table-column header-align="center" align="center" prop="deliveryEmpId" :label="$t('寄送人id')"></el-table-column>-->
                <el-table-column header-align="center" align="center" prop="deliveryEmpName" :label="$t('寄送人')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="deliveryTime" min-width="170" :label="$t('寄送时间')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="deliveryLocation" :label="$t('寄送地')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="receiptLocationType" min-width="150" :label="$t('目的地类型')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="receiptLocation" :label="$t('目的地')"></el-table-column>
<!--                <el-table-column header-align="center" align="center" prop="receiptEmpId" :label="$t('签收人id')"></el-table-column>-->
                <el-table-column header-align="center" align="center" prop="receiptEmpName" :label="$t('签收人')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="receiptTime" min-width="170" :label="$t('签收时间')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="receiptStatus" :label="$t('签收状态')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="trackingNumber" :label="$t('快递单号')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="longitude" :label="$t('定位经度')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="latitude" :label="$t('定位纬度')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="address" :label="$t('定位地址')"></el-table-column>
                <el-table-column header-align="center" align="center" prop="description" :label="$t('描述')"></el-table-column>
                <el-table-column header-align="center" align="center" :label="$t('操作')"  min-width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="$t(item.name)" placement="top" :enterable="false"
                            >
                                <el-button :type="item.buttonStyleType" size="mini"
                                           @click="executeButtonMethod(item,scope.row)">
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                            </el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
<!--                <el-form-item :label="$t('寄送记录id')" prop="deliveryRecordId">-->
<!--                    <el-input v-model="formData.deliveryRecordId" :disabled="formReadonly"></el-input>-->
<!--                </el-form-item>-->
                <el-form-item :label="$t('运输方式 ')" prop="transportMode">
                    <el-input v-model="formData.transportMode" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('磁盘号')" prop="diskCode">
                    <el-input v-model="formData.diskCode" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('磁盘序列号')" prop="diskSerialNumber">
                    <el-input v-model="formData.diskSerialNumber" :disabled="formReadonly"></el-input>
                </el-form-item>
<!--                <el-form-item :label="$t('寄送人id')" prop="deliveryEmpId">-->
<!--                    <el-input v-model="formData.deliveryEmpId" :disabled="formReadonly"></el-input>-->
<!--                </el-form-item>-->
                <el-form-item :label="$t('寄送人')" prop="deliveryEmpName">
                    <el-input v-model="formData.deliveryEmpName" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('寄送时间')" prop="deliveryTime">
                    <el-input v-model="formData.deliveryTime" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('寄送地')" prop="deliveryLocation">
                    <el-input v-model="formData.deliveryLocation" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('目的地类型')" prop="receiptLocationType">
                    <el-input v-model="formData.receiptLocationType" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('目的地')" prop="receiptLocation">
                    <el-input v-model="formData.receiptLocation" :disabled="formReadonly"></el-input>
                </el-form-item>
<!--                <el-form-item :label="$t('签收人id')" prop="receiptEmpId">-->
<!--                    <el-input v-model="formData.receiptEmpId" :disabled="formReadonly"></el-input>-->
<!--                </el-form-item>-->
                <el-form-item :label="$t('签收人')" prop="receiptEmpName">
                    <el-input v-model="formData.receiptEmpName" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('签收时间')" prop="receiptTime">
                    <el-input v-model="formData.receiptTime" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('签收状态')" prop="receiptStatus">
                    <el-input v-model="formData.receiptStatus" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('快递单号')" prop="trackingNumber">
                    <el-input v-model="formData.trackingNumber" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('定位经度')" prop="longitude">
                    <el-input v-model="formData.longitude" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('定位纬度')" prop="latitude">
                    <el-input v-model="formData.latitude" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('定位地址')" prop="address">
                    <el-input v-model="formData.address" :disabled="formReadonly"></el-input>
                </el-form-item>
                <el-form-item :label="$t('描述')" prop="description">
                    <el-input v-model="formData.description" :disabled="formReadonly"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <template v-if="dialogStatus === 'view'">
                        <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">{{$t('关 闭')}}</el-button>
                        <el-button
                            :type="item.buttonStyleType"
                            @click="executeButtonMethod(currentButton)"
                            v-if="currentButton && currentButton.name"
                        >{{ $t(currentButton.name) }}</el-button>
                    </template>
                    <template v-else>
                        <el-button @click="dialogVisible = false">{{$t('取 消')}}</el-button>
                        <el-button type="primary" @click="save">{{$t('保 存')}}</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveFtmDiskReceiptRecord,
        updateFtmDiskReceiptRecord,
        deleteFtmDiskReceiptRecord,
        pageFtmDiskReceiptRecord,
        getFtmDiskReceiptRecord
    } from '@/apis/fleet/ftm-disk-receipt-record'
    import BASE_CONSTANT from '@/plugins/constants/base-constant'
    import { showToast, showConfirmToast } from '@/plugins/util'

    const defaultFormData = {}
    export default {
        name: "FtmDiskReceiptRecord",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                dataOperatePermission:{},
                pageData: {
                    total: 0
                },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({},defaultFormData),
                formRules: {
                    deliveryRecordId: [
                        {required: true, message: this.$t('请输入寄送记录id'), trigger: 'blur'}
                    ],
                    transportMode: [
                        {required: true, message: this.$t('请输入运输方式 '), trigger: 'blur'}
                    ],
                    diskCode: [
                        {required: true, message: this.$t('请输入磁盘号'), trigger: 'blur'}
                    ],
                    diskSerialNumber: [
                        {required: true, message: this.$t('请输入磁盘序列号'), trigger: 'blur'}
                    ],
                    deliveryEmpId: [
                        {required: true, message: this.$t('请输入寄送人id'), trigger: 'blur'}
                    ],
                    deliveryEmpName: [
                        {required: true, message: this.$t('请输入寄送人'), trigger: 'blur'}
                    ],
                    deliveryTime: [
                        {required: true, message: this.$t('请输入寄送时间'), trigger: 'blur'}
                    ],
                    deliveryLocation: [
                        {required: true, message: this.$t('请输入寄送地'), trigger: 'blur'}
                    ],
                    receiptLocationType: [
                        {required: true, message: this.$t('请输入目的地类型'), trigger: 'blur'}
                    ],
                    receiptLocation: [
                        {required: true, message: this.$t('请输入目的地'), trigger: 'blur'}
                    ],
                    receiptEmpId: [
                        {required: true, message: this.$t('请输入签收人id'), trigger: 'blur'}
                    ],
                    receiptEmpName: [
                        {required: true, message: this.$t('请输入签收人'), trigger: 'blur'}
                    ],
                    receiptTime: [
                        {required: true, message: this.$t('请输入签收时间'), trigger: 'blur'}
                    ],
                    receiptStatus: [
                        {required: true, message: this.$t('请输入签收状态， 待签收/已签收'), trigger: 'blur'}
                    ],
                    trackingNumber: [
                        {required: true, message: this.$t('请输入快递单号'), trigger: 'blur'}
                    ],
                    longitude: [
                        {required: true, message: this.$t('请输入定位经度'), trigger: 'blur'}
                    ],
                    latitude: [
                        {required: true, message: this.$t('请输入定位纬度'), trigger: 'blur'}
                    ],
                    address: [
                        {required: true, message: this.$t('请输入定位地址'), trigger: 'blur'}
                    ],
                    description: [
                        {required: true, message: this.$t('请输入描述'), trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData:[],
                currentButton: {}
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
                this.dataOperatePermission = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(button,row){
                this.currentButton = {}
                this[button.buttonCode](row, button)            },
            refresh(){
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageFtmDiskReceiptRecord(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = this.$t('新增')
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveFtmDiskReceiptRecord(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateFtmDiskReceiptRecord(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                    }
                )
            },
            edit(row) {
                this.dialogTitle = this.$t('修改')
                this.dialogStatus = 'edit'
                getFtmDiskReceiptRecord(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            view(row) {
                this.dialogTitle = this.$t('查看')
                this.dialogStatus = 'view'
                getFtmDiskReceiptRecord(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            handleCommand(command){
                if(this.selectedData.length === 0){
                   return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
                }
                if(command === 'batchRemove'){
                    this.batchRemove()
                }
            },
            singleRemove(row) {
                this.remove({id:row.id})
            },
            batchRemove(){
                let idList = [];
                this.selectedData.forEach(ele=>{
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param){
                showConfirmToast({
                    message: BASE_CONSTANT.DELETE_CONFIRM_MSG
                }).then(res => {
                    deleteFtmDiskReceiptRecord(param).then(
                        ()=>{
                            this.query()
                        }
                    )
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened(){

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value){
                this.selectedData = value
            },
            initForm(){
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({},defaultFormData)
            }

        }
    }
</script>

<style scoped lang="scss">
    .button-group{
        .el-button{
            margin-right: 10px;
        }
    }

</style>
