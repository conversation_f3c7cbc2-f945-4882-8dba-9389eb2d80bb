<template>
  <el-descriptions :column="2" border id="requiremrnts-form">
    <el-descriptions-item :label="$t('编码')" align="center">{{ form.code }} </el-descriptions-item>
    <el-descriptions-item :label="$t('名称')" align="center">{{ form.name }} </el-descriptions-item>
    <el-descriptions-item :label="$t('描述')" :span="2" align="center">{{ form.description }} </el-descriptions-item>
    <el-descriptions-item :label="$t('发起人')" align="center">
      {{ form.initiator }}
    </el-descriptions-item>
    <el-descriptions-item :label="$t('接收人')" align="center">
      {{ form.receiver }}
    </el-descriptions-item>
    <el-descriptions-item :label="$t('标签')" align="center">
      <el-link @click="getTags(form)" type="primary" :underline="false">{{ form.tagAmount || 0 }} </el-link>
    </el-descriptions-item>
    <el-descriptions-item :label="$t('期望日期')" align="center">
      <el-tag type>{{ form.expectedStartTime }} </el-tag>&nbsp; - &nbsp;
      <el-tag type>{{ form.expectedEndTime }}</el-tag>
    </el-descriptions-item>
  </el-descriptions>
  <tag-list @reload="tagSave" ref="TagList" />
</template>

<script>
import $store from '@/store'
import { ElDescriptions, ElDescriptionsItem, ElLink, ElTag } from 'element-plus'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import { getDaqReq, updateDaqReq } from '@/apis/data-collect/vt-daq-req'

export default {
  name: 'RequirementsForm',
  emits: ['reload'],
  data() {
    return {
      // 配置标签
      tagsData: [],
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      tagsTitle: '',
      id: '',
      form: {}
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    }
  },
  // computed: {
  //   form() {
  //     return this.item
  //   }
  // },
  watch: {
    item(val) {
      this.form = val
    }
  },
  components: {
    ElDescriptions,
    ElLink,
    ElDescriptionsItem,
    ElTag,
    TagList
  },

  methods: {
    show(data) {
      this.id = data.id
      this.getDaqReq()
    },
    getDaqReq() {
      getDaqReq(this.id).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        res.data.dateRange = [res.data.expectedStartTime, res.data.expectedEndTime]
        this.tagList = res.data.tagList
        this.form = res.data
      })
    },
    getTags(row) {
      let empId = $store.state.permission.currentUser.empId
      let editTags = row.status !== 'finished' && row.empId === empId
      this.$refs.TagList.show({
        type: editTags ? 'edit' : 'view',
        tagList: row.tagList
      })
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        this.form.tagList = dataList
        this.updateDaqReq()
      }
    },
    updateDaqReq() {
      let postData = { ...this.form }
      postData.tagList = this.form.tagList
      postData.tagAmount = this.form.tagList.length
      updateDaqReq(postData).then(() => {
        this.$emit('reload')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-descriptions {
  height: calc(100% - 36px);

  :deep(.el-descriptions__body) {
    height: 100%;

    .el-descriptions__table.is-bordered .el-descriptions__cell {
      font-size: 12px;
      font-weight: 400;
      padding: 2px 11px;

      &.el-descriptions__label {
        font-weight: 600;
      }
    }

    .is-bordered {
      height: 100%;
    }
  }
}
</style>
