<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        border
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
        :cell-style="handleCellStyle"
      >
        <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
        <el-table-column header-align="center" align="center" prop="vin" :label="$t('车架号')"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="driverName"
          :label="$t('司机姓名')"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="complianceOfficerEmpName"
          :label="$t('合规员姓名')"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="startTime"
          :label="$t('开始时间')"
        ></el-table-column>
        <el-table-column header-align="center" align="center" prop="endTime" :label="$t('结束时间')"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="issueTypeName"
          :label="$t('事件类型')"
          class-name="highlight"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="issueDescription"
          show-overflow-tooltip
          :label="$t('事件描述')"
        >
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="contactPerson"
          :label="$t('第三方对接人')"
        ></el-table-column>
        <el-table-column header-align="center" align="center" :label="$t('操作')" min-width="80">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" size="small" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
      <el-form :model="formData" ref="formRef" label-width="100px">
        <el-form-item :label="$t('车架号')" prop="vin">
          <el-input v-model="formData.vin" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('司机姓名')" prop="driverName">
          <el-input v-model="formData.driverName" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('合规员姓名')" prop="complianceOfficerEmpName">
          <el-input v-model="formData.complianceOfficerEmpName" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('开始时间')" prop="startTime">
          <el-input v-model="formData.startTime" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('结束时间')" prop="endTime">
          <el-input v-model="formData.endTime" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('事件类型')" prop="issueType">
          <el-input v-model="formData.issueTypeName" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('事件描述')" prop="issueDescription">
          <el-input type="textarea" v-model="formData.issueDescription" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('第三方对接人')" prop="contactPerson">
          <el-input v-model="formData.contactPerson" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('附件')" prop="photo">
          <upload-file
              ref="uploadImage"
              source-type="vehicle_issue"
              accept=".jpg,.jpeg,.png,.gif"
              :source-id="formData.id"
              id="photo"
              disabled
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="dialogStatus === 'view'">
            <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
            <el-button
              :type="item.buttonStyleType"
              @click="executeButtonMethod(currentButton)"
              v-if="currentButton && currentButton.name"
              >{{ $t(currentButton.name) }}</el-button
            >
          </template>
          <template v-else>
            <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
            <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import UploadFile from '@/components/system/UploadFile.vue'
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  saveFtmVehicleIssue,
  updateFtmVehicleIssue,
  deleteFtmVehicleIssue,
  pageFtmVehicleIssue,
  getFtmVehicleIssue
} from '@/apis/fleet/ftm-vehicle-issue'

const defaultFormData = {}
export default {
  name: 'FtmVehicleIssue',
  components:{UploadFile},
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    handleCellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'issueTypeName') {
        return { 'background-color': '#E6A23C', color: '#111214', 'font-weight': 'bolder' }
      }
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageFtmVehicleIssue(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveFtmVehicleIssue(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmVehicleIssue(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getFtmVehicleIssue(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getFtmVehicleIssue(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmVehicleIssue(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
</style>
