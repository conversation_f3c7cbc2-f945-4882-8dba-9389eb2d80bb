<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="disassemble-requirement"
  >
    <requirements-form
      @reload="getDaqReq"
      :item="form"
      ref="RequirementsForm"
    />
    <div class="header">
      <div
        class="title"
        v-if="
          dialogStatus === 'disassemble' ||
          (form.daqTaskList && form.daqTaskList.length)
        "
      >
        {{ $t('任务拆解') }}
      </div>
      <el-button
        v-if="dialogStatus === 'disassemble'"
        type="primary"
        @click="addTask"
        id="addTask"
        ><ltw-icon icon-code="el-icon-plus"></ltw-icon
        >{{ $t('新增') }}</el-button
      >
    </div>
    <el-card
      class="box-card"
      v-for="(item, index) in form.daqTaskList"
      :key="index"
    >
      <div class="card-header">
        <el-link type="primary" :underline="false" id="code">{{ item.code }}</el-link>
        <el-link :type="getStatusType(item.status).type" id="status">{{
          getStatusType(item.status).name
        }}</el-link>
      </div>
      <daq-task-form @reload="getDaqReq" :item="item" ref="DaqTaskForm" />
      <div class="card-footer">
        <el-link
          v-if="item.status === 'draft' && empId === form.recipientEmpId"
          type="success"
          @click="optTaskDetail('publish', item.id)"
          :underline="false"
          id="release"
          >{{ $t('发布') }}</el-link
        >
        <el-link
          type="warning"
          v-if="item.status === 'draft' && empId === form.recipientEmpId"
          @click="optTaskDetail('edit', item.id)"
          :underline="false"
          id="edit"
          >{{ $t('编辑') }}</el-link
        >
        <el-link
          type="danger"
          v-if="item.status === 'draft' && empId === form.recipientEmpId"
          @click="optTaskDetail('delete', item.id)"
          :underline="false"
          id="delete"
          >{{ $t('删除') }}</el-link
        >
        <el-link
          type="warning"
          v-if="item.status === 'published' && empId === form.recipientEmpId"
          @click="startTask(item.id)"
          :underline="false"
          id="start"
          >{{ $t('开始') }}</el-link
        >
        <el-link
          type="warning"
          v-if="item.status === 'executing' && empId === form.recipientEmpId"
          @click="finishTask(item.id)"
          :underline="false"
          id="finish"
          >{{ $t('完成') }}</el-link
        >
        <el-link
          type="primary"
          @click="optTaskDetail('view', item.id)"
          :underline="false"
          id="detail"
          >{{ $t('详情') }}</el-link
        >
      </div>
    </el-card>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" id="cancel">{{ $t('取消') }}</el-button>
        <el-button
          v-if="dialogStatus === 'finish'"
          type="primary"
          @click="finish"
          id="finish"
          >{{ $t('完成') }}</el-button
        >
      </span>
    </template>
  </el-dialog>
  <view-task-detail ref="ViewTaskDetail" />
  <add-task @reload="getDaqReq" ref="AddTask" />
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElCard,
  ElLink,
  ElSelect,
  ElOption
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { i18n } from '@/plugins/lang'
import $store from '@/store'
import AddTask from '@/pages/dataCollect/dialog/AddTask.vue'
import ViewTaskDetail from '@/pages/dataCollect/dialog/ViewTaskDetail.vue'
import RequirementsForm from '@/pages/dataCollect/components/RequirementsForm.vue'
import DaqTaskForm from '@/pages/dataCollect/components/DaqTaskForm.vue'
import { getDaqReq, finishDaqReq } from '@/apis/data-collect/vt-daq-req'
import {
  startTaskDetail,
  finishTaskDetail,
  publishTaskDetail,
  deleteDaqReqDetail
} from '@/apis/data-collect/vt-daq-task'
import { showToast, showConfirmToast } from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
const defaultform = {
  daqTaskList: []
}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      id: '',
      empId: '',
      $t: i18n.global.t
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElCard,
    ElLink,
    ElSelect,
    ElOption,
    LtwIcon,
    RequirementsForm,
    AddTask,
    DaqTaskForm,
    ViewTaskDetail
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = i18n.global.t('采集需求')
      this.id = row.id
      this.empId = $store.state.permission.currentUser.empId
      this.getDaqReq()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.form = { ...defaultform }
    },
    getDaqReq() {
      getDaqReq(this.id).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        res.data.dateRange = [
          res.data.expectedStartTime,
          res.data.expectedEndTime
        ]
        this.tagList = res.data.tagList
        this.form = res.data
      })
    },
    query() {},
    addTask() {
      this.$refs.AddTask.show({
        type: 'add',
        id: this.form.id,
        acquisitionType: this.form.acquisitionType
      })
    },
    tagClose() {
      this.tagDialogVisible = false
    },
    editTags(tagList) {
      this.$refs.RequirementsForm.editTags(tagList)
      // this.tagDialogVisible = true
      // this.tagsTitle = i18n.global.t('查看标签')
      // if (this.tagsData && this.tagsData.length) {
      //   this.setCheckedList(this.tagsData, tagList)
      // } else {
      //   treeListBsTagGroup().then(res => {
      //     this.tagsData = res.data
      //     this.setCheckedList(this.tagsData, tagList)
      //   })
      // }
    },

    setCheckedList(list, tagList) {
      let checkedCount = 0
      if (tagList && tagList.length) {
        let tagMap = {}
        tagList.forEach(tag => {
          tagMap[tag.id] = tag
        })
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
              if (tagMap[tag.id]) {
                item.checkedTagIdList.push(tag.id)
                tag.checked = true
                if (tag.type === 'continuous') {
                  tag.unit = 'h'
                  if (tagMap[tag.id].min) {
                    let time = this.formatToHour(tagMap[tag.id].min)
                    tag.min = time.num
                    tag.unit = time.unit
                  }
                } else if (tag.type === 'transient') {
                  tag.min = tagMap[tag.id].min
                }
              } else {
                tag.checked = false
              }
            })
            if (item.checkedTagIdList.length > 0) {
              checkedCount++
              if (item.checkedTagIdList.length === item.tagList.length) {
                item.checkedAll = true
              } else {
                item.isIndeterminate = true
              }
            }
          }
          if (item.children && item.children.length > 0) {
            checkedCount++
            let childrenCheckedCount = this.setCheckedList(
              item.children,
              tagList
            )
            if (childrenCheckedCount === item.children.length) {
              item.checkedAll = true
            } else {
              if (childrenCheckedCount > 0) {
                item.isIndeterminate = true
              }
            }
          }
        })
      } else {
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
            })
          }
          if (item.children && item.children.length > 0) {
            this.setCheckedList(item.children, tagList)
          }
        })
      }
      return checkedCount
    },
    formatToHour(sec) {
      let num = sec,
        unit = 's'
      if (sec >= 3600) {
        num = Math.floor(sec / 3600)
        unit = 'h'
      } else if (sec >= 60) {
        num = Math.floor(sec / 60)
        unit = 'min'
      }
      return {
        num,
        unit
      }
    },
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    },
    publishTaskDetail(id) {
      publishTaskDetail(id).then(res => {
        showToast('发布成功')
        this.getDaqReq()
      })
      // })
    },
    deleteDaqReqDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail({ id }).then(() => {
          showToast('删除成功')
          this.getDaqReq()
        })
      })
      // })
    },
    // optTaskDetail(val) {
    //   if (val.type === 'publish') {
    //     this.getDaqReq()
    //   } else {
    //     this.$refs.AddTask.show({ type: val.type, id: val.id })
    //   }
    // },
    optTaskDetail(type, id) {
      if (type === 'publish') {
        this.publishTaskDetail(id)
      } else if (type === 'delete') {
        this.deleteDaqReqDetail(id)
      } else if (type === 'view') {
        this.$refs.ViewTaskDetail.show({ id })
      } else {
        this.$refs.AddTask.show({ type, id })
      }
    },
    finish() {
      finishDaqReq(this.form.id).then(res => {
        this.cancel()
        showToast('完成成功')
      })
    },
    startTask(id) {
      startTaskDetail(id).then(res => {
        showToast('已开始')
        this.getDaqReq()
      })
    },
    finishTask(id) {
      finishTaskDetail(id).then(res => {
        showToast('已完成')
        this.getDaqReq()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  margin: 10px 0;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    padding-left: 8px;
    border-left: 2px solid rgb(0, 120, 90);
  }
}

.el-descriptions {
  :deep(.el-descriptions__cell) {
    font-size: 12px;
    font-weight: 400;
    padding: 0 11px;
    white-space: nowrap;
  }
  :deep(.el-descriptions__label) {
    font-weight: 600;
  }
}
.box-card {
  margin-bottom: 12px;
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px 10px 0;
  }
  .card-footer {
    text-align: right;
    .el-link:not(:last-child) {
      margin-right: 8px;
    }
  }
}
</style>
