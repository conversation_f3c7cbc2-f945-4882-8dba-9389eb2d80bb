<template>
    <div @click="openApplication" class="app-container">
        <div class="icon-container">
            <ltw-auth-image
                    :auth-src="icon ? downloadUrl+icon:defaultIcon"
                    fit="fill">
                <template #error>
                    <div class="image-slot">
                        <el-icon><picture-outline /></el-icon>
                    </div>
                </template>
            </ltw-auth-image>
        </div>
        <div class="text-container">{{name}}</div>
    </div>
</template>

<script>
    import GLB_CONFIG from '@/plugins/glb-constant'
    import LtwAuthImage from "@/components/base/LtwAuthImage";

    export default {
        name: "Application",
        components: {LtwAuthImage},
        data() {
            return {
                downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
                defaultIcon: require('@/assets/images/logo.png')
            }
        },
        props: {
            id: String,
            icon: String,
            code: String,
            name: String,
            enabled: Boolean,
            indexUrl: String
        },
        methods: {
            openApplication() {
                window.open(this.indexUrl)
            }
        },
    }
</script>

<style scoped lang="scss">
    .app-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        margin: 10px 10px;
        cursor: pointer;
        width: 150px;
        height: 150px;
        background: #f9f9f9;
        .icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 62px;
            height: 62px;
            .image-slot {
                font-size: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                background: #f5f7fa;
                color: #909399;
            }
        }
    }

    .text-container {
        height: 60px;
        padding: 5px;
        margin-top: 8px;
        text-align: center;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        line-height: 16px;
    }


</style>
