<template>
  <el-dialog
    v-model="dialogVisible"
    width="60%"
    append-to-body
    draggable
    @close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="add-task"
    :show-close="false"
    :fullscreen="fullscreen"
  >
    <template #header>
      <div class="custom-header">
        <div class="title">{{ dialogTitle }}</div>
        <div class="title-opt">
          <el-button plain class="title-btn" @click="toggleFullScreen()">
            <ltw-icon :icon-code="'el-icon-' + (fullscreen ? 'notification' : 'full-screen')"></ltw-icon>
          </el-button>
          <el-button plain class="title-btn" @click="handleClose">
            <ltw-icon icon-code="el-icon-close"></ltw-icon>
          </el-button>
        </div>
      </div>
    </template>
    <!--    <el-scrollbar>-->
    <el-steps :active="activeStep" finish-status="success" simple>
      <el-step title="基础信息"></el-step>
      <el-step title="标签" />
      <!--      <el-step title="AIO配置" />-->
      <el-step title="推荐" />
    </el-steps>
    <div class="task-body">
      <task-step1
        ref="taskStep1"
        v-show="activeStep === 0"
        @updateData="updateData"
        @reload="reloadStep1"
        @showTagList="showTagList"
        :dialogStatus="dialogStatus"
      ></task-step1>
      <task-step2
        ref="taskStep2"
        :tagData="tagData"
        :requirementList="formData.groupList"
        v-show="activeStep === 1"
        :dialogStatus="dialogStatus"
        @updateTreeData="updateTreeData"
        @updateDes="updateDes"
      ></task-step2>
      <!--      <aio-config ref="AioConfig" v-show="activeStep === 2" />-->
      <task-recommend ref="TaskRecommend" v-show="activeStep === 2" @show-task-record="showTaskRecord" />
    </div>

    <!--    </el-scrollbar>-->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="previous" v-if="activeStep > 0">{{ $t('上一步') }}</el-button>
                <el-button plain type="primary" @click="jump" v-if="activeStep !== 2 && formData.id">{{
                  $t('跳过')
                }}</el-button>
        <el-button type="primary" @click="next" v-if="activeStep < 2">{{ $t('下一步') }}</el-button>
        <!--        <el-button type="primary" @click="next" v-if="activeStep < 3">{{ $t('下一步') }}</el-button>-->
        <el-button type="success" @click="saveDialog" v-else :disabled="dialogStatus === 'views'">{{
          $t('完成')
        }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { updateKpi, updateTagSample } from '@/apis/data-collect/vt-daq-task'
import TaskStep1 from './TaskStep1.vue'
import TaskStep2 from './TaskStep2.vue'
import TaskRecommend from '@/pages/dataCollect/components/TaskRecommend.vue'
// import AioConfig from '@/pages/dataCollect/components/AioConfig.vue'
import { nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { TopLeft } from '@element-plus/icons'

export default {
  name: 'AddTask',
  emits: ['reload', 'show-tag-list', 'show-tag-samples-dialog', 'show-task-record'],
  components: {
    TopLeft,
    LtwIcon,
    TaskStep1,
    TaskStep2,
    TaskRecommend
    // AioConfig
  },
  data() {
    return {
      formData: {},
      activeStep: 0,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      tagData: [],
      treeData: '',
      result: '',
      dragData: '',
      fullscreen: false
    }
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('采集任务')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑任务')

          break
        case 'view':
          this.dialogTitle = this.$t('任务详情')

          break
      }
      nextTick(() => {
        this.$refs.taskStep1.show(row)
      })
    },
    handleClose() {
      this.$refs.taskStep1.dialogClosed()
      this.$refs.taskStep2.closeDialog()
      this.$refs.TaskRecommend.initForm()
      this.activeStep = 0
      this.dialogVisible = false
    },
    previous() {
      // if (this.activeStep === 2) {
      if (this.activeStep === 1) {
        this.$refs.TaskRecommend.initForm()
      }
      this.activeStep--
    },
    updateData(data) {
      this.formData = data
      this.tagData = this.formData.groupList
      this.activeStep++
      if(this.dialogStatus !== 'view'){
        this.$emit('reload')
      }
    },
    updateTreeData(treeData) {
      if (!treeData) return
      const taglist = this.extractTagLists(treeData)
      this.dragData = taglist.map((item, index) => {
        return {
          id: item.targetId,
          min: Number(item.min) || 0,
          minDuration: Number(item.minDuration) || 0,
          sortNum: Number(index),
          description: item.description,
          tagSamples: item.tagSamples,
          previousDuration: Number(item.previousDuration) || 0,
          followingDuration: Number(item.followingDuration) || 0,
          supportVoice: item.supportVoice,
          supportTrigger: item.supportTrigger,
        }
      })
    },
    updateDes(value) {
      const index = this.dragData.findIndex(item => item.id === value.targetId)
      if (~index) {
        this.dragData[index].description = value.description
        this.dragData[index].tagSamples = value.tagSamples
      } else {
        this.dragData.push({
          id: value.targetId,
          min: 0,
          minDuration: 0,
          sortNum: Number(this.dragData.length + 1),
          description: value.description,
          tagSamples: value.tagSamples
        })
      }
    },
    async next() {
      if (this.activeStep === 0) {
        this.$refs.taskStep1.save()
      } else if (this.activeStep === 1) {
        if (this.dialogStatus !== 'view') {
          await updateKpi(this.dragData)
        }
        //   this.activeStep++
        //   this.$refs.AioConfig.show({
        //     type: this.dialogStatus,
        //     data: { id: this.formData.id }
        //   })
        // } else if (this.activeStep === 2) {
        //   await this.$refs.AioConfig.save()
        this.activeStep++
        this.$refs.TaskRecommend.show({
          type: this.dialogStatus,
          data: {
            groupList: this.formData.groupList,
            id: this.formData.id,
            code: this.formData.code,
            acquisitionType: this.formData.acquisitionType,
            requirementIdList: this.formData.requirementList?.map(val => val.id)
          }
        })
      }

      // if (this.activeStep === 0) {
      //   this.$refs.taskStep1.save()
      // } else if (this.activeStep === 1) {
      //   if (this.dialogStatus !== 'view') {
      //     await updateKpi(this.dragData)
      //   }
      //   this.activeStep++
      //   this.$refs.AioConfig.show({
      //     type: this.dialogStatus,
      //     data: { id: this.formData.id }
      //   })
      // } else if (this.activeStep === 2) {
      //   await this.$refs.AioConfig.save()
      //   this.activeStep++
      //   this.$refs.TaskRecommend.show({
      //     type: this.dialogStatus,
      //     data: {
      //       groupList: this.formData.groupList,
      //       id: this.formData.id,
      //       code: this.formData.code,
      //       acquisitionType: this.formData.acquisitionType,
      //       requirementIdList: this.formData.requirementList?.map(val => val.id)
      //     }
      //   })
      // }
    },
    jump() {
      this.activeStep++
      if (this.activeStep === 1) {
        this.tagData = this.formData.groupList
      } else if (this.activeStep === 2) {
      //   this.$refs.AioConfig.show({
      //     type: this.dialogStatus,
      //     data: { id: this.formData.id }
      //   })
      // } else if (this.activeStep === 3) {
        this.$refs.TaskRecommend.show({
          type: this.dialogStatus,
          data: {
            groupList: this.formData.groupList,
            id: this.formData.id,
            code: this.formData.code,
            acquisitionType: this.formData.acquisitionType,
            requirementIdList: this.formData.requirementList?.map(val => val.id)
          }
        })
      }
    },
    reloadStep1(row) {
      this.formData = row
    },

    traverse(node) {
      if (!node.children || node.children.length === 0) {
        // 如果是叶子节点并且有 taglist，添加到结果数组中
        if (node.tagList) {
          this.result = this.result.concat(node.tagList)
        }
      } else {
        // 递归遍历子节点
        node.children.forEach(child => this.traverse(child))
      }
    },
    extractTagLists(nodes) {
      this.result = []
      nodes.forEach(node => this.traverse(node))
      return this.result
    },

    saveDialog() {
      this.$emit('reload')
      this.dialogVisible = false
    },

    showTagList(val) {
      this.$emit('show-tag-list', val)
    },
    showTaskRecord(postData) {
      this.$emit('show-task-record', postData)
    },
    toggleFullScreen() {
      this.fullscreen = this.fullscreen ? false : true
    }
  }
}
</script>
<style lang="scss">
.add-task {
  height: 70vh;

  .el-dialog__body {
    height: calc(100% - 51px - 62px);
  }

  .task-body {
    height: calc(100% - 46px);
  }

  &.is-fullscreen {
    height: 100vh;

    .el-dialog__body {
      height: calc(100% - 51px - 62px);
    }
  }
}
</style>
<style lang="scss" scoped>
.add-task {
  .task-step {
    height: 100%;
  }

  .custom-header {
    display: flex;
    justify-content: space-between;

    .title-opt {
      position: absolute;
      right: 0;
      top: 0;

      .title-btn {
        padding: 0;
        width: 30px;
        height: 54px;
        background: 0 0;
        border: none;
        outline: 0;
        cursor: pointer;
        font-size: var(--el-message-close-size, 16px);
      }
    }
  }

  //margin: 0;
  //position: absolute;
  //top: 50%;
  //left: 50%;
  //transform: translate(-50%, -50%);
}
</style>
