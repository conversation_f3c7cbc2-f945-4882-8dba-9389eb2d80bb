<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" fullscreen destroy-on-close>
    <el-row>
      <el-col :span="5">
        <el-card>
          <el-input
            style="margin-bottom: 5px"
            :placeholder="$t('search')"
            v-model="queryParam.name"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
          <el-scrollbar :height="calculatedHeight" ref="navigateScrollBar">
            <el-table
              :data="dbcMessageList"
              ref="navigateMessageTable"
              border
              @row-click="handleRowClick"
              :row-class-name="customRowClassName"
            >
              <el-table-column prop="name" label="Messages" show-overflow-tooltip></el-table-column>
            </el-table>
          </el-scrollbar>
        </el-card>
      </el-col>

      <el-col :span="18" style="margin-left: 15px">
        <el-card>
          <el-row>
            <el-col :span="24">
              <span class="dbcNameClass">{{ dbcName }}</span>
              <el-scrollbar :height="messageHeight" ref="dbcMessageTableScroll">
                <el-table :data="dbcMessageList" border :row-class-name="customRowClassName" @row-click="handleRowClick">
                  <el-table-column prop="name" label="Messages" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="decimalId" label="Decimal ID" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="format" label="Format" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="length" label="Length" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="txNode" label="TX Nodes" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="remark" label="Remark" show-overflow-tooltip></el-table-column>
                </el-table>
              </el-scrollbar>
            </el-col>
            <el-divider />
            <el-col :span="24" style="padding-top: 20px">
              <el-scrollbar :height="signalHeight" ref="signalMessageTableScroll">
                <el-table :data="dbcSignalList" border>
                  <el-table-column prop="name" width="150" label="Name"></el-table-column>
                  <el-table-column prop="type" label="Type"></el-table-column>
                  <el-table-column prop="byteOrder" label="Byte Order"></el-table-column>
                  <el-table-column prop="bitPosition" label="Bit Position"></el-table-column>
                  <el-table-column prop="length" label="Length"></el-table-column>
                  <el-table-column prop="factor" label="factor"></el-table-column>
                  <el-table-column prop="offset" label="offset"></el-table-column>
                  <el-table-column prop="minimum" label="minimum"></el-table-column>
                  <el-table-column prop="maximum" label="maximum"></el-table-column>
                  <el-table-column prop="description" width="300" label="description"></el-table-column>
                </el-table>
              </el-scrollbar>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { listFtmDbcMessage, listFtmDbcSignal } from '@/apis/fleet/ftm-dbc'

const defaultform = {}
export default {
  name: 'FtmDbcDetail',
  emits: ['reload'],
  components: {},
  data() {
    return {
      dbcName: '',
      scrollHeight: 0,
      highlightedRowId: null,
      selectedRow: null,
      queryParam: {
        dbcId: '',
        name: ''
      },
      calculatedHeight: '500px',
      messageHeight: '',
      signalHeight: '',
      dbcMessageList: [],
      dbcSignalList: [],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      $t: i18n.global.t
    }
  },
  computed: {},
  mounted() {
    this.calculateHeight()
    window.addEventListener('resize', this.calculateHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateHeight)
  },
  methods: {
    calculateRowHeight(rowIndex) {
      const table = this.$refs.navigateMessageTable.$el.querySelector('.el-table__body-wrapper')
      if (table) {
        const rows = table.querySelectorAll('.el-table__row')
        if (rows.length > 0) {
          const rowRect = rows[rowIndex].getBoundingClientRect() // 获取第一行的大小及位置信息
          this.scrollHeight = rowRect.height * rowIndex
        }
      }
    },
    handleRowClick(row, column, event) {
      this.selectedRow = row
      this.listFtmDbcSignal(row.id)
      this.highlightedRowId = row.id
      this.calculateRowHeight(row.index)
      this.$refs.dbcMessageTableScroll.setScrollTop(this.scrollHeight)
      this.$refs.navigateScrollBar.setScrollTop(this.scrollHeight)
    },
    customRowClassName({ row, rowIndex }) {
      row.index = rowIndex
      return {
        'highlighted-row': row.id === this.highlightedRowId
      }
    },
    refresh() {
      this.queryInfo()
    },
    calculateHeight() {
      const screenHeight = window.innerHeight
      this.calculatedHeight = `${screenHeight * 0.85}px`
      this.messageHeight = `${screenHeight * 0.4}px`
      this.signalHeight = screenHeight * 0.45 - 40 + 'px'
    },
    show(row) {
      this.queryParam.dbcId = row.id
      this.queryParam.name = ""
      this.dbcName = row.fileName
      this.queryInfo()
      this.dialogVisible = true
    },
    queryInfo() {
      listFtmDbcMessage(this.queryParam).then(res => {
        this.dbcMessageList = res.data
        this.highlightedRowId = res.data[0]?.id
        if (this.dbcMessageList.length > 0) {
          this.listFtmDbcSignal(this.dbcMessageList[0]?.id)
        }
      })
    },
    listFtmDbcSignal(messageId) {
      listFtmDbcSignal({ messageId: messageId }).then(res => {
        this.dbcSignalList = res.data
      })
    }
  }
}
</script>
<style scoped lang="scss">
:deep(.highlighted-row) {
  background-color: #409eff;
}
.dbcNameClass {
  font-weight: bold;
  margin-bottom: 5px;
}
</style>
