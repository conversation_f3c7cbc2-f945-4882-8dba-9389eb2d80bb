/**
 * v-load-more 表格自定义指令加载
 * load-more-total 数据总行数
 * load-more-selector 数据行样式
 */
 import { debounce } from '@/plugins/util'

 export default {
   updated: function (el, binding, vnode, oldVnode) {
     setTimeout(() => {
       // 设置默认溢出显示数量
       var spillDataNum = 0
 
       // 设置隐藏函数
       var timeout = false
       let setRowDisableNone = function (topNum, showRowNum, binding) {
         if (timeout) {
           clearTimeout(timeout)
         }
         timeout = setTimeout(() => {
           binding.value.call(null, topNum, topNum + showRowNum + spillDataNum)
         })
       }
 
       const dataSize = vnode.props['load-more-total']
       const oldDataSize = oldVnode.props['load-more-total']
 
       if (dataSize === oldDataSize) {
         return 
       }
       const loadMoreSelector = vnode.props['load-more-selector']
       const selectWrap = el.querySelector('.el-scrollbar__wrap')
       const reload = vnode.props['reload']
       if(reload){
         selectWrap.scrollTop = 0
       }
 
 
       const selectScrollbarView = selectWrap.querySelector(
         '.el-scrollbar__view'
       )
       const selectRow = selectWrap.querySelector(loadMoreSelector)
       let domcustomDiv = document.querySelector('#customDiv')
       if (!selectRow) {
         domcustomDiv?.remove()
         // domcustomDiv.setAttribute('style', `height: ${0}px;`)
         return
       }
       const rowHeight = selectRow.clientHeight
       let showRowNum = Math.ceil(selectWrap.clientHeight / rowHeight)
       let createElementTRHeight =
         (dataSize - showRowNum - spillDataNum > 0
           ? dataSize - showRowNum - spillDataNum
           : 0) * rowHeight
       if (domcustomDiv) {
         let topPx = selectWrap.scrollTop - spillDataNum * rowHeight
         domcustomDiv.setAttribute(
           'style',
           `height: ${
             createElementTRHeight - topPx > 0
               ? createElementTRHeight - topPx
               : 0
           }px;`
         )
       } else {
         const createElementTR = document.createElement('div')
         createElementTR.setAttribute(
           'style',
           `height: ${createElementTRHeight}px;`
         )
         createElementTR.setAttribute('id', `customDiv`)
         selectScrollbarView.append(createElementTR)
         domcustomDiv = document.querySelector('#customDiv')
       }
 
       // 监听滚动后事件
       selectWrap.onscroll = debounce(function () {
         //
         let topPx = selectWrap.scrollTop - spillDataNum * rowHeight
         //
         let topNum = Math.ceil(topPx / rowHeight)
         let minTopNum = dataSize - spillDataNum - showRowNum
         if (topNum > minTopNum) {
           topNum = minTopNum
         }
         if (topNum < 0) {
           topNum = 0
           topPx = 0
         }
        //  selectScrollbarView.setAttribute(
        //    'style',
        //    `transform: translateY(${
        //      topPx - createElementTRHeight >= 0
        //        ? createElementTRHeight-rowHeight
        //        : topPx - (topPx % rowHeight)
        //    }px)`
        //  )
        selectScrollbarView.setAttribute(
          'style',
          `transform: translateY(${
            topPx - createElementTRHeight >= 0
              ? createElementTRHeight
              : topPx - (topPx % rowHeight)
          }px)`
        )
         domcustomDiv.setAttribute(
           'style',
           `height: ${
             createElementTRHeight - topPx > 0
               ? createElementTRHeight - topPx
               : 0
           }px;transition: all 0.3s;`
         )
         setRowDisableNone(
           topPx % rowHeight ? topNum - 1 : topNum,
           topPx % rowHeight ? showRowNum + 1 : showRowNum,
           binding
         )
        // setRowDisableNone(topNum, showRowNum, binding)
       }, 300)
     })
   }
 }
 