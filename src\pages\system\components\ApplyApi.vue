<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="500px"
    @close="dialogClosed"
    @open="dialogOpened"
    class="apply-api"
  >
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
      :disabled="formReadonly"
    >
      <el-form-item :label="$t('申请级别')" prop="roleId">
        <el-cascader
          clearable
          v-model="form.roleId"
          :options="roleOptions"
          :props="props"
          popper-class="roles"
        />
      </el-form-item>
      <el-form-item :label="$t('申请应用')" prop="applicationId">
        <el-select v-model="form.applicationId">
          <el-option
            v-for="item in applicationOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            id="applicationOptions"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('申请原因')" prop="reason">
        <ltw-input
          v-model="form.reason"
          type="textarea"
          id="reason"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="formReadonly" id="close">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="saveForm" id="save">{{
            $t('保存')
          }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  getApplyLevels,
  saveApiPrivileges,
  getApplications
} from '@/apis/system/api-authorization'
import { showToast } from '@/plugins/util'
export default {
  name: 'ApplyApi',
  emits: ['reload'],
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      formReadonly: false,
      form: {},
      formRules: {
        // code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        roleId: [
          {
            required: true,
            message: this.$t('请选择申请级别'),
            trigger: 'blur'
          }
        ],
        applicationId: [
          {
            required: true,
            message: this.$t('请选择申请应用'),
            trigger: 'blur'
          }
        ]
      },
      props: {
        expandTrigger: 'hover',
        label: 'name',
        value: 'id'
      },
      roleOptions: [],
      applicationOptions: []
    }
  },
  // mounted() {
  //   this.getCharts();
  // },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.formReadonly = row.type === 'view' ? true : false
      if (row.id) {
      } else {
        this.dialogTitle = this.$t('API申请')
        this.form.apiId = row.apiId
      }
      this.getRoleOptions()
      this.getApplications()
    },
    getRoleOptions(id) {
      if (!(this.roleOptions && this.roleOptions.length)) {
        getApplyLevels(id).then(res => {
          res.data.forEach(val => {
            val.name = val.code
          })
          this.roleOptions = res.data
        })
      }
    },
    getApplications(id) {
      if (!(this.applicationOptions && this.applicationOptions.length)) {
        getApplications(id).then(res => {
          this.applicationOptions = res.data
        })
      }
    },
    saveForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        // if (this.form.id) {
        //   saveApiPrivileges(this.form).then(() => {
        //     this.cancel()
        //   })
        // } else {
        let postData = {
          ...this.form,
          roleId:
            this.form.roleId && this.form.roleId[this.form.roleId.length - 1]
        }
        saveApiPrivileges(postData).then(() => {
          showToast('申请成功')
          this.cancel()
        })
        // }
      })
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = {}
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    }
  }
}
</script>
<style scoped lang="scss"></style>
