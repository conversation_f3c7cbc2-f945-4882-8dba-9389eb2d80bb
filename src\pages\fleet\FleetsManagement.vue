<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <el-radio-group @change="filterPageData" v-model="queryParam.useType">
            <el-radio-button v-for="item in useTypeList" :label="item.code" :key="item.code"
              >{{ item.name }}
            </el-radio-button>
          </el-radio-group>
          <div class="search-content">
            <ltw-input
              :placeholder="$t('通过车辆识别代码查询')"
              v-model="queryParam.key"
              clearable
              @clear="refresh"
              class="search-key"
            >
              <template #append>
                <el-button @click="refresh" id="el-icon-search">
                  <ltw-icon icon-code="el-icon-search"></ltw-icon>
                </el-button>
              </template>
            </ltw-input>
            <el-button
              :type="item.buttonStyleType"
              :key="item.id"
              v-for="item in outlineFunctionList"
              @click="executeButtonMethod(item.buttonCode)"
              :id="item.buttonIconCode"
            >
              <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
              {{ $t(item.name) }}
            </el-button>
            <el-dropdown
              @command="handleCommand"
              style="margin-left: 10px"
              class="batch-operate-btn"
              v-if="batchingFunctionList && batchingFunctionList.length > 0"
            >
              <el-button id="batchOperateBtn" type="primary">
                {{ $t('批量操作') }}
                <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :key="item.id"
                    v-for="item in batchingFunctionList"
                    :command="item.buttonCode"
                    :id="item.buttonCode"
                  >
                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                    {{ $t(item.name) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
      <!-- <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container"></div>
        <div class="ltw-tool-container button-group">
        </div>
      </div> -->
      <div class="table-content" v-if="loading">
        <el-row v-for="rowIndex in Math.ceil(pageData?.records?.length / splitNum)" :key="rowIndex" :gutter="20">
          <el-col
            :xs="24"
            :sm="12"
            :md="12"
            :lg="8"
            :xl="6"
            v-for="colIndex in rowIndex === Math.ceil(pageData?.records?.length / splitNum)
              ? pageData?.records?.length % splitNum || splitNum
              : splitNum"
            :key="colIndex"
            style="margin-bottom: 20px"
          >
            <vehicle-card
              ref="VehicleCard"
              :item="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)]"
              :customFunctionList="customFunctionList"
              :inlineFunctionList="inlineFunctionList"
              :vehicleStatuses="vehicleStatuses"
              type="edit"
              @reload="query"
            />
          </el-col>
        </el-row>
      </div>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[8, 12, 24, 36]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
  </div>
  <import-competitor ref="ImportCompetitorRef"></import-competitor>
  <export-competitor ref="ExportCompetitorRef"></export-competitor>
</template>

<script>
import { debounce } from '@/plugins/util'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { pageBsVehicle } from '@/apis/fleet/bs-vehicle'
import VehicleCard from '@/pages/fleet/components/VehicleCard.vue'
import ImportCompetitor from '@/pages/fleet/dialog/ImportCompetitor.vue'
import ExportCompetitor from '@/pages/fleet/dialog/ExportCompetitor.vue'

export default {
  components: {
    VehicleCard,
    ImportCompetitor,
    ExportCompetitor
  },
  name: 'BsVehicle',
  data() {
    return {
      loading: true,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      customFunctionList: [],
      pageData: {
        records: [],
        total: 0
      },
      queryParam: {
        current: 1,
        size: 12
      },
      splitNum: 3,
      vehicleStatuses: [],
      useTypeList: []
    }
  },
  created() {
    const routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.customFunctionList = routerFunctionMap.customFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    const isCollapseMenu = this.$route?.query?.isCollapseMenu,
      showBackButton = this.$route?.query?.showBackButton
    if (isCollapseMenu === '1') {
      this.$store.commit('setCollapsed', isCollapseMenu === '1' ? true : false)
    }
    if (showBackButton === '1') {
      this.$store.commit('setBackButton', showBackButton === '1' ? true : false)
    }
    this.getUseType()
    // this.query()
    // this.listEquipment()
    let _this = this
    window.onresize = debounce(_this.changeSize)
    this.changeSize()
    this.listStatus()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    handleCommand(command) {
      if (command === 'batchCompetitorImport') {
        this.batchCompetitorImport()
      } else if (command === 'batchCompetitorExport') {
        this.batchCompetitorExport()
      }
    },
    batchCompetitorImport() {
      this.$refs.ImportCompetitorRef.show()
    },
    batchCompetitorExport() {
      this.$refs.ExportCompetitorRef.show()
    },
    listStatus() {
      listSysDictionary({ typeCode: 'bs_vehicle_status' }).then(res => {
        this.vehicleStatuses = res.data
      })
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      // this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      this.loading = false
      pageBsVehicle(this.queryParam).then(res => {
        this.pageData = res.data
        this.loading = true
      })
    },
    add() {
      this.$router.push({
        path: '/fleet/AddFleetManagement'
      })
    },
    changeSize() {
      if (window.innerWidth >= 1920) {
        this.splitNum = 4
      } else if (window.innerWidth >= 1200) {
        this.splitNum = 3
      } else if (window.innerWidth >= 992) {
        this.splitNum = 2
      } else if (window.innerWidth >= 768) {
        this.splitNum = 2
      } else {
        this.splitNum = 1
      }
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },

    getUseType() {
      listSysDictionary({ typeCode: 'use_type' }).then(res => {
        this.useTypeList = res.data
        res.data.unshift({ name: '全部', value: '' })
        if (res.data?.length) {
          this.queryParam.useType = res.data[0].code

          this.query()
        }
      })
    },
    filterPageData() {
      this.query()
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="scss" scoped>

// .ltw-toolbar {
//   justify-content: space-between;
// }
// .table-content {
//   display: flex;
//   flex-wrap: wrap;
//   :deep(.vehicle-card) {
//     margin: 0 20px 20px 0;
//   }
// }
.search-key {
  width: 400px;
  margin-right: 10px;
  // margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;

  .search-content {
    display: flex;
    align-items: center;
  }

  .el-radio-group {
    border-radius: 24px;

    .el-radio-button > :deep(.el-radio-button__inner) {
      border: none;

      &:hover {
        color: rgb(16, 41, 106);
      }
    }

    .el-radio-button.is-active > :deep(.el-radio-button__inner) {
      background-color: rgb(16, 41, 106);
      border-color: rgb(16, 41, 106);
      border-radius: 24px;

      &:hover {
        color: white;
      }
    }
  }
}
</style>
