<template>
  <div class="fleets-management-new">
    <el-card shadow="never" class="main-card">
      <!-- 筛选和操作栏 -->
      <div class="filter-action-bar">
        <div class="filter-section">
          <!-- 显示选项 -->
          <!-- <div class="display-options">
            <ltw-icon icon-code="el-icon-van"></ltw-icon>
            <span class="display-label">{{ $t('车辆总数') }}</span>
            <span class="display-count">{{ filteredVehicles.length }}</span>
          </div> -->
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <div class="search-filter-section">
            <div class="search-container">
              <ltw-input
                v-model="searchKeyword"
                :placeholder="$t('请输入车型/车辆编号查找')"
                clearable
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #append>
                  <el-button @click="handleSearch">
                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                  </el-button>
                </template>
              </ltw-input>
            </div>
          </div>
          <el-button type="primary" @click="addNewVehicle" class="add-button" size="small">
            <ltw-icon icon-code="el-icon-plus" class="button-icon"></ltw-icon>
            {{ $t('新增车辆') }}
          </el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
                <div class="brand-filter-section">
            <div class="brand-filter-container">
              <!-- 左侧：已选标签区域 -->
              <div class="selected-tags-area">
                <div class="function-filter">
                  <div
                    class="filter-tag-item"
                    :class="{ selected: selectedUseTypes.length === 0 }"
                    @click="selectAllUseTypes"
                  >
                    全部
                  </div>
                  <!-- 用途选项 -->
                  <div
                    v-for="useType in useTypeList"
                    :key="useType.code"
                    class="filter-tag-item"
                    :class="{ selected: selectedUseTypes.includes(useType.code) }"
                    @click="toggleUseType(useType.code)"
                  >
                    {{ useType.name }}
                  </div>
                </div>
                <div class="selected-tags-wrapper">
                  <!-- 品牌标签 -->
                  <template v-if="selectedBrands.length > 0">
                    <span class="tag-category-label">供应商:</span>
                    <el-tag
                      v-for="brandCode in selectedBrands"
                      :key="'brand-' + brandCode"
                      class="selected-tag brand-tag"
                      closable
                      @close="removeBrand(brandCode)"
                    >
                      {{ getBrandName(brandCode) }}
                    </el-tag>
                  </template>

                  <!-- 车型标签 -->
                  <template v-if="selectedVehicleTypes.length > 0">
                    <span class="tag-category-label">车型:</span>
                    <el-tag
                      v-for="typeCode in selectedVehicleTypes"
                      :key="'type-' + typeCode"
                      class="selected-tag type-tag"
                      closable
                      @close="removeVehicleType(typeCode)"
                    >
                      {{ getVehicleTypeName(typeCode) }}
                    </el-tag>
                  </template>

                  <!-- 状态标签 -->
                  <template v-if="selectedVehicleStatus.length > 0">
                    <span class="tag-category-label">车辆状态:</span>
                    <el-tag
                      v-for="statusCode in selectedVehicleStatus"
                      :key="'status-' + statusCode"
                      class="selected-tag status-tag"
                      closable
                      @close="removeVehicleStatus(statusCode)"
                    >
                      {{ getVehicleStatusName(statusCode) }}
                    </el-tag>
                  </template>

                  <!-- 用途标签 -->
                  <!-- <template v-if="selectedUseTypes.length > 0">
                    <span class="tag-category-label">功能分类:</span>
                    <el-tag
                      v-for="useTypeCode in selectedUseTypes"
                      :key="'use-' + useTypeCode"
                      class="selected-tag use-tag"
                      closable
                      @close="removeUseType(useTypeCode)"
                    >
                      {{ getUseTypeName(useTypeCode) }}
                    </el-tag>
                  </template> -->
                </div>
              </div>

              <!-- 右侧：展开按钮 -->
              <div class="expand-button-area">
                <div class="expand-info" @click="toggleBrandPanel">
                  {{ showBrandPanel ? '收起所有选项' : '展开所有选项' }}
                  <svg
                    class="arrow-icon"
                    :class="{ rotate: showBrandPanel }"
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M3 4.5L6 7.5L9 4.5"
                      stroke="currentColor"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>

              <!-- 品牌选择展开面板 -->
              <div v-if="showBrandPanel" class="brand-selection-dropdown">
                <div class="brand-selection-panel">
                  <!-- 品牌分类 -->
                  <div class="filter-category">
                    <div class="category-title">供应商 ({{ tempSelectedBrands.length }}/{{ brandList.length }})</div>
                    <div class="filter-tags">
                      <!-- 全部选项 -->
                      <div
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedBrands.length === 0 }"
                        @click="selectAllBrandsTemp"
                      >
                        全部
                      </div>
                      <!-- 品牌选项 -->
                      <div
                        v-for="brand in brandList"
                        :key="brand.code"
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedBrands.includes(brand.code) }"
                        @click="toggleBrandTemp(brand.code)"
                      >
                        {{ brand.name }}
                      </div>
                    </div>
                  </div>

                  <!-- 车型分类 -->
                  <div class="filter-category">
                    <div class="category-title">
                      车型 ({{ tempSelectedVehicleTypes.length }}/{{ vehicleTypeOptions.length }})
                    </div>
                    <div class="filter-tags">
                      <!-- 全部选项 -->
                      <div
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedVehicleTypes.length === 0 }"
                        @click="selectAllVehicleTypesTemp"
                      >
                        全部
                      </div>
                      <!-- 车型选项 -->
                      <div
                        v-for="vehicleType in vehicleTypeOptions"
                        :key="vehicleType.code"
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedVehicleTypes.includes(vehicleType.code) }"
                        @click="toggleVehicleTypeTemp(vehicleType.code)"
                      >
                        {{ vehicleType.name }}
                      </div>
                    </div>
                  </div>

                  <!-- 车辆状态分类 -->
                  <div class="filter-category">
                    <div class="category-title">
                      车辆状态 ({{ tempSelectedVehicleStatus.length }}/{{ vehicleStatusOptions.length }})
                    </div>
                    <div class="filter-tags">
                      <!-- 全部选项 -->
                      <div
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedVehicleStatus.length === 0 }"
                        @click="selectAllVehicleStatusTemp"
                      >
                        全部
                      </div>
                      <!-- 状态选项 -->
                      <div
                        v-for="status in vehicleStatusOptions"
                        :key="status.code"
                        class="filter-tag-item"
                        :class="{ selected: tempSelectedVehicleStatus.includes(status.code) }"
                        @click="toggleVehicleStatusTemp(status.code)"
                      >
                        {{ status.name }}
                      </div>
                    </div>
                  </div>

                  <!-- 用途分类
              <div class="filter-category">
                <div class="category-title">功能分类 ({{ tempSelectedUseTypes.length }}/{{ useTypeList.length }})</div>
                <div class="filter-tags">
                     全部选项 -->
                  <!-- <div
                    class="filter-tag-item"
                    :class="{ selected: tempSelectedUseTypes.length === 0 }"
                    @click="selectAllUseTypesTemp"
                  >
                    全部
                  </div> -->
                  <!-- 用途选项 
                  <div
                    v-for="useType in useTypeList"
                    :key="useType.code"
                    class="filter-tag-item"
                    :class="{ selected: tempSelectedUseTypes.includes(useType.code) }"
                    @click="toggleUseTypeTemp(useType.code)"
                  >
                    {{ useType.name }}
                  </div>
                </div>
              </div> -->

                  <!-- 底部按钮 -->
                  <div class="panel-footer">
                    <el-button class="cancel-btn" @click="cancelBrandSelection">取消</el-button>
                    <el-button class="confirm-btn" @click="confirmBrandSelection">确定</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
      <div class="main-content-vehicle" :class="{ 'with-drawer': showDetailDrawer }">
        <!-- 内容卡片 -->
        
        <!-- 品牌筛选标签 -->
        <div
          class="main-container-vehicle"
          :class="{ shrink: showDetailDrawer }"
          :style="showDetailDrawer ? { width: `calc(100% - ${drawerWidth + 16}px)` } : {}"
        >

          <el-card class="content-card" :class="{ 'with-drawer': showDetailDrawer }" shadow="hover">
            <!-- 车辆卡片展示区域 -->
            <div class="vehicles-content">
              <!-- 始终保持容器存在，用于 ResizeObserver 监听 -->
              <div
                ref="vehiclesContainer"
                class="vehicles-list"
                @click.self="clearSelection"
                v-infinite-scroll="loadMore"
                :infinite-scroll-disabled="infiniteScrollDisabled"
                :infinite-scroll-distance="200"
                :infinite-scroll-delay="300"
              >
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                  <el-skeleton :rows="3" animated />
                </div>
                <!-- 空数据状态 -->
                <div v-else-if="filteredVehicles.length === 0" class="empty-container">
                  <el-empty :description="$t('暂无数据')" />
                </div>
                <!-- 车辆列表 -->
                <template v-else>
                  <!-- 使用flex布局实现精确的5列 -->
                  <div class="vehicles-flex-container" :class="{ 'with-drawer': showDetailDrawer }">
                    <div
                      v-for="vehicle in filteredVehicles"
                      :key="vehicle.id"
                      class="vehicle-flex-item"
                      :style="vehicleCardStyle"
                    >
                      <vehicle-card-test-design
                        :vehicle="vehicle"
                        :selected="selectedVehicleId === vehicle.id"
                        @detail="handleVehicleDetail"
                        @menu-click="handleVehicleMenu"
                        @reload="handleVehicleReload"
                        class="vehicle-card-item"
                      />
                    </div>
                  </div>

                  <!-- 滚动加载状态提示 -->
                  <div v-if="filteredVehicles.length > 0" class="infinite-scroll-status">
                    <div v-if="loadingMore" class="loading-more">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>加载中...</span>
                    </div>
                    <div v-else-if="!hasMore" class="no-more-data">
                      <span>已加载全部数据</span>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 编辑抽屉 -->
        <div v-if="showDetailDrawer" class="edit-drawer-fleet" :style="{ width: drawerWidth + 'px' }">
          <!-- 拖拽手柄 -->
          <div
            class="drawer-resize-handle"
            @mousedown="startDrag"
          ></div>
          <div class="drawer-container">
            <!-- 抽屉头部 -->
            <div class="drawer-header">
              <div class="drawer-title">
                <span>{{selectedVehicle.vin}}</span>
              </div>
              <div class="drawer-close" @click="closeDrawer">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M15 5L5 15M5 5L15 15" stroke="#666" stroke-width="1.5" stroke-linecap="round" />
                </svg>
              </div>
            </div>

            <!-- 抽屉内容 -->
            <div class="drawer-content">
              <el-tabs v-model="activeTab" class="vehicle-tabs">
                <el-tab-pane label="基本信息" name="basic">
                  <vehicle-basic-info
                    v-if="selectedVehicle"
                    ref="vehicleBasicInfo"
                    :vehicle="selectedVehicle"
                    @update-vehicle="handleUpdatedVehicle"
                  />
                </el-tab-pane>
                <el-tab-pane label="装车记录" name="installation">
                  <vehicle-installation-records
                    v-if="selectedVehicle"
                    ref="vehicleInstallationRecords"
                    :vehicle-data="selectedVehicle"
                    @published="handleReloadPublish"
                  />
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 新增车辆对话框 -->
    <AddVehicleDialog ref="addVehicleDialog" @finish="handleVehicleAdded" />
  </div>
</template>

<script>
import VehicleCardTestDesign from './components/VehicleCardTestDesign.vue'
import AddVehicleDialog from './components/AddVehicleDialog.vue'
import VehicleDetailDrawer from './components/VehicleDetailDrawer.vue'
import VehicleBasicInfo from './components/VehicleBasicInfo.vue'
import VehicleInstallationRecords from './components/VehicleInstallationRecords.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import { listBsVehicleSelection, getBsVehicle, pageBsVehicle} from '@/apis/fleet/bs-vehicle'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { Loading } from '@element-plus/icons-vue'
export default {
  components: {
    VehicleCardTestDesign,
    AddVehicleDialog,
    VehicleDetailDrawer,
    VehicleBasicInfo,
    VehicleInstallationRecords,
    LtwIcon,
    LtwInput,
    Loading
  },
  name: 'FleetsManagementNew',
  data() {
    return {
      loading: true,
      searchKeyword: '',
      selectedBrands: [], // 选中的品牌
      selectedVehicleTypes: [], // 选中的车型
      vehicleTypeOptions: [], // 车型选项
      selectedVehicleStatus: [], // 选中的车辆状态
      vehicleStatusOptions: [
        { code: 'working', name: 'working' },
        { code: 'free', name: 'free' },
        { code: 'distributed', name: 'distributed' }
      ], // 车辆状态选项
      selectedUseTypes: [], // 选中的用途
      useTypeList: [], // 用途列表
      vehicleList: [],
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      loadingMore: false,
      hasMore: true,
      containerWidth: 0,
      resizeObserver: null,
      showAddVehicleDialog: false,
      showDetailDrawer: false,
      selectedVehicleId: null,
      selectedVehicle: null,
      activeTab: 'basic',
      showBrandPanel: false,
      tempSelectedBrands: [],
      tempSelectedVehicleTypes: [],
      tempSelectedVehicleStatus: [],
      tempSelectedUseTypes: [],
      brandList: [],
      drawerWidth: 752,
      minDrawerWidth: 700, // 最小抽屉宽度（减小最小宽度）
      isDragging: false,
      dragStartX: 0,
      dragStartWidth: 0,
      resizeTimer: null
    }
  },
  computed: {
    filteredVehicles() {
      return this.vehicleList
    },
    infiniteScrollDisabled() {
      return this.loading || this.loadingMore || !this.hasMore
    },
    mainContainerWidth() {
      if (!this.showDetailDrawer) {
        return '100%'
      }
      return `calc(100% - ${this.drawerWidth + 16}px)`
    },
    maxDrawerWidth() {
      const minLeftContentWidth = 620
      const containerPadding = 16
      return Math.min(
        window.innerWidth * 0.7,
        window.innerWidth - minLeftContentWidth - containerPadding
      )
    },
    vehicleCardStyle() {
      const containerWidth = this.containerWidth || window.innerWidth
      const padding = 32
      const gap = 20
      const availableWidth = containerWidth - padding

      const getCardConfig = (width) => {
        if (width >= 1500) {
          return { columns: 5, minWidth: 240, percentage: 20 }
        } else if (width >= 1100) {
          return { columns: 4, minWidth: 220, percentage: 25 }
        } else if (width >= 800) {
          return { columns: 3, minWidth: 220, percentage: 33.333 }
        } else if (width >= 460) {
          return { columns: 2, minWidth: 220, percentage: 50 }
        } else {
          return { columns: 1, minWidth: 220, percentage: 100 }
        }
      }

      const config = getCardConfig(availableWidth)
      const actualColumns = Math.max(1, Math.floor((availableWidth + gap) / (config.minWidth + gap)))
      const finalColumns = Math.min(config.columns, actualColumns)
      const finalPercentage = 100 / finalColumns

      return {
        flex: `0 0 calc(${finalPercentage}% - ${(finalColumns - 1) * gap / finalColumns}px)`,
        minWidth: `${config.minWidth}px`,
        maxWidth: `calc(${finalPercentage}% - ${(finalColumns - 1) * gap / finalColumns}px)`
      }
    }
  },

  watch: {
    activeTab(newTab) {
      if (newTab === 'installation') {
        this.$nextTick(() => {
          if (this.$refs.vehicleInstallationRecords && this.$refs.vehicleInstallationRecords.resetSidebarState) {
            this.$refs.vehicleInstallationRecords.resetSidebarState()
          }
        })
      }
    }
  },

  created() {
    this.loadData()
    this.initDrawerWidth()
  },

  mounted() {
    document.addEventListener('click', this.handleClickOutside)
    window.addEventListener('resize', this.handleWindowResize)
    this.initContainerWidthObserver()
  },

  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
    window.removeEventListener('resize', this.handleWindowResize)

    if (this.isDragging) {
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.body.style.userSelect = ''
    }

    this.destroyContainerWidthObserver()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([this.loadVehicles(), this.loadAllVehiclesForBrands(), this.getVehicleType(), this.getUseTypes()])
      } finally {
        this.loading = false
      }
    },
    async loadVehicles() {
      try {
        this.pagination.current = 1
        this.hasMore = true

        const res = await pageBsVehicle({
          current: 1,
          size: this.pagination.pageSize
        })

        this.vehicleList = res.data?.records || []
        this.pagination.total = res.data?.total || 0

        this.hasMore = this.vehicleList.length < this.pagination.total
      } catch (error) {
        // 静默处理错误
      }
    },
    async loadAllVehiclesForBrands() {
      try {
        const res = await listBsVehicleSelection()
        this.brandList = this.getBrandListFromAllVehicles(res.data || [])
      } catch (error) {
        // 静默处理错误
      }
    },
    getBrandListFromAllVehicles(allVehicles) {
      if (!allVehicles || allVehicles.length === 0) {
        return []
      }

      // 从全量车辆数据中提取唯一的品牌信息
      const brandMap = new Map()
      allVehicles.forEach(vehicle => {
        const supplierId = vehicle.supplierId
        const supplierName = vehicle.supplierName

        // 只有当supplierId和supplierName都有值时才添加到品牌列表
        if (supplierId && supplierName) {
          if (!brandMap.has(supplierId)) {
            brandMap.set(supplierId, {
              code: supplierId,
              name: supplierName
            })
          }
        }
      })

      // 转换为数组并排序
      return Array.from(brandMap.values()).sort((a, b) => {
        return a.name.localeCompare(b.name)
      })
    },

    // 带参数加载车辆数据
    async loadVehiclesWithParams(isLoadMore = false) {
      if (isLoadMore) {
        this.loadingMore = true
      } else {
        this.loading = true
        // 重置状态
        this.pagination.current = 1
        this.hasMore = true
      }

      try {
        const params = {
          // 分页参数
         current: this.pagination.current,
         size: this.pagination.pageSize
        }

        // 添加搜索关键词参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.key = this.searchKeyword.trim()
        }

        // 供应商
        if (this.selectedBrands.length > 0) {
          params.supplierIdList = this.selectedBrands.join(',')
        }
        //用途
        if (this.selectedUseTypes.length > 0) {
          params.useTypeList = this.selectedUseTypes.join(',')
        }
        //车辆状态
        if (this.selectedVehicleStatus.length > 0) {
          params.vehicleStatusList = this.selectedVehicleStatus.join(',')
        }
        //车型
        if (this.selectedVehicleTypes.length > 0) {
          params.vehicleTypeList = this.selectedVehicleTypes.join(',')
        }

        const res = await pageBsVehicle(params)
        const newData = res.data?.records || []

        if (isLoadMore) {
          // 追加数据
          this.vehicleList = [...this.vehicleList, ...newData]
        } else {
          // 替换数据
          this.vehicleList = newData
        }

        this.pagination.total = res.data?.total || 0

        // 检查是否还有更多数据
        this.hasMore = this.vehicleList.length < this.pagination.total

        // 强制更新组件
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } catch (error) {
        // 静默处理错误
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },

    // 加载车型数据
    async getVehicleType() {
      try {
        const res = await listSysDictionary({ typeCode: 'variant_type' })
        this.vehicleTypeOptions = res.data || []
      } catch (error) {
        // 静默处理错误
      }
    },

    // 加载用途数据
    async getUseTypes() {
      try {
        const res = await listSysDictionary({ typeCode: 'use_type' })
        this.useTypeList = res.data || []
      } catch (error) {
        // 静默处理错误
      }
    },

    // 处理车辆信息更新
    handleUpdatedVehicle(updatedVehicleData) {
      // 立即更新选中的车辆数据，确保抽屉中显示最新信息
      this.selectedVehicle = updatedVehicleData

      // 重新查询数据以确保数据同步
      this.loadVehicles()
    },

    // 搜索处理
    handleSearch() {
      this.showDetailDrawer = false
      this.loadVehiclesWithParams()
    },
    handleReloadPublish() {
      this.loadVehiclesWithParams()
    },
    // 选择全部（清空所有筛选）
    selectAllBrands() {
      this.selectedBrands = []
      this.selectedVehicleTypes = []
      this.selectedVehicleStatus = []
      this.selectedUseTypes = []
      this.loadVehiclesWithParams()
    },
    // 切换品牌选择
    toggleBrand(brandCode) {
      const index = this.selectedBrands.indexOf(brandCode)
      if (index > -1) {
        this.selectedBrands.splice(index, 1)
      } else {
        this.selectedBrands.push(brandCode)
      }
      this.loadVehiclesWithParams()
    },

    // 根据品牌代码获取品牌名称
    getBrandName(brandCode) {
      const brand = this.brandList.find(b => b.code === brandCode)
      return brand ? brand.name : '未知品牌'
    },

    // 根据车型代码获取车型名称
    getVehicleTypeName(typeCode) {
      const vehicleType = this.vehicleTypeOptions.find(t => t.code === typeCode)
      return vehicleType ? vehicleType.name : '未知车型'
    },

    // 根据状态代码获取状态名称
    getVehicleStatusName(statusCode) {
      const status = this.vehicleStatusOptions.find(s => s.code === statusCode)
      return status ? status.name : '未知状态'
    },

    // 根据用途代码获取用途名称
    getUseTypeName(useTypeCode) {
      const useType = this.useTypeList.find(u => u.code === useTypeCode)
      return useType ? useType.name : '未知用途'
    },

    // 移除单个品牌标签
    removeBrand(brandCode) {
      const index = this.selectedBrands.indexOf(brandCode)
      if (index > -1) {
        this.selectedBrands.splice(index, 1)
        this.loadVehiclesWithParams()
      }
    },

    // 移除车型
    removeVehicleType(typeCode) {
      const index = this.selectedVehicleTypes.indexOf(typeCode)
      if (index > -1) {
        this.selectedVehicleTypes.splice(index, 1)
        this.loadVehiclesWithParams()
      }
    },

    // 移除车辆状态
    removeVehicleStatus(statusCode) {
      const index = this.selectedVehicleStatus.indexOf(statusCode)
      if (index > -1) {
        this.selectedVehicleStatus.splice(index, 1)
        this.loadVehiclesWithParams()
      }
    },

    // 移除用途
    removeUseType(useTypeCode) {
      const index = this.selectedUseTypes.indexOf(useTypeCode)
      if (index > -1) {
        this.selectedUseTypes.splice(index, 1)
        this.loadVehiclesWithParams()
      }
    },

    // 选择全部用途（清空用途筛选）
    selectAllUseTypes() {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      this.selectedUseTypes = []
      this.loadVehiclesWithParams()
    },

    // 切换用途选择
    toggleUseType(useTypeCode) {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      const index = this.selectedUseTypes.indexOf(useTypeCode)
      if (index > -1) {
        this.selectedUseTypes.splice(index, 1)
      } else {
        this.selectedUseTypes.push(useTypeCode)
      }
      this.loadVehiclesWithParams()
    },

    // Element Plus 无限滚动加载方法
    async loadMore() {
      if (!this.hasMore || this.loadingMore || this.loading) {
        return
      }

      this.pagination.current += 1
      await this.loadVehiclesWithParams(true)
    },

    // 初始化容器宽度监听
    initContainerWidthObserver() {
      setTimeout(() => {
          const container = this.$refs.vehiclesContainer
        if (container && 'ResizeObserver' in window) {
          this.containerWidth = container ? container.clientWidth : window.innerWidth

          // 使用 ResizeObserver 监听容器尺寸变化
          this.resizeObserver = new ResizeObserver(() => {
            // 在监听回调中使用相同的取值逻辑
            const container = this.$refs.vehiclesContainer
            this.containerWidth = container ? container.clientWidth : window.innerWidth
          })
          this.resizeObserver.observe(container)
        }
      }, 100)
    },

    // 销毁容器宽度监听
    destroyContainerWidthObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }
    },

    // 根据屏幕大小初始化抽屉宽度
    initDrawerWidth() {
      const screenWidth = window.innerWidth

      if (screenWidth <= 1366) {
        // 小笔记本屏幕（1366px及以下）
        this.drawerWidth = 700
      } else if (screenWidth <= 1600) {
        // 中等笔记本屏幕（1367-1600px）
        this.drawerWidth = 700
      } else if (screenWidth <= 1920) {
        // 大笔记本/小台式机屏幕（1601-1920px）
        this.drawerWidth = 752
      } else {
        // 大屏幕（>1920px）
        this.drawerWidth = 752
      }

      // 确保不超过最大限制
      if (this.drawerWidth > this.maxDrawerWidth) {
        this.drawerWidth = this.maxDrawerWidth
      }
    },

    // 抽屉拖拽相关方法
    startDrag(event) {
      this.isDragging = true
      this.dragStartX = event.clientX
      this.dragStartWidth = this.drawerWidth

      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)

      // 防止文本选择和添加拖拽样式
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'ew-resize'
      document.body.classList.add('drawer-dragging')
      event.preventDefault()
    },

    onDrag(event) {
      if (!this.isDragging) return

      // 计算新宽度（向左拖拽增加宽度）
      const deltaX = this.dragStartX - event.clientX
      const newWidth = this.dragStartWidth + deltaX

      if (newWidth >= this.minDrawerWidth && newWidth <= this.maxDrawerWidth) {
        this.drawerWidth = newWidth
        // 触发重新计算以更新车辆卡片布局
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    },

    stopDrag() {
      this.isDragging = false

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)

      // 恢复样式
      document.body.style.userSelect = ''
      document.body.style.cursor = ''
      document.body.classList.remove('drawer-dragging')
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 防抖处理抽屉相关逻辑，避免频繁触发
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        if (this.showDetailDrawer) {
          // 确保抽屉宽度不超过新的最大限制
          if (this.drawerWidth > this.maxDrawerWidth) {
            this.drawerWidth = this.maxDrawerWidth
          }
        }
        // ResizeObserver 会自动处理容器宽度变化
      }, 100)
    },

    // 切换筛选面板
    toggleBrandPanel() {
      if (this.showBrandPanel) {
        // 如果面板已打开，则关闭
        this.closeBrandPanel()
      } else {
        // 如果面板已关闭，则打开
        this.openBrandPanel()
      }
    },

    // 打开筛选面板
    openBrandPanel() {
      // 初始化所有临时选择
      this.tempSelectedBrands = [...this.selectedBrands]
      this.tempSelectedVehicleTypes = [...this.selectedVehicleTypes]
      this.tempSelectedVehicleStatus = [...this.selectedVehicleStatus]
      this.tempSelectedUseTypes = [...this.selectedUseTypes]

      this.showBrandPanel = true

      // 添加点击外部关闭事件
      this.$nextTick(() => {
        document.addEventListener('click', this.handleClickOutside)
      })
    },

    // 关闭筛选面板
    closeBrandPanel() {
      this.showBrandPanel = false
      // 移除点击外部关闭事件
      document.removeEventListener('click', this.handleClickOutside)
    },

    // 面板中的临时选择全部品牌
    selectAllBrandsTemp() {
      this.tempSelectedBrands = []
    },

    // 面板中的临时切换品牌选择
    toggleBrandTemp(brandCode) {
      const index = this.tempSelectedBrands.indexOf(brandCode)
      if (index > -1) {
        this.tempSelectedBrands.splice(index, 1)
      } else {
        this.tempSelectedBrands.push(brandCode)
      }
    },

    // 面板中的临时选择全部车型
    selectAllVehicleTypesTemp() {
      this.tempSelectedVehicleTypes = []
    },

    // 面板中的临时切换车型选择
    toggleVehicleTypeTemp(typeCode) {
      const index = this.tempSelectedVehicleTypes.indexOf(typeCode)
      if (index > -1) {
        this.tempSelectedVehicleTypes.splice(index, 1)
      } else {
        this.tempSelectedVehicleTypes.push(typeCode)
      }
    },

    // 面板中的临时选择全部车辆状态
    selectAllVehicleStatusTemp() {
      this.tempSelectedVehicleStatus = []
    },

    // 面板中的临时切换车辆状态选择
    toggleVehicleStatusTemp(statusCode) {
      const index = this.tempSelectedVehicleStatus.indexOf(statusCode)
      if (index > -1) {
        this.tempSelectedVehicleStatus.splice(index, 1)
      } else {
        this.tempSelectedVehicleStatus.push(statusCode)
      }
    },

    // 面板中的临时选择全部用途
    selectAllUseTypesTemp() {
      this.tempSelectedUseTypes = []
    },

    // 面板中的临时切换用途选择
    toggleUseTypeTemp(useTypeCode) {
      const index = this.tempSelectedUseTypes.indexOf(useTypeCode)
      if (index > -1) {
        this.tempSelectedUseTypes.splice(index, 1)
      } else {
        this.tempSelectedUseTypes.push(useTypeCode)
      }
    },

    // 点击外部关闭面板
    handleClickOutside(event) {
      if (this.showBrandPanel) {
        const panel = this.$el.querySelector('.brand-selection-dropdown')
        const button = this.$el.querySelector('.expand-button-area')

        if (panel && !panel.contains(event.target) && button && !button.contains(event.target)) {
          this.cancelBrandSelection()
        }
      }
    },

    // 取消筛选选择
    cancelBrandSelection() {
      // 重置所有临时选择为当前选择
      this.tempSelectedBrands = [...this.selectedBrands]
      this.tempSelectedVehicleTypes = [...this.selectedVehicleTypes]
      this.tempSelectedVehicleStatus = [...this.selectedVehicleStatus]
      this.tempSelectedUseTypes = [...this.selectedUseTypes]

      // 关闭面板
      this.closeBrandPanel()
    },

    // 确认筛选选择
    confirmBrandSelection() {
      // 关闭抽屉
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      // 应用所有筛选选择
      this.selectedBrands = [...this.tempSelectedBrands]
      this.selectedVehicleTypes = [...this.tempSelectedVehicleTypes]
      this.selectedVehicleStatus = [...this.tempSelectedVehicleStatus]
      this.selectedUseTypes = [...this.tempSelectedUseTypes]

      // 关闭面板
      this.closeBrandPanel()
      this.loadVehiclesWithParams()
    },
    // 添加新车辆
    addNewVehicle() {
      this.$refs.addVehicleDialog.openDialog()
    },

    // 处理车辆添加完成
    handleVehicleAdded() {
      // 重新加载车辆列表
      this.loadVehicles()
    },

    // 处理车辆重新加载（删除后）
    handleVehicleReload() {
      // 如果当前有打开的抽屉，需要关闭它
      if (this.showDetailDrawer) {
        this.closeDrawer()
      }

      // 重新加载车辆数据
      this.loadVehiclesWithParams()
    },

    // 处理车型详情点击
    handleVehicleDetail(vehicle) {
      this.selectedVehicleId = vehicle.id

      // 确保进入view模式，清除之前的表单校验
      this.resetDrawerState()

      // 通过接口获取车辆详细信息
      getBsVehicle(vehicle.id)
        .then(res => {
          this.selectedVehicle = res.data
          this.showDetailDrawer = true

          // 确保子组件进入view模式
          this.$nextTick(() => {
            this.ensureViewMode()
          })
        })
        .catch(error => {
          // 静默处理错误
        })
    },

    // 重置抽屉状态
    resetDrawerState() {
      // 重置tab到基本信息
      this.activeTab = 'basic'
    },

    // 确保所有子组件进入view模式
    ensureViewMode() {
      // 确保VehicleBasicInfo组件进入view模式并清除表单校验
      const vehicleBasicInfoRef = this.$refs.vehicleBasicInfo
      if (vehicleBasicInfoRef) {
        // 如果组件正在编辑状态，退出编辑模式
        if (vehicleBasicInfoRef.isEditing) {
          vehicleBasicInfoRef.cancelEdit()
        }
        // 清除表单校验
        if (vehicleBasicInfoRef.$refs.vehicleForm) {
          vehicleBasicInfoRef.$refs.vehicleForm.clearValidate()
        }
      }

      // 如果有其他需要重置的子组件，也可以在这里处理
      const vehicleInstallationRecordsRef = this.$refs.vehicleInstallationRecords
      if (vehicleInstallationRecordsRef && vehicleInstallationRecordsRef.exitAllEditModes) {
        vehicleInstallationRecordsRef.exitAllEditModes()
      }
    },

    // 处理车型菜单点击
    handleVehicleMenu(vehicle) {
      // 可以在这里添加菜单相关的逻辑，比如显示下拉菜单
      // 暂时也打开详情抽屉
      this.selectedVehicleId = vehicle.id
      this.selectedVehicle = vehicle
      this.showDetailDrawer = true
    },

    // 处理编辑车辆提交
    handleEditVehicleSubmit() {
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.$message.success('车型保存成功')
      this.loadVehiclesWithParams()
    },

    // 清除选中状态
    clearSelection() {
      this.selectedVehicleId = null
    },

    // 从抽屉中删除车辆
    handleDeleteFromDrawer() {
      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.loadVehiclesWithParams()
    },

    // 处理新增车型提交
    handleAddVehicleSubmit() {
      this.$message.success('车型保存成功')
      this.loadVehiclesWithParams()
    },

    // 关闭抽屉
    closeDrawer() {
      // 在关闭前确保退出所有编辑模式并清除表单校验
      this.ensureViewMode()

      this.showDetailDrawer = false
      this.selectedVehicleId = null
      this.selectedVehicle = null

      // 重置tab状态
      this.activeTab = 'basic'

      // 重置抽屉宽度
      this.drawerWidth = this.minDrawerWidth
    },

    // 获取能源类型名称
    getEnergyTypeName(energyType) {
      const energyMap = {
        1: '纯电',
        2: '燃油',
        3: '混动'
      }
      return energyMap[energyType] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.fleets-management-new {
  height: calc(100vh - 120px);
  background-color: #f5f7fa;
  font-family: 'Bosch Sans Global', sans-serif;

  .main-card {
    height: 100%;
    border-radius: 8px;
    border: 1px solid #eff1f2;

    :deep(.el-card__header) {
      padding: 20px ;
      border-bottom: 1px solid #eff1f2;
    }

    :deep(.el-card__body) {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .search-filter-section {
    .search-container {
      display: flex;

      .search-input {
        width: 400px;
      }
    }
  }

  .filter-action-bar {
    padding: 12px 16px;
    border-bottom: 1px solid #eff1f2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;

    .filter-section {
      display: flex;
      align-items: center;

      .display-options {
        display: flex;
        align-items: center;

        .display-label {
          color: #4e5256;
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
        }
        .display-count {
          color: blue;
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
          padding-left: 6px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      .add-button {
        background: #5755ff;
        border: 1px solid #5755ff;
        border-radius: 2px;
        color: white;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        padding: 7px 15px;
        height: 32px;

        .button-icon {
          margin-right: 4px;
        }

        &:hover {
          background: #4644dd;
          border-color: #4644dd;
        }
      }
    }
  }

  // 品牌筛选区域
  .brand-filter-section {
    padding: 8px 20px;
    border-bottom: 1px solid #eff1f2;
    background-color: white;

    .brand-filter-container {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      position: relative;

      .selected-tags-area {
        flex: 1;
        min-height: 32px;
        display:flex;
        align-items:center;
        gap:10px;

        .function-filter {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .filter-tag-item {
            padding: 6px 11px;
           
            border-radius: 2px;
            background-color: #fff;
            color: #666;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            cursor: pointer;

            &.selected {
border: 1px solid  #DDF;
background:  #F5F5FF;
            color:#5755FF;
            }
          }
        }

        .selected-tags-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;
          padding-left: 10px;
          border-left:1px #DCDFE6 solid;

          .tag-category-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            margin-right: 4px;
            white-space: nowrap;
          }

          .selected-tag {
            margin: 0 !important;
            border-radius: 2px !important;
            font-size: 12px !important;
            line-height: 20px !important;
            padding: 2px 10px !important;
            border: 1px solid !important;

            // 品牌标签 - 橙色
            &.brand-tag {
              background-color: #ffefd1 !important;
              border-color: #ffdf95 !important;
              color: #e47a01 !important;

              :deep(.el-tag__close) {
                color: #e47a01 !important;
                &:hover {
                  background-color: #e47a01 !important;
                  color: #ffefd1 !important;
                }
              }
            }

            // 车型标签 - 绿色
            &.type-tag {
              background-color: #e2f5e7 !important;
              border-color: #9be4b3 !important;
              color: #32c56a !important;

              :deep(.el-tag__close) {
                color: #32c56a !important;
                &:hover {
                  background-color: #32c56a !important;
                  color: #e2f5e7 !important;
                }
              }
            }

            // 状态标签 - 蓝色
            &.status-tag {
              background-color: #f5f5ff !important;
              border-color: #ddddff !important;
              color: #5755ff !important;

              :deep(.el-tag__close) {
                color: #5755ff !important;
                &:hover {
                  background-color: #5755ff !important;
                  color: #f5f5ff !important;
                }
              }
            }

            // 用途标签 - 紫色
            &.use-tag {
              background-color: #f7eef6 !important;
              border-color: #ebcae8 !important;
              color: #c535bc !important;

              :deep(.el-tag__close) {
                color: #c535bc !important;
                &:hover {
                  background-color: #c535bc !important;
                  color: #f7eef6 !important;
                }
              }
            }
          }
        }
      }

      .expand-button-area {
        flex-shrink: 0;
        .expand-info {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #656a6f;
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          line-height:32px;

          .arrow-icon {
            transition: transform 0.3s ease;

            &.rotate {
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }

  // 品牌选择下拉面板样式
  .brand-selection-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 50%;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 8px;

    // 向上的小三角（右侧位置）
    &::before {
      content: '';
      position: absolute;
      top: -8px;
      right: 20px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #e4e7ed;
    }

    &::after {
      content: '';
      position: absolute;
      top: -7px;
      right: 20px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #fff;
    }
  }

  // 筛选选择面板样式
  .brand-selection-panel {
    padding: 16px;

    .filter-category {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-title {
        font-size: 12px;
        font-weight: 400;
        color: #232628;
        margin-bottom: 16px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 16px;
        line-height: 12px;
      }

      .filter-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        align-items: center;

        .filter-tag-item {
          padding: 6px 11px;
          border: none;
          border-radius: 2px;
          background-color: transparent;
          color: #656a6f;
          cursor: pointer;
          font-size: 12px;
          font-weight: 400;
          line-height: 12px;
          transition: all 0.3s;
          white-space: nowrap;

          &:hover {
            background-color: #f5f5f5;
          }

          &.selected {
            padding: 2px 10px;
            line-height: 20px;
          }
        }
      }

      // 品牌分类 - 橙色（按设计稿）
      &:nth-child(1) .filter-tags .filter-tag-item {
        &.selected {
          background-color: #ffefd1;
          border: 1px solid #ffdf95;
          color: #e47a01;
        }
      }

      // 车型分类 - 绿色（按设计稿）
      &:nth-child(2) .filter-tags .filter-tag-item {
        &.selected {
          background-color: #e2f5e7;
          border: 1px solid #9be4b3;
          color: #32c56a;
        }
      }

      // 状态分类 - 蓝色（按设计稿）
      &:nth-child(3) .filter-tags .filter-tag-item {
        &.selected {
          background-color: #f5f5ff;
          border: 1px solid #ddddff;
          color: #5755ff;
        }
      }

      // 用途分类 - 紫色（按设计稿）
      &:nth-child(4) .filter-tags .filter-tag-item {
        &.selected {
          background-color: #f7eef6;
          border: 1px solid #ebcae8;
          color: #c535bc;
        }
      }
    }

    .panel-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;
      border-top: none;
      padding-top: 0;
      margin-top: 32px;

      .el-button {
        margin-left: 0;
        padding: 6px 11px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;

        &.cancel-btn {
          background: white;
          border: 1px solid #d0d4d8;
          color: #656a6f;
        }

        &.confirm-btn {
          background: #5755ff;
          border: none;
          color: white;
        }
      }
    }
  }

  // 主要内容区域
  .main-content-vehicle {
    display: flex;
    flex-direction: row;
    position: relative;
    transition: all 0.3s ease;
    width: 100%;
    flex: 1;
    overflow: hidden; // 防止内容溢出

    // 抽屉打开时的样式已通过响应式布局处理
    .main-container-vehicle {
      width: 100%;
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease; // 添加宽度变化的过渡动画

      &.shrink {
        margin-right: 6px !important;
        flex: none !important;
        //min-width: 300px !important; // 设置最小宽度防止过度收缩
        // 宽度通过内联样式动态设置
      }
    }
  }

  // 内容卡片
  .content-card {
    flex: 1;
    transition: all 0.3s ease;
    width: 100%;
    min-width: 0; // 允许flex项目收缩到比内容更小

    :deep(.el-card__body) {
      overflow: hidden;
      padding: 0;
    }

    // 收缩状态
    &.shrink {
      flex: none !important;
      margin-right: 16px !important;
      min-width: 300px !important; // 设置最小宽度
      // 宽度通过内联样式动态设置
    }
  }

  // 车辆内容区域
  .vehicles-content {
    padding: 28px 16px 6px 16px;
    background-color: white;
    min-height: 400px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .loading-container {
      padding: 40px;
      text-align: center;
    }

    .empty-container {
      padding: 60px 0;
      text-align: center;
    }

    .vehicles-list {
      flex: 1;
      overflow-y: auto;
      //min-width: 742px;
    @media (min-width: 900px) and (max-width: 1600px) {
      padding-bottom: 18px;
    }
    }

    // 无限滚动状态样式
    .infinite-scroll-status {
      text-align: center;

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #666;
        font-size: 14px;

        .el-icon {
          font-size: 16px;
        }
      }

      .no-more-data {
        color: #999;
        font-size: 14px;
        border-top: 1px solid #ebeef5;
        padding-top: 20px;
      }
    }

    // 车辆列表容器样式 - Flex布局
    .vehicles-flex-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      .vehicle-flex-item {
        margin-bottom: 19px;
        transition: all 0.3s ease;
        box-sizing: border-box;
        overflow: hidden;
        box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);

        // 样式通过 JavaScript 动态计算设置

        .vehicle-card-item {
          width: 100%;
          height: 100%;
          transition: transform 0.3s ease;
          box-sizing: border-box;
        }
      }
      
      // 响应式布局现在通过 JavaScript 动态计算，无需 CSS 媒体查询

      // 抽屉打开时的样式调整
      &.with-drawer {
        gap: 20px;

        .vehicle-flex-item {
          margin-bottom: 16px;
        }

        // 抽屉状态下的响应式布局通过 JavaScript 动态计算
      }
    }

    // 抽屉打开时的响应式调整
    &.with-drawer {
      .vehicles-list {
        // 在抽屉打开时，进一步优化间距
        .el-row {
          margin-left: -10px;
          margin-right: -10px;
        }

        .vehicle-col-item {
          padding-left: 10px;
          padding-right: 10px;
          margin-bottom: 16px; // 减少行间距以适应更紧凑的布局
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fleets-management-new {
    padding: 10px;

    .search-filter-section .search-container .search-input {
      width: 100%;
    }

    .brand-filter-section .filter-tags {
      justify-content: flex-start;
    }

    // 移动端响应式布局已通过el-col :xs="24"处理
  }
}

// 响应式布局 - 抽屉打开时的特殊处理
@media (max-width: 1200px) {
  .fleets-management-new .content-card.with-drawer {
    .vehicles-list {
      // 在较小屏幕上，抽屉打开时使用更紧凑的布局
      .el-row {
        --el-row-gutter: 16px;
      }
    }
  }
}

@media (max-width: 992px) {
  .fleets-management-new .content-card.with-drawer {
    .vehicles-list {
      // 在中等屏幕上，抽屉打开时进一步压缩
      .el-row {
        --el-row-gutter: 12px;
      }

      .vehicle-col-item {
        margin-bottom: 12px;
      }
    }
  }
}

/* 编辑抽屉样式 */
.edit-drawer-fleet {
  width: 752px;
  height: 100%;
  z-index: 100;
  animation: slideInRight 0.3s ease-out;
  flex-shrink: 0; // 防止抽屉被压缩
  position: relative;
  min-width: 700px; // 设置最小宽度（适合笔记本屏幕）
}

/* 拖拽手柄样式 */
.drawer-resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  cursor: ew-resize;
  z-index: 101;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(64, 158, 255, 0.3);
  }

  &:active {
    background: rgba(64, 158, 255, 0.5);
  }

  // 添加拖拽指示器
  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 40px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.edit-drawer-fleet .drawer-container {
  width: 100%;
  height: 100%;
  box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  background: white;
  display: flex;
  flex-direction: column;
  margin-left: 4px; // 为拖拽手柄留出空间
}

.edit-drawer-fleet .drawer-header {
  height: 50px;
  padding: 20px 20px 10px 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.edit-drawer-fleet .drawer-title {
  flex: 1;
  color: #232628;
  font-size: 18px;
  font-family: 'Bosch Sans', sans-serif;
  font-weight: 400;
  line-height: 18px;
}

.edit-drawer-fleet .drawer-close {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-drawer-fleet .drawer-close:hover {
  opacity: 0.7;
}

.edit-drawer-fleet .drawer-content {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
  .vehicle-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 16px 0;

      .el-tabs__nav-wrap {
        &::after {
          background-color: #eff1f2;
        }
      }

      .el-tabs__item {
        font-size: 16px;
        font-family: 'Bosch Sans Global', sans-serif;
        color: #4e5256;
        font-weight: 400;
        padding: 9px 20px 7px 0;
        margin-bottom:4px;

        &.is-active {
          color: #5755ff;
          font-weight: 700;
        }
      }

      .el-tabs__active-bar {
        background-color: #5755ff;
        height: 2px;
      }
    }
  }
}

.edit-drawer-fleet .form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.edit-drawer-fleet .form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e4e7ed;
}

.edit-drawer-fleet .form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.edit-drawer-fleet .form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  min-height: 32px;
}

.edit-drawer-fleet .form-label {
  color: #4e5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 32px;
  min-width: 80px;
}

.edit-drawer-fleet .form-value {
  color: #232628;
  font-size: 12px;
  font-family: 'Bosch Sans Global', sans-serif;
  font-weight: 400;
  line-height: 32px;
  text-align: left;
  display: flex;
  align-items: center;
  height: 32px;
  width: 100%;
}

/* 选项卡样式 */
.edit-drawer-fleet .vehicle-tabs {
  :deep(.el-tabs__header) {
    margin: 0 0 20px 0;
    border-bottom: 1px solid #e4e7ed;
    position: relative;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0;
    margin: 0;
  }

  :deep(.el-tabs__nav-scroll) {
    padding: 0;
  }

  :deep(.el-tabs__nav) {
    border: none;
  }

  :deep(.el-tab-pane) {

    height: 100%;
  }

  :deep(.el-tabs__item) {
    color: #606266;
    font-size: 14px;
    font-weight: 400;
    padding: 0 0;
    height: 40px;
    line-height: 40px;
    border: none;


    &.is-active {
      color: #409eff;
      font-weight: 500;
    }

    &:first-child {
      margin-left: 0;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: #409eff;
    height: 2px;
    bottom: 0;
    left: 0; /* 与选项卡内容对齐 */
  }

  :deep(.el-tabs__content) {
    padding: 0 0;
    position: static;
  }

  :deep(.el-tab-pane) {
    padding: 0;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 拖拽时的全局样式 */
body.drawer-dragging {
  user-select: none !important;
  cursor: ew-resize !important;

  * {
    user-select: none !important;
    pointer-events: none !important;
  }

  .drawer-resize-handle {
    pointer-events: auto !important;
  }
}

/* 全局表单元素32px高度样式 */
.edit-drawer-fleet {
  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-select) {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  :deep(.el-date-editor) {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  :deep(.el-cascader) {
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;

    .el-form-item__label {
      line-height: 32px;
    }

    .el-form-item__content {
      line-height: 32px;
    }
  }
}
</style>
