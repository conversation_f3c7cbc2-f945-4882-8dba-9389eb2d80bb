var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value)
          })
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value))
        } catch (e) {
          reject(e)
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value))
        } catch (e) {
          reject(e)
        }
      }
      function step(result) {
        result.done
          ? resolve(result.value)
          : adopt(result.value).then(fulfilled, rejected)
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next())
    })
  }
import { createScene } from './create-scene'
import { PointCloud } from './point-cloud'
import { Matrix } from './trimmed-babylonjs'
import { ParserPool } from './worker/parser-pool'
// const loadingSvg = `<svg t="1666322329057" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2573" width="32" height="32"><path d="M876.864 782.592c3.264 0 6.272-3.2 6.272-6.656 0-3.456-3.008-6.592-6.272-6.592-3.264 0-6.272 3.2-6.272 6.592 0 3.456 3.008 6.656 6.272 6.656z m-140.544 153.344c2.304 2.432 5.568 3.84 8.768 3.84a12.16 12.16 0 0 0 8.832-3.84 13.76 13.76 0 0 0 0-18.56 12.224 12.224 0 0 0-8.832-3.84 12.16 12.16 0 0 0-8.768 3.84 13.696 13.696 0 0 0 0 18.56zM552.32 1018.24c3.456 3.648 8.32 5.76 13.184 5.76a18.368 18.368 0 0 0 13.184-5.76 20.608 20.608 0 0 0 0-27.968 18.368 18.368 0 0 0-13.184-5.824 18.368 18.368 0 0 0-13.184 5.76 20.608 20.608 0 0 0 0 28.032z m-198.336-5.76c4.608 4.8 11.072 7.68 17.6 7.68a24.448 24.448 0 0 0 17.536-7.68 27.456 27.456 0 0 0 0-37.248 24.448 24.448 0 0 0-17.536-7.68 24.448 24.448 0 0 0-17.6 7.68 27.52 27.52 0 0 0 0 37.184z m-175.68-91.84c5.76 6.08 13.824 9.6 21.952 9.6a30.592 30.592 0 0 0 22.016-9.6 34.368 34.368 0 0 0 0-46.592 30.592 30.592 0 0 0-22.016-9.6 30.592 30.592 0 0 0-21.952 9.6 34.368 34.368 0 0 0 0 46.592z m-121.152-159.36c6.912 7.36 16.64 11.648 26.368 11.648a36.736 36.736 0 0 0 26.432-11.584 41.28 41.28 0 0 0 0-55.936 36.736 36.736 0 0 0-26.432-11.584 36.8 36.8 0 0 0-26.368 11.52 41.28 41.28 0 0 0 0 56zM12.736 564.672a42.88 42.88 0 0 0 30.784 13.44 42.88 42.88 0 0 0 30.784-13.44 48.128 48.128 0 0 0 0-65.216 42.88 42.88 0 0 0-30.72-13.44 42.88 42.88 0 0 0-30.848 13.44 48.128 48.128 0 0 0 0 65.216z m39.808-195.392a48.96 48.96 0 0 0 35.2 15.36 48.96 48.96 0 0 0 35.2-15.36 54.976 54.976 0 0 0 0-74.56 48.96 48.96 0 0 0-35.2-15.424 48.96 48.96 0 0 0-35.2 15.424 54.976 54.976 0 0 0 0 74.56zM168.32 212.48c10.368 11.008 24.96 17.408 39.68 17.408 14.592 0 29.184-6.4 39.552-17.408a61.888 61.888 0 0 0 0-83.84 55.104 55.104 0 0 0-39.616-17.408c-14.656 0-29.248 6.4-39.616 17.408a61.888 61.888 0 0 0 0 83.84zM337.344 124.8c11.52 12.16 27.712 19.264 43.968 19.264 16.256 0 32.448-7.04 43.968-19.264a68.672 68.672 0 0 0 0-93.184 61.248 61.248 0 0 0-43.968-19.264 61.248 61.248 0 0 0-43.968 19.264 68.736 68.736 0 0 0 0 93.184z m189.632-1.088c12.672 13.44 30.528 21.248 48.448 21.248s35.712-7.808 48.384-21.248a75.584 75.584 0 0 0 0-102.464A67.392 67.392 0 0 0 575.36 0c-17.92 0-35.776 7.808-48.448 21.248a75.584 75.584 0 0 0 0 102.464z m173.824 86.592c13.824 14.592 33.28 23.104 52.736 23.104 19.584 0 39.04-8.512 52.8-23.104a82.432 82.432 0 0 0 0-111.744 73.472 73.472 0 0 0-52.8-23.168c-19.52 0-38.912 8.512-52.736 23.168a82.432 82.432 0 0 0 0 111.744z m124.032 158.528c14.976 15.872 36.032 25.088 57.216 25.088 21.12 0 42.24-9.216 57.152-25.088a89.344 89.344 0 0 0 0-121.088 79.616 79.616 0 0 0-57.152-25.088c-21.184 0-42.24 9.216-57.216 25.088a89.344 89.344 0 0 0 0 121.088z m50.432 204.032c16.128 17.088 38.784 27.008 61.632 27.008 22.784 0 45.44-9.92 61.568-27.008a96.256 96.256 0 0 0 0-130.432 85.76 85.76 0 0 0-61.568-27.072c-22.848 0-45.44 9.984-61.632 27.072a96.192 96.192 0 0 0 0 130.432z" fill="currentColor" p-id="2574"></path></svg>`;
// const closeSvg = `<svg t="1666326212440" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2542" width="32" height="32"><path d="M512 128C300.8 128 128 300.8 128 512s172.8 384 384 384 384-172.8 384-384S723.2 128 512 128zM672 627.2c12.8 12.8 12.8 32 0 44.8s-32 12.8-44.8 0L512 556.8l-115.2 115.2c-12.8 12.8-32 12.8-44.8 0s-12.8-32 0-44.8L467.2 512 352 396.8C339.2 384 339.2 364.8 352 352s32-12.8 44.8 0L512 467.2l115.2-115.2c12.8-12.8 32-12.8 44.8 0s12.8 32 0 44.8L556.8 512 672 627.2z" fill="currentColor" p-id="2543"></path></svg>`;
class PcdPreviewer {
  constructor(options = {}) {
    this.opened = false
    this.pointClouds = null
    options = Object.assign(
      {
        defaultColor: [1, 1, 1],
        size: 1,
        opacity: 1,
        rounded: false,
        enableIntensity: true
      },
      options
    )
    // // create canvas
    // this._canvas = document.createElement('canvas');
    // this._canvas.setAttribute('style', 'height: 100%; width: 100%; outline: none;');
    // // create loading icon
    // this._loadingElement = document.createElement('div');
    // this._loadingElement.setAttribute('style', 'position: absolute; top: 0; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.25); display: flex; justify-content: center; align-items: center;');
    // this._loadingElement.innerHTML = `<span style="color: white; animation:rotate 1.5s linear infinite;">${loadingSvg}</span>`;
    // this._style = document.createElement('style');
    // this._style.textContent = `@keyframes rotate { from {transform:rotate(0deg); } to { transform:rotate(360deg); }};`;
    // document.head.appendChild(this._style);
    // // create container
    // this._container = document.createElement('div');
    // this._container.setAttribute('style', `position: fixed; top: 0; bottom: 0; left: 0; right: 0; background: #122430; z-index: 9999;`);
    // this._container.classList.add('pcd_viewer');
    // this._container.appendChild(this._canvas);
    // // create close icon
    // this._closeIcon = document.createElement('div');
    // this._closeIcon.setAttribute('style', 'position: absolute; top: 8px; right: 8px; color: #f87171; cursor: pointer; z-index: 9999;');
    // this._closeIcon.innerHTML = closeSvg;
    // this._container.appendChild(this._closeIcon);
    // this._closeIcon.addEventListener('click', () => {
    //     this.close();
    // });
    // // create toggleIntensityMode
    // this._intensityModeBtn = document.createElement('button');
    // this._intensityModeBtn.setAttribute('style', 'position: absolute; bottom: 8px; right: 208px; cursor: pointer; z-index: 9999;');
    // this._intensityModeBtn.setAttribute('class', 'el-button el-button--danger el-button--default is-plain');
    // this._intensityModeBtn.innerHTML = 'toggleIntensityMode';
    // this._container.appendChild(this._intensityModeBtn);
    // this._intensityModeBtn.addEventListener('click', () => {
    //     this.enableIntensity = !this.enableIntensity;
    // });
    // // create _addSize
    // this._addSize = document.createElement('button');
    // this._addSize.setAttribute('style', 'position: absolute; bottom: 8px; right: 108px; cursor: pointer; z-index: 9999;');
    // this._addSize.setAttribute('class', 'el-button el-button--primary el-button--default is-plain');
    // this._addSize.innerHTML = 'size+';
    // this._container.appendChild(this._addSize);
    // this._addSize.addEventListener('click', () => {
    //     this.size = Math.max(this.size + 0.1, 0.5)
    // });
    // // create _reduceSize
    // this._reduceSize = document.createElement('button');
    // this._reduceSize.setAttribute('style', 'position: absolute; bottom: 8px; right: 8px; cursor: pointer; z-index: 9999;');
    // this._reduceSize.setAttribute('class', 'el-button el-button--primary el-button--default is-plain');
    // this._reduceSize.innerHTML = 'size-';
    // this._container.appendChild(this._reduceSize);
    // this._reduceSize.addEventListener('click', () => {
    //     this.size = Math.max(this.size - 0.1, 0.5)
    // });
    this.options = options
    this._parser = new ParserPool()
  }
  /**
   * dynamic adjust size of point cloud
   */
  get size() {
    return this.options.size || 1
  }
  set size(value) {
    var _a
    this.options.size = value
    ;(_a = this.pointClouds) === null || _a === void 0
      ? void 0
      : _a.forEach(item => item.setSize(value))
  }
  /**
   * dynamic adjust color of point cloud
   */
  set color(value) {
    var _a
    this.options.defaultColor = value
    ;(_a = this.pointClouds) === null || _a === void 0
      ? void 0
      : _a.forEach(item => item.setColor(value))
  }
  /**
   * dynamic adjust color of point cloud
   */
  get opacity() {
    return this.options.opacity || 1
  }
  set opacity(value) {
    var _a
    this.options.opacity = value
    ;(_a = this.pointClouds) === null || _a === void 0
      ? void 0
      : _a.forEach(item => item.setOpacity(value))
  }
  /**
   * dynamic adjust intensity mode
   */
  set enableIntensity(value) {
    var _a
    this.options.enableIntensity = value
    ;(_a = this.pointClouds) === null || _a === void 0
      ? void 0
      : _a.forEach(item => item.setEnableIntensity(value))
  }
  get enableIntensity() {
    return !!this.options.enableIntensity
  }
  /**
   * call resize when container size changed
   */
  resize() {
    this.engine.resize()
  }
  /**
   * reset view to default
   */
  resetView() {
    this.scene.activeCamera && this._resetCameraView(this.scene.activeCamera)
  }
  /**
   * open pcd viewer
   * @return errors if file load failed
   */
  open(paths, id) {
    var _a, _b
    return __awaiter(this, void 0, void 0, function* () {
      // create scene
      // if (!this._canvas) {
      this._canvas = document.querySelector('#' + id)
      const [scene, resetCameraView] = createScene(this._canvas, this.options)
      this.scene = scene
      this._resetCameraView = resetCameraView
      this.engine = scene.getEngine()
      // }
      // console.time('open');
      this._appendToScreen()
      // this._loading(true);
      ;(_a = this._rejects) === null || _a === void 0
        ? void 0
        : _a.forEach(fn => fn())
      ;((_b = this.pointClouds) === null || _b === void 0
        ? void 0
        : _b.length) && this.pointClouds.forEach(item => item.dispose())
      !this.opened &&
        this.engine.runRenderLoop(() => {
          var _a
          if (
            !((_a = this.scene) === null || _a === void 0
              ? void 0
              : _a.activeCamera) ||
            !this.opened
          )
            return
          this.scene.render()
        })
      this.opened = true
      const errors = []
      const urls = []
      for (const item of paths)
        typeof item === 'string' ? urls.push(item) : urls.push(item.path)
      this._rejects = []
      const loadFilePromise = url => {
        return new Promise((resolve, reject) => {
          this._rejects.push(reject)
          fetch(url)
            .then(response => response.arrayBuffer())
            .then(data => this._parser.parse(data))
            .then(resolve)
            .catch(e => reject(e))
        })
      }
      const loadPointClouds = urls.map((url, index) =>
        loadFilePromise(url)
          .catch(e => {
            e.message = `(${url}):${e.message}`
            errors.push(e)
            return { attributes: { POSITION: [] } }
          })
          .then(data => {
            var _a
            const matrix =
              typeof paths[index] === 'object' &&
              ((_a = paths[index]) === null || _a === void 0
                ? void 0
                : _a.matrix)
            const { attributes } = data
            const pointCloudData = {
              position: attributes.POSITION,
              color: attributes.COLOR_0,
              intensity: attributes.INTENSITY
            }
            return new PointCloud(
              this.scene,
              pointCloudData,
              this.options,
              matrix
            )
          })
      )
      const data = yield Promise.all(loadPointClouds)
      // .finally(() => this._loading(false));
      this._rejects = []
      this.pointClouds = data
      // console.timeEnd('open')
      return errors
    })
  }
  /**
   * close pcd viewer
   */
  close() {
    this.opened = false
    this.engine.stopRenderLoop()
    // this._loading(false);
    // this._removeFromScreen();
  }
  /**
   * destroy pcd viewer
   */
  dispose() {
    this.close()
    this.engine.dispose()
    // try {
    //   this._style.parentNode && document.head.removeChild(this._style)
    // } catch (e) {
    //   console.error(e)
    // }
    this._canvas = null
    // this._container = null
    // this._loadingElement = null;
    this._style = null
    // this._parser.dispose()
  }
  /**
   * append pcd viewer to screen
   */
  _appendToScreen() {
    if (this.opened) return
    // document.body.appendChild(this._container);
    this.engine.resize()
  }
  /**
   * remove pcd viewer from screen
   */
  // _removeFromScreen() {
  //     try {
  //         this._container.parentNode && document.body.removeChild(this._container);
  //     }
  //     catch (e) {
  //         console.error(e);
  //     }
  // }
  /**
   * set loading state
   */
  // _loading(state) {
  //     try {
  //         if (state) {
  //             this._container.appendChild(this._loadingElement);
  //         }
  //         else {
  //             this._loadingElement.parentElement && this._container.removeChild(this._loadingElement);
  //         }
  //     }
  //     catch (e) {
  //         console.error(e);
  //     }
  // }
}
export * from './utils'
export { PcdPreviewer, Matrix }
export default PcdPreviewer
