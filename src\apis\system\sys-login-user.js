import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysLoginUser = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users', data, params})
export const updateSysLoginUser = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users', data, params})
export const deleteSysLoginUser = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users', params})
export const listSysLoginUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users', params})
export const listSysLoginUserSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/selections', params})
export const pageSysLoginUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/page', params})
export const getSysLoginUser = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/' + id})
export const assignActors = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/actors/assign', data, params})
export const addActors = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/actors/add', data, params})
export const resetPassword = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/reset_pwd/'+id})
export const auditPass = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/audit/pass/'+id})
export const auditReject = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/audit/reject/'+id})
export const getAccessRole = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/tree/current_user/access_role_type', params})
