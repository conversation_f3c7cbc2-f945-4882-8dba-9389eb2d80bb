<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    @close="dialogClosed"
    append-to-body
    :draggable="true"
    destroy-on-close
  >
    <div class="map-selection-container">
      <base-map
        class="base-map"
        ref="baseMap"
        @right-click-map="rightClickMap"
        @adjust-complete="adjustComplete"
        @click-editor-marker="clickEditorMarker"
      ></base-map>
      <div class="query-param top">
<!--        <div class="label">-->
<!--          <el-button size="small" type="primary" @click="openQueryParams()" id="tag" :disabled="disabled">-->
<!--            筛选-->
<!--            <ltw-icon :class="{ visible: filterForm }" icon-code="el-icon-d-arrow-right"></ltw-icon>-->
<!--          </el-button>-->
<!--        </div>-->
        <div class="form visible">
          <el-select
            size="small"
            clearable
            v-model="queryParam.key"
            remote
            :remote-method="positioningByAddress"
            :loading="queryLoading"
            :placeholder="$t('示例:39.90812,116.397484/苏州市现代大道')"
            filterable
            @change="selectSuggestionHandle"
          >
            <el-option v-for="item in suggestionList" :key="item.id" :label="item.title" :value="item.id">
              <span style="float: left">{{ item.title }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                {{
                  (item.province || '') +
                  (item.city ? ',' + item.city : '') +
                  (item.district ? ',' + item.district : '')
                }}
              </span>
            </el-option>
          </el-select>
          <el-button size="small" @click="search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { latLngPoint, isLatitudeLongitude, searchSuggestionAPI } from '@/plugins/map/TxMap'
import { showToast } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'
import md5 from 'js-md5'
import { jsonp } from 'vue-jsonp'
import MarkerInfoWindow from '@/pages/dataCollect/dialog/MarkerInfoWindow.vue'
import { h, render } from 'vue'
import BaseMap from '@/components/map/BaseMap.vue'

const defaultform = {}
export default {
  name: 'ChooseMapPosition',
  emits: ['reload', 'cancel'],
  components: {
    BaseMap
  },
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      // windowinfo传值
      currentMarker: {},
      queryParam: {},
      filterForm: false,
      suggestionList: [],
      queryLoading: false,
      disabled: false
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('选择点位')
      this.$nextTick(async () => {
        await this.$refs.baseMap.show(row)
        const globalInfoWindow = this.$refs.baseMap.createInfoWindow(this.createInfoWindow())
        globalInfoWindow?.close()
      })
    },
    adjustComplete(e) {
      this.dragEnd(e)
    },

    async dragEnd(event) {
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.setPosition(
        latLngPoint({
          lat: event?.position?.lat,
          lng: event?.position?.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: event?.position?.lat,
        lng: event?.position?.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      this.currentMarker.cantonCode = currentMarker.cantonCode
      globalInfoWindow?.open()
    },
    async rightClickMap(e) {
      if (!this.disabled) {
        const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')

        globalInfoWindow?.close()
        await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.NONE)

        const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
        this.$refs.baseMap.removeGeo(globalMapEditorMarker.getGeometries().map(val => val.id))

        await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
        this.$refs.baseMap.setActiveOverlay(OVER_LAY_TYPE.DRAGMARKER)
        let markerList = [
          {
            id: 0, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            style: {
              width: 17,
              height: 25,
              anchor: { x: 17, y: 50 },
              src: 'data:image/png;base64,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'
            },
            position: e.latLng
          }
        ]
        this.$refs.baseMap.addGeo(markerList, 'globalMapEditorMarker')
      }
    },
    clickEditorMarker(e) {
      this.openInfoWindow(e)
    },
    async openInfoWindow(evt) {
      this.$refs.baseMap.goCenter([evt.geometry.position.lng, evt.geometry.position.lat])
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.setPosition(
        latLngPoint({
          lat: evt.geometry.position.lat,
          lng: evt.geometry.position.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: evt.geometry.position.lat,
        lng: evt.geometry.position.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      this.currentMarker.cantonCode = currentMarker.cantonCode
      this.currentMarker.adInfo = currentMarker.adInfo
      globalInfoWindow?.open()
    },
    createInfoWindow() {
      const _this = this
      const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
      let instance = h(MarkerInfoWindow, {
        item: _this.currentMarker,
        type: 'choose',
        onSetPoint: val => {
          // 确保传递的数据包含adInfo
          const dataToPass = {
            ...val,
            adInfo: _this.currentMarker.adInfo
          }
          this.setPoint(dataToPass)
        }
      })
      let infoWindow = new TMap.InfoWindow({
        map: globalMap,
        enableCustom: true,
        position: latLngPoint({ lat: 31.380155339677, lng: 121.27259505835 }),
        offset: { y: -50, x: 0 },
        content: '<div id="ref-card"></div>'
      })
      let card = document.querySelector('#ref-card')
      render(instance, card)
      return infoWindow
    },
    setPoint(val) {
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')

      if (val.type === 'confirm') {
        this.cancel()
        this.$emit('reload', val)
      }
      globalInfoWindow?.close()
      // console.log(val)
      // if (this.leftHidden) {
      //   this.leftHidden = false
      // }
      // const index = this.routePointList.findIndex(point => val.id === point.id)
      // if (!~index) {
      //   const startIndex = this.routePointList.findIndex(val => val.type === 'start')
      //   const endIndex = this.routePointList.findIndex(val => val.type === 'end')
      //   if (val.type === 'start') {
      //     if (~startIndex) {
      //       this.routePointList[startIndex].type = 'pass'
      //     }
      //     this.routePointList.unshift(val)
      //   } else if (val.type === 'end') {
      //     if (~endIndex) {
      //       this.routePointList[endIndex].type = 'pass'
      //     }
      //     this.routePointList.push(val)
      //   } else {
      //     if (!~endIndex) {
      //       this.routePointList.push(val)
      //     } else {
      //       this.routePointList.splice(-1, 0, val)
      //     }
      //   }
      //   const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
      //   const ids = globalMapEditorMarker
      //     .getGeometries()
      //     .filter(marker => marker.id !== val.id)
      //     .map(marker => marker.id)
      //   this.$refs.baseMap.removeGeo(globalMapEditorMarker, ids)
      // } else {
      //   showToast(this.$t('该点位已添加'), 'warning')
      // }
    },
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.queryParam = {}
    },
    cancel(obj) {
      this.dialogVisible = false
    },
    openQueryParams() {
      this.filterForm = !this.filterForm
    },
    async positioningByAddress(key) {
      if (!key) return
      const isLatLng = isLatitudeLongitude(key)
      let suggestionList = []
      if (isLatLng) {
        suggestionList = [
          {
            title: key,
            id: key
          }
        ]
      } else {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(key)
        this.queryLoading = false
      }
      this.suggestionList = suggestionList
    },
    selectSuggestionHandle(key) {
      if (!key) return
      const item = this.suggestionList.find(val => val.id === key)
      // this.queryParam.keyword = item?.title
      // this.queryParam.location = item?.location
      this.queryParam.keyword = (item?.province || '') + (item?.city || '') + (item?.district || '') + item?.title
      this.search()
    },
    async search() {
      if (!this.queryParam.key) return
      const isLatLng = isLatitudeLongitude(this.queryParam.key)
      let suggestionList = []
      if (!isLatLng) {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(this.queryParam.keyword)
        this.queryLoading = false
      } else {
        suggestionList = [{ location: latLngPoint(this.queryParam.key.split(',')) }]
      }
      let markerList = suggestionList.map((val, index) => {
        // const item = this.convertorItem(val)
        return {
          id: val.id, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          style: {
            width: 34,
            height: 50,
            anchor: { x: 17, y: 50 },
            enableRelativeScale: true,
            relativeScaleOptions: {
              scaleZoom: 17, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
              minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
              maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
            },
            src: 'data:image/png;base64,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'
          },

          position: val.location //点标记坐标位置
        }
      })

      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.NONE)
      const globalMapEditorMarker = this.$refs.baseMap.getGlobalMapObj('globalMapEditorMarker')
      this.$refs.baseMap.removeGeo(globalMapEditorMarker.getGeometries().map(val => val.id))
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
      this.$refs.baseMap.addGeo(markerList)
      const globalInfoWindow = this.$refs.baseMap.getGlobalMapObj('globalInfoWindow')
      globalInfoWindow?.close()
      this.$refs.baseMap.showFitView(markerList)
    },
    getGeoCoder(param) {
      let sig = md5(
        `/ws/geocoder/v1?callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&location=${param.location}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
      )
      // sig = encodeURI(sig) //url化一下
      let getData = {
        callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
        callbackName: 'jsonpCallback', // 设置callback 参数的值
        key: GLB_CONFIG.TxMapKey,
        location: param.location,
        output: 'jsonp',
        sig
      }
      //签名失败的解决办法 https://lbs.qq.com/faq/serverFaq/webServiceKey
      return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
    },
    async getLocationName(item) {
      // this.currentVehicle = row
      if (item.lat && item.lng) {
        let res = await this.getGeoCoder({
          location: item.lat + ',' + item.lng
        })
        if (res.status === 0) {
          item.title = res.result?.formatted_addresses?.recommend
          item.id = res.request_id
          item.cantonCode = res.result?.ad_info?.adcode
          debugger
          // 添加完整的行政区信息
          item.adInfo = res.result?.ad_info
          return item
        } else {
          showToast(res.message, 'warning')
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.map-selection-container {
  position: relative;

  .base-map {
    height: 400px;
    z-index: 1;
  }

  .query-param {
    width: 100%;
    display: flex;
    z-index: 2;
    position: absolute;
    left: 0;

    &.top {
      top: 0;
    }

    .label {
      display: flex;
      align-items: center;
      white-space: nowrap;

      .ltw-icon {
        transition: all 0.3s;
      }

      .visible {
        transform: rotateZ(180deg);
      }
    }

    .form {
      width: calc(100% - 42px);
      padding-left: 10px;
      display: flex;
      align-items: center;
      transform-origin: 0 50%;
      transform: scaleX(0);
      transition: all 0.3s;

      &.visible {
        transform: scaleX(1);
      }
    }
  }
}
</style>
