import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsTagClassification = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications',
    data,
    params
  })
export const updateBsTagClassification = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications',
    data,
    params
  })
export const deleteBsTagClassification = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications',
    params
  })
export const listBsTagClassification = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications',
    params
  })
export const listBsTagClassificationSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/selections',
    params
  })
export const pageBsTagClassification = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/page',
    params
  })
export const getBsTagClassification = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/' + id })
//
export const getTagList = (id, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/bs_tag_classifications/tags/' + id,
    unloading
  })
export const pageQuery = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/page/current_user',
    params
  })
export const share = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/share',
    data,
    params
  })
// export const publish = id =>
//   httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_classifications/push/' + id })
export const publish = id =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/basic/bs_tag_classifications/push_to_task/' + id })
export const listUserTagClassification = () =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/bs_tag_classifications/current_user' })
