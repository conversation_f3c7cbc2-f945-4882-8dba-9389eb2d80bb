/**
 * v-drag 表格自定义指令加载
 */

export default {
  updated: function (el, binding, vnode) {
    setTimeout(() => {
      // 入参
      const dragClass = vnode.props['v-drag-class']
      const draggable = vnode.props['v-draggable']
      const dragDays = vnode.props['v-drag-days']
      // const dragDisabledDays = vnode.props['v-drag-disabled-days']
      const dragVisible = vnode.props['v-drag-visible']
      // 当前元素
      let oDiv = el
      let domcustomDiv = el.querySelector('#customDiv')

      if (dragClass) {
        oDiv = el.querySelector(dragClass)
      }
      // 取消拖拽
      if (draggable === false) {
        moveToDefault()
        oDiv.style.position = 'relative'
        oDiv.style.zIndex = '0'
        domcustomDiv?.remove()
        oDiv.onselectstart = function () {}
        oDiv.onmousedown = () => {}
        return
      } else {
        // 鼠标放下时归位
        if (!dragVisible) {
          moveToDefault()
        }
      }
      function moveToDefault() {
        oDiv.style.top = 0
      }
      // 获取可滚动距离
      let dragLength = 0,
        offsetTop = 0,
        // draggableTop = 0,
        // draggableBottom = 0,
        dragSingleHeight = 0
      if (!el.previousElementSibling) {
        dragSingleHeight = el.clientHeight / dragDays
        // dragSingleHeight = el.clientHeight / (dragDays + dragDisabledDays)
        dragLength = dragSingleHeight * dragDays
        // dragLength =
        //   dragSingleHeight * (dragDays + dragDisabledDays)
        offsetTop = el.offsetTop
      } else if (!el.nextElementSibling) {
        dragSingleHeight = el.previousElementSibling.clientHeight / dragDays
        dragLength = dragSingleHeight * dragDays
        offsetTop = el.previousElementSibling.offsetTop
      } else {
        dragSingleHeight =
          (el.clientHeight + el.previousElementSibling.clientHeight) / dragDays
        dragLength = el.clientHeight + el.previousElementSibling.clientHeight
        offsetTop = el.previousElementSibling.offsetTop
      }
      // 设置隐藏函数
      var timeout = false
      let setRowDisableNone = function (topPx, dragLength, offsetTop, binding) {
        if (timeout) {
          clearTimeout(timeout)
        }
        timeout = setTimeout(() => {
          binding.value.call(null, topPx, dragLength, offsetTop)
        })
      }
      setRowDisableNone('', dragLength, offsetTop, binding)

      if (!domcustomDiv) {
        const createElementTR = document.createElement('div')
        createElementTR.setAttribute('style', `height: ${oDiv.clientHeight}px;`)
        createElementTR.setAttribute('id', `customDiv`)
        el.append(createElementTR)
      }

      oDiv.style.position = 'absolute'
      oDiv.style.zIndex = '1'
      // let self = this // 上下文
      // 禁止选择网页上的文字
      oDiv.onselectstart = function () {
        return false
      }
      oDiv.onmousedown = e => {
        oDiv.style.transition = ''
        // 鼠标按下，计算当前元素距离可视区的距离
        // let disX = e.clientX - oDiv.offsetLeft
        let disY = e.clientY - oDiv.offsetTop
        if (
          e.target.id === 'tagResize' ||
          e.target.nodeName === 'INPUT' ||
          e.target.nodeName === 'TEXTAREA'
        ) {
          return
        }
        // document.onmousemove = throttle(function (e) {
        document.onmousemove = e => {
          // 通过事件委托，计算移动的距离
          let t = e.clientY - disY
          // if (!(t > -draggableTop && t < draggableBottom)) {
          //   return
          // }
          // 移动当前元素
          // oDiv.style.left = l + 'px'
          oDiv.style.top = t + 'px'
          let num = t / dragSingleHeight
          // if (typeof num === 'number' && num % 1 === 0) {
          setRowDisableNone(Math.floor(num), dragLength, offsetTop, binding)
          // }
        }
        document.onmouseup = () => {
          oDiv.style.transition = 'all .3s'
          document.onmousemove = null
          document.onmouseup = null
        }
        // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false
      }
    })
  }
  // updated: function (el, binding) {
  //   setTimeout(() => {})
  // }
}
