import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysPrivilegeMenu = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus', data, params})
export const updateSysPrivilegeMenu = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus', data, params})
export const deleteSysPrivilegeMenu = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus', params})
export const listSysPrivilegeMenu = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus', params})
export const listSysPrivilegeMenuSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/selections', params})
export const pageSysPrivilegeMenu = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/page', params})
export const getSysPrivilegeMenu = (id) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/' + id})
export const treeListSysPrivilegeMenuOfCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/tree/current_user', params})
export const treeListSysPrivilegeMenu = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/tree', params})
export const treePageSysPrivilegeMenu = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/tree/page', params})
export const listSysPrivilegeMenuOfCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_menus/current_user', params})

