<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="400px"
    @close="dialogClosed"
    @open="dialogOpened"
    class="add-module"
  >
    <!-- label-width="100px" -->
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      :disabled="formReadonly"
    >
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="form.name" id="name"></ltw-input>
      </el-form-item>
      <!-- <el-form-item :label="$t('排序字段')" prop="sortNum">
        <ltw-input v-model="form.sortNum" id="sortNum"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('是否启用')" prop="enabled">
        <el-switch v-model="form.enabled" id="enabled"></el-switch>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="dialogVisible = false"
          v-if="formReadonly"
          id="close"
          >{{ $t('关闭') }}</el-button
        >
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{
            $t('取消')
          }}</el-button>
          <el-button type="primary" @click="saveForm" id="saveForm">{{
            $t('保存')
          }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import {
  saveSysDictionary,
  updateSysDictionary,
  getSysDictionary
} from '@/apis/system/sys-dictionary'
import {getUuid, isInteger, isPositiveNum} from '@/plugins/util'
export default {
  name: 'CarEventStatistics',
  emits: ['reload'],
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      formReadonly: false,
      form: {},
      formRules: {
        // code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        name: [
          { required: true, message: this.$t('请输入名称'), trigger: 'blur' }
        ],
        sortNum: [
          {
            required: true,
            message: this.$t('请输入排序字段'),
            trigger: 'blur'
          },
          { validator: isInteger, trigger: 'blur' },
          {
            validator: isPositiveNum,
            trigger: 'change'
          }
        ]
        // enabled: [
        //   {
        //     required: true,
        //     message: this.$t('请输入是否启用'),
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  // mounted() {
  //   this.getCharts();
  // },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.formReadonly = row.type === 'view' ? true : false
      if (row.id) {
        this.getSysDictionary(row.id)
      } else {
        this.dialogTitle = this.$t('添加模块')
        this.form.typeCode = 'api_module'
      }
    },
    getSysDictionary(id) {
      getSysDictionary(id).then(res => {
        this.dialogTitle = this.$t('编辑模块')
        // this.formReadonly = false
        this.dialogVisible = true
        this.form = res.data
      })
    },
    saveForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.form.id) {
          updateSysDictionary(this.form).then(() => {
            this.cancel()
          })
        } else {
          saveSysDictionary({ ...this.form, code: getUuid() }).then(() => {
            this.cancel()
          })
        }
      })
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = {}
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    }
  }
}
</script>
<style lang="scss">
.car-event-dialog {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    height: 100%;
    padding: 0;
  }
}
</style>
<style scoped lang="scss">
#car-event-statistics {
  height: 100%;
  // height: 500px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.no-data {
  color: #909399;
}
</style>
