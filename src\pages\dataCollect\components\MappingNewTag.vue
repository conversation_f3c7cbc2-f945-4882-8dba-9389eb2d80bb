<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    :close-on-click-modal="false"
  >
    <el-form :model="formData" ref="formRef" label-width="150px">
      <el-form-item :label="$t('标签')" prop="mappingNewTagList">
        <el-row>
          <el-button type="primary" @click="chooseTag">{{ $t('选择标签') }}</el-button>
        </el-row>
        <el-row class="tag-container">
          <el-tag v-for="item in tagList" :key="item.code">{{ item.name }}</el-tag>
        </el-row>
        <bs-tag-group-drawer
          :drawer-visible="tagDistributeDrawerVisible"
          :row-tag-list="rowTagList"
          :force-reload="true"
          :is-eng-new="true"
          @drawer-click="confirmDistributeTags"
          ref="tagDrawerRef"
        ></bs-tag-group-drawer>
      </el-form-item>
      <el-form-item :label="$t('是否匹配新标签')" prop="mappingNewTag">
        <el-radio-group v-model="formData.mappingNewTag">
          <el-radio :value="true" :label="true">{{ $t('是') }}</el-radio>
          <el-radio :value="false" :label="false">{{ $t('否') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
      <el-button type="primary" @click="confirm">{{ $t('确定') }}</el-button>
    </template>
  </el-dialog>
</template>
<script>
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'

export default {
  name: 'MappingNewTag',
  components: { BsTagGroupDrawer },
  emits: ['confirm'],
  data() {
    return {
      dialogTitle: '匹配新标签',
      dialogVisible: false,
      formData: {},
      rowTagList: [],
      tagList: [],
      tagDistributeDrawerVisible: false
    }
  },
  methods: {
    show(mappingNewTagList, mappingNewTag) {
      this.formData = {
        mappingNewTagList: mappingNewTagList,
        mappingNewTag: mappingNewTag === undefined ? true : mappingNewTag
      }
      this.tagList = mappingNewTagList
      this.dialogVisible = true
    },
    dialogClosed() {},
    chooseTag() {
      this.tagDistributeDrawerVisible = true
      this.rowTagList = this.tagList
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    confirm() {
      this.formData.mappingNewTagList = this.tagList
      this.$emit('confirm', this.formData)
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  width: 100%;
}

.tag-container {
  margin-top: 10px;

  .el-tag {
    margin: 0 3px;
  }
}
</style>
