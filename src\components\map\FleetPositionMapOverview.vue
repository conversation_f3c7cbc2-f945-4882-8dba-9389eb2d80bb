<template>
  <div class="map-overview">
    <div id="allmap" ref="allmap" />
    <!-- <div class="category-list">
      <div class="category-item">
        <span class="name">Total</span><span class="num" v-text="total"></span>
      </div>
      <div class="category-item">
        <span class="name"
          >Free<span class="category-icon free-icon"></span></span
        ><span class="num free" v-text="free"></span>
      </div>
      <div class="category-item">
        <span class="name"
          >DistributedNum<span class="category-icon distributed-icon"></span
        ></span>
        <span class="num working" v-text="distributed"></span>
      </div>
      <div class="category-item">
        <span class="name"
          >WorkingNum<span class="category-icon working-icon"></span
        ></span>
        <span class="num working" v-text="working"></span>
      </div>
    </div> -->
    <!-- <v-fleet-detail-screen ref="FleetDetailScreen"></v-fleet-detail-screen> -->
  </div>
</template>

<script>
// import { h, render } from 'vue'
import { BaiduMap } from '@/plugins/map/map'
// import mapStyle from '@/plugins/map/custom_map_config.js'
// import { getBsVehicle } from '@/apis/basic/bs-vehicle'

export default {
  name: 'Map',
  data() {
    return {
      map: '',
      BMap: '',
      list: [],
      convertorPoints: [],
      total: 0,
      working: 0,
      free: 0,
      distributed: 0,
      fleetList: []
    }
  },
  mounted() {},
  methods: {
    show(data) {
      // this.total = data.total
      // this.working = data.working
      // this.free = data.free
      // this.distributed = data.distributed
      this.list = data.data
      // this.$nextTick(() => {
      BaiduMap.init().then(BMap => {
        // window.BMap = BMap;
        this.map = new BMap.Map(this.$refs.allmap, { enableMapClick: false }) // 创建Map实例
        this.map.centerAndZoom(new BMap.Point(116.404, 39.915), 11) // 初始化地图,设置中心点坐标和地图级别
        if (data.disabledScrollWheelZoom) {
          this.map.disableScrollWheelZoom()
          //平移缩放控件
          this.map.addControl(new BMap.NavigationControl())
        } else {
          this.map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
        }
        this.map.disableDoubleClickZoom()
        // this.map.setMapStyleV2({ styleJson: mapStyle })
        // this.map.setMapType(window.BMAP_SATELLITE_MAP)
        // this.map.setMapType(window.BMAP_NORMAL_MAP)
        // this.map.setMapType(window.BMAP_EARTH_MAP)
        this.getFleetPosition('all')
      })
      // });
    },
    //显示原始路线
    translatePoints(list) {
      return new Promise((resolve, reject) => {
        let points = []
        list.forEach((val, i) => {
          var p = new BMap.Point(val.longitude, val.latitude)
          points.push(p)
        })
        let convertorPoints = [],
          translatePoints = [],
          convertor = new BMap.Convertor(),
          convertorIndex = 0
        for (let i = 0, pointsLen = points.length; i < pointsLen; i += 10) {
          translatePoints.push(points.slice(i, i + 10))
        }
        convertorPoints = JSON.parse(JSON.stringify(translatePoints))
        for (
          let i = 0, translatePointsLen = translatePoints.length;
          i < translatePointsLen;
          i++
        ) {
          convertor.translate(translatePoints[i], 1, 5, data => {
            if (data.status === 0) {
              convertorIndex++
              convertorPoints.splice(i, 1, data.points)
              if (convertorIndex === translatePointsLen) {
                convertorPoints = convertorPoints.reduce((a, b) => {
                  return a.concat(b)
                })
                resolve(convertorPoints)
              }
            }
          })
        }
      })

      // this.addLine(points, color, weight, true)
    },
    async getFleetPosition(companyType, entName) {
      // let this = this

      // getListEntWeighbridgeInfo({
      //   entName: entName
      // })
      //   .then(res => {
      this.map.clearOverlays()
      // window.location.href = url
      let entity = this.list
      let companyList = []
      switch (companyType) {
        case 'all':
          companyList = entity
          break
        case 'free':
          entity.forEach(function (val) {
            if (val.latitude && val.longitude && val.status === companyType) {
              companyList.push(val)
            }
          })
          break
        case 'working':
          entity.forEach(function (val) {
            if (val.latitude && val.longitude && val.status === companyType) {
              companyList.push(val)
            }
          })
          break
      }
      let translatePoints = await this.translatePoints(companyList)
      for (let i = 0, len = companyList.length; i < len; i++) {
        companyList[i].latitude = translatePoints[i].lat
        companyList[i].longitude = translatePoints[i].lng
      }
      this.createMakers(companyList, companyType)
      // })
      // .catch(() => {})
    },
    // 创建marker点位
    createMakers: function (markers, companyType) {
      let firstPoint
      // 定位到第一个点位
      // markerIndex = 0
      let freeNum = 0,
        workingNum = 0
      markers.forEach((val, index) => {
        if (val.longitude && val.latitude) {
          if (!firstPoint) {
            // firstPoint = new BMap.Point(val.longitude, val.latitude);
            firstPoint = {
              longitude: val.longitude,
              latitude: val.latitude
            }
          }
          let marker = '',
            myIcon = '',
            point = ''
          if (val.status === 'free') {
            // markerIndex++
            // freeNum++
            myIcon = new BMap.Symbol(
              '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192"><path d="M52 69.946c9.558 0 17.334-7.857 17.334-17.513 0-9.658-7.776-17.514-17.334-17.514s-17.334 7.856-17.334 17.514c0 9.656 7.776 17.513 17.334 17.513zm0-27.027c5.146 0 9.334 4.268 9.334 9.514 0 5.245-4.188 9.513-9.334 9.513s-9.334-4.268-9.334-9.513c0-5.246 4.188-9.514 9.334-9.514zm-3.458 79.092L52 127.958l3.458-5.947c.22-.378 21.967-37.783 26.646-45.911C85.852 69.993 88 61.372 88 52.433 88 32.343 71.85 16 52 16S16 32.344 16 52.433c0 8.939 2.149 17.56 5.896 23.668 4.681 8.13 26.426 45.532 26.646 45.91zM77.523 84l-.166-.14c.31-.369.608-.752.906-1.136l-.74 1.276zM52 24c15.44 0 28 12.755 28 28.433 0 7.412-1.771 14.71-4.738 19.522l-.062.104c-3.484 6.053-16.808 28.985-23.2 39.982-6.392-10.997-19.716-33.929-23.2-39.982l-.061-.104C25.77 67.144 24 59.845 24 52.433 24 36.755 36.56 24 52 24zm115.122 83.64c2.357 4.256 4.879 10.243 4.879 16.378v28c0 7.216-.975 24-10 24H144.67l-.327-.055c-2.042-.34-8.39-2.8-9.246-19.945H56.883c-.41 7.125-2.17 18.77-9.225 19.945l-.327.055H30c-9.025 0-10-16.784-10-24v-28c0-6.135 2.522-12.122 4.879-16.378C19.262 106.223 16 101.046 16 96.018c0-3.785 1.43-6.49 3.323-8.372A13421.837 13421.837 0 0 0 26.3 99.675l6.225 10.72C30.49 113.81 28 119 28 124.017v28c0 8.987 1.612 14.536 2.644 16h15.272c.792-.922 3.084-4.581 3.084-16v-4h94.001v4c0 11.419 2.292 15.078 3.084 16h15.272c1.032-1.464 2.644-7.013 2.644-16v-28c0-7.053-5.142-15.025-7.124-17.501l-5.198-6.5H164c2.935 0 4-2.392 4-4 0-2.878-2.19-3.743-3.37-4H145.12l-.913-2.733c-.048-.144-4.876-14.47-11.363-22.81l-.132-.18c-6.365-9.195-28.566-10.54-36.582-10.278l-.127.004-.129-.004h-.009c.081-1.316.135-2.637.135-3.965 0-1.352-.07-2.698-.197-4.04l.198.005c1.107-.027 8.665-.159 17.28 1.18 12.817 1.992 21.545 6.181 25.943 12.45 5.524 7.141 9.764 17.484 11.593 22.372l14.75.04c3.609.515 10.434 3.678 10.434 11.96 0 5.028-3.262 10.205-8.879 11.622zM133 128h-17c-6.065 0-11-4.934-11-11 0-6.008 4.037-10.212 10.536-10.972 4.677-.547 16.996-2 16.996-2L133 104c6.066 0 11 4.935 11 11v2c0 6.066-4.934 11-11 11zm.187-15.994c-1.864.22-12.462 1.47-16.722 1.968C113 114.379 113 115.957 113 117c0 1.655 1.346 3 3 3h17c1.655 0 3-1.345 3-3v-2a3.003 3.003 0 0 0-2.813-2.994z"/></svg>',
              {
                // rotation : 0,// 顺时针旋转40度
                fillColor: 'rgba(43,143,83,1)',
                strokeColor: 'rgba(43,143,83,1)',
                fillOpacity: 1,
                // strokeWeight: 0,//线宽
                // // 大小
                scale: 0.17
              }
            )
          }else if (val.status === 'distributed') {
            // markerIndex++
            // freeNum++
            myIcon = new BMap.Symbol(
              '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192"><path d="M52 69.946c9.558 0 17.334-7.857 17.334-17.513 0-9.658-7.776-17.514-17.334-17.514s-17.334 7.856-17.334 17.514c0 9.656 7.776 17.513 17.334 17.513zm0-27.027c5.146 0 9.334 4.268 9.334 9.514 0 5.245-4.188 9.513-9.334 9.513s-9.334-4.268-9.334-9.513c0-5.246 4.188-9.514 9.334-9.514zm-3.458 79.092L52 127.958l3.458-5.947c.22-.378 21.967-37.783 26.646-45.911C85.852 69.993 88 61.372 88 52.433 88 32.343 71.85 16 52 16S16 32.344 16 52.433c0 8.939 2.149 17.56 5.896 23.668 4.681 8.13 26.426 45.532 26.646 45.91zM77.523 84l-.166-.14c.31-.369.608-.752.906-1.136l-.74 1.276zM52 24c15.44 0 28 12.755 28 28.433 0 7.412-1.771 14.71-4.738 19.522l-.062.104c-3.484 6.053-16.808 28.985-23.2 39.982-6.392-10.997-19.716-33.929-23.2-39.982l-.061-.104C25.77 67.144 24 59.845 24 52.433 24 36.755 36.56 24 52 24zm115.122 83.64c2.357 4.256 4.879 10.243 4.879 16.378v28c0 7.216-.975 24-10 24H144.67l-.327-.055c-2.042-.34-8.39-2.8-9.246-19.945H56.883c-.41 7.125-2.17 18.77-9.225 19.945l-.327.055H30c-9.025 0-10-16.784-10-24v-28c0-6.135 2.522-12.122 4.879-16.378C19.262 106.223 16 101.046 16 96.018c0-3.785 1.43-6.49 3.323-8.372A13421.837 13421.837 0 0 0 26.3 99.675l6.225 10.72C30.49 113.81 28 119 28 124.017v28c0 8.987 1.612 14.536 2.644 16h15.272c.792-.922 3.084-4.581 3.084-16v-4h94.001v4c0 11.419 2.292 15.078 3.084 16h15.272c1.032-1.464 2.644-7.013 2.644-16v-28c0-7.053-5.142-15.025-7.124-17.501l-5.198-6.5H164c2.935 0 4-2.392 4-4 0-2.878-2.19-3.743-3.37-4H145.12l-.913-2.733c-.048-.144-4.876-14.47-11.363-22.81l-.132-.18c-6.365-9.195-28.566-10.54-36.582-10.278l-.127.004-.129-.004h-.009c.081-1.316.135-2.637.135-3.965 0-1.352-.07-2.698-.197-4.04l.198.005c1.107-.027 8.665-.159 17.28 1.18 12.817 1.992 21.545 6.181 25.943 12.45 5.524 7.141 9.764 17.484 11.593 22.372l14.75.04c3.609.515 10.434 3.678 10.434 11.96 0 5.028-3.262 10.205-8.879 11.622zM133 128h-17c-6.065 0-11-4.934-11-11 0-6.008 4.037-10.212 10.536-10.972 4.677-.547 16.996-2 16.996-2L133 104c6.066 0 11 4.935 11 11v2c0 6.066-4.934 11-11 11zm.187-15.994c-1.864.22-12.462 1.47-16.722 1.968C113 114.379 113 115.957 113 117c0 1.655 1.346 3 3 3h17c1.655 0 3-1.345 3-3v-2a3.003 3.003 0 0 0-2.813-2.994z"/></svg>',
              {
                // rotation : 0,// 顺时针旋转40度
                fillColor: 'rgba(230, 162, 60, 1)',
                strokeColor: 'rgba(230, 162, 60, 1)',
                fillOpacity: 1,
                // strokeWeight: 0,//线宽
                // // 大小
                scale: 0.17
              }
            )
          } else if (val.status === 'working') {
            // markerIndex++
            // workingNum++
            myIcon = new BMap.Symbol(
              '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192"><path d="M160.278 81.304l-7.493 3.18c-11.63-16.435-32.477-27.417-56.218-27.417-23.92 0-44.907 11.145-56.484 27.786l-7.472-3.172C45.448 62.198 69.3 49.067 96.567 49.067c27.091 0 50.816 12.96 63.71 32.237zM136.186 91.53l7.618-3.234c-10.107-13.335-27.501-22.162-47.237-22.162-19.915 0-37.444 8.99-47.508 22.529l7.59 3.221c8.758-10.713 23.381-17.75 39.918-17.75 16.352 0 30.83 6.884 39.62 17.396zM23.512 77.819A72.736 72.736 0 0 1 33.72 64.748C49.171 49.02 71.58 40 95.195 40c31.074 0 58.471 15.304 73.114 37.895l7.462-3.168C159.873 49.29 129.58 32 95.195 32c-25.749 0-50.236 9.893-67.182 27.14a80.656 80.656 0 0 0-11.949 15.517l7.448 3.162zM148 119.999h-13.55c-1.75-3.484-5.28-10.125-8.809-14.454-7.012-9.41-27.4-9.554-29.662-9.544-2.332-.036-22.698.135-29.71 9.544-3.53 4.328-7.06 10.97-8.81 14.454H44.024a4 4 0 0 0 0 8h5.873c-2.737 2.788-5.942 7.093-5.942 12v20c0 10.168 2.916 16 8 16h12.013c1.689-.006 6.822-.958 7.818-12h48.337c.997 11.042 6.13 11.994 7.832 12h12c5.084 0 8-5.832 8-16v-20c0-4.907-3.206-9.212-5.943-12H148a4 4 0 0 0 0-8zm-8.045 40c0 4.205-.632 6.862-1.113 8h-9.718c-.41-.798-1.17-2.958-1.17-8v-4h-64v4c0 5.042-.76 7.202-1.169 8h-9.719c-.48-1.138-1.111-3.795-1.111-8v-20c0-3.138 4.273-7.255 6.334-8.752l.264-.19 4.726-4.724.298-.636c.047-.1 4.701-9.993 8.941-15.158l.138-.178c3.433-4.698 16.112-6.419 23.323-6.36 6.035-.024 19.7 1.47 23.274 6.36l.138.178c4.24 5.165 8.894 15.058 8.941 15.158l.298.637 4.497 4.494.485.414c2.07 1.502 6.343 5.62 6.343 8.757v20z"/></svg>',
              {
                // rotation : 0,// 顺时针旋转40度
                fillColor: 'rgba(245,108,108,1)',
                strokeColor: 'rgba(245,108,108,1)',
                fillOpacity: 1,
                // // strokeWeight: 3//线宽
                // // 大小
                scale: 0.2
              }
            )
          }
          point = new BMap.Point(val.longitude, val.latitude)
          // 定位到第一个点位
          // (markerIndex === 1) && this.map.centerAndZoom(point, 16);
          // this.map.centerAndZoom(point, 16)
          marker = new BMap.Marker(point, { icon: myIcon }) // 自定义标记

          // marker.setTitle(val.title)
          // marker = new BMap.Marker(point);
          this.map.addOverlay(marker)
          // let content = val.title
          // let label = new BMap.Label(content, {
          //   // 创建文本标注
          //   position: point, // 设置标注的地理位置
          //   offset: new BMap.Size(10, 20) // 设置标注的偏移量
          // })
          // this.map.addOverlay(label)
          // label.setStyle({
          //   // 设置label的样式
          //   color: '#fff',
          //   backgroundColor: 'rgba(1, 124, 142, 0.5)',
          //   fontSize: '18px',
          //   border: 'none',
          //   padding: '10px 20px',
          //   borderRadius: '10px'
          // })
          // let infoWindow = new BMap.InfoWindow(
          //   "<p style='font-size:14px;'>" + val.title + '</p>'
          // )
          // marker.openInfoWindow(infoWindow, point)
          marker.setAnimation('BMAP_ANIMATION_DROP') // 跳动的动画BMAP_ANIMATION_DROP BMAP_ANIMATION_BOUNCE
          // (index === markers.length) ? this.drawOverlay(marker, val, firstPoint) : this.drawOverlay(marker, val)
          if (index === markers.length - 1) {
            this.drawOverlay(marker, val, firstPoint)
          } else {
            this.drawOverlay(marker, val)
          }
        }
        // this.createTag(marker, val);
      })
      this.freeNum = freeNum
      this.workingNum = workingNum
    },
    // 配置点位信息
    drawOverlay(marker, val, firstPoint) {
      let _this = this

      function SquareOverlay(center, width, height, color) {
        this._center = center
        this._width = width
        this._height = height
        this._color = color
      }

      // 继承API的BMap.Overlay
      SquareOverlay.prototype = new BMap.Overlay()

      SquareOverlay.prototype.initialize = function (map) {
        // 保存map对象实例
        this._map = map
        // 创建div元素，作为自定义覆盖物的容器
        let div = document.createElement('div')
        div.style.position = 'absolute'
        div.id = val.id
        // div.entType = val.entType
        // 可以根据参数设置元素外观
        // div.style.width = this._length + "px";
        // div.style.height = this._length + "px";
        // div.style.background = this._color;
        // div.onclick=function(e){
        //     this.style.display = "none"
        // }
        // 将div添加到覆盖物容器中
        map.getPanes().markerPane.appendChild(div)
        // 保存div实例
        this._div = div
        // 需要将div元素作为方法的返回值，当调用该覆盖物的show、
        // hide方法，或者对覆盖物进行移除时，API都将操作此元素。
        return div
      }
      SquareOverlay.prototype.draw = function () {
        // 根据地理坐标转换为像素坐标，并设置给容器
        let position = this._map.pointToOverlayPixel(this._center)
        this._div.style.left = position.x - this._width / 2 + 'px'
        this._div.style.top = position.y - this._height / 2 - 70 + 'px'
      }

      // 实现显示方法
      SquareOverlay.prototype.show = function () {
        if (this._div) {
          this._div.style.display = ''
        }
      }
      // 实现隐藏方法
      SquareOverlay.prototype.hide = function () {
        if (this._div) {
          this._div.style.display = 'none'
        }
      }
      // 添加自定义方法
      SquareOverlay.prototype.toggle = function () {
        if (this._div) {
          if (this._div.style.display === '') {
            this.hide()
          } else {
            this.show()
          }
        }
      }
      let point = new BMap.Point(val.longitude, val.latitude)
      // 添加自定义覆盖物
      let mySquare = new SquareOverlay(point, 360, 260)
      _this.map.addOverlay(mySquare)
      mySquare.hide()

      
      if (firstPoint) {
        let point = new BMap.Point(firstPoint.longitude, firstPoint.latitude)
        this.map.centerAndZoom(point, 16)
      }
    },
    focusPosition(id) {
      this.list.forEach(val => {
        if (val.id === id) {
          let point = new BMap.Point(val.longitude, val.latitude)
          this.map.centerAndZoom(point, 16)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.map-overview {
  position: relative;
  .category-list {
    position: absolute;
    bottom: 0;
    right: 0;
    background: rgba(6, 30, 93, 0.6);
    padding: 10px 15px;
    font-weight: bold;
    .category-item {
      display: flex;
      line-height: 28px;
      .name {
        display: flex;
        width: 150px;
        .category-icon {
          margin-left: 7px;
          display: block;
          width: 23px;
          height: 25px;
          background: url(@/assets/images/fleetScreen/markers.png) no-repeat; //关键是这句话
        }
        .working-icon {
          background-position: 1px -275px;
        }
        .distributed-icon {
          background-position: 1px -300px;
        }
        .free-icon {
          background-position: 1px -250px;
        }
      }
      .num {
        font-size: 22px;
        // &.free {
        //   color: rgb(50, 94, 218);
        // }
        // &.working {
        //   color: rgb(237, 45, 45);
        // }
      }
    }
  }
}
#allmap {
  height: 100%;
  :deep(.anchorBL) {
    display: none;
  }
}

#mapContent {
  border: none;
  height: 100%;
  width: 100%;
  margin: 0px;
  padding: 0px;
}
</style>
