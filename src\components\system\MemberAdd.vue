<template>
    <el-drawer
            title="成员添加" ref="drawerRef"
            v-model="visible" size="50%"
            direction="rtl"
            :before-close="handleBeforeClose" destroy-on-close @open="handleOpen" @opened="handleOpened"
            @close="handleClose" @closed="handleClosed">
        <div class="drawer-body">
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container">
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button type="primary" @click="confirm">确认添加</el-button>
                </div>

            </div>
            <el-table :data="pageData.records" 
                      @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                <el-table-column header-align="left" align="left" prop="name" label="姓名"></el-table-column>
                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                <el-table-column header-align="left" align="left" label="机构">
                    <template #default="scope">
                        <template v-if="scope.row.roleListMap">
                            <el-tag type="success" v-for="item in scope.row.roleListMap['org']"
                                    :key="item.id">
                                {{item.name}}
                            </el-tag>
                        </template>
                    </template>
                </el-table-column>
                <!--                        <el-table-column header-align="left" align="left" prop="mobilePhoneNum" label="手机号码"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="telephoneNum" label="座机号码"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="email" label="电子邮箱"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="birthday" label="出生日期"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="politicsStatus" label="政治面貌"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="maritalStatus" label="婚姻状况"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="identityCardNum" label="身份证号码"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="educationDegree" label="员工获得的最高学历"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="nationCode" label="民族"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="genderCode" label="性别"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="statusCode" label="员工状态"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="residencePropertyCode" label="户口性质"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="residenceLocation" label="户口所在地"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="address" label="居住地址"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="hireDate" label="入职日期"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="nativePlaceCantonCode" label="籍贯"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="employmentTypeCode" label="用工制度"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="directSuperiorId" label="直属上级"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="resume" label="个人履历"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="partyJoinedDate" label="入党日期"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="fertilityStatusCode" label="生育情况"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="mainLanguages" label="主要语种"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="workDate" label="首次参加工作日期"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="graduatedSchool" label="毕业院校"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="schoolSystem" label="毕业学校的类型 全日制，非全日制"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="educationForm" label="员工获得教育的教学形式"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="majorType" label="员工所学专业所属类别"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="degreeType" label="员工获得的学位类型"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="facultyName" label="专业教育或高等教育所在系别"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="majorName" label="所学专业"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="credentialType" label="证件类型"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="credentialCode" label="证件号码"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="sortNum" label="顺序"></el-table-column>-->
                <!--                        <el-table-column header-align="left" align="left" prop="tenantId" label="租户id"></el-table-column>-->
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </div>

    </el-drawer>
</template>

<script>
    import {
        pageNotInWorkgroupSysRoleEmployee
    } from '@/apis/system/sys-role-employee'

    const DEFAULT_QUERY_PARAM = {
        current: 1,
        size: 10
    }
    export default {
        name: "MemberAdd",
        props: {
            modelValue: Boolean,
            addQueryParam: Object,
            data: [],
            beforeClose: Function,
            loadDataHttpMethod: Function
        },
        emits: ['open', 'close', 'opened', 'closed', 'update:modelValue', 'confirm'],
        data() {
            return {
                pageData: {
                total:0
            },
                selectedData: [],
                queryParam: DEFAULT_QUERY_PARAM
            }
        },
        computed:{
            visible:{
                                get(){
                    return this.modelValue
                },
                set(val){
                    this.$emit('update:modelValue', val)
                }            }
        },
        methods: {
            reload() {
                this.queryParam = Object.assign({},DEFAULT_QUERY_PARAM, this.addQueryParam)
                this.refresh()
            },
            refresh() {
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                if(this.loadDataHttpMethod && typeof this.loadDataHttpMethod === 'function'){
                    this.loadDataHttpMethod(this.queryParam).then(
                        res => {
                            this.pageData = res.data
                        }
                    )
                }else{
                    pageNotInWorkgroupSysRoleEmployee(this.queryParam).then(
                        res => {
                            this.pageData = res.data
                        }
                    )
                }
            },
            handleOpen() {
                this.queryParam = Object.assign({},DEFAULT_QUERY_PARAM, this.addQueryParam)
                this.query()
                this.$emit('open')
                this.$emit('update:modelValue', true)
            },
            handleOpened() {
                this.$emit('opened')
            },
            handleClose() {
                this.$refs.tableRef.clearSelection()
                this.queryParam = Object.assign({},DEFAULT_QUERY_PARAM)
                this.pageData = {}
                this.$emit('close')
                this.$emit('update:modelValue', false)
            },
            handleClosed() {
                this.$emit('closed')
            },
            handleBeforeClose(done) {
                if (this.beforeClose) {
                    this.beforeClose(done)
                } else {
                    done()
                }
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            handleSelectionChange(value) {
                this.selectedData = value
            },
            confirm() {
                // this.$refs.drawerRef.handleClose()
                this.$emit('confirm', this.selectedData)
                // this.reload()
            }
        }
    }
</script>

<style scoped lang="scss">
    .drawer-body {
        padding: 10px 20px;
    }
</style>
