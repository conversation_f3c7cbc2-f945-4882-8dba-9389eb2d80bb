<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    draggable
    destroy-on-close
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px" :hide-required-asterisk="formReadonly">
      <el-form-item :label="$t('标签Tag')" prop="name">
        <el-link style="margin-right: 20px" type="primary" :underline="false">{{ form.name }}</el-link>
        <el-button @click="chooseTags()" type="primary" id="tag" :disabled="formReadonly">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          {{ $t('选择') }}
        </el-button>
      </el-form-item>
      <el-form-item :label="$t('照片')" :prop="'fileIdList'">
        <upload-file
            v-if="form.sourceType === 'DATA_PLATFORM'"
            :disabled="formReadonly"
            ref="uploadImage"
            source-type="image_sample"
            :limit="1"
            accept=".jpg,.jpeg,.png,.gif"
            :source-id="form.id"
            v-model="form.fileIdList"
        />
        <el-image
            v-else
            :src="form.filePath ? downloadUrl + token + '&path=' + form.filePath : ''"
            :preview-src-list="[form.filePath ? downloadUrl + token + '&path=' + form.filePath : '']"
            fit="cover"
            class="upload-file"
        >
          <template #error>
            <div class="image-slot">
              <ltw-icon icon-code="el-icon-picture"></ltw-icon>
            </div>
          </template>
        </el-image>
      </el-form-item>
      <el-form-item :label="$t('描述')" prop="description">
        <ltw-input
          v-model="form.description"
          :disabled="formReadonly"
          textType="remark"
          type="textarea"
          id="remark"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
  <radio-bs-tag-group-drawer
    @drawerClick="confirmDistributeTags"
    :drawerVisible="tagDistributeDrawerVisible"
    :tagId="form.relatedId"
  ></radio-bs-tag-group-drawer>
</template>

<script>
import { saveDmImageSample, updateDmImageSample, getDmImageSample } from '@/apis/data-collect/dm-image-sample'
import UploadFile from '@/components/system/UploadFile.vue'
import RadioBsTagGroupDrawer from '@/components/basic/RadioBsTagGroupDrawer.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'

const defaultform = {
  sourceType: 'DATA_PLATFORM'
}
export default {
  name: 'AddImageSample',
  emits: ['reload'],
  components: { RadioBsTagGroupDrawer, UploadFile },
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/preview',
      token: '?token=' + util.getToken(),
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      tagDistributeDrawerVisible: false,
      rowTagList: [],
      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('标签Tag'),
            trigger: 'change'
          }
        ],
        description: [
          {
            required: true,
            message: this.$t('请填写') + this.$t('描述'),
            trigger: 'change'
          }
        ],
        fileIdList: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('文件'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增') + this.$t('示例图')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑') + this.$t('示例图')
          this.getDmImageSample(row.id)
          break
        case 'view':
          this.dialogTitle = this.$t('示例图') + this.$t('详情')
          this.getDmImageSample(row.id)
          break
      }
    },
    getDmImageSample(id) {
      getDmImageSample(id).then(res => {
        this.form = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          relatedType: 'tag'
        }
        if (this.form.fileIdList?.length) {
          if(this.form.fileIdList[0] === postData.fileId){
            delete postData.fileId
          } else {
            postData.fileId = this.form.fileIdList[0]
          }
        }
        if (!this.form.id) {
          saveDmImageSample(postData).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
          })
        } else {
          updateDmImageSample(postData).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
          })
        }
      })
    },
    chooseTags() {
      // this.tagId = this.form.relatedId
      // this.rowTagList = this.form.rowTagList
      this.tagDistributeDrawerVisible = true
      // this.rowTagList = this.form.tagList || []
    },
    confirmDistributeTags(data) {
      // if (data && data.tagList && data.tagList.length) {
      //   this.form.tagList = []
      //   data.tagList.forEach(tagGroup => {
      //     this.saveCheckedTagList(tagGroup)
      //   })
      // }
      // this.tagDistributeDrawerVisible = false
      // //找到标签组的父节点
      // // this.getTags()
      // console.log(data)
      if (data?.tagItem) {
        this.form.name = data?.tagItem?.nameCn
        this.form.relatedId = data?.tagItem?.id
        this.form.relatedCode = data?.tagItem?.code
      }
      // this.form.rowTagList = [{ ...data?.tagItem }]
      // this.tagId = data?.tagItem?.id
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      //遍历查询选中的标签
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.form.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.upload-file {
  height: 148px;
  width: 148px;
}
</style>
