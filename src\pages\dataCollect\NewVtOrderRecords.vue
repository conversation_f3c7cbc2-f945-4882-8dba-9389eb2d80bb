<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <el-input
            :placeholder="$t('请输入关键字')"
            v-model="queryParam.key"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon
                icon-code="el-icon-arrow-down"
                class="el-icon--right"
              ></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <!-- <div class="demo-app-sidebar">
        <div class="demo-app-sidebar-section">
          <h2>Instructions</h2>
          <ul>
            <li>Select dates and you will be prompted to create a new event</li>
            <li>Drag, drop, and resize events</li>
            <li>Click an event to delete it</li>
          </ul>
        </div>
        <div class="demo-app-sidebar-section">
          <label>
            <input
              type="checkbox"
              :checked="calendarOptions.weekends"
              @change="handleWeekendsToggle"
            />
            toggle weekends
          </label>
        </div>
        <div class="demo-app-sidebar-section">
          <h2>All Events ({{ currentEvents.length }})</h2>
          <ul>
            <li v-for="event in currentEvents" :key="event.id">
              <b>{{ event.startStr }}</b>
              <i>{{ event.title }}</i>
            </li>
          </ul>
        </div>
      </div> -->
      <div class="demo-app-main">
        <FullCalendar
          ref="fullcalendar"
          class="demo-app-calendar"
          :options="calendarOptions"
        >
          <template v-slot:eventContent="arg">
            <b>{{ arg.timeText }}</b>
            <i>{{ arg.event.title }}</i>
          </template>
        </FullCalendar>
      </div>
    </el-card>
  </div>
  <add-order-record
    @getCollectRecord="getCollectRecord"
    @reload="query"
    ref="AddOrderRecord"
  />
  <add-collect-record-dialog ref="AddCollectRecordDialog" @reload="query" />
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  showToast,
  showConfirmToast,
  getLocale,
  dateUtils
} from '@/plugins/util'
import { deleteFtmTask, pageFtmTask } from '@/apis/data-collect/vt-order-records'
import { updateFtmTask } from '@/apis/data-collect/vt-order-records'
import FullCalendar from '@fullcalendar/vue3' //FullCalendar 的 vue 组件
import dayGridPlugin from '@fullcalendar/daygrid' //月视图插件(按需安装)
import timeGridPlugin from '@fullcalendar/timegrid' //周视图和日视图插件(按需安装)
import interactionPlugin from '@fullcalendar/interaction' //接口插件(按需安装)
// import listPlugin from '@fullcalendar/list'   //列表视图插件(按需安装)

import AddOrderRecord from '@/pages/dataCollect/dialog/AddNewOrderRecord.vue'
import AddCollectRecordDialog from '@/pages/dataCollect/dialog/AddOrderCollectRecordDialog.vue'

export default {
  name: 'VtOrderRecords',
  components: { AddOrderRecord, FullCalendar, AddCollectRecordDialog },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      selectedData: [],
      calendarOptions: {
        plugins: [
          dayGridPlugin,
          timeGridPlugin,
          interactionPlugin // needed for dateClick
          // listPlugin
        ],
        initialView: 'dayGridMonth',
        locale: getLocale(),
        firstDay: 1,
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        initialEvents: [], // alternatively, use the `events` setting to fetch from a feed
        editable: true,
        selectable: true,
        selectMirror: true,
        dayMaxEvents: true,
        weekends: true,
        select: this.handleDateSelect,
        eventClick: this.handleEventClick, // 点击任务触发
        eventsSet: this.handleEvents,
        eventDrop: this.handleEventDrop,
        slotEventOverlap: false
        /* you can update a remote database when these fire:
        eventAdd:
        eventChange:
        eventRemove:
        */
      },
      currentEvents: []
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].dataOperatePermission
    }
    this.query()
  },
  methods: {
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.query()
    },
    query() {
      pageFtmTask(this.queryParam).then(res => {
        // console.log(this.$refs.fullcalendar)
        // console.log(this.$refs.fullcalendar.getApi())
        // this.calendarOptions.initialEvents.push()
        let eventList = []
        res.data.records.forEach(val => {
          eventList.push({
            id: val.id,
            title: val.complianceOfficerEmpName + ' - ' + val.vin,
            start: val.expectedStartTime,
            end: val.expectedEndTime
          })
        })
        this.calendarOptions.events = eventList
        // this.pageData = res.data
      })
    },
    add(data) {
      this.$refs.AddOrderRecord.show({ type: 'add', data })
    },
    edit(row) {
      this.$refs.AddOrderRecord.show({ type: 'edit', id: row.id })
    },
    view(row) {
      this.$refs.AddOrderRecord.show({ type: 'view', id: row.id })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmTask(param).then(() => {
          this.query()
        })
      })
    },
    handleWeekendsToggle() {
      this.calendarOptions.weekends = !this.calendarOptions.weekends // update a property
    },
    handleDateSelect(selectInfo) {
      if (
        new Date(selectInfo.startStr).getTime() >=
        new Date(
          dateUtils.parseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00'
        ).getTime()
      ) {
        if (selectInfo.allDay) {
          this.add({
            expectedDate: selectInfo.startStr,
            startTime: '00:00:00',
            endTime: '23:30:00'
          })
        } else {
          let startDateTime = dateUtils.parseTime(
            new Date(selectInfo.startStr),
            '{y}-{m}-{d} {h}:{i}:{s}'
          )
          let startDate = startDateTime.split(' ')[0]
          let startTime = startDateTime.split(' ')[1]
          let endTime = dateUtils.parseTime(
            new Date(selectInfo.endStr),
            '{h}:{i}:{s}'
          )
          this.add({ expectedDate: startDate, startTime, endTime })
        }
      }
      // let title = prompt('Please enter a new title for your event')
      // let calendarApi = selectInfo.view.calendar

      // calendarApi.unselect() // clear date selection

      // if (title) {
      //   calendarApi.addEvent({
      //     id: createEventId(),
      //     title,
      //     start: selectInfo.startStr,
      //     end: selectInfo.endStr,
      //     allDay: selectInfo.allDay
      //   })
      // }
    },
    handleEventClick(clickInfo) {
      // this.singleRemove(clickInfo.event)

      this.$refs.AddOrderRecord.show({ type: 'view', id: clickInfo.event.id })
      // if (
      //   confirm(
      //     `Are you sure you want to delete the event '${clickInfo.event.title}'`
      //   )
      // ) {
      //   clickInfo.event.remove()
      // }
    },
    handleEvents(events) {
      // this.currentEvents = events
    },
    handleEventDrop(events) {
      let startDateTime = dateUtils.parseTime(
        new Date(events.event.startStr),
        '{y}-{m}-{d} {h}:{i}:{s}'
      )
      let startDate = startDateTime.split(' ')[0]
      let startTime = startDateTime.split(' ')[1]
      let endTime = dateUtils.parseTime(
        new Date(events.event.endStr),
        '{h}:{i}:{s}'
      )

      let postData = {
        id: events.event.id,
        expectedStartTime: startDate + ' ' + startTime,
        expectedEndTime: startDate + ' ' + endTime
      }
      updateFtmTask(postData).catch(err => {
        events.revert()
      })
    },
    getCollectRecord(row) {
      this.$refs.AddCollectRecordDialog.show({
        type: 'view',
        data: {
          taskCode: row.code
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
.demo-app-main {
  width: 80%;
}
</style>
