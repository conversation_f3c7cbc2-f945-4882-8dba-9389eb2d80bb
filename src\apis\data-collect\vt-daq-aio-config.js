import { httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveAioConfig = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config',
    data,
    params
  })
export const updateAioConfig = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config',
    data,
    params
  })
export const getAioConfigDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config',
    params
  })
export const previewAioConfigDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config/preview',
    params,
    responseType: 'blob'
  })
export const getAioConfigWhiteList = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config/whiteList',
    data,
    params
  })
export const getAioConfigSoftwarePackage = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/task_aio_config/softwarePackage',
    data,
    params
  })
