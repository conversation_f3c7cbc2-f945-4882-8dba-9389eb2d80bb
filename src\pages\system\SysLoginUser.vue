<template>
  <div>
    <div class="main-container">
      <div class="org-tree-container">
        <org-tree
          @org-click="handleOrgClick"
          ref="userOrgTree"
          :default-selected-org="currentUser.currentTenantId"
        ></org-tree>
      </div>
      <div class="content-container">
        <el-card>
          <div class="ltw-toolbar">
            <div class="ltw-search-container ltw-tool-container">
              <ltw-input
                placeholder="请输入名称"
                v-model="queryParam.key"
                clearable
                @clear="refresh"
              >
                <template #append>
                  <el-button @click="refresh">
                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                  </el-button>
                </template>
              </ltw-input>
            </div>
            <div class="ltw-tool-container button-group">
              <el-button
                :type="item.buttonStyleType"
                :key="item.id"
                v-for="item in outlineFunctionList"
                @click="executeButtonMethod(item.buttonCode)"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                {{ item.name }}
              </el-button>
              <el-dropdown
                @command="handleCommand"
                class="batch-operate-btn"
                v-if="batchingFunctionList && batchingFunctionList.length > 0"
              >
                <el-button type="primary">
                  批量操作
                  <ltw-icon
                    icon-code="el-icon-arrow-down"
                    class="el-icon--right"
                  ></ltw-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :key="item.id"
                      v-for="item in batchingFunctionList"
                      :command="item.buttonCode"
                    >
                      <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                      {{ item.name }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <el-table
            :data="pageData.records"
            @selection-change="handleSelectionChange"
            row-key="id"
            ref="tableRef"
          >
            <el-table-column
              header-align="left"
              align="left"
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="userName"
              label="姓名"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="loginName"
              label="账号"
            ></el-table-column>
            <el-table-column header-align="left" align="left" label="机构">
              <template #default="scope">
                <template v-if="scope.row.roleListMap">
                  <el-tag
                    type="success"
                    v-for="item in scope.row.roleListMap[ROLE_TYPE_ORG]"
                    :key="item.id"
                    >{{ item.name }}</el-tag
                  >
                </template>
              </template>
            </el-table-column>
            <el-table-column header-align="left" align="left" label="角色">
              <template #default="scope">
                <template v-if="scope.row.roleListMap">
                  <el-tag
                    type="success"
                    v-for="item in scope.row.roleListMap[
                      ROLE_TYPE_CUSTOM_ACTOR
                    ]"
                    :key="item.id"
                    >{{ item.name }}</el-tag
                  >
                </template>
              </template>
            </el-table-column>
            <!--                <el-table-column header-align="left" align="left" prop="saltValue" label="加密盐值"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="password" label="密码"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="wxOpenId" label="微信用户id"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="type" label="用户类型"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="mobilephoneNum" label="手机号码"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="email" label="电子邮箱"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="deviceNum" label="绑定设备数"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="mobileAuthenticated" label="手机号码是否验证"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="emailAuthenticated" label="电子邮箱是否验证"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="empId" label="员工id"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="status" label="状态"></el-table-column>-->
            <el-table-column
              header-align="left"
              align="left"
              label="操作"
              min-width="180"
            >
              <template #default="scope">
                <el-button-group>
                  <template :key="item.id" v-for="item in inlineFunctionList">
                    <el-tooltip
                      :key="item.id"
                      effect="dark"
                      :content="item.name"
                      placement="top"
                      :enterable="false"
                      v-if="checkButtonVisble(item, scope.row)"
                    >
                      <el-button
                        :type="item.buttonStyleType"
                        @click="executeButtonMethod(item.buttonCode, scope.row)"
                      >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParam.current"
            :page-sizes="[5, 10, 20, 30]"
            :page-size="queryParam.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageData.total"
          ></el-pagination>
        </el-card>
      </div>
    </div>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="userName">
          <ltw-input
            v-model="formData.userName"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="账号" prop="loginName">
          <ltw-input
            v-model="formData.loginName"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <ltw-input text-type="description" v-model="formData.email" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item label="机构">
          <template
            v-if="
              dialogStatus !== 'add' &&
              formData.roleListMap &&
              formData.roleListMap[ROLE_TYPE_ORG]
            "
          >
            <el-tag
              type="success"
              v-for="org in formData.roleListMap[ROLE_TYPE_ORG]"
              :key="org.id"
              >{{ org.name }}</el-tag
            >
          </template>
          <template v-if="dialogStatus === 'add'">
            <el-tag type="success">{{ currentOrg.name }}</el-tag>
          </template>
        </el-form-item>
        <el-form-item label="角色" v-if="dialogStatus !== 'add'">
          <template
            v-if="
              dialogStatus !== 'add' &&
              formData.roleListMap &&
              formData.roleListMap[ROLE_TYPE_CUSTOM_ACTOR]
            "
          >
            <el-tag
              type="success"
              v-for="actor in formData.roleListMap[ROLE_TYPE_CUSTOM_ACTOR]"
              :key="actor.id"
              >{{ actor.name }}</el-tag
            >
          </template>
        </el-form-item>
        <!--                <el-form-item label="加密盐值" prop="saltValue">-->
        <!--                    <ltw-input v-model="formData.saltValue" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="密码" prop="password">-->
        <!--                    <ltw-input v-model="formData.password" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="微信用户id" prop="wxOpenId">-->
        <!--                    <ltw-input v-model="formData.wxOpenId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="用户类型" prop="type">-->
        <!--                    <ltw-input v-model="formData.type" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="手机号码" prop="mobilephoneNum">-->
        <!--                    <ltw-input v-model="formData.mobilephoneNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="电子邮箱" prop="email">-->
        <!--                    <ltw-input v-model="formData.email" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="绑定设备数" prop="deviceNum">-->
        <!--                    <ltw-input v-model="formData.deviceNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="手机号码是否验证" prop="mobileAuthenticated">-->
        <!--                    <ltw-input v-model="formData.mobileAuthenticated" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="电子邮箱是否验证" prop="emailAuthenticated">-->
        <!--                    <ltw-input v-model="formData.emailAuthenticated" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工id" prop="empId">-->
        <!--                    <ltw-input v-model="formData.empId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="编码" prop="code">-->
        <!--                    <ltw-input v-model="formData.code" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="状态" prop="status">-->
        <!--                    <ltw-input v-model="formData.status" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >关 闭</el-button
          >
          <template v-else>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="角色分配"
      v-model="actorDialogVisible"
      @close="actorDialogClosed"
    >
      <div class="actor-container">
        <el-transfer
          v-model="selectedActors"
          style="text-align: left; display: inline-block"
          :titles="['待分配', '已分配']"
          :button-texts="['删除', '添加']"
          :data="actors"
          :props="transferProps"
        >
          <template #default="{ option }">
            <span>{{ option.name }}</span>
          </template>
        </el-transfer>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="actorDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveActors">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { showToast } from '@/plugins/util'
import {
  addActors,
  assignActors,
  deleteSysLoginUser,
  getSysLoginUser,
  pageSysLoginUser,
  saveSysLoginUser,
  updateSysLoginUser,
  auditReject,
  auditPass
} from '@/apis/system/sys-login-user'
import { listSysRoleCustomActor } from '@/apis/system/sys-role-custom-actor'
import OrgTree from '@/components/system/OrgTree'
import ROLE_TYPE from '@/plugins/constants/role-type'
import { resetPassword } from '../../apis/system/sys-login-user'

const defaultFormData = {}
export default {
  name: 'SysLoginUser',
  components: { 'org-tree': OrgTree },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      ROLE_TYPE_CUSTOM_ACTOR: ROLE_TYPE.CUSTOM_ACTOR,
      ROLE_TYPE_ORG: ROLE_TYPE.ORG,
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        userName: [
          { required: true, message: 'Please input user name', trigger: 'blur' }
        ],
        loginName: [
          {
            required: true,
            message: 'Please input login name',
            trigger: 'blur'
          }
        ],
        email: [
          { required: true, message: 'Please input email', trigger: 'blur' }
        ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      operateType: 'single',
      actors: [],
      selectedActors: [],
      actorDialogVisible: false,
      transferProps: {
        key: 'id',
        label: 'name'
      },
      currentOperatedUser: undefined,
      currentUser: {},
      currentOrg: {}
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
    this.currentUser = this.$store.state.permission.currentUser
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageSysLoginUser(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加登录用户'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formData.orgId = this.currentOrg.id
    },
    save() {
      if (this.dialogStatus === 'add') {
        saveSysLoginUser(this.formData).then(() => {
          this.dialogVisible = false
          this.query()
        })
      }
      if (this.dialogStatus === 'edit') {
        updateSysLoginUser(this.formData).then(() => {
          this.dialogVisible = false
          this.query()
        })
      }
    },
    edit(row) {
      this.dialogTitle = '修改登录用户'
      this.dialogStatus = 'edit'
      getSysLoginUser(row.id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          this.formData = res.data
        })
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      this.operateType = 'multi'
      if (command === 'batchRemove') {
        this.batchRemove()
      }
      if (command === 'batchActorAssign') {
        this.openActorAssignDialog()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysLoginUser(param).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    view(row) {
      this.dialogTitle = '查看登录用户'
      this.dialogStatus = 'view'
      getSysLoginUser(row.id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          this.formData = res.data
        })
      })
    },
    resolve(row) {
      auditPass(row.id).then(res => {
        showToast('用户已通过')
        this.query()
      })
    },
    reject(row) {
      auditReject(row.id).then(res => {
        showToast('用户已驳回')
        this.query()
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    handleOrgClick(org) {
      this.currentOrg = org
      this.queryParam.orgId = undefined
      this.queryParam.orgCodeLink = undefined
      if (org.asLeaf) {
        this.queryParam.orgId = org.id
      } else {
        this.queryParam.orgCodeLink = org.codeLink
      }
      this.query()
    },
    actorAssign(row) {
      this.operateType = 'single'
      if (
        row.roleListMap &&
        row.roleListMap[this.ROLE_TYPE_CUSTOM_ACTOR] &&
        row.roleListMap[this.ROLE_TYPE_CUSTOM_ACTOR].length
      ) {
        row.roleListMap[this.ROLE_TYPE_CUSTOM_ACTOR].forEach(ele => {
          this.selectedActors.push(ele.id)
        })
      }
      this.openActorAssignDialog()
      this.currentOperatedUser = row
    },
    getActors() {
      listSysRoleCustomActor({
        tenantId: this.currentUser.currentTenantId
      }).then(res => {
        this.actors = res.data
      })
    },
    openActorAssignDialog() {
      this.actorDialogVisible = true
      this.getActors()
    },
    actorDialogClosed() {
      this.currentOperatedUser = undefined
      this.selectedActors = []
      this.actors = []
    },
    saveActors() {
      if (this.operateType === 'single') {
        let data = {
          roleId: this.currentOperatedUser.empId,
          parentRoleIdList: this.selectedActors
        }
        assignActors(data).then(() => {
          this.actorDialogVisible = false
          this.query()
        })
      }
      if (this.operateType === 'multi') {
        let selectedUserEmpIds = []
        this.selectedData.forEach(user => selectedUserEmpIds.push(user.empId))
        let data = {
          roleIdList: selectedUserEmpIds,
          parentRoleIdList: this.selectedActors
        }
        addActors(data).then(() => {
          this.actorDialogVisible = false
          this.query()
        })
      }
    },
    resetPassword({ id }) {
      resetPassword(id).then(() => {
        this.$message.success('该用户密码重置成功')
      })
    },
    checkButtonVisble(button, row) {
      if (button.buttonCode === 'resolve' && row.status !== 'unaudited') {
        return false
      } else if (button.buttonCode === 'reject' && row.status !== 'unaudited') {
        return false
      }
      return true
      // if (
      //   button.buttonCode === 'edit' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.DRAFT &&
      //   row.empId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'singleRemove' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.DRAFT &&
      //   row.empId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'publish' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.DRAFT &&
      //   row.empId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'reject' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.PUBLISHED &&
      //   row.recipientEmpId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'resolve' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.PUBLISHED &&
      //   row.recipientEmpId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'disassemble' &&
      //   (row.status === DAQ_REQUIREMENT_STATUS.ACCEPTED ||
      //     row.status === DAQ_REQUIREMENT_STATUS.BROKENDOWN) &&
      //   row.recipientEmpId === this.empId
      // ) {
      //   return true
      // } else if (
      //   button.buttonCode === 'finish' &&
      //   row.status === DAQ_REQUIREMENT_STATUS.EXECUTING &&
      //   row.recipientEmpId === this.empId
      // ) {
      //   return true
      // }
      // return false
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin-left: 5px;
  margin-bottom: 5px;
}

.actor-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-transfer-panel {
    width: 200px;
  }
}

.main-container {
  display: flex;
  flex-direction: row;

  .org-tree-container {
    width: 280px;
    margin-right: 20px;
  }

  .content-container {
    width: calc(100% - 300px);
  }
}
</style>
