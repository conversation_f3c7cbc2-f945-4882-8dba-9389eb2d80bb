import { ElMessage } from 'element-plus'
var websock = null //websocket实例
let rec //断线重连后，延迟5秒重新创建WebSocket连接  rec用来存储延迟请求的代码
let isConnect = false //连接标识 避免重复连接
let checkMsg = 'heartbeat' //心跳发送/返回的信息 服务器和客户端收到的信息内容如果如下 就识别为心跳信息 不要做业务处理
let wsUrl = null //ws连接地址
let recNum = 5,  //最多连接5次
  nowNum = 0,
  recTime = 1000,
  recSend = null
// 连接成功执行回调
var globalCallback = function () {}

var globalCloseCallback = function () {}

let createWebSocket = url => {
  wsUrl ??= url
  if (window.WebSocket) {
    try {
      // var ws = "ws://192.168.1.22:18308/chat";
      if (!websock) {
        websock = new WebSocket(wsUrl)
        initWebSocket() //初始化websocket连接
      }
    } catch (e) {
      console.log('尝试创建连接失败')
      reConnect() //如果无法连接上webSocket 那么重新连接！可能会因为服务器重新部署，或者短暂断网等导致无法创建连接
    }
  } else {
    ElMessage.error({
      message: '您的浏览器不支持websocket,请升级或更换浏览器！',
      type: 'error',
      center: true
    })
    return
  }
}

//定义重连函数
let reConnect = () => {
  nowNum++
  console.log('尝试重新连接')
  if (isConnect) return //如果已经连上就不在重连了
  rec && clearTimeout(rec)
  if (nowNum < recNum) {
    rec = setTimeout(function () {
      // 没连接上会一直重连，设置延迟避免请求过多
      // 延迟5秒重连  避免过多次过频繁请求重连
      createWebSocket()
    }, recTime)
  } else {
    nowNum = 0
  }
}
//设置关闭连接
let closeWebSocket = () => {
  websock.close()
}
//心跳设置
var heartCheck = {
  timeout: 20000, //每段时间发送一次心跳包 这里设置为20s
  timeoutObj: null, //延时发送消息对象（启动心跳新建这个对象，收到消息后重置对象）

  start: function () {
    this.timeoutObj = setTimeout(function () {
      if (isConnect) websock.send(checkMsg)
    }, this.timeout)
  },

  reset: function () {
    clearTimeout(this.timeoutObj)
    this.start()
  },

  stop:function(){
    clearTimeout(this.timeoutObj)
  }
}

// 初始化websocket
function initWebSocket() {
  websocketonmessage()
  websocketclose()
  websocketOpen()
  socketOnError()
}
// 0 CONNECTING
// 正在建立连接连接，还没有完成。
// 1 OPEN
// 连接成功建立，可以进行通信。
// 2 CLOSING
// 连接正在进行关闭握手，即将关闭。
// 3 CLOSED
// 实际调用的方法
function sendSock(agentData, callback, errCallback) {
  // this.isConnect = true
  globalCallback = callback
  globalCloseCallback = errCallback
  if (websock.readyState === websock.OPEN) {
    // 若是ws开启状态
    websocketsend(agentData)
  } else if (websock.readyState === websock.CONNECTING) {
    // 若是 正在开启状态，则等待1s后重新调用
    setTimeout(function () {
      sendSock(agentData, callback, errCallback)
    }, 1000)
  } else {
    createWebSocket()
    // 若未开启 ，则等待5s后重新调用
    recSend && clearTimeout(recSend)
    if (nowNum < recNum) {
      nowNum++
      recSend = setTimeout(function () {
        sendSock(agentData, callback, errCallback)
      }, recTime)
    } else {
      nowNum = 0
    }
  }
}

function getSock(callback) {
  globalCallback = callback
}
// 数据接收
function websocketonmessage() {
  websock.onmessage = e => {
    globalCallback(JSON.parse(e.data))

    //   let O_o = JSON.parse(decodeUnicode(e.data))

    //   if (!O_o) {
    //     heartCheck.reset()
    //   } else {
    //     if (O_o.msg == 'open success') {
    //       sessionStorage.setItem('wid', O_o.wid)
    //     } else {
    //       console.log(O_o)
    //       globalCallback(O_o)
    //     }
    //   }
  }

  // // globalCallback(JSON.parse(e.data))
  // function decodeUnicode(str) {
  //   str = str.replace(/\\/g, '%')
  //   //转换中文
  //   str = unescape(str)
  //   //将其他受影响的转换回原来
  //   str = str.replace(/%/g, '\\')
  //   //对网址的链接进行处理
  //   str = str.replace(/\\/g, '')
  //   return str
  // }
}

// 数据发送
function websocketsend(agentData) {
  websock.send(JSON.stringify(agentData))
}

// 关闭
function websocketclose() {
  websock.onclose = e => {
    isConnect = false //断开后修改标识
    console.log('connection closed (' + e.code + ')')
    websock = null
    globalCloseCallback()
  }
}

// 创建 websocket 连接
function websocketOpen() {
  websock.onopen = () => {
    console.log('WebSocket连接成功')
  }
}

function socketOnError() {
  websock.onerror = () => {
    console.log('WebSocket连接发生错误')
    isConnect = false //连接断开修改标识
    // sendsock时未连接，此处不进行重连
    if (!recSend) {
      reConnect() //连接错误 需要重连
    }
  }
}

// 将方法暴露出去
export { sendSock, getSock, createWebSocket, closeWebSocket }
