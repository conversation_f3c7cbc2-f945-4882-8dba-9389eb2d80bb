import {httpGet, httpDelete} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getMyApplyApiList = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/list/apply/current_user', params})
export const getApplyApiPrivileges = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/page/apply/current_user', params})
export const deleteApiPrivileges = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges', params})
export const tokenCreate = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/token/create', params})
export const tokenUpdate = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/token/update', params})
export const tokenView = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/token/view', params})
