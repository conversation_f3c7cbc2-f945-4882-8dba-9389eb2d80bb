<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="70%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <div class="step1">
      <div class="header-title">
        <div class="ltw-toolbar">
          <div class="left-button">
            <dictionary-type-selection
              size="small"
              v-model="queryParam.sensorType"
              :placeholder="$t('请选择') + $t('传感器类型')"
              dictionaryType="sensor_type"
              @change="changeSensorType"
              filterable
              clearable
            />
            <common-selection
              size="small"
              v-model="queryParam.modality"
              :model-code="'code'"
              :model-name="'name,code'"
              :model-options="modalityData"
              :placeholder="$t('请选择') + $t('传感器')"
              @change="query"
              clearable
            />
            <el-button size="small" type="primary" @click="addSoftVersion">
              <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              {{ $t('新增') }}
            </el-button>
          </div>
        </div>
      </div>

      <el-table height="400px" :data="pageData.records" ref="tableRef">
        <el-table-column header-align="left" align="left" :label="$t('传感器类型')" prop="sensorTypeName" fixed="left">
          <template #default="scope">
            <el-link type="primary" :underline="false" @click="viewSoftVersion(scope.row)"
              >{{ scope.row.sensorTypeName || '整车' }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('传感器')" prop="modalityName" fixed="left">
          <template #default="scope">
            {{ (scope.row.modality || '') + (scope.row.modalityName ? '-' + scope.row.modalityName : '') }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('软件版本')" prop="softwareVersion" />
        <el-table-column header-align="left" align="left" :label="$t('ASW版本')" prop="aswVersion" />
        <el-table-column header-align="left" align="left" :label="$t('BSW版本')" prop="bswVersion" />
        <el-table-column header-align="left" align="left" :label="$t('开始日期')" prop="startDate">
          <template #default="scope">
            <el-tag>{{ scope.row.startDate }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('结束日期')" prop="endDate">
          <template #default="scope">
            <el-tag>{{ scope.row.endDate }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('备注')" prop="remark" show-overflow-tooltip />
        <el-table-column
          header-align="left"
          align="left"
          fixed="right"
          :label="$t('操作')"
          width="120"
          v-if="dialogStatus !== 'view_modality'"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip effect="dark" :content="$t('编辑')" placement="top" :enterable="false">
                <el-button type="warning" @click="editSoftVersion(scope.row)">
                  <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                <el-button type="danger" @click="singleRemove(scope.row)">
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
    <AddSoftVersion @reload="query" ref="AddSoftVersion" />
  </el-dialog>
</template>

<script>
import { pageVehicleSoftwareVersions, deleteVehicleSoftwareVersions } from '@/apis/fleet/ftm-software-versions'
import { listFtmVehicleModality } from '@/apis/fleet/ftm-vehicle-modality'
import { showConfirmToast } from '@/plugins/util'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection'
import CommonSelection from '@/components/system/CommonSelection'
import { i18n } from '@/plugins/lang'
import AddSoftVersion from '@/pages/fleet/dialog/AddSoftVersion'
import BASE_CONSTANT from '@/plugins/constants/base-constant'

export default {
  name: 'AddDriver',
  emits: ['reload'],
  components: {
    DictionaryTypeSelection,
    AddSoftVersion,
    CommonSelection
  },
  data() {
    return {
      vin: '',
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      modalityData: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  created() {
    // this.listInstallPostionList();
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.vin = row.vin
      switch (row.type) {
        case 'view':
          this.dialogTitle = i18n.global.t('软件版本')
          this.query()
          this.listFtmVehicleModality()
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {},
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageVehicleSoftwareVersions({
        ...this.queryParam,
        vin: this.vin
      }).then(res => {
        this.pageData = res.data
      })
    },
    addSoftVersion() {
      this.$refs.AddSoftVersion.show({
        type: 'add',
        data: { vin: this.vin }
      })
    },
    editSoftVersion(row) {
      this.$refs.AddSoftVersion.show({
        type: 'edit',
        data: { id: row.id }
      })
    },
    viewSoftVersion(row) {
      this.$refs.AddSoftVersion.show({
        type: 'view',
        data: { id: row.id }
      })
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteVehicleSoftwareVersions(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    listFtmVehicleModality() {
      listFtmVehicleModality({ sensorType: this.queryParam.sensorType }).then(res => {
        this.modalityData = res.data
      })
    },
    changeSensorType() {
      this.queryParam.modality = ''
      this.listFtmVehicleModality()
      this.query()
    }
  }
}
</script>

<style scoped lang="scss">
.step-content {
  margin-bottom: 20px;
}

.dialog-footer {
  display: block;
  width: 100%;
  text-align: center;

  .el-button {
    margin-right: 10px;
  }

  .el-button + .el-button {
    margin-right: 10px;
    margin-left: 10px;
  }
}

.template-tabs {
  :deep(.el-tabs__content) {
    .json-editor-vue {
      height: 300px;

      .jsoneditor-poweredBy {
        display: none;
      }

      .jsoneditor-outer.has-main-menu-bar,
      .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
        height: 100%;
        margin: 0;
        padding: 0;
      }

      .jsoneditor-menu,
      .jsoneditor-navigation-bar {
        display: none;
      }
    }
  }
}

.ltw-toolbar {
  display: flex;

  .left-button {
    display: flex;

    :deep(.el-select) {
      margin: 0 10px;
    }
  }
}
</style>
