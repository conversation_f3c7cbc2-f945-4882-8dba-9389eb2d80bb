<template>
  <el-dialog
    :title="titleText"
    :model-value="visible"
    width="800px"
    draggable
    destroy-on-close
    @open="dialogOpened"
    @close="dialogClosed"
    class="dialog-container"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('编码')" prop="code">
        <el-input v-model="formData.code" :disabled="formReadonly" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <el-input v-model="formData.name" :disabled="formReadonly" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item :label="$t('数采类型')" prop="acquisitionType">
        <el-radio-group v-model="formData.acquisitionType">
          <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
            >{{ $t(item.name) }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('描述')" prop="description">
        <ltw-input v-model="formData.description" :disabled="formReadonly" text-type="remark"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('是否为父层级')" prop="isParent">
        <el-switch v-model="formData.isParent" :disabled="formReadonly" @change="handleParentChange"></el-switch>
      </el-form-item>
      <template v-if="formData.isParent">
        <el-form-item :label="$t('子标签分类')" prop="childTagGroupList">
          <el-button type="primary" :disabled="formReadonly" @click="handleAddChild">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('新增') }}
          </el-button>
        </el-form-item>
        <tag-child-table
          :formData="formData"
          :formReadonly="formReadonly"
          :treeTagGroupList="treeTagGroupList"
          :status="status"
          @getTags="getTags"
          @chooseChildTags="chooseChildTags"
        ></tag-child-table>
      </template>
      <el-form-item v-else :label="$t('标签数量')" prop="tagList">
        <el-link @click="getTags()" style="margin-right: 20px" type="primary" :underline="false"
          >{{ (formData.tagList && formData.tagList.length) || 0 }}
        </el-link>
        <el-button @click="chooseTags()" type="primary" id="tag" :disabled="formReadonly">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          {{ $t('选择') }}
        </el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="status === 'view'">
          <el-button @click="visible = false" v-if="status === 'view'">{{ $t('关 闭') }}</el-button>
          <el-button
            :type="item.buttonStyleType"
            @click="executeButtonMethod(currentButton)"
            v-if="currentButton && currentButton.name"
            >{{ $t(currentButton.name) }}</el-button
          >
        </template>
        <template v-else>
          <el-button @click="visible = false">{{ $t('取 消') }}</el-button>
          <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
  <tag-list ref="TagList" />
  <el-dialog v-model="tagCardVisible">
    <bs-tag-group-panel
      :continuous-units="continuousUnits"
      :edit-tags="editTags"
      :timeEdit="timeEdit"
      :data="tagsData"
      @tagClose="tagClose"
      @tagSave="tagSave"
      :classificationTag="true"
    ></bs-tag-group-panel>
    <!-- <tag-group-panel :data="tagsData"></tag-group-panel> -->
  </el-dialog>
  <bs-tag-group-drawer
    @drawerClick="confirmDistributeTags"
    :enabled="true"
    :drawerVisible="tagChooseDrawerVisible"
    :rowTagList="rowTagList"
  ></bs-tag-group-drawer>
</template>
<script>
import TagChildTable from './TagChildTable.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import TagList from '@/pages/dataCollect/dialog/TagList'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { saveBsTagClassification, updateBsTagClassification } from '@/apis/data-collect/bs-tag-classification'
import { getAccessRole } from '@/apis/system/sys-login-user'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import { cloneDeep } from 'lodash'

const DEFAULT_FORM_DATA = {
  code: '',
  name: '',
  acquisitionType: '',
  description: '',
  isParent: false,
  tagList: [],
  childTagGroupList: []
}
export default {
  name: 'TagClassification',
  components: {
    EmployeeSelection,
    TagList,
    BsTagGroupDrawer,
    DictionarySelection,
    BsTagGroupPanel,
    TagChildTable
  },
  emits: ['update:modelValue', 'save'],
  props: {
    title: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: 'view'
    },
    modelValue: {
      type: Object,
      default: () => {
        return cloneDeep(DEFAULT_FORM_DATA)
      }
    },
    formReadonly: {
      type: Boolean,
      default: true
    },
    treeTagGroupList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      visible: false,
      tagChooseDrawerVisible: false,
      tagList: [],
      rowTagList: [],
      accessRoleOptions: [],
      accessRoleTypeProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      flag: false,
      formRules: {
        code: [{ required: true, message: this.$t('请输入编码'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        acquisitionType: [{ required: true, message: this.$t('请选择采集类型'), trigger: 'change' }],
        tagList: [
          {
            required: true,
            message: this.$t('请选择标签'),
            trigger: 'change'
          }
        ],
        childTagGroupList: [
          {
            required: true,
            message: this.$t('请添加子标签分类')
          }
        ]
      },
      tagCardVisible: false,
      continuousUnits: [],
      tagsData: [],
      editTags: false,
      timeEdit: false,
      acquisitionTypeList: []
    }
  },
  computed: {
    formData: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    },
    titleText() {
      const titleMap = {
        add: this.$t('新增标签分类'),
        edit: this.$t('编辑标签分类'),
        view: this.$t('查看标签分类')
      }
      return this.title || titleMap[this.status]
    }
  },
  created() {
    this.accessRoleTypeQuery()
  },
  methods: {
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    show() {
      this.visible = true
      this.listAcquisitionType()
    },
    listAcquisitionType() {
      if (this.acquisitionTypeList?.length) return
      listSysDictionary({
        typeCode: 'data_acquisition_type'
      }).then(res => {
        this.acquisitionTypeList = res.data
      })
    },
    accessRoleTypeQuery() {
      getAccessRole().then(res => {
        this.accessRoleOptions = res.data
      })
    },
    getTags() {
      if (this.status === 'view') {
        this.editTags = false
        this.timeEdit = false
      } else {
        this.editTags = true
        this.timeEdit = true
      }
      this.tagsData = this.setCheckedTag(this.formData.tagList)
      this.tagCardVisible = true
    },
    chooseTags() {
      this.tagChooseDrawerVisible = true
      this.rowTagList = this.formData.tagList || []
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.formData.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      this.tagChooseDrawerVisible = false
      //找到标签组的父节点
      this.getTags()
    },
    setCheckedTag() {
      let groupArr = []
      let tagObj = this.groupBy(this.formData.tagList, 'groupId')
      for (let item in tagObj) {
        let parentGroupName = this.getParentNode(this.treeTagGroupList, item)
        let index = groupArr.findIndex(group => {
          return group.nameCn === parentGroupName
        })
        if (index < 0) {
          groupArr.push({
            nameCn: parentGroupName,
            asLeaf: false,
            children: [
              {
                nameCn: tagObj[item][0].groupNameCn,
                id: item,
                tagList: tagObj[item],
                asLeaf: true
              }
            ]
          })
        } else {
          groupArr[index].children.push({
            nameCn: tagObj[item][0].groupNameCn,
            id: item,
            tagList: tagObj[item],
            asLeaf: true
          })
        }
      }
      return groupArr
    },
    groupBy(array, prop) {
      return array.reduce((cur, pre) => {
        let key = pre[prop]
        if (!cur[key]) {
          cur[key] = []
        }
        cur[key].push(pre)
        return cur
      }, {})
    },
    getParentNode(tree, childId) {
      let parentInfo
      for (let node of tree) {
        // 如果当前节点就是目标节点的父节点，直接返回当前节点
        if (node.children && node.children.some(child => child.id === childId)) {
          return node.nameCn
        }
        // 否则继续遍历当前节点的子节点
        if (node.children) {
          parentInfo = this.getParentNode(node.children, childId)
          if (parentInfo !== null) {
            return parentInfo
          }
        }
      }
      return null
    },
    saveCheckedTagList(group) {
      //遍历查询选中的标签
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.formData.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    // 添加子标签分类
    handleAddChild() {
      this.formData.childTagGroupList.unshift({
        code: '',
        name: '',
        tagList: []
      })
    },
    handleParentChange() {
      this.formData.childTagGroupList = []
      this.formData.tagList = []
      this.formData.tagGroupList = []
    },
    tagSave(dataList) {
      let arr = []
      if (dataList?.length > 0) {
        arr = dataList
          .map(item => {
            return item.children
          })
          .flat(Infinity)
          .map(tag => {
            return tag.tagList
          })
          .flat(Infinity)
        arr.map((val, index) => {
          val.sortNum = index
        })
        this.formData.tagList = arr
      }
      this.tagCardVisible = false
    },
    tagClose() {
      this.tagCardVisible = false
    },
    initForm() {
      this.$refs.formRef?.resetFields()
      this.formData = JSON.parse(JSON.stringify(DEFAULT_FORM_DATA))
    },
    dialogOpened() {},
    dialogClosed() {
      this.visible = false
      this.initForm()
    },
    validateChildTagNull() {
      return this.formData.childTagGroupList.every(item => {
        return item.code && item.name
      })
    },
    validateChildTagName() {
      const codes = new Set()
      const names = new Set()
      for (const item of this.formData.childTagGroupList) {
        if (item.code) {
          if (codes.has(item.code)) return false
          codes.add(item.code)
        }
        if (item.name) {
          if (names.has(item.name)) return false
          names.add(item.name)
        }
      }
      return true
    },
    validateChildTagNumber() {
      return this.formData.childTagGroupList.every(item => {
        return item.tagList && item.tagList.length > 0
      })
    },
    save() {
      // 子标签分类校验
      if (this.formData.isParent) {
        // 非空校验
        if (!this.validateChildTagNull()) {
          this.$message.warning(this.$t('子标签分类的编码和名称不能为空'))
          return
        }
        // 同名校验
        if (!this.validateChildTagName()) {
          this.$message.warning(this.$t('子标签分类的编码或名称不能重复'))
          return
        }
        // 标签数量校验
        if (!this.validateChildTagNumber()) {
          this.$message.warning(this.$t('子标签分类的标签数量不能为空'))
          return
        }
      }

      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const reqParams = {
          ...this.formData,
          childTagGroupList: this.formData.isParent ? this.formData.childTagGroupList : [],
          tagList: this.formData.isParent ? [] : this.formData.tagList,
          tagGroupList: this.formData.isParent ? [] : this.formData.tagGroupList,
          tagAmount: this.formData.isParent
            ? this.formData.childTagGroupList.reduce(
                (sum, child) => sum + (child.tagList ? child.tagList.length : 0),
                0
              )
            : this.formData.tagList.length
        }
        if (this.status === 'add') {
          saveBsTagClassification(reqParams).then(() => {
            this.visible = false
            this.$emit('save', this.formData)
          })
        }
        if (this.status === 'edit') {
          updateBsTagClassification(reqParams).then(() => {
            this.visible = false
            this.$emit('save', this.formData)
          })
        }
      })
    }
  }
}
</script>
<style>
.dialog-container > .el-dialog__body {
  margin-top: 15px;
}
</style>
