<template>
  <el-card class="box-card" id="task-form">
    <div class="card-header">
      <div>
        <el-checkbox
          v-model="form.checked"
          v-if="selectable"
          @change="handleClick"
          style="margin-right: 15px"
        ></el-checkbox>
        <el-link @click="getTaskDetail(form.id)" type="primary" :underline="false" id="code">{{ item.code }} </el-link>
      </div>

      <el-link :type="getStatusType(item.status).type" id="name">{{ getStatusType(item.status).name }} </el-link>
    </div>

    <el-descriptions :column="4" border>
      <!--      <el-descriptions-item :label="$t('需求编号')" align="center"-->
      <!--        ><el-link-->
      <!--          @click="getReqDetail(form.reqId)"-->
      <!--          type="primary"-->
      <!--          :underline="false"-->
      <!--          id="taskCode"-->
      <!--        >-->
      <!--          <el-tooltip effect="dark" :content="form.taskCode">{{-->
      <!--            form.taskCode-->
      <!--          }}</el-tooltip>-->
      <!--        </el-link>-->
      <!--      </el-descriptions-item>-->
      <!--      <el-descriptions-item :label="$t('需求名称')" align="center">-->
      <!--        <el-tooltip effect="dark" :content="form.taskName">{{-->
      <!--          form.taskName-->
      <!--        }}</el-tooltip></el-descriptions-item-->
      <!--      >-->
      <!--      <el-descriptions-item :label="$t('需求发起人')" align="center">-->
      <!--        <el-tooltip effect="dark" :content="form.initiator">{{-->
      <!--          form.initiator-->
      <!--        }}</el-tooltip></el-descriptions-item-->
      <!--      >-->
      <el-descriptions-item :label="$t('车辆')" align="center">
        <el-tooltip effect="dark" :content="form.vehicleVariant + '_' + form.vehicleVin">
          <el-link @click="getVehicleStatisticDetail(form.vehicleId)" type="primary" :underline="false" id="vin">
            {{ form.vehicleVariant + '_' + form.vehicleVin }}
          </el-link>
        </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('司机')" align="center">
        <el-tooltip effect="dark" :content="form.driver">{{ form.driver }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :span="1" :label="$t('类型')" align="center">
        <el-tooltip effect="dark" :content="form.acquisitionType">{{ form.acquisitionType }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item
        :span="1"
        :label="form.locationType === 'parking_lot' ? $t('停车场') : $t('路线')"
        align="center"
      >
        <el-tooltip effect="dark" :content="form.locationName">{{ form.locationName }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('负责人')" align="center">
        <el-tooltip effect="dark" :content="form.recipient">{{ form.recipient }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('合规官')" align="center">
        <el-tooltip effect="dark" :content="form.complianceOfficer">{{ form.complianceOfficer }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('设备')" :span="1" align="center">
        <el-tooltip effect="dark" :content="form.equipmentCode">{{ form.equipmentCode }} </el-tooltip>
        <!-- <el-link type="primary" :underline="false">{{
          form.equipmentCode
        }}</el-link> -->
      </el-descriptions-item>
      <el-descriptions-item :label="$t('副驾')" align="center">
        <el-tooltip effect="dark" :content="form.copilot">{{ form.copilot }} </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('采集标签')" align="center">
        <el-link @click="getTags(form)" type="primary" :underline="false" id="tag"
          >{{ form?.tagList?.length || 0 }}
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('采集记录')" align="center">
        <el-link @click="getTaskRecord(form)" type="primary" :underline="false" id="tag"
          >{{ form.recordAmount || 0 }}
        </el-link>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('期望日期')" align="center">
        <el-tooltip effect="dark" :content="form.expectedStartTime + ' ~ ' + form.expectedEndTime"
          ><span
            ><el-tag type>{{ form.expectedStartTime }}</el-tag
            >&nbsp; ~ &nbsp; <el-tag type>{{ form.expectedEndTime }}</el-tag></span
          ></el-tooltip
        >
      </el-descriptions-item>
      <el-descriptions-item
        v-if="form.status === 'finished' || form.status === 'executing'"
        :label="$t('执行时间')"
        :span="1"
        align="center"
      >
        <el-tooltip effect="dark" :content="form.startTime + ' ~ ' + (form.endTime || '-')"
          ><span
            ><el-tag type>{{ form.startTime }}</el-tag
            >&nbsp; ~ &nbsp; <el-tag type>{{ form.endTime || '-' }}</el-tag></span
          ></el-tooltip
        >
      </el-descriptions-item>
    </el-descriptions>
    <div class="card-footer">
      <el-link
        v-if="form.status === 'draft' && empId === form.recipientEmpId"
        type="success"
        @click="publishTaskDetail(form.id)"
        :underline="false"
        id="release"
        >{{ $t('发布') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'draft' && empId === form.recipientEmpId"
        @click="editDaqReqDetail(form.id)"
        :underline="false"
        id="edit"
        >{{ $t('编辑') }}
      </el-link>
      <el-link
        type="danger"
        v-if="form.status === 'draft' && empId === form.recipientEmpId"
        @click="deleteDaqReqDetail(form.id)"
        :underline="false"
        id="delete"
        >{{ $t('删除') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'published' && empId === form.recipientEmpId"
        @click="start(form.id)"
        :underline="false"
        id="start"
        >{{ $t('开始') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'executing' && empId === form.recipientEmpId"
        @click="finish(form.id)"
        :underline="false"
        id="finish"
        >{{ $t('完成') }}
      </el-link>
      <el-link type="primary" @click="getTaskDetail(form.id)" :underline="false" id="detail">{{ $t('详情') }} </el-link>
    </div>
  </el-card>
  <tag-list @reload="tagSave" ref="TagList" />
  <vehicle-statistic-detail ref="VehicleStatisticDetail" />
  <view-task-detail ref="ViewTaskDetail" />
  <disassemble-requirements ref="DisassembleRequirements" />
  <add-task @reload="cancel" ref="AddTask" />
  <collect-record-dialog @reload="cancel" ref="CollectRecordDialog" />
</template>

<script>
// task20220928141
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import $store from '@/store'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import AddTask from '@/pages/dataCollect/dialog/AddTask.vue'
import DisassembleRequirements from '@/pages/dataCollect/dialog/DisassembleRequirements.vue'
import VehicleStatisticDetail from '@/pages/fleet/dialog/VehicleStatisticDetail.vue'
import ViewTaskDetail from '@/pages/dataCollect/dialog/ViewTaskDetail.vue'
import {
  publishTaskDetail,
  deleteDaqReqDetail,
  updateDaqReqDetail,
  startTaskDetail,
  finishTaskDetail
} from '@/apis/data-collect/vt-daq-task'
import CollectRecordDialog from '@/pages/dataCollect/dialog/CollectRecordDialog.vue'

export default {
  name: 'TaskForm',
  emits: ['reload', 'opt-task-detail', 'handleFormChecked'],
  data() {
    return {
      // 配置标签
      tagsData: [],
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      tagsTitle: '',
      empId: $store.state.permission.currentUser.empId
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    },
    batchingFunctionList: {
      type: Array,
      default: []
    },
    outlineFunctionList: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    selectable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    form() {
      return this.item
    }
  },
  components: {
    VehicleStatisticDetail,
    ViewTaskDetail,
    DisassembleRequirements,
    TagList,
    AddTask,
    CollectRecordDialog
  },

  methods: {
    handleClick(val) {
      this.$emit('handleFormChecked', this.form, val)
    },
    tagClose() {
      this.tagDialogVisible = false
    },
    getTags(row) {
      let empId = $store.state.permission.currentUser.empId
      let editTags = row.status !== 'finished' && row.recipientEmpId === empId
      this.$refs.TagList.show({
        type: editTags ? 'edit' : 'view',
        requirementId: row.reqId,
        tagList: row.tagList
      })
    },
    getTaskRecord(row) {
      // let empId = $store.state.permission.currentUser.empId
      // let editTags = row.status !== 'finished' && row.recipientEmpId === empId
      this.$refs.CollectRecordDialog.show({
        type: 'edit',
        // type: editTags ? 'edit' : 'view',
        data: {
          ...row,
          taskCode: row.code,
          // taskId: row.id,
          // locationType: row.locationType,
          // locationId: row.locationId,
          // locationName: row.locationName,
          tagRecordCount: row?.tagList?.length,
          // startTime: row.startTime,
          // complianceOfficerEmpId: row.complianceOfficerEmpId,
          // complianceOfficer: row.complianceOfficer,
          // copilotEmpId: row.copilotEmpId,
          // copilot: row.copilot,
          // driverEmpId: row.driverEmpId,
          // driver: row.driver,
          // vehicleVin: row.vehicleVin,
          btnPermission: {
            batchingFunctionList: this.batchingFunctionList,
            outlineFunctionList: this.outlineFunctionList,
            inlineFunctionList: this.inlineFunctionList
          }
        }
      })
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        this.form.tagList = dataList
        this.updateDaqReqDetail()
      }
    },
    updateDaqReqDetail() {
      let postData = { ...this.form }
      postData.tagList = this.form.tagList
      updateDaqReqDetail(postData).then(() => {
        this.$emit('reload')
      })
    },
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    },
    cancel(val) {
      this.$emit('reload', val)
    },
    getVehicleStatisticDetail(id) {
      this.$refs.VehicleStatisticDetail.show({
        type: 'view',
        id: id
      })
    },
    getTaskDetail(id) {
      this.$refs.ViewTaskDetail.show({ id })
    },
    getReqDetail(id) {
      this.$refs.DisassembleRequirements.show({ type: 'view', id })
    },
    editDaqReqDetail(id) {
      this.$refs.AddTask.show({ type: 'edit', id })
    },
    publishTaskDetail(id) {
      publishTaskDetail(id).then(res => {
        showToast('发布成功')
        this.cancel()
      })
      // })
    },
    deleteDaqReqDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail({ id }).then(() => {
          showToast('删除成功')
          this.cancel()
        })
      })
      // })
    },
    start(id) {
      startTaskDetail(id).then(res => {
        showToast('已开始')
        this.cancel()
      })
    },
    finish(id) {
      finishTaskDetail(id).then(res => {
        showToast('已完成')
        this.cancel()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-card {
  margin-bottom: 15px;

  .el-descriptions,
  :deep(.el-descriptions__body),
  tbody {
    width: 100%;

    .el-descriptions__table.is-bordered .el-descriptions__cell {
      font-size: 12px;
      font-weight: 400;
      padding: 2px 11px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.el-descriptions__label {
        font-weight: 600;
        white-space: nowrap;
      }
    }

    .el-descriptions__table {
      table-layout: fixed;
    }

    // :deep(.el-descriptions__table) {
    //   :deep(.el-descriptions__cell) {
    //     font-size: 12px;
    //     font-weight: 400;
    //     padding: 0 11px;
    //     // white-space: nowrap;
    //     // overflow: hidden;
    //     // text-overflow: ellipsis;
    //     // word-break: break-all;
    //   }
    // :deep(.el-descriptions__label) {
    //   font-weight: 600;
    // }
    // }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px 10px 0;
  }

  .card-footer {
    text-align: right;

    .el-link:not(:last-child) {
      margin-right: 8px;
    }
  }
}
</style>
