<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="app-container">
        <div class="dimension-switch">
          <el-radio-group v-model="currentDimension" size="large">
            <el-radio-button label="data">数据维度</el-radio-button>
            <el-radio-button label="disk">磁盘维度</el-radio-button>
          </el-radio-group>
        </div>
        <ftm-disk-transport-filter
          :currentDimension="currentDimension"
          @filter="handelFilter"
        ></ftm-disk-transport-filter>

        <!-- 数据维度卡片 -->
        <div v-if="currentDimension === 'data'" class="data-cards-container">
          <el-empty v-if="!groupedData.length" description="暂无数据" :image-size="200">
            <template #description>
              <p>暂无磁盘流转记录</p>
            </template>
          </el-empty>

          <div class="expand-all-container" v-else>
            <el-button type="primary" size="small" @click="toggleExpandAll" style="background:#5755FF">
              <el-icon><ArrowDown v-if="!allExpanded" /><ArrowUp v-else /></el-icon>
              {{ allExpanded ? '收起全部' : '展开全部' }}
            </el-button>
          </div>
          <div v-for="(item, index) in groupedData" :key="index" class="data-card">
            <div class="card-header">
              <div class="card-box">
                <div class="date-title">{{ item.acquisitionDate }}</div>
                <div class="card-summary">
                  <div class="summary-item">
                    <el-icon><Files /></el-icon>
                    <span
                      >包数:<span style="color: blue; margin-left: 4px">{{ item.packageNumSum }}</span></span
                    >
                  </div>
                  <div class="summary-item">
                    <el-icon><DataAnalysis /></el-icon>
                    <span
                      >数据大小:<span style="color: blue; margin-left: 4px">{{ item.dataSizeSum }}</span> GB
                    </span>
                  </div>
                </div>
              </div>

              <div class="expand-button" @click="toggleExpand(index)">
                <el-icon><ArrowDown v-if="!item.isExpanded" /><ArrowUp v-else /></el-icon>
              </div>
            </div>

            <div v-if="item.isExpanded" class="card-details">
              <el-table :data="item.deliveryDataList" border style="width: 100%">
                <el-table-column prop="vin" label="车辆编号" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag type="primary"
                      ><span>{{ row.vin }}</span>
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ftmDiskTransportVO.diskCode" label="磁盘编号" min-width="120" align="center"/>
                <el-table-column prop="ftmDiskTransportVO.diskSerialNumber" label="磁盘序列号" min-width="165" align="center"/>
                <!-- <el-table-column label="来源" min-width="100">
                  <template #default="{ row }">
                    <span v-if="row.sourceType === 'vehicle'">车端</span>
                    <span v-else>uploader</span>
                  </template>
                </el-table-column> -->

                <el-table-column prop="packageNum" label="包数" min-width="120" align="center"/>
                <el-table-column prop="dataSize" label="数据大小(GB)" min-width="120" align="center"/>
                <el-table-column label="快递单号" min-width="160" align="center">
                  <template #default="{ row }">
                    <el-tag type="info" v-if="row.ftmDiskTransportVO.transportMode === 'manual'">人工</el-tag>
                    <el-tag type="success" v-if="row.ftmDiskTransportVO.transportMode === 'delivery'" class="tracking-number" @click="showLogisticsDialog(row.ftmDiskTransportVO)">{{
                      row.ftmDiskTransportVO.trackingNumber
                    }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ftmDiskTransportVO.receiptStatusName" label="签收状态" min-width="130" align="center"/>
                <el-table-column prop="ftmDiskTransportVO.deliveryEmpName" label="寄送人" min-width="150" align="center">
                  <template #default="{ row }">
                    <el-tag type="warning"
                      ><span>{{ row.ftmDiskTransportVO.deliveryEmpName }}</span>
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ftmDiskTransportVO.deliveryTime" label="寄送时间" min-width="165" />
                <el-table-column
                  prop="ftmDiskTransportVO.deliveryAddress"
                  label="寄送人定位"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                />
                <el-table-column label="寄送附件" min-width="140" align="center">
                  <template #default="{ row }">
                    <div
                      class="img-container"
                      v-if="row.ftmDiskTransportVO.deliveryFileList && row.ftmDiskTransportVO.deliveryFileList.length"
                    >
                      <div v-for="(item, index) in row.ftmDiskTransportVO.deliveryFileList" :key="index">
                        <el-image
                          style="min-width: 40px; height: 40px; margin-right: 4px"
                          :src="item.url"
                          fit="cover"
                          :preview-src-list="[item.url]"
                          :initial-index="0"
                          preview-teleported
                        >
                          <template #error>
                            <div class="image-error">
                              <el-icon>
                                <Picture />
                              </el-icon>
                            </div>
                          </template>
                        </el-image>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="ftmDiskTransportVO.receiptEmpName" label="签收人" align="center" min-width="150">
                  <template #default="{ row }">
                    <el-tag type="danger"
                      ><span>{{ row.ftmDiskTransportVO.receiptEmpName }}</span>
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="ftmDiskTransportVO.receiptTime" label="签收时间" align="center" min-width="165" />
                <el-table-column
                  prop="ftmDiskTransportVO.receiptAddress"
                  label="签收人定位"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                />
                <el-table-column prop="ftmDiskTransportVO.receiptFileList" label="签收附件" min-width="140" align="center">
                  <template #default="{ row }">
                    <div
                      class="img-container"
                      v-if="row.ftmDiskTransportVO.receiptFileList && row.ftmDiskTransportVO.receiptFileList.length"
                    >
                      <div v-for="(item, index) in row.ftmDiskTransportVO.receiptFileList" :key="index">
                        <el-image
                          style="min-width: 40px; height: 40px; margin-right: 4px"
                          :src="item.url"
                          fit="cover"
                          :preview-src-list="[item.url]"
                          :initial-index="0"
                          preview-teleported
                        >
                          <template #error>
                            <div class="image-error">
                              <el-icon><Picture /></el-icon>
                            </div>
                          </template>
                        </el-image>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 磁盘维度表格 -->
        <el-table v-else :data="rawData" style="width: 100%; margin-top: 20px" border>
          <el-table-column type="index" width="50" />
          <el-table-column prop="diskSerialNumber" label="磁盘序列号" width="165" />
          <el-table-column prop="diskCode" label="磁盘编号" width="165" />
          <el-table-column label="快递单号" min-width="150">
            <template #default="{ row }">
              <span v-if="row.transportMode === 'manual'">人工</span>
              <span v-if="row.transportMode === 'delivery'"  class="tracking-number" @click="showLogisticsDialog(row)">
                {{ row.trackingNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="receiptStatusName" label="签收状态" min-width="120" />
          <el-table-column prop="deliveryEmpName" label="寄送人" min-width="120" />
          <el-table-column prop="deliveryTime" label="寄送时间" min-width="165" />
          <el-table-column prop="deliveryAddress" label="寄送人定位" min-width="100" show-overflow-tooltip />
          <el-table-column prop="deliveryFileList" label="寄送附件" min-width="120">
            <template #default="{ row }">
              <div class="img-container" v-if="row.deliveryFileList && row.deliveryFileList.length">
                <div v-for="(item, index) in row.deliveryFileList" :key="index">
                  <el-image
                    style="min-width: 40px; height: 40px; margin-right: 4px"
                    :src="item.url"
                    fit="cover"
                    :preview-src-list="[item.url]"
                    :initial-index="0"
                    preview-teleported
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="receiptEmpName" label="签收人" min-width="120" />
          <el-table-column prop="receiptTime" label="签收时间" min-width="165" />
          <el-table-column prop="receiptAddress" label="签收人定位" min-width="120" show-overflow-tooltip />
          <el-table-column prop="receiptFileList" label="签收附件" min-width="120">
            <template #default="{ row }">
              <div class="img-container" v-if="row.receiptFileList && row.receiptFileList.length">
                <div v-for="(item, index) in row.receiptFileList" :key="index">
                  <el-image
                    style="min-width: 40px; height: 40px; margin-right: 4px"
                    :src="item.url"
                    fit="cover"
                    :preview-src-list="[item.url]"
                    :initial-index="0"
                    preview-teleported
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container" v-show="pagination.total">
          <el-pagination
           background
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 物流信息对话框 -->
    <el-dialog v-model="logisticsDialogVisible" title="物流信息" width="50%" :close-on-click-modal="false">
      <el-timeline v-if="logisticsData.length">
        <el-timeline-item
          v-for="(item, index) in logisticsData"
          :key="index"
          :timestamp="item.acceptTime"
          :type="index === 0 ? 'primary' : ''"
          placement="top"
        >
          <div class="logistics-item">
            <div class="logistics-status">{{ item.firstStatusName }} - {{ item.acceptAddress }}</div>
            <div class="logistics-remark">
              {{ item.remark }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else>
        <template #description>
          <p>暂无物流信息</p>
        </template>
      </el-empty>
    </el-dialog>
  </div>
</template>

<script>
import FtmDiskTransportFilter from '@/components/fleet/FtmDiskTransportFilter.vue'
import { pageWebDiskTransport, pageWebDiskDataTransport } from '@/apis/fleet/ftm-dis-transport'
import { getLogisticsInfo } from '@/apis/fleet/ftm-dis-transport'
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'
import { Picture, ArrowDown, ArrowUp, Package, Files, DataAnalysis } from '@element-plus/icons-vue'

export default {
  name: 'BuzDiskProcessRecord',
  components: {
    FtmDiskTransportFilter,
    Picture,
    ArrowDown,
    ArrowUp,
    Package,
    Files,
    DataAnalysis
  },
  data() {
    return {
      filterParam: {},
      currentDimension: 'data', // 默认显示磁盘维度
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      rawData: [],
      groupedData: [],
      logisticsDialogVisible: false,
      logisticsData: [], // 物流信息数据
      currentTrackingNumber: '', // 当前查看的物流单号
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/preview',
      token: 'token=' + util.getToken(),
      allExpanded: false // 添加控制全部展开/收起的状态
    }
  },
  mounted() {
    this.handelFilter()
  },
  // watch: {
  //   currentDimension: {
  //     handler(newVal) {
  //       this.handelFilter()
  //     }
  //   }
  // },
  methods: {
    // 处理文件列表URL
    processFileListUrl(recordList) {
      if (!recordList || !recordList.length) return recordList

      return recordList.map(item => {
        // 处理寄送文件列表
        if (item.deliveryFileList?.length) {
          item.deliveryFileList = item.deliveryFileList.map(it => ({
            ...it,
            url: this.downloadUrl + '?path=' + it.fileAddress + '&' + this.token
          }))
        }

        // 处理签收文件列表
        if (item.receiptFileList?.length) {
          item.receiptFileList = item.receiptFileList.map(it => ({
            ...it,
            url: this.downloadUrl + '?path=' + it.fileAddress + '&' + this.token
          }))
        }

        return item
      })
    },
    processGroupFileListUrl(newRecordList) {
      if (!newRecordList || !newRecordList.length) return newRecordList

      return newRecordList.map((group, index) => {
        if (group.deliveryDataList?.length) {
          group.deliveryDataList = group.deliveryDataList.map(dataItem => {
            const transportVO = dataItem.ftmDiskTransportVO
            if (transportVO?.deliveryFileList?.length) {
              transportVO.deliveryFileList = transportVO.deliveryFileList.map(file => ({
                ...file,
                url: this.downloadUrl + '?path=' + file.fileAddress + '&' + this.token
              }))
            }
            if (transportVO?.receiptFileList?.length) {
              transportVO.receiptFileList = transportVO.receiptFileList.map(file => ({
                ...file,
                url: this.downloadUrl + '?path=' + file.fileAddress + '&' + this.token
              }))
            }
            return dataItem
          })
        }
        if (index === 0) {
          return {
            ...group,
            isExpanded: true
          }
        } else {
          return {
            ...group,
            isExpanded: false
          }
        }
      })
    },

    handelFilter(data) {
      this.filterParam = data
      const filterParams = {
        ...data,
        current: this.pagination.currentPage,
        size: this.pagination.pageSize
      }
      if (this.currentDimension === 'data') {
        // 数据维度
        pageWebDiskDataTransport(filterParams).then(res => {
          const recordList = res.data.records
          if (recordList && recordList.length) {
            this.groupedData = this.processGroupFileListUrl(recordList)
            this.pagination.total = res.data.total
            this.allExpanded = false
          } else {
            this.groupedData = []
            this.pagination.total = 0
          }
        })
      } else {
        // 磁盘维度
        pageWebDiskTransport(filterParams).then(res => {
          const recordList = res.data.records
          if (recordList && recordList.length) {
            this.rawData = this.processFileListUrl(recordList)
            this.pagination.total = res.data.total
          } else {
            this.rawData = []
            this.pagination.total = 0
          }
        })
      }
    },
    // 切换卡片展开/收起状态
    toggleExpand(index) {
      if (!this.groupedData[index].hasOwnProperty('isExpanded')) {
        this.$set(this.groupedData[index], 'isExpanded', true)
      } else {
        this.groupedData[index].isExpanded = !this.groupedData[index].isExpanded
      }

      // 检查是否所有卡片都已展开，更新allExpanded状态
      this.updateAllExpandedStatus()
    },

    // 添加检查所有卡片展开状态的方法
    updateAllExpandedStatus() {
      this.allExpanded = this.groupedData.every(item => item.isExpanded)
    },

    // 添加一键展开/收起所有卡片的方法
    toggleExpandAll() {
      this.allExpanded = !this.allExpanded
      this.groupedData.forEach(item => {
        item.isExpanded = this.allExpanded
      })
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handelFilter(this.filterParam)
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.handelFilter(this.filterParam)
    },

    async showLogisticsDialog(row) {
      const { trackingNumber, deliveryEmpMobileNum, receiptEmpMobileNum } = row
      let checkPhoneNos = ''
      if (deliveryEmpMobileNum || receiptEmpMobileNum) {
        if (deliveryEmpMobileNum && receiptEmpMobileNum) {
          const deliveryLastFour = deliveryEmpMobileNum.slice(-4)
          const receiptLastFour = receiptEmpMobileNum.slice(-4)
          checkPhoneNos = `${deliveryLastFour},${receiptLastFour}`
        } else if (deliveryEmpMobileNum) {
          checkPhoneNos = deliveryEmpMobileNum.slice(-4)
        } else if (receiptEmpMobileNum) {
          checkPhoneNos = receiptEmpMobileNum.slice(-4)
        }

        const data = {
          checkPhoneNos,
          mailNos: trackingNumber
        }
        try {
          const res = await getLogisticsInfo(data)
          // const mockData = {"data":{"routeResps":[{"mailNo":"SF3144586454724","routes":[{"acceptAddress":"宜昌市","acceptTime":"2025-03-24 17:58:49","firstStatusCode":"1","firstStatusName":"已揽收","opCode":"50","remark":"顺丰速运 已收取快件，您的期待，我们定竭诚守护，不负所托。","secondaryStatusCode":"101","secondaryStatusName":"已揽收"},{"acceptAddress":"宜昌市","acceptTime":"2025-03-24 21:26:18","firstStatusCode":"2","firstStatusName":"运送中","opCode":"30","remark":"快件在【宜昌秭归茶博园店】完成分拣，准备发往 【武汉吴家山转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"宜昌市","acceptTime":"2025-03-24 21:26:51","firstStatusCode":"2","firstStatusName":"运送中","opCode":"36","remark":"快件离开 【宜昌秭归茶博园店】，已在发往  【武汉吴家山转运中心】 的路上","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"武汉市","acceptTime":"2025-03-25 03:18:27","firstStatusCode":"2","firstStatusName":"运送中","opCode":"31","remark":"快件到达 【武汉吴家山转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"武汉市","acceptTime":"2025-03-25 03:19:36","firstStatusCode":"2","firstStatusName":"运送中","opCode":"30","remark":"快件在【武汉吴家山转运中心】完成分拣，准备发往 【苏州常熟转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"武汉市","acceptTime":"2025-03-25 05:05:29","firstStatusCode":"2","firstStatusName":"运送中","opCode":"36","remark":"快件离开 【武汉吴家山转运中心】，已在发往  【苏州常熟转运中心】 的路上","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"武汉市","acceptTime":"2025-03-25 09:09:11","firstStatusCode":"2","firstStatusName":"运送中","opCode":"310","remark":"快件途经安庆市","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"武汉市","acceptTime":"2025-03-25 13:09:12","firstStatusCode":"2","firstStatusName":"运送中","opCode":"310","remark":"快件途经南京市","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-25 16:10:58","firstStatusCode":"2","firstStatusName":"运送中","opCode":"31","remark":"快件到达 【苏州常熟转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-25 17:03:58","firstStatusCode":"2","firstStatusName":"运送中","opCode":"30","remark":"快件在【苏州常熟转运中心】完成分拣，准备发往 【苏州甪直转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-25 22:19:51","firstStatusCode":"2","firstStatusName":"运送中","opCode":"36","remark":"快件离开 【苏州常熟转运中心】，已在发往  【苏州甪直转运中心】 的路上","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 00:40:10","firstStatusCode":"2","firstStatusName":"运送中","opCode":"31","remark":"快件到达 【苏州甪直转运中心】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 02:54:39","firstStatusCode":"2","firstStatusName":"运送中","opCode":"30","remark":"快件在【苏州甪直转运中心】完成分拣，准备发往 【苏州相城芯光里驿小店】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 04:22:53","firstStatusCode":"2","firstStatusName":"运送中","opCode":"36","remark":"快件离开 【苏州甪直转运中心】，已在发往  【苏州相城芯光里驿小店】 的路上","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 06:39:24","firstStatusCode":"2","firstStatusName":"运送中","opCode":"31","remark":"快件到达 【苏州相城芯光里驿小店，地址：相城区蠡太路518号芯光里A栋北楼128室驿收发】","secondaryStatusCode":"201","secondaryStatusName":"运送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 06:41:52","firstStatusCode":"3","firstStatusName":"派送中","opCode":"44","remark":"我们正在为您的快件分配最合适的快递员，请您稍等。","secondaryStatusCode":"301","secondaryStatusName":"派送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 07:07:55","firstStatusCode":"3","firstStatusName":"派送中","opCode":"204","remark":"快件交给【张俊伟，联系电话：17300690394】，正在派送途中（如有任何问题可先联系我，我将尽全力为您解决。您的认可，是我最大的动力！）","secondaryStatusCode":"301","secondaryStatusName":"派送中"},{"acceptAddress":"苏州市","acceptTime":"2025-03-26 08:12:15","firstStatusCode":"4","firstStatusName":"已签收","opCode":"80","remark":"经客户同意，快件已放在（家门口），如有疑问请电联快递员【张俊伟，电话：17300690394】。您的体验对于我们至关重要，如果您对我们的服务有任何的想法和建议，请随时联系我们，我们一定用心倾听，全力改进，不辜负您的信任与支持。","secondaryStatusCode":"401","secondaryStatusName":"已签收"}]}]},"status":1,"success":true}
          this.logisticsDialogVisible = true
          this.logisticsData = res.data.routeResps[0].routes.reverse() || []
        } catch (error) {
          this.$message.error('获取物流信息失败')
          this.logisticsData = []
          this.logisticsDialogVisible = false
        }
      } else {
        this.$message.warning('该单据暂不支持物流信息查询')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 空状态样式 */
.el-empty {
  padding: 40px 0;

  :deep(.el-empty__image) {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  :deep(.el-empty__description) {
    margin-top: 20px;

    p {
      margin: 0;
      line-height: 1.6;
      color: #606266;
      font-size: 14px;

      &.empty-subtitle {
        font-size: 13px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }
}
.tracking-number {
  cursor: pointer;
  color: #409eff;
  text-decoration: underline;
}

/* 数据维度卡片列表样式 */
.data-dimension-cards {
  margin-top: 20px;

  .cards-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .total-info {
      font-size: 14px;
      color: #606266;
    }

    .expand-all-btn {
      display: flex;
      align-items: center;
      height: 26px;
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

/* 确保磁盘维度表格也有合适的空状态 */
.el-table {
  :deep(.el-empty) {
    padding: 32px 0;
  }
}

.data-cards-container {
  margin-top: 20px;
}

.data-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  .card-box {
    display: flex;
    align-items: center;
  }
}

.date-title {
  font-size: 16px;
  font-weight: bold;
}

.expand-button {
  cursor: pointer;
  padding: 4px;
}

.card-summary {
  display: flex;
  padding: 12px 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
}

.summary-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.card-details {
  padding: 16px;
  background-color: #fff;
}

.img-container {
  display: flex;
  flex-wrap: wrap;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}

.pagination-container {
  margin-top: 20px;
}

.logistics-item {
  margin-bottom: 8px;
}

.logistics-status {
  font-weight: bold;
  margin-bottom: 4px;
}

.logistics-remark {
  color: #606266;
}

/* 添加一键展开/收起按钮的样式 */
.expand-all-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.expand-all-container .el-button {
  display: flex;
  align-items: center;
}

.expand-all-container .el-icon {
  margin-right: 5px;
}
</style>
