<template>
  <div class="task-step">
    <el-scrollbar>
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item :label="$t('名称')" prop="name">
          <ltw-input v-model="form.name" :disabled="formReadonly" id="name" text-type="remark"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('期望日期')" prop="dateRange" id="dateRange">
          <el-date-picker
            clearable
            :disabled="formReadonly"
            v-model="form.dateRange"
            type="daterange"
            :range-separator="$t('到')"
            :start-placeholder="$t('开始日期')"
            :end-placeholder="$t('结束日期')"
            value-format="YYYY-MM-DD"
            popper-class="dateRange"
          />
        </el-form-item>

        <el-form-item v-if="formReadonly" :label="$t('执行开始时间')" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="date"
            :disabled-date="disabledStartDate"
            :disabled="formReadonly"
            :placeholder="$t('开始时间')"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item v-if="formReadonly" :label="$t('执行结束时间')" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            :disabled-date="disabledEndDate"
            :disabled="formReadonly"
            type="date"
            :placeholder="$t('结束时间')"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <!--      <el-form-item v-if="formReadonly" :label="$t('执行时间')" prop="executionTimeRange" id="executionTimeRange">-->
        <!--        <el-date-picker-->
        <!--          clearable-->
        <!--          :disabled="formReadonly"-->
        <!--          v-model="form.executionTimeRange"-->
        <!--          type="datetimerange"-->
        <!--          :range-separator="$t('到')"-->
        <!--          :start-placeholder="$t('开始时间')"-->
        <!--          :end-placeholder="$t('结束时间')"-->
        <!--          value-format="YYYY-MM-DD HH:mm:ss"-->
        <!--          @change="getFreeOption()"-->
        <!--          popper-class="dateRange"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('车辆')" prop="vehicleId" id="vehicleId">
              <bs-vehicle-selection
                clearable
                filterable
                :data="vehicleList"
                :auto-load="false"
                modelCode="id"
                v-model="form.vehicleId"
                :disabled="!(dialogStatus === 'add' || form.status === 'draft') || formReadonly"
                @change="handleVehilceChange($event)"
                ref="vehicleSelectionRef"
              ></bs-vehicle-selection>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('场景选择')" prop="taskFunctions">
              <el-button size="small" plain v-if="!formReadonly" @click="chooseScene()" type="primary" id="tag">
                <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              </el-button>
              <div class="group-list">
                <div class="group-item" v-for="(group, index) in form.taskFunctions" :key="group.id">
                  <el-tag>{{ group.name }}</el-tag>
                  <el-link @click="getTags([group], 'taskFunctions')" class="tag-num" :underline="false">
                    <ltw-icon icon-code="el-icon-discount"></ltw-icon>
                    <span class="tag-text">{{ getTaskFunctionsLength(group) }}</span></el-link
                  >
                  <el-button
                    v-if="!formReadonly"
                    class="opt-btn"
                    type="danger"
                    size="small"
                    @click="deleteTaskFunction(index)"
                  >
                    <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('类型')" prop="type">
              <el-radio-group size="small" v-model="form.type" :disabled="formReadonly">
                <el-radio border v-for="item in typeList" :label="item.code" :key="item.code"
                  >{{ $t(item.name) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('采集类型')" prop="acquisitionType">
              <el-radio-group
                size="small"
                :disabled="formReadonly"
                v-model="form.acquisitionType"
                id="acquisitionType"
                @change="handleAcquisitionTypeChange"
              >
                <el-radio border v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
                  >{{ $t(item.name) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-----<el-form-item :label="$t('设备')" prop="equipmentId" id="equipmentId">
              <bs-tag-equipment-selection
                clearable
                filterable
                :data="equipmentList"
                :auto-load="false"
                :disabled="formReadonly"
                v-model="form.equipmentId"
              ></bs-tag-equipment-selection>
            </el-form-item>--->

        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('负责人')" prop="recipientEmpId" id="recipientEmpId">
              <employee-selection
                clearable
                :disabled="formReadonly"
                v-model="form.recipientEmpId"
                @change="changeRecipient"
              ></employee-selection>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('合规官')" prop="complianceOfficerEmpId" id="complianceOfficerEmpId">
              <employee-selection
                clearable
                :data="complianceOfficerList"
                :auto-load="false"
                :query-param="complianceEmployeeQueryParam"
                :disabled="formReadonly"
                v-model="form.complianceOfficerEmpId"
                @change="changeComplianceOfficer"
              ></employee-selection>
            </el-form-item>
          </el-col>
        </el-row>
        <!--      <el-form-item v-if="form.locationType === 'route'" :label="$t('路线')" prop="locationName">-->
        <!--        <ltw-input v-model="form.locationName" :disabled="formReadonly" id="locationName"></ltw-input>-->
        <!--      </el-form-item>-->
        <el-form-item v-if="form.type === 'special'" :label="$t('需求标签')" prop="requirementList">
          <!--        <el-link-->
          <!--          type="primary"-->
          <!--          @click="getTags(form.requirementList, 'req')"-->
          <!--          style="margin-right: 10px"-->
          <!--          :underline="false"-->
          <!--          >{{ form.requirementList?.length || 0 }}-->
          <!--        </el-link>-->
          <el-button v-if="!formReadonly" size="small" plain @click="chooseRequirementTags()" type="primary" id="tag">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            <!--          form.requirementList?.length > 0 ? $t('选择') : $t('选择')-->
          </el-button>
          <div class="group-list">
            <div class="group-item" v-for="(group, index) in form.requirementList" :key="group.id">
              <el-tag type="success">{{ group.name }}</el-tag>
              <el-link @click="getTags([group], 'req')" class="tag-num" :underline="false">
                <ltw-icon icon-code="el-icon-discount"></ltw-icon>
                <span class="tag-text">{{ getTagListLength(group) || 0 }}</span></el-link
              >
              <el-button
                v-if="!formReadonly"
                class="opt-btn"
                type="danger"
                size="small"
                @click="deleteRequirementList(index)"
              >
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          :label="$t('分类标签')"
          prop="classificationList"
          :rules="
            form.type === 'daily'
              ? {
                  required: true,
                  message: this.$t('请选择'),
                  trigger: 'change'
                }
              : {
                  required: false
                }
          "
        >
          <!--        <el-link-->
          <!--          type="primary"-->
          <!--          @click="getTags(form.classificationList, 'class')"-->
          <!--          style="margin-right: 10px"-->
          <!--          :underline="false"-->
          <!--          >{{ form.classificationList?.length || 0 }}-->
          <!--        </el-link>-->
          <el-button size="small" plain v-if="!formReadonly" @click="chooseTags()" type="primary" id="tag">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          </el-button>
          <div class="group-list">
            <div class="group-item" v-for="(group, index) in form.classificationList" :key="group.id">
              <el-tag>{{ group.name }}</el-tag>
              <el-link @click="getTags([group], 'class')" class="tag-num" :underline="false">
                <ltw-icon icon-code="el-icon-discount"></ltw-icon>
                <span class="tag-text">{{ getTagListLength(group) }}</span></el-link
              >
              <el-button
                v-if="!formReadonly"
                class="opt-btn"
                type="danger"
                size="small"
                @click="deleteClassification(index)"
              >
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item :label="$t('备注')" prop="remark">
          <ltw-input type="textarea" v-model="form.remark" :disabled="formReadonly" id="remark"></ltw-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <!--  <tag-samples-dialog ref="TagSamplesDialog" @reload="confirmSamples"></tag-samples-dialog>-->
    <tag-requirement-drawer
      ref="TagRequirementDrawer"
      @reload="confirmRequirementTag"
      @requirementTagClose="handleRequirementTagClose"
    />

    <tag-classification-drawer
      ref="TagClassificationDrawer"
      @reload="confirmClassification"
      @classficationTagClose="handleClassficationTagClose"
    />
    <tag-scene-drawer ref="TagSceneDrawer" @reload="confirmScene" />
  </div>
</template>

<script>
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import { saveDaqReqDetail, updateDaqReqDetail, getDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import { getDriverList, listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import TagRequirementDrawer from '@/pages/dataCollect/dialog/TagRequirementDrawer.vue'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import BsVehicleSelection from '@/components/dataCollect/BsVehicleSelection.vue'
import BsTagEquipmentSelection from '@/components/basic/BsTagEquipmentSelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import TagClassificationDrawer from '@/pages/dataCollect/dialog/TagClassificationDrawer.vue'
import TagSceneDrawer from '@/pages/dataCollect/dialog/TagSceneDrawer.vue'
import TagSamplesDialog from '@/pages/dataCollect/dialog/TagSamplesDialog.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'

const defaultform = {
  type: 'daily',
  acquisitionType: 'parking'
}
export default {
  name: 'TaskStep1',
  emits: ['reload', 'show-tag-list', 'show-tag-samples-dialog', 'updateData'],
  props: {
    dialogStatus: {
      type: String
    }
  },
  data() {
    return {
      //dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      acquisitionTypeList: [],
      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        acquisitionType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        vehicleId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        // equipmentId: [
        //   {
        //     required: true,
        //     message: this.$t('请选择'),
        //     trigger: 'change'
        //   }
        // ],
        recipientEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        driverEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        copilotEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        complianceOfficerEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        dateRange: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        requirementList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ]
        // classificationList: [
        //   {
        //     required: true,
        //     message: this.$t('请选择'),
        //     trigger: 'change'
        //   }
        // ]
      },
      complianceEmployeeQueryParam: {
        orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
      },
      // 配置标签
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      // 选择标签
      tagsData: [],
      tagGroupList: [],
      tagsTitle: '',
      vehicleList: [],
      driverList: [],
      complianceOfficerList: [],
      // equipmentList: [],
      parkingList: [],
      tagList: [],
      reqTagList: [],
      tagCardVisible: false,
      checkedTag: [],
      flag: false,
      requirementList: [],
      chooseTagType: '',
      typeList: [],
      classficationTagCloseFlag: false,
      requirementTagCloseFlag: false
    }
  },
  components: {
    LtwIcon,
    DictionarySelection,
    EmployeeSelection,
    BsTagEquipmentSelection,
    BsVehicleSelection,
    TagList,
    BsTagGroupPanel,
    TagRequirementDrawer,
    TagClassificationDrawer,
    TagSceneDrawer,
    TagSamplesDialog
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    show(row) {
      this.getDataTypes()
      this.getDaqTaskType()
      if (this.dialogStatus === 'edit' || this.dialogStatus === 'view') {
        this.getReqDetail(row.id)
      }
      this.listComplianceOfficer()
      this.listBsVehicle()
      if (!this.treeTagGroupList?.length) {
        this.treeListTagGroup()
      }
    },
    getDataTypes() {
      listSysDictionary({
        typeCode: 'acquisition_task_type'
      }).then(res => {
        this.acquisitionTypeList = res.data
      })
    },
    getDaqTaskType() {
      listSysDictionary({
        typeCode: 'daq_task_type'
      }).then(res => {
        this.typeList = res.data
      })
    },
    getReqDetail(id) {
      getDaqReqDetail(id).then(res => {
        if (res.data?.expectedStartTime && res.data?.expectedEndTime) {
          res.data.dateRange = [res.data?.expectedStartTime, res.data?.expectedEndTime]
        }
        // res.data.executionTimeRange = [res.data.startTime, res.data.endTime]
        this.form = res.data

        //场景选择格式化
        if (this.form?.taskFunctions?.length) {
          this.form.taskFunctions = this.formatTaskFunctions(this.form.taskFunctions)
        }
        if (this.form?.classificationList) {
          this.form.classificationList.forEach(item => {
            // item.children =
            let groupObj = {}
            item.tagList.forEach(tag => {
              if (groupObj[tag.isParent ? tag.childCode : tag.groupCode]) {
                groupObj[tag.isParent ? tag.childCode : tag.groupCode].tagList.push(tag)
              } else {
                groupObj[tag.isParent ? tag.childCode : tag.groupCode] = {
                  name: tag.isParent ? tag.childName : tag.groupName,
                  nameCn: tag.isParent ? tag.childName : tag.groupNameCn,
                  code: tag.isParent ? tag.childCode : tag.groupCode,
                  id: tag.id,
                  tagList: [tag]
                }
              }
            })
            item.children = Object.values(groupObj) || []
            // item.children = item.tagGroupList
          })
        }
        if (this.form?.requirementList) {
          this.form.requirementList.forEach(item => {
            item.children = item.tagGroupList
          })
        }
        this.$emit('reload', this.form)
        // this.listEquipment()
      })
    },

    treeListTagGroup() {
      treeListBsTagGroup().then(res => {
        this.treeTagGroupList = res.data
      })
    },
    handleAcquisitionTypeChange() {
      if (this.form.acquisitionType === 'parking') {
        this.form.locationType = 'parking_lot'
      } else if (this.form.acquisitionType === 'driving') {
        this.form.locationType = 'route'
      } else {
        this.form.locationType = ''
      }
    },
    // getFreeOption() {
      //this.form.equipmentId = undefined
      // this.form.vehicleId = undefined
      // this.listBsVehicle()
      // this.listEquipment()
    // },
    listComplianceOfficer() {
      listSysRoleEmployee({ orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE }).then(res => {
        this.complianceOfficerList = res.data
      })
    },
    // listEquipment() {
    //   let postData = {
    //     expectedStartDate: this.form.dateRange && this.form.dateRange[0],
    //     expectedEndDate: this.form.dateRange && this.form.dateRange[1],
    //     taskId: this.form.id
    //   }
    //   listBsTagEquipment(postData).then(res => {
    //     this.equipmentList = res.data
    //   })
    // },
    listBsVehicle() {
      let postData = {
        // expectedStartDate: this.form.dateRange && this.form.dateRange[0],
        // expectedEndDate: this.form.dateRange && this.form.dateRange[1],
        // taskId: this.form.id
      }
      listBsVehicle(postData).then(res => {
        this.vehicleList = res.data
      })
    },
    getDriverList() {
      getDriverList().then(res => {
        this.driverList = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    handleClassficationTagClose() {
      this.classficationTagCloseFlag = true
    },
    handleRequirementTagClose() {
      this.requirementTagCloseFlag = true
    },
    getTagList(item) {
      let result = []

      function recursiveFlatten(children) {
        children.forEach(child => {
          if (child.children && child.children.length > 0) {
            recursiveFlatten(child.children)
          }
          if (child.tagList) {
            result = result.concat(child.tagList)
          }
        })
      }

      recursiveFlatten(item.children)
      return result
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        // this.$emit('updateData',this.form)
        if (!valid) return
        let postData = { ...this.form }
        // if (this.form.type === 'special') {
        let classificationTags = [],
          requirementTags = []
        let num = 1

        //需求格式化
        if (this.form.type === 'special' && this.form.requirementList?.length) {
          requirementTags = JSON.parse(JSON.stringify(this.form.requirementList))
          requirementTags.forEach(item => {
            if (this.requirementTagCloseFlag || !item.tagList) {
              const tagList = this.getTagList(item)
              item.tagList = tagList.map(item => {
                return {
                  ...item,
                  sortNum: Number(num++)
                }
              })
            }
          })
          postData.requirementList = [...requirementTags]
        }

        //分类格式化
        if (this.form.classificationList?.length) {
          classificationTags = JSON.parse(JSON.stringify(this.form.classificationList))
          classificationTags.forEach(item => {
            if (this.classficationTagCloseFlag || !item.tagList) {
              const tagList = this.getTagList(item)
              item.tagList = tagList.map(item => {
                return {
                  ...item,
                  sortNum: Number(num++)
                }
              })
            }
          })
          postData.classificationList = [...classificationTags]
        }
        postData.expectedStartTime = postData.dateRange[0]
        postData.expectedEndTime = postData.dateRange[1]
        if (this.dialogStatus !== 'view') {
          if (!this.form.id) {
            saveDaqReqDetail(postData).then(res => {
              this.form.code = res.data.code
              this.form.id = res.data.id
              this.form.status = res.data.status
              if (res.data.groupList.length > 0) {
                this.form.groupList = res.data.groupList
              }
              this.$emit('updateData', this.form)
            })
          } else {
            updateDaqReqDetail(postData).then(res => {
              if (res.data.groupList.length > 0) {
                this.form.groupList = res.data.groupList
              }
              this.$emit('updateData', this.form)
            })
          }
        } else {
          this.$emit('updateData', this.form)
        }
      })
    },

    formatList(list) {
      list?.forEach(req => {
        req.children ??= []
        req.tagList?.forEach(tag => {
          let groupIndex = req.children.findIndex(child => child.id === tag.groupId)
          if (~groupIndex) {
            req.children[groupIndex].tagList.push(tag)
          } else {
            req.children.push({
              code: tag.groupCode,
              id: tag.groupId,
              name: tag.groupName,
              nameCn: tag.groupNameCn,
              tagList: [tag]
            })
          }
        })
      })
      return list
    },
    handleVehilceChange({ node }) {
      this.form.vin = node.vin
      // if (node.tagEquipId && !this.form.equipmentId) {
      //   if (~this.equipmentList.findIndex(val => val.id === node.tagEquipId)) {
      //     this.form.equipmentId = node.tagEquipId
      //   }
      // }
    },
    formatTaskFunctions(row) {
      return row.map(val => {
        if (val.children?.length) {
          return { ...val, children: this.formatTaskFunctions(val.children) }
        } else if (val.items?.length) {
          return {
            ...val,
            nameCn: val.name,
            tagList: val.items.map(val => {
              val.nameCn = val.name
              return val
            })
          }
        } else {
          return { ...val, nameCn: val.name }
        }
      })
    },
    getTags(groupList, taskType) {
      let tmpGroupList = JSON.parse(JSON.stringify(groupList))
      tmpGroupList.forEach(val => {
        val.nameCn = val.name
        delete val.tagList
      })
      let postData = {
        type: 'view',
        data: {
          taskType: taskType,
          groupList: tmpGroupList
        }
      }
      this.$emit('show-tag-list', postData)
      // this.$refs.AddTagList.show(postData)
    },
    tagSave(row) {
      if (row.data.groupList?.length > 0) {
        if (row.data.taskType === 'class') {
          this.form.classificationList = row.data.groupList
        } else if (row.data.taskType === 'req') {
          let postData = {
            type: this.dialogStatus === 'view' ? 'onlyView' : 'view',
            data: row.data
          }
          this.$emit('show-tag-samples-dialog', postData)
          // this.$refs.TagSamplesDialog.show({
          //   type: this.dialogStatus === 'view' ? 'onlyView' : 'view',
          //   data: row.data
          // })
        }
      }
    },
    chooseRequirementTags() {
      this.$refs.TagRequirementDrawer.show({
        dataList: this.form.requirementList
      })
    },
    chooseTags() {
      this.$refs.TagClassificationDrawer.show({
        dataList: this.form.classificationList
      })
    },
    chooseScene() {
      this.$refs.TagSceneDrawer.show({
        dataList: this.form.taskFunctions
      })
    },
    confirmClassification(tagList) {
      this.form.classificationList = tagList
      this.$refs.formRef.validateField('classificationList')
      //this.getTags(tagList, 'class')
    },
    confirmRequirementTag(row) {
      this.form.requirementList = row
      this.$refs.formRef.validateField('requirementList')
      // this.getTags(row, 'req')
    },
    confirmScene(row) {
      this.form.taskFunctions = this.formatTaskFunctions(row)
    },
    confirmSamples(row) {
      this.form.requirementList = row.groupList
    },
    confirmClassificationTag(row) {
      this.form.classificationList = row.groupList
    },
    getTagListLength(node) {
      const that = this
      if (node.children) {
        return node.children.reduce((sum, child) => sum + that.getTagListLength(child), 0)
      }
      // 如果节点有标签列表，则返回标签列表的长度
      if (node.tagList) {
        return node.tagList.length
      }
      // 如果节点没有子节点和标签列表，则返回0
      return 0
    },
    getTaskFunctionsLength(group) {
      if (group.children?.length) {
        return group.children.reduce((sum, child) => sum + this.getTaskFunctionsLength(child), 0)
      } else if (group.items?.length) {
        return group.items.length || 0
      }
      return 0
    },
    save() {
      this.submit()
    },
    disabledEndDate(val) {
      if (this.form.startTime) {
        return new Date(val) < new Date(this.form.startTime).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.form.endTime) {
        return new Date(val) > new Date(this.form.endTime).getTime()
      }
    },
    deleteClassification(index) {
      this.form.classificationList.splice(index, 1)
    },
    deleteRequirementList(index) {
      this.form.requirementList.splice(index, 1)
    },
    deleteTaskFunction(index) {
      this.form.taskFunctions.splice(index, 1)
    },
    changeRecipient(val) {
      this.form.recipientEmpName = val.node.name
    },
    changeComplianceOfficer(val) {
      this.form.complianceOfficerEmpName = val.node.name
    }
  }
}
</script>

<style scoped lang="scss">
.el-form {
  margin-top: 10px;
}

.group-list {
  width: 100%;

  .group-item {
    display: flex;
    align-items: center;

    .tag-num {
      display: flex;
      margin-left: 10px;

      //.ltw-icon {
      //  color: #909399;
      //}

      .tag-text {
        margin-left: 5px;
        color: #409eff;
      }
    }

    .opt-btn {
      margin-left: 10px;
    }
  }
}
</style>
