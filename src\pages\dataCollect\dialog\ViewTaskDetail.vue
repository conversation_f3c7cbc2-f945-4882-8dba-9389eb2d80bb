<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="1200px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    destroy-on-close
    class="ViewTaskDetail"
  >
    <div class="task-info">
      <el-row :gutter="10">
<!--        <el-col :span="12">-->
<!--          <div class="task-info-item">-->
<!--            <div class="task-header">-->
<!--              <div class="title">{{ $t('需求') }}</div>-->
<!--            </div>-->
<!--            <requirements-form ref="RequirementsForm" />-->
<!--          </div>-->
<!--        </el-col>-->
        <el-col :span="24">
          <div class="task-info-item">
            <div class="task-header">
              <div class="title">{{ $t('任务') }}</div>
              <el-link :type="getStatusType(form.status).type" id="status">{{
                getStatusType(form.status).name
              }}</el-link>
            </div>
            <daq-task-form :item="form" ref="DaqTaskForm" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="task-charts">
      <el-tabs v-model="task.type" @tab-change="queryCharts" tab-position="left">
        <el-tab-pane :label="$t('时序图')" :name="'sequenceDiagram'">
          <car-event-statistics ref="CarEventStatistics" />
        </el-tab-pane>
        <el-tab-pane :label="$t('统计图')" :name="'statisticalChart'">
          <map-screen ref="mapScreen" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false" id="close">{{
          $t('关闭')
        }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  ElDialog,
  ElButton,
  ElRow,
  ElCol,
  ElLink,
  ElTabs,
  ElTabPane
} from 'element-plus'
// import RequirementsForm from '@/pages/data-collect/components/RequirementsForm.vue'
import DaqTaskForm from '@/pages/dataCollect/components/DaqTaskForm.vue'
import { getDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import CarEventStatistics from '@/pages/dataCollect/dialog/CarEventStatistics.vue'
import MapScreen from '@/pages/dataCollect/dialog/MapScreen.vue'
import { i18n } from '@/plugins/lang'
const defaultform = {}
export default {
  name: 'ViewTaskDetail',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      requirementsForm: {},
      task: {
        type: 'sequenceDiagram'
      },
      $t: i18n.global.t
    }
  },
  components: {
    ElDialog,
    ElButton,
    ElRow,
    ElCol,
    ElLink,
    ElTabs,
    ElTabPane,
    // RequirementsForm,
    DaqTaskForm,
    CarEventStatistics,
    MapScreen
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      // this.dialogTitle = i18n.global.t('采集需求')
      this.getDaqReqDetail(row.id)
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      // if (this.task.type === 'sequenceDiagram') {
      //   this.$refs.CarEventStatistics.cancel()
      // }
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.form = { ...defaultform }
    },
    getDaqReqDetail(id) {
      getDaqReqDetail(id).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        this.dialogTitle = res.data.code
        res.data.dateRange = [
          res.data.expectedStartTime,
          res.data.expectedEndTime
        ]
        // this.tagList = res.data.tagList
        this.form = res.data
        this.$nextTick(() => {
          // this.$refs.RequirementsForm.show({ id: res.data.reqId })
          this.queryCharts()
        })
      })
    },
    queryCharts() {
      if (this.task.type === 'sequenceDiagram') {
        if (this.form.status === 'finished') {
          this.$refs.CarEventStatistics.show({
            code: this.form.code,
            endTime: this.form.endTime
          })
        } else {
          this.$refs.CarEventStatistics.show({ code: this.form.code })
        }
      } else {
        this.$refs.mapScreen.show({ id: this.form.id })
      }
    },
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    }
  }
}
</script>

<style scoped lang="scss">
.task-info {
  //display: flex;
  //width: 100%;
  margin-bottom: 20px;
  .task-info-item {
    height: 100%;
    overflow: hidden;
    // width: 50%;
    .task-header {
      margin: 10px 0;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        padding-left: 8px;
        border-left: 2px solid rgb(0, 120, 90);
      }
    }
  }
}
</style>
