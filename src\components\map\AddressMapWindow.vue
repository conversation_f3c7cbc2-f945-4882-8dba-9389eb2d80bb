<template>
  <div class="map-address">
    <div class="map-form-item">名称：{{ item.name || '-' }}</div>
    <div class="map-form-item">地址：{{ item.address || '-' }}</div>
    <div class="map-form-item">经度: {{ item.lng || '-' }}</div>
    <div class="map-form-item">纬度: {{ item.lat || '-' }}</div>
    <div class="map-form-item" v-if="!formReadonly">
      <el-button type="primary" @click="saveAddress(item)">保存</el-button>
      <!-- <el-button @click="closeInfo">取消</el-button> -->
    </div>
  </div>
</template>

<script>
import { ElButton } from 'element-plus'
import { i18n } from '@/plugins/lang'
export default {
  name: 'AddressMapWindow',
  emits: ['reload'],
  data() {
    return {
      $t: i18n.global.t
    }
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    formReadonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    // item: {
    //   handler(val) {
    //     // // 采集时长
    //     // val.acquisitionDuration = parseFloat(
    //     //   parseFloat(val.acquisitionDuration / 60).toFixed(1)
    //     // )
    //     // // 采集数据
    //     // val.acquisitionDataSize = checkFileSize(val.acquisitionDataSize)
    //     this.row = val
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  components: {
    ElButton
  },
  mounted() {},
  methods: {
    saveAddress(row) {
      this.$emit('reload', row)
    }
  }
}
</script>

<style scoped lang="scss">
.map-address {
  position: absolute;
  left: 0;
  top: 0;
  color: #455a70;
  text-align: left;
  padding: 15px 20px;
  white-space: nowrap;
  background: #fff;
  .map-form-item {
    padding: 5px;
    font-size: 14px;
  }
  .el-button {
    min-height: 36px;
    height: 36px;
    line-height: 36px;
    padding: 0 19px;
  }
  .el-input__inner {
    height: 36px;
    line-height: 36px;
  }
}
.tangram-suggestion {
  z-index: 9999;
}
</style>
