<template>
  <div>
    <div class="empty-content" v-if="!list?.length">
      <el-empty description="暂无数据"></el-empty>
    </div>
    <div v-else>
      <el-timeline>
        <template v-for="(item, index) in list" :key="index">
          <el-timeline-item
              :center="false"
              :timestamp="item.activationDate"
              placement="top"
          >
            <el-card>
              <dbc-form
                  operation-type="view"
                  :index="index"
                  :item="item"
                  :ref="'DbcForm' + index"
              ></dbc-form>
            </el-card>
          </el-timeline-item>
        </template>
      </el-timeline>
      <el-slider
          v-if="sliderVisible"
          class="custom-slider"
          vertical
          :min="sliderMin"
          :max="sliderMax"
          :height="sliderHeight + 'px'"
          v-model="sliderValue"
          :marks="marks"
          :style="{ top: offsetTop + 'px' }"
      />
    </div>
  </div>
</template>

<script>
import DbcForm from '@/pages/fleet/components/DbcForm'
import {i18n} from '@/plugins/lang'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import Drag from '@/directives/drag-timeline'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElTimeline,
  ElTimelineItem,
  ElSlider,
  ElSelect
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
import {
  listFtmVehicleDbcTimeLineData
} from '@/apis/fleet/ftm-vehicle-dbc'

const defaultFormData = {}
export default {
  name: 'DbcConfigRecord',
  emits: ['reload'],
  data() {
    return {
      $t: i18n.global.t,
      //参数标定
      calibrationVisible: false,
      pageData: {
        records: [],
        total: 0
      },
      calibrationParamsQueryParam: {
        current: 1,
        size: 10
      },
      formVisible: false,
      form: Object.assign({}, defaultFormData),
      calibrationParamsEditFormVisible: false,
      expandKeys: [],

      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      currentRow: '',
      currentRowIndex: '',
      options: [],
      typeList: [],

      //为了上传图片的参数
      variant: '',
      list: [],
      sliderValue: 0,
      marks: {},
      sliderVisible: false,
      sliderMin: 0,
      sliderMax: 100,
      sliderHeight: 300,
      offsetTop: 0,
      vehicleId: '',
      vin: '',
      draggable: false,
      edit: false,
      activeItem: {}
    }
  },
  computed: {},
  directives: {
    Drag
  },
  components: {
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElTimeline,
    ElTimelineItem,
    ElSlider,
    ElSelect,
    LtwInput,
    DictionarySelection,
    EmployeeSelection,
    LtwIcon,
    DbcForm
  },
  created() {
  },
  methods: {
    show(row) {
      this.vin = row.vin
      this.listFtmDbcRecords()
    },
    getDiffDay(date_1, date_2) {
      // 计算两个日期之间的差值
      let totalDays, diffDate
      let myDate_1 = new Date(date_1)
      let myDate_2 = new Date(date_2)
      // 将两个日期都转换为毫秒格式，然后做差
      diffDate = myDate_1 - myDate_2 // 取相差毫秒数的绝对值

      totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整

      return totalDays // 相差的天数
    },
    // 参数标定
    listFtmDbcRecords() {
      this.list = []
      listFtmVehicleDbcTimeLineData({
        vin: this.vin,
        enabled: true
      }).then(res => {
        this.list = res.data.map(val => {
          val.draggable = false
          return val
        })
        this.edit = false
      })
    },
  }
}
</script>
<style lang="scss">
.calibration-dialog {
  .el-dialog__body {
    position: relative;
  }
}
</style>
<style scoped lang="scss">
.empty-content {
  text-align: center;
}
.custom-slider {
  position: absolute;
  left: 46px;
  // top: 0;
  display: block;
  z-index: 2;
}

.add-calibration {
  margin-left: calc(100% - 70px);
  margin-bottom: 10px;
}

.el-timeline-item {
  :deep(.el-timeline-item__wrapper) {
    width: 100%;
  }

  .form-item {
    line-height: 32px;
    color: #303133;
    // color: #606266;
  }

  .file-row {
    display: flex;

    .form-item {
      white-space: nowrap;
    }
  }

  .footer {
    position: absolute;
    bottom: 8px;
    right: 20px;
    text-align: right;

    .el-link {
      margin-left: 6px;
    }
  }
}
</style>
