<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
      :hide-required-asterisk="formReadonly"
    >
      <el-form-item :label="$t('日期')" prop="dateRange">
        <el-date-picker
          :disabled="formReadonly"
          v-model="form.dateRange"
          type="daterange"
          :range-separator="$t('到')"
          :start-placeholder="$t('开始日期')"
          :end-placeholder="$t('结束日期')"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item :label="$t('传感器类型')" prop="sensorType">
        <dictionary-type-selection
          v-model="form.sensorType"
          :placeholder="$t('请选择')"
          :disabled="formReadonly"
          dictionaryType="sensor_type"
          @change="changeSensorType"
          filterable
        />
      </el-form-item>
      <el-form-item
        v-if="form.sensorType !== 'Vehicle'"
        :label="$t('传感器')"
        prop="modality"
      >
        <common-selection
          v-model="form.modality"
          :model-code="'code'"
          :model-name="'name,code'"
          :model-options="modalityData"
          :disabled="formReadonly"
          @change="changeModality"
        />
      </el-form-item>
      <el-form-item
        v-if="form.sensorType !== 'domain_controller'"
        :label="$t('软件版本')"
        prop="softwareVersion"
      >
        <ltw-input
          v-model="form.softwareVersion"
          :disabled="formReadonly"
        ></ltw-input>
      </el-form-item>
      <el-form-item
        v-if="form.sensorType === 'domain_controller'"
        :label="$t('ASW版本')"
        prop="aswVersion"
      >
        <ltw-input
          v-model="form.aswVersion"
          :disabled="formReadonly"
        ></ltw-input>
      </el-form-item>
      <el-form-item
        v-if="form.sensorType === 'domain_controller'"
        :label="$t('BSW版本')"
        prop="bswVersion"
      >
        <ltw-input
          v-model="form.bswVersion"
          :disabled="formReadonly"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('备注')" prop="remark">
        <ltw-input
          v-model="form.remark"
          :disabled="formReadonly"
          textType="remark"
          type="textarea"
          id="remark"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="dialogVisible = false"
          v-if="dialogStatus === 'view'"
          id="view-cancel"
          >{{ $t('关闭') }}</el-button
        >
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{
            $t('取消')
          }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{
            $t('保存')
          }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { i18n } from '@/plugins/lang'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection'
import CommonSelection from '@/components/system/CommonSelection'
import {
  getVehicleSoftwareVersions,
  saveVehicleSoftwareVersions,
  updateVehicleSoftwareVersions
} from '@/apis/fleet/ftm-software-versions'
import { listFtmVehicleModality } from '@/apis/fleet/ftm-vehicle-modality'
const defaultform = {
  sensorType: '',
  sensorTypeName: '整车'
}
export default {
  name: 'AddDriver',
  emits: ['reload'],
  components: { DictionaryTypeSelection, CommonSelection },
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        sensorType: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('传感器类型'),
            trigger: 'change'
          }
        ],
        modality: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('传感器'),
            trigger: 'change'
          }
        ],
        dateRange: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('日期'),
            trigger: 'change'
          }
        ],
        softwareVersion: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('软件版本'),
            trigger: 'change'
          }
        ],
        bswVersion: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('BSW版本'),
            trigger: 'change'
          }
        ],
        aswVersion: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('ASW版本'),
            trigger: 'change'
          }
        ]
        // sensorType: [
        //   {
        //     required: true,
        //     message: this.$t('请选择'),
        //     trigger: 'change'
        //   }
        // ],
      },
      $t: i18n.global.t,
      id: '',
      sensorData: [],
      modalityData: [],
      index: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    showModalityList() {
      this.visible = true
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      // 判断是否更新调用接口
      // this.form.variant = row.data.variant
      switch (row.type) {
        case 'add':
          this.dialogTitle = i18n.global.t('新增') + i18n.global.t('软件版本')
          this.form.vin = row.data.vin
          break
        case 'edit':
          this.dialogTitle = i18n.global.t('编辑') + i18n.global.t('软件版本')
          this.form.id = row.data.id
          this.getVehicleSoftwareVersions()
          break
        case 'view':
          this.dialogTitle = i18n.global.t('软件版本') + i18n.global.t('详情')
          this.form.id = row.data.id
          this.getVehicleSoftwareVersions()
          break
      }
    },
    getVehicleSoftwareVersions() {
      getVehicleSoftwareVersions(this.form.id).then(res => {
        if (res.data.startDate && res.data.endDate) {
          res.data.dateRange = [res.data.startDate, res.data.endDate]
        }
        this.form = res.data
        this.changeSensorType()
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      if (this.visible) {
        this.closeRequirementList()
      }
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          startDate: this.form.dateRange?.length && this.form.dateRange[0],
          endDate: this.form.dateRange?.length && this.form.dateRange[1]
        }
        // if (postData.sensorType === 'domain_controller') {
        //   delete postData.softwareVersion
        // } else {
        //   delete postData.aswVersion
        //   delete postData.bswVersion
        // }
        if (this.dialogStatus === 'add') {
          saveVehicleSoftwareVersions(postData).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
          })
        }
        if (this.dialogStatus === 'edit') {
          updateVehicleSoftwareVersions(postData).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
          })
        }
      })
    },

    listFtmVehicleModality() {
      listFtmVehicleModality({ sensorType: this.form.sensorType }).then(res => {
        this.modalityData = res.data
      })
    },
    changeSensorType(row) {
      if (row) {
        this.form.modality = ''
        this.form.sensorTypeName = row.name
        if (row?.code === 'domain_controller') {
          this.form.softwareVersion = ''
        } else if (row?.code === 'Vehicle') {
          this.form.modality = 'Vehicle'
        } else {
          this.form.aswVersion = ''
          this.form.bswVersion = ''
        }
      }
      this.listFtmVehicleModality()
      // this.listFtmSensorSelection()
    },
    changeModality(row) {
      this.form.modalityName = row['name,code']
    },
    clearTask() {
      this.form.sensorId = ''
      this.form.sensorModelSpecification = ''
      // this.row = ''
      this.closeRequirementList()
    }
  }
}
</script>

<style scoped lang="scss"></style>
