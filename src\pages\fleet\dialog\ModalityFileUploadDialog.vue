<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-button v-if="!formReadonly" type="primary" @click="addTableRow">
      <ltw-icon icon-code="el-icon-plus"></ltw-icon>
      新增
    </el-button>
    <el-table :data="attachmentList" style="width: 100%">
      <el-table-column prop="sourceType" label="配置文件类型">
        <template #default="scope">
          <dictionary-selection 
            :disabled="scope.row.id != null"
            v-model="scope.row.sourceType"
            dictionaryType="vehicle_modality_file"
          ></dictionary-selection>
        </template>
      </el-table-column>
      <el-table-column label="附件">
        <template #default="scope">
          <upload-file
            ref="uploadImage"
            :disabled="!scope.row.sourceType"
            v-if="!scope.row.id"
            :limit="1"
            :source-id="scope.row.sourceId"
            :source-type="scope.row.sourceType"
            listType="text"
            v-model="scope.row.fileId"
            class="file-btn"
          />
          <el-link target="_blank" v-else :href="scope.row.url">
            <span style="display: inline">
              <span class="inline-span">{{ scope.row.fileName }}</span>
              <span v-if="scope.row.fileType">.{{ scope.row.fileType }}</span>
            </span>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" v-if="!formReadonly">
        <template #default="scope">
          <el-button type="primary" v-if="!scope.row.id" @click="saveAttachment(scope.row)">
            <ltw-icon icon-code="el-icon-check"></ltw-icon>
          </el-button>
          <el-button type="danger" v-if="!scope.row.id" @click="deleteRow(scope.$index)">
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
          </el-button>
          <el-button type="danger" v-if="scope.row.id" @click="deleteAttachment(scope.row, scope.$index)">
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util from '@/plugins/util'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import { showToast, showConfirmToast } from '@/plugins/util'
import { updateVehicleModalityFile } from '@/apis/fleet/modality-file-upload-dialog.js'
import { deleteFile, getFileList } from '@/apis/base/file'

const defaultform = {}
export default {
  name: 'ModalityFileUploadDialog',
  components: {
    // JsonEditorVue,
    // Codemirror
    DictionarySelection,
    UploadFile
  },
  emits: ['reload'],
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      attachmentList: [],
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      dialogStatus: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('上传传感器附件')
      this.dialogStatus = row.type
      this.form.id = row.id
      this.getModalityFileList(row.id)
    },
    deleteRow(index) {
      this.attachmentList.splice(index, 1)
    },
    deleteAttachment(row, index) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        this.attachmentList.splice(index, 1)
        deleteFile(row.id).then(() => {
          showToast('删除成功')
          this.getModalityFileList(this.form.id)
        })
      })
    },
    getModalityFileList(id) {
      let postData = {
        sourceId: id
      }
      getFileList(postData).then(res => {
        res.data.forEach(val => {
          val.url = this.downloadUrl + val.id + '?token=' + util.getToken()
        })
        this.attachmentList = res.data
      })
    },
    saveAttachment(row) { 
      if (!row.sourceType) {
        showToast('请先选择附件类型', 'warning')
        return
      }
      if (!row.fileId) {
        showToast('请先上传附件', 'warning')
        return
      }
      row.sourceId = this.form.id
      updateVehicleModalityFile(row).then(res => {
        if (res.data) {
          showToast('保存成功')
          this.getModalityFileList(this.form.id)
        } else {
          showToast('上传失败')
        }
      })
    },
    addTableRow() {
      this.attachmentList.push({})
    },
    dialogClosed() {
      this.initForm()
      this.$emit('reload')
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    initForm() {
      this.form = { ...defaultform }
    }
  }
}
</script>

<style scoped lang="scss">
.file-btn,
.file-btn > :deep(div) {
  display: flex;
}

.inline-span {
  display: inline;
}
</style>
