<template>
  <el-header class="ltw-header" :class="{ 'login-header': $router.currentRoute.value.path === '/login' }">
    <div class="ltw-header-left">
      <div class="title-container">
        <img @click="goHome()" alt="" class="logo bosch" :src="themeType === 'dark' ? boschDarkSrc : boschLightSrc" />
        <img @click="goHome()" alt="" class="logo pmt" :src="themeType === 'dark' ? pmtDarkSrc : pmtLightSrc" />
        <img @click="goHome()" alt="" class="logo arena" :src="themeType === 'dark' ? arenaDarkSrc : arenaLightSrc" />
        <!--        <img @click="goHome()" class="logo" :src="logoSrc" v-if="logoSrc" />-->
        <span>
          <slot name="title"> </slot>
        </span>
      </div>
      <slot name="left"></slot>
    </div>
    <div class="ltw-header-center">
      <slot name="center"></slot>
    </div>
    <div class="ltw-header-right">
      <slot name="right">
        <div class="time-container" v-if="showTime">
          {{ currentTime }}
        </div>
        <div class="login-list">
          <el-link v-if="$router.currentRoute.value.path === '/forgetPassword'" @click="register" :underline="false"
            >{{ $t('注册') }}
          </el-link>
          <el-divider v-if="$router.currentRoute.value.path === '/forgetPassword'" direction="vertical" />
          <el-link v-if="$router.currentRoute.value.path === '/register'" @click="forgetPassword" :underline="false"
            >{{ $t('忘记密码') }}
          </el-link>
          <el-divider v-if="$router.currentRoute.value.path === '/register'" direction="vertical" />
          <el-link
            @click="login"
            v-if="
              $router.currentRoute.value.path === '/register' || $router.currentRoute.value.path === '/forgetPassword'
            "
            :underline="false"
            >{{ $t('登录') }}
          </el-link>
          <el-divider
            v-if="
              $router.currentRoute.value.path === '/register' || $router.currentRoute.value.path === '/forgetPassword'
            "
            direction="vertical"
          />
          <el-link class="switch-skin" @click="switchSkin()" :underline="false">
            <ltw-icon icon-code="svg-skin"></ltw-icon>
            {{ $t('皮肤切换') }}
          </el-link>
        </div>

        <el-dropdown @command="changeLang">
          <span class="dropDown"> CN/EN </span>
          <template #dropdown>
            <el-dropdown-menu class="menu">
              <el-dropdown-item
                v-for="item in locales"
                :key="item"
                :command="item"
                :class="{ active: locale === item }"
              >
                {{ languageLabels[item] }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown @command="changeLoginUser" class="change-user">
          <span class="dropDown swith-user">
            <span v-if="currentUser.currentTenantCode">{{ currentUser.currentTenantName}}</span>
            <span class="login-swith" v-else>{{ $t('租户切换') }}</span></span
          >
          <template #dropdown>
            <el-dropdown-menu class="menu" v-show="loginUserList && loginUserList.length">
              <el-dropdown-item
                v-for="item in loginUserList"
                :key="item.id"
                :command="item.id"
                :class="{ active: currentUser.currentTenantCode === item.code }"
              >
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown v-if="currentUser && currentUser.userName">
          <!-- <div class="avatar-wrapper">
          <img :src="avatar"
               class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div> -->

          <div class="user-container">
            <div class="user-info-container" @click="showUserDetail">
              <el-avatar
                size="large"
                src="https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"
              ></el-avatar>
              <span>{{ currentUser && currentUser.userName }}</span>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link> -->
              <!-- <el-dropdown-item @click="setting = true">
                <span>布局设置</span>
              </el-dropdown-item> -->
              <!-- <el-dropdown-item @click="updatePassword">
                <span>Update Password</span>
              </el-dropdown-item> -->
              <!-- divided -->
              <el-dropdown-item class="dropdown-divided" @click="logout">
                <span>Sign Out</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown v-if="roleList?.length">
          <div class="user-container">
            <div class="user-info-container">
              <span>{{ currentRole.name }}</span>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in roleList" :key="item.id" @click="handleRoleClick(item)">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- <div class="update-pwd-container">
          <i class="el-icon-lock" @click="updatePassword"></i>
          <el-button type="text" @click="updatePassword"
            >Update Password</el-button
          >
        </div> -->
        <!-- <div class="logout-container">
          <i class="iconfont icon-exit" @click="logout"></i>
          <span @click="logout">Sign Out</span>
        </div> -->
      </slot>
    </div>
  </el-header>
  <el-dialog title="修改密码" v-model="updatePasswordDialogVisible" @close="handleDialogClose">
    <el-form :model="updatePasswordFormData" :rules="updatePasswordFormRules" ref="updatePasswordFormRef">
      <el-form-item label="旧密码" prop="oldPwd">
        <ltw-input
          id="oldPwd"
          v-model="updatePasswordFormData.oldPwd"
          :placeholder="$t('请输入')"
          show-password
        ></ltw-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <ltw-input
          id="newPwd"
          v-model="updatePasswordFormData.newPwd"
          :placeholder="$t('请输入')"
          show-password
        ></ltw-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="rePwd">
        <ltw-input
          id="rePwd"
          v-model="updatePasswordFormData.rePwd"
          :placeholder="$t('请输入')"
          show-password
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button id="cancelUpdate" @click="cancelUpdate">{{ $t('取 消') }}</el-button>
        <el-button id="confirmUpdate" type="primary" @click="confirmUpdate">{{ $t('确 定') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import { checkInitialPwd, updatePassword, getLoginUserList, changeLoginUser } from '@/apis/base/index'
import util, { setLocale, checkKeyboardContinuousChar, isContinousNumOrChar } from '@/plugins/util'
import { useI18n } from 'vue-i18n'
import LtwIcon from '@/components/base/LtwIcon'

export default {
  name: 'LtwHeader',
  components: { LtwIcon },
  emits: ['reload', 'switch-skin', 'handleRoleChange'],
  props: {
    themeType: String,
    showTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let validateNewPwd = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入新密码'))
      }

      if (value.length < 8) {
        return callback(new Error('新密码至少8位以上'))
      }

      if (this.updatePasswordFormData.oldPwd === this.updatePasswordFormData.newPwd) {
        return callback(new Error('新密码和旧密码不可相同'))
      }

      //不能连续12345、abcde

      if (!this.updatePasswordFormData.rePwd) {
        this.$refs.updatePasswordFormRef.validateField('rePwd')
      }
      callback()
    }
    let validateRePwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认密码'))
      } else if (value !== this.updatePasswordFormData.newPwd) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      currentTime: '',
      currentUser: {},
      updatePasswordDialogVisible: false,
      updatePasswordFormData: {},
      pwdInitial: false,
      currentTimeInterval: '',
      updatePasswordFormRules: {
        oldPwd: [{ required: true, message: '请输旧密码', trigger: ['blur', 'change'] }],
        newPwd: [
          {
            required: true,
            validator: validateNewPwd,
            trigger: ['blur', 'change']
          },
          {
            validator: checkKeyboardContinuousChar,
            trigger: ['blur', 'change']
          },
          {
            validator: isContinousNumOrChar,
            trigger: ['blur', 'change']
          },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
            message: '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为 8 - 30位',
            trigger: ['blur', 'change']
          }
        ],
        rePwd: [
          {
            required: true,
            validator: validateRePwd,
            trigger: ['blur', 'change']
          }
        ]
      },
      locales: ['zh', 'en'],
      languageLabels: {
        zh: '简体中文',
        en: 'English'
      },
      locale: '',
      loginTheme: '',
      boschDarkSrc: require('@/assets/images/login/bosch-dark.svg'),
      boschLightSrc: require('@/assets/images/login/bosch-light.svg'),
      pmtDarkSrc: require('@/assets/images/login/pmt-dark.svg'),
      pmtLightSrc: require('@/assets/images/login/pmt-light.svg'),
      arenaDarkSrc: require('@/assets/images/login/arena-dark.svg'),
      arenaLightSrc: require('@/assets/images/login/arena-light.svg'),
      currentRole: { id: 'all', name: 'All' },
      roleList: [],
      loginUserList: []
    }
  },
  created() {
    this.loginTheme = localStorage.getItem('login-theme') || 'light'
    this.$emit('switch-skin', this.loginTheme)
    this.locale = useI18n().locale.value

    // console.log(this.$router.currentRoute.value.path)
    if (!~GLB_CONFIG.noAuthRouterPath.indexOf(this.$router.currentRoute.value.path)) {
      this.currentUser = this.$store.state.permission.currentUser
      if (this.currentUser?.employee?.roleListMap?.actor?.length) {
        this.roleList = [
          {
            id: 'all',
            name: 'All'
          },
          ...this.currentUser.employee.roleListMap.actor
        ]
      }

      if (this.showTime) {
        this.currentTimeInterval = setInterval(() => {
          this.getCurrentTime()
        }, 1000)
      }
      this.getLoginUserList()
      // this.checkInitialPwd()
    }
  },
  methods: {
    getLoginUserList() {
      this.loginUserList = []
      getLoginUserList().then(res => {
        this.loginUserList = res.data
      })
    },
    changeLoginUser(command) {
      changeLoginUser(command).then(() => {
        location.reload()
      })
    },
    getCurrentTime() {
      let date = new Date()
      this.currentTime =
        date.getFullYear() +
        '年' +
        (date.getMonth() + 1) +
        '月' +
        date.getDate() +
        '日' +
        date.getHours() +
        '时' +
        date.getMinutes() +
        '分'
    },
    updatePassword() {
      this.updatePasswordDialogVisible = true
    },
    cancelUpdate() {
      this.updatePasswordDialogVisible = false
    },
    confirmUpdate() {
      this.$refs.updatePasswordFormRef.validate(valid => {
        if (!valid) return
        let data = {}
        data.password = this.updatePasswordFormData.oldPwd
        data.newPassword = this.updatePasswordFormData.newPwd
        let encryptData = util.encrypt(JSON.stringify(data))
        updatePassword(encryptData).then(() => {
          this.pwdInitial = false
          this.updatePasswordDialogVisible = false
          this.$alert('密码修改成功，请重新登录！', '提示', {
            confirmButtonText: '确定',
            callback: () => {
              util.toLogin()
            }
          })
        })
      })
    },
    handleDialogClose() {
      this.$refs.updatePasswordFormRef.resetFields()
      if (this.pwdInitial) {
        this.alertInitialPwd()
      }
    },
    alertInitialPwd() {
      this.$alert('您的密码是初始密码，为了您账户的安全，请修改密码！', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          this.pwdInitial = true
          this.updatePasswordDialogVisible = true
        }
      })
    },
    logout() {
      util.logout()
    },
    checkInitialPwd() {
      checkInitialPwd().then(res => {
        if (res.data === true) {
          this.alertInitialPwd()
        }
      })
    },
    changeLang(command) {
      setLocale(command)
    },
    goHome() {
      if (this.currentUser.userName) {
        this.$emit('reload')
      } else {
        this.login()
      }
    },
    forgetPassword() {
      this.$router.push('/forgetPassword')
    },
    register() {
      this.$router.push('/register')
    },
    login() {
      this.$router.push('/login')
    },
    switchSkin() {
      this.loginTheme = this.loginTheme === 'light' ? 'dark' : 'light'
      localStorage.setItem('login-theme', this.loginTheme)
      this.$emit('switch-skin', this.loginTheme)
    },
    handleRoleClick(item) {
      if (item.name === this.currentRole.name) return
      this.$store
        .dispatch('listSysPrivilegeMenuOfCurrentUserByRole', item.id)
        .then(() => {
          this.currentRole = item
          this.$emit('handleRoleChange', item.id)
        })
        .catch(error => {
          console.error('获取权限菜单失败:', error)
        })
    }
  }
}
</script>

<style scoped lang="scss">
.dropdown-divided {
  flex-direction: column;
  padding: 0;

  &::before {
    width: 100%;
  }

  span {
    padding: 0 17px;
  }
}

:deep(.el-dropdown-menu__item.active) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}

.el-dropdown {
  cursor: pointer;
}

.ltw-header {
  // background: linear-gradient(90deg, #569bf3, #2a81da);
  background-image: url('@/assets/images/login/header-light.png');
  background-size: 100% 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 0;
  border-bottom: solid 1px #dad7d7;
  color: #000000;
  text-align: center;
  line-height: 60px;
  height: 60px;

  &.login-header {
    transition: all 0.3s;
    background: #fff;
    padding: 0 10%;

    .login-list .el-link,
    .switch-skin,
    .dropDown,
    .el-dropdown {
      color: var(--el-link-text-color);
    }

    .logo.pmt {
      position: relative;
      top: -8px;
    }
  }
  .tenant-text {
    color: #fff;
    margin-left: 5px;
    line-height: 62px;
  }

  .title-container span {
    color: #fff;
  }

  :deep(.el-menu-item) {
    transition: all 0.3s;
    color: #fff;
    // color: #fff !important;
    &.is-active,
    &:focus {
      background: rgba(230, 243, 255, 0);
      border-bottom: 2px solid #fff;
      color: #fff !important;
    }

    &:hover {
      background: rgba(230, 243, 255, 1);
      color: #1d5e99 !important;
    }
  }

  .login-list .el-link,
  .switch-skin,
  .dropDown,
  .el-dropdown {
    color: #fff;
  }

  .login-list .el-link,
  .switch-skin,
  .dropDown {
    &:hover {
      color: var(--el-link-hover-text-color);
    }
  }

  .logo {
    cursor: pointer;
    object-fit: cover;
    height: 34px;

    &.bosch {
      height: 80px;
      width: 160px;
    }

    &.pmt {
      height: 80px;
      width: 100px;
      margin: 0 20px;
    }

    &.arena {
      height: 76px;
      width: 160px;
    }
  }

  .ltw-header-left {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    // min-width: 40%;
    flex-grow: 1;

    .title-container {
      height: 100%;
      padding: 0 10px;
      // width: 200px;
      // width: 300px;
      display: flex;
      flex-direction: row;
      align-items: center;
      // margin-right: 10px;
      //img {
      //  height: 34px;
      //}

      span {
        margin-left: 10px;
      }
    }

    .el-menu {
      margin-left: 20px;
    }
  }

  .ltw-header-center {
    height: 100%;
  }

  .ltw-header-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;

    .login-list {
      margin-right: 10px;
      display: flex;
      align-items: center;
      color: #fff;

      .switch-skin {
        &:hover {
          color: var(--el-link-hover-text-color);

          :deep(.svg-icon) {
            color: #409eff;
          }
        }

        :deep(.svg-icon) {
          font-size: 20px;
          margin-right: 5px;
        }
      }
    }

    .time-container {
      font-size: 14px;
      margin-right: 20px;
    }

    .user-container {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      // width: 150px;

      .user-info-container {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 14px;

        span {
          margin-left: 10px;
        }
      }

      .user-detail-container {
        width: 200px;
        position: relative;
        left: -70px;
        top: 60px;
        background-color: #ffffff;
        color: black;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        border-radius: 3px;
        z-index: 999999;
        box-shadow: rgb(0 0 0 / 20%) 0px 3px 6px !important;

        .avatar {
          transform: translate(-50%, -50%);
          position: absolute;
          left: 50%;
          height: 80px;

          .el-avatar {
            width: 80px;
            height: 80px;
            padding: 0;
          }
        }

        .user-name-container {
          border-bottom: 1px solid #f4f4f4;
          font-size: 14px;
          line-height: 20px;
          color: #212121;
          padding: 5px;
          font-weight: 600;
        }

        .item-container {
          border-bottom: 1px solid #f4f4f4;
          line-height: 20px;
          font-size: 14px;
          color: #979797;
          padding: 10px;

          .el-icon-d-arrow-left {
            margin-right: 5px;
          }
        }
      }
    }

    .update-pwd-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 20px;

      span {
        cursor: pointer;
      }

      i {
        cursor: pointer;
        font-size: 20px;
        margin-right: 5px;
      }
    }

    .logout-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 14px;

      i {
        cursor: pointer;
        font-size: 20px;
        margin-right: 5px;
      }

      span {
        cursor: pointer;
      }
    }
  }
  .change-user {
    margin-left: 10px;
  }
}

.dark {
  .login-header.ltw-header {
    background: rgba(255, 255, 255, 0);
    border-bottom: solid 1px rgba(255, 255, 255, 0);
  }

  .ltw-header {
    background: rgba(15, 23, 59, 1);

    .title-container span {
      color: #fff;
    }

    :deep(.el-menu-item) {
      color: #fff;

      &:hover,
      &:focus,
      &.is-active {
        // color: var(--el-menu-hover-text-color);
        color: #fff !important;
        background: #182864;
      }
    }

    .login-list .el-link,
    .switch-skin,
    .dropDown,
    .el-dropdown {
      color: #fff;
    }

    .logo.pmt {
      top: 0;
    }
  }
}
</style>
