import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveVtTagRecord = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records', data, params})
export const updateVtTagRecord = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records', data, params})
export const deleteVtTagRecord = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records', params})
export const listVtTagRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records', params})
export const listVtTagRecordSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/selections', params})
export const pageVtTagRecord = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/page', params})
export const pageVtTagRecordAbnormal = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/abnormal/page', params})
export const getTagRecordAbnormalChart = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/abnormal/chart', params})
export const getVtTagRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/' + id})
export const clickTag = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/click', data, params})
