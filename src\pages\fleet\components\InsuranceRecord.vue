<template>
  <div>
    <el-timeline>
      <template v-for="(item, index) in list" :key="index">
        <el-timeline-item
            :center="false"
            :timestamp="item.endDate"
            placement="top"
        >
          <el-card>
            <insurance-form
                :index="index"
                :item="item"
                :ref="'insuranceForm' + index"
            ></insurance-form>
          </el-card>
        </el-timeline-item>
      </template>
    </el-timeline>
  </div>
</template>

<script>
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElTimeline,
  ElTimelineItem,
  ElSlider,
  ElSelect
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
import InsuranceForm from '@/pages/fleet/components/InsuranceForm.vue'

const defaultFormData = {}
export default {
  name: 'InsuranceRecord',
  emits: ['reload'],
  data() {
    return {
      list:[]
    }
  },
  computed: {},
  components: {
    InsuranceForm,
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElTimeline,
    ElTimelineItem,
    ElSlider,
    ElSelect,
    LtwInput,
    DictionarySelection,
    LtwIcon,
  },
  created() {
  },
  methods: {
    show(data) {
      this.list=data
    },
  }
}
</script>
<style lang="scss">
.calibration-dialog {
  .el-dialog__body {
    position: relative;
  }
}
</style>
<style scoped lang="scss">
.custom-slider {
  position: absolute;
  left: 46px;
  // top: 0;
  display: block;
  z-index: 2;
}

.el-timeline-item {
  :deep(.el-timeline-item__wrapper) {
    width: 100%;
  }

  .form-item {
    line-height: 32px;
    color: #303133;
    // color: #606266;
  }

  .file-row {
    display: flex;

    .form-item {
      white-space: nowrap;
    }
  }
}
</style>
