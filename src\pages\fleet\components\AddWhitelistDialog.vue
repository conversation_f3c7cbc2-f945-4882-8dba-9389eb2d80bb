<template>
  <div>
    <el-dialog
      v-model="visible"
      title="新增白名单"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <!-- 步骤指示器 -->
      <div class="steps-container">
        <el-steps :active="currentStep" align-center>
          <el-step title="基础信息">
            <!-- <template #icon>
              <el-icon v-if="currentStep > 0"><Check /></el-icon>
              <span v-else class="step-number">1</span>
            </template> -->
          </el-step>
          <el-step title="制定topic">
            <!-- <template #icon>
              <el-icon v-if="currentStep > 1"><Check /></el-icon>
              <span v-else class="step-number">2</span>
            </template> -->
          </el-step>
          <el-step title="完成">
            <!-- <template #icon>
              <el-icon v-if="currentStep > 2"><Check /></el-icon>
              <span v-else class="step-number">3</span>
            </template> -->
          </el-step>
        </el-steps>
      </div>

      <!-- 第一步：基础信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-form
          ref="basicFormRef"
          :model="basicForm"
          :rules="basicFormRules"
          label-width="120px"
          class="basic-form"
        >
          <!-- 车型 -->
          <el-form-item label="车型" prop="vehicleType">
            <el-select
              v-model="basicForm.vehicleType"
              placeholder="请选择车型"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in vehicleTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <!-- 配置方式 -->
          <el-form-item label="配置方式" prop="configMethod">
            <el-radio-group v-model="basicForm.configMethod" @change="handleConfigMethodChange">
              <el-radio-button label="jfrog" value="jfrog">JFrog制品库</el-radio-button>
              <el-radio-button label="upload" value="upload">手动上传</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 选择配置文件 -->
          <el-form-item label="选择配置文件" prop="configFile">
            <div class="config-file-section">
              <div v-if="basicForm.configMethod === 'jfrog'" class="jfrog-section">
                <div class="jfrog-file-input">
                  <el-input
                    v-model="selectedFileName"
                    placeholder="请选择配置文件"
                    disabled
                    class="file-display-input"
                  />
                  <el-button 
                    type="primary" 
                    @click="showFileSelectDialog"
                    :loading="jfrogLoading"
                  >
                    选择文件
                  </el-button>
                </div>
              </div>
              
              <div v-else class="upload-section">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :show-file-list="false"
                  accept=".zip"
                  :on-change="handleFileUpload"
                  drag
                >
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">
                      <p>点击或拖拽文件到此区域上传</p>
                      <p class="upload-tip">只支持 .zip 格式文件</p>
                    </div>
                  </div>
                </el-upload>
                <div v-if="uploadedFile" class="uploaded-file">
                  <el-icon><Document /></el-icon>
                  <span>{{ uploadedFile.name }}</span>
                  <el-button type="text" @click="removeUploadedFile">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- Function -->
          <el-form-item label="Function" prop="function">
            <ltw-input
              v-model="basicForm.function"
              placeholder="请输入功能名称"
              @input="generateFileName"
              clearable
            />
          </el-form-item>

          <!-- Version -->
          <el-form-item label="Version" prop="version">
            <ltw-input
              v-model="basicForm.version"
              placeholder="请输入版本号"
              @input="generateFileName"
              clearable
            />
          </el-form-item>

          <!-- 白名单文件名 -->
          <el-form-item label="白名单文件名" prop="fileName">
            <el-input
              v-model="basicForm.fileName"
              placeholder="文件名将根据输入信息自动生成"
              @input="handleFileNameInput"
              clearable
            />
            <div class="filename-hint">
              <span class="hint-text">默认文件名规则：租户_车型_功能_版本_时间.json</span>
            </div>
          </el-form-item>

          <!-- 描述 -->
          <el-form-item label="描述">
            <el-input
              v-model="basicForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入描述信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第二步：制定topic -->
      <div v-else-if="currentStep === 1" class="step-content">
        <TopicConfigStep
          :zip-path="basicForm.configFile"
          @topics-changed="handleTopicsChanged"
        />
      </div>

      <!-- 第三步：结果展示 -->
      <div v-else-if="currentStep === 2" class="step-content">
        <WhitelistResultStep
          :result-data="saveResultData"
          @preview-file="handlePreviewFile"
        />
      </div>

      <!-- 底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <!-- 第三步只显示完成按钮 -->
          <template v-if="currentStep === 2">
            <el-button
              type="primary"
              @click="handleNext"
              :loading="nextLoading"
            >
              完成
            </el-button>
          </template>

          <!-- 前两步显示完整按钮组 -->
          <template v-else>
            <el-button @click="handleClose">取消</el-button>
            <el-button v-if="currentStep > 0" @click="handlePrevious">上一步</el-button>
            <el-button
              type="primary"
              @click="handleNext"
              :loading="nextLoading"
              :disabled="!canProceedToNext"
            >
              下一步
            </el-button>
          </template>
        </div>
      </template>
    </el-dialog>

    <!-- JFrog 文件选择器 -->
    <JFrogFileSelector
      v-model="fileSelectDialogVisible"
      @file-selected="handleFileSelected"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Check, 
  UploadFilled, 
  Document, 
  Delete, 
  Setting,
  Search,
  Loading
} from '@element-plus/icons-vue'
import { searchZipPackagesByProperties, saveAndUploadWhitelist } from '@/apis/fleet/whitelist-management'
import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import JFrogFileSelector from './JFrogFileSelector.vue'
import TopicConfigStep from './TopicConfigStep.vue'
import WhitelistResultStep from './WhitelistResultStep.vue'

export default {
  name: 'AddWhitelistDialog',
  components: {
    Check,
    UploadFilled,
    Document,
    Delete,
    Setting,
    Search,
    Loading,
    JFrogFileSelector,
    TopicConfigStep,
    WhitelistResultStep
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    vehicleType: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })

    const currentStep = ref(0)
    const nextLoading = ref(false)
    const jfrogLoading = ref(false)
    const basicFormRef = ref(null)
    const uploadRef = ref(null)

    // 车型选项
    const vehicleTypeOptions = ref([])
    
    // JFrog 文件选项
    const jfrogFileOptions = ref([])
    const uploadedFile = ref(null)
    
    // 文件选择对话框相关
    const fileSelectDialogVisible = ref(false)
    const selectedFileName = ref('')

    // 用户是否手动修改过文件名
    const userModifiedFileName = ref(false)

    // Topic配置相关
    const topicConfig = ref({
      total: 0,
      selected: 0,
      topics: []
    })

    // 保存结果数据
    const saveResultData = ref({
      dataSize: 0,
      fileAddress: '',
      fileId: '',
      fileName: '',
      remark: '',
      topicInfoList: [],
      topicSum: 0
    })

    // 基础表单数据
    const basicForm = reactive({
      vehicleType: '',
      configMethod: 'jfrog',
      configFile: '',
      function: '',
      version: '',
      fileName: '',
      description: ''
    })

    // 表单验证规则
    const basicFormRules = {
      vehicleType: [{ required: true, message: '请选择车型', trigger: 'change' }],
      configMethod: [{ required: true, message: '请选择配置方式', trigger: 'change' }],
      configFile: [{ required: true, message: '请选择配置文件', trigger: 'change' }],
      function: [{ required: true, message: '请输入功能名称', trigger: 'blur' }],
      version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
      fileName: [
        { required: true, message: '请输入文件名' },
        { 
          pattern: /^[a-zA-Z0-9_\-\.]+\.json$/, 
          message: '文件名格式不正确，必须以.json结尾', 
          trigger: 'blur' 
        }
      ]
    }

    // 加载车型选项
    const loadVehicleTypeOptions = async () => {
      try {
        const response = await listFtmVehicleVariant()

        if (response.data) {
          vehicleTypeOptions.value = response.data.map(item => ({
            label: item.code,
            value: item.code
          }))
        }
      } catch (error) {
        console.error('加载车型选项失败:', error)
       // ElMessage.error('加载车型选项失败')
      }
    }

    // 自动生成文件名
    const generateFileName = () => {
      if (basicForm.function && basicForm.version && basicForm.vehicleType) {
        const tenant = 'J6M' // 默认租户，可以从配置或用户信息获取
        const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
        basicForm.fileName = `${tenant}_${basicForm.vehicleType}_${basicForm.function}_${basicForm.version}_${timestamp}.json`
        // 重新生成后，重置用户修改标记
        userModifiedFileName.value = false
      }
    }

    // 监听车型变化
    watch(() => props.vehicleType, (newValue) => {
      if (newValue) {
        basicForm.vehicleType = newValue
      }
      // 车型变化时重新生成文件名
      generateFileName()
    }, { immediate: true })

    // 监听车型选择变化
    watch(() => basicForm.vehicleType, () => {
      // 车型选择变化时重新生成文件名
      generateFileName()
    })

    // 监听功能名称变化
    watch(() => basicForm.function, () => {
      // 功能变化时重新生成文件名
      generateFileName()
    })

    // 监听版本变化
    watch(() => basicForm.version, () => {
      // 版本变化时重新生成文件名
      generateFileName()
    })

    // 添加文件名输入框的手动编辑检测
    const handleFileNameInput = () => {
      // 当用户在文件名输入框中输入时，标记为手动修改
      userModifiedFileName.value = true
    }

    // 是否可以进入下一步
    const canProceedToNext = computed(() => {
      if (currentStep.value === 0) {
        return basicForm.vehicleType &&
               basicForm.configMethod &&
               basicForm.configFile &&
               basicForm.function &&
               basicForm.version &&
               basicForm.fileName
      } else if (currentStep.value === 1) {
        // 第二步需要至少选择一个Topic
        return topicConfig.value.selected > 0
      }
      return true
    })

    // 配置方式改变
    const handleConfigMethodChange = () => {
      basicForm.configFile = ''
      uploadedFile.value = null
      selectedFileName.value = ''
    }

    // 显示文件选择对话框
    const showFileSelectDialog = () => {
      fileSelectDialogVisible.value = true
    }

    // 获取默认属性
    const getDefaultProperties = () => {
      return {
        variant: basicForm.vehicleType,
        function: basicForm.function,
        version: basicForm.version,
      }
    }

    // 处理文件选择
    const handleFileSelected = (file) => {
      selectedFileName.value = file.name
      basicForm.configFile = file.path
     // ElMessage.success('文件选择成功')
    }

    // 处理Topic配置变化
    const handleTopicsChanged = (config) => {
      topicConfig.value = config
      console.log('Topic配置变化:', config)
    }

    // 转换Topic数据为接口格式
    const transformTopicData = (topics) => {
      return topics.map(topic => ({
        dataCheck: topic.dataQuality?.enabled ? {
          enabled: topic.dataQuality.enabled,
          errLevel: topic.dataQuality.errorLevel,
          maxFreq: topic.dataQuality.maxFrequency,
          minFreq: topic.dataQuality.minFrequency
        } : undefined,
        frequency: topic.frequency,
        instanceName: topic.instanceName,
        interfaceName: topic.interfaceName,
        latched: topic.latched,
        minibag: topic.minibag,
        selected: topic.selected,
        size: topic.size,
        hppSecondParam: topic.hppSecondParam,
        topic: topic.topic
      }))
    }

    // 搜索 JFrog 文件
    const searchJfrogFiles = async (query) => {
      if (!query || query.length < 2) return
      
      jfrogLoading.value = true
      try {
        const response = await searchZipPackagesByProperties({
          variant: basicForm.vehicleType,
          function: query,
          platform: 'linux'
        })
        
        if (response.code === 200 && response.data) {
          jfrogFileOptions.value = response.data.map(item => ({
            name: item.name || item.path.split('/').pop(),
            path: item.path,
            size: item.size
          }))
        }
      } catch (error) {
        console.error('搜索 JFrog 文件失败:', error)
        ElMessage.error('搜索文件失败')
      } finally {
        jfrogLoading.value = false
      }
    }

    // 配置文件选择改变
    const handleConfigFileChange = (value) => {
      console.log('选择的配置文件:', value)
    }

    // 文件上传
    const handleFileUpload = (file) => {
      if (!file.name.endsWith('.zip')) {
        ElMessage.error('只支持 .zip 格式文件')
        return false
      }
      
      uploadedFile.value = file
      basicForm.configFile = file.name
      return true
    }

    // 移除上传的文件
    const removeUploadedFile = () => {
      uploadedFile.value = null
      basicForm.configFile = ''
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
    }

    // 下一步
    const handleNext = async () => {
      if (currentStep.value === 0) {
        // 验证第一步表单
        if (!basicFormRef.value) return

        try {
          await basicFormRef.value.validate()
          currentStep.value = 1
        } catch (error) {
          console.log('表单验证失败:', error)
        }
      } else if (currentStep.value === 1) {
        // 第二步：保存白名单数据
        await handleSaveWhitelist()
      } else if (currentStep.value === 2) {
        // 完成逻辑
        handleComplete()
      }
    }

    // 保存白名单数据
    const handleSaveWhitelist = async () => {
      try {
        nextLoading.value = true

        // 检查是否有选中的Topic
        if (!topicConfig.value.topics || topicConfig.value.topics.length === 0) {
          ElMessage.error('请至少选择一个Topic')
          return
        }

        // 构造保存数据
        const saveData = {
          description: basicForm.description || '',
          fileName: basicForm.fileName,
          functionName: basicForm.function,
          regexServices: topicConfig.value.regexFilters || [],
          topicInfoList: transformTopicData(topicConfig.value.topics),
          variantCode: basicForm.vehicleType,
          version: basicForm.version
        }

        console.log('保存数据:', saveData)

        // 调用保存接口
        const response = await saveAndUploadWhitelist(saveData)
            // currentStep.value = 2
        if (response.data) {
          // 保存返回的结果数据
          saveResultData.value = response.data
          ElMessage.success('白名单保存成功')
          currentStep.value = 2
        } else {

          //ElMessage.error(response.message || '保存失败')
        }
      } catch (error) {
        console.error('保存白名单失败:', error)
       // ElMessage.error('保存失败，请重试')
      } finally {
        nextLoading.value = false
      }
    }

    // 上一步
    const handlePrevious = () => {
      if (currentStep.value > 0) {
        currentStep.value--
      }
    }

    // 完成
    const handleComplete = () => {
      ElMessage.success('白名单创建成功')
      emit('success', {
        ...basicForm,
        resultData: saveResultData.value
      })
      handleClose()
    }

    // 处理文件预览
    const handlePreviewFile = (fileInfo) => {
      console.log('预览文件:', fileInfo)
      // 这里可以实现文件预览逻辑，比如打开新窗口或下载文件
      if (fileInfo.fileAddress) {
        // 可以打开新窗口预览文件
        window.open(fileInfo.fileAddress, '_blank')
      } else {
        ElMessage.warning('文件地址不可用')
      }
    }

    // 关闭对话框
    const handleClose = () => {
      visible.value = false
      // 重置表单
      currentStep.value = 0
      userModifiedFileName.value = false
      Object.assign(basicForm, {
        vehicleType: props.vehicleType,
        configMethod: 'jfrog',
        configFile: '',
        function: '',
        version: '',
        fileName: '',
        description: ''
      })

      // 重置保存结果数据
      saveResultData.value = {
        dataSize: 0,
        fileAddress: '',
        fileId: '',
        fileName: '',
        remark: '',
        topicInfoList: [],
        topicSum: 0
      }
      uploadedFile.value = null
      jfrogFileOptions.value = []
      
      // 重置文件选择相关
      selectedFileName.value = ''
      fileSelectDialogVisible.value = false

      // 重置Topic配置
      topicConfig.value = {
        total: 0,
        selected: 0,
        topics: []
      }
      
      if (basicFormRef.value) {
        basicFormRef.value.clearValidate()
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadVehicleTypeOptions()
    })

    return {
      visible,
      currentStep,
      nextLoading,
      jfrogLoading,
      basicFormRef,
      uploadRef,
      basicForm,
      basicFormRules,
      vehicleTypeOptions,
      jfrogFileOptions,
      uploadedFile,
      canProceedToNext,
      // 文件选择相关
      fileSelectDialogVisible,
      selectedFileName,
      // Topic配置相关
      topicConfig,
      // 保存结果相关
      saveResultData,
      // 方法
      handleConfigMethodChange,
      searchJfrogFiles,
      handleConfigFileChange,
      handleFileUpload,
      removeUploadedFile,
      handleNext,
      handlePrevious,
      handleClose,
      handleFileNameInput,
      showFileSelectDialog,
      getDefaultProperties,
      handleFileSelected,
      handleTopicsChanged,
      transformTopicData,
      handleSaveWhitelist,
      handlePreviewFile
    }
  }
}
</script>

<style lang="scss" scoped>
.steps-container {
  margin-bottom: 12px;

  :deep(.el-steps) {
    .el-step__title {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #c0c4cc;
    color: white;
    font-size: 12px;
    font-weight: 500;
  }

  :deep(.el-step.is-process) {
    .step-number {
      background-color: #409eff;
    }
  }

  :deep(.el-step.is-finish) {
    .step-number {
      background-color: #67c23a;
    }
  }
}

.step-content {
  min-height: 400px;
  padding: 0 20px;
}

.basic-form {
  .readonly-input {
    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      color: #909399;
    }
  }

  .config-file-section {
    width: 100%;
    .jfrog-section {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
      .jfrog-file-input {
        width: 100%;
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;

        .file-display-input {
          width: 82%;

          :deep(.el-input__inner) {
            background-color: #f5f7fa;
            color: #303133;
          }
        }
      }
    }

    .upload-section {
      .upload-content {
        text-align: center;
        padding: 40px 20px;

        .upload-icon {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        .upload-text {
          p {
            margin: 0;
            color: #606266;

            &.upload-tip {
              font-size: 12px;
              color: #909399;
              margin-top: 4px;
            }
          }
        }
      }

      .uploaded-file {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 12px;
        padding: 8px 12px;
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;

        .el-icon {
          color: #409eff;
        }

        span {
          flex: 1;
          color: #303133;
          font-size: 14px;
        }
      }
    }
  }

  .filename-hint {
    margin-top: 8px;

    .hint-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .step-content {
    padding: 0 10px;
    min-height: 350px;
  }

  .basic-form {
    :deep(.el-form-item__label) {
      font-size: 13px;
    }

    .config-file-section {
      .upload-section {
        .upload-content {
          padding: 30px 15px;

          .upload-icon {
            font-size: 36px;
          }

          .upload-text p {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
