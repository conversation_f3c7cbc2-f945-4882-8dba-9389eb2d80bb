<template>
  <div>
    <el-drawer ref="DbcFileDrawerRef" v-model="visible" size="45%" direction="rtl" @close="handleClose">
      <template #header>
        <span style="font-size: 18px; color: black">{{ $t('选择dbc文件') }}</span>
      </template>
      <el-table
        :data="pageData.records"
        stripe
        border
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
        :default-expand-all="false"
        @expand-change="handleExpand"
        :expand-row-keys="expandedRowKeys"
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-table :data="props.row.subFiles || []" style="margin-left: 30px" border>
              <el-table-column prop="dbcFileType" label="文件类型" width="100" />
              <el-table-column prop="fileName" label="附件" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="mini" @click="choiceDbcFile(scope.row)"> 选择 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="variantName"
          width="180"
          :label="$t('车型')"
        ></el-table-column>
        <el-table-column :label="$t('版本')" prop="version"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="startTime"
          :label="$t('生效时间')"
          width="140"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="endTime"
          width="140"
          :label="$t('失效时间')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="enabled" label="是否启用">
          <template #default="scope">
            {{ scope.row.enable ? '启用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="description"
          :label="$t('描述')"
          show-tooltip-when-overflow
        >
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-drawer>
  </div>
</template>

<script>
import { pageDbc, getDbc } from '@/apis/fleet/ftm-dbc'
import { getFileList } from '@/apis/base/file'
import util from '@/plugins/util'
export default {
  name: 'DbcFileDrawer',
  components: {},
  emits: ['selected-dbc-file'],
  data() {
    return {
      visible: false,
      childNodeList: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      loadingSubTable: {},
      expandedRowKeys: []
    }
  },
  methods: {
    show(data) {
      this.query()
      this.visible = true
    },
    handleClose() {
      this.visible = false
      this.expandedRowKeys = []
    },
    choiceDbcFile(row) {
      this.$emit('selected-dbc-file', row)
      this.handleClose()
    },
    query() {
      pageDbc(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    async handleExpand(row) {
      this.expandedRowKeys = [row.id]
      if (row.subFiles) return
      const res = await getDbc(row.id)
      row.subFiles = res.data?.dbcFiles || []
      if (row.subFiles.length) {
        const promises = row.subFiles.map(async val => {
          const fileList = await this.downloadFile(val.id)
          val.fileName = fileList.map(file => file.name).join(', ')
        })
        await Promise.all(promises)
      }
    },
    downloadFile(fileId) {
      return new Promise(resolve => {
        if (fileId) {
          let postData = {
            sourceId: fileId,
            sourceType: 'dbc_file'
          }
          getFileList(postData).then(res => {
            res.data.forEach(val => {
              val.name = val.fileName + (val.fileType ? '.' + val.fileType : '')
              val.url = this.downloadUrl + val.id + '?token=' + util.getToken()
            })
            resolve(res.data)
          })
        } else {
          resolve([])
        }
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    }
  }
}
</script>

<style lang="scss" scoped>
.node-card {
  margin-bottom: 16px;
  width: 100%;
  font-size: 12px;
}
.card-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.label {
  display: inline-block;
  width: 100px;
  color: #606266;
  font-weight: 500;
}
:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
