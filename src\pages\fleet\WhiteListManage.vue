<template>
  <div class="whitelist-manage">
  <el-card>
    <!-- 统计信息和操作栏 -->
    <div class="stats-and-actions">
      <div class="actions-section">
        <ltw-input
          v-model="searchKeyword"
          placeholder="请输入关键字"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #append>
            <el-button class="search-btn" @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </ltw-input>

        <el-button type="primary" class="add-btn" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增白名单
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 车型卡片列表 -->
      <div class="vehicle-cards">
        <div
          v-for="(vehicle, index) in vehicleTypeList"
          :key="vehicle.vehicleType"
          class="vehicle-card"
        >
          <!-- 卡片头部 -->
          <div
            class="card-header"
            @click="toggleCard(index)"
          >
            <div class="expand-icon">
              <el-icon>
                <ArrowDown v-if="!expandedCards.includes(index)" />
                <ArrowUp v-else />
              </el-icon>
            </div>
            <div class="card-content">
              <div class="vehicle-info">
                <h3 class="vehicle-name">{{ vehicle.vehicleType }}</h3>
                <!-- <div class="vehicle-stats">
                  <div class="stat-group">
                    <el-icon class="stat-icon"><Document /></el-icon>
                    <span class="stat-label">文件数</span>
                    <span class="stat-value">{{ vehicle.fileCount }} / {{ totalFiles }}</span>
                  </div>
                  <div class="divider"></div>
                  <div class="stat-group">
                    <el-icon class="stat-icon"><Document /></el-icon>
                    <span class="stat-label">数据大小</span>
                    <span class="stat-value">{{ vehicle.dataSize }} / {{ vehicle.percentage }}%</span>
                  </div>
                </div> -->
              </div>
            </div>
          </div>

          <!-- 展开的子表格 -->
          <div
            v-if="expandedCards.includes(index)"
            class="card-body"
            v-loading="vehicle.loading"
          >
            <div class="sub-table-container">
              <el-table
                :data="vehicle.whitelistData"
                class="sub-table"
                stripe
              >
                <el-table-column prop="name" label="Name" min-width="300">
                  <template #default="{ row }">
                    <span class="file-name">{{ row.name }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="function" label="Function" width="150">
                  <template #default="{ row }">
                    <span class="function-text">{{ row.function }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="version" label="Version" width="120">
                  <template #default="{ row }">
                    <span class="version-text">{{ row.version }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="size" label="Size" width="100">
                  <template #default="{ row }">
                    <span class="size-text">{{ row.size }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="createUser" label="Create User" width="150">
                  <template #default="{ row }">
                    <span class="user-text">{{ row.createUser }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="createTime" label="Create Time" width="180">
                  <template #default="{ row }">
                    <span class="time-text">{{ row.createTime }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="description" label="Description" min-width="200">
                  <template #default="{ row }">
                    <span class="description-text">{{ row.description || '新增topic: xxxxxx' }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="120" align="center" fixed="right">
                  <template #default="{ row }">
                    <div class="action-buttons">
                      <el-button
                        type="primary"
                        size="small"
                        @click="handleCompare(row)"
                        class="compare-btn"
                      >
                        <el-icon><Sort /></el-icon>
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="handleDelete(row)"
                        class="delete-btn"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 子表格分页 -->
              <div class="sub-pagination">
                <el-pagination
                  background
                  :current-page="vehicle.pagination.current"
                  :page-size="vehicle.pagination.size"
                  :page-sizes="[5, 10, 20, 50]"
                  :total="vehicle.pagination.total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @current-change="handleSubCurrentChange(vehicle, $event)"
                  @size-change="handleSubSizeChange(vehicle, $event)"
                  class="pagination-controls"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!vehicleTypeList.length && !loading"
        description="暂无数据"
        class="empty-state"
      />

      <!-- 主分页 -->
      <div class="main-pagination" v-if="vehicleTypeList.length">
        <el-pagination
          background
          :current-page="pagination.current"
          :page-size="pagination.size"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 版本选择对话框 -->
    <el-dialog
      v-model="compareDialogVisible"
      title="选择对比版本"
      width="600px"
      @close="handleCompareDialogClose"
    >
      <div class="compare-selection">
        <div class="current-version">
          <h4>当前版本</h4>
          <div class="version-info">
            <p><strong>文件名：</strong>{{ currentCompareFile.name }}</p>
            <p><strong>版本：</strong>{{ currentCompareFile.version }}</p>
            <p><strong>功能：</strong>{{ currentCompareFile.function }}</p>
          </div>
        </div>

        <div class="select-version">
          <h4>选择对比版本</h4>
          <el-select
            v-model="selectedCompareFileId"
            placeholder="请选择要对比的版本"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in compareVersionOptions"
              :key="item.fileId"
              :label="`${item.name} (${item.version})`"
              :value="item.fileId"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ item.name }}</span>
                <span style="color: #8492a6; font-size: 12px;">{{ item.version }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>

      <template #footer>
        <el-button @click="compareDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="performComparison"
          :disabled="!selectedCompareFileId"
          :loading="compareLoading"
        >
          开始对比
        </el-button>
      </template>
    </el-dialog>

        <!-- 差异显示对话框 -->
    <el-dialog
      v-model="differenceDialogVisible"
      title="版本差异对比"
      width="1200px"
      @close="handleDifferenceDialogClose"
    >
      <div class="difference-content">
        <div class="difference-header">
          <div class="version-compare-info">
            <div class="version-item">
              <h4>当前版本</h4>
              <p>{{ currentCompareFile.name }} ({{ currentCompareFile.version }})</p>
            </div>
            <div class="vs-divider">VS</div>
            <div class="version-item">
              <h4>对比版本</h4>
              <p>{{ selectedCompareFileName }}</p>
            </div>
          </div>
        </div>

        <!-- 代码对比区域 -->
        <div class="code-diff-section">
          <h4 class="section-title">
            <el-icon><Document /></el-icon>
            JSON 文件对比
          </h4>
          
          <!-- 调试信息 -->
          <div class="debug-info" style="margin-bottom: 16px; padding: 12px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
            <p><strong>调试信息：</strong></p>
            <p>当前版本内容长度: {{ mockJsonData.current.content ? mockJsonData.current.content.length : 0 }}</p>
            <p>对比版本内容长度: {{ mockJsonData.compare.content ? mockJsonData.compare.content.length : 0 }}</p>
            <p>当前版本: {{ mockJsonData.current.name }}</p>
            <p>对比版本: {{ mockJsonData.compare.name }}</p>
          </div>
          
          <div class="code-diff-container">
            <!-- 自定义代码对比组件 -->
            <div class="custom-code-diff">
              <div class="diff-header">
                <div class="diff-toolbar">
                  <el-button size="small" @click="toggleSplitView">
                    <el-icon><View /></el-icon>
                    {{ isSplitView ? '单屏' : '分屏' }}
                  </el-button>
                  <el-button size="small" @click="toggleHighlight">
                    <el-icon><Document /></el-icon>
                    {{ isHighlight ? '关闭高亮' : '开启高亮' }}
                  </el-button>
                </div>
              </div>
              
              <div class="diff-content" :class="{ 'split-view': isSplitView }">
                <div class="diff-side old-side">
                  <div class="side-header">
                    <span class="side-title">旧版本 ({{ mockJsonData.compare.version }})</span>
                  </div>
                  <div class="code-content" :class="{ 'highlight': isHighlight }">
                    <pre><code>{{ mockJsonData.compare.content }}</code></pre>
                  </div>
                </div>
                
                <div class="diff-side new-side">
                  <div class="side-header">
                    <span class="side-title">新版本 ({{ mockJsonData.current.version }})</span>
                  </div>
                  <div class="code-content" :class="{ 'highlight': isHighlight }">
                    <pre><code>{{ mockJsonData.current.content }}</code></pre>
                  </div>
                </div>
              </div>
              
              <!-- 差异摘要 -->
              <div class="diff-summary">
                <div class="summary-item added">
                  <el-icon><Plus /></el-icon>
                  <span>新增: {{ getDiffSummary().added }} 行</span>
                </div>
                <div class="summary-item removed">
                  <el-icon><Minus /></el-icon>
                  <span>删除: {{ getDiffSummary().removed }} 行</span>
                </div>
                <div class="summary-item changed">
                  <el-icon><Edit /></el-icon>
                  <span>修改: {{ getDiffSummary().changed }} 行</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="difference-body">
          <div class="difference-section" v-if="differenceData.increase && differenceData.increase.length > 0">
            <h4 class="section-title increase">
              <el-icon><Plus /></el-icon>
              新增的 Topic ({{ differenceData.increase.length }})
            </h4>
            <div class="topic-list">
              <el-tag
                v-for="topic in differenceData.increase"
                :key="topic"
                type="success"
                class="topic-tag"
              >
                {{ topic }}
              </el-tag>
            </div>
          </div>

          <div class="difference-section" v-if="differenceData.reduced && differenceData.reduced.length > 0">
            <h4 class="section-title reduced">
              <el-icon><Minus /></el-icon>
              减少的 Topic ({{ differenceData.reduced.length }})
            </h4>
            <div class="topic-list">
              <el-tag
                v-for="topic in differenceData.reduced"
                :key="topic"
                type="danger"
                class="topic-tag"
              >
                {{ topic }}
              </el-tag>
            </div>
          </div>

          <div v-if="(!differenceData.increase || differenceData.increase.length === 0) &&
                       (!differenceData.reduced || differenceData.reduced.length === 0)"
               class="no-difference">
            <el-icon><Check /></el-icon>
            <p>两个版本没有差异</p>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="differenceDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportDifference">导出差异报告</el-button>
      </template>
    </el-dialog>

    <!-- 新增白名单对话框 -->
    <AddWhitelistDialog
      v-model="addDialogVisible"
      :vehicle-type="currentVehicleType"
      @success="handleAddSuccess"
    />
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Edit, Delete, ArrowDown, ArrowUp, Sort, Plus, Minus, Check,
  Document, Warning, Car, MoreFilled, Download, User, Clock, List, Refresh,
  ArrowRight, View
} from '@element-plus/icons-vue'
import {
  pageWhiteListVariant,
  pageWhiteList,
  createWhitelist,
  updateWhitelist,
  deleteWhitelist,
  compareWhitelistDifference
} from '@/apis/fleet/whitelist-management'
import AddWhitelistDialog from './components/AddWhitelistDialog.vue'
// import VueCodeDiff from 'vue-code-diff' // 注释掉，因为与 Vue 2 不兼容

export default {
  name: 'WhiteListManage',
  components: {
    Search,
    Edit,
    Delete,
    ArrowDown,
    ArrowUp,
    Sort,
    Plus,
    Minus,
    Check,
    Document,
    Warning,
    Car,
    MoreFilled,
    Download,
    User,
    Clock,
    List,
    Refresh,
    ArrowRight,
    View,
    AddWhitelistDialog,
    // VueCodeDiff
  },
  setup() {
    const loading = ref(false)
    const searchKeyword = ref('')
    const expandedCards = ref([])
    const dialogVisible = ref(false)
    const dialogTitle = ref('新增白名单')
    const formRef = ref(null)

    // 统计数据
    const totalFiles = ref(150)
    const totalSize = ref('150,230.8 GB')

    // 车型卡片列表
    const vehicleTypeList = ref([])

    // 白名单项目列表
    const whitelistItems = ref([])

    // 对比功能相关变量
    const compareDialogVisible = ref(false)
    const differenceDialogVisible = ref(false)
    const compareLoading = ref(false)
    const currentCompareFile = ref({})
    const selectedCompareFileId = ref('')
    const selectedCompareFileName = ref('')
    const compareVersionOptions = ref([])
    const differenceData = ref({ increase: [], reduced: [] })
    
    // 代码对比相关变量
    const isSplitView = ref(true)
    const isHighlight = ref(true)
    
    // Mock JSON 数据用于对比示例
    const mockJsonData = ref({
      current: {
        name: 'Parking_HPA_combined_whitelist_getk_Arch10.0_0728_V2.json',
        version: '10.0.0',
        function: 'mapless_L2',
        content: JSON.stringify({
          "version": "10.0.0",
          "function": "mapless_L2",
          "topics": [
            "/vehicle/status/speed",
            "/vehicle/status/position",
            "/vehicle/status/heading",
            "/vehicle/status/acceleration",
            "/vehicle/status/steering",
            "/vehicle/status/brake",
            "/vehicle/status/gear",
            "/vehicle/status/engine",
            "/vehicle/status/fuel",
            "/vehicle/status/temperature",
            "/vehicle/status/battery",
            "/vehicle/status/odometer",
            "/vehicle/status/maintenance",
            "/vehicle/status/error",
            "/vehicle/status/warning",
            "/vehicle/status/info",
            "/vehicle/status/debug",
            "/vehicle/status/trace",
            "/vehicle/status/performance",
            "/vehicle/status/security"
          ],
          "metadata": {
            "created": "2024-07-28T10:00:00Z",
            "author": "Li.Dongliang",
            "description": "新增topic: xxxxxx",
            "tags": ["parking", "HPA", "mapless", "L2"]
          }
        }, null, 2)
      },
      compare: {
        name: 'Parking_HPA_combined_whitelist_getk_Arch9.5_0620_V1.json',
        version: '9.5.0',
        function: 'mapless_L2',
        content: JSON.stringify({
          "version": "9.5.0",
          "function": "mapless_L2",
          "topics": [
            "/vehicle/status/speed",
            "/vehicle/status/position",
            "/vehicle/status/heading",
            "/vehicle/status/acceleration",
            "/vehicle/status/steering",
            "/vehicle/status/brake",
            "/vehicle/status/gear",
            "/vehicle/status/engine",
            "/vehicle/status/fuel",
            "/vehicle/status/temperature",
            "/vehicle/status/battery",
            "/vehicle/status/odometer",
            "/vehicle/status/maintenance",
            "/vehicle/status/error",
            "/vehicle/status/warning",
            "/vehicle/status/info",
            "/vehicle/status/debug",
            "/vehicle/status/trace",
            "/vehicle/status/performance"
          ],
          "metadata": {
            "created": "2024-06-20T10:00:00Z",
            "author": "Li.Dongliang",
            "description": "基础版本",
            "tags": ["parking", "HPA", "mapless", "L2"]
          }
        }, null, 2)
      }
    })

    // 新增白名单相关变量
    const addDialogVisible = ref(false)
    const currentVehicleType = ref('')

    const pagination = reactive({
      current: 1,
      size: 10,
      total: 0
    })

    const formData = reactive({
      id: null,
      vehicleType: '',
      name: '',
      function: '',
      version: '',
      size: '',
      description: ''
    })

    const formRules = {
      vehicleType: [{ required: true, message: '请选择车型', trigger: 'change' }],
      name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      function: [{ required: true, message: '请输入功能', trigger: 'blur' }],
      version: [{ required: true, message: '请输入版本', trigger: 'blur' }]
    }

    // 车型选项将从API动态获取
    const vehicleTypeOptions = ref([])

    // 加载车型选项（用于表单下拉框）
    const loadVehicleTypeOptions = async () => {
      try {
        const response = await pageWhiteListVariant({
          pageNum: 1,
          pageSize: 10, // 获取更多选项
        })

        if (response.data) {
          vehicleTypeOptions.value = response.data.records.map(item => ({
            label: item.variantCode,
            value: item.variantCode
          }))
        }
      } catch (error) {
        console.error('加载车型选项失败:', error)
      }
    }

    // 加载车型数据
    const loadVehicleTypes = async () => {
      loading.value = true
      try {
        const params = {
          pageNum: pagination.current,
          pageSize: pagination.size,
        }

        // 搜索条件
        if (searchKeyword.value) {
          params.variantCode = searchKeyword.value
        }

        const response = await pageWhiteListVariant(params)

        if (response.data) {
          const { records, total } = response.data

          // 转换为车型卡片数据格式
          vehicleTypeList.value = records.map(item => ({
            vehicleType: item.variantCode || 'Unknown',
            fileCount: Math.floor(Math.random() * 10) + 1, // 模拟数据
            dataSize: `${(Math.random() * 2000 + 100).toFixed(1)} GB`,
            percentage: Math.floor(Math.random() * 15) + 1,
            loading: false,
            whitelistData: [],
            pagination: {
              current: 1,
              size: 10,
              total: 0
            }
          }))

          pagination.total = total
        } else {
          ElMessage.error(response.message || '加载车型数据失败')
        }
      } catch (error) {
        console.error('加载车型数据失败:', error)
        ElMessage.error('加载车型数据失败')
      } finally {
        loading.value = false
      }
    }

    // 切换卡片展开/收起
    const toggleCard = (index) => {
      const cardIndex = expandedCards.value.indexOf(index)
      if (cardIndex > -1) {
        expandedCards.value.splice(cardIndex, 1)
      } else {
        expandedCards.value.push(index)
        // 加载该车型的白名单数据
        loadVehicleWhitelists(vehicleTypeList.value[index])
      }
    }

    // 加载指定车型的白名单数据
    const loadVehicleWhitelists = async (vehicle) => {
      vehicle.loading = true
      try {
        const params = {
          pageNum: vehicle.pagination.current,
          pageSize: vehicle.pagination.size,
          variantCode: vehicle.vehicleType
        }

        const response = await pageWhiteList(params)

        if (response.data) {
          const { records, total } = response.data

          vehicle.whitelistData = records.map(item => ({
            id: item.id,
            name: item.fileName || 'Parking_HPA_combined_whitelist_getk_Arch10.0_0728_V2.json',
            function: item.functionName || 'mapless_L2',
            version: item.version || '10.0.0',
            size: item.dataSize ? formatFileSize(item.dataSize) : '5kb',
            createUser: item.createUser || 'Li.Dongliang',
            createTime: formatDateTime(item.createTime || new Date()),
            description: item.description || '新增topic: xxxxxx',
            fileId: item.fileId,
            fileAddress: item.fileAddress
          }))

          vehicle.pagination.total = total
        } else {
          ElMessage.error(response.message || '加载白名单数据失败')
        }
      } catch (error) {
        console.error('加载白名单数据失败:', error)
        ElMessage.error('加载白名单数据失败')
      } finally {
        vehicle.loading = false
      }
    }

    const loadWhitelistData = async (row) => {
      row.loading = true
      try {
        // 调用子表接口：分页查询车型功能白名单表视图
        const params = {
          variantCode: row.vehicleType, // 根据车型编码查询
          pageNum: row.pagination.current,
          pageSize: row.pagination.size,
        }

        const response = await pageWhiteList(params)

        if ( response.data) {
          const { records, total } = response.data
          // 转换数据格式，适配表格显示
          row.whitelistData = records.map(item => ({
            id: item.id,
            name: item.fileName || '-',
            function: item.functionName || '-',
            version: item.version || '-',
            size: item.dataSize ? formatFileSize(item.dataSize) : '-',
            createUser: 'System', // 接口暂无此字段，使用默认值
            createTime: formatDateTime(new Date()), // 接口暂无此字段，使用当前时间
            description: item.description || '-',
            fileId: item.fileId,
            fileAddress: item.fileAddress
          }))

          row.pagination.total = total

          // 更新车型的白名单数量
          row.count = total
        } else {
          ElMessage.error(response.message || '加载白名单数据失败')
        }
      } catch (error) {
        console.error('加载白名单数据失败:', error)
        ElMessage.error('加载白名单数据失败')
      } finally {
        row.loading = false
      }
    }

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    }

    // 格式化日期时间
    const formatDateTime = (date) => {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    // 对比功能相关方法
    const handleCompare = async (row) => {
      try {
        // 设置当前对比文件信息
        currentCompareFile.value = {
          fileId: row.fileId,
          name: row.name,
          version: row.version,
          function: row.function
        }

        // 获取同一车型下的其他版本文件
        await loadCompareVersionOptions(row)

        // 显示版本选择对话框
        compareDialogVisible.value = true
      } catch (error) {
        console.error('准备对比失败:', error)
        ElMessage.error('准备对比失败')
      }
    }

    // 加载可对比的版本选项
    const loadCompareVersionOptions = async (currentRow) => {
      try {
        // 使用 mock 数据替代真实 API 调用
        // 在实际项目中，这里应该调用真实的 API
        // const params = {
        //   variantCode: vehicleItem.vehicleType,
        //   pageNum: 1,
        //   pageSize: 100,
        //   orderBy: 'createTime',
        //   orderType: 'DESC'
        // }
        // const response = await pageWhiteList(params)

        // Mock 数据：同一车型下的其他版本文件
        compareVersionOptions.value = [
          {
            fileId: 'mock_file_001',
            name: 'Parking_HPA_combined_whitelist_getk_Arch9.5_0620_V1.json',
            version: '9.5.0',
            function: 'mapless_L2'
          },
          {
            fileId: 'mock_file_002',
            name: 'Parking_HPA_combined_whitelist_getk_Arch9.0_0515_V3.json',
            version: '9.0.0',
            function: 'mapless_L2'
          },
          {
            fileId: 'mock_file_003',
            name: 'Parking_HPA_combined_whitelist_getk_Arch8.5_0410_V2.json',
            version: '8.5.0',
            function: 'mapless_L2'
          },
          {
            fileId: 'mock_file_004',
            name: 'Parking_HPA_combined_whitelist_getk_Arch8.0_0320_V1.json',
            version: '8.0.0',
            function: 'mapless_L2'
          },
          {
            fileId: 'mock_file_005',
            name: 'Parking_HPA_combined_whitelist_getk_Arch7.5_0215_V4.json',
            version: '7.5.0',
            function: 'mapless_L2'
          }
        ]

        // 过滤掉当前文件（模拟真实场景）
        compareVersionOptions.value = compareVersionOptions.value.filter(
          item => item.fileId !== currentRow.fileId
        )
      } catch (error) {
        console.error('加载对比版本失败:', error)
        ElMessage.error('加载对比版本失败')
      }
    }

    // 执行对比
    const performComparison = async () => {
      if (!selectedCompareFileId.value) {
        ElMessage.warning('请选择要对比的版本')
        return
      }

      compareLoading.value = true
      try {
        // 使用 mock 数据进行对比示例
        // 在实际项目中，这里应该调用真实的对比接口
        // const response = await compareWhitelistDifference(
        //   currentCompareFile.value.fileId,
        //   selectedCompareFileId.value
        // )

        // 模拟 API 调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 使用 mock 数据
        differenceData.value = {
          increase: ["/vehicle/status/security"],
          reduced: []
        }

        // 获取选中的对比文件名称
        const selectedFile = compareVersionOptions.value.find(
          item => item.fileId === selectedCompareFileId.value
        )
        selectedCompareFileName.value = selectedFile ?
          `${selectedFile.name} (${selectedFile.version})` : '未知版本'

        // 关闭选择对话框，显示差异对话框
        compareDialogVisible.value = false
        differenceDialogVisible.value = true
      } catch (error) {
        console.error('对比失败:', error)
        ElMessage.error('对比失败')
      } finally {
        compareLoading.value = false
      }
    }

    // 关闭对比选择对话框
    const handleCompareDialogClose = () => {
      selectedCompareFileId.value = ''
      compareVersionOptions.value = []
      currentCompareFile.value = {}
    }

    // 关闭差异显示对话框
    const handleDifferenceDialogClose = () => {
      differenceData.value = { increase: [], reduced: [] }
      selectedCompareFileName.value = ''
    }

    // 代码对比相关方法
    const toggleSplitView = () => {
      isSplitView.value = !isSplitView.value
    }
    
    const toggleHighlight = () => {
      isHighlight.value = !isHighlight.value
    }
    
    const getDiffSummary = () => {
      // 简单的差异统计
      const oldLines = mockJsonData.value.compare.content.split('\n').length
      const newLines = mockJsonData.value.current.content.split('\n').length
      
      if (newLines > oldLines) {
        return {
          added: newLines - oldLines,
          removed: 0,
          changed: 0
        }
      } else if (oldLines > newLines) {
        return {
          added: 0,
          removed: oldLines - newLines,
          changed: 0
        }
      } else {
        return {
          added: 0,
          removed: 0,
          changed: 1 // 假设有内容变化
        }
      }
    }
    
    // 导出差异报告
    const exportDifference = () => {
      try {
        const reportContent = {
          currentVersion: currentCompareFile.value,
          compareVersion: selectedCompareFileName.value,
          differences: differenceData.value,
          exportTime: new Date().toISOString()
        }

        const blob = new Blob([JSON.stringify(reportContent, null, 2)], {
          type: 'application/json'
        })

        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `whitelist-difference-${Date.now()}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        ElMessage.success('差异报告导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
      }
    }



    const handleSearch = () => {
      pagination.current = 1
      loadVehicleTypes()
    }

    // 刷新数据
    const handleRefresh = () => {
      pagination.current = 1
      expandedCards.value = []
      loadVehicleTypes()
    }







    // 显示新增对话框
    const showAddDialog = () => {
      // 重置车型选择，让用户手动选择
      currentVehicleType.value = ''

      addDialogVisible.value = true
    }

    // 新增成功回调
    const handleAddSuccess = async (data) => {
      console.log('新增白名单成功:', data)
      ElMessage.success('白名单创建成功')

      // 重新加载数据
      await loadVehicleTypes()

      // 如果有指定车型，自动展开该车型
      if (data.vehicleType) {
        const vehicleIndex = vehicleTypeList.value.findIndex(
          item => item.vehicleType === data.vehicleType
        )
        if (vehicleIndex !== -1) {
          expandedCards.value = [vehicleIndex]
          await loadWhitelistData(vehicleTypeList.value[vehicleIndex])
        }
      }
    }

    const handleAdd = () => {
      dialogTitle.value = '新增白名单'
      resetFormData()
      dialogVisible.value = true
    }

    const handleEdit = (row) => {
      dialogTitle.value = '编辑白名单'
      Object.assign(formData, row)
      dialogVisible.value = true
    }

    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这条白名单记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用删除API
        const response = await deleteWhitelist(row.id)

        if (response.code === 200) {
          ElMessage.success('删除成功')
          // 重新加载当前展开的车型数据
          const expandedIndex = expandedCards.value[0]
          if (expandedIndex !== undefined) {
            const vehicleItem = vehicleTypeList.value[expandedIndex]
            if (vehicleItem) {
              vehicleItem.whitelistData = []
              vehicleItem.pagination.current = 1
              await loadWhitelistData(vehicleItem)
            }
          }
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    const resetFormData = () => {
      Object.assign(formData, {
        id: null,
        vehicleType: '',
        name: '',
        function: '',
        version: '',
        size: '',
        description: ''
      })
    }

    const handleDialogClose = () => {
      resetFormData()
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    const handleSubmit = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()

        // 准备提交数据
        const submitData = {
          variantCode: formData.vehicleType,
          functionName: formData.function,
          version: formData.version,
          description: formData.description,
          fileName: formData.name,
          dataSize: parseInt(formData.size) || 0
        }

        let response
        if (formData.id) {
          // 更新操作
          response = await updateWhitelist(formData.id, submitData)
        } else {
          // 新增操作
          response = await createWhitelist(submitData)
        }

        if (response.code === 200) {
          ElMessage.success(formData.id ? '更新成功' : '新增成功')
          dialogVisible.value = false

          // 重新加载数据
          if (formData.id) {
            // 如果是更新，重新加载当前展开的车型数据
            const expandedIndex = expandedCards.value[0]
            if (expandedIndex !== undefined) {
              const vehicleItem = vehicleTypeList.value[expandedIndex]
              if (vehicleItem) {
                vehicleItem.whitelistData = []
                await loadWhitelistData(vehicleItem)
              }
            }
          } else {
            // 如果是新增，重新加载车型列表
            await loadVehicleTypes()
          }
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        if (error !== false) {
          console.error('操作失败:', error)
          ElMessage.error('操作失败')
        }
      }
    }

    const handleSizeChange = (size) => {
      pagination.size = size
      pagination.current = 1
      loadVehicleTypes()
    }

    const handleCurrentChange = (current) => {
      pagination.current = current
      loadVehicleTypes()
    }

    const handleSubSizeChange = (row, size) => {
      row.pagination.size = size
      row.pagination.current = 1
      loadWhitelistData(row)
    }

    const handleSubCurrentChange = (row, current) => {
      row.pagination.current = current
      loadWhitelistData(row)
    }

    onMounted(() => {
      loadVehicleTypes()
      loadVehicleTypeOptions()
    })

    return {
      loading,
      searchKeyword,
      expandedCards,
      dialogVisible,
      dialogTitle,
      formRef,
      pagination,
      vehicleTypeList,
      formData,
      formRules,
      vehicleTypeOptions,
      // 统计数据
      totalFiles,
      totalSize,
      // 白名单项目
      whitelistItems,
      // 对比功能相关
      compareDialogVisible,
      differenceDialogVisible,
      compareLoading,
      currentCompareFile,
      selectedCompareFileId,
      selectedCompareFileName,
      compareVersionOptions,
      differenceData,
      mockJsonData,
      // 代码对比相关
      isSplitView,
      isHighlight,
      // 新增白名单相关
      addDialogVisible,
      currentVehicleType,
      // 方法
      toggleCard,
      handleSearch,
      handleRefresh,
      handleAdd,
      handleEdit,
      handleDelete,
      handleDialogClose,
      handleSubmit,
      handleSizeChange,
      handleCurrentChange,
      handleSubSizeChange,
      handleSubCurrentChange,
      // 对比功能方法
      handleCompare,
      performComparison,
      handleCompareDialogClose,
      handleDifferenceDialogClose,
      exportDifference,
      // 代码对比方法
      toggleSplitView,
      toggleHighlight,
      getDiffSummary,
      // 新增白名单方法
      showAddDialog,
      handleAddSuccess
    }
  }
}
</script>

<style lang="scss" scoped>
.whitelist-manage {
  padding: 16px;
  background: white;
  min-height: 100vh;

  .page-header {
    padding: 16px 0;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .menu-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon-dots {
          width: 4px;
          height: 4px;
          background: #222222;
          border-radius: 1px;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: #222222;
            border-radius: 1px;
          }

          &::before {
            top: -6px;
          }

          &::after {
            top: 6px;
          }
        }
      }

      .page-title {
        font-size: 16px;
        font-weight: 700;
        color: #232628;
        margin: 0;
        line-height: 16px;
      }
    }
  }

  .stats-and-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 24px;
    margin-bottom: 16px;

    .stats-section {
      display: flex;
      align-items: center;
      gap: 10px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-icon {
          width: 16px;
          height: 16px;
          color: #232628;
        }

        .stat-label {
          font-size: 12px;
          color: #4E5256;
          font-weight: 400;
          line-height: 24px;
        }

        .stat-value {
          font-size: 12px;
          color: #5755FF;
          font-weight: 700;
          line-height: 24px;
        }
      }

      .divider {
        width: 1px;
        height: 13px;
        background: #D0D4D8;
        border-radius: 1px;
      }
    }

    .actions-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .search-input {
        width: 390px;

      }

      .add-btn {
        background: #F5F5FF;
        border: 1px solid #DDDDFF;
        border-radius: 2px;
        padding: 6px 11px;
        font-size: 12px;
        color: #5755FF;
        font-weight: 400;
        line-height: 12px;
        height: 32px;

        &:hover {
          background: #EDEDFF;
          border-color: #CCCCFF;
        }

        .el-icon {
          margin-right: 8px;
          font-size: 12px;
        }
      }
    }
  }

  .main-content {
    .vehicle-cards {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .vehicle-card {
        background: white;
        border: 1px solid #D0D4D8;
        border-radius: 2px;
        overflow: hidden;

        .card-header {
          display: flex;
          align-items: center;
          background: #FAFAFC;
         // border-bottom: 1px solid #D0D4D8;
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background: #F0F2F5;
          }

          .expand-icon {
            width: 55px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 13px 12px;
            //border-right: 1px solid #D0D4D8;

            .el-icon {
              width: 24px;
              height: 24px;
              color: #5755FF;
              font-size: 16px;
            }
          }

          .card-content {
            flex: 1;
            padding: 8px 12px 9px;

            .vehicle-info {
              display: flex;
              align-items: center;
              gap: 20px;

              .vehicle-name {
                font-size: 16px;
                font-weight: 700;
                color: #5755FF;
                margin: 0;
                line-height: 23px;
              }

              .vehicle-stats {
                display: flex;
                align-items: center;
                gap: 10px;

                .stat-group {
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .stat-icon {
                    width: 16px;
                    height: 16px;
                    color: #232628;
                  }

                  .stat-label {
                    font-size: 12px;
                    color: #4E5256;
                    font-weight: 400;
                    line-height: 24px;
                  }

                  .stat-value {
                    font-size: 12px;
                    color: #4E5256;
                    font-weight: 700;
                    line-height: 24px;
                  }
                }

                .divider {
                  width: 1px;
                  height: 13px;
                  background: #D0D4D8;
                  border-radius: 1px;
                }
              }
            }
          }
        }

        .card-body {
          background: white;
          border-bottom-left-radius: 2px;
          border-bottom-right-radius: 2px;

          .sub-table-container {
            padding: 16px;

            .sub-table {
              :deep(.el-table) {
                .el-table__header {
                  th {
                    background: #FAFAFC;
                    color: #909399;
                    font-weight: 400;
                    font-size: 12px;
                    border-bottom: 1px solid #EBEEF5;
                    padding: 8px 12px;
                    line-height: 23px;
                  }
                }

                .el-table__body {
                  tr {
                    &:hover {
                      background: #f8f9fa;
                    }

                    td {
                      padding: 8px 12px;
                      border-bottom: 1px solid #EBEEF5;
                      font-size: 12px;
                      color: #303133;
                      line-height: 23px;
                    }
                  }
                }
              }

              .file-name {
                color: #303133;
                font-size: 12px;
                line-height: 23px;
              }

              .function-text,
              .version-text,
              .size-text,
              .user-text,
              .time-text,
              .description-text {
                color: #303133;
                font-size: 12px;
                line-height: 23px;
              }

              .action-buttons {
                display: flex;
                gap: 0;
                justify-content: flex-start;

                .compare-btn {
                  background: #5755FF;
                  border-color: #5755FF;
                  border-right: 1px solid rgba(255, 255, 255, 0.5);
                  padding: 6px 11px;

                  .el-icon {
                    color: white;
                    font-size: 12px;
                  }
                }

                .delete-btn {
                  background: #FF6E6F;
                  border-color: #FF6E6F;
                  padding: 6px 11px;

                  .el-icon {
                    color: white;
                    font-size: 12px;
                  }
                }
              }
            }

            .sub-pagination {
              display: flex;
              align-items: center;
              gap: 16px;
              margin-top: 12px;

              .pagination-info {
                font-size: 12px;
                color: #606266;
                line-height: 20px;
              }

              .page-size-select {
                width: 100px;

                :deep(.el-select__wrapper) {
                  height: 24px;
                  border-radius: 2px;
                  border: 1px solid #DCDFE6;

                  .el-select__selected-item {
                    font-size: 12px;
                    color: #606266;
                    line-height: 20px;
                  }
                }
              }

              .pagination-controls {
                :deep(.el-pagination) {
                  .el-pager li {
                    min-width: 24px;
                    height: 24px;
                    line-height: 20px;
                    border-radius: 2px;
                    font-size: 12px;
                    margin: 0 4px;

                    &.is-active {
                      background: #5755FF;
                      color: white;
                    }

                    &:not(.is-active) {
                      background: #F0F2F5;
                      color: #606266;
                    }
                  }

                  .btn-prev,
                  .btn-next {
                    width: 24px;
                    height: 24px;
                    border-radius: 2px;
                    margin: 0 4px;

                    &:disabled {
                      background: #F5F7FA;
                    }

                    &:not(:disabled) {
                      background: #F0F2F5;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .empty-state {
        padding: 60px 20px;
        text-align: center;

        :deep(.el-empty__description) {
          margin-bottom: 20px;
        }
      }
    }

    .main-pagination {
      display: flex;
      padding: 32px 0;

      :deep(.el-pagination) {
        .el-pager li {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          border-radius: 6px;
          margin: 0 2px;
          font-weight: 500;
        }

        .btn-prev,
        .btn-next {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          margin: 0 2px;
        }
      }
    }

  }

  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #e9ecef;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #e9ecef;
    }
  }

  :deep(.el-form) {
    .el-form-item__label {
      color: #333;
      font-weight: 500;
    }

    .el-input__wrapper,
    .el-textarea__inner,
    .el-select .el-input__wrapper {
      border-radius: 6px;
    }
  }

  :deep(.el-button) {

    &.is-circle {
      width: 28px;
      height: 28px;
      padding: 0;
      margin: 0 2px;
    }
  }

  :deep(.el-pagination) {
    .el-pagination__sizes .el-select .el-input {
      width: 100px;
    }

    .el-pagination__jump {
      margin-left: 16px;
    }
  }

  // 对比功能样式
  .compare-selection {
    .current-version,
    .select-version {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      .version-info {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        p {
          margin: 0 0 8px 0;
          color: #606266;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            color: #303133;
            margin-right: 8px;
          }
        }
      }
    }
  }

  // 代码对比样式
  .code-diff-section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #5755FF;
      }
    }

    .code-diff-container {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;
      min-height: 400px;

      .custom-code-diff {
        .diff-header {
          background: #f8f9fa;
          border-bottom: 1px solid #e4e7ed;
          padding: 12px 16px;

          .diff-toolbar {
            display: flex;
            gap: 8px;
            align-items: center;

            .el-button {
              font-size: 12px;
              padding: 4px 8px;
              height: 28px;
            }
          }
        }

        .diff-content {
          display: flex;
          height: 300px;

          &.split-view {
            .diff-side {
              flex: 1;
              border-right: 1px solid #e4e7ed;

              &:last-child {
                border-right: none;
              }
            }
          }

          .diff-side {
            .side-header {
              background: #f5f7fa;
              padding: 8px 12px;
              border-bottom: 1px solid #e4e7ed;

              .side-title {
                font-size: 13px;
                font-weight: 600;
                color: #303133;
              }
            }

            .code-content {
              height: calc(100% - 40px);
              overflow: auto;
              padding: 12px;

              &.highlight {
                pre {
                  margin: 0;
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  font-size: 12px;
                  line-height: 1.5;
                  color: #333;

                  code {
                    display: block;
                    white-space: pre-wrap;
                    word-break: break-all;
                  }
                }
              }

              &:not(.highlight) {
                pre {
                  margin: 0;
                  font-family: 'Courier New', monospace;
                  font-size: 12px;
                  line-height: 1.5;
                  color: #666;

                  code {
                    display: block;
                    white-space: pre-wrap;
                    word-break: break-all;
                  }
                }
              }
            }
          }
        }

        .diff-summary {
          background: #f8f9fa;
          border-top: 1px solid #e4e7ed;
          padding: 12px 16px;
          display: flex;
          gap: 24px;
          align-items: center;

          .summary-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #606266;

            .el-icon {
              font-size: 14px;
            }

            &.added {
              color: #67c23a;
              .el-icon { color: #67c23a; }
            }

            &.removed {
              color: #f56c6c;
              .el-icon { color: #f56c6c; }
            }

            &.changed {
              color: #e6a23c;
              .el-icon { color: #e6a23c; }
            }
          }
        }
      }
    }
  }

  .difference-content {
    .difference-header {
      margin-bottom: 24px;

      .version-compare-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        .version-item {
          flex: 1;
          text-align: center;

          h4 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
          }

          p {
            margin: 0;
            color: #606266;
            font-size: 13px;
          }
        }

        .vs-divider {
          margin: 0 20px;
          color: #909399;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    .difference-body {
      .difference-section {
        margin-bottom: 24px;

        .section-title {
          display: flex;
          align-items: center;
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;

          .el-icon {
            margin-right: 8px;
          }

          &.increase {
            color: #67c23a;
          }

          &.reduced {
            color: #f56c6c;
          }
        }

        .topic-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .topic-tag {
            margin: 0;
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            word-break: break-all;
          }
        }
      }

      .no-difference {
        text-align: center;
        padding: 40px 20px;
        color: #909399;

        .el-icon {
          font-size: 48px;
          color: #67c23a;
          margin-bottom: 16px;
        }

        p {
          margin: 0;
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .whitelist-manage {
    padding: 16px;

    .top-bar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
      padding: 16px;

      .search-container .search-input {
        width: 100%;
      }

      .add-btn {
        width: 100%;
        justify-content: center;
      }
    }

    .main-content {
      .vehicle-groups {
        .vehicle-group-card {
          margin-bottom: 16px;

          .vehicle-header {
            padding: 16px;

            .vehicle-info .vehicle-name {
              font-size: 16px;
            }
          }

          .vehicle-content {
            .whitelist-table {
              overflow-x: auto;

              .data-table {
                min-width: 800px;

                :deep(.el-table__body) {
                  td {
                    padding: 12px 8px;

                    .file-name,
                    .description-text {
                      font-size: 12px;
                    }

                    .action-buttons {
                      .compare-btn,
                      .delete-btn {
                        width: 24px;
                        height: 24px;
                      }
                    }
                  }
                }
              }
            }

            .table-pagination {
              flex-direction: column;
              gap: 12px;
              padding: 12px 16px;

              .pagination-controls {
                :deep(.el-pagination) {
                  justify-content: center;
                  flex-wrap: wrap;
                }
              }
            }
          }
        }
      }

      .main-pagination {
        padding: 16px 0;

        :deep(.el-pagination) {
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }

    // 对比功能移动端适配
    :deep(.el-dialog) {
      &.compare-dialog,
      &.difference-dialog {
        width: 95% !important;
        margin: 0 auto;
      }
    }

    .compare-selection {
      .current-version,
      .select-version {
        h4 {
          font-size: 14px;
        }

        .version-info {
          padding: 12px;

          p {
            font-size: 12px;
          }
        }
      }
    }

    .difference-content {
      .difference-header {
        .version-compare-info {
          flex-direction: column;
          gap: 16px;
          padding: 16px;

          .version-item {
            h4 {
              font-size: 12px;
            }

            p {
              font-size: 11px;
            }
          }

          .vs-divider {
            margin: 0;
            font-size: 14px;
          }
        }
      }

      .difference-body {
        .difference-section {
          .section-title {
            font-size: 14px;
          }

          .topic-list {
            .topic-tag {
              font-size: 10px;
              padding: 4px 8px;
            }
          }
        }

        .no-difference {
          padding: 20px 10px;

          .el-icon {
            font-size: 32px;
            margin-bottom: 12px;
          }

          p {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>