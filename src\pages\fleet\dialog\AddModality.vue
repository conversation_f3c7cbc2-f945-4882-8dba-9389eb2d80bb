<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="60%"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      :draggable="true"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="130px" :hide-required-asterisk="formReadonly">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('传感器类型')" prop="sensorType">
            <dictionary-type-selection
                v-model="form.sensorType"
                clearable
                dictionaryType="sensor_type"
                :placeholder="$t('请选择')"
                select-disabled="Vehicle"
                :disabled="formReadonly"
                @change="changeSensorType"
                filterable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('传感器')" prop="modality">
            <common-selection
                v-model="form.modality"
                :model-code="'code'"
                :model-name="'name,code'"
                :model-options="modalityData"
                :disabled="formReadonly"
                @change="changeModality"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('传感器实体')" prop="sensorId">
            <ltw-icon icon-code="el-icon-caret-right"></ltw-icon>
            <el-link :disabled="formReadonly" @click="getSensorList" type="primary"
            >{{ form.sensorModelSpecification ? form.sensorModelSpecification : $t('请选择') }}
            </el-link>
          </el-form-item>
        </el-col>
      </el-row>
      <el-card class="sensor-model-form" v-if="form?.cameraVO?.id">
        <el-form ref="sensorModelFormRef" label-width="130px">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item :label="$t('供应商')">
                <el-tag>{{ form?.cameraVO?.supplierName }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('型号')">
                <el-tag>{{ form?.cameraVO?.model }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('规格')">
                <el-tag>{{ form?.cameraVO?.specification }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="form.sensorType === 'camera'">
              <el-form-item :label="$t('分辨率')">
                <el-tag>{{ form?.cameraVO?.resolution }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="form.sensorType === 'camera'">
              <el-form-item :label="$t('水平视角')">
                <el-tag>{{ form?.cameraVO?.hfov }}°</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="form.sensorType === 'camera'">
              <el-form-item :label="$t('垂直视角')">
                <el-tag>{{ form?.cameraVO?.vfov }}°</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="form.sensorType === 'camera'">
              <el-form-item :label="$t('相邻两行曝光间隔')">
                <el-tag>{{ form?.cameraVO?.exposureInterval }}us</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="form.sensorType === 'camera'">
              <el-form-item :label="$t('畸变模型')">
                <el-tag>{{ form?.cameraVO?.distortionModel }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'x'" prop="positionX">
            <ltw-input v-model="form.positionX" :disabled="formReadonly" id="positionX"
                         @change="clearPositionX">
              <template #append>
                <span>mm</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'y'" prop="positionY">
            <ltw-input v-model="form.positionY" :disabled="formReadonly" id="positionY"
                         @change="clearPositionY">
              <template #append>
                <span>mm</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'z'" prop="positionZ">
            <ltw-input v-model="form.positionZ" :disabled="formReadonly" id="positionZ"
                         @change="clearPositionZ">
              <template #append>
                <span>mm</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('距地') + $t('高度')" prop="height">
            <ltw-input v-model="form.height" :disabled="formReadonly" id="height"
                         @change="clearHeight">
              <template #append>
                <span>mm</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="msop" prop="msop" v-if="form.sensorType === 'lidar'">
            <ltw-input v-model="form.msop" :disabled="formReadonly" id="msop"
                         @change="clearMsop"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="difop" prop="difop" v-if="form.sensorType === 'lidar'">
            <ltw-input v-model="form.difop" :disabled="formReadonly" id="difop"
                         @change="clearDifop"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('帧同步偏移值')" prop="pcdJpgOffset" >
            <ltw-input v-model="form.pcdJpgOffset" :disabled="formReadonly" id="pcdJpgOffset">
              <template #append>
                <span>ms</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('间隔差的基准值')" prop="intervalDif">
            <ltw-input v-model="form.intervalDif" :disabled="formReadonly" id="intervalDif">
              <template #append>
                <span>ms</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
              :label="$t('帧同步差前偏移值')"
              prop="syncFrameOffsetForward"
              :rules="[
              {
                required: useType === 'daq',
                message: $t('请输入'),
                trigger: 'change'
              }
            ]"
          >
            <ltw-input v-model="form.syncFrameOffsetForward" id="syncFrameOffsetForward">
              <template #append>
                <span>ms</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
              :label="$t('帧同步差后偏移值')"
              prop="syncFrameOffsetBack"
              :rules="[
              {
                required: useType === 'daq',
                message: $t('请输入'),
                trigger: 'change'
              }
            ]"
          >
            <ltw-input v-model="form.syncFrameOffsetBack" id="syncFrameOffsetBack">
              <template #append>
                <span>ms</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.sensorType === 'lidar'">
          <el-form-item :label="$t('雷达时间戳定义角度')" prop="lidarTimestampAngle">
            <ltw-input v-model="form.lidarTimestampAngle" :disabled="formReadonly" id="lidarTimestampAngle">
              <template #append>
                <span>°</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('图片中坐标')" prop="imageCoordinate">
            <ltw-input v-model="form.imageCoordinate" :disabled="formReadonly" id="imageCoordinate"/>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('备注')" prop="remark">
            <ltw-input
                v-model="form.remark"
                :disabled="formReadonly"
                textType="remark"
                type="textarea"
                id="remark"
            ></ltw-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
            $t('关闭')
          }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
    <ChooseSensor ref="ChooseSensor" @reload="reload"/>
  </el-dialog>
</template>

<script>
import {
  saveFtmVariantVersionMappingModality,
  updateFtmVariantVersionMappingModality,
  getFtmVariantVersionMappingModality
} from '@/apis/fleet/ftm-variant-version-mapping-modality'
import {listFtmVehicleModality} from '@/apis/fleet/ftm-vehicle-modality'
import {
  showToast,
  isPositiveNum,
  isInteger,
  validateTwoFloatValidity,
  validateFourDecimalValidity,
  validateSevenDigitInteger, validateFourDigitInteger, validateThreeDigitInteger
} from '@/plugins/util'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection.vue'
import CommonSelection from '@/components/system/CommonSelection.vue'
import ChooseSensor from '@/pages/fleet/dialog/ChooseSensor.vue'

const defaultform = {sensorType: ''}
export default {
  name: 'AddDriver',
  emits: ['reload'],
  components: {DictionaryTypeSelection, CommonSelection, ChooseSensor},
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        modality: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('传感器'),
            trigger: 'change'
          }
        ],
        sensorType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        sensorId: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('传感器实体'),
            trigger: 'change'
          }
        ],
        positionX: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateSevenDigitInteger,
            trigger: 'change'
          }
        ],
        positionY: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateSevenDigitInteger,
            trigger: 'change'
          }
        ],
        positionZ: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateSevenDigitInteger,
            trigger: 'change'
          }
        ],
        height: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateSevenDigitInteger,
            trigger: 'change'
          },
          {
            validator: isPositiveNum,
            trigger: 'change'
          }
        ],
        syncFrameOffsetForward: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateFourDigitInteger,
            trigger: 'change'
          }
        ],
        syncFrameOffsetBack: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateFourDigitInteger,
            trigger: 'change'
          }
        ],
        lidarTimestampAngle: [
          {
            validator: validateTwoFloatValidity,
            trigger: 'change'
          },
          {
            validator: validateFourDigitInteger,
            trigger: 'change'
          }
        ],
        pcdJpgOffset: [
            {
              required: true,
              message: this.$t('请输入'),
              trigger: 'change'
            },
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateFourDigitInteger,
            trigger: 'change'
          }
        ],
        intervalDif: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          },
          {
            validator: validateTwoFloatValidity,
            trigger: ['change']
          },
          {
            validator: validateThreeDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: 'blur'
          }
        ],
        // difop: [
        //   {
        //     required: true,
        //     message: this.$t('请输入'),
        //     trigger: 'change'
        //   },
        //   {
        //     validator: isInteger,
        //     trigger: 'change'
        //   },
        //   {
        //     validator: isPositiveNum,
        //     trigger: 'change'
        //   }
        // ],
        // msop: [
        //   {
        //     required: true,
        //     message: this.$t('请输入'),
        //     trigger: 'change'
        //   },
        //   {
        //     validator: isInteger,
        //     trigger: 'change'
        //   },
        //   {
        //     validator: isPositiveNum,
        //     trigger: 'change'
        //   }
        // ]
      },
      id: '',
      sensorData: [],
      modalityData: [],
      useType: ''
      // filterSensorData: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    clearPositionX(data){
      if (!data){
        setTimeout(() => {
          this.form.positionX = 0
        })
      }
    },
    clearPositionY(data){

      if (!data){
        setTimeout(() => {
          this.form.positionY = 0
        })
      }
    },
    clearPositionZ(data){
      if (!data){
        setTimeout(() => {
          this.form.positionZ = 0
        })
      }
    },
    clearHeight(data){
      if (!data){
        setTimeout(() => {
          this.form.height = 0
        })
      }
    },
    clearDifop(data){
      if (!data){
        setTimeout(() => {
          this.form.difop = 0
        })
      }
    },
    clearMsop(data){
      if (!data){
        setTimeout(() => {
          this.form.msop = 0
        })
      }
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      // 判断是否更新调用接口
      this.form.variantVersionId = row.data.variantVersionId
      this.useType = row.data.useType
      // this.form.variant = row.data.variant
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增') + this.$t('传感器')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑') + this.$t('传感器')
          this.getFtmVariantVersionMappingModality(row.data)
          this.listFtmVehicleModality()
          break
        case 'view':
          this.dialogTitle = this.$t('传感器') + this.$t('详情')
          this.getFtmVariantVersionMappingModality(row.data)
          this.listFtmVehicleModality()
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {
    },
    initForm() {
      if (this.visible) {
        this.closeRequirementList()
      }
      this.$refs.formRef.resetFields()
      this.form = {...defaultform}
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveFtmVariantVersionMappingModality(this.form).then(() => {
            this.dialogVisible = false
            this.$emit('reload', this.form.variantVersionId)
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmVariantVersionMappingModality(this.form).then(() => {
            this.dialogVisible = false
            this.$emit('reload', this.form.variantVersionId)
          })
        }
      })
    },

    listFtmVehicleModality() {
      listFtmVehicleModality({sensorType: this.form.sensorType}).then(res => {
        this.modalityData = res.data
      })
    },
    getSupplierName(val) {
      let supplierItem = this.supplierList.find(item => item.id === val)
      this.form.supplierName = supplierItem.name
    },
    getModalityName(val) {
      let modalityItem = this.modalityData.find(item => item.code === val)
      this.form.modalityName = modalityItem.name
    },
    changeSensorType(row) {
      // 手动选择传感器时清空
      if (row) {
        this.form.sensorTypeName = row.name
      }
      // 清空关联表单
      this.form.modality = ''
      this.form.sensorModelSpecification = ''
      this.form.sensorId = ''
      this.form.cameraVO = {}
      this.modalityData = []
      // this.filterSensorData = []

      this.listFtmVehicleModality()
      // this.listFtmSensorSelection()
    },
    changeModality(row) {
      this.form.modalityName = row.name
    },
    getSensorList() {
      if (this.form.sensorType) {
        this.$refs.ChooseSensor.show({
          type: 'view',
          sensorType: this.form.sensorType
        })
      } else {
        showToast('请先选择传感器类型', 'warning')
      }
    },
    getFtmVariantVersionMappingModality(row) {
      getFtmVariantVersionMappingModality(row.id).then(res => {
        res.data.sensorModelSpecification =
            res.data.sensorTypeName +
            (res.data.model ? '-' + res.data.model : '') +
            (res.data.specification ? '-' + res.data.specification : '')
        this.form = res.data
      })
    },
    reload(row) {
      this.form.sensorId = row.id
      this.form.sensorModelSpecification =
          row.sensorTypeName + (row.model ? '-' + row.model : '') + (row.specification ? '-' + row.specification : '')
      this.form.cameraVO = row
    }
  }
}
</script>

<style scoped lang="scss">
.sensor-model-form {
  margin-bottom: 20px;

  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
