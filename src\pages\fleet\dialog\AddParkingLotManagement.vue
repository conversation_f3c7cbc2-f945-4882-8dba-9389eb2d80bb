<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    @close="dialogClosed"
    @open="dialogOpened"
    destroy-on-close
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <div class="header-title" :id="titleId" :class="titleClass">
          {{ dialogTitle }}
          <el-button
            v-if="dialogStatus === 'edit'"
            size="small"
            @click="toggleLock"
            :loading="lockLoading"
            class="lock-btn"
          >
            <ltw-icon :icon-code="form.isLocked === 1 ? 'svg-unlock' : 'svg-lock'"></ltw-icon>
          </el-button>
        </div>
      </div>
    </template>
    <el-tabs v-model="active" :before-leave="handleTabClick" class="tab-container">
      <el-tab-pane :label="$t('基本信息')" name="BASE"></el-tab-pane>
      <el-tab-pane :label="$t('标签信息')" name="TAG"></el-tab-pane>
    </el-tabs>
    <div class="step-body route" v-if="active === 'BASE'">
      <base-info
        :card-info="form"
        :dialog-status="dialogStatus"
        :mapStatusEdit="mapStatusEdit"
        ref="BaseInfo"
        @reload="reload"
        @cancel-btn="cancel"
        @update_map_status_edit="updateMapStatusEdit"
        @save-info="saveInfo"
      ></base-info>
    </div>
    <div class="step-body route" v-if="active === 'TAG'">
      <tag-info
        :card-info="form"
        :dialog-status="dialogStatus"
        :mapStatusEdit="mapStatusEdit"
        ref="TagInfo"
        @reload="reload"
        @cancel-btn="cancel"
        @update_map_status_edit="updateMapStatusEdit"
        @save-info="saveInfo"
      ></tag-info>
    </div>
  </el-dialog>
</template>
<script>
import BaseInfo from '@/pages/fleet/components/BaseInfo.vue'
import TagInfo from '@/pages/fleet/components/TagInfo.vue'
import { saveMdParkingLots, updateMdParkingLots, getMdParkingLots, lockMdParkingLot } from '@/apis/fleet/parking-lot-management'
import { showToast, showConfirmToast } from '@/plugins/util'

const defaultform = {}
export default {
  name: 'AddParkingLot',
  emits: ['reload'],
  data() {
    return {
      active: '',
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, JSON.parse(JSON.stringify(defaultform))),
      mapStatusEdit: false,
      lockLoading: false
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    BaseInfo,
    TagInfo
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增停车场')
          this.active = 'BASE'
          break
        case 'copy':
          this.dialogTitle = this.$t('新增停车场')
          this.getMdParkingLots(row.id)
          break
        case 'edit':
          this.getMdParkingLots(row.id)
          break
        case 'view':
          this.getMdParkingLots(row.id)
          break
        case 'map':
          this.getMdParkingLots(row.id)
          break
      }
    },
    async handleTabClick(e) {
      const active = this.active
      let changeFlag
      if (active === 'BASE') {
        changeFlag = this.$refs.BaseInfo?.checkFormChanged()
      } else if (active === 'TAG') {
        changeFlag = this.$refs.TagInfo?.checkFormChanged()
      }
      if (changeFlag) {
        await showConfirmToast({
          message: '请先确认已存储当前数据，是否继续？'
        })
      } else {
        return true
      }
    },
    saveInfo(postData) {
      if (!postData.longitude || !postData.name || !postData.type || !postData.envType) {
        return showToast('请先完善基本信息', 'warning')
      }
      if (postData.id) {
        updateMdParkingLots(postData).then(res => {
          showToast('数据已更新')
          this.form = postData
          this.$emit('reload')
          this.mapStatusEdit = false
          // this.getMdParkingLots(postData.id)
        })
      } else {
        saveMdParkingLots(postData).then(res => {
          this.form = res.data
          showToast('保存成功')
          this.$emit('reload')
          this.mapStatusEdit = false
        })
      }
    },
    dialogClosed() {
      this.initForm()
      this.cancel()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.active = ''
      this.form = Object.assign({}, JSON.parse(JSON.stringify(defaultform)))
    },
    getMdParkingLots(id) {
      getMdParkingLots(id).then(res => {
        if(res.data.addressPoint?.coordinates?.length){
          res.data.longitude = res.data.addressPoint.coordinates[0]
          res.data.latitude = res.data.addressPoint.coordinates[1]
        }
        if (this.dialogStatus === 'copy') {
          res.data.id = ''
          this.dialogTitle = this.$t('新增停车场')
        } else {
          this.dialogTitle = res.data.name || this.$t('停车场详情')
        }
        this.form = res.data
        this.active = 'BASE'
      })
    },
    reload(obj) {
      this.$emit('reload')
    },
    updateMapStatusEdit(flag) {
      this.mapStatusEdit = flag
    },
    async toggleLock() {
      const actionText = this.form.isLocked === 1 ? '解锁' : '锁定'
      const confirmMessage = `确定要${actionText}该停车场吗？`
      
      try {
        await showConfirmToast({
          message: confirmMessage
        })
      } catch {
        // 用户取消操作
        return
      }
      
      this.lockLoading = true
      try {
        const lockData = {
          parkingLotId: this.form.id,
          isLocked: this.form.isLocked === 1 ? false : true
        }
        
        await lockMdParkingLot(lockData)
        
        // 更新本地状态
        this.form.isLocked = this.form.isLocked === 1 ? 0 : 1
        
        // 显示成功提示
        showToast(`${actionText}成功`)
        
        // 通知父组件刷新数据
        this.$emit('reload')
      } catch (error) {
        console.error('锁定操作失败:', error)
        showToast('操作失败，请重试', 'error')
      } finally {
        this.lockLoading = false
      }
    }
  }
}
</script>
<style>
.canton-list .el-cascader-panel {
  height: 50vh;
}
</style>
<style scoped lang="scss">
.dialog-header {
  display: flex;
  align-items: center;
  
  .header-title {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .lock-btn {
      background-color: #F56C6C;
      font-size: 12px;
      padding: 4px 8px;
      
      .ltw-icon {
        margin-right: 4px;
      }
    }
  }
}

.el-form-item {
  .el-link {
    font-size: 20px;
  }
}

.parking-list {
  padding-left: 15px;

  .header-title {
    padding-left: 15px;
    border-left: 3px solid #409eff;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}
</style>
