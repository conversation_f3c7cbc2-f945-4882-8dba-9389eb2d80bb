<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-tool-container">
                    <el-select
                        v-model="queryParam.applicationId"
                        placeholder="请选择应用"
                        @change="handleApplicationChange"
                    >
                        <el-option
                            v-for="item in applications"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </div>
                <div class="ltw-search-container ltw-tool-container">
                    <ltw-input
                        placeholder="请输入关键字"
                        v-model="queryParam.key"
                        clearable
                        @clear="refresh"
                    >
                        <template #append>
                            <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                        {{ item.name }}
                    </el-button>
                    <el-dropdown
                        @command="handleCommand"
                        class="batch-operate-btn"
                        v-if="batchingFunctionList && batchingFunctionList.length > 0"
                    >
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                    {{ item.name }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table
                :data="pageData.records"
                row-key="id"
                @selection-change="handleSelectionChange"
                :tree-pros="{ children: 'children', hasChildren: '!asLeaf' }"
                default-expand-all
                ref="tableRef"
            >
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                <el-table-column header-align="left" prop="name" label="名称"></el-table-column>
                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                <el-table-column
                    header-align="left"
                    align="left"
                    prop="iconCode"
                    label="图标"
                    width="120"
                >
                    <template #default="scope">
                        <div class="table-icon-container">
                            <ltw-icon :icon-code="scope.row.iconCode"></ltw-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    header-align="left"
                    align="left"
                    prop="pageTypeName"
                    label="页面类型"
                ></el-table-column>
                <el-table-column header-align="left" align="left" prop="pageUrl" label="页面url"></el-table-column>
                <el-table-column
                    header-align="left"
                    align="left"
                    prop="pagePath"
                    label="前端页面目录"
                ></el-table-column>

                <el-table-column header-align="left" align="left" prop="sortNum" label="展示顺序"></el-table-column>
                <el-table-column header-align="left" align="left" prop="enabled" label="状态">
                    <template #default="scope">
                        <el-switch v-model="scope.row.enabled" @change="changeStatus(scope.row)"></el-switch>
                    </template>
                </el-table-column>
                <!--                <el-table-column header-align="left" align="left" prop="parentId" label="父级单元id"></el-table-column>-->
                <!--                <el-table-column header-align="left" align="left" prop="codeLink" label="编码层级链"></el-table-column>-->
                <!--                <el-table-column header-align="left" align="left" prop="pageId" label="菜单入口页面id"></el-table-column>-->
                <!--                <el-table-column header-align="left" align="left" prop="paramValues" label="入参值"></el-table-column>-->
                <el-table-column header-align="left" align="left" label="操作" width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip
                                :key="item.id"
                                v-for="item in inlineFunctionList"
                                effect="dark"
                                :content="item.name"
                                placement="top"
                                :enterable="false"
                            >
                                <el-button
                                    :type="item.buttonStyleType"

                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                </el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryParam.current"
                :page-sizes="[5, 10, 20, 30]"
                :page-size="queryParam.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageData.total"
            ></el-pagination>
        </el-card>

        <el-dialog
            :title="dialogTitle"
            v-model="dialogVisible"
            width="50%"
            @close="dialogClosed"
            @open="dialogOpened"
            draggable
        >
            <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
                <el-form-item label="所属应用" prop="applicationId">
                    <el-select
                        v-model="formData.applicationId"
                        filterable
                        placeholder="请选择"
                        @change="handleApplicationSelect"
                        :disabled="dialogStatus !== 'add'"
                    >
                        <el-option
                            v-for="item in applications"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属模块" prop="moduleId">
                    <el-select
                        v-model="formData.moduleId"
                        filterable
                        placeholder="请选择"
                        @change="handleModuleSelect"
                        :disabled="dialogStatus !== 'add'"
                    >
                        <el-option
                            v-for="item in modules"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="名称" prop="name">
                    <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="编码" prop="code">
                    <ltw-input v-model="formData.code" :disabled="formReadonly" textType="remark"></ltw-input>
                </el-form-item>
                <el-form-item label="图标" prop="iconCode">
                    <icon-selection
                        v-model="formData.iconCode"
                        :disabled="formReadonly"
                        ref="iconSelection"
                    ></icon-selection>
                </el-form-item>
                <el-form-item label="父级菜单" prop="parentId">
                    <el-cascader
                        v-model="formData.parentId"
                        :props="parentMenuProps"
                        placeholder="请选择父菜单"
                        :options="parentMenuSelections"
                        filterable
                        @change="handleParentMenuChange"
                        clearable
                        :disabled="formReadonly"
                    ></el-cascader>
                </el-form-item>
                <el-form-item label="页面类型" prop="pageType">
                    <dictionary-selection
                        ref="dictionarySelectionRef"
                        v-model="formData.pageType"
                        dictionary-type="page_type"
                        :disabled="formReadonly"
                        clearable
                    ></dictionary-selection>
                </el-form-item>
                <el-form-item label="地址" prop="pageUrl" v-if="!asParentMenu">
                    <ltw-input textType="description" v-model="formData.pageUrl" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="前端地址" prop="pagePath" v-if="!asParentMenu">
                    <ltw-input textType="description" v-model="formData.pagePath" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="顺序" prop="sortNum">
                    <ltw-input v-model="formData.sortNum" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="功能按钮" prop="sortNum" v-if="showFunction">
                    <el-button
                        type="primary"
                        size="small"
                        @click="addFunction()"
                        v-if="!formReadonly"
                    >添加</el-button>
                    <el-table
                        :data="formData.functionList"
                        row-key="buttonId"
                        class="mini-table" border stripe
                    >
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="name"
                            label="名称"
                        >
                            <template #default="scope">
                                <ltw-input v-model="scope.row.name" v-if="scope.row.editing"></ltw-input>
                                <span v-else>{{ scope.row.name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="authCode"
                            label="授权标识"
                        >
                            <template #default="scope">
                                <ltw-input v-model="scope.row.authCode" v-if="scope.row.editing"></ltw-input>
                                <span v-else>{{ scope.row.authCode }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="button"
                            label="按钮"
                        >
                            <template #default="scope">{{ scope.row.buttonName }}</template>
                        </el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="sortNum"
                            label="顺序"
                            width="50"
                        >
                            <template #default="scope">
                                <ltw-input v-model="scope.row.sortNum" v-if="scope.row.editing"></ltw-input>
                                <span v-else>{{ scope.row.sortNum }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="enabled"
                            label="启用状态"
                            width="80"
                        >
                            <template #default="scope">
                                <el-switch
                                    v-model="scope.row.enabled"
                                    :disabled="!scope.row.editing"
                                ></el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            label="操作"
                            width="120"
                            v-if="!formReadonly"
                        >
                            <template #default="scope">
                                <el-button-group>
                                    <el-tooltip
                                        effect="dark"
                                        content="保存"
                                        placement="top"
                                        :enterable="false"
                                        v-if="scope.row.editing"
                                    >
                                        <el-button
                                            type="success"

                                            @click="saveFunction(scope.row)"
                                        >
                                            <el-icon>
                                                <Finished />
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip
                                        effect="dark"
                                        content="编辑"
                                        placement="top"
                                        :enterable="false"
                                        v-else
                                    >
                                        <el-button
                                            type="warning"

                                            @click="editFunction(scope.row)"
                                        >
                                            <el-icon>
                                                <Edit />
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip
                                        effect="dark"
                                        content="删除"
                                        placement="top"
                                        :enterable="false"
                                    >
                                        <el-button
                                            type="danger"

                                            @click="singleRemoveFunction(scope.row.id, scope.$index, formData.functionList)"
                                        >
                                            <el-icon>
                                                <Delete />
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>
                                </el-button-group>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form-item>
                <el-form-item label="启用状态" prop="enabled">
                    <el-switch v-model="formData.enabled" :disabled="formReadonly"></el-switch>
                </el-form-item>
                <!--                <el-form-item label="父级单元id" prop="parentId">-->
                <!--                    <ltw-input v-model="formData.parentId" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="编码层级链" prop="codeLink">-->
                <!--                    <ltw-input v-model="formData.codeLink" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="名称" prop="name">-->
                <!--                    <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="icon编码" prop="iconCode">-->
                <!--                    <ltw-input v-model="formData.iconCode" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="菜单入口页面id" prop="pageId">-->
                <!--                    <ltw-input v-model="formData.pageId" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="入参值" prop="paramValues">-->
                <!--                    <ltw-input v-model="formData.paramValues" :disabled="formReadonly"></ltw-input>-->
                <!--                </el-form-item>-->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>

        <el-drawer
            title="添加功能按钮"
            v-model="functionAddVisible"
            direction="rtl"
            :size="500"
            @close="handleFunctionAddClose"
        >
            <div class="drawer-body">
                <div class="ltw-toolbar">
                    <div class="ltw-search-container ltw-tool-container">
                        <ltw-input
                            placeholder="请输入关键字"
                            v-model="buttonQueryParam.key"
                            clearable
                            @clear="buttonRefresh"
                        >
                            <template #append>
                                <el-button @click="buttonRefresh">
                                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                                </el-button>
                            </template>
                        </ltw-input>
                    </div>
                    <div class="ltw-tool-container button-group">
                        <el-button type="primary" @click="confirmAddFunction">确认</el-button>
                    </div>
                </div>
                <div class="table-contaier">
                    <el-table
                        :data="buttonList"
                        @selection-change="handleButtonSelectionChange"
                        row-key="id"
                        ref="buttonTableRef"
                    >
                        <el-table-column
                            header-align="left"
                            align="left"
                            type="selection"
                            width="55"
                        ></el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="code"
                            label="编码"
                        ></el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="name"
                            label="名称"
                        ></el-table-column>
                        <el-table-column
                            header-align="left"
                            align="left"
                            prop="iconCode"
                            label="图标"
                            width="120"
                        >
                            <template #default="scope">
                                <div class="table-icon-container">
                                    <ltw-icon :icon-code="scope.row.iconCode"></ltw-icon>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {
    deleteSysPrivilegeMenu,
    getSysPrivilegeMenu,
    saveSysPrivilegeMenu,
    treeListSysPrivilegeMenu,
    treePageSysPrivilegeMenu,
    updateSysPrivilegeMenu
} from '@/apis/system/sys-privilege-menu'
import { deleteSysPrivilegeFunction, updateSysPrivilegeFunction, saveSysPrivilegeFunction } from '@/apis/system/sys-privilege-function'
import { listSysButton } from '@/apis/system/sys-button'
import { listSysPrivilegeModule } from "../../apis/system/sys-privilege-module";
import GLB_CONFIG from '@/plugins/glb-constant'
import { listSysPrivilegeApplication } from "@/apis/system/sys-privilege-application";
import IconSelection from "@/components/system/IconSelection";
import DictionarySelection from "@/components/system/DictionarySelection";
import MENU_PAGE_TYPE from '@/plugins/constants/menu-page-type'
import LtwIcon from '@/components/base/LtwIcon.vue';
import BASE_CONSTANT from '../../plugins/constants/base-constant';



const defaultFormData = {
    enabled: true,
    iconCode: '',
    pageType: MENU_PAGE_TYPE.INNER_ROUTER
}
export default {
    name: "SysPrivilegeMenu",
    components: { IconSelection, DictionarySelection, LtwIcon },
    data() {
        return {
            batchingFunctionList: [],
            inlineFunctionList: [],
            outlineFunctionList: [],

            pageData: {
                total: 0
            },
            queryParam: {
                current: 1,
                size: 10,
                applicationId: GLB_CONFIG.applicationId
            },
            dialogVisible: false,
            formData: Object.assign({}, defaultFormData),
            formRules: {
                code: [
                    { required: true, message: '请输入菜单编码', trigger: 'blur' }
                ],
                name: [
                    { required: true, message: '请输入菜单名称', trigger: 'blur' }
                ],
                applicationId: [
                    { required: true, message: '请选择所属应用', trigger: 'blur' }
                ]
            },
            dialogTitle: '',
            dialogStatus: '',
            selectedData: [],
            parentMenuProps: {
                expandTrigger: 'hover',
                value: 'id',
                label: 'name',
                children: 'children',
                checkStrictly: true,
                disabled: 'disabled',
                emitPath: false
            },
            applications: [],
            parentMenuSelections: [],
            currentSelectedApplicationId: '',
            currentOperatedMenuId: '',
            modules: [],
            buttonList: [],
            functionAddVisible: false,
            selectedButtonList: [],
            buttonQueryParam: {}
        }
    },
    created() {
        if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
            this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
            this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
            this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList

        }
        this.queryApplication()
        this.query()
    },
    computed: {
        formReadonly() {
            return this.dialogStatus === 'view'
        },
        showFunction() {
            return this.formData.pageType === MENU_PAGE_TYPE.INNER_ROUTER
        },
        asParentMenu() {
            return this.formData.pageType === MENU_PAGE_TYPE.PARENT_MENU
        }
    },
    methods: {
        executeButtonMethod(funcName, row) {
            this[funcName](row)
        },
        refresh() {
            this.$refs.tableRef.clearSelection()
            this.query()
        },
        query() {
            treePageSysPrivilegeMenu(this.queryParam).then(
                res => {
                    this.pageData = res.data
                }
            )
        },
        addFunctionList(basicButtons) {
            if (!this.formData.functionList) {
                this.formData.functionList = []
            }
            basicButtons.forEach(
                btn => {
                    let data = {
                        menuId: this.formData.id,
                        buttonId: btn.id,
                        sortNum: btn.sortNum,
                        buttonName: btn.name,
                        name: btn.name,
                        enabled: true
                    }
                    if (!data.id) {
                        data.editing = true
                    }
                    this.formData.functionList.push(data)
                }
            )
        },
        add() {
            this.formData.applicationId = this.queryParam.applicationId
            if (this.$store.state.basicButtons.length > 0) {
                this.addFunctionList(this.$store.state.basicButtons)
            } else {
                this.$store.dispatch('setBasicButtons').then(
                    basicButtons => this.addFunctionList(basicButtons)
                )
            }
            this.currentSelectedApplicationId = this.formData.applicationId
            this.dialogTitle = '添加菜单'
            this.dialogStatus = 'add'
            this.dialogVisible = true
            if (this.pageData.total) {
                let lastSorNum = this.pageData.total;
                if (lastSorNum) {
                    this.formData.sortNum = lastSorNum + 1
                }
            }
            this.queryModule({ applicationId: this.currentSelectedApplicationId })
            this.queryParentMenu({ applicationId: this.currentSelectedApplicationId })
        },
        save() {
            if (!this.showFunction) {
                this.formData.functionList = []
            }
            if (this.dialogStatus === 'add') {
                saveSysPrivilegeMenu(this.formData).then(
                    () => {
                        this.dialogVisible = false
                        this.query()
                    }
                )
            }
            if (this.dialogStatus === 'edit') {
                let updatedFunctions = []
                if (this.formData.functionList && this.formData.functionList.length > 0) {
                    this.formData.functionList.forEach(
                        func => {
                            if (func.editing) {
                                updatedFunctions.push(func)
                            }
                        }
                    )
                }
                let data = Object.assign({}, this.formData)
                data.functionList = updatedFunctions
                updateSysPrivilegeMenu(data).then(
                    () => {
                        this.dialogVisible = false
                        this.query()
                    }
                )
            }
        },
        edit({ id }) {
            this.dialogTitle = '修改菜单'
            this.dialogStatus = 'edit'
            this.currentOperatedMenuId = id
            getSysPrivilegeMenu(id).then(
                res => {
                    this.dialogVisible = true
                    this.$nextTick(function () {
                        this.formData = res.data
                        this.queryModule({ application: this.formData.applicationId })
                        this.queryParentMenu({ moduleId: this.formData.moduleId })
                    })
                }
            )
        },
        handleCommand(command) {
            if (this.selectedData.length === 0) {
                this.$message.warning({
                    message: '请先选择数据再执行批量操作',
                    type: 'warning'
                })
                return
            }
            if (command === 'batchRemove') {
                this.batchRemove()
            }
        },
        singleRemove(row) {
            this.remove({ id: row.id, containChildren: !row.asLeaf })
        },
        batchRemove() {
            let idList = [];
            this.selectedData.forEach(ele => {
                idList.push(ele.id)
            })
            let ids = idList.join(',')
            this.remove({ ids })
        },
        remove(param) {
            let msg = '此操作将永久删除选中菜单及其子菜单，是否继续?'
            this.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteSysPrivilegeMenu(param).then(
                    () => {
                        this.query()
                    }
                )
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
            })
        },
        view({ id }) {
            this.dialogTitle = '查看菜单'
            this.dialogStatus = 'view'
            this.currentOperatedMenuId = id
            getSysPrivilegeMenu(id).then(
                res => {
                    this.dialogVisible = true
                    this.$nextTick(function () {
                        this.formData = res.data
                        this.queryModule({ application: this.formData.applicationId })
                        this.queryParentMenu({ moduleId: this.formData.moduleId })
                    })
                }
            )
        },
        handleSizeChange(value) {
            this.queryParam.size = value
            this.query()
        },
        handleCurrentChange(value) {
            this.queryParam.current = value
            this.query()
        },
        dialogOpened() {
            listSysPrivilegeApplication().then(
                res => {
                    this.applications = res.data
                }
            )
        },
        dialogClosed() {
            this.initForm()
        },
        handleApplicationSelect(applicationId) {
            if (!applicationId) {
                this.currentSelectedApplicationId = ''
                this.parentMenuSelections = []
                this.modules = []
                return
            }
            this.currentSelectedApplicationId = applicationId
            this.queryModule({ applicationId })
            this.queryParentMenu({ applicationId })
        },
        queryModule(param) {
            listSysPrivilegeModule(param).then(res => {
                this.modules = res.data
            })
        },
        handleModuleSelect(moduleId) {
            this.queryParentMenu({ moduleId })
        },
        queryParentMenu(param) {
            treeListSysPrivilegeMenu(Object.assign({}, param, { currentSelectedId: this.currentOperatedMenuId })).then(
                res => {
                    this.parentMenuSelections = res.data
                }
            )
        },
        handleSelectionChange(value) {
            this.selectedData = value
        },
        changeStatus(row) {
            updateSysPrivilegeMenu({
                id: row.id,
                enabled: row.enabled
            }).catch(() => {
                row.enabled = !row.enabled
            })
        },
        handleParentMenuChange(val) {
            if (!val) {
                this.formData.parentId = BASE_CONSTANT.TREE_ROOT_PARENT
            }
        },
        queryApplication() {
            listSysPrivilegeApplication().then(
                res => {
                    this.applications = res.data
                }
            )
        },
        handleApplicationChange() {
            this.query()
        },
        initForm() {
            this.$refs.formRef.resetFields()
            this.formData = Object.assign({}, defaultFormData)
            this.currentOperatedMenuId = ''
            this.parentMenuSelections = []
            this.$refs.iconSelection.close()
        },
        editFunction(row) {
            row.editing = true
        },
        saveFunction(row) {
            if (row.id) {
                updateSysPrivilegeFunction(row).then(
                    () => {
                        this.$message.success({
                            message: '保存成功',
                            type: 'success'
                        })
                        row.editing = false
                    }
                )
            } else {
                if (this.formData.id) {
                    saveSysPrivilegeFunction(row).then(
                        res => {
                            this.$message.success({
                                message: '保存成功',
                                type: 'success'
                            })
                            row.id = res.data.id
                            row.editing = false
                        }
                    )
                } else {
                    row.editing = false
                }
            }
        },
        singleRemoveFunction(id, index, rows) {
            let msg = '此操作将永久删除选中数据，是否继续?'
            this.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (id) {
                    deleteSysPrivilegeFunction({ id }).then(
                        () => {
                            this.$message.success({
                                message: '删除成功',
                                type: 'success'
                            })
                            rows.splice(index, 1)
                        }
                    )
                } else {
                    rows.splice(index, 1)
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
            })
        },
        addFunction() {
            this.functionAddVisible = true
            this.buttonQuery()
        },
        handleButtonSelectionChange(value) {
            this.selectedButtonList = value
        },
        buttonRefresh() {
            this.$refs.buttonTableRef.clearSelection()
            this.buttonQuery()
        },
        buttonQuery() {
            listSysButton(this.buttonQueryParam).then(
                res => {
                    let data = res.data
                    this.buttonList = []
                    data.forEach(
                        ele => {
                            let has = false
                            if (this.formData.functionList && this.formData.functionList.length !== 0) {
                                this.formData.functionList.every(
                                    func => {
                                        if (ele.id === func.buttonId) {
                                            has = true
                                            return false
                                        } else {
                                            return true
                                        }
                                    }
                                )
                            }
                            if (!has) {
                                this.buttonList.push(ele)
                            }
                        }
                    )
                }
            )
        },
        confirmAddFunction() {
            this.functionAddVisible = false
            this.addFunctionList(this.selectedButtonList)
        },
        handleFunctionAddClose() {
            this.$refs.buttonTableRef.clearSelection()
        }
    }
}
</script>

<style scoped lang="scss">

.table-icon-container {
    font-size: 21px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-icon-container {
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
}
:deep .el-drawer__body {
    height: calc(100vh - 125px);
    padding-right: 10px;
    .drawer-body {
        height: 100%;
        .table-contaier {
            height: calc(100% - 50px);
            overflow-y: auto;
        }
    }
}
</style>
