<template>
  <div class="dialog-container task-step">
    <el-scrollbar>
      <el-tabs v-model="activeName" type="border-card" class="tab-box">
        <el-tab-pane label="标签列表" name="first">
          <tag-draggable
            :dialogStatus="dialogStatus"
            :checkedTagList="checkedTagList"
            @updateTreeData="updateTreeData"
          ></tag-draggable>
        </el-tab-pane>
        <el-tab-pane label="标签KPI" name="second">
          <TagKPI :data="newTreeData" :continuousUnits="continuousUnits" :editTags="!formReadonly"></TagKPI>
        </el-tab-pane>
        <el-tab-pane label="标签示例图" name="third">
          <tag-new-samples-dialog
            ref="TagSamplesDialog"
            :dialogStatus="dialogStatus"
            :requirementList="requirementList"
            @updateDes="updateDes"
          />
        </el-tab-pane>
        <el-tab-pane label="标签属性" name="forth">
          <TagAttribute :data="newTreeData" :continuousUnits="continuousUnits" :editTags="!formReadonly"></TagAttribute>
        </el-tab-pane>
      </el-tabs>
    </el-scrollbar>
  </div>
</template>

<script>
import TagDraggable from '../components/TagDraggable.vue'
import TagKPI from '../components/TagKPI.vue'
import TagAttribute from '../components/TagAttribute.vue'
import TagNewSamplesDialog from './TagNewSamplesDialog.vue'

export default {
  components: {
    TagDraggable,
    TagKPI,
    TagNewSamplesDialog,
    TagAttribute
  },
  props: {
    tagData: {
      type: Array
    },
    dialogStatus: {
      type: String,
      default: ''
    },
    requirementList: {
      type: Array
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  watch: {
    tagData: {
      handler(newval, old) {
        this.checkedTagList = newval
        this.newTreeData = newval
      },
      immediate: true
    },
    newTreeData: {
      handler(newval, old) {
        this.checkedTagList = newval
        this.$emit('updateTreeData', newval)
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      activeName: 'first',
      checkedTagList: [],
      listData: '',
      continuousUnits: [],
      editTagsFlag: '',
      newTreeData: ''
    }
  },
  mounted() {
    switch (this.dialogStatus) {
      case 'add':
        this.editTagsFlag = true
        break
      case 'edit':
        this.editTagsFlag = true
        break
      case 'view':
        this.editTagsFlag = false
        break
    }
  },
  methods: {
    updateTreeData(val) {
      this.newTreeData = val
      this.$emit('updateTreeData', val)
    },
    updateDes(val) {
      this.$emit('updateDes', val)
    },
    closeDialog() {
      this.activeName = 'first'
      this.checkedTagList = []
      this.listData = ''
      this.continuousUnits = []
      this.editTagsFlag = ''
      this.newTreeData = ''
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-container {
  display: flex;
  margin-top: 10px;

  .tab-box {
    width: 100%;
  }
  .el-scrollbar{
    width: 100%;
  }
}
</style>
