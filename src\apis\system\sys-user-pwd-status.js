import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysUserPwdStatus = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss', data, params})
export const updateSysUserPwdStatus = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss', data, params})
export const deleteSysUserPwdStatus = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss', params})
export const listSysUserPwdStatus = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss', params})
export const listSysUserPwdStatusSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss/selections', params})
export const pageSysUserPwdStatus = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss/page', params})
export const getSysUserPwdStatus = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_user_pwd_statuss/' + id})
