<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      :destroy-on-close="true"
  >
    <div class="dialog-body">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item :label="$t('日报类型')" prop="type">
          <el-select
              filterable
              v-model="formData.type"
              clearable
              placeholder="请选择日报类型"
          >
            <el-option
                v-for="item in workReportTypeList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('日报')" prop="fileIds">
          <upload-file
              accept=".csv,.xlsx"
              ref="uploadDbc"
              @returnFile="assignFile"
              :limit="1"
              source-type="work_report_import_file"
              listType="text"
              v-model="formData.fileIds"
              class="file-btn"
          />
        </el-form-item>
      </el-form>
      <div style="display: flex;justify-content: flex-end">
        <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
        <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'
import {uploadWorkReport} from "@/apis/base/file";

const defaultFormData = {}
export default {
  name: 'ImportWorkReport',
  emits: ['reload', 'cancel'],
  data() {
    return {
      formData: {},
      file:{},
      formRules: {
        type: [{required: true, message: this.$t('请选择日报类型'), trigger: 'blur'}],
        fileIds: [{required: true, message: this.$t('请上传日报'), trigger: 'blur'}]
      },
      workReportTypeList: [
        {
          "code": "urban_e2e",
          "name": "Urban E2E"
        },
        {
          "code": "highway_e2e",
          "name": "Highway E2E"
        }
      ],
      dialogStatus: '',
      objectValue: {},
      dialogVisible: false,
      dialogTitle: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    UploadFile
  },
  created() {
  },
  methods: {
    assignFile(file) {
      this.file = file
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = new FormData()
        postData.append('file', this.file)
        postData.append('type', this.formData.type)
        uploadWorkReport(postData).then(() => {
          this.dialogVisible = false
          this.$message.success("导入成功")
        })
      })
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = this.$t('导入数据')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {
    },
    submit() {
    },
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.add-form {
  padding: 10px 0;

  .form-footer {
    text-align: right;
  }
}

.sensor-files {
  .json-content {
    position: relative;
    margin-top: 36px;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    // .jsonTxt{
    //   height:200px

    // }
  }

  .sensor-content {
    position: relative;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    .file-type-tabs {
      // position: absolute;
      // top: -10px;
      position: absolute;
      top: -40px;

      :deep(.el-tabs__header) {
        margin: 0;
      }
    }

    .sensor-list-tabs {
      display: flex;
      margin-top: 50px;
      // overflow: visible;
      :deep(.el-tabs__header) {
        margin-right: 0;
        border-right: none;
      }

      :deep(.el-tabs__content) {
        // overflow: visible;
        flex-grow: 1;
        padding: 0;

        .el-tab-pane,
        .el-tab-pane > .border-card-content,
        .el-tab-pane > .border-card-content > .text,
        .el-tab-pane > .border-card-content > .text > .container {
          height: 100%;
        }

        .el-tab-pane > .border-card-content > .text {
          width: 100%;
        }

        // .el-tab-pane > .border-card-content > .text > .container>.dividerStyle {

        //   border: thin solid #3b4969;
        // }
        .el-tab-pane > .border-card-content > .text > .container .jsoneditor {
          border: thin solid #dcdfe6;
        }

        .el-tab-pane > .border-card-content > .text > .container {
          margin: 0 !important;

          .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
            height: 100%;
            margin: 0;
            padding: 0;
          }

          .jsoneditor-menu,
          .jsoneditor-navigation-bar {
            display: none;
          }
        }
      }

      .border-card-content {
        position: relative;

        :deep(.jsoneditor-poweredBy) {
          display: none;
        }
      }
    }
  }
}
</style>
