<template>
  <div class="map-overview" :id="mapId" ref="mapContainerRef"></div>
</template>

<script>
import {
  initMap,
  initMapEditor,
  initMarkers,
  latLngPoint,
  showFitView,
  createPolylineLayer,
  initMultiLabel,
  addGeo,
  removeGeo,
  goCenter,
  initDriving,
  createPolygonLayer,
  initClusterMarkers,
  initInfoWindows
} from '@/plugins/map/TxMap'
import { debounce } from '@/plugins/util'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'

// let mapObj = {}
export default {
  name: 'MapPoi',
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'reload',
    'change-bounds',
    'click-marker',
    'draw-complete',
    'delete-complete',
    'adjust-complete',
    'select-complete',
    'click-route',
    'right-click-map',
    'click-editor-marker',
    'click-area',
    'cluster-changed',
    'zoom-changed',
    'click-label'
  ],
  data() {
    return {
      OVER_LAY_TYPE,
      mapId: 'map',
      selectedStyleName: 'selectedStyle'
    }
  },
  beforeUnmount() {
    if (this.mapObj.globalMap) {
      this.mapObj.globalMap?.destroy()
      this.mapObj = {}
    }
  },
  mounted() {
    // if (!mapObj.globalMap) {
    //   this.loadMap()
    // }
  },
  methods: {
    async show(row) {
      // if (this.mapObj.globalMap) {
      //   this.mapObj.globalMap?.destroy()
      //   mapObj = {}
      // }
      await this.loadMap(row)
      if(row?.centerMarker !== false){
        this.initCenterMarkers(row?.markers)
      }
    },
    async loadMap(row) {
      const _this = this
      this.mapObj = {}
      this.mapObj.globalMap = await initMap(this.$refs.mapContainerRef, row)
      const throttledHandleBoundsChange = debounce(this.handleBoundsChange, 1000)
      this.mapObj.globalMap.addListener('bounds_changed', throttledHandleBoundsChange)
      this.mapObj.globalMap.addListener('rightclick', _this.handleMapRightClick)
      this.mapObj.globalMap.addListener('zoom_changed', _this.handleZoomChange)
    },
    initDotMap(key = 'globalDotMap') {
      if (!this.mapObj?.[key]) {
        this.mapObj[key] = new TMap.visualization.Dot({
          styles: {
            default: {
              fillColor: '#331dfa',
              radius: 2
            }
          },
          enableBloom: true
        }).addTo(this.mapObj.globalMap)
      }
      return this.mapObj?.[key]
    },
    initMarkers(markers, key = 'globalMarker') {
      this.mapObj[key] = initMarkers(this.mapObj.globalMap, markers)
      this.mapObj?.[key].on('click', this.clickHandlerMarkers)
      return this.mapObj?.[key]
    },
    initMultiLabel(labels, key = 'globalMultiLabel') {
      const _this = this
      this.mapObj[key] = initMultiLabel(this.mapObj.globalMap, labels)
      this.mapObj?.[key].on('click', _this.handleLabelClick)
      return this.mapObj?.[key]
    },
    initClusterMarkers(markers, key = 'globalMarkerCluster') {
      const _this = this
      this.mapObj[key] = initClusterMarkers(this.mapObj.globalMap, markers)
      this.mapObj?.[key].on('cluster_changed', _this.clusterChange)
      return this.mapObj?.[key]
    },
    createPolylineLayer(routeLines, options, multPolylineId, zIndex, key = 'globalRouteListPolylineLayer') {
      const _this = this
      this.mapObj[key] = createPolylineLayer(this.mapObj.globalMap, routeLines, options, multPolylineId, zIndex)
      this.mapObj?.[key].on('click', _this.handleRouteClick)
      return this.mapObj?.[key]
    },
    createPolygonLayer(areas, options, multPolylineId, zIndex, key = 'globalPolygonLayer') {
      const _this = this
      this.mapObj[key] = createPolygonLayer(this.mapObj.globalMap, areas, options, multPolylineId, zIndex)
      this.mapObj?.[key].on('click', _this.handleAreaClick)
      return this.mapObj?.[key]
    },
    initInfoWindow(key = 'globalInfoWindow') {
      this.mapObj[key] = initInfoWindows(this.mapObj.globalMap)
      return this.mapObj?.[key]
    },
    createInfoWindow(infoWindow, key = 'globalInfoWindow') {
      this.mapObj[key] = infoWindow
      return this.mapObj?.[key]
    },
    initDriving(options, key = 'globalDriving') {
      this.mapObj[key] = initDriving(options)
      return this.mapObj?.[key]
    },
    getGlobalMapObj(key) {
      return this.mapObj?.[key]
    },
    initCenterMarkers(markerList) {
      markerList = markerList?.length
        ? markerList
        : [
            {
              id: 'centerMarker',
              style: { width: 25, height: 35, anchor: { x: 10, y: 30 } }
            }
          ]
      this.mapObj.globalCenterMarker = initMarkers(this.mapObj.globalMap, markerList)
    },
    showFitView(list) {
      const fitViewList = list.map(val => {
        if (val.position) {
          return val.position
        } else {
          return latLngPoint({ lat: val.latitude, lng: val.longitude })
        }
      })
      showFitView(fitViewList, this.mapObj.globalMap)
    },
    //支持对象/string名称
    clearObjects(...objects) {
      objects.forEach(obj => {
        if (Object.prototype.toString.call(obj) === '[object Object]') {
          obj.setMap(null)
        } else if (Object.prototype.toString.call(obj) === '[object String]') {
          this.mapObj?.[obj]?.setMap(null)
        }
      })
    },
    handleMapRightClick(e) {
      this.$emit('right-click-map', e)
    },
    handleRouteClick(e) {
      this.$emit('click-route', e)
    },
    handleLabelClick(e) {
      this.$emit('click-label', e)
    },
    handleAreaClick(e) {
      this.$emit('click-area', e)
    },
    clickHandlerMarkers(e) {
      this.$emit('click-marker', e)
    },
    clusterChange(e) {
      this.$emit('cluster-changed', e)
    },
    handleZoomChange(e) {
      this.$emit('zoom-changed', e)
    },
    getGeometries() {
      return [
        ...(this.mapObj.globalMapEditorPolygon?.geometries || []),
        ...(this.mapObj.globalMapEditorCircle?.geometries || []),
        ...(this.mapObj.globalMapEditorEllipse?.geometries || []),
        ...(this.mapObj.globalMapEditorRectangle?.geometries || [])
      ]
    },
    clearEditorPolygon() {
      this.mapObj.globalMapEditorPolygon?.setGeometries([])
      this.mapObj.globalMapEditorCircle?.setGeometries([])
      this.mapObj.globalMapEditorEllipse?.setGeometries([])
      this.mapObj.globalMapEditorRectangle?.setGeometries([])
      this.mapObj.globalMapEditorMarker?.setGeometries([])
    },
    async changeEditorMode(val) {
      if (!this.mapObj.globalMapEditor) {
        await this.initEditor()
      }
      if (val === TMap.tools.constants.EDITOR_ACTION.NONE) {
        this.resetEditorMode()
        return
      }
      const isDrawMode = val === TMap.tools.constants.EDITOR_ACTION.DRAW
      this.mapObj.globalMapEditor.setSelectable(!isDrawMode)
      this.mapObj.globalMapEditor.setSnappable(!isDrawMode)
      this.mapObj.globalMapEditor.setActionMode(
        isDrawMode ? TMap.tools.constants.EDITOR_ACTION.DRAW : TMap.tools.constants.EDITOR_ACTION.INTERACT
      )
    },
    // 重置编辑模式
    resetEditorMode() {
      // 开启或关闭点选功能，点选功能开启时用户可点击图形进行单选和多选，选中图形后会自动将其所属图层设置为编辑状态
      this.mapObj.globalMapEditor?.setSelectable(false)
      // 开启或关闭吸附功能，吸附功能开启时绘制或编辑图形会自动吸附到临近的点或线段上
      this.mapObj.globalMapEditor?.setSnappable(false)
      this.mapObj.globalMapEditor?.setActionMode(TMap.tools.constants.EDITOR_ACTION.NONE)
    },
    // 初始化编辑图层
    async initEditor() {
      const shapeOptions = [
        {
          type: OVER_LAY_TYPE.POLYGON,
          id: OVER_LAY_TYPE.POLYGON
        },
        {
          type: OVER_LAY_TYPE.CIRCLE,
          id: OVER_LAY_TYPE.CIRCLE
        },
        {
          type: OVER_LAY_TYPE.ELLIPSE,
          id: OVER_LAY_TYPE.ELLIPSE
        },
        {
          type: OVER_LAY_TYPE.RECTANGLE,
          id: OVER_LAY_TYPE.RECTANGLE
        },
        {
          type: OVER_LAY_TYPE.DRAGMARKER,
          id: OVER_LAY_TYPE.DRAGMARKER, // 可编辑图层
          options: {
            selectedStyleId: this.selectedStyleName
          }
        }
      ]
      const { mapEditor, overlayList } = await initMapEditor(this.mapObj.globalMap, shapeOptions)
      this.mapObj.globalMapEditor = mapEditor
      this.mapObj.globalMapEditorPolygon = overlayList[0].overlay
      this.mapObj.globalMapEditorCircle = overlayList[1].overlay
      this.mapObj.globalMapEditorEllipse = overlayList[2].overlay
      this.mapObj.globalMapEditorRectangle = overlayList[3].overlay
      this.mapObj.globalMapEditorMarker = overlayList[4].overlay
      this.mapObj.globalMapEditor.on('draw_complete', async geometry => {
        this.$emit('draw-complete')
      })
      this.mapObj.globalMapEditor.on('delete_complete', geometry => {
        this.$emit('delete-complete', geometry)
      })
      this.mapObj.globalMapEditor.on('adjust_complete', geometry => {
        this.$emit('adjust-complete', geometry)
      })
      this.mapObj.globalMapEditor.on('select', geometry => {
        this.$emit('select-complete')
      })
      this.mapObj.globalMapEditorMarker.on('click', this.clickEditorMarker)
    },
    clickEditorMarker(e) {
      this.$emit('click-editor-marker', e)
    },
    calculateBounds(bounds) {
      if (bounds) {
        const { _ne: ne, _sw: sw } = bounds
        const nw = { lat: ne.lat, lng: sw.lng }
        const se = { lat: sw.lat, lng: ne.lng }
        return [
          [nw.lng, nw.lat],
          [ne.lng, ne.lat],
          [se.lng, se.lat],
          [sw.lng, sw.lat],
          [nw.lng, nw.lat]
        ]
      }
    },
    handleBoundsChange(event) {
      this.$emit('change-bounds', this.calculateBounds(this.mapObj.globalMap?.getBounds()))
    },
    setActiveOverlay(drawOverlay) {
      this.mapObj.globalMapEditor.setActiveOverlay(drawOverlay)
    },
    addGeo(markerList, key = 'globalMapEditorMarker') {
      addGeo(this.mapObj?.[key], markerList)
    },
    removeGeo(markerList, key = 'globalMapEditorMarker') {
      removeGeo(this.mapObj?.[key], markerList)
    },
    goCenter(position) {
      goCenter(position, this.mapObj.globalMap)
    }
    // removeGeo(geoIds) {
    //   removeGeo(mapObj.globalMarker, removeIds)
    // }
  }
}
</script>
<style lang="scss" scoped>
.map-overview {
  user-select: none;
  height: 100%;
  width: 100%;
  z-index: 0;
}
</style>
