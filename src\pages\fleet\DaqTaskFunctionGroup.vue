<template>
  <el-card shadow="always" class="bs-tag-group-card">
    <template #header>
      <div class="bs-tag-group-card-header">
        <span>{{ tagGroup.name }} - {{ tagGroup.code }}</span>
        <el-dropdown @command="handleTagGroupCommand($event,tagGroup)">
          <span class="el-dropdown-link" id="operation-button">
            {{ $t('操作') }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="bs-tag-group-dropdown-menu">
              <el-dropdown-item command="addSubGroup" v-if="!sub" id="el-icon-plus">
                <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                {{ $t('新增') }}
              </el-dropdown-item>
              <el-dropdown-item command="edit" id="el-icon-edit">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>{{ $t('编辑') }}
              </el-dropdown-item>
              <el-dropdown-item command="singleRemove" id="el-icon-delete">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>{{ $t('删除') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
    <div class="bs-tag-group-card-body">
      <el-button size="small" @click="handleTagGroupCommand('addTag', tagGroup,tagGroup.items)" id="tag-add"
                 v-if="!tagGroup.switchable&& tagGroup.items.length===0&&!sub">
        <ltw-icon icon-code="el-icon-plus"></ltw-icon>
        {{ '新增' }}
      </el-button>
      <!-- </div> -->
      <div class="bs-tag-tontainer" v-if="sub || (tagGroup.items && tagGroup.items.length > 0)">
        <draggable
            :list="tagGroup.items || []"
            :component-data="{ name: 'fade' }"
            ghost-class="ghost-tag"
            item-key="id"
            @change="handleTagChange"
            sub
        >
          <template #item="{ element }">
            <el-button
                @click="handleTagCick(element, tagGroup)"
                size="small"
                plain
            >
              <el-row
              >{{ element.name }}
              </el-row>
            </el-button>
          </template>
        </draggable>
        <el-button size="small" @click="handleTagGroupCommand('addTag', tagGroup,tagGroup.items)" id="tag-add">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          {{ '新增' }}
        </el-button>
      </div>
      <draggable
        v-if="tagGroup.children && tagGroup.children.length > 0"
        :list="tagGroup.children"
        tag="div"
        :component-data="{ name: 'fade', class: 'bs-tag-sub-group-container' }"
        item-key="id"
        @change="handleSubGroupChange"
        ghost-class="ghost"
        group="tag-group"
        filter=".el-card__body,.el-dropdown,.disable-dragging"
      >
        <template #item="{ element }">
          <daq-task-function-group
            :tag-group="element"
            @command="handleTagGroupCommand"
            @update-children="handleUpdateChildren($event, element)"
            @update-tag-list="handleUpdateTagList($event, element)"
            class="bs-tag-sub-group-card"
            sub
          ></daq-task-function-group>
        </template>
      </draggable>
    </div>
  </el-card>
</template>

<script>
import DaqTaskFunctionGroup from '@/pages/fleet/DaqTaskFunctionGroup.vue'
import draggable from 'vuedraggable'
import { treeDaqTaskFunctionGroup,reorderFunctionGroup } from '@/apis/fleet/daq-task-function-group'
import { listDaqTaskFunctionItem,reorderFunctionItem } from '@/apis/fleet/daq-task-function-item'
import { getLocale } from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'

export default {
  components: { LtwIcon, DaqTaskFunctionGroup, draggable },
  name: 'DaqTaskFunctionGroup',
  props: {
    tagGroup: {
      type: Object,
      default: () => {
        return {}
      }
    },
    sub: {
      type: Boolean,
      default: false
    }
  },
  emits: ['command', 'updateChildren', 'updateTagList'],
  data() {
    return {
      getLocale: getLocale(),
      pwidth: 120,
      maxWidth: 100
    }
  },
  created() {

  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    handleTagGroupCommand(command, row,group) {
      if (!row) {
        row = this.tagGroup
      }
      this.$emit('command', command, row, group)
    },
    checkTagType(tag) {
      if (!tag.enabled) {
        return 'info'
      }
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : 'primary'
      }
      return 'info'
    },
    handleSubGroupChange(e) {
      if (e.moved) {
        this.handleGroupMove(e.moved)
      }
      if (e.added) {
        this.handleGroupAdd(e.added)
      }
    },
    handleGroupMove(moved) {
      let element = moved.element
      let preSortNum
      let nextSortNum
      let newIndex = moved.newIndex
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.tagGroup.children[newIndex - 1].sortNum
      }
      if (newIndex === this.tagGroup.children.length - 1) {
        nextSortNum = undefined
      } else {
        nextSortNum = this.tagGroup.children[newIndex + 1].sortNum
      }
      this.sortSubFunctionGroup(element, preSortNum, nextSortNum)
    },
    handleGroupAdd(added) {
      let element = added.element
      let preSortNum
      let nextSortNum
      let newIndex = added.newIndex
      let newParentId = ''
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.tagGroup.children[newIndex - 1].sortNum
        newParentId = this.tagGroup.children[newIndex - 1].parentId
      }
      if (newIndex === this.tagGroup.children.length - 1) {
        nextSortNum = undefined
      } else {
        newParentId = this.tagGroup.children[newIndex + 1].parentId
        nextSortNum = this.tagGroup.children[newIndex + 1].sortNum
      }
      this.sortSubFunctionGroup(element, preSortNum, nextSortNum, newParentId)
    },
    sortSubFunctionGroup(element, preSortNum, nextSortNum, newParentId) {
      reorderFunctionGroup({
        id: element.id,
        preSortNum,
        nextSortNum,
        newParentId
      }).then(res => {
        let newSortNum = res.data
        if (!nextSortNum) {
          element.sortNum = newSortNum
        } else {
          if (!preSortNum) {
            preSortNum = 0
          }
          if (preSortNum < newSortNum && newSortNum < nextSortNum) {
            element.sortNum = newSortNum
          }
          // else {
          //   this.querySubGroup(element.parentId)
          // }
        }
      })
    },
    // querySubGroup(parentId) {
    //   listDaqTaskFunctionGroup({parentId: parentId}).then(res => {
    //     this.$emit('updateChildren', res.data)
    //   })
    // },
    handleUpdateChildren(children, element) {
      element.children = children
    },
    handleTagChange({ moved }) {
      let element = moved.element
      let preSortNum
      let nextSortNum
      let newIndex = moved.newIndex
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.tagGroup.items[newIndex - 1].sortNum
      }
      if (newIndex === this.tagGroup.items.length - 1) {
        nextSortNum = undefined
      } else {
        nextSortNum = this.tagGroup.items[newIndex + 1].sortNum
      }
      this.sortFunctionItem(element, preSortNum, nextSortNum)
    },
    sortFunctionItem(element, preSortNum, nextSortNum) {
      reorderFunctionItem({ id: element.id, preSortNum, nextSortNum }).then(res => {
        let newSortNum = res.data
        if (!nextSortNum) {
          element.sortNum = newSortNum
        } else {
          if (!preSortNum) {
            preSortNum = 0
          }
          if (preSortNum < newSortNum && newSortNum < nextSortNum) {
            element.sortNum = newSortNum
          } else {
            this.queryTag(element.groupId)
          }
        }
      })
    },
    queryTag(groupId) {
      listDaqTaskFunctionItem({ groupId:groupId }).then(res => {
        this.$emit('updateTagList', res.data)
      })
    },
    handleUpdateTagList(items, element) {
      element.items = items
    },
    handleTagCick(tag, group) {
      this.handleTagGroupCommand('viewTag', {tag, group})
    },
    sourceList(element) {
      let arr = element.split(',')
      return arr
    },
    adjustPopoverWidth(element) {
      const rows = element.split(',')
      let maxCharCount = 0
      rows.forEach(row => {
        const rowText = row.split(':')[0] // 获取每一行的文本内容
        const charCount = this.getCharacterCount(rowText) // 获取文本的字符个数

        if (charCount > maxCharCount) {
          maxCharCount = charCount // 更新最大字符个数
        }
      })
      const charWidth = 12
      this.maxWidth = maxCharCount * charWidth +20+ 'px'
      this.pwidth = maxCharCount * charWidth + 100 > 150 ? maxCharCount * charWidth + 100 : 150
    },
    getCharacterCount(text) {  
    const canvas = document.createElement('canvas');  
    const context = canvas.getContext('2d');  
    context.font = '12px Arial'; // 设置字体样式和大小，根据实际情况调整  
      const metrics = context.measureText(text);  
    return metrics.width / 12; // 返回字符宽度除以字体大小，根据实际情况调整  
  }  

  }
}
</script>
<style lang="scss">
.info.el-popover {
  background: #f4f4f5 !important;
}

.danger.el-popover {
  background: #fef0f0 !important;
}
.success.el-popover {
  background-color: #f0f9eb !important;
}

.primary.el-popover {
  background-color: #ecf5ff !important;
}

.warning.el-popover {
  background-color: #fdf6ec;
}
</style>
<style scoped lang="scss">
.bs-tag-group-card {
  margin-top: 10px;
  .bs-tag-group-card-header {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    .el-dropdown {
      width: 80px;
      font-size: 12px;
      .el-dropdown-link {
        cursor: pointer;
        color: var(--el-color-primary);
        .el-icon--right {
          font-size: 12px;
          vertical-align: middle;
        }
      }
    }
  }
  .bs-tag-group-card-body {
    min-height: 50px;
    .bs-tag-sub-group-container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .bs-tag-group-card {
        width: 30%;
        margin-right: 4%;
      }
      :nth-child(3n + 1).bs-tag-group-card {
        margin-left: 1%;
      }
      :nth-child(3n).bs-tag-group-card {
        margin-right: 1%;
      }
      .ghost {
        background-color: rgba(241, 239, 239, 0.8);
      }
      .fade-move {
        transition: transform 0.5s;
      }
    }
    .bs-tag-tontainer {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      // .el-tag {
      //     margin-right: 10px;
      //     margin-bottom: 5px;
      //     cursor: pointer;
      // }

      .el-button {
        margin-bottom: 5px;
        margin-right: 5px;
        margin-left: 0px;
      }
      .el-button.tag-active {
        background: var(--el-button-bg-color);
        border-color: var(--el-button-border-color);
        color: var(--el-button-text-color);
      }
      .el-button.el-button--info.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #f4f4f5;
        border-color: #c8c9cc;
      }
      .el-button.el-button--danger.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #fef0f0;
        border-color: #fbc4c4;
      }
      .el-button.el-button--primary.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #ecf5ff;
        border-color: #b3d8ff;
      }
      .el-button.el-button--warning.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #fdf6ec;
        border-color: #f5dab1;
      }
      .el-button.el-button--success.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #f0f9eb;
        border-color: #c2e7b0;
      }

      .ghost {
        background-color: rgba(241, 239, 239, 0.8);
      }

      .fade-move {
        transition: transform 0.5s;
      }
    }
  }
}
</style>
