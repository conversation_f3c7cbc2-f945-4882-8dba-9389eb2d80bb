<template></template>

<script>
import util from '@/plugins/util'
import { setLocale } from '@/plugins/util'
export default {
  name: 'token',
  data() {
    return {}
  },
  created() {
    if (this.$route?.query?.token) {
      this.getToken()
    } else {
      util.toLogin()
    }
    if (this.$route?.query?.locale) {
      setLocale(this.$route?.query?.locale)
    }
    if (this.$route?.query?.loginTheme) {
      localStorage.setItem('login-theme', this.$route?.query?.loginTheme)
    }
  },
  methods: {
    getToken() {
      let token = this.$route?.query?.token
      if (token) {
        util.setToken(token)
        this.$router.push(
          decodeURIComponent(this.$route?.query?.redirect || '/')
        )
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
