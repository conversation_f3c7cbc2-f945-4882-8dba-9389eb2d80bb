import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmImageSample = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples',
    data,
    params
  })
export const updateDmImageSample = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples',
    data,
    params
  })
export const deleteDmImageSample = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples',
    params
  })
export const listDmImageSample = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples',
    params
  })
export const listDmImageSampleSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples/selections',
    params
  })
export const pageDmImageSample = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples/page',
    params
  })
export const getDmImageSample = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dm/dm_image_samples/' + id })
