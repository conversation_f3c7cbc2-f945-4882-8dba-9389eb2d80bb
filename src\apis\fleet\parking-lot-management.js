import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveMdParkingLots = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots',
    data,
    params
  })
export const updateMdParkingLots = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots',
    data,
    params
  })
export const deleteMdParkingLots = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots',
    params
  })
export const pageMdParkingLots = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/page',
    data,
    params
  })
export const getMdParkingLots = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/' + id })

export const listParkingLotFieldWithItems = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lot_fields/listParkingLotFieldWithItems',
    params
  })
export const listMdParkingLots = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/list',
    data,
    params
  })
export const listMdParkingLotsSimple = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/simple/list',
    data,
    params
  })
export const listMdParkingLotsSimpleGeo = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/simple/listWithGeo',
    data,
    params
  })

export const listFtmParkingLotSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/selections',
    params
  })
export const getFtmParkingLotFieldItems = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_field_items',
    params
  })

// 停车场锁定/解锁接口
export const lockMdParkingLot = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/md/parking_lots/lock',
    data,
    params
  })
