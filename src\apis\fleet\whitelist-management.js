/**
 * 车型功能白名单表相关API
 */
import { httpGet, httpPost, httpPut, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

const BASE_URL = GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_software_whitelists'

/**
 * 分页查询车型功能白名单车型分页视图（主表接口）
 * @param {Object} params 查询参数
 * @param {string} params.variantCode 车型编码
 * @param {string} params.functionName 功能名称
 * @param {string} params.version 版本号
 * @param {number} params.pageNum 页码，从1开始
 * @param {number} params.pageSize 每页大小
 * @param {string} params.orderBy 排序字段
 * @param {string} params.orderType 排序方式：ASC/DESC
 * @returns {Promise} 返回车型列表
 */
export function pageWhiteListVariant(params) {
  return httpGet({
    url: `${BASE_URL}/pageWhiteListVariant`,
    params
  })
}

/**
 * 分页查询车型功能白名单表视图（子表接口）
 * @param {Object} params 查询参数
 * @param {string} params.variantCode 车型编码
 * @param {string} params.functionName 功能名称
 * @param {string} params.version 版本号
 * @param {number} params.pageNum 页码，从1开始
 * @param {number} params.pageSize 每页大小
 * @param {string} params.orderBy 排序字段
 * @param {string} params.orderType 排序方式：ASC/DESC
 * @returns {Promise} 返回白名单详细列表
 */
export function pageWhiteList(params) {
  return httpGet({
    url: `${BASE_URL}/page`,
    params
  })
}

/**
 * 根据JFrog properties查询白名单zip包
 * @param {Object} data 查询条件
 * @param {string} data.variant 车型编码
 * @param {string} data.function 功能名称
 * @param {string} data.version 版本号
 * @param {string} data.platform 平台类型
 * @returns {Promise} 返回zip包列表
 */
export function searchZipPackagesByProperties(data) {
  return httpPost({
    url: `${BASE_URL}/search_zip_packages_by_properties`,
    data
  })
}


/**
 * 解析zip包中的topic映射关系
 * @param {Object} params 查询参数
 * @param {string} params.zipPath ZIP包在JFrog中的路径
 * @returns {Promise} 返回topic信息列表
 */
export function parseZipTopics(params) {
  return httpGet({
    url: `${BASE_URL}/parse_zip_topics`,
    params
  })
}

/**
 * 解析上传的HPP文件中的mapping关系
 * @param {FormData} formData 包含HPP文件的表单数据
 * @returns {Promise} 返回topic映射信息列表
 */
export function parseHppMappings(formData) {
  return httpPost({
    url: `${BASE_URL}/parse_hpp_mappings`,
    data: formData
  })
}

/**
 * 解析上传的interface yaml文件
 * @param {FormData} formData 包含YAML文件的表单数据
 * @returns {Promise} 返回Master Topic配置列表
 */
export function parseInterfaceYaml(formData) {
  return httpPost({
    url: `${BASE_URL}/parse_interface_yaml`,
    data: formData
  })
}

/**
 * 对比两个白名单JSON文件内容差异
 * @param {string} currentFileId 当前白名单文件ID
 * @param {string} toCompareFileId 待对比白名单文件ID
 * @returns {Promise} 返回差异信息
 */
export function compareWhitelistDifference(currentFileId, toCompareFileId) {
  return httpGet({
    url: `${BASE_URL}/compare_whitelist_difference`,
    params: {
      currentFileId,
      toCompareFileId
    }
  })
}

/**
 * 新增白名单记录
 * @param {Object} data 白名单数据
 * @returns {Promise}
 */
export function createWhitelist(data) {
  return httpPost({
    url: `${BASE_URL}`,
    data
  })
}

/**
 * 更新白名单记录
 * @param {string} id 记录ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export function updateWhitelist(id, data) {
  return httpPut({
    url: `${BASE_URL}/${id}`,
    data
  })
}

/**
 * 删除白名单记录
 * @param {string} id 记录ID
 * @returns {Promise}
 */
export function deleteWhitelist(id) {
  return httpDelete({
    url: `${BASE_URL}/${id}`
  })
}

/**
 * 保存并上传软件白名单
 * @param {Object} data 白名单数据
 * @param {string} data.description 白名单描述信息
 * @param {string} data.fileName 白名单JSON文件名
 * @param {string} data.functionName 功能名称
 * @param {Array} data.regexServices 正则规则列表
 * @param {Array} data.topicInfoList Topic配置列表
 * @param {string} data.variantCode 车型编码
 * @param {string} data.version 版本号
 * @returns {Promise} 返回保存结果
 */
export function saveAndUploadWhitelist(data) {
  return httpPost({
    url: `${BASE_URL}/saveAndUploadSoftwareWhiteList`,
    data
  })
}
