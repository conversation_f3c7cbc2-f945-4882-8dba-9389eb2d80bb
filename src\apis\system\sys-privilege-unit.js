import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysPrivilegeUnit = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units', data, params})
export const updateSysPrivilegeUnit = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units', data, params})
export const deleteSysPrivilegeUnit = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units', params})
export const listSysPrivilegeUnit = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units', params})
export const listSysPrivilegeUnitSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units/selections', params})
export const pageSysPrivilegeUnit = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units/page', params})
export const getSysPrivilegeUnit = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_units/' + id})
