<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            :placeholder="$t('请输入关键字')"
            v-model="queryParam.key"
            clearable
            @clear="refresh"
            id="ltw-input"
          >
            <template #append>
              <el-button @click="refresh" id="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
            :id="item.buttonIconCode"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary" id="batch-operation">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                  :id="item.buttonIconCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="ltw-filter-container">
          <el-button type="primary" @click="filterDialogVisible = true" id="filter-button">
            {{ $t('筛选') }}
          </el-button>
        </div>
      </div>
      <el-table :data="pageData.records" :row-key="getRowKeys" @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column
          header-align="left"
          align="left"
          type="selection"
          width="55"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('车辆')" width="150">
          <template #default="scope">
            <el-link type="primary">{{ scope.row.vin }}</el-link>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('任务')" width="150" prop="taskCode">
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('时间')" width="350">
          <template #default="scope">
            <template v-if="scope.row.startTime">
              <el-tag>{{ scope.row.startTime }}</el-tag>
              &nbsp;&nbsp;To&nbsp;&nbsp;
              <el-tag> {{ scope.row.endTime }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('标签')" width="200">
          <template #default="scope">
            <el-tag :type="checkTagType(scope.row)"
              >{{ locale === 'zh' ? scope.row.tagNameCn : scope.row.tagName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('标签组')" width="150">
          <template #default="scope">
            <el-link type="primary">{{ scope.row.tagGroupName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="180">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item.buttonCode, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      ></el-pagination>
    </el-card>
    <add-tag-record ref="AddTagRecord" @reload="query"></add-tag-record>

    <el-dialog
      v-model="filterDialogVisible"
      @close="dialogClosed"
      @open="dialogOpened"
      width="40%"
      draggable
      class="filter-dialog"
    >
      <el-form :model="queryParam" label-width="50px" ref="filterFormRef">
        <el-form-item :label="$t('车辆')" class="vehicle-form-item">
          <bs-vehicle-selection
            :data="vehicleList"
            :auto-load="false"
            modelCode="vin"
            v-model="queryParam.vinList"
            ref="vehicleSelectionRef"
            clearable
            filterable
            multiple
            @clear="queryParam.vinList = undefined"
            collapse-tags
          ></bs-vehicle-selection>
        </el-form-item>
        <el-form-item :label="$t('任务')" class="task-form-item">
          <el-select
            v-model="queryParam.taskCodeList"
            multiple
            collapse-tags
            clearable
            filterable
            :teleported="false"
            @clear="queryParam.taskCodeList = undefined"
            popper-class="task-selector"
          >
            <el-option
              v-for="item in taskCodeList"
              :key="item.id"
              :label="item.code"
              :value="item.code"
              id="task-list"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('时间')" class="time-form-item">
          <el-date-picker
            v-model="queryParam.time"
            type="datetimerange"
            range-separator="To"
            start-placeholder="Start dateTime"
            end-placeholder="End dateTime"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item :label="$t('标签')" class="ltw-filter-tag-container" prop="tagList">
          <div>
            <el-button @click="tagDistribute" class="ltw-filter-tag-add" id="el-icon-plus">
              <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              {{ $t('新增') }}
            </el-button>
            <el-button @click="tagList = []" class="ltw-filter-tag-add" id="el-icon-close">
              <ltw-icon icon-code="el-icon-close"></ltw-icon>
              {{ $t('重置') }}
            </el-button>
          </div>
          <div>
            <el-tag
              v-for="(tag, index) in tagList"
              :key="index"
              :type="checkTagType(tag)"
              closable
              @close="handleClose(tag)"
              style="margin: 3px 5px"
            >
              {{ tag.groupName }}:{{ tag.name }}
            </el-tag>
            <bs-tag-group-drawer
              :drawerVisible="tagDistributeDrawerVisible"
              :rowTagList="rowTagList"
              @drawerClick="confirmDistributeTags"
              :matchFlag="true"
            ></bs-tag-group-drawer>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="filter" id="confirm">{{ $t('确定') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { getLocale, checkTagType, showToast, showConfirmToast } from '@/plugins/util'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import { listDaqReqDetailSelection } from '@/apis/data-collect/vt-daq-task'
import {
  saveVtTagRecord,
  updateVtTagRecord,
  deleteVtTagRecord,
  pageVtTagRecord,
  getVtTagRecord
} from '@/apis/data-collect/vt-tag-record'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import AddTagRecord from '@/pages/dataCollect/dialog/AddTagRecord.vue'

const defaultFormData = {}
export default {
  name: 'VtTagRecord',
  components: {
    BsTagGroupDrawer,
    BsVehicleSelection,
    AddTagRecord
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      checkTagType: checkTagType,
      locale: getLocale(),
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      formData: Object.assign({}, defaultFormData),
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      //filter
      filterDialogVisible: false,
      vehicleList: [],
      taskCodeList: [],
      rowTagList: [],
      tagDistributeDrawerVisible: false,
      tagList: undefined
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageVtTagRecord(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加标签记录'
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveVtTagRecord(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateVtTagRecord(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.$refs.AddTagRecord.show({ type: 'edit', data: { id: row.id } })
      // this.dialogTitle = '修改标签记录'
      // this.dialogStatus = 'edit'
      // getVtTagRecord(row.id).then(res => {
      //   this.formData = res.data
      //   this.dialogVisible = true
      // })
    },
    view(row) {
      this.$refs.AddTagRecord.show({ type: 'view', data: { id: row.id } })
      // this.dialogTitle = '查看标签记录'
      // this.dialogStatus = 'view'
      // getVtTagRecord(row.id).then(res => {
      //   this.formData = res.data
      //   let arr = []
      //   arr.push(res.data.startTime, res.data.endTime)
      //   this.formData.time = arr
      //   this.dialogVisible = true
      // })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        showToast('请先选择数据再执行批量操作', 'warning')
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteVtTagRecord(param).then(() => {
          this.$refs.tableRef.clearSelection()
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    listVehicle() {
      listBsVehicle().then(res => {
        this.vehicleList = res.data
      })
    },
    listTaskCode() {
      listDaqReqDetailSelection().then(res => {
        this.taskCodeList = res.data
      })
    },
    dialogOpened() {
      if (this.queryParam.startTime) {
        this.queryParam.time = []
        this.queryParam.time.push(this.queryParam.startTime, this.queryParam.endTime)
      }
      if (!Array.isArray(this.queryParam.vinList) && this.queryParam.vinList?.length !== 0) {
        this.queryParam.vinList = this.queryParam.vinList?.split(',')
      }
      if (!Array.isArray(this.queryParam.taskCodeList) && this.queryParam.taskCodeList?.length !== 0) {
        this.queryParam.taskCodeList = this.queryParam.taskCodeList?.split(',')
      }
      if (!Array.isArray(this.queryParam.tagIdList) && this.queryParam.tagIdList?.length !== 0) {
        this.queryParam.tagIdList = this.queryParam.tagIdList?.split(',')
      }
      this.listTaskCode()
      this.listVehicle()
    },
    dialogClosed() {
      this.selectedData = []
      this.filterDialogVisible = false
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.formData = Object.assign({}, defaultFormData)
    },
    //filter
    tagDistribute() {
      this.tagDistributeDrawerVisible = true
      this.rowTagList = this.tagList || []
    },
    confirmDistributeTags(data) {
      this.queryParam.tagSingleMatch = data?.isOr?.value
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
      }
      if (this.tagList?.length > 0) {
        this.queryParam.tagIdList = []
        this.tagList.forEach(item => {
          this.queryParam.tagIdList.push(item.id)
        })
        this.queryParam.tagIdList = this.queryParam.tagIdList.join(',')
      } else {
        this.queryParam.tagIdList = undefined
        this.queryParam.tagSingleMatch = undefined
      }
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    handleClose(tag) {
      this.tagList = this.tagList.filter(item => {
        return item !== tag
      })
      this.rowTagList = this.tagList
      if (this.tagList?.length > 0) {
        this.queryParam.tagIdList = []
        this.tagList.forEach(item => {
          this.queryParam.tagIdList.push(item.id)
        })
        this.queryParam.tagIdList = this.queryParam.tagIdList.join(',')
      }
    },
    filter() {
      this.queryParam.vinList = this.queryParam.vinList && this.queryParam.vinList?.join(',')
      this.queryParam.taskCodeList = this.queryParam.taskCodeList && this.queryParam.taskCodeList?.join(',')
      this.queryParam.startTime = this.queryParam.time && this.queryParam.time[0]
      this.queryParam.endTime = this.queryParam.time && this.queryParam.time[1]
      if (this.tagList?.length === 0) {
        this.queryParam.tagIdList = undefined
      }
      this.filterDialogVisible = false
      delete this.queryParam.time
      this.queryParam.current = 1
      this.query()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  background: rgb(250, 249, 249);
  color: #000;
}

.ltw-filter-container {
  margin-right: 10px;
}

:deep(.el-descriptions__title) {
  padding-top: 20px;
}

.twoColumns {
  display: flex;
  justify-content: space-between;

  .el-form-item {
    flex: 1;
    width: 100%;
  }

  :deep(.el-date-editor) {
    --el-date-editor-width: 100%;
  }
}

.el-dialog {
  .el-dialog__body .json-body {
    position: relative;

    .jv-container {
      height: 50vh;
      overflow: auto;
      position: static;

      .jv-tooltip.right {
        top: 0px;
        right: 28px;
      }
    }

    .el-link {
      position: absolute;
      right: 80px;
      top: 6px;
    }
  }

  .el-dialog__body .detail-dialog-content {
    max-height: 60vh;
    overflow: auto;
    overflow-x: hidden;
    word-break: normal;

    :deep(.ltw-input__inner) {
      tr {
        display: flex;
      }
    }

    :deep(.el-descriptions__table .el-descriptions__label) {
      width: 220px;
      white-space: nowrap;
    }

    :deep(.el-descriptions__table .el-descriptions__content) {
      width: calc(100% - 220px);
    }

    :deep(.el-descriptions__table .el-descriptions__content .el-descriptions__cell) {
      width: 100%;
    }
  }
}

.ltw-filter-tag-container {
  .el-tag {
    margin: 5px 3px;
  }

  .ltw-filter-tag-add {
    min-height: 32px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 30px;
  }

  .ltw-filter-task-container {
    display: flex;
  }
}

:deep(.el-range-editor.ltw-input__inner) {
  width: 100%;
}
</style>
