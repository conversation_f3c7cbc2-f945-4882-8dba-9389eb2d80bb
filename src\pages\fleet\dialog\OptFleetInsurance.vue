<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="70%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <FtmVehicleInsurance :vin="vin" ref="FtmVehicleInsurance"/>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="dialogVisible = false"
          v-if="dialogStatus === 'view'"
          id="view-cancel"
          >{{ $t('关闭') }}</el-button
        >
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{
            $t('取消')
          }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{
            $t('保存')
          }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import FtmVehicleInsurance from '@/pages/fleet/FtmVehicleInsurance'
export default {
  name: 'OptFleetInsurance',
  emits: ['reload'],
  components: {
    FtmVehicleInsurance,
  },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      vin: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  created() {
    // this.listInstallPostionList();
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.vin = row.vin
      switch (row.type) {
        case 'view':
          this.dialogTitle = this.$t('车辆保险')
          // this.query(row.vin)
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {},
    query(vin) {
      this.FtmVehicleInsurance({vin})
    }
  }
}
</script>

<style scoped lang="scss">
</style>
