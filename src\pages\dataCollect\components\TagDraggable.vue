<template>
  <div class="draggable-container">
    <div v-show="checkedTagList?.length > 0" style="margin-bottom: 20px">
      <VueDraggable v-model="listData" @update="changeTreeData" :disabled="formReadonly">
        <el-card v-for="item in listData" :key="item.id" shadow="always" class="bs-tag-group-card">
          <template #header>
            <div class="bs-tag-group-card-header">
              <span>{{ item.name }}</span>
            </div>
            <ltw-icon
              :icon-code="item.isCollapseSelf ? 'el-icon-arrowDown' : 'el-icon-arrowRight'"
              @click="item.isCollapseSelf = !item.isCollapseSelf"
            />
          </template>
          <div class="bs-tag-group-card-body" v-show="item.isCollapseSelf">
            <VueDraggable v-model="item.children" @update="changeTreeData" :disabled="formReadonly">
              <el-card v-for="subItem in item.children" :key="subItem.id" class="card-item">
                <template #header>
                  <div class="bs-tag-group-card-header">
                    <span>{{ subItem.name }}</span>
                  </div>
                </template>
                <template v-if="subItem.children?.length">
                  <VueDraggable v-model="subItem.children" @update="changeTreeData" :disabled="formReadonly">
                    <el-card v-for="it in subItem.children" :key="it.id">
                      <template #header>
                        <div class="bs-tag-group-card-header">
                          <span>{{ it.name }}</span>
                        </div>
                      </template>
                      <template v-if="it.children?.length"></template>
                      <template v-else>
                        <VueDraggable
                          ref="el"
                          v-model="it.tagList"
                          :options="{ direction: 'horizontal' }"
                          class="draggable"
                          @update="changeTreeData"
                          :disabled="formReadonly"
                        >
                          <div v-for="(tag, tagIndex) in it.tagList" :key="tag">
                            <el-tag :type="checkTagType(tag)" class="tag-item">
                              {{ tag['nameCn'] }}
                              <ltw-icon
                                class="tag-attribute"
                                icon-code="el-icon-microphone"
                                v-if="tag.supportVoice"
                              ></ltw-icon>
                              <ltw-icon
                                class="tag-attribute"
                                icon-code="el-icon-pointer"
                                v-if="tag.supportTrigger"
                              ></ltw-icon>
                              <div
                                class="tag-attribute"
                                v-if="tag.type === 'transient' && (tag.previousDuration === 0 || tag.previousDuration)"
                              >
                                {{ tag.previousDuration }}
                                <span class="tag-unit">秒</span>
                              </div>
                              <div
                                v-if="
                                  tag.type === 'transient' &&
                                  (tag.previousDuration === 0 ||
                                    tag.previousDuration ||
                                    tag.followingDuration === 0 ||
                                    tag.followingDuration)
                                "
                              >
                                ~
                              </div>
                              <div
                                class="tag-attribute"
                                v-if="
                                  tag.type === 'transient' && (tag.followingDuration === 0 || tag.followingDuration)
                                "
                              >
                                {{ tag.followingDuration }}
                                <span class="tag-unit">秒</span>
                              </div>
                            </el-tag>
                          </div>
                        </VueDraggable>
                      </template>
                    </el-card>
                  </VueDraggable>
                </template>

                <template v-else>
                  <VueDraggable
                    ref="el"
                    v-model="subItem.tagList"
                    :options="{ direction: 'horizontal' }"
                    class="draggable"
                    :disabled="formReadonly"
                  >
                    <div v-for="(tag, tagIndex) in subItem.tagList" :key="tag">
                      <el-tag :type="checkTagType(tag)" class="tag-item">
                        {{ tag['nameCn'] }}
                        <ltw-icon
                          class="tag-attribute"
                          icon-code="el-icon-microphone"
                          v-if="tag.supportVoice"
                        ></ltw-icon>
                        <ltw-icon
                          class="tag-attribute"
                          icon-code="el-icon-pointer"
                          v-if="tag.supportTrigger"
                        ></ltw-icon>
                        <div
                          class="tag-attribute"
                          v-if="tag.type === 'transient' && (tag.previousDuration === 0 || tag.previousDuration)"
                        >
                          {{ tag.previousDuration }}
                          <span class="tag-unit">秒</span>
                        </div>
                        <div
                          v-if="
                            tag.type === 'transient' &&
                            (tag.previousDuration === 0 ||
                              tag.previousDuration ||
                              tag.followingDuration === 0 ||
                              tag.followingDuration)
                          "
                        >
                          ~
                        </div>
                        <div
                          class="tag-attribute"
                          v-if="tag.type === 'transient' && (tag.followingDuration === 0 || tag.followingDuration)"
                        >
                          {{ tag.followingDuration }}
                          <span class="tag-unit">秒</span>
                        </div>
                      </el-tag>
                    </div>
                  </VueDraggable>
                </template>
              </el-card>
            </VueDraggable>
          </div>
        </el-card>
      </VueDraggable>
    </div>
    <el-empty v-show="checkedTagList?.length == 0" description="暂无选择标签"></el-empty>
  </div>
</template>

<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import { VueDraggable } from 'vue-draggable-plus'

export default {
  components: {
    LtwIcon,
    VueDraggable
  },
  props: {
    checkedTagList: {
      type: Array,
      default: []
    },
    dialogStatus: {
      type: String,
      default: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  watch: {
    checkedTagList: {
      handler(newval, old) {
        if (newval.length > 0) {
          this.listData = newval.map(item => {
            return {
              ...item,
              isCollapseSelf: true
            }
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      activeNames: '1',
      listData: '',
      treeData: ''
    }
  },
  methods: {
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    changeTreeData() {
      this.$emit('updateTreeData', this.listData)
    }
  }
}
</script>
<style lang="scss">
.draggable-container {
  .el-card__header {
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: space-between;

    .collapse-icon-right::after {
      content: '\e6e0';
    }

    .collapse-icon-down::after {
      content: '\e6df';
    }
  }
}
</style>
<style lang="scss" scoped>
.draggable-container {
  .bs-tag-group-card {
    margin-bottom: 10px;
  }

  .card-item {
    margin-bottom: 10px;
  }

  .draggable {
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;

    .ltw-icon :deep(.el-icon) {
      font-size: 12px;
    }
  }

  .tag-item {
    margin: 0 5px 5px 0;

    :deep(.el-tag__content) {
      display: flex;
      align-items: center;

      .tag-attribute {
        margin-left: 4px;
      }
    }
  }
}
</style>
