<template>
  <el-drawer
    custom-class="drawer-container"
    :title="$t('标签选择')"
    v-model="visible"
    direction="rtl"
    size="80%"
    append-to-body
    @close="dialogClosed"
  >
    <div class="drawer-body left">
      <div>标签类别</div>
      <el-scrollbar style="height: calc(100% - 21px)">
        <div class="tag-checkbox">
          <bs-tag-classification-selection
            v-model="currentClassificationList"
            :tabs-data="tabsData"
            :access-role-type="accessRoleType"
            @handle-tab-click="handleTabClick"
            @handleClassificationChecked="handleClassificationChange"
            ref="classificationRef"
          ></bs-tag-classification-selection>
        </div>
      </el-scrollbar>
    </div>
    <div>
      <el-divider direction="vertical" style="height: 100%" />
    </div>
    <div class="drawer-body right">
      <div>已选标签</div>
      <el-scrollbar class="selected-container" v-loading="tagLoading">
        <tag-list :tagList="this.checkedTagList" @close="handleClose"></tag-list>
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm" id="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-drawer>
</template>
<script>
import BsTagClassificationSelection from '@/pages/dataCollect/components/BsTagClassificationSelection.vue'
import { listUserTagClassification, getTagList } from '@/apis/data-collect/bs-tag-classification'
import { showToast } from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'
import TagList from '../components/TagList.vue'
import { groupBy } from 'lodash'

export default {
  name: 'TagClassificationDrawer',
  emits: ['reload'],
  props: {},
  components: {
    LtwIcon,
    BsTagClassificationSelection,
    TagList
  },
  data() {
    return {
      visible: false,
      classificationList: [],
      tagsData: [],
      checkClassificationList: [],
      currentTagList: [],
      checkedTagList: [],
      checkedClassificationIdList: [],
      currentClassificationList: [],
      tabsData: [],
      accessRoleType: '',
      dataList: [],
      tagLoading: false
    }
  },
  created() {},
  methods: {
    show(row) {
      this.checkedTagList = JSON.parse(JSON.stringify(row.dataList || []))
      this.listTagClassification()
      this.visible = true
    },
    listTagClassification() {
      listUserTagClassification().then(res => {
        this.classificationList = res.data

        if (!this.classificationList?.length) {
          showToast('暂无场景数据', 'warning')
          return
        }
        this.classificationList.forEach(item => {
          item.tagClassificationList.forEach(classification => {
            classification.checked = false
          })
        })

        this.tabsData = this.classificationList.map(item => {
          return {
            name: item.roleTypeName,
            code: item.roleTypeCode
          }
        })
        if (!this.accessRoleType) {
          this.accessRoleType = this.classificationList[0].roleTypeCode
        }
        this.currentClassificationList = this.classificationList.find(item => item.roleTypeCode === this.accessRoleType)
        // 校验分类是否选中
        this.checkCurrentClassification()
      })
    },
    checkCurrentClassification() {
      const checkedClassMap = new Map()
      this.checkedTagList.forEach(checkedClass => {
        checkedClassMap.set(checkedClass.id, true)
      })

      this.currentClassificationList.tagClassificationList.forEach(classification => {
        if (checkedClassMap.has(classification.id)) {
          classification.checked = true
        }
      })
    },
    handleTabClick(tab) {
      this.accessRoleType = tab
      this.currentClassificationList = this.classificationList.find(item => item.roleTypeCode === tab)
      this.checkCurrentClassification()
    },
    // checkTagType(tag) {
    //   if (tag.type === 'continuous') {
    //     return tag.mutuallyExclusive ? 'danger' : 'warning'
    //   }
    //   if (tag.type === 'transient') {
    //     return tag.mutuallyExclusive ? 'success' : ''
    //   }
    //   return 'info'
    // },
    setCheckedTag(tagGroupMap) {
      const children = []
      for (let item in tagGroupMap) {
        children.push({
          nameCn: tagGroupMap[item][0].groupNameCn,
          name: tagGroupMap[item][0].groupName,
          id: item,
          tagList: tagGroupMap[item]
        })
      }
      return children
    },
    // 点击左侧分类
    handleClassificationChange(val) {
      let index = this.checkedTagList.findIndex(checked => val.id === checked.id)
      if (!~index) {
        this.tagLoading = true
        getTagList(val.id).then(res => {
          let tagList = res.data
            .map(item => item.tagList)
            .flat(Infinity)
            .map(tag => {
              return {
                ...tag,
                groupId: tag.isParent ? `${tag.childCode}-${tag.childName}` : tag.groupId,
                groupName: tag.isParent ? tag.childName : tag.groupName,
                groupNameCn: tag.isParent ? tag.childName : tag.groupNameCn,
                groupCode: tag.isParent ? tag.childCode : tag.groupCode
              }
            })
          const tagGroupMap = groupBy(tagList, 'groupId')

          val.children = this.setCheckedTag(tagGroupMap, val)

          this.checkedTagList = this.checkedTagList.concat(val) //this.checkedTagList.concat(res.data)
          this.tagLoading = false
        })
      } else {
        this.checkedTagList.splice(index, 1)
      }
    },
    formatList(list) {
      let children = []
      list.forEach(tag => {
        let groupIndex = children.findIndex(child => child.id === tag.groupId)
        if (~groupIndex) {
          children[groupIndex].tagList.push(tag)
        } else {
          children.push({
            code: tag.groupCode,
            id: tag.groupId,
            name: tag.groupName,
            nameCn: tag.groupNameCn,
            tagList: [tag]
          })
        }
      })
      return children
    },
    handleClose(index, tagList) {
      tagList.splice(index, 1)
      this.$emit('classficationTagClose')
    },
    confirm() {
      this.visible = false
      // let tagList = this.checkedTagList
      //   .map(item => {
      //     return item.children
      //   })
      //   .flat(Infinity)
      // let arr = []
      // tagList.forEach(item => {
      //   Object.keys(item).forEach(tag => {
      //     arr = arr.concat(item[tag])
      //   })
      // })
      this.$emit('reload', this.checkedTagList)
    },
    init() {
      this.tagsData = []
      this.checkClassificationList = []
      this.currentTagList = []
      this.checkedTagList = []
      this.checkedClassificationIdList = []
    },
    dialogClosed() {
      this.checkedTagList = []
    }
  }
}
</script>
<style lang="scss">
.drawer-container {
  .el-drawer__header {
    margin-bottom: 0;
  }

  .el-drawer__body {
    height: calc(100% - 125px);
    padding-right: 10px;
    display: flex;
    flex-direction: row;

    .drawer-body {
      height: 100%;
      //overflow-y: auto;
    }

    .left {
      width: 500px;

      .tag-checkbox {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        .checkbox-container {
          width: 100%;
        }
      }
    }

    .right {
      width: calc(100% - 500px);

      .selected-container {
        height: calc(100% - 21px);
        padding-top: 15px;
      }
    }
  }
}
</style>
