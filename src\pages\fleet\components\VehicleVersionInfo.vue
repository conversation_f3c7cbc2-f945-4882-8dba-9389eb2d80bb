<template>
  <div class="vehicle-version-info">
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <div class="left-actions">
        <el-button class="add-version-btn" @click="handleAddVersion">
          <el-icon><Plus /></el-icon>
          新增版本
        </el-button>
        <el-button class="clone-version-btn" @click="handleCloneVersion" :disabled="!selectedVersion">
          <el-icon><DocumentCopy /></el-icon>
          克隆选中版本
        </el-button>
      </div>
      <div class="right-actions">
        <el-button class="delete-version-btn" @click="handleDeleteCurrent" :disabled="!selectedVersion">
          <el-icon><Delete /></el-icon>
          删除当前版本
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="vesion-container">
    <div class="main-content" v-loading="loading">
      <!-- 左侧版本列表 -->
      <div class="version-sidebar" :class="{ 'collapsed': sidebarCollapsed }" v-if="versionList.length > 0">
        <!-- 收起/展开按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon>
            <ArrowLeft v-if="!sidebarCollapsed" />
            <ArrowRight v-if="sidebarCollapsed" />
          </el-icon>
        </div>
        <div
          v-for="version in versionList"
          :key="version.id"
          class="version-item"
          :class="{ 'active': selectedVersion?.id === version.id }"
          @click="selectVersion(version)"
        >
          <div class="version-content">
            <el-tooltip
              v-if="!version.perfect"
              content="版本信息不全不可用"
              placement="top"
              effect="dark"
            >
              <svg class="warning-icon" aria-hidden="true">
                <use xlink:href="#svg-warning-outline" />
              </svg>
            </el-tooltip>

            <el-tooltip
              :content="version.version"
              placement="top"
              effect="dark"
            >
              <div class="version-name" :title="version.version || ''">
                <span v-if="!sidebarCollapsed">{{ version.version }}</span>
                <span v-else class="collapsed-text">{{ version.version.substring(0, 6) }}</span>
              </div>
            </el-tooltip>
          </div>
          <div v-if="selectedVersion?.id === version.id" class="active-indicator"></div>
        </div>
      </div>

      <!-- 右侧详情区域 -->
      <div class="version-details">
        <!-- 空状态 -->
        <div v-if="versionList.length === 0 && !loading" class="empty-state-container">
          <el-empty
            description="暂无版本信息"
            :image-size="120"
          >
            <template #image>
              <ltw-icon :icon-code="'el-icon-document'" style="font-size: 80px; color: #C0C4CC;"></ltw-icon>
            </template>
            <template #description>
              <p style="color: #909399; font-size: 14px; margin: 0;">暂无版本信息</p>
            </template>
          </el-empty>
        </div>

        <!-- 版本详情内容 -->
        <div v-else-if="selectedVersion" class="details-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <div class="section-header">
            <h3 class="section-title">基础信息</h3>
            <div class="header-buttons">
              <!-- 编辑模式按钮 -->
              <template v-if="isBasicInfoEditing">

                <el-button
                  type="success"
                  size="small"
                  @click="saveBasicInfo"
                >
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="cancelBasicInfoEdit"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </template>
              <!-- 查看模式按钮 -->
              <el-button
                v-else
                class="warning-btn"
                size="small"
                @click="startBasicInfoEdit"
              >
                <el-icon><Edit /></el-icon>

              </el-button>
            </div>
          </div>
          <div class="info-content">
            <!-- 编辑模式 -->
            <el-form
              v-if="isBasicInfoEditing"
              ref="basicInfoForm"
              :model="editingBasicInfo"
              :rules="basicInfoRules"
              label-width="100px"
              size="small"

            >
              <el-form-item label="版本名称：" prop="version"  style="height: 32px;line-height:32px">
                <ltw-input
                  v-model="editingBasicInfo.version"
                  placeholder="请输入版本名称"
                  class="edit-input"
                   style="height: 32px;"
                />
              </el-form-item>
              <el-form-item label="备注：" prop="remark">
                <el-input
                  v-model="editingBasicInfo.remark"
                  placeholder="请输入备注信息"
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :rows="3"
                  class="edit-textarea"
                />
              </el-form-item>
            </el-form>

            <!-- 查看模式 -->
            <div v-else class="info-display">
              <div class="info-row">
                <span class="info-label">版本名称：</span>
                <span class="info-value">{{ selectedVersion.version }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">备注：</span>
                <span class="info-value placeholder">{{ selectedVersion.remark || '暂无信息' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置信息 -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">配置信息</h3>
            <div class="header-buttons">
              <!-- 编辑模式按钮 -->
              <template v-if="isConfigEditing">

                <el-button
                  type="success"
                  size="small"
                  @click="saveConfigEdit"
                >
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="cancelConfigEdit"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </template>
              <!-- 查看模式按钮 -->
              <el-button
                v-else
                class="warning-btn"
                size="small"
                @click="startConfigEdit"
              >
                <el-icon><Edit /></el-icon>

              </el-button>
            </div>
            <!-- <el-button
              type="warning"
              :class="['warning-tag', { 'editing-state': isConfigEditing }]"
              @click="toggleConfigEdit"
            >
              <ltw-icon :icon-code="isConfigEditing ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>
            </el-button> -->
          </div>

          <!-- 传感器配置组件 -->
          <SensorConfigSection
            :version-data="selectedVersion"
            :editable="isConfigEditing"
            @add-config="handleAddConfig"
            @edit-config="handleEditConfig"
            @delete-config="handleDeleteConfig"
            @view-config="handleViewConfig"
          />
        </div>
        </div>
      </div>
    </div>
    </div>

    <!-- 新增版本对话框 -->
    <AddVersionDialog
      v-model:visible="showAddVersionDialog"
      :vehicle-data="vehicleData"
      :mode="dialogMode"
      :version-data="dialogMode === 'copy' ? cloneVersionData : null"
      @confirm="handleAddVersionConfirm"
      @updated ="handleAddVersionConfirm"
    />
  </div>
</template>

<script>
import { Plus, Delete, DocumentCopy, Edit, Check, Close, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { listFtmVehicleVariantVersion, getFtmVehicleVariantVersion,updateFtmVehicleVariantVersion,deleteFtmVehicleVariantVersion } from '@/apis/fleet/ftm-vehicle-variant-version'
import SensorConfigSection from './SensorConfigSection.vue'
import AddVersionDialog from './AddVersionDialog.vue'



export default {
  name: 'VehicleVersionInfo',
  components: {
    Plus,
    Delete,
    DocumentCopy,
    Edit,
    Check,
    Close,
    ArrowLeft,
    ArrowRight,
    SensorConfigSection,
    AddVersionDialog
  },
  props: {
    vehicleData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      selectedVersion: null,
      versionList: [],
      sidebarCollapsed: false, // 控制左侧版本列表收起状态
      versionDetail: null, // 存储版本详情数据
      showAddVersionDialog: false, // 控制新增版本对话框显示
      dialogMode: 'add', // 对话框模式：'add' | 'copy'
      cloneVersionData: null, // 要克隆的版本完整数据
      isBasicInfoEditing: false, // 控制基础信息编辑状态
      editingBasicInfo: { // 编辑中的基础信息数据
        version: '',
        remark: ''
      },
      basicInfoRules: { // 基础信息表单验证规则
        version: [
          { required: true, message: '请输入版本名称', trigger: 'blur' },
          { min: 1, max: 50, message: '版本名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 100, message: '备注信息不能超过100个字符', trigger: 'blur' }
        ]
      },
      isConfigEditing: false // 控制配置信息编辑状态
    }
  },

  watch: {
    // 监听vehicleData变化，加载版本列表
    'vehicleData.id': {
      handler(newId, oldId) {
        // 避免重复调用：只有当ID真正发生变化时才执行
        if (newId !== oldId) {
          if (newId) {
            this.loadVersionList(newId)
          } else {
            this.versionList = []
            this.selectedVersion = null
            this.versionDetail = null
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    // mounted中不再重复调用，因为watch已经处理了初始化
    // 确保进入版本信息tab时默认是展开状态
    this.sidebarCollapsed = false
  },

  activated() {
    // 当组件被激活时（比如从其他tab切换回来），确保侧边栏是展开状态
    this.sidebarCollapsed = false
  },
  methods: {
    // 切换侧边栏收起状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 重置侧边栏状态为展开（供父组件调用）
    resetSidebarState() {
      this.sidebarCollapsed = false
    },

    // 加载版本列表
    async loadVersionList(vehicleId) {
      if (!vehicleId) return

      this.loading = true
      try {
        const res = await listFtmVehicleVariantVersion({
          variantId: vehicleId
        })
        this.versionList = res.data || []

        // 默认选中第一个版本
        if (this.versionList.length > 0) {
          this.selectVersion(this.versionList[0])
        } else {
          this.selectedVersion = null
          this.versionDetail = null
        }

        console.log('版本列表加载完成:', this.versionList)
      } catch (error) {
        console.error('加载版本列表失败:', error)
        //this.$message.error('加载版本列表失败')
        this.versionList = []
        this.selectedVersion = null
        this.versionDetail = null
      } finally {
        this.loading = false
      }
    },

    // 加载版本详情
    async loadVersionDetail(versionId) {
      if (!versionId) return

      this.loading = true
      try {
        const res = await getFtmVehicleVariantVersion(versionId)
        this.versionDetail = res.data || {}

        // 更新selectedVersion的详情数据
        if (this.selectedVersion) {
          this.selectedVersion = {
            ...this.selectedVersion,
            ...this.versionDetail
          }
        }

        console.log('版本详情加载完成:', this.versionDetail)
      } catch (error) {
        console.error('加载版本详情失败:', error)
        //this.$message.error('加载版本详情失败')
        this.versionDetail = null
      } finally {
        this.loading = false
      }
    },

    // 选择版本
    selectVersion(version) {
      // 退出所有编辑模式
      this.exitAllEditModes()

      this.selectedVersion = version
      // 加载该版本的详情
      if (version?.id) {
        this.loadVersionDetail(version.id)
      }
    },

    // 退出所有编辑模式
    exitAllEditModes() {
      // 退出基础信息编辑模式
      if (this.isBasicInfoEditing) {
        this.cancelBasicInfoEdit()
      }

      // 退出配置编辑模式
      if (this.isConfigEditing) {
        this.cancelConfigEdit()
      }
    },

    handleVersionCheck() {
      // 处理版本选择
    },

    handleAddVersion() {
      this.dialogMode = 'add'
      this.cloneVersionData = null
      this.showAddVersionDialog = true
    },

    async handleCloneVersion() {
      if (!this.selectedVersion) {
        this.$message.warning('请先选择要克隆的版本')
        return
      }

      try {
        // 获取完整的版本详情数据，包括配置信息
        const res = await getFtmVehicleVariantVersion(this.selectedVersion.id)

        // 设置模式和完整的版本数据
        this.dialogMode = 'copy'
        this.cloneVersionData = res.data
        this.showAddVersionDialog = true
      } catch (error) {
        console.error('获取版本详情失败:', error)
        this.$message.error('获取版本详情失败，无法复制版本')
      }
    },

    handleDeleteCurrent() {
      if (!this.selectedVersion) {
        this.$message.warning('请先选择要删除的版本')
        return
      }

      this.$confirm(`确定要删除版本 "${this.selectedVersion.version}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFtmVehicleVariantVersion({id:this.selectedVersion.id}).then(res =>{
           this.$message.success('删除成功')
           this.loadVersionList(this.vehicleData.id)
        })
       
        
      }).catch(() => {
        // 取消删除
      })
    },

    // 处理新增配置 - 从子组件传递过来的事件
    handleAddConfig() {
      this.loadVersionDetail(this.selectedVersion.id)
    },

    // 处理编辑配置 - 从子组件传递过来的事件
    handleEditConfig() {
        this.loadVersionDetail(this.selectedVersion.id) 
    },

    // 处理删除配置 - 从子组件传递过来的事件
    handleDeleteConfig(row) {
      this.loadVersionDetail(this.selectedVersion.id) 
    },

    // 处理查看配置 - 从子组件传递过来的事件
    handleViewConfig() {
      // 查看操作不需要特殊处理，抽屉组件会自动处理
    },

    // 开始编辑基础信息
    startBasicInfoEdit() {
      if (!this.selectedVersion) return

      this.isBasicInfoEditing = true
      this.editingBasicInfo = {
        version: this.selectedVersion.version || '',
        remark: this.selectedVersion.remark || ''
      }
    },

    // 取消编辑基础信息
    cancelBasicInfoEdit() {
      this.isBasicInfoEditing = false
      this.editingBasicInfo = {
        version: '',
        remark: ''
      }
      // 清除表单验证
      if (this.$refs.basicInfoForm) {
        this.$refs.basicInfoForm.clearValidate()
      }
    },

    // 保存基础信息
    async saveBasicInfo() {
      if (!this.selectedVersion) return

      try {
        // 使用表单验证
        await this.$refs.basicInfoForm.validate()

        // 更新selectedVersion数据
        const param = {
          ...this.selectedVersion,
          version: this.editingBasicInfo.version,
          remark: this.editingBasicInfo.remark
        }

        await updateFtmVehicleVariantVersion(param)
        this.selectedVersion.version = this.editingBasicInfo.version
        this.selectedVersion.remark = this.editingBasicInfo.remark

        // 更新版本列表中对应的数据
        const versionIndex = this.versionList.findIndex(v => v.id === this.selectedVersion.id)
        if (versionIndex !== -1) {
          this.versionList[versionIndex].version = this.editingBasicInfo.version
          this.versionList[versionIndex].remark = this.editingBasicInfo.remark
        }

        // 退出编辑模式
        this.isBasicInfoEditing = false
        this.$message.success('保存成功')
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 切换配置编辑状态
    toggleConfigEdit() {
      if (this.isConfigEditing) {
        // 保存配置编辑
        this.saveConfigEdit()
      } else {
        // 进入配置编辑模式
        this.startConfigEdit()
      }
    },



    // 开始配置编辑
    startConfigEdit() {
      this.isConfigEditing = true
    },

    // 取消配置编辑
    cancelConfigEdit() {
      this.isConfigEditing = false
    },

    // 保存配置编辑
   async saveConfigEdit() {
      this.isConfigEditing = false
       const res = await listFtmVehicleVariantVersion({
          variantId: this.vehicleData.id
        })
        this.versionList = res.data || []
    },

    // 处理新增版本确认
    handleAddVersionConfirm() {
      this.loadVersionList(this.vehicleData.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-version-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100vh - 300px);
      .warning-btn{
      display: flex;
      padding: 5px 10px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      background: #FFB03A;
      border-radius: 2px;
      color:white;
    }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      display: flex;
      gap: 8px;
    }

    .add-version-btn {
      padding: 6px 11px;
      background: #F5F5FF;
      border: 1px solid #DDDDFF;
      border-radius: 2px;
      color: #5755FF;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;
      &:hover {
        background: #EEEEFF;
        border-color: #CCCCFF;
      }
    }

    .clone-version-btn {
      padding: 6px 11px;
      background: #F5F5FF;
      border: 1px solid #DDDDFF;
      border-radius: 2px;
      color: #5755FF;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;

      &:hover {
        background: #EEEEFF;
        border-color: #CCCCFF;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .delete-version-btn {
      padding: 6px 11px;
      background: #FF6E6F;
      border: 1px solid #FF6E6F;
      border-radius: 2px;
      color: white;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;
      &:hover {
        background: #FF5A5B;
        border-color: #FF5A5B;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

  }
  .vesion-container{
    overflow: auto;
  }

  .main-content {
    display: flex;
    background: white;
    border-radius: 2px;
    gap: 24px;

    .version-sidebar {
      width: 130px;
      min-width: 130px;
      max-width: 142px;
      border-right: 1px solid #EFF1F2;
      transition: all 0.3s ease;

      // 收起状态
      &.collapsed {
        width: 60px;
        min-width: 60px;
        max-width: 60px;

        .version-item {
          padding: 8px 4px;
          justify-content: center;

          .version-content {
            justify-content: center;
            .version-name {
              text-align: center;
              max-width: 100%;
            }
          }
        }
         .sidebar-toggle{
          left: 50px;
         }
      }

      // 切换按钮
      .sidebar-toggle {
        position: absolute;
        top: 168px;
        left:115px;
        width: 24px;
        height: 24px;
        background: #fff;
        border: 1px solid #EFF1F2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f7fa;
          border-color: #c0c4cc;
        }

        .el-icon {
          font-size: 12px;
          color: #606266;
        }
      }

      .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #909399;
        font-size: 14px;
      }

      .version-item {
        height: 45px;
        align-items: center;
        position: relative;
        cursor: pointer;
        background: white;
        padding: 8px 12px;
        transition: all 0.3s ease;

        &.active {
          background: #F5F5FF;

          .version-content .version-name {
            color: #5755FF;
            font-weight: 700;
            text-align: end;
          }

          .active-indicator {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #5755FF;
          }
          .version-time{
              color: #5755FF;
          }
        }

        .version-content {
          display: flex;
          align-items: center;
          justify-content: flex-end; // 整体内容靠右对齐
          padding: 4px 7px 2px 7px;
          flex: 1;
          width: 100%;
          overflow: hidden; // 确保容器能处理溢出

          .warning-icon {
            width: 14px;
            height: 14px;
            fill: #E47A01;
            flex-shrink: 0;
            cursor: help;
            margin-left: 4px; // 图标左侧间距
          }

          .version-name {
            color: #4E5256;
            font-size: 12px;
            font-family: 'Bosch Sans Global', sans-serif;
            font-weight: 400;
            line-height: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            max-width: calc(100% - 18px); // 为图标预留空间

            // 收起状态下的文本样式
            .collapsed-text {
              font-size: 10px;
              font-weight: 600;
              text-align: center;
              display: block;
              width: 100%;
            }
            flex-shrink: 1; // 允许收缩
            text-align: right; // 文字右对齐
          }
        }
        .version-time{
          margin-right: 10px;
          text-align: right;
          font-weight: 400;
          font-size: 10px;
          color:#4E5256;
        }
      }
    }

    .version-details {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: 24px;

      .empty-state-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 400px;

        :deep(.el-empty) {
          .el-empty__image {
            margin-bottom: 20px;
          }

          .el-empty__description {
            margin-top: 16px;
          }
        }
      }
      .warning-tag {
            background: #FFB03A;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 22px;
            width: 40px;
            margin-left: 10px;
            transition: background-color 0.3s ease;

            &:hover {
              background: #E6690A;
            }

            // 编辑状态下的绿色样式
            &.editing-state {
              background: #67C23A;

              &:hover {
                background: #5DAE34;
              }
            }
          }

          .cancel-tag {
            background: #909399;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 22px;
            width: 40px;
            margin-left: 8px;

            &:hover {
              background: #73767A;
            }
          }

      .info-section {
        .section-header {
          display: flex;
          align-items: center;
          gap:10px;
          margin-bottom: 24px;

          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 28px;
            margin: 0;
            height: 24px;
          }

          .header-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
          }

        }
        


        .info-content {
          padding-bottom: 24px;
          border-bottom: 1px solid #EBEEF5;
          // 表单样式
         :deep(.el-form) {
            .el-form-item {
              margin-bottom: 16px;

              .el-form-item__label {
                color: #606266;
                font-weight: 500;
                height: 32px !important;
                line-height: 32px !important;
              }

              .el-form-item__content {
                line-height: 32px;
              }
            }
          }

          // 查看模式样式
          .info-display {
            padding-bottom: 12px;


          .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            gap:20px;

            &:last-child {
              margin-bottom: 0;
            }

            .info-label {
              color: #4E5256;
              font-size: 12px;
              font-family: 'Bosch Office Sans', sans-serif;
              font-weight: 400;
              line-height: 23px;
              width: 60px;
              text-align: right;
            }

            .info-value {
              color: #232628;
              font-size: 12px;
              font-family: 'Bosch Office Sans', sans-serif;
              font-weight: 400;
              line-height: 23px;

              &.placeholder {
                color: #B2B9C0;
              }
            }

            .edit-input {
              flex: 1;
              max-width: 300px;
              height:32px;

              :deep(.el-input__wrapper) {
                border-radius: 2px;
                border: 1px solid #DCDFE6;
                height: 32px;

                &:hover {
                  border-color: #C0C4CC;
                }

                &.is-focus {
                  border-color: #5755FF;
                  box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                }
              }

              :deep(.el-input__inner) {
                height: 32px;
                line-height: 32px;
              }
            }

            .edit-textarea {
              flex: 1;
              max-width: 300px;

              :deep(.el-textarea__inner) {
                border-radius: 2px;
                border: 1px solid #DCDFE6;
                font-family: inherit;
                resize: none;

                &:hover {
                  border-color: #C0C4CC;
                }

                &:focus {
                  border-color: #5755FF;
                  box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                }
              }
            }
          }
          }
        }
      }

      .config-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top:20px;

        .section-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          gap:10px;

          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 28px;
            margin: 0;
            height: 24px;
          }
        }

        // 传感器配置相关样式已移至 SensorConfigSection 组件
      }

      .software-section {
        .section-header {
          margin-bottom: 16px;

          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 16px;
            margin: 0;
          }
        }

        .software-table {
          :deep(.el-table) {
            font-size: 12px;

            .el-table__header {
              th {
                background: #FAFAFC;
                color: #909399;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
                font-weight: 400;
                line-height: 23px;
                border-bottom: 1px solid #EBEEF5;
              }
            }

            .el-table__body {
              td {
                color: #606266;
                border-bottom: 1px solid #EBEEF5;
                padding: 8px 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
