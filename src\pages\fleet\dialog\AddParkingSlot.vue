<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" ref="formRef" label-width="100px" :rules="formRules">
      <el-form-item :label="$t('编码')" prop="code">
        <ltw-input v-model="form.code" :disabled="formReadonly" textType="code" id="name"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('车位线宽')" prop="lineWidth">
        <ltw-input v-model="form.lineWidth" :disabled="formReadonly"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('车位类型')" prop="type">
        <el-select
          filterable
          collapse-tags
          collapse-tags-tooltip
          v-model="form.type"
          :disabled="formReadonly"
          clearable
        >
          <el-option v-for="item in groundLevelList" :key="item.code" :label="$t(item.name)" :value="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('地锁类型')" prop="lockType">
        <el-select filterable v-model="form.lockType" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotLockTypeList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('限位器类型')" prop="stopperType">
        <el-select filterable v-model="form.stopperType" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotWheelBlockTypeList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('车位线类型')" prop="lineType">
        <el-select filterable v-model="form.lineType" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotLineStatusList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('车位线颜色')" prop="lineColor">
        <el-select filterable v-model="form.lineColor" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotInnerLineColorList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('车位内部颜色')" prop="innerColor">
        <el-select filterable v-model="form.innerColor" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotInnerColorList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('地面材质')" prop="roadSurfaceMaterial">
        <el-select filterable v-model="form.roadSurfaceMaterial" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingSlotMaterialList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('车位特征')" prop="specialTag">
        <ltw-input v-model="form.specialTag" :disabled="formReadonly" id="specialTag"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('rtk数据')" prop="rtkData">
        <Codemirror
          v-model:value="form.rtkData"
          :options="cmOptions"
          :height="200"
          @change="onChange"
          @blur="onBlur"
          @focus="onFocus"
          @scroll="onScroll"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { isPositiveNum,  validateThreeFloatValidity} from '@/plugins/util.js'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { saveFtmParkingSlot, updateFtmParkingSlot, getFtmParkingSlot } from '@/apis/fleet/ftm-parking-slot'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'

const defaultform = {}
export default {
  name: 'AddParkingSlot',
  emits: ['reload'],
  components: {
    Codemirror
  },
  data() {
    return {
      parkingLotId: '',
      groundLevelList: [],
      parkingSlotLineStatusList: [],
      parkingSlotLockTypeList: [],
      parkingSlotInnerLineColorList: [],
      parkingSlotWheelBlockTypeList: [],
      parkingSlotInnerColorList: [],
      parkingSlotMaterialList: [],
      parkingEntranceRouteList: [],
      parkingEntranceRampList: [],
      parkingEntranceBarrierList: [],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        lineWidth: [
          {
            validator: validateThreeFloatValidity,
            trigger: 'change'
          },
          {
            validator: isPositiveNum,
            trigger: 'change'
          }
        ],
      },
      cmOptions: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.parkingLotId = row.parkingLotId
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增停车位')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑停车位')
          this.getFtmParkingSlot(row.id)
          break
        case 'view':
          this.dialogTitle = this.$t('停车位详情')
          this.getFtmParkingSlot(row.id)
          break
      }
      this.getgroundLevelList() //停车场类型
      //查询停车位和停车场入口字典
      this.getparkingSlotLockType() // 地锁类型
      this.getparkingSlotWheelBlockType() //限位器类型
      this.getparkingSlotLineStatus() //车位线类型
      this.getparkingSlotInnerLineColor() //车位线颜色
      this.getparkingSlotInnerColor() //车位内部颜色
      this.getparkingSlotMaterial() //地面材质
      this.getparkingEntranceRoute() // 进入口路线
      this.getparkingEntranceRamp() //入口坡道类型
      this.getparkingEntranceBarrier() //入口道闸类型
      setTimeout(() => {
        this.cmOptions = {
          mode: 'application/json', // Language mode text/yaml、text/javascript
          theme: 'dracula', // Theme
          // readOnly: 'nocursor'
          indentUnit: 4, // 缩进多少个空格
          tabSize: 4, // 制表符宽度
          // lineNumbers: true, // 是否显示行号
          lineWrapping: true, // 是否默认换行
          // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
          readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
          // line: true,
          smartIndent: true // 智能缩进
        }
      })
    },
    getFtmParkingSlot(id) {
      getFtmParkingSlot(id).then(res => {
        this.form = res.data
      })
    },
    getgroundLevelList() {
      if (!this.groundLevelList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_type'
        }).then(res => {
          this.groundLevelList = res.data
        })
      }
    },
    getparkingSlotLockType() {
      if (!this.parkingSlotLockTypeList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_lock_type'
        }).then(res => {
          this.parkingSlotLockTypeList = res.data
        })
      }
    },
    getparkingSlotWheelBlockType() {
      if (!this.parkingSlotWheelBlockTypeList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_wheel_block_type'
        }).then(res => {
          this.parkingSlotWheelBlockTypeList = res.data
        })
      }
    },
    getparkingSlotLineStatus() {
      if (!this.parkingSlotLineStatusList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_line_status'
        }).then(res => {
          this.parkingSlotLineStatusList = res.data
        })
      }
    },
    getparkingSlotInnerLineColor() {
      if (!this.parkingSlotInnerLineColorList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_inner_line_color '
        }).then(res => {
          this.parkingSlotInnerLineColorList = res.data
        })
      }
    },
    getparkingSlotInnerColor() {
      if (!this.parkingSlotInnerColorList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_inner_color'
        }).then(res => {
          this.parkingSlotInnerColorList = res.data
        })
      }
    },
    getparkingSlotMaterial() {
      if (!this.parkingSlotMaterialList?.length) {
        listSysDictionary({
          typeCode: 'parking_slot_material'
        }).then(res => {
          this.parkingSlotMaterialList = res.data
        })
      }
    },
    getparkingEntranceRoute() {
      if (!this.parkingEntranceRouteList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrance_route'
        }).then(res => {
          this.parkingEntranceRouteList = res.data
        })
      }
    },
    getparkingEntranceRamp() {
      if (!this.parkingEntranceRampList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrace_ramp'
        }).then(res => {
          this.parkingEntranceRampList = res.data
        })
      }
    },
    getparkingEntranceBarrier() {
      if (!this.parkingEntranceBarrierList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrace_barrier'
        }).then(res => {
          this.parkingEntranceBarrierList = res.data
        })
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          this.form.parkingLotId = this.parkingLotId
          saveFtmParkingSlot(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmParkingSlot(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    onChange() {},
    onBlur() {
      try {
        this.formData[this.activeTab] = JSON.stringify(JSON.parse(this.formData[this.activeTab] || '{}'), null, '\t')
      } catch (error) {
        console.log(error)
      }
    },
    onFocus() {},
    onScroll() {}
  }
}
</script>

<style scoped lang="scss"></style>
