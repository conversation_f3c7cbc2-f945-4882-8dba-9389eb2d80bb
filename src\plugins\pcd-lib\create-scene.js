import { ArcRotateCamera, Vector3, Color4, Engine, Scene, AxesViewer } from './trimmed-babylonjs';
const defaultCameraViewParams = function () {
    return {
        alpha: Math.PI / 2,
        beta: 0.01,
        radius: 99,
        lookAt: [0, 0, 0],
        upVector: [0, 0, 1],
        upperBetaLimit: (Math.PI / 2) * 0.85,
        lowerBetaLimit: 0.01,
        lowerRadiusLimit: 1.5,
    };
};
const resetCameraView = (cameraViewParams) => {
    return (camera) => {
        camera.alpha = cameraViewParams.alpha;
        camera.beta = cameraViewParams.beta;
        camera.radius = cameraViewParams.radius;
        camera.setTarget(new Vector3(...cameraViewParams.lookAt));
        camera.getViewMatrix();
    };
};
function createScene(canvas, options) {
    var _a;
    const cameraViewParams = Object.assign({}, defaultCameraViewParams(), options.cameraViewParams);
    const engine = new Engine(canvas, true);
    const scene = new Scene(engine);
    const camera = new ArcRotateCamera('pcd_viewer', cameraViewParams.alpha, cameraViewParams.beta, cameraViewParams.radius, new Vector3(...cameraViewParams.lookAt), scene);
    const upVector = options.upVector || cameraViewParams.upVector;
    const clearColor = options.clearColor || [0.07, 0.14, 0.21, 1];
    scene.clearColor = new Color4(...clearColor);
    scene.useRightHandedSystem = !options.useLeftHandedSystem;
    camera.upperBetaLimit = cameraViewParams.upperBetaLimit;
    camera.lowerBetaLimit = cameraViewParams.lowerBetaLimit;
    camera.lowerRadiusLimit = cameraViewParams.lowerRadiusLimit;
    camera.panningInertia = 0.3;
    camera.maxZ = Number.MAX_SAFE_INTEGER;
    camera.attachControl(canvas, true);
    camera.upVector = new Vector3(...upVector);
    options.debug && new AxesViewer(scene, 5, null);
    const v3 = new Vector3();
    const pointerInputs = (((_a = camera.inputs.attached) === null || _a === void 0 ? void 0 : _a.pointers) || {});
    pointerInputs.onTouch = function (point, offsetX, offsetY) {
        if (this.panningSensibility !== 0 && ((this._ctrlKey && this.camera._useCtrlForPanning) || this._isPanClick)) {
            this.camera.position.z < 0 && (offsetY = -offsetY);
            v3.set(offsetX / this.panningSensibility, offsetY / this.panningSensibility, 0);
            const alpha = camera.alpha + Math.PI / 2;
            const x = v3.y * Math.sin(alpha) - v3.x * Math.cos(alpha);
            const y = v3.x * Math.sin(alpha) + v3.y * Math.cos(alpha);
            v3.set(x, y, 0);
            this.camera.target = this.camera.target.subtract(v3);
            this.camera.position = this.camera.position.subtract(v3);
        }
        else {
            const angularSensibility = 1500 / Math.max(0.1, (options === null || options === void 0 ? void 0 : options.angularSensibility) || 1);
            this.camera.inertialAlphaOffset -= offsetX / angularSensibility;
            this.camera.inertialBetaOffset -= offsetY / angularSensibility;
        }
    };
    camera.onViewMatrixChangedObservable.add((view) => {
        view.target.z = 0;
        view.wheelPrecision = Math.sqrt(100 / (options.wheelSensibility || 1) / Math.abs(camera.radius));
        view.panningSensibility = 1440 / Math.abs(camera.radius);
    });
    return [scene, resetCameraView(cameraViewParams)];
}
export { createScene };
