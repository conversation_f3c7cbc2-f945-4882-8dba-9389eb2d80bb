<template>
  <el-dialog
    title="新增车辆"
    v-model="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleDialogClose"
    class="add-vehicle-dialog"
  >
    <div class="add-vehicle-container">
      <!-- 步骤条 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="车辆基本信息" description="填写车辆基础信息"></el-step>
        <el-step title="配置基本信息" description="配置装车记录基本信息"></el-step>
        <el-step title="传感器配置" description="配置传感器信息"></el-step>
        <el-step title="完成" description="创建完成"></el-step>
      </el-steps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 第一步：车辆基本信息 -->
        <div v-if="currentStep === 0" class="step-vehicle-info">
          <el-form
            ref="vehicleInfoForm"
            :model="vehicleFormData"
            :rules="vehicleInfoRules"
            label-width="120px"
            class="vehicle-info-form"
          >
            <!-- 第一行：车架号和负责人 -->
            <div class="form-row">
              <div class="form-col">
                <el-form-item label="博世编号：" prop="vin">
                  <ltw-input
                    v-model="vehicleFormData.vin"
                    placeholder="请输入博世编号"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
              <div class="form-col">
                <el-form-item label="负责人：" prop="keeperEmpId">
                  <employee-selection
                        style="width: 100%"
                        v-model="vehicleFormData.keeperEmpId"
                        id="keeperEmpId"
                        @change="changeKeeperEmp"
                    />
                 
                </el-form-item>
              </div>
            </div>

            <!-- 第二行：出厂车型和外部编号 -->
            <div class="form-row">
              <div class="form-col">
                <el-form-item label="出厂车架号：" prop="factoryVin">
                  <ltw-input
                    v-model="vehicleFormData.factoryVin"
                    placeholder="请输入出厂车架号"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
              <div class="form-col">
                <el-form-item label="外部编号：" prop="externalVin">
                  <ltw-input
                    v-model="vehicleFormData.externalVin"
                    placeholder="请输入外部编号"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <!-- 第三行：车牌 -->
            <div class="form-row">
              <div class="form-col">
                <el-form-item label="车牌：" prop="license">
                  <ltw-input
                    v-model="vehicleFormData.license"
                    placeholder="请输入车牌号"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
              <div class="form-col">
                <el-form-item label="照片：" prop="photo">
                  <upload-file
                    ref="vehiclePhotoUpload"
                    source-type="vehicle_image"
                    :limit="1"
                    accept=".jpg,.jpeg,.png,.gif"
                    :source-id="createdVehicleData?.id || ''"
                    v-model="vehicleFormData.photo"
                    list-type="picture-card"
                    :disabled="false"
                  />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 第二步：配置基本信息 -->
        <div v-if="currentStep === 1" class="step-installation-basic">
          <el-form
            ref="installationBasicForm"
            :model="installationFormData"
            :rules="installationBasicRules"
            label-width="120px"
            class="installation-basic-form"
          >
          <el-form-item label="版本名称：" prop="name">
              <ltw-input
                v-model="installationFormData.name"
                placeholder="请输入版本名称"
              />
            </el-form-item>
            <el-form-item label="车型版本：" prop="versionCascader">
              <el-cascader
                v-model="installationFormData.versionCascader"
                :options="vehicleVersionCascaderOptions"
                :props="cascaderProps"
                placeholder="请选择车型版本"
                filterable
                clearable
                style="width: 100%"
                @change="handleVersionCascaderChange"
              />
            </el-form-item>
            <el-form-item label="功能分类：" prop="useType">
              <el-select
                v-model="installationFormData.useType"
                placeholder="请选择功能分类"
                style="width: 100%"
              >
                <el-option
                  v-for="item in useTypeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="开始时间：" prop="startTime">
              <el-date-picker
                v-model="installationFormData.startTime"
                type="datetime"
                placeholder="请选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="handleStartTimeChange"
              />
            </el-form-item>

            <el-form-item label="结束时间：" prop="endTime">
              <el-date-picker
                v-model="installationFormData.endTime"
                type="datetime"
                placeholder="请选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="handleEndTimeChange"
              />
            </el-form-item>

            

            <el-form-item label="描述：">
              <el-input
                v-model="installationFormData.description"
                type="textarea"
                :maxlength="100"
                show-word-limit
                :rows="3"
                placeholder="请输入描述信息"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 第三步：传感器配置 -->
        <div v-if="currentStep === 2" class="step-sensor-config">
          <FleetSensorConfigSection
            :version-data="sensorConfigData"
            :editable="true"
            @add-config="handleAddConfig"
            @edit-config="handleEditConfig"
            @delete-config="handleDeleteConfig"
            @view-config="handleViewConfig"
             @reload="handleFileReload"
          />
        </div>

        <!-- 第四步：完成 -->
        <div v-if="currentStep === 3" class="step-complete">
          <div class="complete-content">
            <el-result
              icon="success"
              title="车辆创建成功"
              sub-title="您的车辆信息已成功创建并保存"
            >
              <template #extra>
                <el-button type="primary" @click="handleFinish">完成</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer  v-if="currentStep < 3">
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button v-if="currentStep > 0 && currentStep < 3" @click="handlePrevious">上一步</el-button>
        <el-button
          v-if="currentStep < 3"
          type="primary"
          @click="handleNext"
          :loading="loading"
        >
          下一步
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'
import FleetSensorConfigSection from './fleetSensorConfigSection.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { listFtmVehicleVariantCascade } from '@/apis/fleet/ftm-vehicle-variant'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import {saveBsVehicle, updateBsVehicle, getBsVehicle} from '@/apis/fleet/bs-vehicle'
import {
  saveFtmVehicleInstallationRecord,
  updateFtmVehicleInstallationRecord,
  deleteFtmVehicleInstallationRecord,
  listFtmVehicleInstallationRecord,
  copyFtmVehicleInstallationRecord
} from '@/apis/fleet/ftm-vehicle-installation-record'
import {
  listFtmVehicleMappingModality,
  deleteFtmVehicleMappingModalitys
} from '@/apis/fleet/ftm-variant-mapping-modality'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import {
  showToast,
  showConfirmToast,
  validateFourDecimalValidity,
  validateTwoDigitInteger,
  isPositiveNum,
  isInteger,
  dateUtils
} from '@/plugins/util'


export default {
  name: 'AddVehicleDialog',
  components: {
    Plus,
    FleetSensorConfigSection,
    UploadFile,
    EmployeeSelection
  },
  data() {
    return {
      dialogVisible: false,
      currentStep: 0,
      loading: false,
      vehicleFormData: {
        vin: '',
        keeperEmpId: '',
        factoryVin: '',
        externalVin: '',
        license: '',
        photo: '' // 照片文件ID，upload-file组件会自动处理
      },
      installationFormData: {
        vin: '',
        version: '',
        versionCascader: null,
        name: '',
        startTime: '',
        endTime: '',
        useType: '',
        description: '',
        code: '',
        variantVersionId: ''
      },
      sensorConfigData: {
        versionMappingModalityVOS: {
          modalityTypeMap: {}
        }
      },
      createdVehicleData: null, // 创建的车辆数据
      vehicleVariantOptions: [], // 车型版本选项（保留兼容性）
      vehicleVersionCascaderOptions: [], // 车型版本级联选项
      useTypeOptions: [], // 功能分类选项
      cascaderProps: { // 级联选择器配置
        value: 'code',
        label: 'label', // 使用label字段作为显示标签
        children: 'children',
        emitPath: false // 只返回最后一级的值
      },
      vehicleInfoRules: {
        vin: [
          { required: true, message: '请输入车架号', trigger: 'blur' }
        ],
        keeperEmpId: [
          { required: true, message: '请输入负责人', trigger: 'blur' }
        ]
      },
      installationBasicRules: {
        versionCascader: [
          { required: true, message: '请选择车型版本', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
          { validator: this.validateStartTime, trigger: 'change' }
        ],
        endTime: [
          { validator: this.validateEndTime, trigger: 'change' }
        ],
        useType: [
          { required: true, message: '请选择功能分类', trigger: 'change' }
        ]
      },
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
    }
  },
  mounted() {
    this.initializeData()
  },
  methods: {
    changeKeeperEmp(e) {
      this.vehicleFormData.keeperEmpName = e.node.name
    },
    // 初始化数据
    async initializeData() {
      await this.loadVehicleVariantOptions()
      await this.loadUseTypeOptions()
    },

    // 加载车型版本选项
    async loadVehicleVariantOptions() {
      try {
        const res = await listFtmVehicleVariantCascade()
        // 保留原有数据结构兼容性
        this.vehicleVariantOptions = res.data || []
        // 处理数据，为级联选择器添加label字段
        this.vehicleVersionCascaderOptions = this.processVehicleVariantData(res.data || [])
      } catch (error) {
        console.error('加载车型版本选项失败:', error)
        this.vehicleVariantOptions = []
        this.vehicleVersionCascaderOptions = []
      }
    },

    // 处理车型版本数据，添加label字段
    processVehicleVariantData(data) {
      return data.map(variant => {
        const processedVariant = {
          ...variant,
          label: variant.code, // 第一层使用code作为显示标签
          code: variant.code,  // 第一层的值使用code
          children: variant.children ? variant.children.map(version => ({
            ...version,
            label: version.version, // 第二层使用version作为显示标签
            code: version.id        // 第二层的值使用id
          })) : []
        }
        return processedVariant
      })
    },

    // 加载功能分类选项
    async loadUseTypeOptions() {
      try {
        const res = await listSysDictionary({ typeCode: 'use_type' })
        this.useTypeOptions = res.data || []
      } catch (error) {
        console.error('加载功能分类选项失败:', error)
      }
    },

    // 打开弹窗
    openDialog() {
      this.dialogVisible = true
      this.resetForm()
      this.currentStep = 0
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false
      this.resetForm()
      this.currentStep = 0
    },

    // 重置表单
    resetForm() {
      this.vehicleFormData = {
        vin: '',
        keeperEmpId: '',
        factoryVins: '',
        externalVin: '',
        license: '',
        photo: ''
      }
      this.installationFormData = {
        vin: '',
        version: '',
        versionCascader: null,
        name: '',
        startTime: '',
        endTime: '',
        useType: '',
        description: '',
        code: '',
        variantVersionId: ''
      }
      this.sensorConfigData = {
        versionMappingModalityVOS: {
          modalityTypeMap: {}
        }
      }
      this.createdVehicleData = null
      if (this.$refs.vehicleInfoForm) {
        this.$refs.vehicleInfoForm.clearValidate()
      }
      if (this.$refs.installationBasicForm) {
        this.$refs.installationBasicForm.clearValidate()
      }
    },

    // 下一步
    async handleNext() {
      if (this.currentStep === 0) {
        // 验证车辆基本信息
        const valid = await this.validateVehicleInfo()
        if (valid) {
          // 创建车辆并进入下一步
          await this.createVehicle()
        }
      } else if (this.currentStep === 1) {
        // 验证装车记录基本信息
        const valid = await this.validateInstallationBasicInfo()
        if (valid) {
          // 创建装车记录并进入传感器配置步骤
          await this.createInstallationRecord()
        }
      } else if (this.currentStep === 2) {
        // 完成传感器配置
        this.currentStep = 3
      }
    },

    // 上一步
    handlePrevious() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 验证车辆基本信息
    validateVehicleInfo() {
      return new Promise((resolve) => {
        this.$refs.vehicleInfoForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 验证装车记录基本信息
    validateInstallationBasicInfo() {
      return new Promise((resolve) => {
        this.$refs.installationBasicForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 创建车辆
    async createVehicle() {
      this.loading = true
      try {
        this.vehicleFormData.wheelBase = 0
        const res = await saveBsVehicle(this.vehicleFormData)
        this.createdVehicleData = res.data
        this.currentStep = 1
        this.$message.success('保存成功')
        this.$emit('finish')
      } catch (error) {
        console.error('创建车辆失败:', error)

      } finally {
        this.loading = false
      }
    },

    // 创建装车记录
    async createInstallationRecord() {
      this.loading = true
      try {

        const res = await saveFtmVehicleInstallationRecord({
          ...this.installationFormData,
          vehicleId: this.createdVehicleData.id,
          vin:this.createdVehicleData.vin
        })
         if (res.data) {
           this.installationFormData = res.data
           this.sensorConfigData = {
             ...res.data,
             vehicleId: this.createdVehicleData.id
           }
           this.currentStep++
           showToast('保存成功')
           this.getModalityList()
           this.$emit('finish')
         }
      } catch (error) {
       // console.error('创建装车记录失败:', error)
      } finally {
        this.loading = false
      }
    },
    getModalityList(){
      let postData = {
        vehicleId: this.createdVehicleData.id,
        variantVersionId: this.installationFormData.variantVersionId,
        installationRecordId: this.installationFormData.id
      }
      listFtmVehicleMappingModality(postData).then(res => {
        this.sensorConfigData.versionMappingModalityVOS = res.data
        this.sensorConfigData.versionMappingModalityVOS.forEach(val => {
          if (val.files?.length) {
            val.files.forEach(f => {
              f.url = this.downloadUrl + f.id + '?token=' + util.getToken()
            })
          }
        })
      })
    },

    // 传感器配置事件处理
    handleAddConfig() {
       this.getModalityList()
      this.getModalityCount()
    },

    handleEditConfig() {
      this.getModalityList()
      this.getModalityCount()
    },

    handleDeleteConfig() {
      // 删除传感器配置
      this.getModalityList()
      this.getModalityCount()
    },
    handleFileReload(){
       this.getModalityList()
    },
    async getModalityCount(){
      const res = await listFtmVehicleInstallationRecord({vin:this.createdVehicleData.vin})
      this.versionList = res.data || []
      const item = this.versionList.find(item => item.id === this.sensorConfigData.id)
      if(item){
        // 使用对象展开运算符确保响应式更新
        this.sensorConfigData = {
          ...this.sensorConfigData,
          modalityTypeMap: item.modalityTypeMap
        }
      }

    },

    // 处理级联选择器变化
    handleVersionCascaderChange(value) {
      if (value) {
        // 根据选中的版本ID，从级联选项中找到对应的车型和版本信息
        this.findVersionInfoById(value)
      } else {
        // 清空时重置相关字段
        this.installationFormData.version = ''
        this.installationFormData.variantCode = ''
        this.installationFormData.variantVersionId = ''
        this.installationFormData.name = ''
      }
    },

    // 根据版本ID查找车型和版本信息
    findVersionInfoById(versionId) {
      for (const variant of this.vehicleVersionCascaderOptions) {
        if (variant.children) {
          for (const version of variant.children) {
            if (version.id === versionId) {
              this.installationFormData.variantCode = variant.code
              this.installationFormData.version = version.version
              this.installationFormData.variantVersionId = version.id
              return
            }
          }
        }
      }
    },

    // 处理时间变化
    handleStartTimeChange() {
      this.$nextTick(() => {
        if (this.$refs.installationBasicForm && this.installationFormData.endTime) {
          this.$refs.installationBasicForm.validateField('endTime')
        }
      })
    },

    handleEndTimeChange() {
      this.$nextTick(() => {
        if (this.$refs.installationBasicForm) {
          this.$refs.installationBasicForm.validateField('endTime')
        }
      })
    },

    // 时间验证
    validateStartTime(rule, value, callback) {
      callback()
      this.$nextTick(() => {
        if (this.$refs.installationBasicForm && this.installationFormData.endTime) {
          this.$refs.installationBasicForm.validateField('endTime')
        }
      })
    },

    validateEndTime(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      if (!this.installationFormData.startTime) {
        callback()
        return
      }

      const startDate = new Date(this.installationFormData.startTime)
      const endDate = new Date(value)

      if (endDate <= startDate) {
        callback(new Error('结束时间不能早于开始时间'))
      } else {
        callback()
      }
    },

    // 处理传感器配置变化
    handleSensorConfigChange(configData) {
      this.sensorConfigData = { ...this.sensorConfigData, ...configData }
    },

    // 完成
    handleFinish() {
      this.closeDialog()
      this.$emit('finish')
    },

    // 再创建一个
    handleCreateAnother() {
      this.resetForm()
      this.currentStep = 0
    },

    // 取消
    handleCancel() {
      this.closeDialog()
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.add-vehicle-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.add-vehicle-container {
  .step-content {
    margin: 30px 0;
    min-height: 500px;

    .step-vehicle-info {
      .vehicle-info-form {
        max-width: 100%;

        .form-row {
          display: flex;
          gap: 20px;
          margin-bottom: 16px;

          .form-col {
            flex: 1;

            &.full-width {
              flex: 2;
            }
          }
        }

        // 照片上传区域样式由upload-file组件自己处理
      }
    }

    .step-installation-record {
      padding: 10px 0;
    }

    .step-complete {
      .complete-content {
        text-align: center;
        padding: 50px 0;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>
