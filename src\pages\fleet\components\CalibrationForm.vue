<template>
  <el-form :model="form" :rules="formRules" ref="itemFormRef" label-width="100px">
    <el-row>
      <el-col :span="12">
        <el-form-item :label="$t('有效时间')" prop="activationDate">
          <el-date-picker
            v-if="form.edit"
            v-model="form.activationDate"
            type="datetime"
            :disabled-date="disabledStartDate"
            :placeholder="$t('有效时间')"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          <el-tag v-else><span v-text="form.activationDate"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('失效时间')" prop="deactivationDate">
          <el-date-picker
            v-if="form.edit"
            v-model="form.deactivationDate"
            :disabled-date="disabledEndDate"
            type="datetime"
            :placeholder="$t('失效时间')"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          <el-tag v-else><span v-text="form.deactivationDate"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('标定日期')" prop="calibrationDate">
          <el-date-picker
            v-if="form.edit"
            v-model="form.calibrationDate"
            type="date"
            :placeholder="$t('标定日期')"
            value-format="YYYY-MM-DD"
          />
          <el-tag v-else><span v-text="form.calibrationDate"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.version">
        <el-form-item :label="$t('版本')" prop="version">
          <el-tag><span v-text="form.version"></span></el-tag>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item id="calibra_file_form" :label="$t('文件')" prop="sysFileVOList">
      <el-button v-if="form.edit" type="primary" size="small" class="add-file" @click="addFiles">新增</el-button>
      <el-table :data="form.sysFileVOList" style="width: 100%; margin-top: -8px" class="no-header-table">
        <el-table-column prop="sourceType" label="文件类型" align="center" header-align="center" width="200">
          <template #default="scope">
            <el-select
              v-if="form.dialogStatus === 'add'"
              v-model="scope.row.sourceType"
              @change="handleChange"
              @clear="scope.row.sourceType = undefined"
            >
              <el-option
                v-for="item in fileTypeList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
                :disabled="item.disabled || getSourceTypeDisabled(scope.row.sourceType, item.code)"
              />
            </el-select>
            <span v-else> {{ getNameByCode(scope.row.sourceType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件">
          <template #default="scope">
            <upload-file
              class="upload-file"
              ref="uploadImage"
              :disabled="!form.edit || scope.row.sourceId"
              :source-id="scope.row.sourceId"
              :source-type="scope.row.sourceType"
              :limit="1"
              listType="text"
              v-model="scope.row.fileId"
              accept=".zip"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="operation" width="80" v-if="form.edit === true">
          <template #default="scope">
            <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
              <el-popconfirm
                width="250"
                :title="$t('这个操作会删除当前选择的数据，是否继续？')"
                @confirm="deleteSysFile(scope.row, scope.$index)"
              >
                <template #reference>
                  <el-button type="danger" size="small">
                    <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                  </el-button>
                </template>
              </el-popconfirm>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-col :span="12" v-if="form.edit">
      <el-form-item :label="$t('是否有效')" id="enable" prop="enable">
        <el-switch
          v-model="form.enable"
          inline-prompt
          :active-text="$t('是')"
          :inactive-text="$t('否')"
          id="enable"
          style="--el-switch-on-color: #13ce66"
        ></el-switch>
      </el-form-item>
    </el-col>
    <el-row>
      <el-col :span="20">
        <el-form-item
          :label="$t('描述')"
          prop="description"
          :rules="[
            {
              required: form.dialogStatus === 'edit',
              message: $t('请输入'),
              trigger: 'change'
            }
          ]"
        >
          <ltw-input
            v-if="form.edit"
            textType="remark"
            type="textarea"
            v-model="form.description"
            :disabled="formReadonly"
          />
          <el-tag v-else><span v-text="form.description"></span></el-tag>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <div class="footer" v-if="operationType !== 'view'">
    <span v-if="form.edit">
      <el-button :id="index + 'cancel'" size="small" @click="cancelForm()">{{ $t('取消') }}</el-button>
      <el-button :id="index + 'save'" size="small" type="primary" @click="submit">{{ $t('保存') }}</el-button>
    </span>
    <span v-else>
      <el-link :id="index + 'edit'" type="warning" @click="editForm()" :underline="false">{{ $t('编辑') }}</el-link>
      <el-link :id="index + 'delete'" type="danger" @click="singleRemoveForm()" :underline="false">{{
        $t('删除')
      }}</el-link>
      <el-link v-if="form.id" :id="index + 'delete'" type="primary" @click="getFormDetail()" :underline="false">{{
        $t('详情')
      }}</el-link>
    </span>
  </div>
</template>

<script>
import { i18n } from '@/plugins/lang'
import LtwInput from '@/components/base/LtwInput'
import UploadFile from '@/components/system/UploadFile.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { showConfirmToast, showToast, dateUtils } from '@/plugins/util.js'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  deleteFtmCalibrationRecords
} from '@/apis/fleet/ftm-calibration-records'
import { deleteFile } from '@/apis/base/file'
import {
  ElForm,
  ElFormItem,
  ElButton,
  ElRow,
  ElCol,
  ElInputNumber,
  ElDatePicker,
  ElOption,
  ElSelect
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

export default {
  name: 'CalibrationForm',
  data() {
    return {
      fileTypeList: [
        {
          code: 'calibration_parameter_factory_file',
          name: '产线',
          disabled: false
        },
        {
          code: 'calibration_parameter_file',
          name: '标定间',
          disabled: false
        },
        {
          code: 'calibration_parameter_trans_file',
          name: '标定间trans',
          disabled: true
        }
      ],
      $t: i18n.global.t,
      form: {},
      backupForm: {},
      dialogStatus: '',
      formRules: {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        calibrationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        fileId: [{ required: true, message: this.$t('请上传文件'), trigger: 'change' }],
        enable: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        sysFileVOList: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      cmOptions: {
        // mode: "text/javascript", // Language mode
        // theme: "dracula", // Theme
        // readOnly: "nocursor",
        mode: 'application/json', // Language mode text/yaml、text/javascript
        theme: 'dracula', // Theme
        // readOnly: 'nocursor'
        indentUnit: 4, // 缩进多少个空格
        tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否默认换行
        // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
        // line: true,
        smartIndent: true // 智能缩进
      }
    }
  },
  emits: ['reload'],
  props: {
    item: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    // modelValue: {
    //   type: Object,
    //   default: {}
    // },
    options: {
      type: Array,
      default: []
    },
    status: {
      type: String,
      default: 'add'
    },
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    operationType: {
      type: String,
      default: ''
    }
  },
  watch: {
    item: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
        this.form.enable = true
        if (!this.form.sysFileVOList || this.form.sysFileVOList.length === 0) {
          this.form.sysFileVOList = []
          this.addFiles()
          // this.form.sysFileVOList = []
          // this.form.sysFileVOList.push({
          //   sourceType: 'calibration_parameter_factory_file'
          // })
        }
        this.backupForm = JSON.parse(JSON.stringify(val))
      },
      deep: true,
      immediate: true
    }
    // 'form.fileId': {
    //   handler(val, oldVal) {
    //     if(this.form.edit && val?.length){
    //       this.$refs.itemFormRef.clearValidate('fileId')
    //     }
    //   }
    // }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    LtwIcon,
    ElForm,
    ElFormItem,
    ElButton,
    ElRow,
    ElCol,
    ElInputNumber,
    ElDatePicker,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    UploadFile
  },
  created() {
    this.listFileTypes()
  },
  methods: {
    getNameByCode(code) {
      const fileType = this.fileTypeList.find(item => item.code === code)
      return fileType ? fileType.name : ''
    },
    listFileTypes() {
      listSysDictionary({ typeCode: 'calibration_file_type' }).then(res => {
        this.fileTypeList = res.data.map(val => {
          return {
            code: val.code,
            name: val.name,
            disabled: val.code === 'calibration_parameter_trans_file'
          }
        })
      })
    },
    handleChange(val) {
      if (this.form.fileId) {
        this.deleteAttachment(this.form.fileId)
      }
    },
    deleteAttachment(id) {
      deleteFile(id).then(() => {
        this.form.id = null
      })
    },
    show(row) {
      this.dialogVisible = true
      // this.dialogStatus = row.type
    },
    checkValidate() {
      return new Promise((resolve, reject) => {
        this.$refs.itemFormRef.validate(valid => {
          // this.$emit('reload', {
          //   form: this.form,
          //   index: this.index,
          //   type: 'add'
          // })
          resolve(valid)
        })
      })
    },
    submit() {
      const fileIdList = this.form.sysFileVOList
        .filter(val => val.fileId?.[0] || val.id)
        .map(val => val.fileId?.[0] || val.id)
      if (!fileIdList?.length) {
        showToast('请先上传文件', 'warning')
        return
      }
      this.$refs.itemFormRef.validate(valid => {
        if (valid) {
          let postData = {
            ...this.form,
            fileIdList: fileIdList
          }
          if(this.form.deactivationDate && this.form.activationDate){
            if(new Date(this.form.deactivationDate).getTime() < new Date(this.form.activationDate).getTime()){
              showToast('生效时间不可大于失效时间', 'warning')
              return
            }
          }
          if (this.form.id) {
            updateFtmCalibrationRecords(postData).then(res => {
              this.saveForm()
            })
          } else {
            // if (this.form.parkingLotId) {
            saveFtmCalibrationRecords(postData).then(res => {
              this.form = res.data
              this.saveForm()
            })
            // } else {
            //   this.saveForm()
            // }
          }
        }
      })
    },
    cancelForm() {
      // if (this.form.typeList?.length) {
      this.backupForm.edit = false
      this.backupForm.dialogStatus = ''
      if (this.form.id) {
        this.$emit('reload', {
          form: this.backupForm,
          index: this.index,
          type: 'cancel'
        })
      } else {
        this.$emit('reload', {
          form: this.form,
          index: this.index,
          type: 'delete'
        })
      }
      this.$refs.itemFormRef.clearValidate(['description'])
      // } else {
      // this.$emit('reload', {
      //   form: this.form,
      //   index: this.index,
      //   type: 'delete'
      // })
      // }
    },
    saveForm() {
      // this.form.edit = false
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'add'
      })
    },
    editForm() {
      this.form.edit = true
      this.form.dialogStatus = 'edit'
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'edit'
      })
    },
    getFormDetail() {
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'view'
      })
    },
    singleRemoveForm() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        if (this.form.id) {
          deleteFtmCalibrationRecords({ id: this.form.id }).then(() => {
            this.$emit('reload', {
              form: this.form,
              index: this.index,
              type: 'delete'
            })
            showToast('标定参数已删除')
            // this.query()
          })
        } else {
          this.$emit('reload', {
            form: this.form,
            index: this.index,
            type: 'delete'
          })
          showToast('标定参数已删除')
        }
      })
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return new Date(val) < new Date(dateUtils.parseTime(this.form.activationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    disabledStartDate(val) {
      if (this.form.deactivationDate) {
        return new Date(val) > new Date(dateUtils.parseTime(this.form.deactivationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    addFiles() {
      const index = this.fileTypeList.findIndex(
        val => !~this.form.sysFileVOList.findIndex(row => row.sourceType === val.code) && !val.disabled
      )
      if (~index) {
        this.form.sysFileVOList.push({
          sourceType: this.fileTypeList[index].code
        })
      } else {
        showToast(this.$t('每种文件类型仅可上传一次'), 'warning')
      }
    },
    deleteSysFile(row, index) {
      this.form.sysFileVOList.splice(index, 1)
    },
    getSourceTypeDisabled(sourceType, code) {
      const disabledList = this.form.sysFileVOList.filter(val => val.sourceType !== sourceType)
      const index = disabledList.findIndex(val => val.sourceType === code)
      return ~index
    }
  }
}
</script>

<style scoped lang="scss">
.add-file {
  margin-bottom: 10px;
}

.row-container {
  .el-col {
    margin-bottom: 5px;
  }
}

.upload-file {
  padding: 8px 0;
}

.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.form-item {
  line-height: 32px;
  // color: #606266;
}

.file-row {
  margin-top: 8px;
  display: flex;

  .form-item {
    white-space: nowrap;
  }
}

.footer {
  position: absolute;
  bottom: 8px;
  right: 20px;
  text-align: right;

  .el-link {
    margin-left: 6px;
  }
}

.no-header-table {
  :deep(.el-table__header-wrapper, .el-table__column, .el-table__row) {
    display: none;
  }
}

#calibra_file_form {
  :deep(.el-table .cell) {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding: 0 12px;
    font-size: small;
  }

  :deep(.el-upload-list__item-file-name) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: smaller;
  }
}
</style>
