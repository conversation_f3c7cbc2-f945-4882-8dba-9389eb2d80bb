<template>
  <div class="draggable-container">
    <div v-show="checkedTagList?.length > 0" style="margin-bottom: 20px">

        <el-collapse v-model="activeNames" @change="handleChange">
          <VueDraggable :list="listData">
          <el-collapse-item v-for="item in listData" :title="item.name" :name="item.name">
            <div class="bs-tag-group-card-body">
              <VueDraggable :list="item.children">
              <template v-for="subItem in item.children" :key="subItem">
                
                <template v-if="subItem.children?.length">
                  <VueDraggable :list="subItem.children">
                   <!---- <el-collapse-item v-for="it in subItem.children" :title="it.name" :name="it.name">
                    </el-collapse-item>-->
                    <tag-draggable :checkedTagList="[subItem]" ></tag-draggable>
                  </VueDraggable>
                </template>
              
                <template v-else>
                  <el-collapse-item
                    v-if="subItem.tagList?.length"
                    :title="subItem.name"
                    :name="subItem.name"
                    class="tag-box"
                  >
                    <VueDraggable
                      ref="el"
                      v-model="subItem.tagList"
                      :options="{ direction: 'horizontal' }"
                      class="draggable"
                    >
                      <div v-for="(tag, tagIndex) in subItem.tagList" :key="tag">
                        <el-tag :type="checkTagType(tag)" style="margin: 0 5px 5px 0">
                          {{ tag['nameCn'] }}
                          <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                        </el-tag>
                      </div>
                    </VueDraggable>
                  </el-collapse-item>
               
                </template>
              
              </template>
              </VueDraggable>
            </div>
          </el-collapse-item>
        </VueDraggable>
        </el-collapse>

    </div>
    <el-empty v-show="checkedTagList?.length == 0" description="暂无选择标签"></el-empty>
  </div>
</template>

<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import { VueDraggable } from 'vue-draggable-plus'
export default {
  components: {
    LtwIcon,
    VueDraggable
  },
  props: {
    checkedTagList: {
      type: Array,
      default: []
    }
  },
  watch: {
    checkedTagList: {
      handler(newval, old) {
        this.listData = newval
      }
    }
  },
  computed:{
    getItem(item){

      // Use subItem.name as key to dynamically create and update model
      if (!this.subItemModels[subItem.name]) {
        this.$set(this.subItemModels, subItem.name, [subItem]);
      }
      return this.subItemModels[subItem.name];
    },
    
  },
  data() {
    return {
      activeNames: '1',
      listData: '',
      treeData:""
    }
  },
  methods: {
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    handleClose(index, tagList) {
      this.$emit('close', index, tagList)
    }
  }
}
</script>

<style lang="scss">
.draggable-container {
  .el-collapse-item__wrap {
    border: 1px solid #d0d0d0;
    padding: 20px;
  }
  .tag-box {
    .draggable {
      display: flex;
      flex-wrap: wrap;
      cursor: pointer;
    }
  }
}
</style>
