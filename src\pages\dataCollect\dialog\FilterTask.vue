<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="765px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="filter-task"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('任务编号')" prop="code">
            <ltw-input v-model="form.code" :disabled="formReadonly" clearable id="code" />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item :label="$t('需求')" prop="reqId" id="reqId">-->
        <!--            <el-popover :visible="visible" :width="700" trigger="click">-->
        <!--              <template #reference>-->
        <!--                <ltw-input-->
        <!--                  v-model="requirementCode"-->
        <!--                  :disabled="formReadonly"-->
        <!--                  clearable-->
        <!--                  :placeholder="$t('请选择')"-->
        <!--                  @input="getRequirementList"-->
        <!--                  @click="showRequirementList"-->
        <!--                  @clear="clearTask"-->
        <!--                  @blur="closeRequirementList"-->
        <!--                  id="requirementCode"-->
        <!--                >-->
        <!--                  &lt;!&ndash; <template #append>-->
        <!--                    <el-button @click="clearTask" id="el-icon-close"-->
        <!--                      ><ltw-icon icon-code="el-icon-close"></ltw-icon-->
        <!--                    ></el-button>-->
        <!--                  </template> &ndash;&gt;-->
        <!--                </ltw-input>-->
        <!--              </template>-->
        <!--              <el-table-->
        <!--                ref="popTable"-->
        <!--                highlight-current-row-->
        <!--                max-height="500px"-->
        <!--                :data="filterRequirementList"-->
        <!--                @row-click="chooseRow"-->
        <!--              >-->
        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  prop="code"-->
        <!--                  :label="$t('编码')"-->
        <!--                >-->
        <!--                  <template #default="scope">-->
        <!--                    <el-link-->
        <!--                      @click.stop="view(scope.row)"-->
        <!--                      type="primary"-->
        <!--                      :underline="false"-->
        <!--                      id="code"-->
        <!--                      >{{ scope.row.code }}</el-link-->
        <!--                    ></template-->
        <!--                  >-->
        <!--                </el-table-column>-->
        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  prop="name"-->
        <!--                  :label="$t('名称')"-->
        <!--                ></el-table-column>-->
        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  prop="initiator"-->
        <!--                  :label="$t('发布人')"-->
        <!--                ></el-table-column>-->
        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  prop="receiver"-->
        <!--                  :label="$t('接收人')"-->
        <!--                ></el-table-column>-->

        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  :label="$t('期望日期')"-->
        <!--                  width="250"-->
        <!--                >-->
        <!--                  <template #default="scope">-->
        <!--                    <template-->
        <!--                      v-if="-->
        <!--                        scope.row.expectedStartTime && scope.row.expectedEndTime-->
        <!--                      "-->
        <!--                    >-->
        <!--                      <el-tag>{{ scope.row.expectedStartTime }}</el-tag-->
        <!--                      >&nbsp; To &nbsp;-->
        <!--                      <el-tag>{{ scope.row.expectedEndTime }}</el-tag>-->
        <!--                    </template>-->
        <!--                  </template>-->
        <!--                </el-table-column>-->
        <!--                <el-table-column-->
        <!--                  header-align="left"-->
        <!--                  align="left"-->
        <!--                  prop="statusName"-->
        <!--                  :label="$t('状态')"-->
        <!--                ></el-table-column>-->
        <!--              </el-table>-->
        <!--            </el-popover>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="12">
          <el-form-item :label="$t('车辆')" prop="vehicleId" id="vehicleId">
            <bs-vehicle-selection modelCode="id" v-model="form.vehicleId" clearable filterable />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item :label="$t('司机')" prop="driverEmpId" id="driverEmpId">-->
        <!--            <employee-selection-->
        <!--                clearable-->
        <!--                :data="driverList"-->
        <!--                :auto-load="false"-->
        <!--                :disabled="formReadonly"-->
        <!--                v-model="form.driverEmpId"-->
        <!--            ></employee-selection>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="12">
          <el-form-item :label="$t('类型')" prop="acquisitionType">
            <el-radio-group v-model="form.acquisitionType" id="acquisitionType">
              <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
                >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item-->
        <!--              v-show="form.acquisitionType === 'parking'"-->
        <!--              :label="$t('停车场')"-->
        <!--              prop="locationId"-->
        <!--              id="locationId"-->
        <!--          >-->
        <!--            <el-select-->
        <!--                v-model="form.locationId"-->
        <!--                clearable-->
        <!--                :disabled="formReadonly"-->
        <!--                filterable-->
        <!--                popper-class="locationId"-->
        <!--            >-->
        <!--              <el-option-->
        <!--                  v-for="item in parkingList"-->
        <!--                  :key="item.id"-->
        <!--                  :label="item.name"-->
        <!--                  :value="item.id"-->
        <!--              >-->
        <!--              </el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item-->
        <!--              v-show="form.acquisitionType === 'driving'"-->
        <!--              :label="$t('路线')"-->
        <!--              prop="locationName"-->
        <!--          >-->
        <!--            <ltw-input-->
        <!--                v-model="form.locationName"-->
        <!--                :disabled="formReadonly"-->
        <!--                clearable-->
        <!--                id="locationName"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </el-row>
      <el-row>
        <el-col :span="12" v-if="dialogStatus !== 'myTask'">
          <el-form-item :label="$t('负责人')" prop="recipientEmpId" id="recipientEmpId">
            <employee-selection clearable :disabled="formReadonly" v-model="form.recipientEmpId"></employee-selection>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('期望日期')" prop="exceptDateRange" id="exceptDateRange">
            <el-date-picker
              clearable
              :disabled="formReadonly"
              v-model="form.exceptDateRange"
              type="daterange"
              :range-separator="$t('到')"
              :start-placeholder="$t('开始日期')"
              :end-placeholder="$t('结束日期')"
              value-format="YYYY-MM-DD"
              popper-class="exceptDateRange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item :label="$t('执行时间')" prop="executionTimeRange" id="executionTimeRange">
            <el-date-picker
              clearable
              :disabled="formReadonly"
              v-model="form.executionTimeRange"
              type="datetimerange"
              :range-separator="$t('到')"
              :start-placeholder="$t('开始时间')"
              :end-placeholder="$t('结束时间')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime"
              popper-class="executionTimeRange"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item :label="$t('标签')" prop="tagAmount">
        <el-tag
          v-for="(tag, index) in tagList"
          :key="index"
          :type="checkTagType(index)"
          closable
          @close="tagClose(tag, index)"
          :id="tag.name"
        >
          {{ tag.name }}
        </el-tag>
        <el-button size="small" @click="tagChoose()" id="el-icon-plus">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          {{ $t('添加') }}
        </el-button>
        <el-button size="small" @click="resetTag()" id="el-icon-close">
          <ltw-icon icon-code="el-icon-close"></ltw-icon>
          {{ $t('重置') }}
        </el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="confirm">{{ $t('确认') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>

  <bs-tag-group-drawer
    @drawerClick="confirmDistributeTags"
    :drawerVisible="tagChooseDrawerVisible"
    :rowTagList="tagList"
  ></bs-tag-group-drawer>
  <disassemble-requirements ref="DisassembleRequirements" />
</template>

<script>
// import { listFtmParkingLot } from '@/apis/fleet/ftm-parking-lot'
import { listMdParkingLotsSimple } from '@/apis/fleet/parking-lot-management'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { i18n } from '@/plugins/lang'
// import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import DisassembleRequirements from '@/pages/dataCollect/dialog/DisassembleRequirements.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import { getDaqReq, listDaqReq } from '@/apis/data-collect/vt-daq-req'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import { getDriverList } from '@/apis/system/sys-role-employee'
import { debounce } from '@/plugins/util'

const defaultform = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {},
      $t: i18n.global.t,
      tagChooseDrawerVisible: false,
      tagList: [],
      // 配置标签
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      // 选择标签
      tagsData: [],
      tagGroupList: [],
      tagsTitle: '',

      vehicleList: [],
      driverList: [],
      requirementList: [],
      row: '',
      visible: false,
      requirementCode: '',
      defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
      // acquisitionType: '',
      acquisitionTypeList: [],
      parkingList: [],
      filterRequirementList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    EmployeeSelection,
    BsTagGroupDrawer,
    BsTagGroupPanel,
    BsVehicleSelection,
    DisassembleRequirements
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = i18n.global.t('筛选')
      row && (this.dialogStatus = row.type)
      // this.listVehicle()
      // this.getDriverList()
      this.getDataTypes()
      // this.getParkingList()
      // this.getRequirements()
      // this.getRequirements()
    },
    setForm(form) {
      this.form = {
        ...this.form,
        ...form
      }
    },
    getParkingList() {
      if (!(this.parkingList && this.parkingList?.length)) {
        listMdParkingLotsSimple().then(res => {
          this.parkingList = res.data
        })
      }
    },
    getDataTypes() {
      if (!(this.acquisitionTypeList && this.acquisitionTypeList?.length)) {
        listSysDictionary({
          typeCode: 'acquisition_task_type'
        }).then(res => {
          res.data.push({ code: '', name: '全部' })
          this.acquisitionTypeList = res.data
        })
      }
    },
    view(row) {
      this.$refs.DisassembleRequirements.show({ type: 'view', id: row.id })
    },
    // getRequirementList: debounce(function () {
    //   if (this.requirementList?.length) {
    //     let requirementList = []
    //     if (this.requirementCode) {
    //       for (let i = 0, iLen = this.requirementList?.length; i < iLen; i++) {
    //         if (~this.requirementList[i].code.indexOf(this.requirementCode)) {
    //           requirementList.push(this.requirementList[i])
    //         }
    //       }
    //       this.filterRequirementList = JSON.parse(
    //         JSON.stringify(requirementList)
    //       )
    //     } else {
    //       this.filterRequirementList = JSON.parse(
    //         JSON.stringify(this.requirementList)
    //       )
    //     }
    //   }
    // }),
    // getRequirements() {
    //   if (!(this.requirementList && this.requirementList.length)) {
    //     listDaqReq().then(res => {
    //       this.requirementList = res.data
    //       this.filterRequirementList = res.data
    //       // this.showRequirementList()
    //     })
    //   } else {
    //     this.$refs.popTable.setCurrentRow(this.row)
    //   }
    // },
    // clickRequirementList() {
    //   if (!this.requirementCode && this.requirementList?.length) {
    //     this.showRequirementList()
    //   } else {
    //     this.getRequirements()
    //   }
    // },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        postData.tagIds = []
        this.tagList.forEach(val => {
          postData.tagIds.push(val.id)
        })
        postData.tagIds = postData.tagIds.join(',')
        if (postData.exceptDateRange && postData.exceptDateRange.length) {
          postData.expectedStartTime = postData.exceptDateRange[0]
          postData.expectedEndTime = postData.exceptDateRange[1]
          delete postData.exceptDateRange
        }
        if (postData.executionTimeRange && postData.executionTimeRange.length) {
          postData.startTime = postData.executionTimeRange[0]
          postData.endTime = postData.executionTimeRange[1]
          delete postData.executionTimeRange
        }
        this.cancel(postData)
      })
    },
    getDaqReq(id) {
      getDaqReq(id).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        res.data.dateRange = [res.data.expectedStartTime, res.data.expectedEndTime]
        this.tagList = res.data.tagList
        this.form = res.data
      })
    },
    tagChoose() {
      this.tagChooseDrawerVisible = true
      this.tagList = JSON.parse(JSON.stringify(this.tagList || []))
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        let tagsData = JSON.parse(JSON.stringify(data.tagList))
        this.tagList = []
        this.saveCheckedTagList({ children: tagsData })
        // this.form.tagAmount = this.form.tagList.length
        this.tagsData = tagsData
        // this.editTags(true)
      }
      this.tagChooseDrawerVisible = false
    },

    saveCheckedTagList(group) {
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          if (group.tagMap[tagId].type === 'continuous') {
            if (group.tagMap[tagId].unit !== 's') {
              let time = this.formatToSec(group.tagMap[tagId])
              group.tagMap[tagId].min = time.num
              group.tagMap[tagId].unit = time.unit
            }
          }
          this.tagList.push({
            ...group.tagMap[tagId]
          })
        })
      }
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        this.tagList = []
        let tagList = JSON.parse(JSON.stringify(dataList))
        this.saveCheckedTagList({ children: tagList })
        this.form.tagAmount = this.tagList.length
      }
      this.tagDialogVisible = false
    },
    editTags(editTags) {
      this.tagDialogVisible = true
      this.tagsTitle = i18n.global.t('编辑标签')
      this.editTagsFlag = editTags
      if (this.tagsData && this.tagsData.length) {
        this.setCheckedList(this.tagsData)
      } else {
        treeListBsTagGroup().then(res => {
          this.tagsData = res.data
          this.setCheckedList(this.tagsData)
        })
      }
    },

    setCheckedList(list) {
      let checkedCount = 0
      if (this.tagList && this.tagList.length) {
        let tagMap = {}
        this.tagList.forEach(tag => {
          tagMap[tag.id] = tag
        })
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
              if (tagMap[tag.id]) {
                item.checkedTagIdList.push(tag.id)
                tag.checked = true
                if (tag.type === 'continuous') {
                  if (tagMap[tag.id].min) {
                    let time = this.formatToHour(tagMap[tag.id].min)
                    tag.min = time.num
                    tag.unit = time.unit
                  } else {
                    tag.min = 0
                    tag.unit = 'h'
                  }
                } else if (tag.type === 'transient') {
                  tag.min = tagMap[tag.id].min
                }
              } else {
                tag.checked = false
              }
            })
            if (item.checkedTagIdList.length > 0) {
              checkedCount++
              if (item.checkedTagIdList.length === item.tagList.length) {
                item.checkedAll = true
              } else {
                item.isIndeterminate = true
              }
            }
          }
          if (item.children && item.children.length > 0) {
            checkedCount++
            let childrenCheckedCount = this.setCheckedList(item.children)
            if (childrenCheckedCount === item.children.length) {
              item.checkedAll = true
            } else {
              if (childrenCheckedCount > 0) {
                item.isIndeterminate = true
              }
            }
          }
        })
      } else {
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
            })
          }
          if (item.children && item.children.length > 0) {
            this.setCheckedList(item.children)
          }
        })
      }
      return checkedCount
    },
    reChoose() {
      this.tagClose()
      this.tagChoose()
    },
    getContinuousUnits() {
      if (!(this.continuousUnits && this.continuousUnits.length)) {
        listSysDictionary({ typeCode: 'continuous_units' }).then(res => {
          this.continuousUnits = res.data
        })
      }
    },
    formatToHour(sec) {
      let num = sec,
        unit = 's'
      if (sec >= 3600) {
        num = Math.floor(sec / 3600)
        unit = 'h'
      } else if (sec >= 60) {
        num = Math.floor(sec / 60)
        unit = 'min'
      }
      return {
        num,
        unit
      }
    },
    formatToSec(time) {
      let num = 0,
        unit = 's'
      if (time.unit === 'h') {
        num = time.min * 3600
      } else if (time.unit === 'min') {
        num = time.min * 60
      } else {
        num = time.min
      }
      return { num, unit }
    },

    // listVehicle() {
    //   if (!(this.vehicleList && this.vehicleList.length)) {
    //     listBsVehicle().then(res => {
    //       this.vehicleList = res.data
    //     })
    //   }
    // },
    resetTag() {
      this.tagList = []
    },
    checkTagType(index) {
      if ((index + 1) % 2 === 0) {
        return 'danger'
      }
      if ((index + 1) % 3 === 0) {
        return 'success'
      }
      return 'warning'
    },
    tagClose(tag, index) {
      this.tagList.splice(index, 1)
    },
    getDriverList() {
      if (!(this.driverList && this.driverList.length)) {
        getDriverList().then(res => {
          this.driverList = res.data
        })
      }
    },
    chooseRow(row) {
      this.row = row
      this.form.reqId = row.id
      this.requirementCode = row.code
      this.closeRequirementList()
    },
    closeRequirementList() {
      this.visible = false
    },
    showRequirementList() {
      // this.getRequirementList()
      this.visible = true
      // if(!this.requirementCode){
      //   getRequirementList
      // }
    },
    clearTask() {
      this.form.reqId = ''
      this.requirementCode = ''
      this.row = ''
      this.closeRequirementList()
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin: 5px 3px;
}

.el-select {
  width: 100%;
}

:deep(.el-form-item__content) > .el-button {
  margin-left: 12px;
}
</style>
