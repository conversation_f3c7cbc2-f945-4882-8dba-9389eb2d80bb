<template>
  <el-container :class="loginTheme === 'dark' ? 'dark' : 'light'">
    <ltw-header
      @reload="goHome"
      @switchSkin="switchSkin"
      :showTime="false"
      :logoSrc="logoSrc"
    >
      <template #title>
        <span class="page-name">{{ $t('忘记密码') }}</span>
      </template>
    </ltw-header>
    <!-- <el-header class="ltw-header">
      <div class="ltw-header-left">
        <div class="title-container">
          <img @click="goHome()" class="logo" :src="logoSrc" v-if="logoSrc" />
          <span>{{ $t('WAVE3') }} </span>
          <span class="page-name">{{ $t('忘记密码') }}</span>
        </div>
      </div>
      <div class="ltw-header-center">
        <slot name="center"> </slot>
      </div>
      <div class="ltw-header-right">
        <slot name="right">
          <div class="login-list">
            <el-link @click="register" :underline="false">{{
              $t('注册')
            }}</el-link>
            <el-divider direction="vertical" />
            <el-link @click="login" :underline="false">{{
              $t('登录')
            }}</el-link>
          </div>
          <el-dropdown @command="changeLang" trigger="click">
            <span class="dropDown"> CN/EN </span>
            <template #dropdown>
              <el-dropdown-menu class="menu">
                <el-dropdown-item
                  v-for="item in locales"
                  :key="item"
                  :command="item"
                  :class="{ active: locale === item }"
                >
                  {{ languageLabels[item] }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </el-header> -->
    <el-main>
      <el-card class="box-card">
        <el-steps :active="activeIndex" align-center finish-status="success">
          <el-step
            @click="goBack(index)"
            v-for="(item, index) in resetPwdProgress"
            :title="item.name"
            :key="index"
          />
        </el-steps>
        <el-form
          v-show="activeIndex === 0"
          :model="form"
          :rules="formRules"
          ref="formRef"
          label-width="80px"
        >
          <el-form-item :label="$t('账号')" prop="loginName">
            <ltw-input
              id="loginName"
              v-model="form.loginName"
              placeholder="请使用您的NT账号"
              clearable
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('邮箱')" prop="email">
            <ltw-input
              id="email"
              text-type="description"
              v-model="form.email"
              clearable
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('验证码')" prop="verifyCode">
            <ltw-input id="verifyCode" v-model="form.verifyCode" clearable
              ><template #append>
                <el-button
                  id="verificationCode"
                  :loading="codeLoading"
                  :disabled="codeSeconds > 0"
                  type="primary"
                  @click="getVerificationCode"
                  >{{
                    codeSeconds
                      ? codeSeconds + '秒后重新获取'
                      : $t('获取邮箱验证码')
                  }}</el-button
                >
              </template></ltw-input
            >
          </el-form-item>
          <div class="dialog-footer">
            <el-button id="user-info-back" @click="resetForm">{{
              $t('返回')
            }}</el-button>
            <!-- <el-button @click="resetForm">{{ $t('清空') }}</el-button> -->
            <el-button id="user-info-next" type="primary" @click="next">{{
              $t('下一步')
            }}</el-button>
          </div>
        </el-form>
        <el-form
          v-show="activeIndex === 1"
          :model="pwdForm"
          :rules="pwdFormRules"
          ref="pwdFormRef"
          label-width="110px"
        >
          <el-form-item :label="$t('新密码')" prop="password">
            <ltw-input
              id="password"
              v-model="pwdForm.password"
              :placeholder="$t('请输入新密码')"
              type="password"
              clearable
            ></ltw-input>
          </el-form-item>
          <el-form-item :label="$t('确认新密码')" prop="confirmPwd">
            <ltw-input
              id="confirmPwd"
              :placeholder="$t('请输入')"
              v-model="pwdForm.confirmPwd"
              type="password"
              clearable
            ></ltw-input>
          </el-form-item>
          <div class="dialog-footer">
            <el-button id="password-back" @click="resetForm">{{
              $t('返回')
            }}</el-button>
            <!-- <el-button @click="resetForm">{{ $t('清空') }}</el-button> -->
            <el-button id="password-next" type="primary" @click="next">{{
              $t('下一步')
            }}</el-button>
          </div>
        </el-form>
        <div v-show="activeIndex === 2" class="finished">
          <div>{{ $t('新密码重置成功，请重新登录！') }}</div>
          <div class="dialog-footer">
            <el-button id="login" type="primary" @click="login">{{
              $t('重新登录')
            }}</el-button>
          </div>
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>

<script>
import LtwHeader from '@/components/base/LtwHeader'
import util from '@/plugins/util'
import { getVerifyCode, passwordReset, getVerifyAuth } from '@/apis/base/index'
import { setLocale } from '@/plugins/util'
import { useI18n } from 'vue-i18n'
export default {
  name: 'ForgetPassword',
  components: {
    LtwHeader
  },
  data() {
    const validatepassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else {
        if (value.length < 8) {
          callback(new Error('新密码至少8位以上'))
        } else {
          callback()
        }
      }
    }
    const validateconfirmPwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认密码'))
      } else if (value !== this.pwdForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      logoSrc: require('@/assets/images/login/logo-dark.png'),
      locales: ['zh', 'en'],
      languageLabels: {
        zh: '简体中文',
        en: 'English'
      },
      locale: '',
      form: {},
      formRules: {
        loginName: [
          {
            required: true,
            message: this.$t('请输入账号'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            type: 'email',
            required: true,
            message: this.$t('请输入正确的邮箱'),
            trigger: 'blur'
          }
        ],
        verifyCode: [
          {
            required: true,
            message: this.$t('请输入验证码'),
            trigger: 'blur'
          }
        ]
      },
      activeIndex: 0,
      pwdForm: {},
      pwdFormRules: {
        password: [
          {
            required: true,
            validator: validatepassword,
            trigger: 'blur'
          },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
            message:
              '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为 8 - 30位',
            trigger: 'change'
          }
        ],
        confirmPwd: [
          {
            required: true,
            validator: validateconfirmPwd,
            trigger: 'change'
          }
        ],
        learInterval: false
      },
      resetPwdProgress: [
        {
          name: '身份验证'
        },
        {
          name: '密码重置'
        },
        {
          name: '重置完成'
        }
      ],
      codeSeconds: 0,
      clearInterval: '',
      codeLoading: false,
      loginTheme: ''
    }
  },
  created() {
    this.locale = useI18n().locale
  },
  methods: {
    goHome() {
      this.$router.push('/login')
    },
    changeLang(command) {
      setLocale(command)
    },
    register() {
      this.$router.push('/register')
    },
    login() {
      this.$router.push('/login')
    },
    resetForm() {
      if (this.activeIndex === 0) {
        // this.$refs.formRef.resetFields()
        this.goHome()
      } else if (this.activeIndex === 1) {
        // this.$refs.pwdFormRef.resetFields()
        this.activeIndex--
      }
    },
    next() {
      this.$refs[this.activeIndex === 0 ? 'formRef' : 'pwdFormRef'].validate(
        valid => {
          if (valid) {
            if (this.activeIndex === 1) {
              this.passwordReset()
            } else if (this.activeIndex === 0) {
              getVerifyAuth(this.form).then(res => {
                this.activeIndex++
              })
            } else {
              this.activeIndex++
            }
          }
        }
      )
    },
    getVerificationCode() {
      if (!this.form.loginName) {
        this.$refs.formRef.validateField(['loginName'])
      } else if (!this.form.email) {
        this.$refs.formRef.validateField(['email'])
      } else {
        this.codeLoading = true
        getVerifyCode(this.form)
          .then(res => {
            this.codeSeconds = 120
            // this.learInterval = ''  // console.log(res)
            this.clearInterval = setInterval(() => {
              this.codeSeconds--
              if (!this.codeSeconds) {
                clearInterval(this.clearInterval)
              }
              // i=this.codeSeconds  ? i: this.codeSeconds
            }, 1000)
          })
          .finally(info => {
            this.codeLoading = false
          })
      }
    },
    passwordReset() {
      let postData = { ...this.form }
      postData.password = this.pwdForm.password
      let data = util.encrypt(JSON.stringify(postData))
      passwordReset(data).then(res => {
        this.activeIndex++
      })
    },
    goBack(index) {
      if (this.activeIndex > index) {
        this.activeIndex = index
      }
    },
    switchSkin(loginTheme) {
      this.loginTheme = loginTheme
    }
  }
}
</script>

<style scoped lang="scss">
.el-container {
  height: 100%;
  flex-direction: column;
  .page-name {
    font-size: 14px;
    position: relative;
    letter-spacing: 1px;
    white-space: nowrap;
    padding-left: 10px;

    &::before {
      position: absolute;
      content: '';
      display: block;
      height: 20px;
      width: 1px;
      background: #bfbfbf;
      top: calc(50% - 10px);
      left: 0;
    }
  }
  .dropdown-divided {
    flex-direction: column;
    padding: 0;
    &::before {
      width: 100%;
    }
    span {
      padding: 0 17px;
    }
  }
  :deep(.el-dropdown-menu__item.active) {
    background-color: var(--el-dropdown-menuItem-hover-fill);
    color: var(--el-dropdown-menuItem-hover-color);
  }
  .el-dropdown {
    cursor: pointer;
  }

  .el-main {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: calc(100% - 60px);
    padding-top: 140px;
  }
  .box-card {
    // margin-top: 5%;
    width: 800px;
    // height: 400px;
    display: flex;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    :deep(.el-card__body) {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .el-steps {
      width: 100%;
      margin: 40px 0;
      .el-step {
        cursor: pointer;
      }
    }
    .el-form {
      width: 500px;
      :deep(.el-input-group__append) button.el-button {
        background-color: var(--el-button-bg-color, var(--el-fill-color-blank));
        color: var(--el-button-text-color, var(--el-text-color-regular));
      }
    }
    .finished {
      text-align: center;
      color: #505050;
      font-size: 14px;
      .dialog-footer {
        margin-top: 20px;
      }
    }
    .dialog-footer {
      text-align: center;
    }
  }
}
</style>
