<template>
  <div class="base-filter">
    <div class="filter-content">
      <!-- 包裹所有查询条件的容器 -->
      <div class="filter-conditions-wrapper">
        <!-- 第一行查询条件 -->
        <div class="filter-row first-row">
          <div class="filter-panel common-filters">
            <slot name="common-filters"></slot>
          </div>
        </div>
        
        <!-- 更多查询条件（展开时显示） -->
        <div v-show="showMore" class="filter-row">
          <div class="filter-panel more-filters">
            <slot name="more-filters"></slot>
          </div>
        </div>
        <div v-show="showMore" class="filter-row">
          <div class="filter-panel extra-filters">
            <slot name="extra-filters"></slot>
          </div>
        </div>
        
        <!-- 展开/收起按钮 -->
        <div class="toggle-more-btn" v-if="hasMoreFilters">
          
            <div
              @click="toggleShowMore"
              class="toggle-button"
            >
              <ltw-icon
                :icon-code="!showMore ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                class="more"
              ></ltw-icon>
              {{ !showMore ? '更多筛选' : '收起' }}
        </div>
         
        </div>
      </div>
    </div>
    
    <!-- 筛选和重置按钮 -->
    <div class="filter-actions">
      <el-button @click="filter" type="primary" style="background:#5755FF">
        <ltw-icon icon-code="el-icon-search"></ltw-icon>
        {{ $t('筛选') }}
      </el-button>
      <el-button @click="exportFile" type="primary">
        <ltw-icon icon-code="el-icon-download"></ltw-icon>
        {{ $t('导出') }}
      </el-button>
      <el-button @click="reset" plain style="background:#FF6E6F;color:#fff">
        <ltw-icon icon-code="el-icon-refresh"></ltw-icon>
        {{ $t('重置') }}
      </el-button>
      
    </div>
  </div>
</template>
<script>
export default {
  name: 'BaseFilter',
  emits: ['filter', 'updateShowMore','init','exportFile'],
  data() {
    return {
      showMore: false
    }
  },
  computed: {
    // 计算属性来判断more-filters插槽是否有内容
    hasMoreFilters() {
      return !!this.$slots['more-filters']
    },
    hasExtraFilters() {
      return !!this.$slots['extra-filters']
    }
  },
  methods: {
    toggleShowMore() {
      this.showMore = !this.showMore
      this.$emit('updateShowMore', this.showMore)
    },
    filter() {
      this.$emit('filter')
    },
    reset(){
      this.$emit('init')
    },
    exportFile(){
      this.$emit('exportFile')
    }
  }
}
</script>
<style lang="scss" scoped>
.base-filter {
  margin-top:16px;
  background-color: #fff;
  border-radius: 4px;
  padding: 10px 0;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  
  .filter-content {
   
  }
  
  /* 包裹所有查询条件的容器 */
  .filter-conditions-wrapper {
    position: relative;
    border-radius: 4px;
    padding: 10px 0px;
    padding-right: 100px; /* 为右侧按钮留出空间 */
  }
  
  /* 展开/收起按钮 */
  .toggle-more-btn {
    position: absolute;
    top: 16px;
    right: 300px;
    
    .toggle-button {
      display: flex;
      align-items: center;
      font-size: 13px;
      white-space: nowrap;
      
      .more {
        margin-right: 4px;
      }
    }
  }
  
  .filter-row {
    display: flex;
    width: 100%;
    
    &:not(:last-child) {
      margin-bottom: 5px;
    }
    
    &.first-row {
      .filter-panel {
        flex: 1;
        min-width: 0; /* 防止内容溢出 */
      }
    }
  }
  
  .filter-panel {
    flex: 1;
    
    &.common-filters,
    &.more-filters,
    &.extra-filters {
      width: 100%;
    }
  }
  
  .filter-actions {
    position: absolute;
    top:23px;
    right: 20px;
    display: flex;
    gap: 8px;

    
    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 25px;
      line-height: 12px;
      font-size: 12px;
      
      .ltw-icon {
        margin-right: 4px;
      }
    }
  }
}

@media (max-width: 768px) {
  .base-filter {
    padding: 12px;
    
    .filter-conditions-wrapper {
      padding: 12px;
      padding-right: 12px; /* 在移动端不需要为按钮留出右侧空间 */
    }
    
    .toggle-more-btn {
      position: static;
      display: flex;
      justify-content: center;
      margin-top: 12px;
    }
    
    .filter-actions {
      flex-direction: column;
      width: 100%;
      
      .el-button {
        width: 100%;
        margin-left: 0 !important;
        margin-bottom: 8px;
        justify-content: center;
      }
    }
  }
}
</style>
