import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveBsTagGroup = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups', data, params})
export const updateBsTagGroup = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups', data, params})
export const deleteBsTagGroup = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups', params})
export const listBsTagGroup = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups', params})
export const listBsTagGroupSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/selections', params})
export const pageBsTagGroup = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/page', params})
export const getBsTagGroup = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/' + id})
export const treeListBsTagGroup = (params = {},fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/tree', params,fullLoading})
export const treeListBsTagGroupReq = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/tree/req', params})
export const reorderBsTagGroup = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/basic/bs_tag_groups/reorder', data, params})
