<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    @close="dialogClosed"
    append-to-body
    :draggable="true"
    class="add-task"
  >
    <el-form :inline="true" :model="form" ref="formRef">
      <el-form-item :label="$t('类型')" prop="type">
        <!--        clearable-->
        <el-select
          v-model="form.type"
          placeholder="请选择"
          class="supplier-code"
          filterable
          :disabled="dialogStatus === 'edit'"
          @change="changeType"
        >
          <el-option
            :disabled="checkedCode(item.code)"
            v-for="item in typeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input
          v-if="!formReadonly"
          textType="remark"
          :placeholder="$t('请输入名称')"
          v-model="searchName"
          clearable
          @clear="refresh"
          id="input-search"
        >
          <template #append>
            <el-button @click="refresh" id="el-icon-search">
              <ltw-icon icon-code="el-icon-search"></ltw-icon>
            </el-button>
          </template>
        </ltw-input>
      </el-form-item>
    </el-form>
    <el-table
      class="aio-table"
      :data="filterTableData"
      stripe
      row-key="url"
      ref="tableRef"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        header-align="left"
        align="left"
        type="selection"
        width="55"
        reserve-selection
        fixed="left"
      ></el-table-column>

      <el-table-column header-align="left" prop="name" :label="$t('名称')" show-overflow-tooltip></el-table-column>
      <el-table-column header-align="left" prop="path" :label="$t('地址')" show-overflow-tooltip></el-table-column>
      <el-table-column header-align="left" prop="repo" :label="$t('仓库')" show-overflow-tooltip></el-table-column>
      <el-table-column header-align="left" prop="size" :label="$t('文件大小')" show-overflow-tooltip width="100">
        <template #default="scope">
          <el-tag>{{ checkFileSize(scope.row.size) }}</el-tag>
        </template>
      </el-table-column>

      <!--      <el-table-column header-align="left" align="left" :label="$t('操作')" width="130" fixed="right">-->
      <!--        <template #default="scope">-->
      <!--          <el-button-group>-->
      <!--            <el-tooltip effect="dark" :content="$t('预览')" placement="top" :enterable="false">-->
      <!--              <el-button-->
      <!--                v-if="scope.row.url !== chooseUrl"-->
      <!--                type="primary"-->
      <!--                @click="previewTextFile(scope.row)"-->
      <!--                size="small"-->
      <!--              >-->
      <!--                <ltw-icon icon-code="el-icon-view"></ltw-icon>-->
      <!--              </el-button>-->
      <!--            </el-tooltip>-->
      <!--            <el-tooltip effect="dark" :content="$t('下载')" placement="top" :enterable="false">-->
      <!--              <el-button plain type="primary" @click="downloadFile(scope.row)" size="small">-->
      <!--                <ltw-icon icon-code="el-icon-download"></ltw-icon>-->
      <!--              </el-button>-->
      <!--            </el-tooltip>-->
      <!--          </el-button-group>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="submit" id="submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getAioConfigWhiteList } from '@/apis/data-collect/vt-daq-aio-config'
import { checkFileSize } from '@/plugins/util'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

const defaultform = {}
export default {
  name: 'AddSoftwareUpdate',
  emits: ['reload', 'previewTextFile', 'downloadFile'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {},
      typeList: [],
      tableData: [],
      filterTableData: [],
      checkedTypeList: [],
      checkFileSize,
      chooseUrl: [],
      searchName: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  created() {},
  methods: {
    async show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = this.$t('白名单')
      switch (row.type) {
        case 'add':
          this.checkedTypeList = row?.data?.checkedList
          break
        case 'edit':
          this.form.type = row.data.type
          this.chooseUrl = row.data.urlList
        // await this.changeType(this.form.type)
      }
      this.getTypes()
    },
    async getTypes() {
      if (!this.platformList?.length) {
        this.typeList = await listSysDictionary({ typeCode: 'aio_whitelist' }).then(res => res.data)
        if (this.typeList?.length) {
          if (this.dialogStatus === 'add') {
            this.form.type = this.typeList[0].code
          }
          await this.changeType(this.form.type)

          if (this.dialogStatus !== 'add') {
            this.tableData.forEach(item => {
              if (!!~this.chooseUrl.indexOf(item.url)) {
                this.$refs.tableRef.toggleRowSelection(item, true)
              }
            })
          }
        }
      }
    },
    async changeType(val) {
      const data = await getAioConfigWhiteList({ type: val }).then(res => res.data)
      this.tableData = data
      this.filterTableData = data
    },
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.selectedData = []
      this.checkedTypeList = []
      this.tableData = []
      this.filterTableData = []
      this.form = Object.assign({}, defaultform)
      this.searchName = ''
      this.chooseUrl = []
      this.$refs.tableRef.clearSelection()
    },
    checkedCode(code) {
      const flag = !!~this.checkedTypeList.findIndex(val => val === code)
      return flag
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    previewTextFile(row) {
      this.$emit('previewTextFile', row)
    },
    downloadFile(row) {
      this.$emit('downloadFile', row)
    },
    submit() {
      let postData = {
        type: this.form.type,
        data: this.tableData.filter(val => !!~this.selectedData.findIndex(select => select.url === val.url))
      }
      this.$emit('reload', JSON.parse(JSON.stringify(postData)))
      this.dialogVisible = false
    },
    refresh() {
      this.filterTableData = this.searchName
        ? this.tableData.filter(val => val.name.includes(this.searchName))
        : this.tableData
    }
  }
}
</script>

<style scoped lang="scss">
.aio-table {
  height: calc(100% - 50px);
}
</style>
