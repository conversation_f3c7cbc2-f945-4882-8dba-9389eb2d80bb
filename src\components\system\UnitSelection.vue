<template>
  <el-select
    v-model="selectValue"
    placeholder="Please select"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    class="unit-selection"
  >
    <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" :id="item.name"> </el-option>
  </el-select>
</template>

<script>
import { listSysUnit } from '@/apis/system/sys-unit'

export default {
  name: 'UnitSelection',
  props: {
    defaultSelection: String,
    unitType: String,
    modelValue: [String, Number],
    disabled: Boolean,
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      list: [],
      map: {},
      queryParam: {
        enabled: true
      }
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    data(val) {
      this.list = val
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  created() {
    if (this.autoLoad) {
      this.query()
    } else {
      this.list = this.data
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          if (this.defaultSelection === 'first' && !this.selectValue) {
            this.selectValue = this.list[0].id
          }
          if (this.defaultSelection === 'last' && !this.selectValue) {
            this.selectValue = this.list[this.list.length - 1].id
          }
          this.map[item.id] = item
        })
      }
    }
  },
  // watch: {
  // unitType: {
  //   handler() {
  //     if (this.unitType) {
  //       this.reload();
  //     }
  //   },
  //   immediate: true,
  // },
  // },
  methods: {
    query() {
      this.queryParam.typeCode = this.unitType
      listSysUnit(this.queryParam).then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          if (this.defaultSelection === 'first' && !this.selectValue) {
            this.selectValue = this.list[0].id
          }
          if (this.defaultSelection === 'last' && !this.selectValue) {
            this.selectValue = this.list[this.list.length - 1].id
          }
          this.map = {}
          this.list.forEach(item => {
            this.map[item.id] = item
          })
        }
      })
    },
    initialize() {
      if (this.list && this.list.length > 0) {
        if (this.defaultSelection === 'first' && !this.selectValue) {
          this.selectValue = this.list[0].id
        }
        if (this.defaultSelection === 'last' && !this.selectValue) {
          this.selectValue = this.list[this.list.length - 1].id
        }
      }
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      this.$emit('change', {
        value: value,
        node: this.map[value]
      })
      this.$emit('update:modelValue', value)
    },
    clear() {
      this.list = []
    },
    getNode(value) {
      return this.map[value]
    },
    getNodes() {
      return this.list
    }
  }
}
</script>

<style scoped></style>
