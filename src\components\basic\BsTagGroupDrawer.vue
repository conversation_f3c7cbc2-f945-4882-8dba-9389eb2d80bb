<template>
  <el-drawer
    custom-class="BsTagGroupDrawer"
    :title="title"
    v-model="modelVisible"
    direction="rtl"
    :before-close="handleClose"
    :size="500"
    append-to-body
  >
    <div class="query-form">
      <ltw-input
        :placeholder="$t('请输入关键字')"
        v-model="queryParam.key"
        clearable
        @clear="refresh"
        id="research-input"
      >
        <template #append>
          <el-button @click="refresh" id="el-icon-search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </template>
      </ltw-input>
    </div>
    <div class="drawer-body">
      <el-scrollbar>
        <template v-for="superGroup in filterTagGroupList" :key="superGroup.id">
          <!-- 超级组级别 -->
          <div
            class="super-group-container"
            v-if="isSuperGroup(superGroup)"
          >
            <div class="super-group-header">
              <el-checkbox
                :disabled="superGroup.disabled"
                v-model="superGroup.checkedAll"
                @change="handleGroupTagsAllChecked($event, superGroup)"
                :indeterminate="superGroup.isIndeterminate"
                id="tag-super-group-header"
                >{{ superGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
              </el-checkbox>
            </div>
            <div class="super-group-content">
              <template v-for="group in superGroup.children" :key="group.id">
                <div
                  class="group-container"
                  v-if="(group.children && group.children.length > 0) || (group.tagList && group.tagList.length > 0)"
                >
                  <div class="group-header">
                    <el-checkbox
                      :disabled="group.disabled"
                      v-model="group.checkedAll"
                      @change="handleGroupTagsAllChecked($event, group, superGroup)"
                      :indeterminate="group.isIndeterminate"
                      id="tag-group-header"
                      >{{ group[locale === 'zh' ? 'nameCn' : 'name'] }}
                    </el-checkbox>
                  </div>
                  <div class="group-content">
                    <div
                      class="sub-group-container"
                      v-if="group.children && group.children.length > 0"
                    >
                      <div class="sub-group" v-for="subGroup in group.children" :key="subGroup.id">
                        <el-divider content-position="left">
                          <el-checkbox
                            :disabled="subGroup.disabled"
                            v-model="subGroup.checkedAll"
                            @change="handleGroupTagsAllChecked($event, subGroup, group, superGroup)"
                            :indeterminate="subGroup.isIndeterminate"
                            id="tag--sub-group-header"
                            >{{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                          </el-checkbox>
                        </el-divider>
                        <div class="tag-container">
                          <el-checkbox-group
                            v-model="subGroup.checkedTagIdList"
                            @change="handleTagChange($event, subGroup, group, superGroup)"
                          >
                            <el-checkbox :label="tag.id" v-for="tag in subGroup.tagList" :key="tag.id" id="tag">
                              <el-row
                                >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                                <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                              </el-row>
                            </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                    <!-- 组级别的标签展示（仅当没有子组时） -->
                    <div class="tag-container" v-if="group.tagList && group.tagList.length > 0 && (!group.children || group.children.length === 0)">
                      <el-checkbox-group v-model="group.checkedTagIdList" @change="handleTagChange($event, group, superGroup)">
                        <el-checkbox :label="tag.id" v-for="tag in group.tagList" :key="tag.id" id="tag">
                          <el-row
                            >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                            <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                          </el-row>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </template>
              <!-- 超级组级别的标签展示（仅当没有子组时） -->
              <div class="tag-container" v-if="superGroup.tagList && superGroup.tagList.length > 0 && (!superGroup.children || superGroup.children.length === 0)">
                <el-checkbox-group v-model="superGroup.checkedTagIdList" @change="handleTagChange($event, superGroup)">
                  <el-checkbox :label="tag.id" v-for="tag in superGroup.tagList" :key="tag.id" id="tag">
                    <el-row
                      >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-row>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <!-- 兼容原有二级结构 -->
          <div
            class="group-container"
            v-else-if="(superGroup.children && superGroup.children.length > 0) || (superGroup.tagList && superGroup.tagList.length > 0)"
          >
            <div class="group-header">
              <el-checkbox
                :disabled="superGroup.disabled"
                v-model="superGroup.checkedAll"
                @change="handleGroupTagsAllChecked($event, superGroup)"
                :indeterminate="superGroup.isIndeterminate"
                id="tag-group-header"
                >{{ superGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
              </el-checkbox>
            </div>
            <div class="group-content">
              <div
                class="sub-group-container"
                v-if="(superGroup.children && superGroup.children.length > 0) || (superGroup.tagList && superGroup.tagList.length > 0)"
              >
                <div class="sub-group" v-for="subGroup in superGroup.children" :key="subGroup.id">
                  <el-divider content-position="left">
                    <el-checkbox
                      :disabled="subGroup.disabled"
                      v-model="subGroup.checkedAll"
                      @change="handleGroupTagsAllChecked($event, subGroup, superGroup)"
                      :indeterminate="subGroup.isIndeterminate"
                      id="tag--sub-group-header"
                      >{{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                    </el-checkbox>
                  </el-divider>
                  <div class="tag-container">
                    <el-checkbox-group
                      v-model="subGroup.checkedTagIdList"
                      @change="handleTagChange($event, subGroup, superGroup)"
                    >
                      <el-checkbox :label="tag.id" v-for="tag in subGroup.tagList" :key="tag.id" id="tag">
                        <el-row
                          >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                          <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                        </el-row>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
              <div class="tag-container" v-if="superGroup.tagList && superGroup.tagList.length > 0">
                <el-checkbox-group v-model="superGroup.checkedTagIdList" @change="handleTagChange($event, superGroup)">
                  <el-checkbox :label="tag.id" v-for="tag in superGroup.tagList" :key="tag.id" id="tag">
                    <el-row
                      >{{ tag[locale === 'zh' ? 'nameCn' : 'name'] }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-row>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <template v-if="matchFlag">
        <span style="font-size: 14px">{{ $t('标签匹配方式') }}：</span>
        <el-radio-group v-model="isOr" style="margin-right: 15px">
          <el-radio :label="true" id="single">{{ $t('单一匹配') }}</el-radio>
          <el-radio :label="false" id="all">{{ $t('全部匹配') }}</el-radio>
        </el-radio-group>
      </template>
      <el-button type="primary" @click="confirmDistributeTags" id="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-drawer>
</template>
<script setup>
import { treeListBsTagGroup, treeListBsTagGroupReq } from '@/apis/data-collect/bs-tag-group'
import { ref, reactive, toRefs, watch, computed } from 'vue'
import { getLocale } from '@/plugins/util'
import { i18n } from '@/plugins/lang'

import { ElCheckbox, ElDivider, ElCheckboxGroup, ElButton, ElDrawer, ElRadioGroup, ElRadio } from 'element-plus'
import LtwInput from '@/components/base/LtwInput'
import LtwIcon from '@/components/base/LtwIcon'

const props = defineProps({
  // 选中tag
  rowTagList: {
    type: Array,
    require: true,
    default: () => []
  },
  drawerVisible: {
    type: Boolean,
    required: true,
    default: false
  },
  requirementId: {
    type: String,
    default: ''
  },
  matchFlag: {
    type: Boolean,
    default: false
  },
  enabled: {
    type: Boolean,
    default: false
  },
  parentDisabled: {
    type: Boolean,
    default: false
  },
  singleSelect: {
    type: Boolean,
    default: false
  },
  apiType: {
    type: String,
    default: ''
  }
  // model: {
  //   type: String,
  //   default: 'priview'
  // }
})
// 初始化字段
// let { rowTagList, model, drawerVisible } = toRefs(props)
// tag列表数据
let queryParam = reactive({ key: '' })
let tagList = reactive({})
let tagGroupList = ref([])
let filterTagGroupList = ref([])
let modelVisible = ref(false)
let locale = getLocale()
let $t = ref(i18n.global.t)
// const { locale } = useI18n()
let title = ref('')
let isOr = ref(true)
let selectedNode = ref(null)
title = i18n.global.t('标签筛选')
watch(() => props.rowTagList, handleTagDistributeDrawerOpen)
watch(
  () => props.requirementId,
  () => {
    tagGroupList.value = []
  }
)
// modelVisible = computed(() => {
//   return props.drawerVisible
// })
modelVisible = computed({
  get: () => {
    return props.drawerVisible
  },
  set: value => {
    return props.drawerVisible
  }
})

// 判断是否为超级组（三级结构）
function isSuperGroup(item) {

  // 如果没有children，则不是超级组
  if (!item.children || item.children.length === 0) {
    return false
  }

  // 检查是否有子元素具有children属性（表示三级结构）
  // 三级结构：超级组 -> 组 -> 子组 -> 标签
  // 如果item的children中有任何一个还有children，则item是超级组
  return item.children.some(child =>
    child.children && child.children.length > 0
  )
}

// 页面tag管理
function handleTagDistributeDrawerOpen(val) {
  tagList = JSON.parse(JSON.stringify(val))
  if (!tagGroupList.value || tagGroupList.value.length === 0) {
    if (props.requirementId) {
      treeListBsTagGroupReq({ reqId: props.requirementId }).then(res => {
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else if (props.enabled) {
      let postData = {
        enabled: props.enabled,
      }
      if(props.apiType === 'poi'){
        postData.poi = true
        postData.ignoreEmptyLeafGroup = true
      }
      treeListBsTagGroup(postData).then(res => {
 
        tagGroupList.value = res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    } else {
      let postData = {}
      if(props.apiType === 'poi'){
        postData.poi = true
        postData.ignoreEmptyLeafGroup = true
      }
      treeListBsTagGroup(postData).then(res => {
        tagGroupList.value =  res.data
        filterTagGroup(tagGroupList.value)
        setCheckedList(filterTagGroupList.value)
      })
    }
  } else {
    filterTagGroup(tagGroupList.value)
    setCheckedList(filterTagGroupList.value)
  }
}

function filterTagGroup() {
  // 引用对象防止改变原数组
  let copyTagGroupList = JSON.parse(JSON.stringify(tagGroupList.value))
  if (queryParam.key) {
    let list = []
    copyTagGroupList.forEach(item => {
      if (isSuperGroup(item)) {
        // 处理三级结构：超级组 -> 组 -> 子组 -> 标签
        if (~item[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
          list.push(item)
        } else {
          let groupList = []
          if (item.children && item.children.length) {
            item.children.forEach(group => {
              if (~group[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                groupList.push(group)
              } else {
                let subGroupList = []
                if (group.children && group.children.length) {
                  group.children.forEach(subGroup => {
                    if (~subGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                      subGroupList.push(subGroup)
                    } else {
                      let tagList = []
                      if (subGroup.tagList && subGroup.tagList.length) {
                        subGroup.tagList.forEach(tag => {
                          if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                            tagList.push(tag)
                          }
                        })
                      }
                      subGroup.tagList = tagList
                      if (subGroup.tagList && subGroup.tagList.length) {
                        subGroupList.push(subGroup)
                      }
                    }
                  })
                }
                // 检查组级别的tagList（仅当没有子组时）
                if (group.tagList && group.tagList.length && (!group.children || group.children.length === 0)) {
                  let groupTagList = []
                  group.tagList.forEach(tag => {
                    if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                      groupTagList.push(tag)
                    }
                  })
                  group.tagList = groupTagList
                }
                group.children = subGroupList
                if ((group.children && group.children.length) || (group.tagList && group.tagList.length)) {
                  groupList.push(group)
                }
              }
            })
          }
          // 检查超级组级别的tagList（仅当没有子组时）
          if (item.tagList && item.tagList.length && (!item.children || item.children.length === 0)) {
            let superGroupTagList = []
            item.tagList.forEach(tag => {
              if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                superGroupTagList.push(tag)
              }
            })
            item.tagList = superGroupTagList
          }
          item.children = groupList
          if ((item.children && item.children.length) || (item.tagList && item.tagList.length)) {
            list.push(item)
          }
        }
      } else {
        // 处理二级结构：组 -> 子组 -> 标签（保持原有逻辑）
        if (~item[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
          list.push(item)
        } else {
          let subGroupList = []
          if (item.children && item.children.length) {
            item.children.forEach(subGroup => {
              if (~subGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                subGroupList.push(subGroup)
              } else {
                let tagList = []
                if (subGroup.tagList && subGroup.tagList.length) {
                  subGroup.tagList.forEach(tag => {
                    if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                      tagList.push(tag)
                    }
                  })
                }
                subGroup.tagList = tagList
                if (subGroup.tagList && subGroup.tagList.length) {
                  subGroupList.push(subGroup)
                }
              }
            })
          }
          // 检查组级别的tagList（仅当没有子组时）
          if (item.tagList && item.tagList.length && (!item.children || item.children.length === 0)) {
            let groupTagList = []
            item.tagList.forEach(tag => {
              if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                groupTagList.push(tag)
              }
            })
            item.tagList = groupTagList
          }
          item.children = subGroupList
          if ((item.children && item.children.length) || (item.tagList && item.tagList.length)) {
            list.push(item)
          }
        }
      }
    })
    filterTagGroupList.value = list
  } else {
    // saveCheckedTagList({ children: filterTagGroupList.value })
    filterTagGroupList.value = copyTagGroupList
  }
}

function saveCheckedTagList(group) {
  if (group.children && group.children.length > 0) {
    group.children.forEach(subGroup => {
      saveCheckedTagList(subGroup)
    })
  } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
    group.checkedTagIdList.forEach(tagId => {
      if (!~tagList.findIndex(val => val.id === tagId)) {
        tagList.push({
          ...group.tagMap[tagId]
        })
      }
    })
  }
}

function setCheckedList(list) {
  let checkedCount = 0
  if (tagList && tagList.length) {
    let tagMap = {}
    tagList.forEach(tag => {
      tagMap[tag.id] = tag
    })
    list.forEach(item => {
      if (props.parentDisabled) {
        disabledParentNode(item)
      }
      item.checkedAll = false
      item.isIndeterminate = false

      // 处理当前级别的标签
      if (item.tagList && item.tagList.length > 0) {
        item.checkedTagIdList = []
        item.tagMap = {}
        item.tagList.forEach(tag => {
          item.tagMap[tag.id] = tagMap[tag.id] || tag
          if (tagMap[tag.id]) {
            item.checkedTagIdList.push(tag.id)
            tag.checked = true
          } else {
            tag.checked = false
          }
        })
        if (item.checkedTagIdList.length > 0) {
          checkedCount++
          if (item.checkedTagIdList.length === item.tagList.length) {
            item.checkedAll = true
          } else {
            item.isIndeterminate = true
          }
        }
      }

      // 递归处理子级别
      if (item.children && item.children.length > 0) {
        let childrenCheckedCount = setCheckedList(item.children)
        let hasCheckedTags = item.checkedTagIdList && item.checkedTagIdList.length > 0

        // 计算当前节点的选中状态
        if (childrenCheckedCount === item.children.length && (!item.tagList || hasCheckedTags)) {
          item.checkedAll = true
          item.isIndeterminate = false
          checkedCount++
        } else if (childrenCheckedCount > 0 || hasCheckedTags) {
          item.checkedAll = false
          item.isIndeterminate = true
          checkedCount++
        } else {
          item.checkedAll = false
          item.isIndeterminate = false
        }
      }
    })
  } else {
    list.forEach(item => {
      if (props.parentDisabled) {
        disabledParentNode(item)
      }
      item.checkedAll = false
      item.isIndeterminate = false
      if (item.tagList && item.tagList.length > 0) {
        item.checkedTagIdList = []
        item.tagMap = {}
        item.tagList.forEach(tag => {
          item.tagMap[tag.id] = tag
        })
      }
      if (item.children && item.children.length > 0) {
        setCheckedList(item.children)
      }
    })
  }
  return checkedCount
}

function disabledParentNode(item) {
  if ((item.children && item.children.length > 0) || item.tagList) {
    item.disabled = true
  } else {
    item.disabled = false
  }
}

// 页面tag全选操作
function handleGroupTagsAllChecked(val, group, parentGroup, superParentGroup) {
  if (group.tagList && group.tagList.length > 0) {
    group.isIndeterminate = false
    group.checkedTagIdList = []
    group.checkedAll = val
    if (val) {
      group.tagList.forEach(tag => {
        group.checkedTagIdList.push(tag.id)
      })
    }
  }
  if (group.children && group.children.length > 0) {
    group.isIndeterminate = false
    group.children.forEach(subGroup => {
      handleGroupTagsAllChecked(val, subGroup, group, parentGroup)
    })
  }

  // 更新父级状态
  if (parentGroup) {
    updateParentGroupStatus(parentGroup)
  }

  // 更新超级父级状态
  if (superParentGroup) {
    updateParentGroupStatus(superParentGroup)
  }
}

// 更新父级组状态的通用函数
function updateParentGroupStatus(parentGroup) {
  if (parentGroup.children && parentGroup.children.length > 0) {
    // 对于有children的中间节点，只检查子级状态
    let allChildrenChecked = true
    let hasCheckedChildren = false

    for (let child of parentGroup.children) {
      if (!child.checkedAll) {
        allChildrenChecked = false
      }
      if (child.checkedAll || child.isIndeterminate) {
        hasCheckedChildren = true
      }
    }

    // 中间节点的状态只依赖子级
    parentGroup.checkedAll = allChildrenChecked
    parentGroup.isIndeterminate = hasCheckedChildren && !allChildrenChecked
  } else {
    // 对于叶子节点，检查自身的标签
    if (parentGroup.tagList && parentGroup.tagList.length > 0) {
      let checkedCount = parentGroup.checkedTagIdList ? parentGroup.checkedTagIdList.length : 0
      parentGroup.checkedAll = checkedCount === parentGroup.tagList.length
      parentGroup.isIndeterminate = checkedCount > 0 && checkedCount < parentGroup.tagList.length
    } else {
      // 没有标签的叶子节点
      parentGroup.checkedAll = false
      parentGroup.isIndeterminate = false
    }
  }
}

function cancelCheckBox(list) {
  list.forEach(item => {
    if (item.tagList && item.tagList.length > 0) {
      item.tagList.forEach(tag => {
        if (tag.id !== val[val.length - 1]) {
          const index = group.checkedTagIdList.indexOf(tag.id)
          if (index !== -1) {
            group.checkedTagIdList.splice(index, 1)
          }
        }
      })
    }
    if (item.children && item.children.length > 0) {
      cancelCheckBox(item.children, val, group)
    }
  })
}

function uncheckNode(node) {
  node.checkedTagIdList = [] // 取消当前节点的选中状态
  if (node.children && node.children.length > 0) {
    // 如果当前节点有子节点，则递归取消子节点的选中状态
    for (const childNode of node.children) {
      uncheckNode(childNode)
    }
  }
}

// 页面tag单选操作
function handleTagChange(val, group, parentGroup, superParentGroup) {
  const checkedCount = val.length
  group.checkedAll = checkedCount === group.tagList.length
  group.isIndeterminate = checkedCount > 0 && checkedCount < group.tagList.length

  if (props.singleSelect) {
    if (val.length > 0) {
      // 遍历filterTagGroupList，取消所有复选框的选中状态
      filterTagGroupList.value.forEach(item => {
        uncheckNode(item)
      })
      // 设置当前节点的勾选状态
      if (group.checkedTagIdList.indexOf(val[val.length - 1]) === -1) {
        group.checkedTagIdList.push(val[val.length - 1])
      }
    }
  }

  // 更新父级状态
  if (parentGroup) {
    updateParentGroupStatus(parentGroup)
  }

  // 更新超级父级状态
  if (superParentGroup) {
    updateParentGroupStatus(superParentGroup)
  }
}

// 注册事件
const emit = defineEmits(['drawer-click'])

function handleClose() {
  emit('drawer-click')
}

function confirmDistributeTags() {
  if (queryParam.key) {
    queryParam.key = ''
    refresh()
  }
  emit('drawer-click', { tagList: filterTagGroupList.value, isOr })
}

function refresh() {
  saveCheckedTagList({ children: filterTagGroupList.value })
  filterTagGroup(tagGroupList.value)
  setCheckedList(filterTagGroupList.value)
}
</script>
<style lang="scss">
.BsTagGroupDrawer {
  .el-drawer__body {
    height: calc(100vh - 125px);
    padding-right: 10px;

    .query-form {
      margin-bottom: 10px;
    }

    .drawer-body {
      height: calc(100% - 92px);
      overflow-y: auto;

      // 超级组容器样式
      .super-group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        box-shadow: var(--el-box-shadow-light);
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
        margin-top:10px;


        .super-group-header {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;

          .el-checkbox {
            font-weight: bold;
            font-size: 16px;
          }
        }

        .super-group-content {
          padding-left: 10px;
        }

        .el-checkbox {
          margin-right: 15px;
        }
      }

      .group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        border-radius: 4px;
        padding: 10px;
        box-shadow: var(--el-box-shadow-light);
        margin-bottom: 10px;

        .group-header {
          display: flex;
          justify-content: center;
          align-items: center;

          .el-checkbox {
            font-weight: bold;
          }
        }

        .el-checkbox {
          margin-right: 15px;
        }
      }
    }

    .drawer-footer {
      height: 50px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
</style>
