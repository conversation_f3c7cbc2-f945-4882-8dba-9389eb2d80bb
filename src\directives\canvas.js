import { ParkingSlot_Avilable_Type } from '@/plugins/enum/ParkingSlot_Avilable_Type.js'
import { ParkingSlot_Function_Type } from '@/plugins/enum/ParkingSlot_Function_Type.js'
import { ParkingSlot_Occupy_Type } from '@/plugins/enum/ParkingSlot_Occupy_Type.js'
import { ParkingSlot_Shape_Type } from '@/plugins/enum/ParkingSlot_Shape_Type.js'
import { ParkingSlot_Material_Type } from '@/plugins/enum/ParkingSlot_Material_Type.js'
import { ParkingSlot_Corner_Shape_Type } from '@/plugins/enum/ParkingSlot_Corner_Shape_Type.js'
let foundCanvas = (function () {
  let localBox = []

  // 创建画布
  function _createCanvas(obj, id, config, times) {
    if (!document.getElementById(id)) {
      console.log(`标签id:${id},不存在`)
      return
    }
    let div = document.getElementById(id)
    let divSize = {
      w: div.offsetWidth, // 返回元素的总宽度
      h: div.offsetHeight // 返回元素的总高度
    }
    // 创建canvas标签
    let canvas = document.createElement('canvas')
    let canvasId = 'canvas' + id
    canvas.id = canvasId
    div.appendChild(canvas)

    let img = document.getElementById(canvasId)
    img.width = divSize.w
    img.height = divSize.h
    _initCanvas(divSize, obj, canvasId, config, times)
  }

  // 初始化 显示画布
  function _initCanvas(divSize, obj, id, config, times) {
    let canvasObj = JSON.parse(JSON.stringify(obj.targetList))
    let conv = document.getElementById(id)
    let ctx = conv.getContext('2d')
    ctx.clearRect(0, 0, conv.width, conv.height)
    let imgwidth // 图片宽度
    let imgheight // 图片高度
    let imgObj = new Image() // 创建image对象
    imgObj.src = obj.src

    imgObj.onload = function () {
      // 设置画布大小
      imgwidth = imgObj.width
      imgheight = imgObj.height
      document.getElementById(id).style.offsetWidth = divSize.w
      document.getElementById(id).style.offsetHeight =
        (divSize.w * imgheight) / imgwidth
      conv.width = divSize.w
      conv.height = (divSize.w * imgheight) / imgwidth
      ctx.drawImage(
        this,
        90 * times,
        90 * times,
        divSize.w - 180 * times,
        ((conv.height - 180 * times) * imgheight) / imgwidth
      )
      canvasObj = _calculateSize(divSize, canvasObj, imgwidth, imgheight, times)
      _drawOldRecs(canvasObj, config)
    }
    // 计算比例尺寸显示适配
    function _calculateSize(divSize, obj, imgwidth, imgheight, times) {
      let widthImgCanvasPercentage // 宽度比例
      let heightImgCanvasPercentage // 高度比例
      let subWidth = (divSize.w - 180 * times) / imgwidth // 算出显示比例
      let subHeight = (conv.height - 180 * times) / imgheight
      widthImgCanvasPercentage = subWidth.toString()
      heightImgCanvasPercentage = subHeight.toString()

      obj.forEach(element => {
        element.graphicsList.forEach(target => {
          target.coordinates.forEach(item => {
            item.x = Math.round(item.x * widthImgCanvasPercentage) + 90 * times
            item.y = Math.round(item.y * heightImgCanvasPercentage) + 90 * times
          })
        })
      })
      return obj
    }
    function _drawText(ctx, target, item) {
      if (target.targetType === 'parking_slot') {
        Object.keys(target).forEach(tar => {
          if (tar !== 'target_type' && tar !== 'graphicsList') {
            switch (tar) {
              case 'availableType':
                if (target[tar] === '0') return
                else {
                  ctx.fillStyle = 'white'
                  ctx.fillText(
                    ParkingSlot_Avilable_Type[target[tar]],
                    item.coordinates[0].x + 3,
                    item.coordinates[0].y - 10
                  )
                }
                break
              case 'functionType':
                if (target[tar] === '0' || target[tar] == '8') return
                else {
                  ctx.fillStyle = 'white'
                  ctx.fillText(
                    ParkingSlot_Function_Type[target[tar]],
                    item.coordinates[0].x + 3,
                    item.coordinates[0].y + 10
                  )
                }
                break
              case 'materialType':
                if (target[tar] === '4') return
                else {
                  ctx.fillStyle = 'white'
                  ctx.fillText(
                    ParkingSlot_Material_Type[target[tar]],
                    item.coordinates[0].x + 3,
                    item.coordinates[1].y + 20,
                    70
                  )
                }
                break
              case 'occupyType':
                if (target[tar] === '0' || target[tar] == '6') return
                else {
                  ctx.fillStyle = 'white'
                  ctx.fillText(
                    ParkingSlot_Occupy_Type[target[tar]],
                    item.coordinates[0].x + 3,
                    item.coordinates[0].y + 30
                  )
                }
                break
              case 'shapeType':
                ctx.fillStyle = 'white'
                ctx.fillText(
                  ParkingSlot_Shape_Type[target[tar]],
                  item.coordinates[0].x + 3,
                  item.coordinates[0].y + 40
                )
                break
            }
          }
        })
      } else {
        let n = 1
        Object.keys(target).forEach(tar => {
          if (tar !== 'targetType' && tar !== 'graphicsList') {
            ctx.fillStyle = 'white'
            if(tar==='ignore'){
              if(target[tar]){
                ctx.fillText(
                 'ignore',
                  item.coordinates[0].x + 3,
                  item.coordinates[0].y + 10 * (n ++)
                )
              }
            }else{
              ctx.fillText(
                target[tar],
                item.coordinates[0].x + 3,
                item.coordinates[0].y + 10 * (n ++)
              )
            }

          }
        })
      }
    }
    function _drawCornerText(ctx, coor, cornerShapeType) {
      if (ParkingSlot_Corner_Shape_Type[cornerShapeType] && cornerShapeType!==6) {
        ctx.fillStyle = 'white'
        ctx.fillText(ParkingSlot_Corner_Shape_Type[cornerShapeType], coor.x+2, coor.y-2)
      }
    }
    // 画框
    function _drawOldRecs(targetList, config) {
      if (targetList.length == 0) {
        return
      }
      for (let target of targetList) {
        if (target.targetType === 'key_point') {
          target.graphicsList.forEach(item => {
            item.coordinates.forEach(coor => {
              ctx.save()
              ctx.fillStyle = 'red'
              ctx.beginPath()
              ctx.arc(coor.x, coor.y, 2, 0, 2 * Math.PI)
              ctx.closePath()
              ctx.fill()
            })
          })
        } else {
          target.graphicsList.forEach(item => {
            ctx.lineWidth = config.lineWidth
            ctx.strokeStyle = config.strokeStyle
            ctx.beginPath()
            ctx.fillStyle = 'red'
            ctx.moveTo(item.coordinates[0].x, item.coordinates[0].y)
            if (item.coordinates[0].additionalData) {
              _drawCornerText(
                ctx,
                item.coordinates[0],
                item.coordinates[0].additionalData.cornerShapeType
              )
            }
            item.coordinates.forEach((coor, index) => {
              if (index !== 0) {
                ctx.lineTo(coor.x, coor.y)
                if (coor.additionalData) {
                  _drawCornerText(
                    ctx,
                    coor,
                    coor.additionalData.cornerShapeType
                  )
                }
              }
            })
            ctx.closePath()
            ctx.font = '10px serif'
            _drawText(ctx, target, item)
            ctx.stroke()
          })
        }
      }
    }
  }

  // js入口，监听屏幕
  function _prepareCanvas(obj, id, config, times) {
    localBox = JSON.parse(JSON.stringify(obj))
    _delCanvas(obj,id)
    let conf = _isConfig(config)
    _createCanvas(obj, id, conf, times)
    // 执行一次
    window.addEventListener('load', function () {
      // 窗口改变执行
      window.addEventListener('resize', function () {
        localBox = JSON.parse(JSON.stringify(obj))
        _delCanvas(obj, id)
        //   // 重新创建画布
        _createCanvas(localBox, id, conf, times)
      })
    })
  }

  // 判断是否配置
  function _isConfig(config) {
    let conf = {}
    // 配置判断
    if (config && config != '') {
      conf = Object.assign({}, config)
    } else {
      conf = {
        lineWidth: 1, // 画框线粗细
        strokeStyle: 'red' // 画框线颜色
      }
    }
    return conf
  }
  // 删除画布canvas标签
  function _delCanvas(obj, id) {
    localBox = JSON.parse(JSON.stringify(obj))
    // 判断标签容器是否存在
    if (!document.getElementById(id)) {
      console.log(`标签id:${id},不存在`)
      return
    }
    // 删除画布
    let div = document.getElementById(id)
    let canvasCount = div.getElementsByTagName('canvas')

    if (canvasCount.length > 0) {
      for (let i = 0; i < localBox.length; i++) {
        document.getElementById(id).remove()
      }
    }
  }
  return {
    prepareCanvas: _prepareCanvas
  }
})()

export default foundCanvas
