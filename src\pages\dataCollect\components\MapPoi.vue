<template>
  <div class="map-selection-container intersection">
    <div class="map-content">
      <div class="query-param top">
        <div class="label">
          <el-button size="default" type="primary" @click="openQueryParams()" id="tag" :disabled="disabled">
            筛选
            <ltw-icon :class="{ visible: filterForm }" icon-code="el-icon-d-arrow-right"></ltw-icon>
          </el-button>
        </div>
        <div class="form" :class="{ visible: filterForm }">
          <div class="tag-form-item">
            标签
            <el-link @click="getTags()" style="margin-right: 20px" type="primary" :underline="false"
              >{{ (queryParam.tagList && queryParam.tagList.length) || 0 }}
            </el-link>
            <el-button size="default" @click="tagChoose()" id="tag" :disabled="disabled">
              <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              <!--              {{ $t('选择') }}-->
            </el-button>
            <el-button size="default" @click="resetTag()" :disabled="disabled" id="el-icon-close">
              <ltw-icon icon-code="el-icon-close"></ltw-icon>
              {{ $t('重置') }}
            </el-button>
          </div>
          <div class="canton-select">
            <el-cascader
              clearable
              filterable
              popper-class="canton-list"
              v-model="queryParam.cantonCode"
              :placeholder="$t('请选择区域')"
              :options="cantonCodeList"
              :props="props"
            />
          </div>
          <div class="search-container">
            <el-select
              class="search-tx-marker"
              v-if="!disabled"
              size="default"
              clearable
              v-model="queryParam.key"
              remote
              :remote-method="positioningByAddress"
              :loading="queryLoading"
              :placeholder="$t('示例:39.90812,116.397484/苏州市现代大道')"
              filterable
              @change="selectSuggestionHandle"
            >
              <el-option v-for="item in suggestionList" :key="item.id" :label="item.title" :value="item.id">
                <span style="float: left">{{ item.title }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                  {{
                    (item.province || '') +
                    (item.city ? ',' + item.city : '') +
                    (item.district ? ',' + item.district : '')
                  }}
                </span>
              </el-option>
            </el-select>
            <el-button size="default" @click="search">
              <ltw-icon icon-code="el-icon-search"></ltw-icon>
            </el-button>
          </div>
        </div>
      </div>
      <div class="query-param tools" v-if="geoType === 'point'">
        <div class="label">
          <el-button size="default" type="primary" @click="openTools()" id="tag" :disabled="disabled">
            工具栏
            <ltw-icon :class="{ visible: filterTools }" icon-code="el-icon-d-arrow-right"></ltw-icon>
          </el-button>
        </div>
        <div class="form" :class="{ visible: filterTools }">
          <div
            @click="setDrawType(item.type)"
            v-for="item in drawTools"
            :key="item.type"
            class="tool-item"
            :class="[item.type, { active: drawType === item.type }]"
          ></div>
          <div @click="deleteMapShape()" class="tool-item delete"></div>
        </div>
      </div>
      <div class="opt-list">
        <div
          class="opt-btn"
          v-for="item in geoTypes"
          :key="item.value"
          :class="{ active: geoType === item.value }"
          @click="changeGeoType(item.value)"
        >
          <ltw-icon :icon-code="getIconCode(item.value)"></ltw-icon>
          {{ item.name }}
          <!--          <el-dropdown @command="changeGeoType">-->
          <!--            <div class="btn-text">-->
          <!--              <ltw-icon :icon-code="getIconCode(geoType)"></ltw-icon>-->
          <!--              <div>{{ geoTypeName }}</div>-->
          <!--            </div>-->
          <!--            <template #dropdown>-->
          <!--              <el-dropdown-menu class="menu">-->
          <!--                <el-dropdown-item-->
          <!--                  v-for="item in geoTypes"-->
          <!--                  :key="item"-->
          <!--                  :command="item"-->
          <!--                  :class="{ active: geoType === item.value }"-->
          <!--                >-->
          <!--                  <ltw-icon :icon-code="getIconCode(item.value)"></ltw-icon>-->
          <!--                  {{ item.name }}-->
          <!--                </el-dropdown-item>-->
          <!--              </el-dropdown-menu>-->
          <!--            </template>-->
          <!--          </el-dropdown>-->
        </div>
      </div>
      <div class="btn-add-poi">
        <el-button @click="addPoi()" type="primary" size="default" :disabled="disabled">
          <ltw-icon class="icon-add-poi" icon-code="el-icon-upload"></ltw-icon>
        </el-button>
      </div>
      <!--      <div class="map-overview" :id="mapId" ref="mapContainerRef"></div>-->
      <base-map
        ref="baseMap"
        @change-bounds="changeBounds"
        @click-marker="clickMarker"
        @click-area="clickArea"
        @click-route="clickRoute"
        @draw-complete="drawComplete"
        @delete-complete="deleteComplete"
        @adjust-complete="adjustComplete"
        @select-complete="selectComplete"
        @cluster-changed="clusterChanged"
      ></base-map>
    </div>
    <el-card class="choosed-intersection">
      <template #header>已选POI</template>
      <div class="body">
        <el-scrollbar class="selected-container">
          <div class="total">
            <div class="label">Total</div>
            <div class="num">{{ markersBoardList?.length || 0 }}</div>
          </div>
          <el-divider />
          <div class="list" v-for="(value, key) in formatPoiObj" :key="key">
            <div class="label">{{ key }}</div>
            <div class="num">{{ value || 0 }}</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="footer" v-if="!disabled">
        <el-button size="small" @click="clearIntersection()">清除</el-button>
        <el-button size="small" type="primary" @click="save()">确认</el-button>
      </div>
    </el-card>

    <bs-tag-group-drawer
      @drawerClick="confirmDistributeTags"
      :drawerVisible="tagChooseDrawerVisible"
      :rowTagList="tagList"
      apiType="poi"
    ></bs-tag-group-drawer>
    <el-dialog v-model="tagCardVisible">
      <bs-tag-group-panel
        :editTags="editTags"
        :timeEdit="timeEdit"
        :data="tagList"
        @tagClose="tagClose"
        @tagSave="tagSave"
        :classificationTag="true"
      ></bs-tag-group-panel>
    </el-dialog>
  </div>
</template>

<script>
import {
  latLngPoint,
  isLatitudeLongitude,
  searchSuggestionAPI,
  isPointInPolygon,
  initClusterBubble
} from '@/plugins/map/TxMap'
import { showConfirmToast, showToast } from '@/plugins/util'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'
import LtwIcon from '@/components/base/LtwIcon.vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { getPoiList } from '@/apis/data-collect/vt-daq-task'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import BaseMap from '@/components/map/BaseMap.vue'
import { getSysCantonTree } from '@/apis/system/sys-canton'

let mapObj = {}
export default {
  name: 'MapPoi',
  components: {
    LtwInput,
    LtwIcon,
    BsTagGroupDrawer,
    BsTagGroupPanel,
    BaseMap
  },
  props: {
    propDisabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propDisabled: {
      handler(val) {
        this.disabled = val
      },
      deep: true,
      immediate: true
    },
    markersBoardList: {
      handler(val) {
        let obj = {}
        val?.forEach(val => {
          val.itemName = val.itemName || '无tag名'
          if (obj[val.itemName]) {
            obj[val.itemName]++
          } else {
            obj[val.itemName] = 1
          }
        })
        this.formatPoiObj = obj
      },
      deep: true,
      immediate: true
    }
  },
  emits: ['reload'],
  data() {
    return {
      OVER_LAY_TYPE,
      mapId: 'map',
      queryParam: {},
      bounds: [],
      queryLoading: false,
      suggestionList: [],
      // windowinfo传值

      disabled: false,
      filterForm: false,
      filterTools: false,
      drawType: '',
      drawTools: [
        {
          type: 'polygon'
        },
        {
          type: 'circle'
        }
        // {
        //   type: 'ellipse'
        // },
        // {
        //   type: 'rectangle'
        // }
      ],
      // 右侧面板marker
      markersBoardList: [],
      //总markers
      mapMarkers: [],
      selectedStyle: 'selectedStyle',
      // 高亮点位
      activeMarkerList: [],
      tagChooseDrawerVisible: false,
      tagList: [],
      tagCardVisible: false,
      editTags: false,
      timeEdit: false,
      treeTagGroupList: [],
      polygonSelected: false,
      formatPoiObj: {},
      //高亮点击点位，可被拖拽区域取消
      activeClickMarkerList: [],
      cantonCodeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
        // checkStrictly: true
      },
      geoType: 'point',
      geoTypes: [
        {
          name: '点',
          value: 'point'
        },
        {
          name: '线',
          value: 'line'
        },
        {
          name: '面',
          value: 'area'
        },
        {
          name: '聚合',
          value: 'cluster'
        }
      ],
      poiList: [],
      clusterBubbleList: []
    }
  },
  methods: {
    async show(row) {
      await this.$refs.baseMap.show()
      if (row?.data?.length) {
        if (row.fileType === 'req') {
          this.activeClickMarkerList = JSON.parse(JSON.stringify(row.data))
          this.activeMarkerList = JSON.parse(JSON.stringify(row.data))
        } else {
          this.markersBoardList = row.data
        }
        this.$refs.baseMap.showFitView(row.data)
      }
    },
    changeBounds(bounds) {
      if (!this.queryParam.cantonCode?.length) {
        this.bounds = bounds
        const postData = {
          targetCrs: 1,
          intersectingArea: { type: 'Polygon', userData: { crs: 1 }, coordinates: [bounds] },
          tagCodes: this.queryParam.tagList?.map(val => val.code)?.join(','),
          cantonCode: this.queryParam.cantonCode?.length
            ? this.queryParam.cantonCode[this.queryParam.cantonCode?.length - 1]
            : ''
        }
        this.getPoiList(postData)
      }
    },
    clearPoiMap() {
      this.$refs.baseMap.clearObjects('globalMarker', 'globalPolylineLayer', 'globalPolygonLayer')
      this.clusterBubbleList.forEach(item => {
        item.destroy()
      })
      this.clusterBubbleList = []
    },
    getPoiList(postData) {
      getPoiList(postData).then(async res => {
        this.clearPoiMap()
        if (!res.data.length) {
          showToast('该区域暂无POI', 'warning')
          return
        }
        this.poiList = res.data

        switch (this.geoType) {
          case 'point':
            await this.drawPoints(this.queryParam.cantonCode?.length)
            break
          case 'line':
            this.drawLines()
            break
          case 'area':
            this.drawAreas()
            break
          case 'cluster':
            this.drawCluster()
            break
        }
        this.drawType = ''
      })
    },
    clickArea() {
      showToast('仅支持点模式下选取', 'warning')
    },
    clickRoute() {
      showToast('仅支持点模式下选取', 'warning')
    },
    clickMarker(evt) {
      if (!this.disabled) {
        if (this.geoType === 'point') {
          const indexClickMarker = this.mapMarkers.findIndex(val => val.itemKey === evt.geometry.id)
          const indexMarkerBoard = this.markersBoardList.findIndex(
            val => val?.itemKey === this.mapMarkers[indexClickMarker].itemKey
          )
          const indexActiveMarkerList = this.activeMarkerList.findIndex(val => val.itemKey === evt.geometry.id)
          const indexActiveClickMarker = this.activeClickMarkerList.findIndex(
            val => val.itemKey === this.mapMarkers[indexClickMarker].itemKey
          )
          // 右侧面板没有
          if (!~indexMarkerBoard) {
            // 不在当前激活marker中
            if (!~indexActiveMarkerList) {
              // 记录点击的缓存marker
              if (!~indexActiveClickMarker) {
                this.activeClickMarkerList.push(this.mapMarkers[indexClickMarker])
              }
              //点击marker添加到左侧地图
              this.activeMarkerList.push(this.mapMarkers[indexClickMarker])
            } else {
              // 记录点击的缓存marker
              if (~indexActiveClickMarker) {
                this.activeClickMarkerList.splice(indexActiveClickMarker, 1)
              }
              this.activeMarkerList.splice(indexActiveMarkerList, 1)
            }
          } else {
            if (~indexActiveMarkerList) {
              this.activeMarkerList.splice(indexActiveMarkerList, 1)
            }
            this.markersBoardList.splice(indexMarkerBoard, 1)
          }
          const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')

          const marker = globalMarker.geometries[indexClickMarker]
          marker.styleId = marker.styleId === this.selectedStyle ? evt.geometry.id : this.selectedStyle
          globalMarker.updateGeometries(marker)
        } else {
          showToast('仅支持点模式下选取', 'warning')
        }
      }
    },
    removeMarkerStatus(geometry) {
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')
      const markersGeometries = globalMarker?.geometries
      markersGeometries?.forEach(val => {
        const indexMarker = this.activeMarkerList.findIndex(marker => marker.itemKey === val.id)
        const indexBoard = this.markersBoardList.findIndex(marker => marker.itemKey === val.id)
        let isInArea
        if (Array.isArray(geometry)) {
          isInArea = ~geometry.findIndex(geo => isPointInPolygon(val.position, geo))
        } else {
          isInArea = isPointInPolygon(val.position, geometry)
        }
        if (isInArea) {
          //删除当前激活点位
          this.activeMarkerList.splice(indexMarker, 1)
          //在右侧面板中也删除
          if (~indexBoard) {
            this.markersBoardList.splice(indexBoard, 1)
          }
          val.styleId = val.id
        }
      })
      globalMarker.updateGeometries(markersGeometries)
    },
    updateMarkerStatus(geometries) {
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')
      const markersGeometries = globalMarker?.geometries
      markersGeometries?.forEach(val => {
        let isInArea
        if (Array.isArray(geometries)) {
          isInArea = ~geometries.findIndex(geo => isPointInPolygon(val.position, geo))
        }
        //是否已选择到右侧面板上
        const isOnBoard = ~this.markersBoardList.findIndex(boardItem => boardItem.itemKey === val.id)
        const marker = this.mapMarkers.find(marker => marker.itemKey === val.id)
        const indexActiveMarkerList = this.activeMarkerList.findIndex(val => val.itemKey === marker.itemKey)
        if (isInArea || isOnBoard) {
          //添加当前激活点位
          if (!~indexActiveMarkerList) {
            this.activeMarkerList.push(marker)
          }
          val.styleId = this.selectedStyle
        } else {
          // 不在区域内但是在高亮markerList中

          // 点击后高亮的marker依旧高亮
          const indexActiveClickMarker = this.activeClickMarkerList.findIndex(
            activeItem => activeItem.itemKey === val.id
          )
          if (~indexActiveClickMarker) {
            val.styleId = this.selectedStyle
          } else {
            // 其他marker置default
            val.styleId = val.id
            if (~indexActiveMarkerList) {
              this.activeMarkerList.splice(indexActiveMarkerList, 1)
            }
          }
        }
      })
      globalMarker.updateGeometries(markersGeometries)
      // this.$refs.baseMap.updateMarkers()
    },
    async drawComplete() {
      this.drawType = ''
      this.polygonSelected = false
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
      this.updateMarkerStatus(this.$refs.baseMap.getGeometries())
    },
    deleteComplete(geometry) {
      this.polygonSelected = false
      this.removeMarkerStatus(geometry)
    },
    adjustComplete(geometry) {
      const geometries = [...this.$refs.baseMap.getGeometries(), geometry]
      this.updateMarkerStatus(geometries)
    },
    selectComplete() {
      this.polygonSelected = true
    },
    setDrawType(drawType) {
      this.drawType = this.drawType === drawType ? '' : drawType
      this.editMap()
    },
    async editMap() {
      if (!this.disabled && this.drawType) {
        await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.DRAW)
        this.$refs.baseMap.setActiveOverlay(this.drawType)
      } else {
        this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
        this.polygonSelected = false
      }
    },
    deleteMapShape() {
      if (this.polygonSelected) {
        const globalMapEditor = this.$refs.baseMap.getGlobalMapObj('globalMapEditor')
        globalMapEditor.delete()
      } else {
        showConfirmToast({
          message: '当前未选中任何图形, 是否全部删除图形'
        }).then(() => {
          this.removeMarkerStatus(this.$refs.baseMap.getGeometries())
          this.$refs.baseMap.clearEditorPolygon()
        })
      }
    },
    async clearIntersection() {
      this.setDrawType()
      this.markersBoardList = []
      this.updateMarkerStatus([])
      this.$refs.baseMap.clearEditorPolygon()
    },

    async positioningByAddress(key) {
      if (!key) return
      const isLatLng = isLatitudeLongitude(key)
      let suggestionList = []
      if (isLatLng) {
        suggestionList = [
          {
            title: key,
            id: key
          }
        ]
      } else {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(key)
        this.queryLoading = false
      }
      this.suggestionList = suggestionList
    },
    selectSuggestionHandle(key) {
      if (!key) return
      const item = this.suggestionList.find(val => val.id === key)
      // this.queryParam.keyword = item?.title
      // this.queryParam.location = item?.location
      this.queryParam.keyword = (item?.province || '') + (item?.city || '') + (item?.district || '') + item?.title
      this.search()
    },
    async search() {
      if (this.queryParam.key) {
        const isLatLng = isLatitudeLongitude(this.queryParam.key)
        let suggestionList = []
        if (!isLatLng) {
          this.queryLoading = true
          suggestionList = await searchSuggestionAPI(this.queryParam.keyword)
          this.queryLoading = false
        } else {
          suggestionList = [{ location: latLngPoint(this.queryParam.key.split(',')) }]
        }
        let markerList = suggestionList.map((val, index) => {
          // const item = this.convertorItem(val)
          return {
            id: val.id, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            style: {
              width: 34,
              height: 50,
              anchor: { x: 17, y: 50 },
              src: 'data:image/png;base64,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'
            },

            position: val.location //点标记坐标位置
          }
        })
        this.$refs.baseMap.showFitView(markerList)
        // showFitView(fitViewList, mapObj.globalMap)
      } else {
        this.drawerPoi()
      }
    },
    drawerPoi() {
      const postData = {
        targetCrs: 1,
        // intersectingArea: { type: 'Polygon', userData: { crs: 1 } },
        tagCodes: this.queryParam.tagList?.map(val => val.code)?.join(',')
      }
      if (this.queryParam.cantonCode?.length) {
        postData.cantonCode = this.queryParam.cantonCode[this.queryParam.cantonCode?.length - 1]
      } else {
        postData.intersectingArea = { type: 'Polygon', userData: { crs: 1 }, coordinates: [this.bounds] }
      }
      this.getPoiList(postData)
    },
    openQueryParams() {
      this.filterForm = !this.filterForm
      this.treeListTagGroup()
      this.getSysCantonTree()
    },
    openTools() {
      this.filterTools = !this.filterTools
    },
    addPoi() {
      if (this.activeMarkerList?.length) {
        this.activeClickMarkerList = []
        this.markersBoardList = JSON.parse(JSON.stringify(this.activeMarkerList))
      } else {
        showToast('请先选择poi点位', 'warning')
      }
    },
    // 选择标签查看标签（todo组件）
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        let tagsData = JSON.parse(JSON.stringify(data.tagList))
        this.queryParam.tagList = []
        this.saveCheckedTagList({ children: tagsData })
        this.tagList = tagsData
        // this.editTags(true)
      }
      this.tagChooseDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.queryParam.tagList.push({
            ...group.tagMap[tagId]
          })
        })
      }
    },
    handleClose(tag) {
      this.queryParam.tagList = this.queryParam.tagList.filter(item => {
        return item !== tag
      })
      this.tagList = this.queryParam.tagList
      if (this.queryParam.tagList?.length > 0) {
        this.queryParam.tagIdList = []
        this.queryParam.tagList.forEach(item => {
          this.queryParam.tagIdList.push(item.id)
        })
        this.queryParam.tagIdList = this.queryParam.tagIdList.join(',')
      }
    },
    tagChoose() {
      this.tagChooseDrawerVisible = true
      this.tagList = JSON.parse(JSON.stringify(this.queryParam.tagList || []))
    },
    resetTag() {
      this.queryParam.tagList = []
      this.tagList = []
    },
    getTags() {
      this.editTags = !this.disabled
      this.tagList = this.setCheckedTag(this.queryParam.tagList)
      this.tagCardVisible = true
    },
    tagClose() {
      this.tagCardVisible = false
    },
    setCheckedTag() {
      let groupArr = []
      let tagObj = this.groupBy(this.queryParam.tagList, 'groupId')
      for (let item in tagObj) {
        let parentGroupName = this.getParentNode(this.treeTagGroupList, item)
        let index = groupArr.findIndex(group => {
          return group.nameCn === parentGroupName
        })
        if (index < 0) {
          groupArr.push({
            nameCn: parentGroupName,
            asLeaf: false,
            children: [
              {
                nameCn: tagObj[item][0].groupNameCn,
                id: item,
                tagList: tagObj[item],
                asLeaf: true
              }
            ]
          })
        } else {
          groupArr[index].children.push({
            nameCn: tagObj[item][0].groupNameCn,
            id: item,
            tagList: tagObj[item],
            asLeaf: true
          })
        }
      }
      return groupArr
    },
    tagSave(dataList) {
      let arr = []
      if (dataList?.length > 0) {
        arr = dataList
          .map(item => {
            return item.children
          })
          .flat(Infinity)
          .map(tag => {
            return tag.tagList
          })
          .flat(Infinity)
        arr.map((val, index) => {
          val.sortNum = index
        })
        this.queryParam.tagList = arr
      }
      this.tagCardVisible = false
    },
    groupBy(array, prop) {
      return array.reduce((cur, pre) => {
        let key = pre[prop]
        if (!cur[key]) {
          cur[key] = []
        }
        cur[key].push(pre)
        return cur
      }, {})
    },
    getParentNode(tree, childId) {
      let parentInfo
      for (let node of tree) {
        // 如果当前节点就是目标节点的父节点，直接返回当前节点
        if (node.children && node.children.some(child => child.id === childId)) {
          return node.nameCn
        }
        // 否则继续遍历当前节点的子节点
        if (node.children) {
          parentInfo = this.getParentNode(node.children, childId)
          if (parentInfo !== null) {
            return parentInfo
          }
        }
      }
      return null
    },
    treeListTagGroup() {
      if (!this.treeTagGroupList?.length) {
        treeListBsTagGroup().then(res => {
          this.treeTagGroupList = res.data
        })
      }
    },
    async save() {
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
      const fileData = {
        markersBoardList: this.markersBoardList,
        geometries: this.$refs.baseMap.getGeometries()
      }
      this.$emit('reload', fileData)
    },
    changeGeoType(val) {
      this.geoType = val
      this.drawerPoi()
    },
    getIconCode(geoType) {
      let iconCode = ''
      switch (geoType) {
        case 'point':
          iconCode = 'el-icon-location'
          break
        case 'line':
          iconCode = 'el-icon-top-right'
          break
        case 'area':
          iconCode = 'el-icon-help'
          break
        case 'cluster':
          iconCode = 'el-icon-connection'
          break
      }
      return iconCode
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    async drawPoints(showFitView) {
      const globalMarker = this.$refs.baseMap.getGlobalMapObj('globalMarker')
      this.$refs.baseMap.clearObjects(globalMarker)
      this.mapMarkers = this.poiList.map(val => {
        const intersectionIndex = this.markersBoardList?.findIndex(
          intersectionItem => intersectionItem.itemKey === val.id
        )
        return {
          itemType: 'POI',
          itemKey: val.id,
          itemName: val.tagVO?.name,
          tagCode: val.tagVO?.code,
          checked: !!~intersectionIndex,
          longitude: val.point.coordinates[0],
          latitude: val.point.coordinates[1]
        }
      })
      // 自定义点位样式
      const markers = this.mapMarkers.map((val, index) => {
        return {
          id: val.itemKey,
          style: {
            enableRelativeScale: true,
            src: 'data:image/png;base64,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'
          },
          styleId: val.checked ? this.selectedStyle : '',
          position: latLngPoint({ lat: val.latitude, lng: val.longitude })
        }
      })
      // if (this.disabled) {
      this.$refs.baseMap.initMarkers(markers)
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.DRAW)
      const geometries = this.$refs.baseMap.getGeometries()
      this.updateMarkerStatus(geometries)
      await this.$refs.baseMap.changeEditorMode(TMap.tools.constants.EDITOR_ACTION.INTERACT)
      // 仅搜索区域时
      if (showFitView) {
        this.$refs.baseMap.showFitView(markers)
      }
    },
    drawLines() {
      const lines = this.poiList
        .filter(val => val.line?.coordinates?.length)
        .map((val, index) => {
          return {
            id: val.id,
            color: '#409eff',
            paths: val.line.coordinates.map(coord => [coord[1], coord[0], coord[2]]),
            styles: {
              arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
            }
          }
        })
      const options = {
        styles: {
          borderWidth: 2,
          borderColor: '#a8a8a8',
          showArrow: true,
          arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        }
      }
      const globalPolylineLayer = this.$refs.baseMap.createPolylineLayer(
        lines,
        options,
        'poi-polyline-layer',
        2,
        'globalPolylineLayer'
      )
    },
    drawAreas() {
      const areas = this.poiList
        .filter(val => val.polygon?.coordinates?.length)
        .map((val, index) => {
          return {
            id: val.id,
            paths: val.polygon.coordinates[0].map(coord => latLngPoint([coord[1], coord[0], coord[2]]))
          }
        })
      const options = {}
      const globalPolygonLayer = this.$refs.baseMap.createPolygonLayer(areas, options, 'poi-polygon-layer', 2)
    },
    drawCluster() {
      const _this = this
      const markers = this.poiList.map((val, index) => {
        return {
          id: val.id,
          style: {
            enableRelativeScale: true,
            src: 'data:image/png;base64,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'
          },
          position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]])
        }
      })
      this.$refs.baseMap.initClusterMarkers(markers)
    },
    clusterChanged(e) {
      if (this.geoType === 'cluster') {
        const _this = this
        let markerGeometries = []
        // 销毁旧聚合簇生成的覆盖物
        this.clearPoiMap()
        // this.$refs.baseMap.clearObjects('globalMarker', 'globalPolylineLayer', 'globalPolygonLayer')
        const globalMarkerCluster = this.$refs.baseMap.getGlobalMapObj('globalMarkerCluster')
        const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
        // 根据新的聚合簇数组生成新的覆盖物和点标记图层
        const clusters = globalMarkerCluster.getClusters()
        clusters.forEach(function (item) {
          if (item.geometries.length > 1) {
            const ClusterBubble = initClusterBubble(globalMap)
            let clusterBubble = new ClusterBubble({
              map: globalMap,
              position: item.center,
              content: item.geometries.length
            })
            clusterBubble.on('click', () => {
              globalMap.fitBounds(item.bounds, {
                padding: 100 // 自适应边距
              })
            })
            _this.clusterBubbleList.push(clusterBubble)
          } else {
            markerGeometries.push(item.geometries[0])
          }
        })

        if (markerGeometries?.length) {
          // 创建点标记图层

          const markers = this.poiList
            .filter(val => ~markerGeometries.findIndex(geo => geo.id === val.id))
            .map((val, index) => {
              return {
                id: val.id,
                style: {
                  enableRelativeScale: true,
                  src: 'data:image/png;base64,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'
                },
                position: latLngPoint([val.point.coordinates[1], val.point.coordinates[0]])
              }
            })
          this.$refs.baseMap.initMarkers(markers)
          // this.drawInfoWindow(globalMarker)
        }
      }
    }
  }
}
</script>
<style>
.clusterBubble {
  border-radius: 50%;
  color: #fff;
  font-weight: 500;
  text-align: center;
  opacity: 0.88;
  background-image: linear-gradient(139deg, #4294ff 0%, #295bff 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 0;
  left: 0;
}
</style>
<style lang="scss" scoped>
.map-selection-container.intersection {
  width: 100%;
  height: 100%;
  display: flex;

  .map-content {
    position: relative;
    height: 100%;
    width: calc(100% - 200px);

    .map-overview {
      user-select: none;
      height: 100%;
      width: 100%;
      z-index: 0;
    }

    .query-param {
      width: 100%;
      display: flex;
      z-index: 2;
      position: absolute;
      left: 0;

      &.top {
        top: 0;
      }

      &.tools {
        bottom: 0;
      }

      .label {
        display: flex;
        align-items: center;
        white-space: nowrap;

        .ltw-icon {
          transition: all 0.3s;
        }

        .visible {
          transform: rotateZ(180deg);
        }
      }

      .form {
        width: calc(100% - 42px);
        padding-left: 10px;
        display: flex;
        align-items: center;
        transform-origin: 0 50%;
        transform: scaleX(0);
        //width: 0;
        //opacity: 0;
        transition: all 0.3s;

        .tag-form-item {
          background: #fff;
          white-space: nowrap;
          margin-right: 10px;
          padding-left: 10px;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }

        .search-container {
          white-space: nowrap;
          width: 125px;
        }

        .search-tx-marker {
          width: calc(100% - 42px);
        }

        &.visible {
          transform: scaleX(1);
          //transform-origin: left center;
          //opacity: 1;
          //width: auto;
        }

        .intersection-check {
          margin-left: 6px;
        }

        .intersection-name {
          width: 200px;
        }

        .supplier-code {
          width: 200px;
        }

        .tool-item {
          width: 40px;
          height: 40px;
          margin: 2px;
          padding: 4px;
          border-radius: 3px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          box-shadow: 0 1px 2px 0 #e4e7ef;
          background-color: #ffffff;
          border: 1px solid #ffffff;
          cursor: pointer;

          &.active {
            border-color: #d5dff2;
            background-color: #d5dff2;
          }

          &:hover {
            border-color: #789cff;
          }
        }

        .polygon {
          background-image: url('@/assets/images/mapIcon/polygon.png');
        }

        .circle {
          background-image: url('@/assets/images/mapIcon/circle.png');
        }

        .ellipse {
          background-image: url('@/assets/images/mapIcon/ellipse.png');
        }

        .rectangle {
          background-image: url('@/assets/images/mapIcon/rectangle.png');
        }

        .delete {
          background-image: url('@/assets/images/mapIcon/delete.png');
        }
      }
    }

    .opt-list {
      position: absolute;
      z-index: 2;
      right: 10px;
      top: 40px;
      background: #fff;
      padding: 5px 0;
      border-radius: 3px;

      .opt-btn {
        padding: 5px 10px;
        text-align: left;
        font-size: 12px;
        cursor: pointer;

        &.active,
        &:hover {
          background-color: rgb(235.9, 245.3, 255);
          color: #409eff;
        }

        .btn-text {
          display: flex;
        }

        //&:not(:first-child) {
        //  margin-top: 12px;
        //}
      }
    }
  }

  .choosed-intersection {
    width: 200px;
    margin-left: 10px;

    :deep(.el-card__header) {
      background: rgb(239, 241, 242);
    }

    :deep(.el-card__body) {
      height: 100%;
    }

    .body {
      height: calc(100% - 88px);

      .total {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
      }

      .list {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }

      .num {
        color: rgb(25, 173, 253);
      }
    }

    .intersection-item {
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        border-color: #fab6b6;
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: end;
    align-items: center;

    .intersection-num {
      margin-right: 10px;
    }
  }

  .btn-add-poi {
    position: absolute;
    right: 20px;
    bottom: 0;
    height: 44px;
    z-index: 2;
    display: flex;
    align-items: center;
  }

  .icon-add-poi {
    transform: rotateZ(90deg);
  }
}
</style>
