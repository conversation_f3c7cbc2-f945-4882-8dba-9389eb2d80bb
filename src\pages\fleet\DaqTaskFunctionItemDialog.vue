<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened"
             destroy-on-close
             :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="150px">
      <el-form-item :label="$t('编码')" prop="code">
        <ltw-input v-model="formData.code" maxlength="64" :disabled="formReadonly" id="code"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="formData.name" maxlength="64" :disabled="formReadonly" id="name"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('前端组件类型')" prop="componentType">
        <!--        <ltw-input v-model="formData.componentType" :disabled="formReadonly" id="componentType"></ltw-input>-->
        <dictionary-selection
            @change="changeComponentType"
            style="width:100%"
            :disabled="formReadonly"
            v-model="formData.componentType"
            clearable
            dictionaryType="component_type"
            :placeholder="$t('请选择')"
            filterable
        />
      </el-form-item>
      <el-form-item :label="$t('字典值定义')" prop="valueDefinition" id="valueDefinition"
                    v-if="formData.componentType==='select'">
        <div class="aList">
          <el-row class="F-row">
            <el-col :span="10">
              <div class="f-title">{{ $t('名称') }}</div>
            </el-col>
            <el-col :span="10">
              <div class="f-title">{{ $t('编码') }}</div>
            </el-col>
            <el-col :span="4" v-if="indexCount > 1 && dialogStatus !== 'view'">
              <div class="f-title" style="margin-left: 50px">操作</div>
            </el-col>
          </el-row>
          <el-row class="F-row" v-for="item in aList">
            <el-col :span="10">
              <el-input
                  maxlength="64"
                  v-model="item.name"
                  style="width: 150px"
                  :disabled="dialogStatus === 'view'"
                  size="small"
              ></el-input>
            </el-col>
            <el-col :span="10">
              <el-input
                  maxlength="64"
                  v-model="item.code"
                  style="width: 150px"
                  :disabled="dialogStatus === 'view'"
                  size="small"
              ></el-input>
            </el-col>
            <el-col :span="4" v-if="dialogStatus !== 'view' && indexCount > 1">
              <el-button link type="primary" style="margin-left: 48px" size="small" @click="remove(item.index)">
                {{ $t('删除') }}
              </el-button>
            </el-col>
          </el-row>
          <el-button v-if="dialogStatus !== 'view'" style="width: 100%; margin-top: 5px" size="small" @click="addItem">
            {{ $t('新增') }}
            <el-icon class="el-icon--right">
              <CirclePlus/>
            </el-icon>
          </el-button>
        </div>
      </el-form-item>
      <el-form-item :label="$t('值类型')" prop="valueType" v-if="formData.componentType==='input'">
        <dictionary-selection
            id="valueUnit"
            class="ltw-tool-container"
            v-model="formData.valueType"
            :disabled="formReadonly"
            clearable
            dictionaryType="item_value_type"
        />
      </el-form-item>
      <el-form-item :label="$t('字段单位')" prop="valueUnit" v-if="formData.componentType==='input'">
        <dictionary-selection
            id="valueUnit"
            class="ltw-tool-container"
            v-model="formData.valueUnit"
            :disabled="formReadonly"
            clearable
            dictionaryType="length_unit"
        />
      </el-form-item>
      <el-form-item :label="$t('是否必填')" prop="required">
        <el-checkbox
            :disabled="formReadonly"
            v-model="formData.required"
            id="required"
        />
        <!--        <ltw-input v-model="formData.required" :disabled="formReadonly" id="required"></ltw-input>-->
      </el-form-item>
      <el-form-item :label="$t('依赖')" prop="dependency">
        <div class="dependencyDiv">
          <el-select v-model="selectedDependency" placeholder="请选择依赖" :disabled="formReadonly" clearable
                     @change="updateDependency" @clear="clearSelectedValue">
            <el-option
                v-for="item in selectItems"
                :key="item.id"
                :label="item.name"
                :value="item.code"
            />
          </el-select>
          <el-tag v-if="showOperator"><span>=</span></el-tag>
          <el-select v-if="selectedDependency" v-model="selectedValue" placeholder="请选择值" @change="updateDependency"
                     :disabled="formReadonly" clearable>
            <el-option
                v-for="opt in getValueOptions(selectedDependency)"
                :key="opt.code"
                :label="opt.name"
                :value="opt.code"
            />
          </el-select>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="warning" @click="edit" v-if="dialogStatus === 'view'" id="edit">{{ $t('编辑') }}</el-button>
        <el-button @click="dialogClosed" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <el-button type="danger" @click="deleteItem" v-if="dialogStatus === 'view'" id="edit">{{
            $t('删除')
          }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="save" id="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import {BS_TAG_TYPE, TAG_RELATED_TYPE} from '@/plugins/constants/data-dictionary'
import UnitSelection from '../../components/system/UnitSelection.vue'
import {listSysUnit} from '@/apis/system/sys-unit'
import {ElMessage} from 'element-plus'
import {RemoveFilled, CirclePlus} from '@element-plus/icons-vue'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import {showConfirmToast, showToast} from '@/plugins/util'
import RadioBsTagGroupDrawer from '@/components/basic/RadioBsTagGroupDrawer.vue'

import {
  saveDaqTaskFunctionItem,
  updateDaqTaskFunctionItem,
  deleteDaqTaskFunctionItem
} from '@/apis/fleet/daq-task-function-item'
import BASE_CONSTANT from "@/plugins/constants/base-constant";
import {deleteDaqReqDetail} from "@/apis/data-collect/vt-daq-task";

const defaultFormData = {
  enabled: true,
  type: 'transient',
  mutuallyExclusive: true,
  supportVoice: false,
  supportTrigger: false
}

export default {
  components: {RadioBsTagGroupDrawer, BsTagGroupDrawer, DictionarySelection, UnitSelection},
  name: 'BsTagDialog',
  emits: ['save', 'remove'],
  data() {
    return {
      showOperator: false,
      tagTypeSupportTrigger: false,
      TAG_RELATED_TYPE: TAG_RELATED_TYPE,
      dialogVisible: false,
      selectItems: [], // 存储同层级的 select 节点
      selectedDependency: '', // 当前选择的依赖 ID
      selectedValue: '', // 当前选择的值
      formData: Object.assign({}, defaultFormData),
      formRules: {
        code: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        name: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        componentType: [{required: true, message: this.$t('请输入'), trigger: 'blur'}]
      },
      dialogTitle: '',
      dialogStatus: '',
      minDurationUnit: '',
      maxDurationUnit: '',
      minDuration: 0,
      maxDuration: 9999,
      previousDurationUnit: '',
      followingDurationUnit: '',
      previousDurationSeconds: 0,
      followingDurationSeconds: 9999,
      unitList: [],
      RemoveFilled: RemoveFilled,
      CirclePlus: CirclePlus,
      indexCount: 1,
      aList: [],
      rowTagList: [],
      tagDistributeDrawerVisible: false,
      singleTagDistributeDrawerVisible: false,
      singleRowList: [],
      tagList: [],
      tagId: '1'
    }
  },
  computed: {
    BS_TAG_EQUIP_TYPE() {
      return BS_TAG_EQUIP_TYPE
    },
    formReadonly() {
      return this.dialogStatus === 'view'
    },
    continuous() {
      if (this.formData.type !== BS_TAG_TYPE.CONTINUOUS) {
        this.formData.mutuallyExclusive = false
      }
      return this.formData.type === BS_TAG_TYPE.CONTINUOUS
    }
  },
  created() {
    // this.listUnit()
  },

  methods: {
    changeComponentType(node) {
      if ("textarea" === node.value) {
        this.formData.valueType = 'TEXT'
      }
      if ("input" !== node.value) {
        this.formData.valueUnit = null
        this.formData.valueType = null
      }
    },
    clearSelectedValue() {
      this.selectedValue = undefined
      this.showOperator = false
    },
    updateDependency(value) {
      this.showOperator = true
      if (this.selectedDependency) {
        // 更新依赖属性，构建所需格式
        this.formData.dependency = JSON.stringify({
          code: this.selectedDependency,
          value: value
        });
      } else {
        this.formData.dependency = null
      }
    },
    getValueOptions(dependencyCode) {
      // 根据 selectedDependency 获取对应的值选项
      const selectedItem = this.selectItems.find(item => item.code === dependencyCode);
      return selectedItem.valueDefinition ? JSON.parse(selectedItem.valueDefinition) : [];
    },
    bsTagTypeChange(node) {
      if (BS_TAG_TYPE.CONTINUOUS === node.value) {
        this.tagTypeSupportTrigger = true
      } else {
        this.tagTypeSupportTrigger = false
      }
    },
    handleKeyDown(event) {
      if (event.key === 'e') {
        event.preventDefault()
      }
    },
    dialogOpened() {
      // this.$refs.formRef?.resetFields()
    },
    dialogClosed() {
      this.indexCount = 1
      this.aList = []
      this.rowTagList = []
      this.singleRowList = []
      this.tagList = []
      this.dialogVisible = false
      this.initForm()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    add(group, items) {
      this.dialogTitle = this.$t('增加')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      
      this.selectItems = items.filter(item => item.componentType === 'select');
      this.$nextTick(() => {
        this.formData = Object.assign(this.formData, group)
        this.showOperator=false
        this.formData.valueDefinition = undefined
        this.formData.dependency = undefined
        this.selectedDependency = undefined
        this.selectedValue = undefined
      })
    },
    save() {
      if (this.aList?.length > 0 && this.formData.componentType === 'select') {
        this.formData.valueDefinition = JSON.stringify(this.aList)
      } else {
        this.formData.valueDefinition = ''
      }
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveDaqTaskFunctionItem(this.formData).then(res => {
            this.dialogVisible = false
            this.$emit('save', res.data)
          })
        }
        if (this.dialogStatus === 'edit') {
          updateDaqTaskFunctionItem(this.formData).then(res => {
            this.dialogVisible = false
            this.$emit('save', this.formData)
          })
        }
      })
    },
    deleteItem() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqTaskFunctionItem({id: this.formData.id}).then(() => {
          this.dialogVisible = false
          this.$emit('remove', this.formData)
        })
      })
    },
    edit() {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
    },
    view(row, items) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      this.formData = row
      this.aList = []
      
      this.selectItems = items.filter(item => item.componentType === 'select' && item.id !== this.formData.id);
      if (row.valueDefinition) {
        const splitArr = JSON.parse(row.valueDefinition)
        for (let i = 0; i < splitArr.length; i++) {
          if (splitArr[i]) {
            this.aList.push({
              index: i,
              name: splitArr[i].name,
              code: splitArr[i].code
            })
          }
        }
        this.indexCount = splitArr.length
      }
      if (row.dependency) {
        const dependencyObj = JSON.parse(row.dependency)
        this.selectedDependency = dependencyObj.code
        this.selectedValue = dependencyObj.value
        this.showOperator=true
      } else {
        this.selectedDependency = undefined
        this.selectedValue = undefined
        this.showOperator=false
      }
      this.tagList = row.relatedTagList
      this.dialogVisible = true
    },
    remove(index) {
      this.aList = this.aList.filter(x => x.index !== index)
    },
    addItem() {
      this.aList.push({
        index: this.indexCount
      })
      this.indexCount = this.indexCount + 1
    },
  }
}
</script>
<style lang="scss" scoped>
.dependencyDiv {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
}

.tag-duration-input {
  width: 200px;
}

.unit-selection {
  width: 200px;
  //margin-left: 15px;
}

.aList {
  width: 100%;
  padding: 0 10px;
}

.f-title {
  font-size: 12px;
}

.button-group-container {
  width: 100%;

  .el-button {
    margin-left: 10px;
  }
}

.tag-container {
  margin-top: 10px;

  .el-tag {
    margin: 0 3px;
  }
}
</style>
