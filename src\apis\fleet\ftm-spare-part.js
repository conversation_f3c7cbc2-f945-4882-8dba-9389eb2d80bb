import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmSparePart = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts',
    data,
    params
})
export const updateFtmSparePart = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts',
    data,
    params
})
export const deleteFtmSparePart = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts',
    params
})
export const listFtmSparePart = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts',
    params
})
export const listFtmSparePartSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/selections',
    params
})
export const pageFtmSparePart = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/page',
    params
})
export const getFtmSparePart = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/' + id})

export const importSpareReport = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/importSpareReport/',data,params})


export const exportSpareReport = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/exportSpareReport',
        data,
        params,
        responseType: 'blob'
    })

export const importCompetitor = (data = {}, params = {}) =>
    httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/xxx/importCompetitor', data, params })

export const listVehicleCompetitorTimeLineData = ( params = {}) =>
    httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_spare_parts/listFtmVehicleSparePartTimeLineData', params })

export const listFtmSparePartVersions = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_part_versions',
    params
})

export const getFtmSparePartVersion = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_part_versions/' + id})

export const listFtmVehicleSpareParts = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_parts',
    params
})


export const saveFtmSparePartVersion = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_part_versions',
    data,
    params
})

export const updateFtmSparePartVersion = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_part_versions',
    data,
    params
})
export const deleteFtmSparePartVersion = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_part_versions',
    params
})


export const saveFtmVehicleSparePart = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_parts',
    data,
    params
})

export const updateFtmVehicleSparePart = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_parts',
    data,
    params
})

export const deleteFtmVehicleSparePart = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_parts',
    params
})

export const saveFtmVehicleSparePartList = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_spare_parts/saveList',
    data,
    params
})
