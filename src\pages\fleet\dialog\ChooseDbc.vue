<template>
  <el-drawer
    :title="dialogTitle"
    v-model="dialogVisible"
    width="100%"
    :close-on-click-modal="formReadonly"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="modality-drawer"
  >
    <el-table :data="dbcData" @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
      <el-table-column fixed="left" type="selection" width="55" />
      <el-table-column :filters="variantFilterList"
                       :filter-method="filterTag" header-align="center" align="center" prop="variantName" :label="$t('车型')"></el-table-column>
      <el-table-column header-align="center" align="center" prop="fileName" :label="$t('文件名')">
      </el-table-column>
      <el-table-column
          header-align="center"
          align="center"
          prop="createTime"
          :label="$t('上传日期')"
      ></el-table-column>
      <el-table-column header-align="center" align="center" prop="enable" :label="$t('是否有效')">
        <template #default="scope">
          <span v-if="scope.row.enable">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="remark" :label="$t('备注')"></el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button id="cancel" @click="dialogVisible = false">{{ $t('取消') }}</el-button>
        <el-button id="save" type="primary" @click="submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-drawer>
  <AddModality ref="AddModality"></AddModality>
</template>

<script>
import {listFtmDbc } from '@/apis/fleet/ftm-dbc'
import AddModality from '@/pages/fleet/dialog/AddModality.vue'

const defaultform = {}
export default {
  name: 'ChooseDbc',
  emits: ['reload'],
  components: {
    AddModality
  },
  data() {
    return {
      variantFilterList:[],
      selectedData:[],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      dbcData: [ ],
      vehicleId: '',
      installationRecordId: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'view':
          this.dialogTitle = this.$t('选择dbc文件')
          // this.vehicleId = row.data.vehicleId
          // this.installationRecordId = row.data.installationRecordId
          this.listFtmDbc()
          break
      }
    },
    filterTag(value, row) {
      return row.variantName === value;
    },
    listFtmDbc() {
      listFtmDbc({enable:true}).then(res => {
        this.dbcData = res.data
        if(this.dbcData?.length){
          let uniqueVariantNames = [...new Set(this.dbcData.map(val => val.variantName))]
          uniqueVariantNames.forEach(dbc=>{
            this.variantFilterList.push(
            { text: dbc, value: dbc }
            )
          })
        }
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.tableRef.clearSelection()
    },
    submit() {
      this.$emit('reloadDbcList', this.selectedData)
      this.cancel()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    }
  }
}
</script>

<style>
.modality-drawer > .el-drawer__header {
  margin-bottom: 0;
}
</style>
<style scoped lang="scss">
.expand-form {
  width: 800px;
}

:deep(.no-expand) .el-table__expand-column .cell {
  display: none;
}
</style>
