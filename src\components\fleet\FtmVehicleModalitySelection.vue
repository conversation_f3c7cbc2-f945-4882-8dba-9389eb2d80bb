<template>
  <el-select
    v-model="selectValue"
    :placeholder="$t('请选择传感器')"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.code"
      :value="item.code"
      id="modality-list"
    >
      <div class="select-option">
        <span>{{item.code}}</span><span class="name-container">({{item.name}})</span>
      </div>

    </el-option>

  </el-select>
</template>

<script>
import { listFtmVehicleModalitySelection } from '@/apis/fleet/ftm-vehicle-modality'

export default {
  name: 'FtmVehicleModalitySelection',
  props: {
    status: String,
    modelValue: [String, Number, Array],
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: false
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 默认的返回值id/vin
    modelCode: {
      type: String,
      default: 'id'
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    detailList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      map: {},
      list: [],
      queryParam: {}
    }
  },
  watch: {
    data(val) {
      this.list = val
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  mounted() {
    this.queryParam.status = this.status
    if (this.autoLoad) {
      this.query()
    } else {
      this.list = this.data
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.code] = item
        })
      }
    }
  },
  methods: {
    query() {
      listFtmVehicleModalitySelection().then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          this.map = {}
          this.list.forEach(item => {
            this.map[item.id] = item
          })
        }
      })
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      this.$emit('change', {
        value: value,
        node: this.map[value]
      })
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped lang="scss">
.select-option {
  display: flex;
  flex-direction: row;
  //justify-content: space-between;
  div {
    margin-right: 10px;
    padding: 1px;
  }
  .name-container{
    color: #a3a5ac;
  }
}
</style>
