<template>
  <base-filter @filter="filter" @init="initFilter" @exportFile="exportFile">
    <template #common-filters>
      <div class="filter-row-container">
        <template v-if="currentDimension === 'data'">
          <div class="filter-item">
            <span class="label-txt">{{ $t('车架号') }}</span>
            <ltw-input
              :placeholder="$t('请输入车架号')"
              v-model="formData.key"
              clearable
              class="filter-input"
            ></ltw-input>
            <!-- <bs-vehicle-selection
                modelCode="vin"
                multiple
                v-model="formData.vinList"
                ref="vehicleSelectionRef"
                :clearable="true"
                filterable
                @clear="formData.vinList = undefined"
                class="filter-input"
            ></bs-vehicle-selection> -->
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('采集日期') }}</span>
            <div class="date-picker"                 style="width:350px">
              <el-date-picker
                v-model="formData.acquisitionDate"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="YYYY-MM-DD"
                class="filter-input"

              />
            </div>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('合规员') }}</span>
            <employee-selection
              clearable
              v-model="formData.complianceOfficer"
              class="filter-input"
            ></employee-selection>
          </div>
           <div class="filter-item">
          <span class="label-txt">{{ $t('磁盘序列号') }}</span>
          <el-select
            v-model="formData.serialNumberList"
            placeholder="请选择磁盘序列号"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            class="filter-input"
          >
            <el-option v-for="item in diskSerialList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        </template>
        <template v-if="currentDimension === 'disk'">
          <div class="filter-item">
            <span class="label-txt">{{ $t('寄送日期') }}</span>
            <div class="date-picker">
              <el-date-picker
                v-model="formData.deliveryDate"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="YYYY-MM-DD"
                class="filter-input"
              />
            </div>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('签收日期') }}</span>
            <div class="date-picker">
              <el-date-picker
                v-model="formData.receiptDate"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                :default-value="defaultToday"
                value-format="YYYY-MM-DD"
                class="filter-input"
              />
            </div>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('寄送人') }}</span>
            <el-select
              v-model="formData.deliveryEmpId"
              placeholder="请选择寄送人"
              clearable
              filterable
              class="filter-input"
            >
              <el-option v-for="item in senderList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
           <div class="filter-item">
          <span class="label-txt">{{ $t('磁盘序列号') }}</span>
          <el-select
            v-model="formData.serialNumberList"
            placeholder="请选择磁盘序列号"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            class="filter-input"
          >
            <el-option v-for="item in diskSerialList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        </template>
      </div>
    </template>
    <template #more-filters>
      <div class="filter-row-container">
                 <div class="filter-item">
          <span class="label-txt">{{ $t('磁盘编号') }}</span>
          <el-select
            v-model="formData.diskCodeList"
            placeholder="请选择磁盘编号"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            class="filter-input"
          >
            <el-option v-for="item in diskCodeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <template v-if="currentDimension === 'disk'">

          <div class="filter-item">
            <span class="label-txt">{{ $t('签收人') }}</span>
            <el-select
              v-model="formData.receiptEmpId"
              placeholder="请选择签收人"
              clearable
              filterable
              class="filter-input"
            >
              <el-option v-for="item in receiverList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('签收状态') }}</span>
            <el-select
              multiple
              collapse-tags
              collapse-tags-tooltip
              v-model="formData.receiptStatusList"
              placeholder="请选择状态"
              clearable
              class="filter-input"
            >
              <el-option v-for="item in receiptStatusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="filter-item">
            <span class="label-txt">{{ $t('运单号') }}</span>
            <ltw-input v-model="formData.trackingNumber" placeholder="请输入运单号" clearable class="filter-input" />
          </div>
        </template>
      </div>
    </template>
  </base-filter>
</template>

<script>
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import BaseFilter from '@/components/filter/BaseFilter.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import {
  getDiskList,
  getFullList,
  exportDiskTransportExcel,
  exportExcelWebDiskDataTransport
} from '@/apis/fleet/ftm-dis-transport'
import { listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import { downloadFile,dateUtils } from '@/plugins/util'

const defaultForm = {
  key: '',
  acquisitionDate: null,
  receiptDate: null,
  deliveryDate: null,
  deliveryEmpId: null,
  receiptEmpId: null,
  receiptStatus: null,
  trackingNumber: '',
  serialNumberList: [],
  diskCodeList: []
}

const shortcuts = [
  {
    text: 'today',
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    }
  },
  {
    text: 'Last week',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: 'Last month',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: 'Last 3 months',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]
export default {
  name: 'ProcessRecordFilter',
  components: { BaseFilter, DictionarySelection, BsVehicleSelection, EmployeeSelection },
  emits: ['filter'],
  props: {
    currentDimension: {
      type: String
    }
  },
  data() {
    return {
      defaultToday: [new Date(), new Date()],
      shortcuts: shortcuts,
      formData: Object.assign({}, defaultForm),
      senderList: [],
      receiverList: [],
      receiptStatusList: [],
      diskSerialList: [],
      diskCodeList: [],
      innerCodeList: [],
      siteList: [],
      serialNumberList: [],
      statusList: []
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initFilter() {
      this.formData = { ...defaultForm }
    },
    getTransportModeList() {
      listSysDictionary({
        typeCode: 'receipt_status_seg'
      }).then(res => {
        const data = res.data
        let statusList = []
        if (data && data.length) {
          statusList = data.map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
        }
        this.receiptStatusList = statusList
      })
    },
    getDiskList() {
      getDiskList().then(res => {
        const data = res.data
        let diskList = []
        if (data && data.length) {
          diskList = data.map(item => {
            return {
              label: item.code,
              value: item.code
            }
          })
          // 使用 Map 对象进行去重，保持对象的完整性
          diskList = Array.from(new Map(diskList.map(item => [item.value, item])).values())
        }
        this.diskCodeList = diskList
      })
    },
    getFullList() {
      getFullList().then(res => {
        const data = res.data
        let diskSerialNumList = []
        if (data && data.length) {
          diskSerialNumList = data.map(item => {
            return {
              label: item.serialNumber,
              value: item.serialNumber
            }
          })
        }
        this.diskSerialList = diskSerialNumList
      })
    },
    listSysRoleEmployee() {
      listSysRoleEmployee({
        orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
      }).then(res => {
        const data = res.data
        let employeeList = []
        if (data && data.length) {
          employeeList = data.map(item => {
            return {
              label: item.name,
              value: item.id
            }
          })
        }
        this.senderList = employeeList
        this.receiverList = employeeList
      })
    },
    parseParam() {
      let postData = { ...this.formData }

      // 处理日期范围
      if (postData.receiptDate?.length) {
        postData.receiptStartDate = postData.receiptDate[0]
        postData.receiptEndDate = postData.receiptDate[1]
      }

      if (postData.deliveryDate?.length) {
        postData.deliveryStartDate = postData.deliveryDate[0]
        postData.deliveryEndDate = postData.deliveryDate[1]
      }

      if (postData.acquisitionDate?.length) {
        postData.acquisitionStartDate = postData.acquisitionDate[0]
        postData.acquisitionEndDate = postData.acquisitionDate[1]
      }

      // 处理多选列表
      if (postData.serialNumberList?.length) {
        postData.serialNumberList = postData.serialNumberList.join(',')
      }
      if (postData.receiptStatusList?.length) {
        postData.receiptStatusList = postData.receiptStatusList.join(',')
      }

      if (postData.diskCodeList?.length) {
        postData.diskCodeList = postData.diskCodeList.join(',')
      }
      if (postData.vinList?.length) {
        postData.vinList = postData.vinList.join(',')
      }
      // 删除原始日期数组
      delete postData.receiptDate
      delete postData.deliveryDate
      delete postData.acquisitionDate
      return postData
    },
    filter() {
      let postData = this.parseParam()

      this.$emit('filter', postData)
    },
    exportFile() {
      let postData = this.parseParam()
      if (this.currentDimension === 'disk') {
        exportDiskTransportExcel(postData).then(res => {
          if (res.type === 'application/json') {
            this.$message.warning({
              message: '暂无数据可导出!',
              type: 'warning'
            })
            return
          }
          const fileName = '磁盘运输记录磁盘维度' + dateUtils.formatDateNormal(new Date())
          downloadFile(res, fileName)
        })
      } else {
        exportExcelWebDiskDataTransport(postData).then(res => {
          if (res.type === 'application/json') {
            this.$message.warning({
              message: '暂无数据可导出!',
              type: 'warning'
            })
            return
          }
          const fileName = '磁盘运输记录数据维度' + dateUtils.formatDateNormal(new Date())
          downloadFile(res, fileName)
        })
      }
    },
    initData() {
      this.getTransportModeList()
      this.getDiskList()
      this.getFullList()
      this.listSysRoleEmployee()
    }
  },
  watch: {
    currentDimension: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.initFilter()
          this.filter()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-row-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 2px;
}

.filter-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  max-width: 310px; 
  margin-bottom: 8px;

  &.empty-item {
    /* 空项目，用于占位保持对齐 */
    height: 0;
    min-height: 0;
    margin: 0;
    padding: 0;
    visibility: hidden;
  }
}

.label-txt {
  width: 90px; /* 固定标签宽度 */
  flex-shrink: 0;
  font-size: 14px;
  color: #606266;
  text-align: right;
  padding-right: 12px;
  white-space: nowrap;
}

.filter-input {
  flex: 1;
  width: 100%;
}

/* 日期选择器样式 */
.date-picker {
  flex: 1;
  width: 100%;

  :deep(.el-date-editor) {
    width: 100% !important;
  }

  :deep(.el-range-editor) {
    width: 100% !important;
  }
}

/* 下拉选择器样式 */
:deep(.el-select) {
  width: 100%;
}

/* 输入框样式 */
:deep(.el-input) {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .filter-item {
    max-width: calc(50% - 8px); /* 每行2个 */
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%; /* 每行1个 */
  }

  .label-txt {
    width: 80px;
    font-size: 13px;
  }
}
</style>
