function isTransferable(object) {
    if (!object) {
        return false;
    }
    if (object instanceof ArrayBuffer) {
        return true;
    }
    if (typeof MessagePort !== 'undefined' && object instanceof MessagePort) {
        return true;
    }
    if (typeof ImageBitmap !== 'undefined' && object instanceof ImageBitmap) {
        return true;
    }
    // @ts-ignore
    if (typeof OffscreenCanvas !== 'undefined' && object instanceof OffscreenCanvas) {
        return true;
    }
    return false;
}
function getTransferList(object, recursive = true, transfers) {
    // Make sure that items in the transfer list is unique
    const transfersSet = transfers || new Set();
    if (!object) {
        // ignore
    }
    else if (isTransferable(object)) {
        transfersSet.add(object);
    }
    else if (isTransferable(object.buffer)) {
        // Typed array
        transfersSet.add(object.buffer);
    }
    else if (ArrayBuffer.isView(object)) {
        // object is a TypeArray viewing into a SharedArrayBuffer (not transferable)
        // Do not iterate through the content in this case
    }
    else if (recursive && typeof object === 'object') {
        for (const key in object) {
            // Avoid perf hit - only go one level deep
            getTransferList(object[key], recursive, transfersSet);
        }
    }
    // If transfers is defined, is internal recursive call
    // Otherwise it's called by the user
    return transfers === undefined ? Array.from(transfersSet) : [];
}
export { getTransferList };
