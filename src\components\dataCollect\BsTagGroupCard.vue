<template>
  <el-card shadow="always" class="bs-tag-group-card" id="bs-tag-group-card">
    <template #header>
      <div class="bs-tag-group-card-header">
        <span>{{ tagGroup[locale === 'zh' ? 'nameCn' : 'name'] }}</span>
      </div>
    </template>
    <div class="bs-tag-group-card-body">
      <div class="bs-tag-sub-group-container" v-if="tagGroup.children && tagGroup.children.length > 0">
        <VueDraggable class="vue-draggable" v-model="tagGroup.children" :animation="300" :disabled="!timeEdit">
          <template v-for="item in tagGroup.children" :key="item.id">
            <bs-tag-group-card
              :classificationTag="classificationTag"
              :continuous-units="continuousUnits"
              :tag-group="item"
              :edit-tags="editTags"
              :timeEdit="timeEdit"
              :model="model"
              :attributes="attributes"
              class="bs-tag-sub-group-card"
              v-if="checkGroupVisible(item)"
              @tag-set="handleTagSet"
              @tag-click="handleGroupTagClick"
            ></bs-tag-group-card>
          </template>
        </VueDraggable>
      </div>
      <div class="bs-tag-tontainer" v-if="tagGroup.tagList && tagGroup.tagList.length > 0">
        <VueDraggable v-model="tagGroup.tagList" :animation="300" :disabled="!timeEdit">
          <template v-for="item in tagGroup.tagList" :key="item.id">
            <el-tag
              v-if="checkTagVisible(item)"
              :closable="editTags"
              :type="checkTagType(item)"
              @close="handleClose(item, tagGroup)"
              :class="{
                'tag-active': item.active,
                'tag-inactive': !item.active,
                'cannot-click': model !== 'simulation'
              }"
            >
              <span class="tag-name" :class="{ view: !editTags }">
                <ltw-icon class="form-component" icon-code="svg-input" v-if="item.componentType === 'input'"></ltw-icon>
                <ltw-icon
                  class="form-component"
                  icon-code="svg-select"
                  v-if="item.componentType === 'select'"
                ></ltw-icon>
                <ltw-icon
                  class="form-component"
                  icon-code="svg-textarea"
                  v-if="item.componentType === 'textarea'"
                ></ltw-icon>
                {{ item[locale === 'zh' ? 'nameCn' : 'name'] }}
                <!--                <ltw-icon icon-code="el-icon-microphone" v-if="item.supportVoice && !timeEdit"></ltw-icon>-->
              </span>
              <div v-if="timeEdit" class="edit-tag-form">
                <div v-if="attributes.includes(TAG_ATTRIBUTES.MIN)">
                  <el-input-number size="small" v-model="item.min" :min="0" :step="1" class="tag-duration-input" />
                  <span class="tag-unit">次</span>
                  &nbsp;
                </div>
                <div v-if="item.type === 'continuous' && attributes.includes(TAG_ATTRIBUTES.MIN_DURATION)">
                  <el-input-number
                    size="small"
                    v-model="item.minDuration"
                    :min="0"
                    :step="1"
                    class="tag-duration-input"
                  />
                  <span class="tag-unit">秒</span>
                  &nbsp;
                </div>
                <div class="tag-attribute" v-if="attributes.includes(TAG_ATTRIBUTES.SUPPORT_VOICE)">
                  <el-tooltip effect="dark" :content="$t('是否支持语音')" placement="top">
                    <ltw-icon
                      :icon-code="item.supportVoice ? 'el-icon-microphone' : 'el-icon-mute'"
                      @click="changeSupportVoice(item)"
                    ></ltw-icon>
                  </el-tooltip>
                  <!--                  <el-switch size="small" v-model="item.supportVoice"></el-switch>-->
                </div>
                <div
                  class="tag-attribute"
                  v-if="attributes.includes(TAG_ATTRIBUTES.SUPPORT_TRIGGER)"
                  @click="changeSupportTrigger(item)"
                >
                  <el-tooltip effect="dark" :content="$t('是否触发录制')" placement="top">
                    <ltw-icon
                      :icon-code="item.supportTrigger ? 'el-icon-pointer' : 'svg-pointer-off'"
                    ></ltw-icon>
                  </el-tooltip>
                  <!--                  <el-switch size="small" v-model="item.supportTrigger"></el-switch>-->
                </div>
                <div
                  class="tag-attribute"
                  v-if="item.type === 'transient' && attributes.includes(TAG_ATTRIBUTES.PREVIOUS_DURATION)"
                >
                  <el-input-number
                    v-model="item.previousDuration"
                    placeholder=""
                    :min="0"
                    :max="9999"
                    :precision="0"
                    size="small"
                    controls-position="right"
                    class="tag-duration-input"
                    place
                  ></el-input-number>
                  <span class="tag-unit">秒</span>
                </div>
                <div
                  class="tag-attribute"
                  v-if="
                    item.type === 'transient' &&
                    attributes.includes(TAG_ATTRIBUTES.PREVIOUS_DURATION) &&
                    attributes.includes(TAG_ATTRIBUTES.FOLLOWING_DURATION)
                  "
                >
                  ~
                </div>
                <div
                  class="tag-attribute"
                  v-if="item.type === 'transient' && attributes.includes(TAG_ATTRIBUTES.FOLLOWING_DURATION)"
                >
                  <el-input-number
                    v-model="item.followingDuration"
                    placeholder=""
                    :min="0"
                    :max="9999"
                    :precision="0"
                    size="small"
                    controls-position="right"
                    class="tag-duration-input"
                  ></el-input-number>
                  <span class="tag-unit">秒</span>
                </div>
              </div>
              <div v-else class="edit-tag-form">
                <!--                <div v-if="item.min || item.minDuration">：</div>-->
                <div class="tag-num" v-if="item.min && attributes.includes(TAG_ATTRIBUTES.MIN)">
                  {{ item.min }}
                  <span class="tag-unit">次</span>
                </div>
                <div v-if="item.type === 'continuous' && attributes.includes(TAG_ATTRIBUTES.MIN_DURATION)">
                  <span class="tag-num" v-if="item.minDuration"
                    >{{ item.minDuration }} <span class="tag-unit">秒</span>
                  </span>
                </div>
                <div
                  class="tag-attribute"
                  v-if="item.supportVoice && attributes.includes(TAG_ATTRIBUTES.SUPPORT_VOICE)"
                >
                  <ltw-icon :icon-code="item.supportVoice ? 'el-icon-microphone' : 'el-icon-mute'"></ltw-icon>
                </div>
                <div
                  class="tag-attribute"
                  v-if="item.supportTrigger && attributes.includes(TAG_ATTRIBUTES.SUPPORT_TRIGGER)"
                >
                  <ltw-icon
                    :icon-code="item.supportTrigger ? 'el-icon-pointer' : 'svg-pointer-off'"
                  ></ltw-icon>
                </div>
                <div
                  class="tag-attribute"
                  v-if="
                    item.type === 'transient' &&
                    attributes.includes(TAG_ATTRIBUTES.PREVIOUS_DURATION) &&
                    (item.previousDuration === 0 || item.previousDuration)
                  "
                >
                  {{ item.previousDuration }}
                  <span class="tag-unit">秒</span>
                </div>
                <div
                  class="tag-attribute"
                  v-if="
                    item.type === 'transient' &&
                    attributes.includes(TAG_ATTRIBUTES.PREVIOUS_DURATION) &&
                    attributes.includes(TAG_ATTRIBUTES.FOLLOWING_DURATION) &&
                    (item.previousDuration === 0 ||
                      item.previousDuration ||
                      item.followingDuration === 0 ||
                      item.followingDuration)
                  "
                >
                  ~
                </div>
                <div
                  class="tag-attribute"
                  v-if="
                    item.type === 'transient' &&
                    attributes.includes(TAG_ATTRIBUTES.FOLLOWING_DURATION) &&
                    (item.followingDuration === 0 || item.followingDuration)
                  "
                >
                  {{ item.followingDuration }}
                  <span class="tag-unit">秒</span>
                </div>
              </div>
            </el-tag>
          </template>
        </VueDraggable>
      </div>
    </div>
  </el-card>
</template>

<script>
import { VueDraggable } from 'vue-draggable-plus'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import BsTagGroupCard from '@/components/dataCollect/BsTagGroupCard'
import { BS_TAG_TYPE, VT_TAG_STATUS } from '@/plugins/constants/data-dictionary'
import {
  ElCard,
  ElBadge,
  ElPopover,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElButton,
  ElTag,
  ElInputNumber
} from 'element-plus'
import { getLocale } from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'
import TAG_ATTRIBUTES from '@/plugins/constants/tag-attributes.js'

export default {
  components: {
    LtwIcon,
    BsTagGroupCard,
    ElCard,
    ElBadge,
    ElPopover,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElButton,
    ElTag,
    ElInputNumber,
    VueDraggable
  },
  name: 'BsTagGroup',
  props: {
    tagGroup: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editTags: {
      type: Boolean,
      default: false
    },
    model: {
      type: String,
      default: 'priview'
    },
    continuousUnits: {
      type: Array,
      default: () => []
    },
    timeEdit: {
      type: Boolean,
      default: true
    },
    classificationTag: {
      type: Boolean,
      default: false
    },
    attributes: {
      type: Array,
      default: () => [TAG_ATTRIBUTES.MIN, TAG_ATTRIBUTES.MIN_DURATION]
    }
  },
  watch: {
    // 监听 props 变化并同步到本地副本
    data(newVal) {
      this.dragData = newVal
    }
    // attributes: {
    //   handler(newVal) {
    //     console.log(newVal)
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  emits: ['tagClick', 'tag-set'],
  data() {
    return {
      units: [
        {
          name: 'Times',
          conversion: '1'
        }
      ],
      // continuousUnits: [],
      form: {},
      locale: getLocale(),
      TAG_ATTRIBUTES
    }
  },
  created() {},
  methods: {
    handleGroupTagClick(param) {
      this.$emit('tagClick', param)
    },
    handleTagSet(param) {
      this.$emit('tag-set', param)
    },
    handleClose(tag, tagGroup) {
      let msg = BASE_CONSTANT.DELETE_CONFIRM_MSG
      this.$confirm(msg, 'reminder', {
        confirmButtonText: 'confirm',
        cancelButtonText: 'cancel',
        type: 'warning'
      }).then(() => {
        tag.checked = false
        if (tagGroup.checkedTagIdList) {
          tagGroup.checkedTagIdList = tagGroup.checkedTagIdList.filter(val => val !== tag.id)
        } else {
          tagGroup.tagList = tagGroup.tagList.filter(item => item !== tag)
        }
      })
    },
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient' || tag.componentType) {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    checkTagValue(tag) {
      if (tag.type === 'continuous') {
        if (!tag.minContinuousSeconds && !tag.maxContinuousSeconds) {
          return
        }
        let minContinuousSeconds = this.calculateOptimalNode(tag.minContinuousSeconds)
        let maxContinuousSeconds = this.calculateOptimalNode(tag.maxContinuousSeconds)
        return (
          minContinuousSeconds.value +
          minContinuousSeconds.unit.charAt(0) +
          '~' +
          maxContinuousSeconds.value +
          maxContinuousSeconds.unit.charAt(0)
        )
      } else if (tag.type === 'transient') {
        if (!tag.minTimes) {
          return
        }
        return tag.minTimes + 'times'
      }
    },
    checkTagUnit(tag) {
      if (tag.type === 'continuous') {
        // if (!tag.minContinuousSeconds && !tag.maxContinuousSeconds) {
        //   return
        // }

        return (
          this.calculateOptimalNode(tag.minContinuousSeconds) +
          '~' +
          this.calculateOptimalNode(tag.maxContinuousSeconds)
        )
      } else if (tag.type === 'transient') {
        if (!tag.minTimes) {
          return
        }
        return tag.minTimes + 'times'
      }
    },
    calculateOptimalNode(value) {
      if (value) {
        for (let i = this.continuousUnits.length - 1; i >= 0; i--) {
          let node = this.continuousUnits[i]
          if (value % node.conversion === 0) {
            return {
              value: value / node.conversion,
              unit: node.code
            }
          }
        }
      } else {
        return {
          value: 0,
          unit: this.continuousUnits[0].code
        }
      }
    },
    checkGroupVisible(group) {
      if (this.classificationTag) {
        return true
      }
      if ((!group.children || group.children.length === 0) && (!group.tagList || group.tagList.length === 0)) {
        return false
      }
      if (this.model === 'priview' || this.model === 'simulation') {
        return group.checkedAll || group.isIndeterminate
      }
      return true
    },
    checkTagVisible(tag) {
      if (this.classificationTag) {
        return true
      }
      if (this.model === 'priview' || this.model === 'simulation') {
        return tag.checked
      }
      return true
    },
    onClickOutside() {},
    closeTag(item) {
      item.visible = false
    },
    confirmTag(item) {
      item.visible = false
      if (item.type === 'continuous') {
        item.minContinuousSeconds =
          this.form.minContinuousSeconds * this.getUnitValue(this.form.minContinuousUnit).conversion
        item.maxContinuousSeconds =
          this.form.maxContinuousSeconds * this.getUnitValue(this.form.maxContinuousUnit).conversion
      } else if (item.type === 'transient') {
        item.minTimes = this.form.minTimes
      }
    },
    getUnitValue(code) {
      return this.continuousUnits.find(val => val.code === code)
    },
    changeSupportVoice(item) {
      // val = !val
      item.supportVoice = !item.supportVoice
    },
    changeSupportTrigger(item) {
      // val = !val
      item.supportTrigger = !item.supportTrigger
    }
  }
}
</script>

<style scoped lang="scss">
.bs-tag-group-card {
  :deep(.el-badge__content) {
    z-index: 1;
  }

  :deep(.el-card__body) {
    padding-right: 0;
  }

  margin-top: 10px;
  overflow: visible;

  :deep .el-card__header {
    padding: 10px;
  }

  .bs-tag-group-card-header {
    display: flex;
    justify-content: space-between;
  }

  .bs-tag-group-card-body {
    min-height: 50px;

    .bs-tag-sub-group-container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;

      .vue-draggable {
        width: 100%;
      }

      .bs-tag-group-card {
        // width: 30%;
        min-width: 185px;
        margin-right: 20px;
      }

      // :nth-child(3n + 1).bs-tag-group-card {
      //   margin-left: 1%;
      // }
      // :nth-child(3n).bs-tag-group-card {
      //   margin-right: 1%;
      // }
    }

    .bs-tag-tontainer {
      width: 100%;
      // display: flex;
      // flex-wrap: wrap;
      // flex-direction: row;
      // justify-content: flex-start;
      // align-items: center;
      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
        cursor: pointer;

        :deep(.el-tag__content) {
          display: flex;
        }

        .edit-tag-form {
          display: flex;
          align-items: center;

          .tag-attribute {
            display: flex;
            align-items: center;
            margin-left: 4px;

            .ltw-icon :deep(.el-icon) {
              font-size: 12px;
            }
          }

          .tag-duration-input {
            margin-right: 5px;
            margin-left: 4px;
          }
        }

        .tag-name {
          margin-right: 10px;
          display: flex;
          align-items: center;

          .form-component {
            color: orange;
          }

          &.view {
            margin-right: 0;
          }
        }

        .tag-num {
          margin-right: 3px;
        }
      }

      .el-button {
        margin-bottom: 5px;
        margin-right: 5px;
        margin-left: 0px;
      }

      .el-button.cannot-click {
        cursor: auto;
      }

      .el-button.tag-active {
        background: var(--el-button-bg-color);
        border-color: var(--el-button-border-color);
        color: var(--el-button-text-color);
      }

      .el-button.el-button--danger.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #fef0f0;
        border-color: #fbc4c4;
      }

      .el-button.el-button--primary.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #ecf5ff;
        border-color: #b3d8ff;
      }

      .el-button.el-button--warning.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #fdf6ec;
        border-color: #f5dab1;
      }

      .el-button.el-button--success.tag-inactive {
        color: var(--el-button-text-color);
        background-color: #f0f9eb;
        border-color: #c2e7b0;
      }
    }
  }
}
</style>
