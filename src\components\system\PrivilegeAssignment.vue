<template>

  <el-dialog
    title="权限分配"
    v-model="visible"
    width="85%"
    @close="dialogClosed"
    @open="dialogOpened"
  >
    <div>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            placeholder="请输入关键字"
            v-model="queryKey"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
      </div>
    </div>
    <div class="app-container">
      <div class="has-assigned">
        <el-table
          :data="hasAssignedApplicationPageData.records"
          @expand-change="handleExpandChange"
          row-key="id"
          ref="hasAssignedTableRef"
          @selection-change="handleHasAssignedSelectionChange"
          :expand-row-keys="expandRowKeys"
        >
          <el-table-column
            header-align="left"
            align="left"
            type="selection"
            width="55"
          ></el-table-column>
          <el-table-column header-align="left" align="left" type="expand">
            <template #default="scope">
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-tree
                    node-key="id"
                    :props="menuTreeProps"
                    :data="scope.row.menuTree"
                    :default-checked-keys="scope.row.assignedMenus"
                    show-checkbox
                    ref="menuTreeRef"
                    default-expand-all
                    @check-change="handleTreeNodeCheckChange"
                  >
                    <template #default="{ data }">
                      <div class="node-container">
                        <div>{{ data.name }}</div>
                        <div
                          class="function-container"
                          v-if="
                            data.functionList && data.functionList.length > 0
                          "
                        >
                          <el-checkbox
                            :indeterminate="data.isIndeterminate"
                            v-model="data.checkAllFunction"
                            @change="
                              handleCheckAllFunctionChange(data, scope.row)
                            "
                            >全选</el-checkbox
                          >
                          <el-checkbox-group
                            v-model="
                              scope.row.menuMap[data.id].checkedFunctionIdList
                            "
                            @change="
                              handleCheckedFunctionChange(data, scope.row)
                            "
                          >
                            <el-checkbox
                              v-for="item in data.functionList"
                              :label="item.id"
                              :key="item.id"
                              >{{ item.name }}</el-checkbox
                            >
                          </el-checkbox-group>
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="18"> </el-col>
                <el-col
                  :span="6"
                  style="
                    display: flex;
                    justify-content: flex-end;
                    align-items: flex-end;
                  "
                  v-if="scope.row.menuTree && scope.row.menuTree.length > 0"
                >
                  <el-button
                    type="primary"
                    @click="menuAssign(scope.row)"
                    >保存</el-button
                  >
                </el-col>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="name"
            label="名称"
          ></el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="icon"
            label="图标"
            width="100"
          >
            <template #default="scope">
              <div class="icon-container">
                <ltw-auth-image
                  :auth-src="scope.row.icon ? downloadUrl + scope.row.icon : ''"
                  fit="fill"
                >
                  <template #error>
                    <div class="image-slot">
                      <el-icon><picture-outline /></el-icon>
                    </div>
                  </template>
                </ltw-auth-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="tagList"
            label="标签"
          >
            <template #default="scope">
              <div class="tag-container">
                <el-tag
                  :key="tag.id"
                  type="success"
                  v-for="tag in scope.row.tagList"
                  >{{ tag.tagName }}</el-tag
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            label="状态"
            width="80"
          >
            <template #default="scope">
              <el-switch v-model="scope.row.enabled" disabled=""></el-switch>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          small
          @size-change="handleHasAssignedSizeChange"
          @current-change="handleHasAssignedCurrentChange"
          :current-page="hasAssignedQueryParam.current"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="hasAssignedQueryParam.size"
          layout="total, sizes, prev, pager, next"
          :total="hasAssignedApplicationPageData.total"
        >
        </el-pagination>
      </div>
      <div class="transfer">
        <el-button
          type="success"
          icon="el-icon-arrow-left"
          :disabled="selectedHasNotAssignedApplications.length === 0"
          @click="assignApp"
          >分配
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedHasAssignedApplications.length === 0"
          @click="removeApp"
          >删除 <i class="el-icon-arrow-right el-icon--right"></i
        ></el-button>
      </div>
      <div class="has-not-assigned">
        <el-table
          :data="hasNotAssignedApplicationPageData.records"
          row-key="id"
          ref="hasNotAssignedTableRef"
          @selection-change="handleHasNotAssignedSelectionChange"
        >
          <el-table-column
            header-align="left"
            align="left"
            type="selection"
            width="55"
          ></el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="name"
            label="名称"
          ></el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="icon"
            label="图标"
            width="100"
          >
            <template #default="scope">
              <div class="icon-container">
                <ltw-auth-image
                  :auth-src="scope.row.icon && downloadUrl + scope.row.icon"
                  fit="fill"
                >
                  <template #error>
                    <div class="image-slot">
                      <el-icon><picture-outline /></el-icon>
                    </div>
                  </template>
                </ltw-auth-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="tagList"
            label="标签"
          >
            <template #default="scope">
              <div class="tag-container">
                <el-tag
                  :key="tag.id"
                  type="success"
                  v-for="tag in scope.row.tagList"
                  >{{ tag.tagName }}</el-tag
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            label="状态"
            width="80"
          >
            <template #default="scope">
              <el-switch v-model="scope.row.enabled" disabled></el-switch>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          small
          @size-change="handleHasNotAssignedSizeChange"
          @current-change="handleHasNotAssignedCurrentChange"
          :current-page="hasNotAssignedQueryParam.current"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="hasNotAssignedQueryParam.size"
          layout="total, sizes, prev, pager, next"
          :total="hasNotAssignedApplicationPageData.total"
        >
        </el-pagination>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  pageHasAssignedSysPrivilegeApplication,
  pageHasNotAssignedSysPrivilegeApplicationVO
} from '@/apis/system/sys-privilege-application'
import {
  assignPrivilege,
  deleteSysRolePrivilegeRelation,
  reassignPrivilegeList
} from '@/apis/system/sys-role-privilege-relation'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwAuthImage from '@/components/base/LtwAuthImage'
import PRIVILEGE_TYPE from '@/plugins/constants/privilege-type'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  listSysPrivilegeMenu,
  treeListSysPrivilegeMenu
} from '@/apis/system/sys-privilege-menu'
import { listSysPrivilegeModule } from '@/apis/system/sys-privilege-module'

export default {
  name: 'PrivilegeAssignment',
  components: { LtwAuthImage },
  props: {
    modelValue: Boolean,
    roleId: [String, Number],
    roleType: String
  },
  emits: ['open', 'close', 'update:modelValue'],
  data() {
    return {
      hasAssignedApplicationPageData: {},
      hasNotAssignedApplicationPageData: {},
      pageData: {
        total: 0
      },
      queryKey: '',
      hasAssignedQueryParam: {
        current: 1,
        size: 5,
        withTag: true
      },
      hasNotAssignedQueryParam: {
        current: 1,
        size: 5,
        withTag: true
      },
      downloadUrl: GLB_CONFIG.devUrl.fileServer + BASE_CONSTANT.URL_SEPARATOR,
      selectedHasAssignedApplications: [],
      selectedHasNotAssignedApplications: [],
      menuTreeProps: {
        label: 'name',
        children: 'children'
      },
      expandRowKeys: [],
      checkAllFunction: false,
      applicationId:''
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  create() {},
  methods: {
    dialogClosed() {
      // this.queryParam = {
      //     current: 1,
      //     size: 5,
      //     withTag:true
      // }
      this.queryKey = ''
      this.selectedHasAssignedApplications = []
      this.selectedHasNotAssignedApplications = []
      this.$refs.hasNotAssignedTableRef.clearSelection()
      this.$refs.hasAssignedTableRef.clearSelection()
      this.expandRowKeys = []
      this.$emit('close')
      this.$emit('update:modelValue', false)
    },
    dialogOpened() {
      this.hasAssignedQueryParam.roleId = this.roleId
      this.hasNotAssignedQueryParam.roleId = this.roleId
      this.query()
      this.$emit('open')
      this.$emit('update:modelValue', true)
    },
    cancel() {
      this.dialogClosed()
    },
    refresh() {
      this.$refs.hasNotAssignedTableRef.clearSelection()
      this.$refs.hasAssignedTableRef.clearSelection()
      this.query()
    },
    query() {
      this.queryHasAssignedApp()
      this.queryHasNotAssignedApp()
    },
    queryHasAssignedApp() {
      this.hasAssignedQueryParam.key = this.queryKey
      pageHasAssignedSysPrivilegeApplication(this.hasAssignedQueryParam).then(
        res => {
          this.hasAssignedApplicationPageData = res.data
        }
      )
    },
    queryHasNotAssignedApp() {
      this.hasNotAssignedQueryParam.key = this.queryKey
      pageHasNotAssignedSysPrivilegeApplicationVO(
        this.hasNotAssignedQueryParam
      ).then(res => {
        this.hasNotAssignedApplicationPageData = res.data
      })
    },
    handleHasAssignedSizeChange(value) {
      this.hasAssignedQueryParam.size = value
      this.queryHasAssignedApp()
    },
    handleHasAssignedCurrentChange(value) {
      this.hasAssignedQueryParam.current = value
      this.queryHasAssignedApp()
    },
    handleHasNotAssignedSizeChange(value) {
      this.hasNotAssignedQueryParam.size = value
      this.queryHasNotAssignedApp()
    },
    handleHasNotAssignedCurrentChange(value) {
      this.hasNotAssignedQueryParam.current = value
      this.queryHasNotAssignedApp()
    },
    handleHasAssignedSelectionChange(value) {
      this.selectedHasAssignedApplications = value
    },
    handleHasNotAssignedSelectionChange(value) {
      this.selectedHasNotAssignedApplications = value
    },
    handleExpandChange(row, expandedRows) {
      this.applicationId=row.id
      if (expandedRows.indexOf(row) === -1) {
        this.expandRowKeys.splice(this.expandRowKeys.indexOf(row.id), 1)
        return
      }
      this.expandRowKeys.push(row.id)
      row.menuMap = {}
      Promise.all([
        this.listModule(row),
        this.treeListMenu(row),
        this.listCheckedMenu(row)
      ]).then(([moduleList, menuList, checkedMenuList]) => {
        this.setMenuMap(menuList, row.menuMap)
        if (menuList && menuList.length > 0) {
          if (moduleList && moduleList.length > 0) {
            let menuTree = []
            moduleList.forEach(module => {
              let root = {
                id: module.id,
                name: module.name,
                type: 'root',
                children: []
              }
              menuList.forEach(menu => {
                if (menu.moduleId === module.id) {
                  root.children.push(menu)
                }
              })
              menuTree.push(root)
            })
            row.menuTree = menuTree
          } else {
            row.menuTree = menuList
          }
        }
        if (checkedMenuList && checkedMenuList.length > 0) {
          row.assignedMenus = []
          checkedMenuList.forEach(ele => {
            if (ele.asLeaf) {
              row.assignedMenus.push(ele.id)
              row.menuMap[ele.id].checkedFunctionIdList = []
              if (ele.functionList && ele.functionList.length > 0) {
                ele.functionList.forEach(func => {
                  row.menuMap[ele.id].checkedFunctionIdList.push(func.id)
                })
                let checkCount = ele.functionList.length
                row.menuMap[ele.id].checkAllFunction =
                  checkCount === row.menuMap[ele.id].functionList.length
                row.menuMap[ele.id].isIndeterminate =
                  checkCount > 0 &&
                  checkCount < row.menuMap[ele.id].functionList.length
              }
            }
          })
        }
      })
    },
    setMenuMap(menuList, map) {
      if (menuList && menuList.length > 0) {
        menuList.forEach(menu => {
          map[menu.id] = menu
          menu.checkedFunctionIdList = []
          menu.isIndeterminate = false
          menu.checkAllFunction = false
          if (menu.children && menu.children.length > 0) {
            this.setMenuMap(menu.children, map)
          }
        })
      }
    },
    listModule(row) {
      return new Promise(resolve => {
        listSysPrivilegeModule({ applicationId: row.id }).then(res => {
          resolve(res.data)
        })
      })
    },
    treeListMenu(row) {
      return new Promise(resolve => {
        treeListSysPrivilegeMenu({
          applicationId: row.id,
          enabled: true,
          withFunction: true
        }).then(res => {
          resolve(res.data)
        })
      })
    },
    listCheckedMenu(row) {
      return new Promise(resolve => {
        listSysPrivilegeMenu({
          roleId: this.roleId,
          applicationId: row.id,
          withFunction: true
        }).then(res => {
          resolve(res.data)
        })
      })
    },
    assignApp() {
      let data = {}
      data.roleId = this.roleId
      data.type =
        this.roleType +
        BASE_CONSTANT.ROLE_PRIVILEGE_SEPARATOR +
        PRIVILEGE_TYPE.APPLICATION
      data.privilegeIdList = []
      this.selectedHasNotAssignedApplications.forEach(ele => {
        data.privilegeIdList.push(ele.id)
      })
      assignPrivilege(data).then(() => {
        this.refresh()
      })
    },
    removeApp() {
      let param = {}
      param.roleId = this.roleId
      param.type =
        this.roleType +
        BASE_CONSTANT.ROLE_PRIVILEGE_SEPARATOR +
        PRIVILEGE_TYPE.APPLICATION
      let privilegeIdList = []
      this.selectedHasAssignedApplications.forEach(ele => {
        privilegeIdList.push(ele.id)
      })
      param.privilegeIds = privilegeIdList.join(',')
      deleteSysRolePrivilegeRelation(param).then(() => {
        this.refresh()
      })
    },
    menuAssign(row) {
      let nodes = this.$refs.menuTreeRef.getCheckedNodes(false, true)
      let menuKeys = []
      let moduleKeys = []
      let functionKeys = []
      if (nodes && nodes.length > 0) {
        nodes.forEach(node => {
          if (node.type === 'root') {
            moduleKeys.push(node.id)
          } else {
            menuKeys.push(node.id)
            if (
              row.menuMap[node.id].checkedFunctionIdList &&
              row.menuMap[node.id].checkedFunctionIdList.length > 0
            ) {
              functionKeys = functionKeys.concat(
                row.menuMap[node.id].checkedFunctionIdList
              )
            }
          }
        })
      }
      reassignPrivilegeList([
        {
          roleId: this.roleId,
          type:
            this.roleType +
            BASE_CONSTANT.ROLE_PRIVILEGE_SEPARATOR +
            PRIVILEGE_TYPE.MENU,
          privilegeIdList: menuKeys
        },
        {
          roleId: this.roleId,
          type:
            this.roleType +
            BASE_CONSTANT.ROLE_PRIVILEGE_SEPARATOR +
            PRIVILEGE_TYPE.MODULE,
          privilegeIdList: moduleKeys
        },
        {
          roleId: this.roleId,
          type:
            this.roleType +
            BASE_CONSTANT.ROLE_PRIVILEGE_SEPARATOR +
            PRIVILEGE_TYPE.FUNCTION,
          privilegeIdList: functionKeys
        },
        
      ],{'applicationId':row.id}).then(() => this.$message.success('保存成功'))
    },
    handleCheckAllFunctionChange(data) {
      data.isIndeterminate = false
      if (data.checkAllFunction) {
        data.checkedFunctionIdList = []
        data.functionList.forEach(func => {
          data.checkedFunctionIdList.push(func.id)
        })
      } else {
        data.checkedFunctionIdList = []
      }
    },
    handleCheckedFunctionChange(data) {
      let checkCount = data.checkedFunctionIdList.length
      data.isIndeterminate =
        checkCount > 0 && checkCount < data.functionList.length
      data.checkAllFunction = checkCount === data.functionList.length
    },
    handleTreeNodeCheckChange(data, checked) {
      if (!data.functionList || data.functionList.length === 0) {
        return
      }
      if (checked) {
        data.checkAllFunction = true
      } else {
        data.checkAllFunction = false
      }
      this.handleCheckAllFunctionChange(data)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .has-assigned {
    width: 40%;
  }

  .transfer {
    display: flex;
    flex-direction: row;
    justify-items: center;
    align-items: center;
  }

  .has-not-assigned {
    width: 40%;
  }
  :deep .el-table__expanded-cell {
    padding: 20px 20px;
  }
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .ltw-auth-image {
    width: 40px;
    height: 40px;

    .image-slot {
      font-size: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
    }
  }
}
.el-tree {
  :deep .el-tree-node {
    margin: 10px 0px;
    .el-tree-node__content {
      height: auto;
      .node-container {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .function-container {
          margin-left: 15px;
          display: flex;
          flex-direction: row;
          align-items: center;
          .el-checkbox-group {
            margin-left: 10px;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}
</style>
