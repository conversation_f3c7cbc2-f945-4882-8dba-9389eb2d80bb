import { Matrix, Vector3, Quaternion } from './trimmed-babylonjs';
const quaternion = new Quaternion();
const scaling = new Vector3(1, 1, 1);
const translation = new Vector3(0, 0, 0);
const direction = new Vector3();
const rotation = new Vector3(0, 0, 0);
const axis1 = new Vector3();
const axis2 = new Vector3();
export function matrixFromEulerVector(value) {
    const quaternion = Quaternion.FromEulerAngles(value.vector.x, value.vector.y, value.vector.z);
    return matrixFromQuaternion({ quaternion, translate: value.translate });
}
export function matrixFromQuaternion(value) {
    var _a, _b, _c;
    const matrix = new Matrix();
    quaternion.x = value.quaternion.x;
    quaternion.y = value.quaternion.y;
    quaternion.z = value.quaternion.z;
    quaternion.w = value.quaternion.w;
    translation.x = ((_a = value.translate) === null || _a === void 0 ? void 0 : _a.x) || 0;
    translation.y = ((_b = value.translate) === null || _b === void 0 ? void 0 : _b.y) || 0;
    translation.z = ((_c = value.translate) === null || _c === void 0 ? void 0 : _c.z) || 0;
    Matrix.ComposeToRef(scaling, quaternion, translation, matrix);
    return matrix;
}
export function matrixFromDirection(value) {
    var _a, _b, _c;
    const matrix = new Matrix();
    direction.x = value.direction.x || 0;
    direction.y = value.direction.y || 0;
    direction.z = value.direction.z || 0;
    translation.x = ((_a = value.translate) === null || _a === void 0 ? void 0 : _a.x) || 0;
    translation.y = ((_b = value.translate) === null || _b === void 0 ? void 0 : _b.y) || 0;
    translation.z = ((_c = value.translate) === null || _c === void 0 ? void 0 : _c.z) || 0;
    direction.normalize();
    Vector3.RotationFromAxisToRef(direction, axis1, axis2, rotation);
    Quaternion.FromEulerVectorToRef(rotation, quaternion);
    Matrix.ComposeToRef(scaling, quaternion, translation, matrix);
    return matrix;
}
