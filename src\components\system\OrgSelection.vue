<template>
    <el-cascader
            placeholder="可输入名称搜索"
            :options="orgs"
            :props="props"
            v-model="selectValue"
            :show-all-levels="false"
            filterable
            clearable
            popper-class="org-popper-class"
            class="org-selection"
            @change="handleChange"
            ref="orgSelection"
    >
    </el-cascader>
</template>

<script>
    import {treeListSysRoleOrg} from '@/apis/system/sys-role-org'

    export default {
        name: "OrgSelection",
        data(){
            return{
                orgs:[],
                props:{
                    checkStrictly: true,
                    value:'id',
                    label:'name',
                    children:'children',
                    leaf:'asLeaf',
                    emitPath:false,
                    expandTrigger: 'hover'
                }
            }
        },
        computed:{
            selectValue:{
                get(){
                    return this.modelValue
                },
                set(val){
                    this.$emit('update:modelValue', val)
                }
            }
        },
        props:{
            modelValue:[String,Number]
        },
        emits:['change','update:modelValue'],
        created(){
            this.getOrgs()
        },
        methods:{
            getOrgs(){
                treeListSysRoleOrg().then(
                    res => {
                        this.orgs = res.data
                        this.$nextTick(function () {
                            // if (this.orgs && this.orgs.length > 0) {
                            //     this.expandedOrgs = [this.orgs[0].id]
                            //     this.$refs.orgTreeRef.setCurrentKey(this.orgs[0].id, true)
                            //     this.handleNodeClick(this.orgs[0])
                            // }
                        })
                    }
                )
            },
            handleChange(value){
                this.$emit('change',value)
                this.$emit('update:modelValue',value)
            }
        }
    }
</script>

<style  lang="scss">
    .org-selection{
        .ltw-input{
            width: 300px;
        }
    }
</style>
