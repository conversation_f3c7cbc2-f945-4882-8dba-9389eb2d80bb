import cryptoJS from 'crypto-js'
import GLB_CONFIG from '@/plugins/glb-constant'
import router from '@/router'
import store from '@/store'
import { logout } from '@/apis/base/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import { i18n } from '@/plugins/lang'
import { getLogin } from '@/apis/base/index'
import PcdPreviewer, {
  Matrix,
  matrixFromDirection,
  matrixFromQuaternion,
  matrixFromEulerVector
} from '@/plugins/pcd-lib'

export default {
  logoutMessageInstance: null,
  // 加密
  encrypt(word, keyStr) {
    keyStr = keyStr || 'fghjklkbshikjbha' // 判断是否存在ksy，不存在就用定义好的key
    const key = cryptoJS.enc.Utf8.parse(keyStr)
    const srcs = cryptoJS.enc.Utf8.parse(word)
    const encrypted = cryptoJS.AES.encrypt(srcs, key, {
      mode: cryptoJS.mode.ECB,
      padding: cryptoJS.pad.Pkcs7
    })
    return encrypted.toString()
  },
  // 解密
  decrypt(word, keyStr) {
    keyStr = keyStr || 'fghjklkbshikjbha'
    const key = cryptoJS.enc.Utf8.parse(keyStr)
    const decrypt = cryptoJS.AES.decrypt(word, key, {
      mode: cryptoJS.mode.ECB,
      padding: cryptoJS.pad.Pkcs7
    })
    return cryptoJS.enc.Utf8.stringify(decrypt).toString()
  },
  logout() {
    if (GLB_CONFIG.usingCase) {
      const token = this.getToken()
      localStorage.removeItem('token')
      window.location.replace(GLB_CONFIG.devUrl.serviceSiteRootUrl + '/logout?token=' + token)
    } else {
      if (process.env.VUE_APP_PORTAL === 'true') {
        logout().then(() => {
          this.toLogin()
        })
      } else {
        this.toLogin()
      }
    }
  },
  getToken() {
    return localStorage.getItem(GLB_CONFIG.tokenKey)
  },
  setToken(token) {
    localStorage.setItem(GLB_CONFIG.tokenKey, token)
  },
  formatParams(data, isPrefix = false) {
    const prefix = isPrefix ? '?' : ''
    const _result = []
    for (const key in data) {
      const value = data[key]
      // 去掉为空的参数
      if (['', undefined, null].includes(value)) {
        continue
      }
      if (value.constructor === Array) {
        value.forEach(_value => {
          _result.push(encodeURIComponent(key) + '[]=' + encodeURIComponent(_value))
        })
      } else {
        _result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value))
      }
    }
    return _result.length ? prefix + _result.join('&') : ''
  },
  toLogin(fullPath) {
    store.dispatch('clearLoginInfo')
    localStorage.removeItem('token')
    if (process.env.VUE_APP_PORTAL === 'true') {
      getLogin({ client_id: GLB_CONFIG.applicationId }).then(res => {
        const postData = {
          locale: getLocale(),
          loginTheme: localStorage.getItem('login-theme') || 'light'
        }
        if (!~fullPath?.indexOf('/token')) {
          postData.redirect = fullPath
        } else {
          setTimeout(() => {
            window.location.reload()
          }, 500)
        }
        window.location.href = res.data + '&' + this.formatParams(postData)
      })
    } else {
      if (GLB_CONFIG.usingCase) {
        // router.replace(GLB_CONFIG.devUrl.loginUrl)
        window.location.replace(GLB_CONFIG.devUrl.loginUrl)
      } else {
        const postData = {
          redirect: fullPath
        }
        router.replace({
          path: GLB_CONFIG.devUrl.loginUrl,
          query: postData
        })
      }
      this.logoutMessageInstance = null
    }
    // let winRef
    // let targetUrl
    // // 延迟1000ms
    // setTimeout(() => {
    //   winRef = window.open(targetUrl, '_blank')
    //   // winRef.document.title = 'xxx'
    // }, 0)
    // getLogin({ client_id: GLB_CONFIG.client_id }).then(res => {
    //   window.location.replace(res.data)
    //   setTimeout(() => {
    //     window.location.reload()
    //   }, 1000)
    //   // window.open(res.data)
    //   // if (winRef) {
    //   //   winRef.location.href = res.data
    //   // } else {
    //   //   targetUrl = res.data
    //   // }
    // })
    // if (GLB_CONFIG.usingCase) {
    //   // router.replace(GLB_CONFIG.devUrl.loginUrl)
    //   window.location.replace(GLB_CONFIG.devUrl.loginUrl)
    // } else {
    //   router.replace(GLB_CONFIG.devUrl.loginUrl)
    // }
    // this.logoutMessageInstance = null
  },
  loadingShow(fullLoading) {
    let dom = document.querySelector('.ltw-loading-mask')
    if (!dom) {
      dom = document.createElement('div')
      dom.innerHTML = `                    <div>
                        <div class="ltw-loading-spinner">
                            <i class="el-icon-loading"></i>
                            <p class="ltw-loading-text">加载中...</p>
                        </div>
                    </div>`
      dom.setAttribute('class', 'ltw-loading-mask' + (fullLoading ? ' full-loading' : ''))
      // if (fullLoading) {
      //   dom.setAttribute('class', 'full-loading')
      // }
      const parentDom = document.querySelector('#ltwMainContainer')
      if (parentDom) {
        parentDom.classList.add('el-loading-parent--relative')
        parentDom.append(dom)
      }
    }
    if (dom) {
      dom.setAttribute('class', 'ltw-loading-mask' + (fullLoading ? ' full-loading' : ''))
      dom.style.display = 'block'
    }
  },
  loadingHide() {
    const dom = document.querySelector('.ltw-loading-mask')
    if (dom) {
      dom.style.display = 'none'
    }
  },
  bitOperators(val) {
    const list = []
    for (let i = 0; i < val?.length; i++) {
      if (val[i] === '1') {
        list.push(val[i].padStart(i + 1, '0').padEnd(val.length, '0'))
      }
    }
    return list
  }
}

export const dateUtils = {
  formatDateNormal(date) {
    const Y = date.getFullYear()
    const M = date.getMonth() + 1
    const D = date.getDate()
    const times = Y + (M < 10 ? '-0' : '-') + M + (D < 10 ? '-0' : '-') + D
    return times
  },
  getZeroDate(date) {
    if (!date) {
      date = new Date()
    }
    let time = this.formatDateNormal(date)
    time += ' 00:00:00'
    return new Date(time)
  },
  // 日期格式化
  parseTime(time, pattern) {
    if (arguments.length === 0 || !time) {
      return null
    }
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
      date = time
    } else {
      if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
        time = parseInt(time)
      } else if (typeof time === 'string') {
        time = time.replace(new RegExp(/-/gm), '/')
      }
      if (typeof time === 'number' && time.toString().length === 10) {
        time = time * 1000
      }
      date = new Date(time)
    }
    const formatObj = {
      y: date.getFullYear(),
      m: date.getMonth() + 1,
      d: date.getDate(),
      h: date.getHours(),
      i: date.getMinutes(),
      s: date.getSeconds(),
      a: date.getDay()
    }
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
      let value = formatObj[key]
      // Note: getDay() returns 0 on Sunday
      if (key === 'a') {
        return ['日', '一', '二', '三', '四', '五', '六'][value]
      }
      if (result.length > 0 && value < 10) {
        value = '0' + value
      }
      return value || 0
    })
    return time_str
  },
  dateCalculation(second) {
    // 返回天、时、分
    if (second > 0) {
      var day = 0
      var hour = 0
      var minute = 0
      var data = {}
      minute = Math.floor(second / 60)
      if (parseInt(minute) > 60) {
        hour = parseInt(minute / 60)
        minute %= 60 // 算出有多分钟
      }
      if (parseInt(hour) > 24) {
        day = parseInt(hour / 24)
        hour %= 24 // 算出有多分钟
      }
      data.d = day
      data.h = hour
      data.m = minute
      return data
    }
  },
  formatSecToDate(sec) {
    const h = Math.floor(sec / 60 / 60)
    const m = Math.floor((sec / 60) % 60)
    const s = Math.floor(sec % 60)
    return {
      h,
      min: m,
      s,
      time: (h ? h + 'h ' : '') + (s || m ? m + 'min ' : '') + (s ? s + 's' : h || m ? '' : '0s')
    }
  }
}

// 手机或座机校验
export const validatePhoneOrTel = (rule, value, callback) => {
  if (!value && value !== 0) {
    callback()
  }
  if (!/^([1]\d{10}|([\(（]?0[0-9]{2,3}[）\)]?[-]?)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?)$/.test(value)) {
    callback(new Error(i18n.global.t('电话号码格式不正确')))
  } else {
    callback()
  }
}
// 时间转换
export const formatSeconds = seconds => {
  let secondTime = parseInt(seconds) // 秒
  let minuteTime = 0 // 分
  let hourTime = 0 // 小时
  if (secondTime > 60) {
    minuteTime = parseInt(secondTime / 60)
    secondTime = parseInt(secondTime % 60)
    if (minuteTime > 60) {
      hourTime = parseInt(minuteTime / 60)
      minuteTime = parseInt(minuteTime % 60)
    }
  }
  let result = ''
  if (minuteTime > 0) {
    result = '' + parseInt(secondTime) + 's'
  }

  if (minuteTime > 0) {
    result = '' + parseInt(minuteTime) + 'm' + result
  }
  if (hourTime > 0) {
    result = '' + parseInt(hourTime) + 'h' + result
  }
  return result
}
// 校验正数
export const isPositiveNum = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^\d+(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入正数')))
    } else {
      callback()
    }
  })
}
// 校验整数
export const isInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d+$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入整数')))
    } else {
      callback()
    }
  })
}

// 整数位两位以内验证
export const validateTwoDigitInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d{1,2}(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('整数部分不能超过2位')))
    } else {
      callback()
    }
  })
}
// 整数位三位以内验证
export const validateThreeDigitInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d{1,3}(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('整数部分不能超过3位')))
    } else {
      callback()
    }
  })
}
// 整数位三位以内验证
export const validateThirteenDigitInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d{1,13}(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('整数部分不能超过13位')))
    } else {
      callback()
    }
  })
}
// 整数位四位以内验证
export const validateFourDigitInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d{1,4}(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('整数部分不能超过4位')))
    } else {
      callback()
    }
  })
}
// 整数位七位以内验证
export const validateSevenDigitInteger = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^-?\d{1,7}(\.\d+)?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('整数部分不能超过7位')))
    } else {
      callback()
    }
  })
}
// 二位小数验证
export const validateTwoFloatValidity = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^(-)?\d+(\.\d{1,2})?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入最多2位小数')))
    } else {
      callback()
    }
  })
}
// 三位小数验证
export const validateThreeFloatValidity = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^(-)?\d+(\.\d{1,3})?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入最多3位小数')))
    } else {
      callback()
    }
  })
}

// 四位小数验证
export const validateFourDecimalValidity = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^(-)?\d+(\.\d{1,4})?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入最多4位小数')))
    } else {
      callback()
    }
  })
}
// 六位小数验证
export const validateSixDecimalValidity = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^(-)?\d+(\.\d{1,4})?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入最多6位小数')))
    } else {
      callback()
    }
  })
}

// 手机或座机校验
export const getUuid = () => {
  var temp_url = URL.createObjectURL(new Blob())
  var uuid = temp_url.toString() // blob:https://xxx.com/b250d159-e1b6-4a87-9002-885d90033be3
  URL.revokeObjectURL(temp_url)
  return uuid.substr(uuid.lastIndexOf('/') + 1)
}
// 判断字符串不能连续12345、abcde
export const isContinousNumOrChar = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const arr = value.split('')
    let flag = true
    for (let i = 2; i < arr.length - 2; i++) {
      const firstContinuation = arr[i - 1].charCodeAt() - arr[i - 2].charCodeAt() === 1
      const secondContinuation = arr[i].charCodeAt() - arr[i - 1].charCodeAt() === 1
      const thirdContinuation = arr[i + 1].charCodeAt() - arr[i].charCodeAt() === 1
      const fourthContinuation = arr[i + 2].charCodeAt() - arr[i + 1].charCodeAt() === 1
      if (firstContinuation && secondContinuation && thirdContinuation && fourthContinuation) {
        flag = false
        // break
      }
    }
    if (!flag) {
      return callback(new Error('密码不可含有连续五位的数字或字母'))
    }
  })
}

/**
 * 判断字符串是否键盘三连（横着、竖着）
 * @param {String} str
 * @returns boolean 是否满足键盘3连键
 */
export const checkKeyboardContinuousChar = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const c1 = [
      ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+'],
      ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '{', '}', '|'],
      ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ':', '"'],
      ['z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>', '?']
    ]
    const c2 = [
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
      ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
      ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'"],
      ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/']
    ]
    value = value.toLowerCase().split('')
    // 获取坐标位置
    const y = []
    const x = []
    for (let c = 0; c < value.length; c++) {
      y[c] = 0 // 当做~`键处理
      x[c] = -1
      for (let i = 0; i < c1.length; i++) {
        for (let j = 0; j < c1[i].length; j++) {
          if (value[c] == c1[i][j]) {
            y[c] = i
            x[c] = j
          }
        }
      }
      if (x[c] != -1) continue
      for (let i = 0; i < c2.length; i++) {
        for (let j = 0; j < c2[i].length; j++) {
          if (value[c] == c2[i][j]) {
            y[c] = i
            x[c] = j
          }
        }
      }
    }
    // 匹配坐标连线
    for (let c = 2; c < value.length - 2; c++) {
      // 横着同一行
      if (y[c - 2] == y[c - 1] && y[c - 1] == y[c] && y[c] == y[c + 1] && y[c + 1] == y[c + 2]) {
        // 从左往右或者从右往左一排
        if (
          (x[c - 2] + 1 == x[c - 1] && x[c - 1] + 1 == x[c] && x[c] + 1 == x[c + 1] && x[c + 1] + 1 == x[c + 2]) ||
          (x[c + 2] + 1 == x[c + 1] && x[c + 1] + 1 == x[c] && x[c] + 1 == x[c - 1] && x[c - 1] + 1 == x[c - 2])
        ) {
          return callback('密码不能包含键盘五连键')
        }
      }
      // 竖着同一列
      if (x[c - 2] == x[c - 1] && x[c - 1] == x[c] && x[c] == x[c + 1] && x[c + 1] == x[c + 2]) {
        // 从下往上或者从下往下同一列
        if (
          (y[c - 2] + 1 == y[c - 1] && y[c - 1] + 1 == y[c] && y[c] + 1 == y[c + 1] && y[c + 1] + 1 == y[c + 2]) ||
          (y[c + 2] + 1 == y[c + 1] && y[c + 1] + 1 == y[c] && y[c] + 1 == y[c - 1] && y[c - 1] + 1 == y[c - 2])
        ) {
          return callback('密码不能包含键盘五连键')
        }
      }
      // 竖着同一列（类似/而不是\的一列）
      if (
        (x[c - 2] + 1 == x[c - 1] && x[c - 1] + 1 == x[c] && x[c] + 1 == x[c + 1] && x[c + 1] + 1 == x[c + 2]) ||
        (x[c - 2] - 1 == x[c - 1] && x[c - 1] - 1 == x[c] && x[c] - 1 == x[c + 1] && x[c + 1] - 1 == x[c + 2])
      ) {
        // 从下往上或者从下往下同一列
        if (
          (y[c - 2] + 1 == y[c - 1] && y[c - 1] + 1 == y[c] && y[c] + 1 == y[c + 1] && y[c + 1] + 1 == y[c + 2]) ||
          (y[c + 2] + 1 == y[c + 1] && y[c + 1] + 1 == y[c] && y[c] + 1 == y[c - 1] && y[c - 1] + 1 == y[c - 2])
        ) {
          return callback('密码不能包含键盘五连键')
        }
      }
    }
    return callback()
  })
}

// 校验两位小数
export const isTwoFloorValidity = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  setTimeout(() => {
    const re = /^(-)?\d+(\.\d{1,2})?$/
    const rsCheck = re.test(value)
    if (!rsCheck) {
      callback(new Error(i18n.global.t('请输入最多两位小数')))
    } else {
      callback()
    }
  })
}
// 获取本地语言
// 设置语言 选项 en | zh
export const getLocale = () => {
  const lang = typeof window.localStorage !== 'undefined' ? window.localStorage.getItem(localeKey) || 'zh' : 'zh'
  return lang
}

// window.localStorage 存储key
export const localeKey = 'locale'
export const setLocale = (lang, realReload = true, callback = () => {}) => {
  if (getLocale() !== lang) {
    if (typeof window.localStorage !== 'undefined') {
      window.localStorage.setItem(localeKey, lang || '')
    }

    if (realReload) {
      window.location.reload()
    } else {
      setHtmlLang(lang)

      if (typeof callback === 'function') {
        callback()
      }
    }
  }
}
export const setHtmlLang = lang => {
  /**
   * axios.defaults.headers.common['Accept-Language'] = locale
   */
  document.querySelector('html').setAttribute('lang', lang)
}
/* 一维数组转换为二维数组 */
export const transferArr = (arr, num) => {
  const transferArr = []
  for (let i = 0, len = Math.ceil(arr.length / num); i < len; i++) {
    const item = []
    for (let j = 0; j < num; j++) {
      if (arr[i * num + j]) {
        item.push(arr[i * num + j])
      }
    }
    transferArr.push(item)
  }
  return transferArr
}
/* 防抖 */
// export const debounce = (f, wait) => {
//   let timer
//   return (...args) => {
//     clearTimeout(timer)
//     timer = setTimeout(() => {
//       f(...args)
//     }, wait)
//   }
// }
export const debounce = (func, delay = 300, immediate = false) => {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    if (immediate && !timer) {
      func.apply(this, arguments)
    }
    timer = setTimeout(() => {
      func.apply(this, arguments)
    }, delay)
  }
}
/* 节流 */
export const throttle = (fun, delay) => {
  let last, deferTimer
  return function () {
    const that = this
    // let _args = args
    const now = +new Date()
    if (last && now < last + delay) {
      clearTimeout(deferTimer)
      deferTimer = setTimeout(function (...args) {
        const _args = args
        last = now
        fun.apply(that, _args)
      }, delay)
    } else {
      last = now
      fun.apply(that, arguments)
    }
  }
}
// export const throttle = (f, wait) => {
//   let timer
//   return (...args) => {
//     if (timer) {
//       return
//     }
//     timer = setTimeout(() => {
//       f(...args)
//       timer = null
//     }, wait)
//   }
// }
/* 生成txt文件下载 */
export const downloadTxt = (filename, text) => {
  var pom = document.createElement('a')
  pom.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text))
  pom.setAttribute('download', filename)
  if (document.createEvent) {
    var event = document.createEvent('MouseEvents')
    event.initEvent('click', true, true)
    pom.dispatchEvent(event)
  } else {
    pom.click()
  }
}
/* 生成文件下载 */
export const downloadFile = (data, name) => {
  // 创建一个下载链接
  var url = window.URL.createObjectURL(data)

  // 创建一个 <a> 元素，设置下载链接和文件名
  var a = document.createElement('a')
  a.href = url
  name && (a.download = name)

  // 将 <a> 元素添加到 DOM 中
  document.body.appendChild(a)

  // 模拟点击下载链接
  a.click()

  // 从 DOM 中移除 <a> 元素
  document.body.removeChild(a)

  // 释放 Blob 对象的 URL
  window.URL.revokeObjectURL(url)
}
/* 通用提示框 */
export const showToast = (message, type, showClose) => {
  ElMessage({
    message: i18n.global.t(message) || '',
    type: type || 'success',
    showClose: showClose || false
  })
}
/* 通用确认框 */
export const showConfirmToast = config => {
  return new Promise((resolve, reject) => {
    ElMessageBox({
      title: i18n.global.t(config.title || '提示'),
      message: i18n.global.t(config.message),
      type: config.type || 'warning',
      showCancelButton: config.showCancelButton || true,
      confirmButtonText: i18n.global.t(config.confirmButtonText || '确认'),
      cancelButtonText: i18n.global.t(config.cancelButtonText || '取消'),
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          resolve(action)
          done()
        } else {
          showToast('操作已取消', 'info')
          reject(action)
          done()
        }
      }
    })
  })
}
export const copyText = (text = '') => {
  try {
    return navigator.clipboard
      .writeText(text)
      .then(() => {
        return Promise.resolve()
      })
      .catch(err => {
        return Promise.reject(err)
      })
  } catch (e) {
    const input = document.createElement('input')
    input.style.position = 'fixed'
    input.style.top = '-10000px'
    input.style.zIndex = '-999'
    document.body.appendChild(input)
    input.value = text
    input.focus()
    input.select()
    try {
      const result = document.execCommand('copy')
      document.body.removeChild(input)
      if (!result || result === 'unsuccessful') {
        return Promise.reject('复制失败')
      } else {
        return Promise.resolve()
      }
    } catch (e) {
      document.body.removeChild(input)
      return Promise.reject('当前浏览器不支持复制功能，请检查更新或更换其他浏览器操作')
    }
  }
}
export const formatLineBarCharts = data => {
  const legend = []
  const seriesData = []
  data.forEach((value, index) => {
    const item = []
    value.data.forEach(val => {
      if (!index) {
        legend.push(val.times)
      }
      item.push(val.value)
    })
    seriesData.push({
      data: item
    })
  })
  return { legend, seriesData }
}

/* 通用数字格式化*/
export const numUtils = {
  numFormat(value) {
    if (!value) return '0'
    value = value.toFixed(2)
    var intPart = Math.trunc(value)
    var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
    return intPartFormat
  },
  floatNumFormat(value) {
    // 有小数
    if (!value) return '0.00'
    value = value.toFixed(2)
    var intPart = Math.trunc(value) // 获取整数部分
    var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
    var floatPart = '.00' // 预定义小数部分
    var value2Array = value.split('.')
    // =2表示数据有小数位
    if (value2Array.length === 2) {
      floatPart = value2Array[1].toString() // 拿到小数部分
      if (floatPart.length === 1) {
        // 补0
        return intPartFormat + '.' + floatPart + '0'
      } else {
        return intPartFormat + '.' + floatPart
      }
    } else {
      return intPartFormat + floatPart
    }
  }
}

export const checkFileSize = data => {
  if (data > 0 && data < Math.pow(2, 10)) {
    return data + 'B'
  } else if (data >= Math.pow(2, 10) && data < Math.pow(2, 20)) {
    return parseFloat(parseFloat(data / Math.pow(2, 10)).toFixed(2)) + 'KB'
  } else if (data >= Math.pow(2, 20) && data < Math.pow(2, 30)) {
    return parseFloat(parseFloat(data / Math.pow(2, 20)).toFixed(2)) + 'M'
  } else if (data >= Math.pow(2, 30) && data < Math.pow(2, 40)) {
    return parseFloat(parseFloat(data / Math.pow(2, 30)).toFixed(2)) + 'G'
  } else if (data >= Math.pow(2, 40)) {
    return parseFloat(parseFloat(data / Math.pow(2, 40)).toFixed(2)) + 'T'
  } else {
    return 0
  }
}

export const checkTagType = tag => {
  if (tag.tagType === 'continuous' || tag.type === 'continuous') {
    return tag.mutuallyExclusive ? 'danger' : 'warning'
  }
  if (tag.tagType === 'transient' || tag.type === 'transient') {
    return tag.mutuallyExclusive ? 'success' : ''
  }
  return 'info'
}

export const pcdLib = {
  pcdPreviewer: new PcdPreviewer({
    debug: true, // debug 模式显示 xyz 轴指示器
    clearColor: [0.07, 0.14, 0.21, 1], // 画布背景色 rgba
    defaultColor: [1, 1, 1], // PCD 没有颜色时点云的默认颜色 rgb
    size: 1, // 点云尺寸系数
    opacity: 0.75, // 点云透明度
    rounded: false, // 是否圆点
    enableIntensity: false, // 是否展示反射强度
    angularSensibility: 1, // 旋转灵敏系数
    wheelSensibility: 1, // 视图缩进灵敏系数
    useLeftHandedSystem: false, // 默认使用右手坐标系， (红绿蓝轴 -> xyz)
    cameraViewParams: {
      upperBetaLimit: Math.PI // 360 旋转视角
      // upperBetaLimit: (Math.PI / 2) * 0.85 // 一般地图的视图配置，默认值
    }
  }),
  Matrix,
  matrixFromDirection,
  matrixFromQuaternion,
  matrixFromEulerVector
}

// 数字格式验证器
export const createNumberValidator = (totalLength, decimalPlaces) => {
  return function(value) {
    // 如果值为空或undefined，根据业务需求决定是否允许
    if (value === '' || value === null || value === undefined) {
      return {
        valid: false,
        message: '请输入数字'
      }
    }

    // 转换为字符串进行验证
    const strValue = String(value).trim()

    // 根据是否允许小数选择不同的正则表达式
    let numberRegex
    let formatMessage

    if (decimalPlaces === 0) {
      // 只允许整数
      numberRegex = /^-?\d+$/
      formatMessage = '请输入整数'
    } else {
      // 允许小数
      numberRegex = /^-?\d+(\.\d+)?$/
      formatMessage = '请输入有效的数字'
    }

    // 检查是否为有效数字格式
    if (!numberRegex.test(strValue)) {
      return {
        valid: false,
        message: formatMessage
      }
    }

    // 转换为数字
    const numValue = parseFloat(strValue)
    if (isNaN(numValue)) {
      return {
        valid: false,
        message: formatMessage
      }
    }

    // 分离整数部分和小数部分
    const parts = strValue.split('.')
    const integerPart = parts[0].replace('-', '') // 移除负号计算长度
    const decimalPart = parts[1] || ''

    // 检查小数位数
    if (decimalPlaces === 0 && decimalPart.length > 0) {
      return {
        valid: false,
        message: '请输入整数'
      }
    }

    if (decimalPart.length > decimalPlaces) {
      return {
        valid: false,
        message: `小数位数不能超过${decimalPlaces}位`
      }
    }
     if (integerPart.length > totalLength - decimalPlaces) {
      return {
        valid: false,
        message: `整数位数不能超过${totalLength - decimalPlaces}位`
      }
    }

    // 检查总长度（整数部分 + 小数部分）
    const totalDigits = integerPart.length + decimalPart.length
    if (totalDigits > totalLength) {
      return {
        valid: false,
        message: `数字总长度不能超过${totalLength}位`
      }
    }

    return {
      valid: true,
      message: '验证通过'
    }
  }
}

// 创建Element UI表单验证规则
export const createNumberValidationRules = (totalLength, decimalPlaces, required = true) => {
  const rules = []

  if (required) {
    rules.push({
      required: true,
      message: '请输入数字',
      trigger: 'blur'
    })
  }

  rules.push({
    validator: (rule, value, callback) => {
      if (!required && (value === '' || value === null || value === undefined)) {
        callback()
        return
      }

      const validator = createNumberValidator(totalLength, decimalPlaces)
      const result = validator(value)

      if (result.valid) {
        callback()
      } else {
        callback(new Error(result.message))
      }
    },
    trigger: 'blur'
  })

  return rules
}

// 使用示例和测试用例（仅用于开发时参考）
/*
// 测试整数验证（decimalPlaces = 0）
const integerValidator = createNumberValidationRules(5, 0, false)
console.log('整数测试:')
console.log('123 ->', integerValidator) // 应该通过
console.log('123.5 ->', integerValidator) // 应该失败，提示"请输入整数"
console.log('123456 ->', integerValidator) // 应该失败，提示"数字总长度不能超过5位"

// 测试小数验证（decimalPlaces = 2）
const decimalValidator = createNumberValidationRules(5, 2, false)
console.log('小数测试:')
console.log('123.45 ->', decimalValidator) // 应该通过
console.log('123.456 ->', decimalValidator) // 应该失败，提示"小数位数不能超过2位"
console.log('123456.78 ->', decimalValidator) // 应该失败，提示"数字总长度不能超过5位"
*/


