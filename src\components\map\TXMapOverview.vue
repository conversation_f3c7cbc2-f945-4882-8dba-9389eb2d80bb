<template>
  <div class="map-overview">
    <div id="allmap" class="map-chart" />
  </div>
</template>

<script>
import { TxMap } from '@/plugins/map/TxMap'
import Convertor from '@/plugins/map/convertor'

export default {
  name: 'TXMapOverview',
  emits: ['click-marker'],
  data() {
    return {
      map: '',
      mapId: 'allmap',
      positionList: [],
      routeType: 'position',
      markerLayer: '',
      polylineLayer: '',
      convertor: ''
    }
  },
  created() {
    this.convertor = new Convertor()
  },
  methods: {
    show(data) {
      // this.mapId = data?.id || 'allmap'
      this.routeType = data?.type || 'position'
      let list = JSON.parse(JSON.stringify(data?.list || []))
      this.positionList = this.convertorData(list)
      TxMap.init().then(TMap => {
        this.initMap()
      })
    },
    convertorData(data) {
      return data.filter(val => {
        let item = this.convertor.WGS2GCJ({ lng: val.longitude, lat: val.latitude })
        if (item?.lat && item?.lng && typeof item.lat === 'number' && typeof item?.lng === 'number') {
          val.latitude = item.lat
          val.longitude = item.lng
          return true
        }
      })
    },
    initMap() {
      //定义地图中心点坐标
      var center
      if (this.positionList?.length) {
        center = new TMap.LatLng(this.positionList[0].latitude, this.positionList[0].longitude)
      } else {
        center = new TMap.LatLng(39.90812, 116.397484)
      }
      //定义map变量，调用 TMap.Map() 构造函数创建地图
      let map = new TMap.Map(document.getElementById(this.mapId || 'allmap'), {
        center: center, //设置地图中心点坐标
        zoom: 17.2 //设置地图缩放级别
        // pitch: 43.5,  //设置俯仰角
        // rotation: 45    //设置地图旋转角度
      })
      this.map = map

      // this.drawMarkers(TMap, map)

      // if (this.positionList?.length) {
      if (this.routeType === 'trajectory') {
        this.drawRoute()
      } else if (this.routeType === 'position') {
        this.drawMarkers()
      }
      // }
    },
    getMapCenter(item, markers) {
      // this.clearPolylines()
      if (item.latitude && item.longitude) {
        let list = JSON.parse(JSON.stringify([item]))
        item = this.convertorData(list)[0]
        this.map.setCenter(new TMap.LatLng(item.latitude, item.longitude))
      }
    },
    resetMarkers(data) {
      this.clearPolylines()
      this.clearMarkers()
      if (!this.markerLayer) {
        let list = JSON.parse(JSON.stringify(data || []))
        this.positionList = this.convertorData(list)
        this.drawMarkers()
      }
    },
    drawMarkers() {
      const _this = this
      //创建并初始化MultiMarker
      let geometries = this.positionList.map((val, index) => {
        let item = {
          id: index, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          styleId: 'myStyle', //指定样式id
          position: new TMap.LatLng(val.latitude, val.longitude) //点标记坐标位置
          // properties: {
          //   //自定义属性
          //   title: 'marker1'
          // }
        }
        return item
      })
      this.markerLayer = new TMap.MultiMarker({
        map: _this.map, //指定地图容器
        //样式定义
        styles: {
          //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
          myStyle: new TMap.MarkerStyle({
            width: 25, // 点标记样式宽度（像素）
            height: 35, // 点标记样式高度（像素）
            // src: require('/src/assets/images/mapScreen/map-position.png'), //图片路径
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            anchor: { x: 16, y: 32 }
          })
        },
        //点标记数据数组
        geometries
        // geometries: [
        //   {
        //     id: '1', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        //     styleId: 'myStyle', //指定样式id
        //     position: new TMap.LatLng(39.954104, 116.357503), //点标记坐标位置
        //     properties: {
        //       //自定义属性
        //       title: 'marker1'
        //     }
        //   },
        //   {
        //     //第二个点标记
        //     id: '2',
        //     styleId: 'marker',
        //     position: new TMap.LatLng(39.994104, 116.287503),
        //     properties: {
        //       title: 'marker2'
        //     }
        //   }
        // ]
      })
      var clickHandler = function (evt) {
        _this.$emit('click-marker', _this.positionList[evt.geometry.id])
      }
      //监听marker点击事件
      this.markerLayer.on('click', clickHandler)
      // markerLayer.add([
      //   {
      //     id: '3', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
      //     styleId: 'myStyle', //指定样式id
      //     position: new TMap.LatLng(40.954104, 116.357503), //点标记坐标位置
      //     properties: {
      //       //自定义属性
      //       title: 'marker3'
      //     }
      //   },
      //   {
      //     //第4个点标记
      //     id: '4',
      //     styleId: 'marker',
      //     position: new TMap.LatLng(40.994104, 116.287503),
      //     properties: {
      //       title: 'marker4'
      //     }
      //   }
      // ])
    },
    clearMarkers() {
      if (this.markerLayer) {
        this.markerLayer.setMap(null)
        // this.markerLayer.setGeometries([])
        this.markerLayer = null
      }
    },
    clearPolylines() {
      if (this.polylineLayer) {
        this.polylineLayer.setMap(null)
        this.polylineLayer = null
      }
      // this.clearMarkers()
    },
    goDrawRoute(data) {
      this.clearMarkers()
      if (data?.length) {
        let list = JSON.parse(JSON.stringify(data))
        this.positionList = this.convertorData(list)
        this.drawRoute()
      }
    },
    drawRoute() {
      const _this = this
      // list = list.reduce((newValue, oldValue)=>{
      //   return new Date(newValue.collectTime.$date) - new Date(oldValue.collectTime.$date)
      // })
      //小车移动路线
      // var path = [
      //   // new TMap.LatLng(39.98481500648338, 116.30571126937866),
      //   // new TMap.LatLng(39.982266575222155, 116.30596876144409),
      //   // new TMap.LatLng(39.982348784165886, 116.3111400604248),
      //   // new TMap.LatLng(39.978813710266024, 116.3111400604248),
      //   // new TMap.LatLng(39.978813710266024, 116.31699800491333)
      // ]
      // this.positionList.forEach(val => {
      //   path.push(new TMap.LatLng(val.latitude, val.longitude))
      // })
      let path = this.positionList.map(val => {
        return new TMap.LatLng(val.latitude, val.longitude)
      })

      this.polylineLayer = new TMap.MultiPolyline({
        map: _this.map, //指定地图容器
        // 折线样式定义
        styles: {
          style_blue: new TMap.PolylineStyle({
            color: '#3777FF', // 线填充色
            width: 4, // 折线宽度
            borderWidth: 2, // 边线宽度
            borderColor: '#FFF', // 边线颜色
            lineCap: 'round', // 线端头方式
            eraseColor: 'rgba(190,188,188,1)'
          })
        },
        geometries: [
          {
            id: 'erasePath',
            styleId: 'style_blue',
            paths: path
          }
        ]
      })
      this.markerLayer = new TMap.MultiMarker({
        map: _this.map, //指定地图容器
        styles: {
          'car-down': new TMap.MarkerStyle({
            width: 40,
            height: 40,
            anchor: {
              x: 20,
              y: 20
            },
            faceTo: 'map',
            rotate: 180,
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png'
          }),
          start: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/start.png'
          }),
          end: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/end.png'
          })
        },
        geometries: [
          {
            id: 'car',
            styleId: 'car-down',
            position: path[0]
          },
          {
            id: 'start',
            styleId: 'start',
            position: path[0]
          },
          {
            id: 'end',
            styleId: 'end',
            position: path[path.length - 1]
          }
        ]
      })
      this.startCar()
      // // 使用marker 移动接口， https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocMarker
      // this.markerLayer.moveAlong(
      //   {
      //     car: {
      //       path,
      //       speed: 250
      //     }
      //   },
      //   {
      //     autoRotation: true
      //   }
      // )
      // this.markerLayer.on('moving', e => {
      //   var passedLatLngs = e.car && e.car.passedLatLngs
      //   if (passedLatLngs) {
      //     // 使用路线擦除接口 eraseTo, https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocVector
      //     polylineLayer.eraseTo('erasePath', passedLatLngs.length - 1, passedLatLngs[passedLatLngs.length - 1])
      //   }
      // })

      // //创建mareker（小车）
      // var marker = new TMap.MultiMarker({
      //   map: this.map, //指定地图容器
      //   styles: {
      //     //样式设置
      //     'car-down': new TMap.MarkerStyle({
      //       width: 40, //小车图片宽度（像素）
      //       height: 40, //高度
      //       anchor: {
      //         //图片中心的像素位置（小车会保持车头朝前，会以中心位置进行转向）
      //         x: 20,
      //         y: 20
      //       },
      //       faceTo: 'map', //取’map’让小车贴于地面，faceTo取值说明请见下文图示
      //       rotate: 180, //初始小车朝向（正北0度，逆时针一周为360度，180为正南）
      //       src: require('/src/assets/images/car.png') //小车图片（图中小车车头向上，即正北0度）
      //     })
      //   },
      //   geometries: [
      //     {
      //       //小车marker的位置信息
      //       id: 'car', //因MultiMarker支持包含多个点标记，因此要给小车一个id
      //       styleId: 'car-down', //绑定样式
      //       position: new TMap.LatLng(39.98481500648338, 116.30571126937866) //初始坐标位置
      //     }
      //   ]
      // })
      //
      // //调用moveAlong，实现小车移动
      // marker.moveAlong(
      //   {
      //     car: {
      //       //设置让"car"沿"path"移动，速度70公里/小时
      //       path,
      //       speed: 70
      //     }
      //   },
      //   {
      //     autoRotation: true //车头始终向前（沿路线自动旋转）
      //   }
      // )
    },
    startCar() {
      const _this = this
      let path = this.positionList.map(val => {
        return new TMap.LatLng(val.latitude, val.longitude)
      })
      // 使用marker 移动接口， https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocMarker
      this.markerLayer.moveAlong(
        {
          car: {
            path,
            speed: 250
          }
        },
        {
          autoRotation: true
        }
      )
      this.markerLayer.on('moving', e => {
        var passedLatLngs = e.car && e.car.passedLatLngs
        if (passedLatLngs) {
          // 使用路线擦除接口 eraseTo, https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocVector
          _this.polylineLayer.eraseTo('erasePath', passedLatLngs.length - 1, passedLatLngs[passedLatLngs.length - 1])
        }
      })
    },
    destroyMap() {
      map.destroy()
    }
  }
}
</script>

<style lang="scss" scoped>
.map-overview,
.map-chart {
  height: 100%;
  width: 100%;
  z-index: 1;

  :deep(.logo-text) {
    display: none !important;
  }

  //:deep(img) {
  //	display: none;
  //}
}
</style>
