<template>
  <el-carousel
    :loop="false"
    :autoplay="false"
    indicator-position="none"
    ref="carouselShow"
    :initial-index="0"
    @mousewheel.stop="rollScroll"
    class="carousel-container"
  >
    <el-carousel-item v-for="(item, index) in dataList" :key="index">
      <slot :data="item"></slot>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import { debounce } from 'lodash'
export default {
  name: 'LtwCarousel',
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    rollScroll(event) {
      event.preventDefault()
      event.stopPropagation()
      const scrollVal = event.wheelDelta || event.detail
      this.debouncedScroll(scrollVal)
    },
    debouncedScroll: debounce(function (scrollVal) {
      if (scrollVal > 0) {
        this.$refs.carouselShow.prev()
      } else {
        this.$refs.carouselShow.next()
      }
    }, 300)
  }
}
</script>

<style lang="scss" scoped>
.el-carousel {
  width: 95%;
  padding: 0;
  :deep(.el-carousel__container) {
    height: 100%;
  }

  :deep(.el-carousel__item) {
    height: 100%;
  }
  :deep(.el-carousel__arrow) {
    background: rgba(0, 0, 0, 0.3);
  }
  :deep(.el-carousel__arrow:hover) {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
