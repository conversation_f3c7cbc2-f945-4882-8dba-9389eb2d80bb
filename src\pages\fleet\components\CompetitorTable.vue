<template>
  <!--<el-form v-if="form.edit" :model="form" :rules="formRules" ref="itemFormRef" label-width="100px">
    <el-row>
      <el-col :span="12">
        <el-form-item :label="$t('有效日期')" prop="activationDate">
          <el-date-picker
            v-model="form.activationDate"
            type="date"
            :placeholder="$t('有效日期')"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('失效日期')" prop="deactivationDate">
          <el-date-picker
            v-model="form.deactivationDate"
            :disabled-date="disabledEndDate"
            type="date"
            :placeholder="$t('失效日期')"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('版本')" prop="version">
          <ltw-input :limitSize="10" v-model="form.version" :disabled="formReadonly" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('是否启用')" id="enable" prop="enable">
          <el-switch
            v-model="form.enable"
            inline-prompt
            :active-text="$t('是')"
            :inactive-text="$t('否')"
            id="enable"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('DBC文件')">
          <el-button circle size="small" @click="chooseDbc()">
            <el-icon>
              <CirclePlus />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item v-if="fileNameList?.length" :label="$t('已选文件')" id="enable">
      <div v-for="file in fileNameList">
        <span>{{ file }}</span> &nbsp;&nbsp;&nbsp;
      </div>
    </el-form-item>
  </el-form>-->
    <!--<el-table :data="item.vehicleDbcVOList"> -->
    <el-table border stripe :data="item.vehicleSpareVOList">
      <el-table-column header-align="center" align="center" prop="activationDate" :label="$t('对手件')">
        <template #default="scope">
          <el-tag ><span>{{scope.row.sparePartName}}</span></el-tag>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="currentVersion" :label="$t('当前版本')">
        <template #default="scope">
          <el-link :underline="false"><span>{{scope.row.version}}</span></el-link>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="deactivationDate" :label="$t('目标版本')">
        <template #default="scope">
          <el-link :underline="false"><span>{{scope.row.targetVersion}}</span></el-link>
        </template>
      </el-table-column>
<!--      <el-table-column header-align="center" align="center" prop="fileName" :label="$t('文件')">-->
<!--        <template #default="scope">-->
<!--          <el-link :underline="false"><span>{{scope.row.fileName}}</span></el-link>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column header-align="center" align="center"   :label="$t('操作')" v-if="form.dialogStatus==='edit'">
        <template #default="scope">
          <div class="footer">
            <el-button type="danger"  @click="singleRemoveForm(scope.row.id)"  icon="el-icon-delete" circle></el-button>
            <el-button type="success" icon="el-icon-check" circle></el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
      <el-descriptions  :column="2"  v-if="item.vehicleSparePartVersionVO.sourceFileName">
        <el-descriptions-item label="日期">{{item.deactivationDate}}</el-descriptions-item>
        <el-descriptions-item label="来源文件" class="custom-descriptions" >
          <el-link  type="primary" @click="downloadFile(item.vehicleSparePartVersionVO.sourceFileId)">{{item.vehicleSparePartVersionVO.sourceFileName}}.{{item.vehicleSparePartVersionVO.sourceFileType}}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="描述">{{item.vehicleSparePartVersionVO.description}}</el-descriptions-item>
      </el-descriptions>
</template>

<script>
import { i18n } from '@/plugins/lang'
import LtwInput from '@/components/base/LtwInput'
import UploadFile from '@/components/system/UploadFile.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { showConfirmToast, showToast } from '@/plugins/util.js'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { saveCompetitor, deleteCompetitor } from '@/apis/fleet/ftm-spare-part'
import {
  ElForm,
  ElFormItem,
  ElButton,
  ElRow,
  ElCol,
  ElInputNumber,
  ElDatePicker,
  ElOption,
  ElSelect

} from 'element-plus'

export default {
  name: 'DbcForm',
  data() {
    return {
      mockList:[{}],
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      selectedDbcList: [],
      fileNameList: [],
      $t: i18n.global.t,
      form: {},
      dialogStatus: '',
      formRules: {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        deactivationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        version: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        enable: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
    }
  },
  emits: ['reload'],
  props: {
    operationType:{
      type:String,
      default:''
    },
    item: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    options: {
      type: Array,
      default: []
    },
    status: {
      type: String,
      default: 'add'
    },
    variant: {
      type: String
    },
    vin: {
      type: String
    },
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    item: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
        this.form.enable = true
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElRow,
    ElCol,
    ElInputNumber,
    ElDatePicker,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    UploadFile,
  },
  methods: {
    downloadFile(id) {
      const url = this.downloadUrl + id + '?token=' + util.getToken()
      window.open(url)
    },
    show(row) {
      this.dialogVisible = true
    },
    submit() {
      if (!this.selectedDbcList?.length) {
        showToast('请先选择dbc文件', 'warning')
        return
      }
      this.$refs.itemFormRef.validate(valid => {
        if (valid) {
          let postData = {
            ...this.form,
            vin: this.vin,
            dbcIdList: this.selectedDbcList.map(val => val.id)
          }
          saveFtmVehicleDbc(postData).then(res => {
            this.saveForm()
          })
        }
      })
    },
    cancelForm() {
      if (this.form.id) {
        this.$emit('reload', {
          index: this.index,
          type: 'cancel'
        })
      } else {
        this.$emit('reload', {
          form: this.form,
          index: this.index,
          type: 'delete'
        })
      }
    },
    saveForm() {
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'add'
      })
    },
    editForm() {
      this.form.edit = true
      this.form.dialogStatus = 'edit'
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'edit'
      })
    },
    getFormDetail(row) {
      this.$refs.FtmDbcDetail.show({ dbcId: row.dbcId, fileName: row.fileName })
    },
    singleRemoveForm(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmVehicleDbc({ id: id }).then(() => {
          this.$emit('reload', {
            type: 'delete'
          })
          showToast('dbc配置已删除')
        })
      })
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return new Date(val) < new Date(this.form.activationDate).getTime() - 1000 * 60 * 60 * 24
      }
    }
  }
}
</script>

<style lang="scss" scoped>
//.custom-descriptions > .el-descriptions__body {
//  margin-right: 20px !important;
//}

:deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
  padding-bottom: 10px;
  margin-right: 10px;
}
</style>
