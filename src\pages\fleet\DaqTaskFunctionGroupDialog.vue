<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened"
             :close-on-click-modal="false">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="150px">
      <el-form-item :label="$t('编码')" prop="code">
        <ltw-input v-model="formData.code" :disabled="formReadonly" id="code"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input
          v-model="formData.name"
          :disabled="formReadonly"
          id="name"
          text-type="name"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('是否可以切换')" prop="switchable" v-if="top">
        <el-switch v-model="formData.switchable" :disabled="formReadonly" id="switchable"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="save" id="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { saveDaqTaskFunctionGroup, updateDaqTaskFunctionGroup, getDaqTaskFunctionGroup } from '@/apis/fleet/daq-task-function-group'
import {showToast} from "@/plugins/util";

const defaultFormData = {
  switchable: false
}
export default {
  name: 'BsTagGroupDialog',
  emits: ['save'],
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  data() {
    return {
      top:false,
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' },
        ],
        code: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' },
        ],
        sortNum: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        switchable: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },
      dialogTitle: '',
      dialogStatus: ''
    }
  },
  methods: {
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.top = false
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveDaqTaskFunctionGroup(this.formData).then(() => {
            this.dialogVisible = false
            this.$emit('save')
          })
        }
        if (this.dialogStatus === 'edit') {
          updateDaqTaskFunctionGroup(this.formData).then(() => {
            this.dialogVisible = false
            this.$emit('save')
          })
        }
      })
    },
    edit(id,top) {
      this.top =top
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      getDaqTaskFunctionGroup(id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(id) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getDaqTaskFunctionGroup(id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    add(defaultData) {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      Object.assign(this.formData, defaultData)
    },
    addSubGroup(defaultData) {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      Object.assign(this.formData, defaultData)
    }
  }
}
</script>

<style lang="sass" scoped></style>
