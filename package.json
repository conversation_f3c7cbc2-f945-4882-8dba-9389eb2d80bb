{"name": "chenghuang-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --host localhost", "build": "vue-cli-service build", "build:bgn-test": "vue-cli-service build --mode bgn-test", "build:bgn-uat": "vue-cli-service build --mode bgn-uat", "build:cloud-dev": "vue-cli-service build --mode cloud-dev", "build:cloud-staging": "vue-cli-service build --mode cloud-staging", "build:cloud-prod": "vue-cli-service build --mode cloud-prod", "lint": "vue-cli-service lint"}, "dependencies": {"@babylonjs/core": "^5.35.1", "@element-plus/icons": "^0.0.11", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/vue3": "^6.1.8", "@jiaminghi/data-view": "^2.10.0", "axios": "^0.24.0", "clipboard": "^2.0.11", "codemirror": "^5.65.12", "codemirror-editor-vue3": "^2.3.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "echarts": "^5.2.2", "echarts-gl": "^2.0.9", "element-plus": "^2.1.4", "element-resize-detector": "^1.2.4", "express": "^5.1.0", "hotkeys-js": "^3.10.0", "http-proxy-middleware": "^3.0.5", "js-md5": "^0.8.3", "json-editor-vue3": "^1.0.6", "mermaid": "^8.13.10", "vue": "^3.0.0", "vue-code-diff": "^1.2.0", "vue-draggable-plus": "^0.5.0", "vue-i18n": "^9.2.0-beta.30", "vue-json-viewer": "^3.0.4", "vue-jsonp": "^2.0.0", "vue-router": "^4.0.12", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@babel/core": "^7.22.11", "@babel/eslint-parser": "^7.22.11", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.0.0", "eslint": "^8.48.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^7.20.0", "less": "^4.1.2", "less-loader": "^10.2.0", "lint-staged": "^14.0.1", "prettier": "^3.0.2", "sass": "^1.43.4", "sass-loader": "^12.3.0", "svg-sprite-loader": "^6.0.11", "vue-cli-plugin-element-plus": "~0.0.13", "vue-eslint-parser": "^9.3.1", "vue-template-compiler": "^2.7.14", "worker-loader": "^3.0.8"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}