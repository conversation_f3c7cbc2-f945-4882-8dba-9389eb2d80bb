import GLB_CONFIG from '@/plugins/glb-constant'
import md5 from 'js-md5'
import { jsonp } from 'vue-jsonp'
import { showToast } from '@/plugins/util'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'

export const TxMap = {
  init: function () {
    // window.TMap = {};
    // // 是否强制切换至2D的LiteRender模式
    // window.TMap.forceUseLiteRender = true;
    // // 是否允许适配无GPU机器，检测后主动切换至LiteRender模式
    // window.TMap.useLiteRenderForSwiftShader = true;
    const TMapURL =
      '//map.qq.com/api/gljs?v=1.exp&key=' +
      GLB_CONFIG.TxMapKey +
      '&callback=onTMapCallback&libraries=tools,geometry,service'
      // '&callback=onTMapCallback&libraries=tools,geometry,service,literender'
    return new Promise((resolve, reject) => {
      // 如果已加载直接返回
      if (typeof window.TMap !== 'undefined') {
        resolve(window.TMap)
        return true
      }
      window.onTMapCallback = function () {
        resolve(window.TMap)
      }
      const scriptNode = document.createElement('script')
      scriptNode.setAttribute('type', 'text/javascript')
      scriptNode.setAttribute('src', TMapURL)
      scriptNode.onerror = function () {
        reject(new Error('Failed to load Tencent Map API'))
      }
      document.body.appendChild(scriptNode)
    })
  }
}

export function initMap(elem, options = {}) {
  const defaultOptions = {
    zoom: 16,
    center: [31.380155339677, 121.27259505835],
    removeScaleControl: false,
    removeZoomControl: true,
    removeRotationControl: true
  }
  return TxMap.init().then(() => {
    options = {
      ...defaultOptions,
      ...options
    }
    options.center = latLngPoint(options.center)
    let map = new TMap.Map(elem, options)
    if (options.removeScaleControl) {
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE)
    }
    if (options.removeZoomControl) {
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM)
    }
    if (options.removeRotationControl) {
      map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION)
    }
    return map
  })
}

export function latLngPoint(point) {
  let lat, lng, height
  if (Array.isArray(point)) {
    lat = point[0]
    lng = point[1]
    height = point[2] || 0
  } else {
    lat = point.lat
    lng = point.lng
    height = point.height || 0
  }
  return new TMap.LatLng(lat, lng, height)
}

//设置mark
export function addMarker(mapInstance, options = {}) {
  let defaultOptions = {
    position: [39.90812, 116.397484]
  }
  const markObj = { ...defaultOptions, ...options }
  const latLng = latLngPoint(markObj.position)
  return new TMap.MultiMarker({
    icon: markObj.icon || '',
    map: mapInstance,
    geometries: [
      // 点标记数据数组
      {
        // 标记位置(纬度，经度，高度)
        position: latLng,
        id: 'marker'
      }
    ]
  })
}

export function isLatitudeLongitude(val) {
  val = val.trim()
  const parts = val.split(',').map(part => part.trim())
  const normalizedVal = parts.join(',')
  const regex = /^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/
  if (regex.test(normalizedVal)) {
    const [latitude, longitude] = parts.map(Number)
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180
  }
  return false
}

export function getGeoCoderByAddress(address) {
  let sig = md5(
      `/ws/geocoder/v1?address=${address}&callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
  )
  let getData = {
    address: address,
    callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
    callbackName: 'jsonpCallback', // 设置callback 参数的值
    key: GLB_CONFIG.TxMapKey,
    output: 'jsonp',
    sig
  }
  return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
}

export function getGeoCoderByLocation(location) {
  let sig = md5(
      `/ws/geocoder/v1?callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&location=${location}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
  )
  let getData = {
    callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
    callbackName: 'jsonpCallback', // 设置callback 参数的值
    location: location,
    key: GLB_CONFIG.TxMapKey,
    output: 'jsonp',
    sig
  }
  return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
}

export function getBorderGeoByCanton(keyword, polygon = 1, maxOffset = 100) {
  let sig, getData

  if (polygon === 1) {
    sig = md5(
        `/ws/district/v1/search?callback=jsonpCallback&get_polygon=1&key=${GLB_CONFIG.TxMapKey}&keyword=${keyword}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
    )
    getData = {
      callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
      callbackName: 'jsonpCallback', // 设置callback 参数的值
      get_polygon: 1,
      key: GLB_CONFIG.TxMapKey,
      keyword: keyword,
      output: 'jsonp',
      sig
    }
  } else {
    sig = md5(
        `/ws/district/v1/search?callback=jsonpCallback&get_polygon=2&key=${GLB_CONFIG.TxMapKey}&keyword=${keyword}&max_offset=${maxOffset}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
    )
    getData = {
      callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
      callbackName: 'jsonpCallback', // 设置callback 参数的值
      get_polygon: 2,
      key: GLB_CONFIG.TxMapKey,
      keyword: keyword,
      max_offset: maxOffset,
      output: 'jsonp',
      sig
    }
  }
  return jsonp('https://apis.map.qq.com/ws/district/v1/search', getData)
}

export function generateRandomColor() {
  let color
  while (true) {
    // 生成随机颜色并通过 padStart 确保其为6位
    color =
        '#' +
        Math.floor(Math.random() * 16777215)
            .toString(16)
            .padStart(6, '0')
    // 提取 RGB 分量
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    // 计算亮度
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b
    // 如果亮度合适，跳出循环
    if (brightness <= 200 && brightness >= 50) {
      break
    }
  }
  return color
}

export function generateMultipleRandomColors(colorNum) {
  const colors = []
  while (colors.length < colorNum) {
    let color =
        '#' +
        Math.floor(Math.random() * 16777215)
            .toString(16)
            .padStart(6, '0')
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)

    const brightness = 0.299 * r + 0.587 * g + 0.114 * b

    if (brightness <= 200 && brightness >= 50) {
      colors.push(color)
    }
  }
  return colors
}

export function generateMultipleRandomColorsWithCompareColor(colorNum, compareColor = '#FFFFFF') {
  const colors = new Set() // 使用 Set 存储，确保不会有重复颜色
  const hexToRgb = hex => {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return { r, g, b }
  }

  const rgbToLuminance = ({ r, g, b }) => {
    const normalize = value => {
      const v = value / 255
      return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4)
    }
    const R = normalize(r)
    const G = normalize(g)
    const B = normalize(b)
    return 0.2126 * R + 0.7152 * G + 0.0722 * B // 相对亮度公式
  }

  const getContrastRatio = (lum1, lum2) => {
    const L1 = Math.max(lum1, lum2) // 较亮的颜色
    const L2 = Math.min(lum1, lum2) // 较暗的颜色
    return (L1 + 0.05) / (L2 + 0.05) // 对比度公式
  }

  // 将对比颜色（默认为白色）转为 RGB 并计算相对亮度
  const compareColorRgb = hexToRgb(compareColor)
  const compareColorLuminance = rgbToLuminance(compareColorRgb)

  while (colors.size < colorNum) {
    // 生成随机颜色
    let color =
        '#' +
        Math.floor(Math.random() * 16777215)
            .toString(16)
            .padStart(6, '0')

    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)

    // 计算亮度并过滤
    const brightness = 0.299 * r + 0.587 * g + 0.114 * b // 标准亮度公式
    let contrast
    if (brightness <= 200 && brightness >= 50) {
      const colorLuminance = rgbToLuminance({ r, g, b }) // 计算随机颜色的相对亮度
      contrast = getContrastRatio(compareColorLuminance, colorLuminance) // 计算与对比颜色之间的对比度
      if (contrast >= 4.5) {
        colors.add(color) // 使用 Set 确保颜色唯一
      }
    }
    if (colors.size < colorNum / 2 && contrast >= 3.5) {
      colors.add(color) // 放宽对比度条件时添加也生效
    }
  }
  return [...colors] // 将 Set 转为数组并返回
}

const colorPalette = generateMultipleRandomColors(10000)

export function createPolylineLayer(mapInstance, routes, options = {}, multiPolylineId, zIndex) {
  const defaultStyle = {
    width: 5,
    lineCap: 'butt',
    showArrow: true,
    arrowOptions: {
      space: 70
    }
  }
  const finalStyle = { ...defaultStyle, ...options.styles }
  const styles = {
    selectedStyle: new TMap.PolylineStyle({
      ...finalStyle,
      color: '#409eff',
      styles: {
        borderWidth:2,
        borderColor: '#a8a8a8',
        showArrow: true,
        arrowOptions: { width: 5, height: 5, space: 50, animSpeed: 50 }
      }
    }),
    unSelectedStyle: new TMap.PolylineStyle({
      ...finalStyle,
      color: 'rgb(159, 206, 255)',
      arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
    })
  }
  const geometries =
      routes.map((route, index) => {
        if (!route.id) {
          console.warn('Route missing ID:', route)
          return null
        }
        const color = route.color || generateRandomColor()
        const styleName = `style_${route.id}`
        styles[styleName] = new TMap.PolylineStyle({ color, ...finalStyle, ...route?.styles })
        return {
          id: route.id,
          styleId: styleName,
          paths: route.paths?.map(coords => latLngPoint(coords)) || [],
          rank: routes?.length - index
        }
      }) || []
  const polylineId = multiPolylineId || 'polyline-layer'
  return new TMap.MultiPolyline({
    id: polylineId,
    map: mapInstance,
    styles: styles,
    geometries: geometries,
    zIndex
  })
}

export function createPolygonList(mapInstance, routes, options = {}, multiPolygonId) {
  const defaultStyle = {
    color: 'rgba(255, 255, 255, 0.1)',
    width: 0.5,
    borderColor: '#409eff', // 边线颜色
    cursor: 'pointer'
  }
  const finalStyle = { ...defaultStyle, ...options.styles }
  const styles = {}
  const geometries = routes.map((route, index) => {
    if (!route.id) {
      console.warn('Route missing ID:', route)
      return null
    }
    const color = route.color || generateRandomColor()
    const styleName = `style_${route.id}`
    styles[styleName] = new TMap.PolygonStyle({ color, ...finalStyle })
    return {
      id: route.id,
      mesh: route.mesh,
      styleId: styleName,
      paths: route.paths?.map(coords => latLngPoint(coords))
    }
  })
  const polygonId = multiPolygonId || 'polygon-list-layer'
  return new TMap.MultiPolygon({
    id: polygonId,
    map: mapInstance,
    styles: styles,
    geometries: geometries
  })
}

export function createMeshLabels(mapInstance, geometries) {
  // 标签样式
  const labelStyle = {
    color: '#3777FF',
    size: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    weight: 'bold',
    alignment: 'center',
    verticalAlignment: 'middle'
  }
  // 创建标签图层
  return new TMap.MultiLabel({
    map: mapInstance,
    styles: {
      label: new TMap.LabelStyle(labelStyle)
    },
    geometries: geometries.map(geo => ({
      id: `mesh_${geo.id}`,
      position: TMap.geometry.computeCentroid(geo.paths),
      content: geo.mesh?.toString() || '',
      styleId: 'label'
    }))
  })
}

//计算圆的坐标点
export function calculateCirclePoint(center, radius, numPoints) {
  const defaultNumPoints = numPoints || 80
  const points = []
  const EARTH_RADIUS = 6371000 // 地球半径 (米)
  for (let i = 0; i < defaultNumPoints; i++) {
    const angle = i * (360 / defaultNumPoints)
    const radian = angle * (Math.PI / 180)
    const lat = center.lat + (radius / EARTH_RADIUS) * (180 / Math.PI) * Math.cos(radian)
    const lng =
        center.lng +
        (radius / EARTH_RADIUS) * (180 / Math.PI) * (Math.sin(radian) / Math.cos((center.lat * Math.PI) / 180))
    points.push(new TMap.LatLng(lat, lng))
  }
  return points
}

//计算矩形坐标点
export function calculateRectanglePoints(center, width, height) {
  // 计算经纬度的度与米的换算关系
  const latToMeter = 111000 // 纬度每度大约为 111000 米
  const lngToMeter = latToMeter * Math.cos((center.lat * Math.PI) / 180) // 经度每度在给定纬度上的米数

  // 计算矩形的四个角点
  const halfWidth = width / 2
  const halfHeight = height / 2

  const deltaLat = halfHeight / latToMeter
  const deltaLng = halfWidth / lngToMeter

  const points = [
    { lat: center.lat + deltaLat, lng: center.lng - deltaLng }, // 北西角
    { lat: center.lat + deltaLat, lng: center.lng + deltaLng }, // 北东角
    { lat: center.lat - deltaLat, lng: center.lng + deltaLng }, // 南东角
    { lat: center.lat - deltaLat, lng: center.lng - deltaLng } // 南西角
  ]
  const pointsLatLng = points.map(item => {
    return new TMap.LatLng(item.lat, item.lng)
  })

  return pointsLatLng
}

//todo 与上面的createPolylineLayer合并type = 'polyline'|| 'polygon'
export function createPolygonLayer(mapInstance, polygons, options = {}, multiPolygonId) {
  const defaultStyle = {
    color: 'rgba(236,99,10,0.62)', // 默认填充颜色
    showBorder: false,
    borderWidth: 10,
    borderColor: '#11ff00'
  }
  const finalStyle = { ...defaultStyle, ...options.styles }
  const styles = {
    collectedStyle: new TMap.PolygonStyle({
      color: '#f0f9eb', // 面填充色
      showBorder: true, // 是否显示拔起面的边线
      borderColor: '#c2e7b0' // 边线颜色
      // color: '#f56c6c', // 面填充色
    }),
    selectedStyle: new TMap.PolygonStyle({
      // color: '#f56c6c', // 面填充色
      color: 'rgba(100, 149, 237, 0.7)', // 面填充色
      showBorder: true, // 是否显示拔起面的边线
      borderColor: '#409eff' // 边线颜色
    })
  }
  const geometries = polygons.map((polygon, index) => {
    const color = polygon.color || finalStyle.color
    const styleName = `style_${polygon.id}`
    styles[styleName] = new TMap.PolygonStyle({
      color,
      showBorder: finalStyle.showBorder,
      borderColor: finalStyle.borderColor
    })
    return {
      id: polygon.id || polygon.code,
      styleId: polygon.styleId || styleName,
      paths: polygon.paths,
      properties: {
        title: polygon.title || `Polygon ${polygon.id}`,
        count: polygon.count || 0,
        position: polygon.position || null
      }
    }
  })
  const polygonLayerId = multiPolygonId || 'polygon-layer'
  return new TMap.MultiPolygon({
    id: polygonLayerId,
    map: mapInstance,
    styles: styles,
    geometries: geometries
  })
}

export function updatePolygonBorder(layer, targetId) {
  const defaultStyle = {
    color: 'rgba(236,99,10,0.62)', // 默认填充颜色
    showBorder: false,
    borderWidth: 4,
    borderColor: '#11ff00'
  }
  const updatedStyles = {}
  layer.getGeometries().forEach((geometry, index) => {
    const showBorder = geometry.id === targetId
    const styleName = `style_${index}`
    updatedStyles[styleName] = new TMap.PolygonStyle({
      color: 'rgba(236,99,10,0.62)', // 默认填充颜色
      showBorder: showBorder,
      borderWidth: 4,
      borderColor: '#11ff00'
    })
  })
  layer.setStyles(updatedStyles)
}

export function addPolygonLabel(mapInstance, position, title) {
  // 创建一个文本标记
  const label = new TMap.Text({
    map: mapInstance,
    position: position,
    text: title,
    offset: { x: 0, y: -20 }, // 根据需要设置偏移
    // 这里假设地图库允许设置文本样式
    zIndex: 2,
    style: {
      color: '#333', // 字体颜色
      size: 14, // 字体大小
      weight: 'bold' // 字体粗细
    }
  })
  return label
}

export function toggleBorderStyle(mapInstance, routesLayer, routeId, addBorder = true) {
  const geometry = routesLayer.geometries.find(geo => geo.id === routeId)
  if (!geometry) {
    console.warn(`Route with ID ${routeId} not found.`)
    return
  }

  // 使用与创建时相同的样式ID格式
  const styleId = `style_${routeId}`
  const currentStyles = { ...routesLayer.styles }
  const currentStyle = currentStyles[styleId]
  if (!currentStyle) {
    console.warn(`Style with ID ${styleId} not found.`)
    return
  }
  const borderStyle = {
    borderWidth: addBorder ? 1 : 0,
    borderColor: 'rgba(0,125,255,0.7)',
    width: addBorder ? 6 : 5,
    lineCap: 'round',
    showArrow: true,
    arrowOptions: {
      width: 6,
      height: 5,
      space: 50,
      animSpeed: addBorder ? 50 : 0
    }
  }
  // 保持原有颜色
  const originalColor = currentStyle.color
  // 创建新的样式对象
  currentStyles[styleId] = new TMap.PolylineStyle({
    ...currentStyle,
    ...borderStyle,
    color: originalColor // 确保保持原有颜色
  })
  // 更新样式和几何体
  routesLayer.setStyles(currentStyles)
  routesLayer.updateGeometries([geometry])
}

export function initMapEditor(mapInstance, shapeOptions = [], editorOptions = {}) {
  let activeOverlayId = ''
  const overlayList = shapeOptions.map(shapeConfig => {
    const { type, id, style = {}, options = {} } = shapeConfig
    let overlay
    switch (type) {
      case 'polygon':
        overlay = new TMap.MultiPolygon({
          map: mapInstance,
          styles: {
            default: new TMap.PolygonStyle({
              borderWidth: 2,
              borderColor: '#a8a8a8',
              ...style
            }),
            highlight: new TMap.PolygonStyle({
              color: 'rgba(255, 255, 0, 0.6)'
            })
          }
        })
        if (activeOverlayId === '') {
          activeOverlayId = id
        }
        break
      case 'circle':
        overlay = new TMap.MultiCircle({
          map: mapInstance,
          styles: {
            default: new TMap.CircleStyle({
              borderWidth: 2,
              borderColor: '#a8a8a8',
              ...style
            }),
            highlight: new TMap.CircleStyle({
              color: 'rgba(255, 255, 0, 0.6)'
            })
          }
        })
        break
      case 'rectangle':
        overlay = new TMap.MultiRectangle({
          map: mapInstance,
          styles: {
            default: new TMap.RectangleStyle({
              borderWidth: 2,
              borderColor: '#a8a8a8',
              ...style
            }),
            highlight: new TMap.RectangleStyle({
              color: 'rgba(255, 255, 0, 0.6)'
            })
          }
        })
        break
      case 'ellipse':
        overlay = new TMap.MultiEllipse({
          map: mapInstance,
          styles: {
            default: new TMap.EllipseStyle({
              borderWidth: 2,
              borderColor: '#a8a8a8',
              ...style
            }),
            highlight: new TMap.EllipseStyle({
              color: 'rgba(255, 255, 0, 0.6)'
            })
          }
        })
        break
      case OVER_LAY_TYPE.DRAGMARKER:
        overlay = initMarkers(mapInstance)
        break
      default:
        throw new Error(`不支持的形状类型：${type}`)
    }
    return {
      overlay,
      id,
      drawingStyleId: 'default',
      selectedStyleId: 'highlight',
      ...options
    }
  })

  const mapEditor = new TMap.tools.GeometryEditor({
    map: mapInstance,
    overlayList,
    // actionMode: TMap.tools.constants.EDITOR_ACTION.DRAW,
    // activeOverlayId, // Set active overlay ID for a polygon
    // snappable: false,
    ...editorOptions
  })

  return { mapEditor, overlayList }
}

export function initMarkers(mapInstance, markerList = []) {
  // 为每个标记创建样式对象
  const styles = {
    selectedStyle: new TMap.MarkerStyle({
      width: 33, // 点标记样式宽度（像素）
      height: 48, // 点标记样式高度（像素）
      src: require('/src/assets/images/mapScreen/map-position.png'), //图片路径
      // src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png',
      //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
      anchor: { x: 17, y: 50 },
      enableRelativeScale: true,
      relativeScaleOptions: {
        scaleZoom: 16, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
        minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
        maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
      }
    })
  }
  const geometries = markerList.map((marker, index) => {
    const { id, style, position, styleId } = marker
    styles[id] = new TMap.MarkerStyle({
      // 为每个标记创建一个独特的样式
      width: style?.width || 33, // 默认宽度
      height: style?.height || 48, // 默认高度
      anchor: style?.anchor || { x: 17, y: 50 }, // 默认锚点
      rotate: style?.rotate || undefined, // 默认旋转角度
      src: style?.src || 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png', // 默认图片地址
      enableRelativeScale: style?.enableRelativeScale !== false,
      relativeScaleOptions: style?.relativeScaleOptions || {
        scaleZoom: 16, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
        minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
        maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
      }
    })
    return {
      id: id || `marker_${index}`,
      styleId: styleId || id,
      position: position || latLngPoint([31.380155339677, 121.27259505835]),
      properties: marker.properties || {}
    }
  })
  return new TMap.MultiMarker({
    map: mapInstance,
    styles: styles,
    geometries: geometries
  })
}

export function initClusterMarkers(mapInstance, markerList = []) {
  // 为每个标记创建样式对象
  const styles = {
    selectedStyle: new TMap.MarkerStyle({
      width: 33, // 点标记样式宽度（像素）
      height: 48, // 点标记样式高度（像素）
      src: require('/src/assets/images/mapScreen/map-position.png'), //图片路径
      // src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png',
      //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
      anchor: { x: 17, y: 50 },
      enableRelativeScale: true,
      relativeScaleOptions: {
        scaleZoom: 16, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
        minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
        maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
      }
    }),
    poiSelectedStyle: new TMap.MarkerStyle({
      width: 33, // 点标记样式宽度（像素）
      height: 48, // 点标记样式高度（像素）
      src: require('/src/assets/images/mapScreen/map-position.png'), //图片路径
      // src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png',
      //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
      anchor: { x: 17, y: 50 },
      enableRelativeScale: true,
      relativeScaleOptions: {
        scaleZoom: 16, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
        minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
        maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
      }
    })
  }
  const geometries = markerList.map((marker, index) => {
    const { id, style, position, styleId } = marker
    styles[id] = new TMap.MarkerStyle({
      // 为每个标记创建一个独特的样式
      width: style?.width || 33, // 默认宽度
      height: style?.height || 48, // 默认高度
      anchor: style?.anchor || { x: 17, y: 50 }, // 默认锚点
      rotate: style?.rotate || undefined, // 默认旋转角度
      src: style?.src || 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png', // 默认图片地址
      enableRelativeScale: style?.enableRelativeScale !== false,
      relativeScaleOptions: style?.relativeScaleOptions || {
        scaleZoom: 16, // 设置marker图片宽高像素单位与zoom级别的瓦片像素相同的层级 如当设置为18时，zoom小于18marker会被缩小直至达到minScale设置的最小缩放倍数，大于时marker直至达到maxScale设置的最大缩放倍数；enableRelativeScale为true时生效，默认18
        minScale: 0.5, // 设置marker最小缩放倍数，enableRelativeScale为true时生效，默认0.5
        maxScale: 1 // 设置marker最大缩放倍数，enableRelativeScale为true时生效，默认1
      }
    })
    return {
      id: id || `marker_${index}`,
      styleId: styleId || id,
      position: position || latLngPoint([31.380155339677, 121.27259505835])
    }
  })
  return new TMap.MarkerCluster({
    map: mapInstance,
    enableDefaultStyle: false,
    styles: styles,
    geometries: geometries,
    maxZoom: 16
  })
}

export function initClusterBubble(map) {
  // 以下代码为基于DOMOverlay实现聚合点气泡
  function ClusterBubble(options) {
    TMap.DOMOverlay.call(this, options)
  }

  ClusterBubble.prototype = new TMap.DOMOverlay()

  ClusterBubble.prototype.onInit = function (options) {
    this.content = options.content
    this.position = options.position
  }

  // 销毁时需要删除监听器
  ClusterBubble.prototype.onDestroy = function () {
    this.dom.removeEventListener('click', this.onClick)
    this.removeAllListeners()
  }

  ClusterBubble.prototype.onClick = function () {
    this.emit('click')
  }

  // 创建气泡DOM元素
  ClusterBubble.prototype.createDOM = function () {
    var dom = document.createElement('div')
    dom.classList.add('clusterBubble')
    dom.innerText = this.content
    dom.style.cssText = [
      'width: ' + (40 + parseInt(this.content > 50 ? 50 : this.content) * 2) + 'px;',
      'height: ' + (40 + parseInt(this.content > 50 ? 50 : this.content) * 2) + 'px;',
      'line-height: ' + (40 + parseInt(this.content > 50 ? 50 : this.content) * 2) + 'px;'
    ].join(' ')

    // 监听点击事件，实现zoomOnClick
    this.onClick = this.onClick.bind(this)
    // pc端注册click事件，移动端注册touchend事件
    dom.addEventListener('click', this.onClick)
    return dom
  }

  ClusterBubble.prototype.updateDOM = function () {
    if (!map) {
      return
    }
    // 经纬度坐标转容器像素坐标
    let pixel = map.projectToContainer(this.position)

    // 使文本框中心点对齐经纬度坐标点
    let left = pixel.getX() - this.dom.clientWidth / 2 + 'px'
    let top = pixel.getY() - this.dom.clientHeight / 2 + 'px'
    this.dom.style.transform = `translate(${left}, ${top})`

    this.emit('dom_updated')
  }

  return ClusterBubble
}

export function getCirclePathsPoint(center, radius, numPoints) {
  const defaultNumPoints = numPoints || 80
  const points = []
  const EARTH_RADIUS = 6371000 // 地球半径 (米)
  for (let i = 0; i < defaultNumPoints; i++) {
    const angle = i * (360 / defaultNumPoints)
    const radian = angle * (Math.PI / 180)
    const lat = center.lat + (radius / EARTH_RADIUS) * (180 / Math.PI) * Math.cos(radian)
    const lng =
        center.lng +
        (radius / EARTH_RADIUS) * (180 / Math.PI) * (Math.sin(radian) / Math.cos((center.lat * Math.PI) / 180))
    points.push(latLngPoint([lat, lng]))
  }
  return points
}

//计算两点方向
export function clockwiseToCounterclockwise(pointA, pointB) {
  let angle = TMap.geometry.computeHeading(pointA, pointB, false)
  // 将负角度转换为正角度
  angle = angle % 360
  if (angle < 0) {
    angle += 360 // 将负角度转换为正角度
  }

  // 计算逆时针角度
  let counterclockwiseAngle = (360 - angle) % 360
  return counterclockwiseAngle
}

export function createMarkerCluster(mapInstance, positions, options = {}, clusterId) {
  const defaultOptions = {
    map: mapInstance,
    enableDefaultStyle: true,
    minimumClusterSize: 2,
    zoomOnClick: false,
    gridSize: 60,
    averageCenter: false,
    maxZoom: 10
  }
  const config = { ...defaultOptions, ...options }
  config.id = clusterId
  config.geometries = positions
  return new TMap.MarkerCluster(config)
}

export function createInfoWindow(mapInstance, options = {}) {
  const defaultOptions = {
    map: mapInstance,
    position: new TMap.LatLng(39.984104, 116.307503),
    offset: { x: 0, y: -32 }
  }
  const config = { ...defaultOptions, ...options }
  return new TMap.InfoWindow(config)
}

/**
 * 搜索
 * **/
export function searchSuggestionAPI(key) {
  return new Promise((resolve, reject) => {
    // 新建一个关键字输入提示类
    const suggest = new TMap.service.Suggestion({
      pageSize: 10 // 返回结果每页条目数
    })
    const postData = {
      keyword: key,
      servicesk: GLB_CONFIG.TxMapSecretKey
    }
    suggest
        .getSuggestions(postData)
        .then(result => {
          resolve(result.data)
        })
        .catch(error => {
          showToast(error.message, 'warning')
          reject(result.data)
        })
  })
}

//设置自适应显示marker
export function showFitView(list, mapInstance) {
  if (list?.length) {
    //初始化
    let bounds = new TMap.LatLngBounds()
    //判断标注点是否在范围内
    list?.forEach(item => {
      //若坐标点不在范围内，扩大bounds范围
      bounds.extend(item)
    })
    //设置地图可视范围
    mapInstance.fitBounds(bounds, {
      padding: 100 // 自适应边距
    })
  }
}

export function goCenter(item, mapInstance) {
  mapInstance.panTo(new TMap.LatLng(item[1], item[0]), { duration: 1000 })
}

export function initDriving(options) {
  return new TMap.service.Driving({
    // 新建一个驾车路线规划类
    mp: false, // 是否返回多方案
    policy: 'LEAST_TIME,REAL_TRAFFIC', // 规划策略
    ...options
  })
}

export function drivingSearch(globalDriving, options) {
  return new Promise((resolve, reject) => {
    globalDriving
        .search({
          ...options
        })
        .then(res => {
          resolve(res)
        })
        .catch(err => {
          showToast(err.message, 'warning')
          reject(err)
        })
  })
}

export function addGeo(layer, options) {
  layer?.add(options)
}

export function removeGeo(layer, options) {
  layer?.remove(options)
}

//新增自定义label
export function initMultiLabel(mapInstance, labelList = []) {
  // 为每个标记创建样式对象
  const styles = {
    selectedStyle: new TMap.LabelStyle({
      // 为每个标记创建一个独特的样式
      size: 16, //文字大小属性
      width: 20, // 默认宽度
      height: 20, // 默认高度
      // offset: style?.offset || { x: -4, y: 10 }, //文字偏移属性单位为像素
      anchor: { x: 10, y: 30 }, // 默认锚点
      borderColor: '#fff',
      borderRadius: 100,
      borderWidth: 2,
      backgroundColor: '#409eff',
      color: '#fff'
    })
  }
  const geometries = labelList.map((label, index) => {
    const { id, style, position, content } = label
    styles[id] = new TMap.LabelStyle({
      // 为每个标记创建一个独特的样式
      size: style?.size || 16, //文字大小属性
      width: style?.width || 20, // 默认宽度
      height: style?.height || 20, // 默认高度
      // offset: style?.offset || { x: -4, y: 10 }, //文字偏移属性单位为像素
      anchor: style?.anchor || { x: 10, y: 30 }, // 默认锚点
      borderColor: style?.borderColor || '#fff',
      borderRadius: style?.borderRadius || 100,
      borderWidth: style?.borderWidth || 2,
      backgroundColor: style?.backgroundColor || '#409eff',
      color: style?.color || '#fff'
    })
    return {
      id: id || `label_${index}`,
      styleId: id,
      position: position || latLngPoint([31.380155339677, 121.27259505835]),
      content: content?.toString()
    }
  })
  return new TMap.MultiLabel({
    map: mapInstance,
    styles: styles,
    geometries: geometries
  })
}

export function calculateBounds(bounds) {
  const { _ne: ne, _sw: sw } = bounds
  const nw = { lat: ne.lat, lng: sw.lng }
  const se = { lat: sw.lat, lng: ne.lng }
  return [
    [nw.lng, nw.lat],
    [ne.lng, ne.lat],
    [se.lng, se.lat],
    [sw.lng, sw.lat],
    [nw.lng, nw.lat]
  ]
}

export function calculateHeatColor(count, countMin, countMax, startColor, endColor) {
  if (countMin === countMax) {
    const r = Math.round((startColor[0] + endColor[0]) / 2)
    const g = Math.round((startColor[1] + endColor[1]) / 2)
    const b = Math.round((startColor[2] + endColor[2]) / 2)
    return `rgba(${r},${g},${b},0.6)`
  }
  const ratio = Math.max(0, Math.min(1, (count - countMin) / (countMax - countMin)))
  const r = Math.round(startColor[0] + (endColor[0] - startColor[0]) * ratio)
  const g = Math.round(startColor[1] + (endColor[1] - startColor[1]) * ratio)
  const b = Math.round(startColor[2] + (endColor[2] - startColor[2]) * ratio)
  return `rgba(${r},${g},${b},0.6)` // 将透明度写入 rgba，确保效果满足需要
}

export function initInfoWindows(mapInstance) {
  return new TMap.InfoWindow({
    map: mapInstance,
    position: new TMap.LatLng(39.984104, 116.307503),
    offset: { x: 0, y: -32 }, //设置信息窗相对position偏移像素,
    zIndex: 1000001
  })
}

//判断点位是否在多边形内
export function isPointInPolygon(point, polygon, isPolygonWithHoles) {
  let isIn = false
  if (polygon.radius) {
    // 圆形
    const radius = TMap.geometry.computeDistance([point, polygon.center])
    isIn = radius < polygon.radius
  } else if (polygon.majorRadius && polygon.minorRadius) {
    // 椭圆
  } else if (polygon.width && polygon.height) {
    // 矩形
    // const bounds = TMap.LatLngBounds(sw:LatLng, ne:LatLng)
    // isIn = bounds.contains(point)
    // 多边形
    isIn = TMap.geometry.isPointInPolygon(point, polygon.paths, isPolygonWithHoles)
  } else if (polygon.paths?.length) {
    //多边形
    isIn = TMap.geometry.isPointInPolygon(point, polygon.paths, isPolygonWithHoles)
  }
  return isIn
}

//计算多边形面积（只支持简单多边形，即一维数组，单位：平方米）
export function getAreaByPolygon(polygon) {
  return TMap.geometry.computeArea(polygon)
}
