<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="expand-all-container">
        <div class="ltw-search-container ltw-tool-container">
          <el-select
            v-model="typeQueryParam.signalTypeList"
            :disabled="formReadonly"
            filterable
            :placeholder="$t('请选择') + $t('信号类型')"
            clearable
            multiple
            style="width: 350px"
            @change="initData"
          >
            <el-option
              v-for="item in dbcTypeList"
              :key="item.id"
              :label="item.name + '（' + item.code + '）'"
              :value="item.code"
            />
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="toggleExpandAll">
            <el-icon style="margin-right: 4px">
              <ArrowDown v-if="!allExpanded" />
              <ArrowUp v-else />
            </el-icon>
            {{ allExpanded ? '收起全部' : '展开全部' }}
          </el-button>
        </div>
      </div>
      <template v-if="groupedData && groupedData.length > 0">
        <div v-for="(item, index) in groupedData" :key="item.type" class="data-card">
          <div class="card-header">
            <div class="card-left">
              <div class="expand-button" @click="toggleExpand(index)">
                <el-icon><ArrowDown v-if="!item.isExpanded" /><ArrowUp v-else /></el-icon>
              </div>
              <div class="date-title">{{ item.typeName }}（{{ item.type }}）</div>
            </div>
          </div>
          <div v-if="item.isExpanded" class="card-details">
            <el-table
              :data="item.tableData"
              :row-key="getRowKeys"
              ref="tableRef"
              @selection-change="val => handleSelectionChange(val, index)"
              highlight-current-row
            >
              <el-table-column type="selection" header-align="left" align="left" :reserve-selection="true" width="55" />
              <el-table-column prop="typeName" header-align="left" align="left" :label="$t('信号类型')" width="150">
                <template #default="scope">
                  <template v-if="scope.row.typeName?.length > 40">
                    <el-popover placement="top" trigger="hover" :content="scope.row.typeName">
                      <template #reference>
                        <el-tag type="primary">
                          {{ scope.row.typeName.slice(0, 40) + '...' }}（{{ scope.row.type }}）
                        </el-tag>
                      </template>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tag type="primary"> {{ scope.row.typeName }}（{{ scope.row.type }}） </el-tag>
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="variantName" header-align="left" align="left" :label="$t('车型')">
                <template #default="scope">
                  <template v-if="scope.row.variantName?.length > 40">
                    <el-popover placement="top" trigger="hover" :content="scope.row.variantName">
                      <template #reference>
                        <el-tag type="success">
                          {{ scope.row.variantName.slice(0, 40) + '...' }}
                        </el-tag>
                      </template>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tag type="success">
                      {{ scope.row.variantName }}
                    </el-tag>
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="messageId" header-align="left" align="left" :label="$t('message')">
                <template #default="scope">
                  <template v-if="scope.row.messageId?.length > 40">
                    <el-popover placement="top" trigger="hover" :content="scope.row.messageId">
                      <template #reference>
                        <el-tag type="warning">
                          {{ scope.row.messageId.slice(0, 40) + '...' }}
                        </el-tag>
                      </template>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tag type="warning">
                      {{ scope.row.messageId }}
                    </el-tag>
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="signalName" header-align="left" align="left" :label="$t('signal')" width="230">
                <template #default="scope">
                  <template v-if="scope.row.signalName?.length > 40">
                    <el-popover placement="top" trigger="hover" :content="scope.row.signalName">
                      <template #reference>
                        <el-tag type="danger">
                          {{ scope.row.signalName.slice(0, 40) + '...' }}
                        </el-tag>
                      </template>
                    </el-popover>
                  </template>
                  <template v-else>
                    <el-tag type="danger">
                      {{ scope.row.signalName }}
                    </el-tag>
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="modality" header-align="left" align="left" :label="$t('传感器')" width="100">
                <template #default="scope">
                  <el-tag type="info"> {{ scope.row.modality }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="$t('dbc文件')" header-align="left" align="left">
                <template #default="scope">
                  <el-link
                    type="primary"
                    icon="el-icon-download"
                    :underline="false"
                    @click="handleDownload(scope.row.dbcFileSourceId)"
                  >
                    {{ scope.row.dbcFileName }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column :label="$t('操作')" header-align="left" align="left" width="180" fixed="right">
                <template #default="scope">
                  <el-button-group>
                    <el-tooltip
                      v-for="item in inlineFunctionList"
                      :key="item.id"
                      effect="dark"
                      :content="$t(item.name)"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button :type="item.buttonStyleType" size="mini" @click="executeButtonMethod(item, scope.row)">
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                      </el-button>
                    </el-tooltip>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              @size-change="size => handleInnerSizeChange(size, index)"
              @current-change="page => handleInnerPageChange(page, index)"
              :current-page="item.currentPage"
              :page-sizes="[5, 10, 20, 30]"
              :page-size="item.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="item.total"
            />
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty :description="$t('暂无数据')" />
      </template>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="typeQueryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="typeQueryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="typePageData.total"
      >
      </el-pagination>
    </el-card>
    <add-ftm-dbc-signal-library ref="addFtmDbcSignalLibraryRef" @reload="initData" />
  </div>
</template>

<script>
import {
  saveFtmDbcSignalLibrary,
  updateFtmDbcSignalLibrary,
  deleteFtmDbcSignalLibrary,
  pageFtmDbcSignalLibrary,
  pageFtmDbcSignalLibraryType,
  getFtmDbcSignalLibrary
} from '@/apis/fleet/ftm-dbc-signal-library'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import AddFtmDbcSignalLibrary from './dialog/AddFtmDbcSignalLibrary.vue'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
export default {
  name: 'FtmDbcSignalLibrary',
  components: {
    AddFtmDbcSignalLibrary
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        signalTypeList: 'speed'
      },
      typePageData: {
        total: 0
      },
      typeQueryParam: {
        current: 1,
        size: 10,
        signalTypeList: ''
      },
      groupedData: [],
      allExpanded: false,
      selectedData: [],
      currentButton: {},
      dbcTypeList: []
    }
  },
  async created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    await this.initData()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(button, row) {
      if (button.name === '新增') {
        this.$refs.addFtmDbcSignalLibraryRef.show({
          type: 'add',
          data: {}
        })
      } else if (button.name === '查看详情') {
        this.$refs.addFtmDbcSignalLibraryRef.show({
          type: 'view',
          data: row
        })
      } else if (button.name === '编辑') {
        this.$refs.addFtmDbcSignalLibraryRef.show({
          type: 'edit',
          data: row
        })
      } else {
        this.currentButton = {}
        this[button.buttonCode](row, button)
      }
    },
    listdbcType() {
      listSysDictionary({ typeCode: 'dbc_signal_library_type' }).then(res => {
        this.dbcTypeList = res.data
      })
    },
    async initData() {
      const requestParams = {
        ...this.typeQueryParam,
        signalTypeList: (this.typeQueryParam.signalTypeList || []).join(',')
      }
      const typeRes = await pageFtmDbcSignalLibraryType(requestParams)
      this.typePageData = typeRes.data
      this.groupByType()
      this.listdbcType()
    },
    groupByType() {
      const orderedTypes = ['speed']
      const sortedTypes = [
        ...this.typePageData.records.filter(item => orderedTypes.includes(item.type)),
        ...this.typePageData.records.filter(item => !orderedTypes.includes(item.type))
      ]
      this.groupedData = sortedTypes.map(typeItem => {
        return {
          type: typeItem.type,
          typeName: typeItem.typeName,
          tableData: [],
          isExpanded: false,
          hasLoaded: false,
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      })

      if (this.groupedData.length > 0) {
        this.toggleExpand(0)
      }
    },
    getRowKeys(row) {
      return row.id
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getFtmDbcSignalLibrary(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getFtmDbcSignalLibrary(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      const allSelected = this.selectedData.flat().filter(Boolean)
      if (!Array.isArray(allSelected) || allSelected.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      const ids = allSelected.map(item => item.id).join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmDbcSignalLibrary(param).then(() => {
          this.initData()
        })
      })
    },
    handleSizeChange(value) {
      this.typeQueryParam.size = value
      this.initData()
    },
    handleCurrentChange(value) {
      this.typeQueryParam.current = value
      this.initData()
    },
    handleSelectionChange(value, index) {
      this.selectedData[index] = value
    },
    async handleInnerPageChange(page, index) {
      const item = this.groupedData[index]
      item.currentPage = page
      await this.loadTableDataByType(index)
    },
    async handleInnerSizeChange(size, index) {
      const item = this.groupedData[index]
      item.pageSize = size
      item.currentPage = 1
      await this.loadTableDataByType(index)
    },
    updateAllExpandedStatus() {
      this.allExpanded = this.groupedData.every(item => item.isExpanded)
    },
    async loadTableDataByType(index) {
      const item = this.groupedData[index]
      const params = {
        ...this.queryParam,
        signalTypeList: item.type,
        current: item.currentPage,
        size: item.pageSize
      }
      const res = await pageFtmDbcSignalLibrary(params)
      item.tableData = res.data.records
      item.total = res.data.total
      item.hasLoaded = true
    },
    async toggleExpand(index) {
      const item = this.groupedData[index]
      if (!item.isExpanded && !item.hasLoaded) {
        await this.loadTableDataByType(index)
      }
      this.groupedData[index].isExpanded = !item.isExpanded
      this.updateAllExpandedStatus()
    },
    async toggleExpandAll() {
      this.allExpanded = !this.allExpanded
      for (let i = 0; i < this.groupedData.length; i++) {
        const item = this.groupedData[i]
        item.isExpanded = this.allExpanded

        if (this.allExpanded && !item.hasLoaded) {
          await this.loadTableDataByType(i)
        }
      }
    },
    handleDownload(sourceId) {
      window.open(this.downloadUrl + sourceId + '?token=' + util.getToken())
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
/* 空状态样式 */
.el-empty {
  padding: 40px 0;

  :deep(.el-empty__image) {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  :deep(.el-empty__description) {
    margin-top: 20px;

    p {
      margin: 0;
      line-height: 1.6;
      color: #606266;
      font-size: 14px;

      &.empty-subtitle {
        font-size: 13px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }
}

.el-table {
  :deep(.el-empty) {
    padding: 32px 0;
  }
}

.data-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafc;
  border: 1px solid #d0d4d8;
}

.card-left {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
}

.date-title {
  font-size: 15px;
  font-weight: bold;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.blue-text {
  color: blue;
}

.expand-button {
  cursor: pointer;
  font-size: 14px;
}

.card-details {
  padding: 5px 16px 16px 16px;
  background-color: #fff;
}

.expand-all-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
</style>
