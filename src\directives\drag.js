/**
 * v-drag 表格自定义指令加载
 */

export default {
  mounted: function (el) {
    setTimeout(() => {
      let oDiv = el // 当前元素
      oDiv.style.position = 'fixed'
      // let self = this // 上下文
      // 禁止选择网页上的文字
      oDiv.onselectstart = function () {
        return false
      }
      oDiv.onmousedown = e => {
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft
        let disY = e.clientY - oDiv.offsetTop
        if (
          e.target.id === 'tagResize' ||
          e.target.nodeName === 'INPUT' ||
          e.target.nodeName === 'TEXTAREA'
        ) {
          return
        }
        // document.onmousemove = throttle(function (e) {
        document.onmousemove = e => {
          // 通过事件委托，计算移动的距离
          let l = e.clientX - disX
          let t = e.clientY - disY
          // 移动当前元素
          oDiv.style.left = l + 'px'
          oDiv.style.top = t + 'px'
        }
        document.onmouseup = () => {
          document.onmousemove = null
          document.onmouseup = null
        }
        // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
        return false
      }
    })
  }
}
