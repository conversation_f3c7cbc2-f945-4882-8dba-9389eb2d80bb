<template>
  <el-select
    v-model="selectValue"
    :placeholder="$t('请选择车辆')"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    popper-class="vehicle-select"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.vin"
      :value="item[modelCode]"
      :disabled="checkDisabled(item)"
      :id="item.vin"
    >
      <div class="select-option">
        <div>{{ item.variant }}-{{ item.vin }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import { i18n } from '@/plugins/lang'
import { ElSelect, ElOption } from 'element-plus'

export default {
  name: 'BsVehicleSelection',
  props: {
    status: String,
    modelValue: [String, Number, Array],
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: false
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 默认的返回值id/vin
    modelCode: {
      type: String,
      default: 'id'
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    detailList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      map: {},
      list: [],
      queryParam: {},
      $t: i18n.global.t
    }
  },
  watch: {
    data(val) {
      this.list = val
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item[this.modelCode]] = item
        })
      }
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  components: { ElSelect, ElOption },
  mounted() {
    this.queryParam.status = this.status
    if (this.autoLoad) {
      this.query()
    } else {
      this.list = this.data
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item[this.modelCode]] = item
        })
      }
    }
  },
  methods: {
    query() {
      listBsVehicle(this.queryParam).then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          this.map = {}
          this.list.forEach(item => {
            this.map[item[this.modelCode]] = item
          })
        }
      })
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      this.$emit('change', {
        value: value,
        node: this.map[value]
      })
      this.$emit('update:modelValue', value)
    },
    hasVehicle(vehicleId) {
      if (this.map[vehicleId]) {
        return true
      }
      return false
    },
    checkDisabled(item) {
      let index = this.detailList.findIndex(val => {
        return val.vehicleId === item[this.modelCode] && this.selectValue !== item[this.modelCode]
      })
      return index > -1
    }
  }
}
</script>

<style scoped lang="scss">
.select-option {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  div {
    margin-right: 10px;
    padding: 1px;
  }
}
</style>
