<template>
  <el-drawer
      custom-class="BsTagGroupDrawer"
      :title="title"
      v-model="modelVisible"
      direction="rtl"
      :before-close="handleClose"
      :size="500"
      append-to-body
  >
    <div class="query-form">
      <ltw-input
          :placeholder="$t('请输入关键字')"
          v-model="queryParam.key"
          clearable
          @clear="refresh"
          id="research-input"
      >
        <template #append>
          <el-button @click="refresh" id="el-icon-search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </template>
      </ltw-input>
    </div>
    <div class="drawer-body">
      <el-scrollbar>
        <!-- 三级结构：超级组 -> 组 -> 子组 -> 标签 -->
        <div v-for="item in filterTagGroupList" :key="item.id">
          <div v-if="isSuperGroup(item)" class="super-group-container">
            <div class="super-group-header">
              {{ item[locale === 'zh' ? 'nameCn' : 'name'] }}
            </div>
            <div class="super-group-content">
              <div
                v-for="group in item.children.filter(g => (g.children && g.children.length > 0) || (g.tagList && g.tagList.length > 0))"
                :key="group.id"
                class="group-container"
              >
                <div class="group-header">
                  {{ group[locale === 'zh' ? 'nameCn' : 'name'] }}
                </div>
                <div class="group-content">
                  <div
                    class="sub-group-container"
                    v-if="group.children && group.children.length > 0"
                  >
                    <div class="sub-group" v-for="subGroup in group.children" :key="subGroup.id">
                      <el-divider content-position="left">
                        {{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                      </el-divider>
                      <div class="tag-container" v-if="subGroup.tagList && subGroup.tagList.length > 0">
                        <el-radio-group
                          v-model="subGroup.checkedTagIdList"
                          @change="handleTagChange($event, subGroup, group, item)"
                        >
                          <el-radio
                            :disabled="tag.disabled"
                            :label="tag.id"
                            v-for="tag in subGroup.tagList"
                            :key="tag.id"
                            id="tag"
                          >
                            <el-tag
                              :effect="tag.disabled ? 'dark' : 'light'"
                              :type="checkType(tag)"
                            >{{
                              tag[locale === 'zh' ? 'nameCn' : 'name']
                            }}</el-tag>
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </div>
                  <!-- 组级别的标签展示（仅当是叶子节点时） -->
                  <div class="tag-container" v-if="isLeafNode(group) && group.tagList && group.tagList.length > 0">
                    <el-radio-group
                      v-model="group.checkedTagIdList"
                      @change="handleTagChange($event, group, item)"
                    >
                      <el-radio
                        :disabled="tag.disabled"
                        :label="tag.id"
                        v-for="tag in group.tagList"
                        :key="tag.id"
                        id="tag"
                      >
                        <el-tag
                          :effect="tag.disabled ? 'dark' : 'light'"
                          :type="checkType(tag)"
                        >{{
                          tag[locale === 'zh' ? 'nameCn' : 'name']
                        }}</el-tag>
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
              <!-- 超级组级别的标签展示（仅当是叶子节点时） -->
              <div class="tag-container" v-if="isLeafNode(item) && item.tagList && item.tagList.length > 0">
                <el-radio-group
                  v-model="item.checkedTagIdList"
                  @change="handleTagChange($event, item)"
                >
                  <el-radio
                    :disabled="tag.disabled"
                    :label="tag.id"
                    v-for="tag in item.tagList"
                    :key="tag.id"
                    id="tag"
                  >
                    <el-tag
                      :effect="tag.disabled ? 'dark' : 'light'"
                      :type="checkType(tag)"
                    >{{
                      tag[locale === 'zh' ? 'nameCn' : 'name']
                    }}</el-tag>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>

          <!-- 二级结构兼容：组 -> 子组 -> 标签 -->
          <div
            v-else-if="
              (item.children && item.children.length > 0) ||
              (item.tagList && item.tagList.length > 0)
            "
            class="group-container"
          >
            <div class="group-header">
              {{ item[locale === 'zh' ? 'nameCn' : 'name'] }}
            </div>
            <div class="group-content">
              <div
                class="sub-group-container"
                v-if="item.children && item.children.length > 0"
              >
                <div
                  class="sub-group"
                  v-for="subGroup in item.children"
                  :key="subGroup.id"
                >
                  <el-divider content-position="left">
                    {{ subGroup[locale === 'zh' ? 'nameCn' : 'name'] }}
                  </el-divider>
                  <div class="tag-container" v-if="subGroup.tagList && subGroup.tagList.length > 0">
                    <el-radio-group
                      v-model="subGroup.checkedTagIdList"
                      @change="handleTagChange($event, subGroup, item)"
                    >
                      <el-radio
                        :disabled="tag.disabled"
                        :label="tag.id"
                        v-for="tag in subGroup.tagList"
                        :key="tag.id"
                        id="tag"
                      >
                        <el-tag
                          :effect="tag.disabled ? 'dark' : 'light'"
                          :type="checkType(tag)"
                        >{{
                          tag[locale === 'zh' ? 'nameCn' : 'name']
                        }}</el-tag>
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
              <!-- 组级别的标签展示（仅当是叶子节点时） -->
              <div class="tag-container" v-if="isLeafNode(item) && item.tagList && item.tagList.length > 0">
                <el-radio-group
                  v-model="item.checkedTagIdList"
                  @change="handleTagChange($event, item)"
                >
                  <el-radio
                    :disabled="tag.disabled"
                    :label="tag.id"
                    v-for="tag in item.tagList"
                    :key="tag.id"
                    id="tag"
                  >
                    <el-tag
                      :effect="tag.disabled ? 'dark' : 'light'"
                      :type="checkType(tag)"
                    >{{
                      tag[locale === 'zh' ? 'nameCn' : 'name']
                    }}</el-tag>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirmDistributeTags" id="confirm">{{
          $t('确定')
        }}</el-button>
    </div>
  </el-drawer>
</template>
<script setup>
import {
  treeListBsTagGroup,
  treeListBsTagGroupReq
} from '@/apis/data-collect/bs-tag-group'
import { ref, reactive, watch, computed } from 'vue'
import { getLocale } from '@/plugins/util'
import { i18n } from '@/plugins/lang'
const props = defineProps({
  // 选中tag
  rowTagList: {
    type: Array,
    require: true,
    default: () => []
  },
  tagId: {
    type: String,
    require: true,
    default: ''
  },
  drawerVisible: {
    type: Boolean,
    required: true,
    default: false
  },
  requirementId: {
    type: String,
    default: ''
  },
  enabled: {
    type: Boolean,
    default: false
  },
  isPoi: {
    type: Boolean,
    default: false
  },
  isEngNew: {
    type: Boolean,
    default: false
  },
  forceReload: {
    type: Boolean,
    default: false
  }
  // model: {
  //   type: String,
  //   default: 'priview'
  // }
})
// 初始化字段
// let { rowTagList, model, drawerVisible } = toRefs(props)
// tag列表数据
let queryParam = reactive({ key: '' })
let tagItem = reactive({})
let tagGroupList = ref([])
let filterTagGroupList = ref([])
// let rowTagList = ref([])
let modelVisible = ref(false)
let locale = getLocale()
let $t = ref(i18n.global.t)
// 保存当前选中的标签ID，用于筛选后恢复选中状态
let currentSelectedTagId = ref('')
// const { locale } = useI18n()
let title = ref('')
title = i18n.global.t('标签筛选')
watch(() => props.tagId, handleTagDistributeDrawerOpen, { deep: false, immediate: true })
watch(
    () => props.requirementId,
    () => {
      tagGroupList.value = []
    }
)

watch(
  () => props.drawerVisible,
  newValue => {
    if (newValue && props.forceReload) {
     // 先清空缓存数据，强制重新渲染
      tagGroupList.value = []
      filterTagGroupList.value = []
      currentSelectedTagId.value = ''
      tagItem = {}
      // 抽屉打开且 forceReload 为 true 时重新加载数据
      loadTagGroups().then(() => {
        if (props.tagId) {
          handleTagChange(props.tagId)
        }
      })
    }
  }
)
// 监听搜索关键字变化，实时过滤
watch(
    () => queryParam.key,
    () => {
      filterTagGroup()
    }
)
modelVisible = computed({
  get: () => {
    return props.drawerVisible
  },
  set: value => {
    return props.drawerVisible
  }
})
// 页面tag管理
function handleTagDistributeDrawerOpen(val) {
  // tagId = val
  if (!tagGroupList.value || tagGroupList.value.length === 0) {
    loadTagGroups().then(() => {
      if (val) {
        handleTagChange(val) // 这里会通过 findTagInAllGroups 自动查找 group
      }
    })
  } else {
    filterTagGroup()
    if (val) {
      handleTagChange(val) // 这里会通过 findTagInAllGroups 自动查找 group
    }
  }
}

// 加载标签组数据
function loadTagGroups() {
  return new Promise((resolve) => {
    const processData = (res) => {
      tagGroupList.value = res.data
      initializeTagGroups(tagGroupList.value)
      filterTagGroup()
      resolve()
    }

    if (props.requirementId) {
      treeListBsTagGroupReq({ reqId: props.requirementId }).then(processData)
    } else if (props.isPoi) {
      treeListBsTagGroup({ poi: props.isPoi, ignoreEmptyLeafGroup: true }).then(processData)
    } else if (props.enabled) {
      treeListBsTagGroup({ enabled: props.enabled }).then(processData)
    } else if (props.isEngNew) {
      treeListBsTagGroup({ bisType: 'eng_new' }).then(processData)
    } else {
      treeListBsTagGroup({ mappingNewTag: false }).then(processData)
    }
  })
}
// 判断是否是最末级节点
function isLeafNode(node) {
  if (!node) {
    return true // 如果节点不存在，认为是叶子节点
  }
  // 优先使用 asLeaf 属性，如果没有则检查 children
  if (node.hasOwnProperty('asLeaf')) {
    return node.asLeaf
  }
  return !node.children || node.children.length === 0
}

// 判断是否为超级组（三级结构）
function isSuperGroup(item) {
  // 如果没有children，则不是超级组
  if (!item.children || item.children.length === 0) {
    return false
  }

  // 检查是否有子元素具有children属性（表示三级结构）
  // 三级结构：超级组 -> 组 -> 子组 -> 标签
  // 如果item的children中有任何一个还有children，则item是超级组
  return item.children.some(child =>
    child.children && child.children.length > 0
  )
}

// 初始化标签组数据
function initializeTagGroups(list) {
  if (!list) return

  list.forEach(item => {
    // 初始化选中状态属性
    if (!item.hasOwnProperty('checkedTagIdList')) {
      item.checkedTagIdList = ''
    }
    if (!item.hasOwnProperty('checkedAll')) {
      item.checkedAll = false
    }
    if (!item.hasOwnProperty('isIndeterminate')) {
      item.isIndeterminate = false
    }

    // 递归处理子级
    if (item.children && item.children.length > 0) {
      initializeTagGroups(item.children)
    }
  })
}

function filterTagGroup() {
  // 引用对象防止改变原数组，但保持选中状态
  let copyTagGroupList = JSON.parse(JSON.stringify(tagGroupList.value))

  // 保存当前选中状态的函数 - 使用ID匹配而不是索引匹配
  const preserveSelectionStateById = (originalList, copyList) => {
    if (!originalList || !copyList) return

    // 创建原始数据的ID映射
    const createIdMap = (list) => {
      const map = new Map()
      const traverse = (items) => {
        if (!items) return
        items.forEach(item => {
          map.set(item.id, {
            checkedTagIdList: item.checkedTagIdList || '',
            checkedAll: item.checkedAll || false,
            isIndeterminate: item.isIndeterminate || false
          })
          if (item.children) {
            traverse(item.children)
          }
        })
      }
      traverse(list)
      return map
    }

    // 应用选中状态的函数
    const applySelectionState = (items, stateMap) => {
      if (!items) return
      items.forEach(item => {
        const savedState = stateMap.get(item.id)
        if (savedState) {
          item.checkedTagIdList = savedState.checkedTagIdList
          item.checkedAll = savedState.checkedAll
          item.isIndeterminate = savedState.isIndeterminate
        } else {
          // 如果没有保存的状态，初始化为默认值
          item.checkedTagIdList = ''
          item.checkedAll = false
          item.isIndeterminate = false
        }
        if (item.children) {
          applySelectionState(item.children, stateMap)
        }
      })
    }

    const stateMap = createIdMap(originalList)
    applySelectionState(copyList, stateMap)
  }

  // 如果已有过滤结果，从过滤结果中保持状态，否则从原始数据保持状态
  const sourceList = filterTagGroupList.value.length > 0 ? filterTagGroupList.value : tagGroupList.value
  preserveSelectionStateById(sourceList, copyTagGroupList)

  if (queryParam.key) {
    let list = []
    copyTagGroupList.forEach(item => {
      if (isSuperGroup(item)) {
        // 处理三级结构：超级组 -> 组 -> 子组 -> 标签
        if (~item[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
          list.push(item)
        } else {
          let groupList = []
          if (item.children && item.children.length) {
            item.children.forEach(group => {
              if (~group[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                groupList.push(group)
              } else {
                let subGroupList = []
                if (group.children && group.children.length) {
                  group.children.forEach(subGroup => {
                    if (~subGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                      subGroupList.push(subGroup)
                    } else {
                      let tagList = []
                      if (subGroup.tagList && subGroup.tagList.length) {
                        subGroup.tagList.forEach(tag => {
                          if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                            tagList.push(tag)
                          }
                        })
                      }
                      subGroup.tagList = tagList
                      if (subGroup.tagList && subGroup.tagList.length) {
                        subGroupList.push(subGroup)
                      }
                    }
                  })
                }
                // 检查组级别的tagList（仅当没有子组时）
                if (group.tagList && group.tagList.length && (!group.children || group.children.length === 0)) {
                  let groupTagList = []
                  group.tagList.forEach(tag => {
                    if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                      groupTagList.push(tag)
                    }
                  })
                  group.tagList = groupTagList
                }
                group.children = subGroupList
                if ((group.children && group.children.length) || (group.tagList && group.tagList.length)) {
                  groupList.push(group)
                }
              }
            })
          }
          // 检查超级组级别的tagList（仅当没有子组时）
          if (item.tagList && item.tagList.length && (!item.children || item.children.length === 0)) {
            let superGroupTagList = []
            item.tagList.forEach(tag => {
              if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                superGroupTagList.push(tag)
              }
            })
            item.tagList = superGroupTagList
          }
          item.children = groupList
          if ((item.children && item.children.length) || (item.tagList && item.tagList.length)) {
            list.push(item)
          }
        }
      } else {
        // 处理二级结构：组 -> 子组 -> 标签（保持原有逻辑）
        if (~item[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
          list.push(item)
        } else {
          let subGroupList = []
          if (item.children && item.children.length) {
            item.children.forEach(subGroup => {
              if (
                  ~subGroup[locale === 'zh' ? 'nameCn' : 'name'].indexOf(
                      queryParam.key
                  )
              ) {
                subGroupList.push(subGroup)
              } else {
                let tagList = []
                if (subGroup.tagList && subGroup.tagList.length) {
                  subGroup.tagList.forEach(tag => {
                    if (
                        ~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(
                            queryParam.key
                        )
                    ) {
                      tagList.push(tag)
                    }
                  })
                }
                subGroup.tagList = tagList
                if (subGroup.tagList && subGroup.tagList.length) {
                  subGroupList.push(subGroup)
                }
              }
            })
          }
          // 检查组级别的tagList（仅当没有子组时）
          if (item.tagList && item.tagList.length && (!item.children || item.children.length === 0)) {
            let groupTagList = []
            item.tagList.forEach(tag => {
              if (~tag[locale === 'zh' ? 'nameCn' : 'name'].indexOf(queryParam.key)) {
                groupTagList.push(tag)
              }
            })
            item.tagList = groupTagList
          }
          item.children = subGroupList
          if ((item.children && item.children.length) || (item.tagList && item.tagList.length)) {
            list.push(item)
          }
        }
      }
    })
    filterTagGroupList.value = list
  } else {
    filterTagGroupList.value = copyTagGroupList
  }

  // 筛选完成后，如果有当前选中的标签，重新应用选中状态
  if (currentSelectedTagId.value) {
    restoreSelectedTag(currentSelectedTagId.value)
  }
}

// 恢复选中标签的函数
function restoreSelectedTag(tagId) {
  const findAndSelectTag = (list) => {
    for (const item of list) {
      if (item.tagList && item.tagList.length > 0) {
        const foundTag = item.tagList.find(tag => tag.id === tagId)
        if (foundTag) {
          item.checkedTagIdList = tagId
          tagItem = foundTag
          return true
        }
      }
      if (item.children && item.children.length > 0) {
        if (findAndSelectTag(item.children)) {
          return true
        }
      }
    }
    return false
  }

  findAndSelectTag(filterTagGroupList.value)
}

// 页面tag单选操作
function handleTagChange(val, group, parentGroup, superParentGroup) {
  currentSelectedTagId.value = val || ''
  clearAllSelections()

  // 设置当前选择
  if (val && group) {
    // 安全检查：确保 group 存在
    group.checkedTagIdList = val
    // 查找选中的标签项
    if (group.tagList && group.tagList.length > 0) {
      tagItem = group.tagList.find(tag => tag.id === val) || {}
    }
  } else if (val) {
    // 如果 group 为 undefined，尝试在所有数据中查找标签
    const foundTag = findTagInAllGroups(val)
    if (foundTag) {
      tagItem = foundTag.tag
      foundTag.group.checkedTagIdList = val
    }
  }
}

// 在所有分组中查找标签的辅助函数
function findTagInAllGroups(tagId) {
  const searchInList = (list) => {
    for (const item of list) {
      if (item.tagList && item.tagList.length > 0) {
        const foundTag = item.tagList.find(tag => tag.id === tagId)
        if (foundTag) {
          return { tag: foundTag, group: item }
        }
      }
      if (item.children && item.children.length > 0) {
        const result = searchInList(item.children)
        if (result) {
          return result
        }
      }
    }
    return null
  }

  // 先在过滤后的数据中查找
  let result = searchInList(filterTagGroupList.value)
  if (!result) {
    // 如果没找到，在原始数据中查找
    result = searchInList(tagGroupList.value)
  }
  return result
}

// 清空所有选择的辅助函数
function clearAllSelections() {
  // 清空原始数据的选择状态
  const clearSelectionInList = (list) => {
    list.forEach(item => {
      item.checkedTagIdList = ''
      item.checkedAll = false
      item.isIndeterminate = false
      if (item.children && item.children.length > 0) {
        clearSelectionInList(item.children)
      }
    })
  }

  // 同时清空原始数据和过滤数据的选择状态
  clearSelectionInList(tagGroupList.value)
  clearSelectionInList(filterTagGroupList.value)
}
// 注册事件
const emit = defineEmits(['drawer-click'])
function handleClose() {
  emit('drawer-click')
}
function confirmDistributeTags() {
  // 保存当前用户选择的 tagItem，避免被 refresh 覆盖
  const currentSelectedItem = tagItem ? { ...tagItem } : null

  if (queryParam.key) {
    queryParam.key = ''
    refresh()
    // 恢复用户选择的 tagItem
    if (currentSelectedItem) {
      tagItem = currentSelectedItem

    }
  }
  emit('drawer-click', { tagItem })
}
function refresh() {
  if (queryParam.key) {
    // 如果有搜索关键字，重新过滤
    filterTagGroup()
  } else {
    // 如果没有搜索关键字，重新加载数据
    loadTagGroups().then(() => {
      if (props.tagId) {
        handleTagChange(props.tagId) // 这里会通过 findTagInAllGroups 自动查找 group
      }
    })
  }
}

function checkType(tag) {
  if (tag.type === 'continuous') {
    return 'danger'
  } else if (tag.type === 'transient') {
    return 'success'
  }
}
</script>
<style lang="scss" scoped>
.BsTagGroupDrawer {
  .el-drawer__body {
    height: calc(100vh - 125px);
    padding-right: 10px;
    .query-form {
      margin-bottom: 10px;
    }
    .drawer-body {
      height: calc(100% - 92px);
      overflow-y: auto;
      /* 超级组容器样式 */
      .super-group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        border-radius: 6px;
        margin-bottom: 20px;

        .super-group-header {
          font-size: 14px;
          color: #303133;
          padding: 15px 20px;
          border-bottom: 1px solid var(--el-border-color-light, #ebeef5);
          border-radius: 4px 4px 0 0;
          justify-content: center;
          align-items: center;
          display: flex;
          font-weight: bold;
        }

        .super-group-content {
          padding: 15px 0px;
        }
      }

      .group-container {
        border: 1px solid var(--el-border-color-light, #ebeef5);
        border-radius: 4px;
        padding: 10px;
        box-shadow: var(--el-box-shadow-light);
        margin-bottom: 10px;
        .group-header {
          display: flex;
          justify-content: center;
          align-items: center;
          // .el-radio {
          //   font-weight: bold;
          // }
        }
        .el-radio {
          margin-right: 15px;
        }
        :deep(.danger) {
          color: #f56c6c;
        }
        .success {
          color: #67c23a;
        }
      }
    }
    .drawer-footer {
      height: 50px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
</style>