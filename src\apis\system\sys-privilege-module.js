import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysPrivilegeModule = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules', data, params})
export const updateSysPrivilegeModule = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules', data, params})
export const deleteSysPrivilegeModule = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules', params})
export const listSysPrivilegeModule = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules', params})
export const listSysPrivilegeModuleSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules/selections', params})
export const pageSysPrivilegeModule = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules/page', params})
export const getSysPrivilegeModule = (id) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules/' + id})
export const listSysPrivilegeModuleOfCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_privilege_modules/current_user', params})
