<template>
  <div class="version-container">
    <div class="top">
      <div class="title weight-font">
        <ltw-icon icon-code="el-icon-tools"></ltw-icon>
        <span v-text="'工具信息'"></span>
      </div>
      <div class="table-content">
        <el-table :data="toolInfoDataList" ref="toolTableRef" max-height="200px">
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('软件')"
              prop="module"
              show-overflow-tooltip
          >
            <template #default="scope">
              <span class="weight-font">{{scope.row.module  || '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('版本')"
              prop="version"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('buildTime')"
              prop="buildTime"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('最后获取时间')"
              prop="createTime"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="bottom">
      <div class="title weight-font">
        <ltw-icon icon-code="el-icon-tools"></ltw-icon>
        <span v-text="'软件信息'"></span>
      </div>
      <div class="table-content">
        <el-table :data="softwareDataList" ref="softwareTableRef" max-height="calc(100% - 300px)">
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('软件')"
              prop="module"
              show-overflow-tooltip
          >
            <template #default="scope">
              <el-tag>{{scope.row.module || '-'}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('节点')"
              prop="node"
              show-overflow-tooltip
          >
            <template #default="scope">
              <el-tag type="success">{{scope.row.node || '-'}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('Branch')"
              prop="branch"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              width="280"
              :label="$t('CommitID')"
              prop="commitId"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('buildTime')"
              prop="buildTime"
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              header-align="left"
              align="left"
              :label="$t('最后获取时间')"
              prop="createTime"
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SoftwareVersionInfo',
  data() {
    return {
      toolInfoDataList: [],
      softwareDataList: []
    }
  },
  methods: {
    show(data) {
      this.toolInfoDataList = data.toolInfoDataList
      this.softwareDataList = data.softwareDataList
    }
  }

}
</script>
<style lang="scss" scoped>
.version-container {
  display: flex;
  padding-left:10px;
  flex-direction: column;
  .weight-font{
    font-weight: 600;
  }
  .title{
    display: flex;
    height: 25px;
    align-items: end;

  }

  .top {
    margin-bottom: 15px;
    .el-table{
      max-width: 700px;
    }
  }
}

</style>