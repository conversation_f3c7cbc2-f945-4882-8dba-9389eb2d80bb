(function (window) {
    document.addEventListener('DOMContentLoaded', () => {
        const root = document.createElement('DIV')
        root.id = 'yunser-iconfont-symbols'
        root.innerHTML = ` 
            <svg aria-hidden="true" style="position: absolute; width: 0px; height: 0px; overflow: hidden;">
                
        <symbol id="shadow-mode" viewBox="0 0 24 24">
            

<path d="M21.4 12C21.4 17.1886 17.1886 21.4 12 21.4C6.81137 21.4 2.6 17.1886 2.6 12C2.6 6.81137 6.81137 2.6 12 2.6C17.1886 2.6 21.4 6.81137 21.4 12Z" stroke-width="1.2"></path>


<path d="M12 21.1501V3.05008L7.50005 4.05008L3.70005 8.05008L3.05005 10.9001L3.45005 14.8001L5.30005 18.2501L8.60005 20.6501L12 21.1501Z" stroke-width="1.2"></path>


        </symbol>
        <symbol id="shadow-data" viewBox="0 0 24 24">
            

<path d="M19.8 4.2V16.8H7.2V4.2H19.8ZM19.9983 3H7.00802C5.90878 3 6.00872 3.9 6.00872 5V6V16C6.00872 17.1 5.90878 18 7.00802 18H19.9983C21.0976 18 20.9976 17.1 20.9976 16V5C20.9976 3.9 21.0976 3 19.9983 3ZM6.2 6H4.0101C3.45782 6 3.0101 6.44772 3.0101 7V19C3.0101 20.1 2.83583 21 3.93506 21H16.999C17.5513 21 17.999 20.5523 17.999 20V17.654L16.8 18V19.654H4.2V7.2H6.2V6Z"></path>


<path d="M17.4 10.5C17.4 12.6526 15.6526 14.4 13.5 14.4C11.3474 14.4 9.6 12.6526 9.6 10.5C9.6 8.34737 11.3474 6.6 13.5 6.6C15.6526 6.6 17.4 8.34737 17.4 10.5Z" stroke-width="1.2"></path>


<path d="M13.9093 14.6636V6.48801L11.8638 6.81814L10.2275 8.4545L9.81836 9.68178V11.7272L10.2275 12.9545L12.5408 14.6636H13.9093Z"></path>


        </symbol>
        <symbol id="shadow-analysis-rule" viewBox="0 0 24 24">
            

<path d="M3.21098 18.7893V11.9999V5.2106V10.3026V10.0031V18.7893ZM4.00997 19.9874C3.46066 19.9874 2.69096 20.0916 2.30012 19.7009C1.90861 19.3095 2.01248 18.5397 2.01248 17.9905V6.00934C2.01248 5.46021 1.90861 4.69075 2.30012 4.30003C2.69096 3.90864 3.46066 4.01248 4.00997 4.01248H7.15602H9.0037H14.9962H16.8439H19.9899C20.5392 4.01248 21.3092 3.90864 21.7008 4.30003C22.0916 4.69075 21.9874 5.46021 21.9874 6.00934V12.2511C21.9654 12.2353 21.9447 12.2188 21.9251 12.2018C21.6766 11.987 21.5968 11.691 21.2883 11.5522C20.9554 11.4024 21.1551 11.4191 20.7889 11.3026V7.7566V5.2106H3.21098V11.9999V18.7893H7.14354H13.0761C13.126 19.1387 13.0761 18.939 13.2009 19.2386C13.3415 19.5665 13.5041 19.66 13.6474 19.9316C13.6568 19.9494 13.6661 19.968 13.6753 19.9874H4.00997Z"></path>


<path d="M13.0761 18.7893C13.126 19.1387 13.0761 18.939 13.2009 19.2386C13.3415 19.5665 13.5041 19.66 13.6474 19.9316L13.0761 18.7893Z"></path>


<path d="M21.9251 12.2018C21.6766 11.987 21.5968 11.691 21.2883 11.5522C20.9554 11.4024 21.1551 11.4191 20.7889 11.3026L21.9251 12.2018Z"></path>


<path d="M5.10864 7.00684C4.77727 7.00684 4.50864 7.27547 4.50864 7.60684C4.50864 7.93821 4.77727 8.20684 5.10864 8.20684V7.00684ZM5.10864 8.20684H12.8489V7.00684H5.10864V8.20684Z"></path>


<path d="M5.10864 9.20341C4.77727 9.20341 4.50864 9.47203 4.50864 9.80341C4.50864 10.1348 4.77727 10.4034 5.10864 10.4034V9.20341ZM5.10864 10.4034H12.8489V9.20341H5.10864V10.4034Z"></path>


<path d="M5.10864 11.3999C4.77727 11.3999 4.50864 11.6686 4.50864 11.9999C4.50864 12.3313 4.77727 12.5999 5.10864 12.5999V11.3999ZM5.10864 12.5999H10.7016V11.3999H5.10864V12.5999Z"></path>


<path d="M13.498 7.60684H14.6965" stroke-width="1.2"></path>


<path d="M13.3982 9.80341H14.5967" stroke-width="1.2"></path>


<path d="M15.2959 7.60684H16.4944" stroke-width="1.2"></path>


<path d="M17.0935 7.60684H18.292" stroke-width="1.2"></path>


<path d="M21.4 15.5C21.4 17.6526 19.6526 19.4 17.5 19.4C15.3474 19.4 13.6 17.6526 13.6 15.5C13.6 13.3474 15.3474 11.6 17.5 11.6C19.6526 11.6 21.4 13.3474 21.4 15.5Z" stroke-width="1.2"></path>


<path d="M17.909 19.6637V11.4881L15.8636 11.8182L14.2272 13.4546L13.8181 14.6818V16.7273L14.2272 17.9546L16.5406 19.6637H17.909Z"></path>


        </symbol>
        <symbol id="shadow-data-bag" viewBox="0 0 24 24">
            

<path d="M11.5291 6.37179C11.6429 6.51591 11.8164 6.6 12 6.6H20.0526C20.2143 6.6 20.4 6.74809 20.4 7V20C20.4 20.2519 20.2143 20.4 20.0526 20.4H3.94737C3.78567 20.4 3.6 20.2519 3.6 20V4C3.6 3.74811 3.78568 3.6 3.94737 3.6H9.34082L11.5291 6.37179Z" stroke-width="1.2" stroke-linejoin="round"></path>


<path d="M16.1021 13.6011C16.1021 15.8095 14.3095 17.6021 12.1011 17.6021C9.89265 17.6021 8.1 15.8095 8.1 13.6011C8.1 11.3927 9.89265 9.6 12.1011 9.6C14.3095 9.6 16.1021 11.3927 16.1021 13.6011Z" stroke-width="1.2"></path>


<path d="M12.1011 17.8111V9.48312L10.0306 9.94323L8.28222 11.7837L7.98315 13.095L8.1672 14.8894L9.0184 16.4768L10.5368 17.581L12.1011 17.8111Z"></path>


        </symbol>
        <symbol id="shadow-mapping" viewBox="0 0 24 24">
            

<path d="M19.9999 2.99994H4.86322C3.76322 2.99994 2.99993 2.89994 2.99993 3.99994L2.99993 17C2.99993 18.1 2.89993 18 3.99993 18H8.99993L7.99991 21H15.9999L14.9999 18L19.9999 17.9999C21.0999 17.9999 20.9999 18.1 20.9999 17L20.9999 4.99994C20.9999 3.89994 21.0999 2.99994 19.9999 2.99994ZM19.9999 15H3.99993V3.99999L19.9999 3.99994V15Z"></path>


<path d="M18.4 11C18.4 12.3246 17.3246 13.4 16 13.4C14.6754 13.4 13.6 12.3246 13.6 11C13.6 9.67537 14.6754 8.6 16 8.6C17.3246 8.6 18.4 9.67537 18.4 11Z" stroke-width="1.2"></path>


<path d="M16.2727 13.7757V8.32535L14.909 8.54544L13.8181 9.63635L13.5454 10.4545V11.8182L13.8181 12.6363L15.3604 13.7757H16.2727Z"></path>


<path d="M6 5.4C5.66863 5.4 5.4 5.66863 5.4 6C5.4 6.33137 5.66863 6.6 6 6.6V5.4ZM6 6.6H13.7403V5.4H6V6.6Z"></path>


<path d="M14.2402 6H15.4387" stroke-width="1.2"></path>


<path d="M15.9387 6H17.1372" stroke-width="1.2"></path>


<path d="M17.6372 6H18.8357" stroke-width="1.2"></path>


<path d="M6 7.4C5.66863 7.4 5.4 7.66863 5.4 8C5.4 8.33137 5.66863 8.6 6 8.6V7.4ZM6 8.6H11.593V7.4H6V8.6Z"></path>


<path d="M6 9.4C5.66863 9.4 5.4 9.66863 5.4 10C5.4 10.3314 5.66863 10.6 6 10.6V9.4ZM6 10.6H11V9.4H6V10.6Z"></path>


        </symbol>
        <symbol id="shadow-car" viewBox="0 0 24 24">
            

<path d="M19.4501 18.6842C19.4501 19.603 18.7499 20.2947 17.9501 20.2947C17.1503 20.2947 16.4501 19.603 16.4501 18.6842C16.4501 17.7654 17.1503 17.0737 17.9501 17.0737C18.7499 17.0737 19.4501 17.7654 19.4501 18.6842Z" stroke-width="1.2"></path>


<path d="M4.1501 19.5262H3.92881C3.38199 19.5262 2.90432 19.1565 2.76717 18.6272L2.71751 18.4355C2.70593 18.3908 2.6988 18.3451 2.69622 18.299L2.67736 17.9615C2.63014 17.1165 3.07185 16.3193 3.81336 15.9113L4.00831 15.804C4.53157 15.5161 5.10397 15.3284 5.69611 15.2505L6.3668 15.1622C6.65349 15.1245 6.93146 15.0373 7.18835 14.9046L9.5501 13.6841L9.78906 13.5454C10.5823 13.0847 11.4833 12.842 12.4006 12.842H13.7798C14.2258 12.842 14.671 12.8808 15.1103 12.9579L15.3229 12.9952C16.3261 13.1712 17.2716 13.5877 18.0785 14.2092L18.9001 14.842L19.5172 14.9435C20.1288 15.0441 20.6456 15.4523 20.8851 16.0239L21.1008 16.5384C21.2 16.7752 21.2724 17.0223 21.3167 17.2751V17.2751C21.4784 18.1991 20.9239 19.098 20.0256 19.3682L19.5001 19.5262M8.0001 19.5262H16.1501" stroke-width="1.2" stroke-linecap="square"></path>


<path d="M7.5502 18.7894C7.5502 19.7083 6.85 20.4 6.0502 20.4C5.25039 20.4 4.5502 19.7083 4.5502 18.7894C4.5502 17.8706 5.25039 17.1789 6.0502 17.1789C6.85 17.1789 7.5502 17.8706 7.5502 18.7894Z" stroke-width="1.2"></path>


<path d="M15.4 6.5C15.4 8.10063 14.1006 9.4 12.5 9.4C10.8994 9.4 9.6 8.10063 9.6 6.5C9.6 4.89937 10.8994 3.6 12.5 3.6C14.1006 3.6 15.4 4.89937 15.4 6.5Z" stroke-width="1.2"></path>


<path d="M13 9.00005V4L11.5 3.28271L10.3 4.68378L10 5.73458V7.48591L10.3 8.53671L11.9965 9.00005H13Z"></path>


        </symbol>
        <symbol id="shadow-car-prameter" viewBox="0 0 24 24">
            

<path d="M15.4 12C15.4 13.8766 13.8766 15.4 12 15.4C10.1234 15.4 8.6 13.8766 8.6 12C8.6 10.1234 10.1234 8.6 12 8.6C13.8766 8.6 15.4 10.1234 15.4 12Z" stroke-width="1.2"></path>


<path d="M12.5714 14.8572V9.14284L10.8571 8.32309L9.48568 9.92431L9.14282 11.1252V13.1267L9.48568 14.3277L11.4245 14.8572H12.5714Z"></path>


<circle cx="12" cy="12" r="9.4" stroke-width="1.2"></circle>


<path d="M11.4 6C11.4 6.33137 11.6686 6.6 12 6.6C12.3314 6.6 12.6 6.33137 12.6 6L11.4 6ZM11.4 3L11.4 6L12.6 6L12.6 3L11.4 3Z"></path>


<path d="M12.6 18C12.6 17.6686 12.3314 17.4 12 17.4C11.6686 17.4 11.4 17.6686 11.4 18L12.6 18ZM12.6 21L12.6 18L11.4 18L11.4 21L12.6 21Z"></path>


<path d="M6 12.6C6.33137 12.6 6.6 12.3314 6.6 12C6.6 11.6686 6.33137 11.4 6 11.4V12.6ZM3 12.6H6V11.4H3V12.6Z"></path>


<path d="M18 11.4C17.6686 11.4 17.4 11.6686 17.4 12C17.4 12.3314 17.6686 12.6 18 12.6L18 11.4ZM21 11.4L18 11.4L18 12.6L21 12.6L21 11.4Z"></path>


        </symbol>
        <symbol id="shadow-bev" viewBox="0 0 23 24">
            

<path fill-rule="evenodd" clip-rule="evenodd" d="M5 22C4.44772 22 4 21.5523 4 21V3C4 2.44772 4.44771 2 5 2H14.2642L20 7.86185V21C20 21.5523 19.5523 22 19 22H5ZM13.4088 3.15578V8.68342H18.8428V20.8442H5.0566V3.15578H13.4088ZM14.6164 7.47739V4.01005L18.0377 7.47739H14.6164Z"></path>


<path d="M15.4 14C15.4 15.8766 13.8766 17.4 12 17.4C10.1234 17.4 8.6 15.8766 8.6 14C8.6 12.1234 10.1234 10.6 12 10.6C13.8766 10.6 15.4 12.1234 15.4 14Z" stroke-width="1.2"></path>


<path d="M12.3637 17.7011V10.4339L10.5455 10.7274L9.09093 12.1819L8.72729 13.2728V15.091L9.09093 16.1819L11.1473 17.7011H12.3637Z"></path>


        </symbol>
            </svg> 
        `
        document.body.appendChild(root)
    })
})(window)
