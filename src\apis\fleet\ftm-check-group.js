import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmCheckGroup = (data = {}, params = {}) => httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups', data, params })
export const updateFtmCheckGroup = (data = {}, params = {}) => httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups', data, params })
export const deleteFtmCheckGroup = (params = {}) => httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups', params })
export const listFtmCheckGroup = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups', params })
export const listFtmCheckGroupSelection = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups/selections', params })
export const pageFtmCheckGroup = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups/page', params })
export const getFtmCheckGroup = (id) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups/' + id })
export const treeWebCheckGroupList = (params = {}) => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_check_groups/treeWebCheckGroupList', params })
