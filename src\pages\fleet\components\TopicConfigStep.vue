<template>
  <div class="topic-config-step">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-section">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <p>正在解析ZIP包中的Topic映射关系...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-section">
      <el-icon class="error-icon"><Warning /></el-icon>
      <p>{{ error }}</p>
      <el-button type="primary" @click="loadTopics">重新加载</el-button>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="main-content">
      <!-- 上方Regex Filters面板 -->
      <div class="top-panel">
        <div class="regex-filters-header" :class="{ 'expanded': isFiltersExpanded }">
          <div class="header-left">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">Regex Filters</span>
            <el-button
              type="text"
              size="small"
              class="toggle-btn"
              @click="toggleFiltersExpanded"
            >
              <el-icon :class="{ 'rotate-180': isFiltersExpanded }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
          <el-button
            type="primary"
            size="small"
            class="add-filter-btn"
            @click="addRegexFilter"
          >
            + 新增
          </el-button>
        </div>
        
        <div v-show="isFiltersExpanded" class="regex-filters-content">
          <div
            v-for="(filter, index) in regexFilters"
            :key="index"
            class="filter-item"
          >
            <el-input
              v-model="filter.value"
              placeholder="REGEX:.*XXX.*"
              class="filter-input"
              @input="handleFilterChange"
            />
            <el-button
              type="text"
              class="remove-filter-btn"
              @click="removeRegexFilter(index)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <!-- 当没有过滤器时显示提示 -->
          <div v-if="regexFilters.length === 0" class="no-filters">
            <span class="no-filters-text">暂无过滤器</span>
          </div>
        </div>
      </div>

      <!-- 下方Topic列表 -->
      <div class="bottom-panel">
        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="selection-info">
            <div class="stat-item">
              <el-icon class="stat-icon"><Document /></el-icon>
              <span class="stat-label">已选择</span>
              <span class="stat-value">{{ selectedCount }} / {{ topicList.length }}</span>
            </div>
            <div class="divider"></div>
            <div class="stat-item">
              <el-icon class="stat-icon"><Document /></el-icon>
              <span class="stat-label">总数据大小</span>
              <span class="stat-value">{{ formatFileSize(selectedTotalSize) }}</span>
            </div>
          </div>
          <div class="search-section">
            <ltw-input
              v-model="searchKeyword"
              placeholder="请输入关键字"
              class="search-input"
              clearable
              @input="handleSearch"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            >
              <template #append>
                <el-button class="search-btn" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </ltw-input>
          </div>
        </div>

        <!-- Topic表格 -->
        <div class="topic-table">
          <el-table
            :data="filteredTopicList"
            style="width: 100%"
            height="100%"
            @selection-change="handleSelectionChange"
          >
            <!-- 选择列 -->
            <el-table-column width="40" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.selected"
                  @change="handleTopicSelect(row)"
                />
              </template>
            </el-table-column>
            
            <!-- Topic Path -->
            <el-table-column prop="topic" label="Topic Path" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="topic-path">{{ row.topic }}</div>
              </template>
            </el-table-column>
            
            <!-- Size -->
            <el-table-column prop="size" label="Size" width="80" align="center">
              <template #default="{ row }">
                <span class="size-value">{{ formatSize(row.size) }}</span>
              </template>
            </el-table-column>
            
            <!-- Freq(Hz) -->
            <el-table-column prop="frequency" label="Freq(Hz)" width="80" align="center">
              <template #default="{ row }">
                <span class="frequency-value">{{ row.frequency || 0 }}</span>
              </template>
            </el-table-column>
            
            <!-- Minibag(0 B/s) -->
            <el-table-column label="Minibag(0 B/s)" width="110" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.minibag"
                  :disabled="!row.selected"
                  @change="handleMinibagChange(row)"
                />
              </template>
            </el-table-column>
            
            <!-- Latched Port -->
            <el-table-column label="Latched Port" width="100" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.latched"
                  :disabled="!row.selected"
                  @change="handleLatchedChange(row)"
                />
              </template>
            </el-table-column>
            
            <!-- 数据质检 -->
            <el-table-column label="数据质检" width="200" align="center">
              <template #default="{ row }">
                <div class="data-quality-section">
                  <el-switch
                    v-model="row.dataQuality.enabled"
                    :disabled="!row.selected"
                    @change="handleDataQualityToggle(row)"
                  />
                  <el-popover
                    v-if="row.dataQuality.enabled"
                    placement="top"
                    :width="350"
                    trigger="click"
                    popper-class="data-quality-popover"
                  >
                    <template #reference>
                      <div
                        data-closable="off"
                        data-effect="light"
                        data-round="off"
                        data-size="small"
                        data-type="primary"
                        :style="{
                          'padding-left': '8px',
                          'padding-right': '8px',
                          'background': getDataQualityStyle(row.dataQuality).background,
                          'overflow': 'hidden',
                          'border-radius': '2px',
                          'outline': getDataQualityStyle(row.dataQuality).outline,
                          'outline-offset': '-1px',
                          'justify-content': 'center',
                          'align-items': 'center',
                          'gap': '4px',
                          'display': 'inline-flex',
                          'cursor': 'pointer'
                        }"
                      >
                        <div :style="{
                          'text-align': 'center',
                          'color': getDataQualityStyle(row.dataQuality).color,
                          'font-size': '12px',
                          'font-family': 'Inter',
                          'font-weight': '400',
                          'line-height': '20px',
                          'word-wrap': 'break-word'
                        }">
                          {{ formatDataQualityConfig(row.dataQuality) }}
                        </div>
                      </div>
                    </template>
                    
                    <div class="data-quality-popover-content">
                      <h4 class="popover-title">数据质检配置</h4>
                      <div class="popover-topic">{{ row.topic }}</div>
                      
                      <el-form
                        :model="row.dataQuality"
                        label-width="80px"
                        size="small"
                        class="popover-form"
                      >
                        <el-form-item label="最小频率">
                          <el-input-number
                            v-model="row.dataQuality.minFrequency"
                            :min="0"
                            :max="9999.9"
                            :step="0.1"
                            :precision="1"
                            placeholder="最小频率"
                            style="width: 90%"
                            @change="handleDataQualityChange(row)"
                          />
                          <span class="unit">Hz</span>
                        </el-form-item>

                        <el-form-item label="最大频率">
                          <el-input-number
                            v-model="row.dataQuality.maxFrequency"
                            :min="0"
                            :max="9999.9"
                            :step="0.1"
                            :precision="1"
                            placeholder="最大频率"
                            style="width: 90%"
                            @change="handleDataQualityChange(row)"
                          />
                          <span class="unit">Hz</span>
                        </el-form-item>
                        
                        <el-form-item label="错误等级">
                          <el-select
                            v-model="row.dataQuality.errorLevel"
                            placeholder="选择错误等级"
                            style="width: 100%"
                            @change="handleDataQualityChange(row)"
                          >
                            <el-option label="Critical" value="Critical" />
                            <el-option label="Warning" value="Warning" />
                            <el-option label="Info" value="Info" />
                          </el-select>
                        </el-form-item>
                      </el-form>
                    </div>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Document, Close, ArrowDown } from '@element-plus/icons-vue'
import { parseZipTopics } from '@/apis/fleet/whitelist-management'

export default {
  name: 'TopicConfigStep',
  components: {
    Loading,
    Warning,
    Document,
    Close
  },
  props: {
    zipPath: {
      type: String,
      required: true
    }
  },
  emits: ['topics-changed'],
  setup(props, { emit }) {
    const loading = ref(false)
    const error = ref('')
    const topicList = ref([])
    const selectedTopics = ref([])
    const searchKeyword = ref('')
    
    // Regex Filters
    const regexFilters = ref([])
    const isFiltersExpanded = ref(false) // 默认收起

    // 已选择的Topic数量
    const selectedCount = computed(() => {
      return topicList.value.filter(topic => topic.selected).length
    })

    // 已选择的Topic总数据大小
    const selectedTotalSize = computed(() => {
      return topicList.value
        .filter(topic => topic.selected)
        .reduce((total, topic) => total + (topic.size || 0), 0)
    })

    // 过滤后的Topic列表
    const filteredTopicList = computed(() => {
      if (!searchKeyword.value.trim()) {
        return topicList.value
      }

      const keyword = searchKeyword.value.toLowerCase()
      return topicList.value.filter(topic =>
        topic.topic?.toLowerCase().includes(keyword) ||
        topic.interfaceName?.toLowerCase().includes(keyword) ||
        topic.instanceName?.toLowerCase().includes(keyword)
      )
    })

    // 加载Topic列表
    const loadTopics = async () => {
      if (!props.zipPath) {
        error.value = 'ZIP包路径不能为空'
        return
      }

      loading.value = true
      error.value = ''
      
      try {
        const response = await parseZipTopics({ zipPath: props.zipPath })
        
        if (response.data) {
          topicList.value = response.data.map(topic => ({
            ...topic,
            selected: false,
            minibag: false,
            latched: false,
            dataQuality: {
              enabled: false,
              minFrequency: 0,
              maxFrequency: 0,
              errorLevel: 'Critical'
            }
          }))
          
          // 通知父组件Topic列表变化
          emitTopicsChanged()
        } else {
          error.value = response.message || '解析ZIP包失败'
        }
      } catch (err) {
        console.error('加载Topic列表失败:', err)
        error.value = '加载Topic列表失败，请检查网络连接'
      } finally {
        loading.value = false
      }
    }

    // 格式化大小
    const formatSize = (bytes) => {
      if (!bytes) return '0'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    }

    // 格式化文件大小（用于总数据大小显示）
    const formatFileSize = (bytes) => {
      if (!bytes || bytes === 0) return '0 B'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 全选
    const selectAll = () => {
      topicList.value.forEach(topic => {
        topic.selected = true
      })
      emitTopicsChanged()
    }

    // 全不选
    const selectNone = () => {
      topicList.value.forEach(topic => {
        topic.selected = false
        topic.minibag = false
        topic.latched = false
        topic.dataQuality.enabled = false
      })
      emitTopicsChanged()
    }

    // 处理表格选择变化
    const handleSelectionChange = (selection) => {
      selectedTopics.value = selection
    }

    // 处理搜索
    const handleSearch = () => {
      // 搜索逻辑已在computed中处理，这里可以添加额外的搜索逻辑
      console.log('搜索关键词:', searchKeyword.value)
      console.log('过滤后的Topic数量:', filteredTopicList.value.length)
    }

    // 清空搜索
    const clearSearch = () => {
      searchKeyword.value = ''
    }

    // 处理Topic选择
    const handleTopicSelect = (topic) => {
      if (!topic.selected) {
        // 取消选择时，同时取消相关配置
        topic.minibag = false
        topic.latched = false
        topic.dataQuality.enabled = false
      }
      emitTopicsChanged()
    }

    // 处理Minibag变化
    const handleMinibagChange = (topic) => {
      emitTopicsChanged()
    }

    // 处理Latched变化
    const handleLatchedChange = (topic) => {
      emitTopicsChanged()
    }

    // 处理数据质检开关
    const handleDataQualityToggle = (topic) => {
      if (topic.dataQuality.enabled) {
        // 开启数据质检时，设置默认值
        topic.dataQuality.minFrequency = 0
        topic.dataQuality.maxFrequency = 0
        topic.dataQuality.errorLevel = 'Critical'
      }
      emitTopicsChanged()
    }

    // 格式化数据质检配置显示
    const formatDataQualityConfig = (dataQuality) => {
      if (!dataQuality.enabled) {
        return '点击配置'
      }

      const { minFrequency, maxFrequency, errorLevel } = dataQuality

      // 如果有任何值为空或未定义，显示"配置中"
      if (minFrequency === undefined || minFrequency === null ||
          maxFrequency === undefined || maxFrequency === null ||
          !errorLevel) {
        return '配置中...'
      }

      // 格式化错误等级显示
      const levelMap = {
        'Critical': 'critical',
        'Warning': 'warning',
        'Info': 'info'
      }

      const displayLevel = levelMap[errorLevel] || errorLevel.toLowerCase()
      return `${minFrequency} - ${maxFrequency}，${displayLevel}`
    }

    // 获取数据质检配置的样式
    const getDataQualityStyle = (dataQuality) => {
      if (!dataQuality.enabled) {
        return {
          background: '#F5F5F5',
          outline: '1px #DDDDDD solid',
          color: '#999999'
        }
      }

      const { minFrequency, maxFrequency, errorLevel } = dataQuality

      if (minFrequency === undefined || minFrequency === null ||
          maxFrequency === undefined || maxFrequency === null ||
          !errorLevel) {
        return {
          background: '#FFF7E6',
          outline: '1px #FFD591 solid',
          color: '#FA8C16'
        }
      }

      // 根据错误等级设置不同颜色
      const styleMap = {
        'Critical': {
          background: '#FFF2F0',
          outline: '1px #FFCCC7 solid',
          color: '#FF4D4F'
        },
        'Warning': {
          background: '#FFFBE6',
          outline: '1px #FFE58F solid',
          color: '#FAAD14'
        },
        'Info': {
          background: '#F5F5FF',
          outline: '1px #DDDDFF solid',
          color: '#5755FF'
        }
      }

      return  styleMap['Info']
    }

    // 处理数据质检配置变化
    const handleDataQualityChange = (topic) => {
      // 验证配置
      const { minFrequency, maxFrequency, errorLevel } = topic.dataQuality

      if (minFrequency < 0 || maxFrequency < 0) {
        ElMessage.error('频率不能为负数')
        return
      }

      if (minFrequency > 9999.9 || maxFrequency > 9999.9) {
        ElMessage.error('频率不能超过9999.9')
        return
      }

      if (minFrequency > maxFrequency) {
        ElMessage.error('最小频率不能大于最大频率')
        return
      }

      if (!errorLevel) {
        ElMessage.error('请选择错误等级')
        return
      }

      // 配置验证通过，通知父组件
      emitTopicsChanged()
    }

    // 切换Regex Filters展开收起
    const toggleFiltersExpanded = () => {
      isFiltersExpanded.value = !isFiltersExpanded.value
    }

    // 新增Regex Filter
    const addRegexFilter = () => {
      regexFilters.value.push({ value: '' })
      // 新增时自动展开
      isFiltersExpanded.value = true
      // 通知父组件数据变化
      emitTopicsChanged()
    }

    // 移除Regex Filter
    const removeRegexFilter = (index) => {
      regexFilters.value.splice(index, 1)
      // 通知父组件数据变化
      emitTopicsChanged()
    }

    // 处理Regex Filter变化
    const handleFilterChange = () => {
      // 当Regex Filter变化时，重新加载Topic列表以应用过滤
      loadTopics()
      // 通知父组件数据变化
      emitTopicsChanged()
    }

    // 通知父组件Topic变化
    const emitTopicsChanged = () => {
      const selectedTopics = topicList.value.filter(topic => topic.selected)
      emit('topics-changed', {
        total: topicList.value.length,
        selected: selectedTopics.length,
        topics: selectedTopics,
        regexFilters: regexFilters.value.map(filter => filter.value).filter(value => value.trim())
      })
    }

    // 监听zipPath变化
    watch(() => props.zipPath, (newPath) => {
      if (newPath) {
        loadTopics()
      }
    }, { immediate: true })

    // 组件挂载时加载数据
    // onMounted(() => {
    //   if (props.zipPath) {
    //     loadTopics()
    //   }
    // })

    return {
      loading,
      error,
      topicList,
      selectedTopics,
      selectedCount,
      selectedTotalSize,
      searchKeyword,
      filteredTopicList,
      regexFilters,
      isFiltersExpanded,
      loadTopics,
      formatSize,
      formatFileSize,
      selectAll,
      selectNone,
      handleSelectionChange,
      handleSearch,
      clearSearch,
      handleTopicSelect,
      handleMinibagChange,
      handleLatchedChange,
      handleDataQualityToggle,
      handleDataQualityChange,
      formatDataQualityConfig,
      getDataQualityStyle,
      toggleFiltersExpanded,
      addRegexFilter,
      removeRegexFilter,
      handleFilterChange
    }
  }
}
</script>

<style lang="scss" scoped>
.topic-config-step {
  .loading-section,
  .error-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #909399;

    .loading-icon,
    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .loading-icon {
      animation: rotate 2s linear infinite;
    }

    .error-icon {
      color: #f56c6c;
    }

    p {
      margin: 0 0 16px 0;
      font-size: 14px;
    }
  }

  .main-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding:12px 16px;
    background-color: #f5f7fa;
    border-radius: 8px;
    overflow: hidden;
    height: 600px;
  }

  .top-panel {
    flex: 0 0 auto;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .regex-filters-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;
      transition: margin-bottom 0.3s ease;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      &.expanded {
        margin-bottom: 16px;
      }

      .header-icon {
        font-size: 20px;
        color: #409eff;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .toggle-btn {
        padding: 4px;
        margin-left: 8px;

        .el-icon {
          font-size: 14px;
          color: #909399;
          transition: transform 0.3s ease;

          &.rotate-180 {
            transform: rotate(180deg);
          }
        }

        &:hover .el-icon {
          color: #409eff;
        }
      }

      .add-filter-btn {
        flex-shrink: 0;
      }
    }

    .regex-filters-content {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      transition: all 0.3s ease;
      overflow: hidden;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-input {
          width: 250px;
          flex-shrink: 0;
        }

        .remove-filter-btn {
          flex-shrink: 0;
        }
      }

      .no-filters {
        text-align: center;
        color: #909399;
        font-size: 14px;
        padding: 20px 0;
      }
    }
  }

  .bottom-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 0;
      background: white;
      border-radius: 8px;
      padding: 0 10px;
      padding-bottom: 8px;
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    padding-right: 12px;
    padding-bottom: 0;
    border-radius: 8px;

    .selection-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-icon {
          color: #232628;
          font-size: 16px;
        }

        .stat-label {
          color: #4E5256;
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
        }

        .stat-value {
          color: #5755FF;
          font-size: 12px;
          font-weight: 700;
          line-height: 24px;
        }
      }

      .divider {
        width: 1px;
        height: 13px;
        background: #D0D4D8;
        border-radius: 1px;
      }
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }

    .search-section {
      flex: 1;
      max-width: 300px;
    }
    }
  }

    .topic-table {
      flex: 1;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      :deep(.el-table) {
        height: 100%;

        .el-table__body-wrapper {
          max-height: calc(100% - 40px);
          overflow-y: auto;
        }

        .el-table__header {
          background: #f5f7fa;

          th {
            background: #f5f7fa !important;
            color: #303133;
            font-weight: 600;
            border-bottom: 1px solid #e4e7ed;
          }
        }

        .el-table__body {
          tr {
            &:hover {
              background: #f8f9fa;
            }

            td {
              border-bottom: 1px solid #f0f0f0;
              padding: 12px 0;
            }
          }
        }
      }

      .topic-path {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        color: #303133;
        word-break: break-all;
      }

      .size-value,
      .frequency-value {
        font-weight: 500;
        color: #606266;
      }

      .data-quality-section {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
      }
    }
  

  .data-quality-form {
    .el-form-item {
      margin-bottom: 20px;

      .unit {
        margin-left: 8px;
        color: #909399;
        font-size: 12px;
      }
    }
  }

  // 数据质检popover样式
  :deep(.data-quality-popover) {
    .el-popover__title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }
  }

  .data-quality-popover-content {
    .popover-title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .popover-topic {
      font-size: 12px;
      color: #909399;
      margin-bottom: 16px;
      word-break: break-all;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .popover-form {
      .el-form-item {
        margin-bottom: 12px;

        .el-form-item__label {
          font-size: 12px;
          color: #606266;
        }

        .unit {
          margin-left: 4px;
          color: #909399;
          font-size: 11px;
        }
      }
    }
  }


@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .topic-config-step {
    .main-content {
      height: auto;
      min-height: 500px;
    }

    .top-panel {
      flex: none;
      width: 100%;
      padding: 12px;
      box-shadow: none;
    }

    .bottom-panel {
      flex: none;
      width: 100%;

    }

    .action-bar {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;

      .selection-info {
        justify-content: center;
        flex-wrap: wrap;
      }

      .batch-actions {
        justify-content: center;
      }

      .search-section {
        max-width: 100%;
      }
    }

    .topic-table {
      :deep(.el-table) {
        font-size: 12px;

        .topic-path {
          font-size: 11px;
        }
      }
    }
  }
}
</style>
