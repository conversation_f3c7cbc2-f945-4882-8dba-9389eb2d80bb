import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysDictionary = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries', data, params})
export const updateSysDictionary = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries', data, params})
export const deleteSysDictionary = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries', params})
export const listSysDictionary = (params = {}, unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries', params, unloading})
export const listSysDictionarySelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries/selections', params})
export const pageSysDictionary = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries/page', params})
export const sysDictionaryTypes = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionary_types/dictionaries', params})
export const getSysDictionary = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_dictionaries/' + id})
