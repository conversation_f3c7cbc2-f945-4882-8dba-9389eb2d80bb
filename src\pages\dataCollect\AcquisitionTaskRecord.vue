<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="left-button">
          <!-- <ltw-input
            :placeholder="$t('请输入停车场')"
            v-model="queryParam.locationName"
            clearable
            @clear="refresh"
            class="search-input"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input> -->
          <!--          <el-select v-model="queryParam.locationId" clearable filterable @change="refresh" popper-class="locationId">-->
          <!--            <el-option v-for="item in parkingList" :key="item.id" :label="item.name" :value="item.id"></el-option>-->
          <!--          </el-select>-->
        </div>

        <div class="ltw-tool-container button-group">
<!--          <el-button class="filter-btn" @click="filterAcquisitionTaskRecord" id="el-icon-filter">-->
<!--            <ltw-icon icon-code="el-icon-filter"></ltw-icon>-->
<!--            <span>{{ $t('筛选') }}</span>-->
<!--          </el-button>-->
<!--          <el-button class="import-file" type="primary" @click="importData">{{ $t('导入') }}</el-button>-->
          <el-button
            :id="item.buttonIconCode"
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button id="batchOperateBtn" type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                  :id="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table :data="pageData?.records" stripe @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
        <el-table-column
          header-align="left"
          align="left"
          type="selection"
          width="55"
          reserve-selection
          fixed="left"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="taskCode"
          :label="$t('任务')"
          :width="150"
          show-overflow-tooltip
          fixed="left"
        >
          <template #default="scope">
            <el-link type="primary" :underline="false" @click="view(scope.row)">{{ scope.row.taskCode }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
            header-align="left"
            align="left"
            prop="sourceType"
            :label="$t('来源')"
            :width="130"
            show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag type="success">
              <ltw-icon :icon-code="'svg-'+scope.row.sourceType"></ltw-icon>
              {{ scope.row.sourceTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="executingDate" :label="$t('执行日期')" width="120">
          <template #default="scope">
            <el-tag type="success">{{ scope.row.executingDate }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="vin"
          :label="$t('车辆')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="acquisitionType"
          :label="$t('采集类型')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="cantonName"
          :label="$t('区域')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="locationName"
          :label="$t('停车场/路线')"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="address"
          :label="$t('地址')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="typeName" :label="$t('停车场类型')" width="100">
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="duration" :label="$t('采集时长')" width="104">
          <template #default="scope">
            <el-tooltip
              effect="dark"
              :content="formatSecToDate(scope.row.duration || 0).time"
              placement="top"
              :enterable="false"
            >
              <el-tag>{{ formatSecToDate(scope.row.duration || 0).time }}</el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="executingDate" :label="$t('执行日期')" width="310">
          <template #default="scope">
            <template v-if="scope.row.startTime">
              <el-tag>{{ scope.row.startTime }}</el-tag>
              &nbsp;&nbsp;~&nbsp;&nbsp;
              <el-tag> {{ scope.row.endTime }}</el-tag>
            </template>
          </template>
        </el-table-column>
      <el-table-column header-align="left" align="left" prop="editable" :label="$t('是否有数据绑定采集记录')">
        <template #default="scope">
          <span v-if="scope.row.editable">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>

        <el-table-column header-align="left" align="left" :label="$t('动态场景属性')" min-width="130">
          <template #default="scope">
            <el-button type="primary" size="small" @click="showTaskExtension(scope.row.items)" v-if="scope.row.items">
              <ltw-icon icon-code="el-icon-InfoFilled"></ltw-icon>
              Parking Kpi
            </el-button>
          </template>
        </el-table-column>

        <el-table-column
          header-align="left"
          align="left"
          :label="$t('操作')"
          min-width="160"
          fixed="right"
          v-if="inlineFunctionList?.length"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button
                  :id="item.buttonIconCode"
                  :type="item.buttonStyleType"
                  @click="executeButtonMethod(item, scope.row)"
                  :disabled="item.buttonCode === 'edit' && !scope.row.editable"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData?.total"
      >
      </el-pagination>
    </el-card>
    <add-collect-record-dialog :task-readonly="false" ref="AddCollectRecordDialog" @reload="query"></add-collect-record-dialog>

    <el-dialog
      :title="$t('导入数据')"
      v-model="importVisible"
      width="50%"
      @close="importDialogClosed"
      @open="importDialogOpened"
      destroy-on-close
    >
      <el-form :model="importFormData" :rules="formRules" ref="importFormRef" label-width="100px">
        <el-form-item class="form-item" :label="$t('文件')" prop="fileList">
          <el-upload :limit="1" :auto-upload="false" :http-request="httpRequest" accept=".xlsx" :on-change="fileChange">
            <el-button type="primary">上传文件</el-button>
            <template #tip>
              <div class="el-upload__tip">limit 1 file</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('采集类型')" prop="acquisitionType">
          <dictionary-selection
            v-model="importFormData.acquisitionType"
            clearable
            dictionaryType="acquisition_type"
            :placeholder="$t('请选择')"
            filterable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogClosed">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit">{{ $t('确认') }}</el-button>
        </span>
      </template>
    </el-dialog>
    <FilterAcquisitionTaskRecord ref="FilterAcquisitionTaskRecord" @reload="reload" />
  </div>

  <el-dialog title="查看动态参数" v-model="taskExtensionShow" width="600px" append-to-body :draggable="true">
    <Codemirror
        v-if="taskExtensionShow"
        v-model:value="taskExtension"
        border
        :options="cmOptions"
    ></Codemirror>
  </el-dialog>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util, { showToast, showConfirmToast, checkFileSize, dateUtils, downloadFile } from '@/plugins/util'
import {
  pageDAQTaskRecords,
  deleteDAQTaskRecords,
  saveStaTaskRecordData,
    getDAQTaskRecords,
  importData,
  exportDaqTaskRecordExcel
} from '@/apis/data-collect/acquisition-task-record'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import AddCollectRecordDialog from '@/pages/dataCollect/dialog/AddOrderCollectRecordDialog.vue'
import FilterAcquisitionTaskRecord from '@/pages/dataCollect/dialog/FilterAcquisitionTaskRecord.vue'
import LtwIcon from "@/components/base/LtwIcon.vue";
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'

export default {
  name: 'AcquisitionTaskRecord',
  components: {
    LtwIcon,
    AddCollectRecordDialog,
    DictionarySelection,
    FilterAcquisitionTaskRecord,
    Codemirror
  },
  data() {
    return {
      taskExtension:[],
      taskExtensionShow:false,
      cmOptions:{
        mode: 'application/json', // Language mode text/yaml、text/javascript
        theme: 'dracula', // Theme
        indentUnit: 4, // 缩进多少个空格
        tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否默认换行
        // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
        // line: true,
        smartIndent: true // 智能缩进
      },
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
        // locationId: ''
      },
      measureTime: [],
      dialogVisible: false,
      selectedData: [],
      checkFileSize: checkFileSize,
      unitList: [
        {
          code: 'b',
          name: 'B'
        },
        {
          code: 'kb',
          name: 'KB'
        },
        {
          code: 'm',
          name: 'M'
        },
        {
          code: 'g',
          name: 'G'
        },
        {
          code: 't',
          name: 'T'
        }
      ],
      formatSecToDate: dateUtils.formatSecToDate,
      parkingList: [],
      importVisible: false,
      importFormData: {},
      formRules: {
        fileList: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('文件'),
            trigger: 'change'
          }
        ],
        acquisitionType: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('采集类型'),
            trigger: 'change'
          }
        ]
      },
      groundLevelList: [],
      vehicleList: []
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    showTaskExtension(items) {
      this.taskExtensionShow = true
      // this.taskExtension = items
      this.taskExtension = JSON.stringify(
          Array.isArray(items) ? items : [],
          null,
          '\t'
      );
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      // ;[this.queryParam.startTime, this.queryParam.endTime] =
      //   this.measureTime?.length > 0 ? [this.measureTime[0], this.measureTime[1]] : [undefined, undefined]
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageDAQTaskRecords(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.$refs.AddCollectRecordDialog.show({
        type: 'add'
      })
    },
    transformToB(value, name) {
      if (this.formData[name + 'Unit'] !== 'b') {
        switch (this.formData[name + 'Unit']) {
          case 'kb':
            value *= Math.pow(2, 10)
            break
          case 'm':
            value *= Math.pow(2, 20)
            break
          case 'g':
            value *= Math.pow(2, 30)
            break
          case 't':
            value *= Math.pow(2, 40)
            break
        }
      }
      return value
    },
    transformToUnit(value, name) {
      if (value > 0 && value < Math.pow(2, 10)) {
        this.formData[name + 'Unit'] = 'b'
        return value || 0
      } else if (value >= Math.pow(2, 10) && value < Math.pow(2, 20)) {
        this.formData[name + 'Unit'] = 'kb'
        return parseFloat(parseFloat(value / Math.pow(2, 10)).toFixed(2))
      } else if (value >= Math.pow(2, 20) && value < Math.pow(2, 30)) {
        this.formData[name + 'Unit'] = 'm'
        return parseFloat(parseFloat(value / Math.pow(2, 20)).toFixed(2))
      } else if (value >= Math.pow(2, 30) && value < Math.pow(2, 40)) {
        this.formData[name + 'Unit'] = 'g'
        return parseFloat(parseFloat(value / Math.pow(2, 30)).toFixed(2))
      } else if (value >= Math.pow(2, 40)) {
        this.formData[name + 'Unit'] = 't'
        return parseFloat(parseFloat(value / Math.pow(2, 40)).toFixed(2))
      }
    },
    edit(row) {
      this.$refs.AddCollectRecordDialog.show({
        type: 'edit',
        data: {
          id: row.id
        }
      })
    },
    view(row) {
      this.$refs.AddCollectRecordDialog.show({
        type: 'view',
        data: {
          id: row.id
        }
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDAQTaskRecords(param).then(() => {
          this.$refs.tableRef.clearSelection()
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    dataStatistic(row) {
      saveStaTaskRecordData(row.id).then(res => {
        this.query()
        return showToast('车辆该日数据统计成功')
      })
    },
    importData() {
      this.importVisible = true
    },
    fileChange(file, fileList) {
      this.importFormData.fileList = fileList
    },
    httpRequest(option) {
      // this.importFormData.fileList.push(option)
    },
    submit() {
      this.$refs.importFormRef.validate(valid => {
        if (!valid) return
        let postData = new FormData()
        postData.append('file', this.importFormData.fileList[0].raw)
        postData.append('acquisitionType', this.importFormData.acquisitionType)
        importData(postData).then(res => {
          this.importDialogClosed()
        })
      })
    },
    importDialogClosed() {
      this.importVisible = false
      this.importFormData = {}
    },
    importDialogOpened() {},
    export() {
      let postData = {
        ...this.queryParam
      }
      delete postData.current
      delete postData.size
      exportDaqTaskRecordExcel(postData).then(res => {
        downloadFile(res, '采集任务记录列表')
      })
    },
    filter() {
      this.$refs.FilterAcquisitionTaskRecord.show()
    },
    reload(val) {
      if (val) {
        this.queryParam = {
          current: this.queryParam.current,
          size: this.queryParam.size,
          ...JSON.parse(JSON.stringify(val))
        }
      }
      this.query()
    }
  }
}
</script>
<style>
.executing-date-picker > .el-input__inner {
  width: 200px;
}
</style>
<style scoped lang="scss">
.ltw-toolbar {
  display: flex;

  .left-button {
    //display: flex;
    // & > :deep(.search-input) {
    //   width: 300px;
    //   margin-right: 5px;
    // }
    :deep(.el-date-editor) {
      width: 400px;
      margin-right: 10px;
    }

    .select-input {
      margin-right: 10px;
      width: 200px;
    }

    .select-input-key {
      margin-right: 10px;
      width: 300px;
    }

    .import-file {
      margin-right: 10px;
    }
  }
}

.selector-input {
  width: 200px;
}

.executingDate {
  & > :deep(.el-date-editor) {
    width: 200px;
  }
}

.el-select {
  width: 200px;
}

.form-item-line {
  :deep(.el-form-item__label) {
    line-height: 20px;
  }
}

.button-group {
  .el-button {
    margin-right: 0px;
  }
}

.data-size {
  width: 200px;
}
</style>
