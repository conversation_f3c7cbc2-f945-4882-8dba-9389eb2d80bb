<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row :gutter="10">
        <!-- <el-col :span="12">
          <el-form-item :label="$t('名称')" prop="name">
            <ltw-input
              v-model="form.name"
              :disabled="formReadonly"
              id="name"
            ></ltw-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item :label="$t('采集类型')" prop="acquisitionType">
            <el-radio-group v-model="form.acquisitionType" :disabled="formReadonly">
              <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code"
                >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
            <!-- <dictionary-selection
              v-model="form.acquisitionType"
              clearable
              dictionaryType="data_acquisition_type"
              :placeholder="$t('请选择')"
              filterable
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车架号')" prop="vin">
            <bs-vehicle-selection
              v-model="form.vin"
              model-code="vin"
              :clearable="true"
              @change="handleVehilceChange($event)"
              :disabled="formReadonly"
            ></bs-vehicle-selection>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('数据类型')" prop="rawDataBagType">
            <common-selection
              v-model="form.rawDataBagType"
              model-code="rawDataBagType"
              model-Name="rawDataBagType"
              :model-options="rawDataBagTypeList"
              :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('期望日期')" prop="expectedDate">
            <el-date-picker
              v-model="form.expectedDate"
              type="date"
              :placeholder="$t('期望日期')"
              value-format="YYYY-MM-DD"
              :disabled="formReadonly"
              :disabled-date="disabledExpectedDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item :label="$t('期望时间')" prop="startTime">
            <el-time-select
              v-model="form.startTime"
              :max-time="form.endTime"
              :placeholder="$t('开始时间')"
              step="00:30"
              start="00:00"
              end="23:30"
              format="HH:mm:ss"
              :disabled="formReadonly"
            />
            <span class="range">To</span>
            <el-time-select
              v-model="form.endTime"
              :min-time="form.startTime"
              :placeholder="$t('结束时间')"
              step="00:30"
              start="00:00"
              end="23:30"
              format="HH:mm:ss"
              :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('合规官')" prop="complianceOfficerEmpId">
            <employee-selection
              clearable
              :data="complianceOfficerList"
              :auto-load="false"
              :disabled="formReadonly"
              v-model="form.complianceOfficerEmpId"
              @change="changeComplianceOfficer"
            ></employee-selection>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('司机')" prop="driverEmpId">
            <employee-selection
              clearable
              :data="driverList"
              :auto-load="false"
              :disabled="formReadonly"
              v-model="form.driverEmpId"
              @change="changeDriver"
            ></employee-selection>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('软件版本')" prop="softVersion">
            <ltw-input v-model="form.softVersion" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('commit id')" prop="commitId">
            <ltw-input v-model="form.commitId" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item :label="$t('仓库地址')" prop="repositoryUrl">
            <ltw-input v-model="form.repositoryUrl" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('优先级')" prop="priority">
            <dictionary-selection
              v-model="form.priority"
              clearable
              dictionaryType="priority_level"
              :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.acquisitionType === 'driving'">
          <el-form-item :label="$t('路线')" prop="locationName">
            <ltw-input v-model="form.locationName" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item :label="$t('配置信息')" prop="configuration">
            <Codemirror
              v-model:value="form.configuration"
              :options="cmOptions"
              :height="200"
              @change="configurationChange"
              @blur="configurationBlur"
              @focus="configurationFocus"
              @scroll="configurationScroll"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item :label="$t('备注')" prop="remark">
            <ltw-input type="textarea" v-model="form.remark" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          v-if="formReadonly && form.status && form.status !== 'draft'"
          type="info"
          plain
          @click="getCollectRecord"
          >{{ $t('采集记录') }}</el-button
        >
        <el-button v-if="form.id && formReadonly" type="danger" @click="deleteForm">{{ $t('删除') }}</el-button>
        <el-button v-if="!form.id || formReadonly" @click="cancel()" plain id="view-cancel">{{ $t('关闭') }}</el-button>
        <el-button v-if="form.id && !formReadonly" @click="cancelForm" plain id="cancel">{{ $t('取消') }}</el-button>
        <el-button v-if="formReadonly" type="primary" plain @click="edit">{{ $t('编辑') }}</el-button>
        <el-button v-if="!formReadonly" type="primary" @click="submit" id="submit" plain>{{ $t('保存') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { dateUtils, showToast, showConfirmToast } from '@/plugins/util'
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
// JSON验证
// import jsonlint from 'jsonlint' //引入
// import 'codemirror/addon/lint/json-lint.js'

import { listSysDictionary } from '@/apis/system/sys-dictionary'
import {
  saveFtmTask,
  updateFtmTask,
  getFtmTask,
  variantVersionSelection,
  deleteFtmTask
} from '@/apis/data-collect/vt-order-records'
import { getDriverList, listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import CommonSelection from '@/components/system/CommonSelection.vue'

const defaultform = {}
export default {
  name: 'AddDriver',
  components: {
    DictionarySelection,
    BsVehicleSelection,
    CommonSelection,
    EmployeeSelection,
    Codemirror
  },
  emits: ['reload', 'getCollectRecord'],
  data() {
    let isTimes = (rule, value, callback) => {
      if (this.form.startTime && this.form.endTime) {
        callback()
      } else {
        return callback(new Error('请选择时间'))
      }
    }
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      backupForm: {},
      formRules: {
        acquisitionType: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        vin: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        expectedDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        startTime: [
          { required: true, message: this.$t('请选择'), trigger: 'change' },
          {
            validator: isTimes,
            trigger: ['blur', 'change']
          }
        ],
        complianceOfficerEmpId: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        driverEmpId: [{ required: true, message: this.$t('请选择'), trigger: 'change' }]
      },
      rawDataBagTypeList: [],
      acquisitionTypeList: [],
      complianceOfficerList: [],
      driverList: [],
      cmOptions: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.form = { ...row?.data }
          this.dialogTitle = this.$t('新增预约记录')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑预约记录')
          this.getFtmTask(row)
          break
        case 'view':
          this.dialogTitle = this.$t('预约记录详情')
          this.getFtmTask(row)
          break
      }
      setTimeout(() => {
        this.cmOptions = {
          mode: 'application/json', // Language mode text/yaml、text/javascript
          theme: 'dracula', // Theme
          // readOnly: 'nocursor'
          indentUnit: 4, // 缩进多少个空格
          tabSize: 4, // 制表符宽度
          // lineNumbers: true, // 是否显示行号
          lineWrapping: true, // 是否默认换行
          // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
          readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
          // line: true,
          smartIndent: true // 智能缩进
        }
      })
      this.getDataTypes()
      this.getDriverList()
      this.listComplianceOfficer()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      // if (this.form.id) {
      this.$emit('reload', val)
      // }
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
      this.backupForm = {}
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          expectedStartTime: this.form.expectedDate + ' ' + this.form.startTime,
          expectedEndTime: this.form.expectedDate + ' ' + this.form.endTime
        }
        if (this.dialogStatus === 'add') {
          saveFtmTask(postData).then(() => {
            this.cancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmTask(postData, {}, false).then(() => {
            // this.cancel()
            showToast('更新成功')
            this.dialogStatus = 'view'
          })
        }
      })
    },
    edit() {
      this.dialogStatus = 'edit'
      this.backupForm = JSON.parse(JSON.stringify(this.form))
    },
    cancelForm() {
      this.dialogStatus = 'view'
      this.form = JSON.parse(JSON.stringify(this.backupForm))
    },
    deleteForm() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmTask({ id: this.form.id }).then(() => {
          this.cancel()
        })
      })
    },
    getFtmTask(row) {
      getFtmTask(row.id).then(res => {
        if (res.data.expectedStartTime && res.data.expectedEndTime) {
          res.data.expectedDate = res.data.expectedStartTime.split(' ')[0]
          res.data.startTime = res.data.expectedStartTime.split(' ')[1]
          res.data.endTime = res.data.expectedEndTime.split(' ')[1]
        }
        if (res.data.priority) {
          res.data.priority = res.data.priority.toString()
        }
        this.form = res.data
      })
    },
    handleVehilceChange(e) {
      if (e.value) {
        this.variantVersionSelection()
      } else {
        this.rawDataBagTypeList = []
        this.form.rawDataBagType = ''
      }
    },
    variantVersionSelection() {
      variantVersionSelection({ vin: this.form.vin }).then(res => {
        this.form.rawDataBagType = res.data.rawDataBagType
        this.rawDataBagTypeList = [res.data]
      })
    },
    getDataTypes() {
      if (!this.acquisitionTypeList?.length) {
        listSysDictionary({
          typeCode: 'acquisition_task_type'
        }).then(res => {
          if (res.data?.length) {
            this.form.acquisitionType = res.data[0].code
          }
          this.acquisitionTypeList = res.data
        })
      } else {
        this.form.acquisitionType = this.acquisitionTypeList[0].code
      }
    },
    listComplianceOfficer() {
      if (!this.complianceOfficerList?.length) {
        listSysRoleEmployee({
          orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
        }).then(res => {
          this.complianceOfficerList = res.data
        })
      }
    },

    getDriverList() {
      if (!this.driverList?.length) {
        getDriverList().then(res => {
          this.driverList = res.data
        })
      }
    },
    changeComplianceOfficer(e) {
      this.form.complianceOfficerEmpName = e.node.name
    },
    changeDriver(e) {
      this.form.driverEmpName = e.node.name
    },
    configurationChange() {},
    configurationBlur() {
      try {
        this.form.configuration = JSON.stringify(JSON.parse(this.form.configuration || '{}'), null, '\t')
      } catch (error) {
        console.log(error)
      }
    },
    configurationFocus() {},
    configurationScroll() {},
    disabledExpectedDate(val) {
      return new Date(val) < new Date(dateUtils.parseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00')
    },
    getCollectRecord() {
      this.$emit('getCollectRecord', {
        code: this.form.code
      })
    }
  }
}
</script>

<style scoped lang="scss">
.range {
  margin: 0 10px;
}
</style>
