import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmShadowVehicle = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles',
    data,
    params
})
export const updateFtmShadowVehicle = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles',
    data,
    params
})
export const deleteFtmShadowVehicle = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles',
    params
})
export const listFtmShadowVehicle = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles',
    params
})
export const listFtmShadowVehicleSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles/selections',
    params
})
export const pageFtmShadowVehicle = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles/page',
    params
})
export const getFtmShadowVehicle = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_shadow_vehicles/' + id})
