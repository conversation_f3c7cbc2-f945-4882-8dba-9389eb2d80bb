<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="60%"
    @close="dialogClosed"
    append-to-body
    z-index="9999999"
    :destroy-on-close="true"
  >
    <div class="pose-file">
      <div v-if="txt">
        <Codemirror height="500px" v-model:value="txt" border :options="cmOptions" />
      </div>
      <div v-else>
        <el-empty :image-size="200" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" id="cancel">{{ $t('关闭') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import Codemirror from 'codemirror-editor-vue3'
// language
// import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js'
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'

const defaultFormData = {}
export default {
  name: 'ViewTxtDialog',
  emits: ['reload', 'cancel'],
  components: { Codemirror },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      cmOptions: {},
      txt: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    show(row) {
      this.txt = row.data
      this.dialogTitle = '预览文本'
      this.dialogVisible = true
      this.dialogStatus = row.type
      setTimeout(() => {
        this.cmOptions = {
          mode: 'application/json', // Language mode text/yaml、text/javascript
          theme: 'dracula', // Theme
          // readOnly: 'nocursor'
          indentUnit: 4, // 缩进多少个空格
          tabSize: 4, // 制表符宽度
          // lineNumbers: true, // 是否显示行号
          lineWrapping: true, // 是否默认换行
          // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
          readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
          // line: true,
          smartIndent: true // 智能缩进
        }
      })
    },
    dialogClosed() {
      //this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    initForm() {
      this.txt = ''
    },
    onFocus() {},
    onScroll() {}
  }
}
</script>

<style scoped lang="scss">
.pose-file {
  height: 500px;
}
</style>
