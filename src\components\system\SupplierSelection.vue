<template>
  <el-select
    v-model="selectValue"
    :placeholder="$t('请选择')"
    filterable
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    popper-class="supplier-selection"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.name"
      :value="item.id"
      id="RoleEmployeeList"
    ></el-option>
  </el-select>
</template>

<script>
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { ElSelect, ElOption } from 'element-plus'
import { i18n } from '@/plugins/lang'
export default {
  name: 'EmployeeSelection',
  props: {
    queryParam: {
      type: Object,
      default: () => {
        return {
          tagCode: 'org_supplier_fleet'
        }
      }
    },
    modelValue: [String, Number],
    disabled: Boolean,
    autoLoad: {
      type: Boolean,
      default: true
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      map: {},
      list: [],
      hasLoaded: false,
      $t: i18n.global.t
    }
  },
  components: {
    ElSelect,
    ElOption
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  created() {
    if (this.autoLoad) {
      this.query()
    } else {
      this.list = this.data
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  watch: {
    data(val) {
      this.list = val
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  methods: {
    query() {
      listSysRoleOrg(this.queryParam).then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          this.map = {}
          this.list.forEach(item => {
            this.map[item.id] = item
          })
        }
      })
    },
    reload() {
      this.query()
    },
    clear() {
      this.list = []
    },
    handleChange(value) {
      // this.$emit('change', value)
      this.$emit('change', {
        value: value,
        node: this.map[value]
      })
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped></style>
