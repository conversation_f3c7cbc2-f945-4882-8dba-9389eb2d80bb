<template>
  <div id="allmap" ref="allmap"></div>
</template>

<script>
import { BaiduMap } from '@/plugins/map/map'

export default {
  name: 'ParkingPosition',
  data() {
    return {
      map: ''
    }
  },
  props: {
    position: {
      type: Object,
      default: {}
    }
  },
  watch: {
    position: {
      handler(val) {
        this.show(val)
      },
      deep: true,
      immediate: true
    }
  },
  emits: ['changePosition', 'reload'],
  mounted() {},
  methods: {
    show(data) {
      this.dialogVisible = true
      this.formReadonly = data.formReadonly
      BaiduMap.init().then(BMap => {
        // window.BMap = BMap;
        this.map = new BMap.Map(this.$refs.allmap, { enableMapClick: false }) // 创建Map实例
        this.map.centerAndZoom(
          new BMap.Point(120.59168154789141, 31.305976454846554),
          11
        ) // 初始化地图,设置中心点坐标和地图级别
        // if (data?.disabledScrollWheelZoom) {
        this.map.disableScrollWheelZoom()
        //平移缩放控件
        this.map.addControl(new BMap.NavigationControl())
        // } else {
        //   this.map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
        // }
        this.map.disableDoubleClickZoom()
        if (data.longitude && data.latitude) {
          this.getPosition(data)
        }
      })
      // });
    },
    getPosition(obj) {
      let myIcon = new BMap.Icon(
        require('@/assets/images/mapScreen/map-position.png'),
        new BMap.Size(26, 36)
      )
      let point = new BMap.Point(obj.longitude, obj.latitude)
      this.map.centerAndZoom(point, 16)
      // 创建标注，为要查询的地方对应的经纬度
      let marker = new BMap.Marker(point, { icon: myIcon }) // 自定义标记
      this.map.addOverlay(marker)
      marker.setAnimation(BMAP_ANIMATION_DROP) // 跳动的动画BMAP_ANIMATION_DROP BMAP_ANIMATION_BOUNCE
    }
  }
}
</script>
<style lang="scss">
.search-position-dialog {
  .el-dialog__header {
    position: relative;
    padding: 0;
    margin-bottom: 0;
    .el-button {
      position: absolute;
      right: -32px;
      top: -16px;
      z-index: 1;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}

.map-address {
  position: absolute;
  left: 0;
  top: 0;
  color: #455a70;
  text-align: left;
  padding: 15px 20px;
  white-space: nowrap;
  background: #fff;
  .map-form-item {
    padding: 5px;
    font-size: 14px;
  }
  .el-button {
    min-height: 36px;
    height: 36px;
    line-height: 36px;
    padding: 0 19px;
  }
  .el-input__inner {
    height: 36px;
    line-height: 36px;
  }
}
.tangram-suggestion {
  z-index: 9999;
}
</style>

<style lang="scss" scoped>
.map-form {
  position: absolute;
  width: 100%;
  z-index: 1;
  top: 5%;
  #r-result {
    width: 60%;
    margin: 0 auto;
  }
}
// }

#allmap {
  height: 300px;
  :deep(.anchorBL) {
    display: none;
  }
}
</style>
