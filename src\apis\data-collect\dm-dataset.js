import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDmDataset = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets', data, params})
export const saveDmData = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/data', data, params})
export const createDmData = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/datas', data, params})
export const updateDmDataset = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets', data, params})
export const deleteDmDataset = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets', params})
export const listDmDataset = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/current_user', params})
export const listDmDatasetSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/selections', params})
export const pageDmDataset = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/page', params})
export const getDmDataset = (id,unloading=false) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/' + id,unloading})
export const treeListDmDataset= (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/tree/current_user', params})
export const currentPageDmDataset= (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/page/current_user', params})
export const treeGroupPageDmDataset= (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/tree/page', params})
export const pageData = (data = {},params = {},unloading=true) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/data/page', data,params,unloading})
export const deleteData = (data = {}, params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/data', data, params})
export const listAccessRole=(params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/share/access_role', params})
export const shareDmDataset=(data = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/share', data})
export const getdownloadData = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/data/list', data, params})
export const syncFrameData=(data = {}, params = {})=> httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dataset/inner_data/sync',data, params})
export const createDatasetSimulationType=(data = {}, params = {})=> httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootAnalyticsUrl + '/dataset_simulation/background_create',data, params})
export const listDataset = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets', params})
export const listRelatedDataset= (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/related/'+id})
export const mergeDataset=(data = {},id)=> httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/data/dm_datasets/merge/'+id , data})
