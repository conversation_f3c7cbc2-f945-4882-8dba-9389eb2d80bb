<template>
  <div class="step-recommend">
    <el-tabs v-model="activeRecommend" @tab-change="handleTabClick" class="tab-container">
      <el-tab-pane v-if="form.acquisitionType !== 'driving'" :label="$t('停车场')" name="PARKING_LOT"></el-tab-pane>
      <el-tab-pane v-if="form.acquisitionType !== 'parking'" :label="$t('路口')" name="INTERSECTION"></el-tab-pane>
      <el-tab-pane v-if="form.acquisitionType !== 'parking'" :label="$t('路线')" name="ROUTE"></el-tab-pane>
      <el-tab-pane :label="$t('POI')" name="POI"></el-tab-pane>
    </el-tabs>
    <div class="step-body parking-lot" v-show="activeRecommend === 'PARKING_LOT'">
      <div class="drawer-body left">
        <div class="title">已选标签</div>
        <el-scrollbar class="selected-container">
          <el-card class="choose-tag">
            <el-collapse v-model="collapseModel">
              <el-collapse-item v-for="item in tagList" :title="item.name" :name="item.id" :key="item.id">
                <tag-list
                  :closeable="false"
                  :choosenable="!formReadonly"
                  :tagList="item.children"
                  :checked-tag-list="checkedTagList"
                ></tag-list>
              </el-collapse-item>
            </el-collapse>
          </el-card>
        </el-scrollbar>
      </div>
      <div class="drawer-body right">
        <div class="title">
          <el-button type="primary" circle @click="add" v-if="!formReadonly">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          </el-button>
        </div>
        <el-scrollbar class="selected-container" v-loading="tagLoading">
          <el-card>
            <div class="parking-recommend-list" v-show="parkingRecommendList?.length">
              <el-card class="parking-recommend-item" v-for="(item, index) in parkingRecommendList" :key="index">
                <el-popconfirm
                  v-if="!formReadonly"
                  width="250"
                  :title="DELETE_CONFIRM_MSG"
                  @confirm="removeRecommend(item, index)"
                >
                  <template #reference>
                    <el-link class="recommend-close" :underline="false" type="info">
                      <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                    </el-link>
                  </template>
                </el-popconfirm>
                <div class="tag-list">
                  <div class="label">标签</div>
                  <div class="list">
                    <el-tag
                      v-for="tag in item.tagList"
                      :key="tag.itemKey"
                      :type="checkTagType(tag)"
                      :effect="'dark'"
                      closeable="false"
                      >{{ tag.itemName }}
                    </el-tag>
                  </div>
                </div>
                <div class="parking-lot-list">
                  <div
                    class="parking-lot-item"
                    v-for="(parkinglot, parkinglotIndex) in item.parkingList"
                    :key="item.itemKey"
                  >
                    <el-select-v2
                      :disabled="!item.editFlag"
                      v-model="parkinglot.itemKey"
                      filterable
                      clearable
                      :options="parkingList"
                      @change="changeParkinglot($event, parkinglot)"
                    />
                    <el-link
                      @click="addParkinglot(parkinglot, item)"
                      :underline="false"
                      v-show="parkinglotIndex === item.parkingList?.length - 1 && item.editFlag"
                      type="primary"
                    >
                      <ltw-icon icon-code="el-icon-circle-plus"></ltw-icon>
                    </el-link>
                    <el-popconfirm
                      width="250"
                      :title="DELETE_CONFIRM_MSG"
                      @confirm="removeParkinglot(item, parkinglotIndex)"
                    >
                      <template #reference>
                        <el-link
                          :underline="false"
                          v-show="parkinglotIndex !== item.parkingList?.length - 1 && item.editFlag"
                          type="danger"
                        >
                          <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                        </el-link>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
                <div class="footer" v-show="item.editFlag">
                  <el-button type="primary" @click="submitParking(item)">保存</el-button>
                </div>
              </el-card>
            </div>
            <el-empty v-show="!parkingRecommendList?.length" description="暂无选择停车场"></el-empty>
          </el-card>
        </el-scrollbar>
      </div>
    </div>
    <div class="step-body route" v-if="activeRecommend === 'INTERSECTION'">
      <map-intersection
        :prop-disabled="formReadonly"
        ref="MapIntersection"
        @reload="updateFile"
        @show-task-record="showTaskRecord"
      ></map-intersection>
    </div>
    <div class="step-body route" v-if="activeRecommend === 'ROUTE'">
      <map-preview-route :prop-disabled="formReadonly" ref="MapPreviewRoute" @reload="updateFile"></map-preview-route>
    </div>
    <div class="step-body route" v-if="activeRecommend === 'POI'">
      <map-poi :prop-disabled="formReadonly" ref="MapPoi" @reload="savePoi"></map-poi>
    </div>
  </div>
</template>
<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import TagList from '@/pages/dataCollect/components/TagList.vue'
// import { listFtmParkingLot } from '@/apis/fleet/ftm-parking-lot'
import { listMdParkingLotsSimple } from '@/apis/fleet/parking-lot-management'
import util, { showToast, showConfirmToast } from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import TXMapIntersection from '@/components/map/TXMapIntersection'
import {
  getIntersectionList,
  daqTaskRecommendsBatch,
  getDaqTaskRecommends,
  daqTaskRecommends,
  deleteDaqTaskRecommends,
  updateDaqTaskRecommends,
  upsertFileTask,
  getRequirementRecommends
} from '@/apis/data-collect/vt-daq-task'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import MapIntersection from '@/pages/dataCollect/components/MapIntersection'
import MapPreviewRoute from '@/pages/dataCollect/components/MapPreviewRoute'
import MapPoi from '@/pages/dataCollect/components/MapPoi'
import { getFilesPreview } from '@/apis/base/file'

export default {
  name: 'TaskStep3',
  emits: ['reload', 'show-task-record'],
  props: {},
  components: {
    'tx-map-intersection': TXMapIntersection,
    LtwIcon,
    TagList,
    MapPreviewRoute,
    MapPoi,
    MapIntersection
  },
  data() {
    return {
      dialogVisible: false,
      form: {},
      tagList: [],
      checkedTagList: [],
      requirementSelection: [],
      tagLoading: false,
      dialogStatus: '',
      activeRecommend: '',
      parkingRecommendList: [],
      intersectionRecommendList: [],
      parkingList: [],
      collapseModel: [],
      DELETE_CONFIRM_MSG: BASE_CONSTANT.DELETE_CONFIRM_MSG,
      cantonCode: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
        // checkStrictly: true
      },
      cantonCodeList: [],
      mapParkingList: {},
      currentMarkerKey: '',
      taskFileId: '',
      fileId: '',
      supplierList: [],
      supplierCode: '',
      intersectionChecked: false,
      baseCheckedIntersectionList: [],
      baseIntersectionRecommendList: [],
      backupCheckedIntersectionList: [],
      intersectionCollected: '',
      intersectionCollectedList: [
        {
          name: '全部',
          code: ''
        },
        {
          name: '已采集',
          code: true
        },
        {
          name: '未采集',
          code: false
        }
      ],
      hdComplete: false
    }
  },
  created() {},
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  watch: {
    checkedTagList: {
      handler(val) {
        const tagList = this.checkedTagList.map(val => {
          return {
            type: val.type,
            itemType: 'TAG',
            itemKey: val.id,
            itemName: val.name,
            mutuallyExclusive: val.mutuallyExclusive
          }
        })
        const index = this.parkingRecommendList.findIndex(val => val.editFlag === true)
        if (~index) {
          this.parkingRecommendList[index].tagList = tagList
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    show(row) {
      this.tagList = JSON.parse(JSON.stringify(row.data.groupList || []))
      this.collapseModel = this.tagList.map(val => val.id)
      this.form = {
        id: row.data.id,
        code: row.data.code,
        acquisitionType: row.data.acquisitionType,
        requirementIdList: row.data.requirementIdList
      }
      if (this.form.acquisitionType === 'driving') {
        this.activeRecommend = 'INTERSECTION'
      } else {
        this.activeRecommend = 'PARKING_LOT'
      }
      // this.handleTabClick(this.activeRecommend)
      // this.listDmRequirementsPublishedSelection()
      this.dialogStatus = row.type
      // if (this.dialogStatus !== 'add') {
      // }
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    // 已选的路口列表
    getDaqTaskRecommends() {
      let postData = {
        taskId: this.form.id,
        taskCode: this.form.code,
        type: this.activeRecommend
      }
      getDaqTaskRecommends(postData).then(res => {
        if (this.activeRecommend === 'PARKING_LOT') {
          this.drawParking(res.data)
        } else if (this.activeRecommend === 'INTERSECTION') {
          this.drawIntersection(res.data)
        } else if (this.activeRecommend === 'POI') {
          // dongliang：只当单需求做
          this.getChoosenPoiList(res.data)
        } else if (this.activeRecommend === 'ROUTE') {
          this.drawRoute(res.data)
        }
      })
    },
    getChoosenPoiList(data, type) {
      const _this = this
      if (data?.length) {
        // 需求带过来的文件不需要传递fileid
        if (type !== 'req') {
          this.fileId = data[0].id
        }
        const promises = data.map(val =>
          getFilesPreview({
            path: val.attachment,
            token: util.getToken()
          }).then(res => _this.readFile(res))
        )
        Promise.all(promises).then(async res => {
          if (res?.length) {
            let objFile = JSON.parse(res[0])
            if (objFile.markersBoardList?.length) {
              this.$refs.MapPoi.show({ type: this.dialogStatus, data: objFile.markersBoardList, fileType: type })
            } else {
              this.getRequirementPoi()
            }
          }
        })
      } else {
        this.getRequirementPoi()
        // this.$refs.MapPoi.show()
      }
    },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = event => {
          // 获取文件的字节数据
          resolve(event.target.result)
        }
        reader.onerror = event => {
          reject(error)
        }
        reader.readAsText(file) // 使用readAsText方法读取文件内容
      })
    },
    getRequirementPoi() {
      const promises = this.form.requirementIdList?.map(val =>
        getRequirementRecommends({
          requirementId: val,
          type: 'POI'
        }).then(res => res)
      )
      Promise.all(promises || []).then(result => {
        const fileList = result.map(val => val.data || []).flat(2)
        if (fileList?.length) {
          this.getChoosenPoiList(fileList, 'req')
        } else {
          this.$refs.MapPoi.show()
        }
      })
    },
    handleTabClick(e) {
      if (e === 'INTERSECTION') {
        this.getDaqTaskRecommends()
      } else if (e === 'PARKING_LOT' && !this.parkingRecommendList?.length) {
        this.getDaqTaskRecommends()
        this.getParkingList()
      } else if (e === 'ROUTE' || e === 'POI') {
        this.getDaqTaskRecommends()
      }
    },
    add() {
      if (this.checkedTagList?.length) {
        const tagList = this.checkedTagList.map(val => {
          return {
            type: val.type,
            mutuallyExclusive: val.mutuallyExclusive,
            itemType: 'TAG',
            itemKey: val.id,
            itemName: val.name
          }
        })
        this.parkingRecommendList.push({
          editFlag: true,
          type: 'PARKING_LOT',
          taskId: this.form.id,
          taskCode: this.form.code,
          tagList,
          parkingList: [
            {
              itemType: 'PARKING_LOT'
            }
          ]
        })
      } else {
        showToast('请先选择标签', 'warning')
      }
    },
    initForm() {
      this.currentMarkerKey = null
      this.activeRecommend = null
      this.checkedTagList = []
      this.parkingRecommendList = []
      this.intersectionRecommendList = []
      this.baseIntersectionRecommendList = []
      this.baseCheckedIntersectionList = []
      this.backupCheckedIntersectionList = []
      this.supplierCode = ''
      this.cantonCode = []
      this.intersectionChecked = false
      this.intersectionCollected = ''
      this.intersectionName = ''
      // this.$refs.TXMapIntersection.destroyMap()
      // this.requirementSelection = []
    },
    dialogClosed() {
      // this.checkedTagList = []
    },
    getParkingList() {
      if (!this.parkingList?.length) {
        listMdParkingLotsSimple().then(res => {
          res.data.forEach(val => {
            val.label = val.name
            val.value = val.id
            this.mapParkingList[val.id] = val
          })
          this.parkingList = res.data
        })
      }
    },
    changeParkinglot(e, item) {
      const parkinglot = this.mapParkingList[e]
      // const parkinglot = this.parkingList.find(val => val.id === e)
      item.itemType = 'PARKING_LOT'
      item.itemKey = parkinglot?.id || null
      item.itemName = parkinglot?.name || null
      item.latitude = parkinglot?.latitude || null
      item.longitude = parkinglot?.longitude || null
    },
    submitParking(item) {
      const parkingList = item.parkingList.filter(val => val.itemKey)
      if (parkingList?.length && item.tagList?.length) {
        let postData = {
          type: item.type,
          taskId: item.taskId,
          taskCode: item.taskCode,
          recommendItems: [...item.parkingList, ...item.tagList]
        }
        daqTaskRecommends(postData).then(res => {
          item.id = res.data.id
          item.editFlag = false
          this.checkedTagList = []
          item.parkingList = parkingList
        })
      } else {
        showToast('请选择标签及停车场', 'warning')
      }
    },
    submitIntersection() {
      const intersectionRecommendList = this.intersectionRecommendList[0]
      if (intersectionRecommendList?.id) {
        updateDaqTaskRecommends(intersectionRecommendList).then(res => {
          showToast('保存成功')
          this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      } else {
        daqTaskRecommends(intersectionRecommendList).then(res => {
          this.intersectionRecommendList[0].id = res.data.id
          showToast('保存成功')
          this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      }
    },
    clearIntersection() {
      this.intersectionChecked = false
      // this.backupCheckedIntersectionList = []
      this.intersectionRecommendList[0].recommendItems = []
      this.baseCheckedIntersectionList = []
      // this.baseIntersectionRecommendList = []

      this.changeCheckAll(false)
    },
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    addParkinglot(parkinglot, item) {
      if (parkinglot.itemKey) {
        item.parkingList.push({
          itemType: 'PARKING_LOT'
        })
      } else {
        showToast('请选择停车场', 'warning')
      }
    },
    removeParkinglot(item, index) {
      // showConfirmToast({
      //   message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      // }).then(res => {
      // console.log(item.parkingList)
      item.parkingList.splice(index, 1)
      // console.log(item.parkingList)
      // })
    },
    removeRecommend(item, index) {
      // showConfirmToast({
      //   message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      // }).then(res => {
      let postData = {
        id: item.id
      }
      deleteDaqTaskRecommends(postData).then(res => {
        this.parkingRecommendList.splice(index, 1)
        // this.checkedTagList = []
      })
      // })
    },
    clickMarker(obj) {
      this.intersectionChecked = false
      let intersectionIndex = -1
      if (this.intersectionRecommendList?.length) {
        this.intersectionRecommendList[0].recommendItems = this.intersectionRecommendList[0].recommendItems || []
        intersectionIndex = this.intersectionRecommendList[0].recommendItems?.findIndex(val => {
          return val.itemKey === obj.itemKey
        })
      } else {
        this.intersectionRecommendList.push({
          type: 'INTERSECTION',
          taskId: this.form.id,
          taskCode: this.form.code,
          recommendItems: []
        })
      }
      if (!~intersectionIndex) {
        this.currentMarkerKey = obj.itemKey
        this.intersectionRecommendList[0].recommendItems.unshift(obj)
      } else {
        this.currentMarkerKey = null
        this.intersectionRecommendList[0].recommendItems.splice(intersectionIndex, 1)
      }
    },
    showTaskRecord(obj) {
      const postData = {
        type: this.dialogStatus,
        data: {
          locationId: obj.itemKey,
          type: obj.itemType,
          locationName: obj.itemName
        }
      }
      this.$emit('show-task-record', postData)
    },
    getMapCenter(item) {
      this.currentMarkerKey = item.itemKey
      this.$refs.TXMapIntersection.goCenter(item)
    },
    updateFile(data) {
      const postData = {
        id: this.taskFileId,
        taskId: this.form.id,
        type: 'ROUTE',
        taskCode: this.form.code
      }
      const blob = new Blob([JSON.stringify(data)], { type: 'application/json' }) // 使用 FormData 组合文件和其他参数
      const formData = new FormData()
      formData.append('file', blob)
      formData.append('param', JSON.stringify(postData))
      upsertFileTask(formData).then(res => {
        showToast(this.$t('保存成功'))
      })
    },

    listSupplier() {
      if (!this.supplierList?.length) {
        listSysRoleOrg({ tagCodeList: 'org_supplier' }).then(res => {
          this.supplierList = res.data
        })
      }
    },
    changeCheckAll(e) {
      this.intersectionRecommendList = e ? JSON.parse(JSON.stringify(this.baseIntersectionRecommendList)) : []
      const list = (
        this.backupCheckedIntersectionList?.length
          ? this.backupCheckedIntersectionList
          : this.baseCheckedIntersectionList
      ).map(val => {
        if (this.backupCheckedIntersectionList?.length && e) {
          // this.clickMarker(val)
          this.clickMarkerNoClear(val)
        }
        val.checked = e
        return val
      })
      let postData = {
        list,
        type: 'area',
        formReadonly: this.formReadonly
      }
      this.$refs.TXMapIntersection.show(postData)
    },
    clickMarkerNoClear(obj) {
      let intersectionIndex = -1
      if (this.intersectionRecommendList?.length) {
        this.intersectionRecommendList[0].recommendItems = this.intersectionRecommendList[0].recommendItems || []
        intersectionIndex = this.intersectionRecommendList[0].recommendItems?.findIndex(val => {
          return val.itemKey === obj.itemKey
        })
      } else {
        this.intersectionRecommendList.push({
          type: 'INTERSECTION',
          taskId: this.form.id,
          taskCode: this.form.code,
          recommendItems: []
        })
      }
      if (!~intersectionIndex) {
        this.currentMarkerKey = obj.itemKey
        this.intersectionRecommendList[0].recommendItems.unshift(obj)
      } else {
        this.currentMarkerKey = null
        // this.intersectionRecommendList[0].recommendItems.splice(intersectionIndex, 1)
      }
    },
    savePoi(obj) {
      const postData = {
        id: this.fileId,
        taskId: this.form.id,
        type: 'POI',
        taskCode: this.form.code,
        recommendItems: obj.markersBoardList
      }
      const blob = new Blob([JSON.stringify(obj)], { type: 'application/json' }) // 使用 FormData 组合文件和其他参数
      const formData = new FormData()
      formData.append('file', blob)
      formData.append('param', JSON.stringify(postData))
      upsertFileTask(formData).then(res => {
        showToast(this.$t('保存成功'))
      })
    },
    drawParking(data) {
      this.parkingRecommendList = data.map(val => {
        let tagList = [],
          parkingList = []
        val?.recommendItems?.forEach(recommend => {
          if (recommend.itemType === 'PARKING_LOT') {
            parkingList.push(recommend)
          } else if (recommend.itemType === 'TAG') {
            tagList.push(recommend)
          }
        })
        return {
          ...val,
          tagList,
          parkingList
        }
      })
    },
    drawIntersection(data) {
      this.$refs.MapIntersection.show({
        type: this.dialogStatus,
        data: {
          recommendItems: data[0]?.recommendItems,
          id: data[0]?.id,
          type: 'INTERSECTION',
          taskId: this.form.id,
          taskCode: this.form.code
        }
      })
    },
    drawRoute(data) {
      // const promises = this.form.requirementIdList?.map(val =>
      //   getRequirementRecommends({
      //     requirementId: val,
      //     type: 'ROUTE'
      //   }).then(res => res)
      // )
      // Promise.all(promises || []).then(result => {
      //   const routes = result.map(val => val.data || []).flat(2)
      this.$refs.MapPreviewRoute.show({
        type: this.dialogStatus,
        data: {
          // routes: routes,
          checkedRoutes: data,
          requirementIdList: this.form.requirementIdList
        }
        // address: 'minio://arena-platform/requirement_recommend_attachment/21654469c31d4a45abf75e829f2a5860.'
      })
      if (data?.length) {
        const taskFile = data.find(val => val.type === 'ROUTE')
        this.taskFileId = taskFile?.id
      }
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.step-recommend {
  //display: flex;
  width: 100%;
  height: 100%;

  .step-body {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;

    .footer {
      display: flex;
      justify-content: end;
      align-items: center;

      .intersection-num {
        margin-right: 10px;
      }
    }
  }

  .drawer-body {
    &:nth-child(odd) {
      margin-right: 10px;
    }

    width: 50%;
    height: 100%;

    .title {
      height: 32px;
      line-height: 32px;
      padding-left: 10px;
      margin-bottom: 10px;

      .el-button.hidden {
        opacity: 0;
      }
    }

    .choose-tag > :deep(.el-card__body) {
      padding-top: 0;
      padding-bottom: 0;
    }

    .selected-container {
      height: calc(100% - 42px);

      .parking-recommend-list {
        .parking-recommend-item {
          position: relative;
          margin-bottom: 10px;
          overflow: visible;

          &:hover {
            .recommend-close {
              opacity: 1;
            }
          }

          .recommend-close {
            opacity: 0;
            position: absolute;
            right: -11px;
            top: -11px;
            font-size: 22px;
            background: #fff;
            transition: all 0.3s;
          }

          .tag-list {
            display: flex;

            .label {
              white-space: nowrap;
              margin-right: 10px;
            }

            .list {
              .el-tag {
                margin: 0 5px 5px 0;
              }
            }
          }

          .parking-lot-list {
            .parking-lot-item {
              display: flex;
              margin-bottom: 10px;

              .el-link {
                font-size: 24px;
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }

  .intersection {
    .map-content {
      position: relative;
      height: 100%;
      width: calc(100% - 250px);

      .query-param {
        width: 100%;
        background: #fff;
        display: flex;
        z-index: 2;
        position: absolute;
        left: 0;
        top: 0;

        //.intersection-check {
        //  margin-left: 6px;
        //}

        .intersection-name {
          width: 250px;
        }

        //.intersection-select.is-bordered.is-checked {
        //  border-color: #67c23a;
        //
        //  :deep(.el-checkbox__input) {
        //    .el-checkbox__inner {
        //      border-color: #67c23a;
        //      background-color: #67c23a;
        //    }
        //
        //    & + .el-checkbox__label {
        //      color: #67c23a;
        //    }
        //  }
        //}
      }
    }

    .choosed-intersection {
      width: 250px;
      margin-left: 10px;

      :deep(.el-card__body) {
        height: 100%;
      }

      .body {
        height: calc(100% - 32px);
      }

      .intersection-item {
        cursor: pointer;
        margin-bottom: 10px;

        &.active {
          border-color: #fab6b6;
          background-color: #fef0f0;
          color: #f56c6c;
        }
      }
    }
  }
}

//.req-drawer-container {
//.el-drawer__header {
//  margin-bottom: 0;
//}
//
//.el-drawer__body {
//  height: calc(100% - 125px);
//  padding-right: 10px;
//  display: flex;
//  flex-direction: row;
//
//  .drawer-body {
//    height: 100%;
//    //overflow-y: auto;
//  }
//
//  .left {
//    width: 500px;
//  }
//
//  .title {
//    margin-bottom: 10px;
//  }
//
//  .requirement-list {
//    .item {
//      margin-right: 10px;
//      margin-bottom: 10px;
//      cursor: pointer;
//    }
//  }
//
//  .right {
//    width: calc(100% - 500px);
//
//    .selected-container {
//      height: calc(100% - 31px);
//      //padding-top: 15px;
//    }
//  }
//}
//}
</style>
