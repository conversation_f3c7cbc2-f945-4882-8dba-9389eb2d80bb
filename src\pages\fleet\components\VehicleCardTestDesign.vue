<template>
  <div class="vehicle-card-test-design" :class="{ 'selected': selected }" @click="handleCardClick">
    <!-- 主容器 -->
    <div class="card-container">
      <!-- 内容区域 -->
      <div class="content-wrapper">
        
        <!-- 图片区域 -->
        <div class="image-section">
          <div class="image-container">
            <!-- 信息待完善标签 -->
            <div v-if="!vehicle.withinValidityPeriod" class="info-incomplete-tag">
              <div class="tag-left-border"></div>
              <div class="tag-background"></div>
              <div class="tag-text">信息待完善</div>
            </div>

            <div class="image-background">
              <div class="image-inner">
                <div class="image-placeholder">
                  <el-image
                    :src="vehicleImage"
                    fit="cover"
                    class="vehicle-image"
                  >
                    <template #error>
                      <div class="placeholder-content">
                        <ltw-icon icon-code="el-icon-picture" class="placeholder-icon"></ltw-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 信息区域 -->
        <div class="info-section">
          <div class="info-content">
            
            <!-- 主要信息区域 -->
            <div class="main-info">
              <div class="title-section">
                <!-- 编号信息行 -->
                <div class="number-row">
                  <div class="number-group">
                    <div class="bosch-number">
                      <el-tooltip :content="vehicle.vin" placement="top" effect="dark">
                        <div class="number-value">{{ vehicle.vin || '-' }}</div>
                      </el-tooltip>
                      <div class="number-label">博世编号</div>
                    </div>
                    <div class="external-number" v-if="vehicle.externalVin">
                      <div v-if="vehicle.externalVin" class="number-divider"></div>
                      <el-tooltip :content="vehicle.externalVin " placement="top" effect="dark">
                        <div class="number-value">{{ vehicle.externalVin  }}</div>
                      </el-tooltip>
                      <div class="number-label">外部编号</div>
                    </div>
                  </div>
                </div>

                <!-- 标签行 -->
                <div class="tags-row">
                  <div class="tags-group">
                    <el-tag type="primary" size="small" class="function-tag" v-if="vehicle.useTypeName">{{vehicle.useTypeName }}</el-tag>
                    <el-tag 
                      :type="getStatusTagType(vehicle.status)" 
                      size="small" 
                      class="status-tag"
                    >
                      {{ vehicle.status }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="detail-info">
              <div class="detail-section">
                <!-- 车型信息 -->
                <div class="vehicle-info">
                  <div class="vehicle-model" v-if="vehicle.variantCode">{{ vehicle.variantCode || '-'  }}</div>
                  <div class="vehicle-model" v-else>暂无装车记录</div>
                </div>
                
                <!-- 属性信息行 -->
                <div class="attributes-row">
                  <el-tooltip v-if="vehicle.supplierName" :content="vehicle.supplierName" placement="top" effect="dark">
                    <div class="attribute-item">{{ vehicle.supplierName }}</div>
                  </el-tooltip>
                  <!-- 分割线：供应商和型号之间 -->
                  <div v-if="vehicle.supplierName && vehicle.variantModelName" class="attribute-divider"></div>
                  <el-tooltip v-if="vehicle.variantModelName" :content="vehicle.variantModelName" placement="top" effect="dark">
                    <div class="attribute-item">{{ vehicle.variantModelName }}</div>
                  </el-tooltip>
                  <!-- 分割线：型号和能源方式之间 -->
                  <div v-if="vehicle.variantModelName && vehicle.energyTypeName" class="attribute-divider"></div>
                  <el-tooltip v-if="vehicle.energyTypeName" :content="vehicle.energyTypeName" placement="top" effect="dark">
                    <div class="attribute-item">{{ vehicle.energyTypeName }}</div>
                  </el-tooltip>
                  <!-- 分割线：能源方式和保管人之间 -->
                  <div v-if="(vehicle.energyTypeName||vehicle.variantModelName) && vehicle.keeperEmpName " class="attribute-divider"></div>
                  <el-tooltip :content="vehicle.keeperEmpName" placement="top" effect="dark">
                    <div class="attribute-item-name">{{ vehicle.keeperEmpName }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- 菜单按钮 -->
      <!-- <div class="menu-button" @click.stop="handleMenuClick">
        <div class="menu-dots">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div> -->

        <!-- 菜单按钮 -->
        <el-dropdown @command="handleCommand" class="menu-dropdown" @click.stop>
          <el-button class="menu-button" text>
            <ltw-icon icon-code="el-icon-more"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="pageType === 'map'" :command="commandObj('position', vehicle)">
                <ltw-icon icon-code="el-icon-location"></ltw-icon>
                {{ $t('定位') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType === 'map'" :command="commandObj('trajectory', vehicle)">
                <ltw-icon icon-code="el-icon-guide"></ltw-icon>
                {{ $t('轨迹') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('remove', vehicle)">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                {{ $t('删除') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('calibration', vehicle)">
                <ltw-icon icon-code="el-icon-aim"></ltw-icon>
                {{ $t('标定参数') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('competitors', vehicle)">
                <ltw-icon icon-code="el-icon-aim"></ltw-icon>
                {{ $t('对手件') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="pageType !== 'map'" :command="commandObj('fleetInsurance', vehicle)">
                <ltw-icon icon-code="el-icon-document"></ltw-icon>
                {{ $t('保险') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

    </div>
  </div>

  <!-- 标定参数配置组件 -->
  <CalibrationConfig ref="CalibrationConfig" @reload="reloadForm" />

  <!-- 保险管理组件 -->
  <OptFleetInsurance ref="OptFleetInsurance" @reload="reloadForm" />
</template>

<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'
import { showToast, showConfirmToast } from '@/plugins/util'
import { getBsVehicle, deleteBsVehicle } from '@/apis/fleet/bs-vehicle'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import CalibrationConfig from '@/pages/fleet/dialog/CalibrationConfig.vue'
import OptFleetInsurance from '@/pages/fleet/dialog/OptFleetInsurance.vue'

export default {
  name: 'VehicleCardTestDesign',
  components: {
    LtwIcon,
    CalibrationConfig,
    OptFleetInsurance
  },
  props: {
    vehicle: {
      type: Object,
      required: true,
      default: () => ({})
    },
    selected: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: 'list' // 'list' 或 'map'
    }
  },
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken()
    }
  },
  emits: ['detail', 'menu-click', 'reload', 'vehicle-position'],
  computed: {
    vehicleImage() {
      if (this.vehicle.photoList?.length) {
        return GLB_CONFIG.devUrl.fileServer + '/' + this.vehicle.photoList[0].id + '?token=' + util.getToken()
      }
      // 将variantCode转为小写进行包含匹配
      const variantCode = (this.vehicle.variantCode || '').toLowerCase()
      const variantModelName = (this.vehicle.variantModelName || '').toLowerCase()

      if (variantCode.includes('e0y')) {
        return require('@/assets/images/E0Y.png')
      } else if (variantCode.includes('e03')) {
        return require('@/assets/images/E03.png')
      } else if (variantCode.includes('honda')) {
        return require('@/assets/images/Honda.png')
      } else if (variantCode.includes('jetour')||variantModelName.includes('jetour')) {
        return require('@/assets/images/Jetour.png')
      } else if (variantCode.includes('zeekr')) {
        return require('@/assets/images/zeekr.png')
      } else if (variantCode.includes('lync')) {
        return require('@/assets/images/lyncco.png')
      } else if(variantCode.includes('dongfeng')||variantCode.includes('df')){
        return require('@/assets/images/dongfeng.png')
      } else if(variantCode.includes('b41')){
        return require('@/assets/images/beiqi.png')
      }
      // 如果没有匹配到，返回默认图片
      return require('@/assets/images/sedan.png')
    }
  },
  methods: {
    handleCardClick() {
      this.$emit('detail', this.vehicle)
    },

    handleMenuClick() {
      this.$emit('menu-click', this.vehicle)
    },

    getStatusTagType(status) {
      switch (status) {
        case 'working':
          return 'danger'
        case 'free':
          return 'success'
        case 'distributed':
          return 'warning'
        default:
          return 'info'
      }
    },

    handleCommand(obj) {
      if (obj.type === 'remove') {
        this.deleteVehicleDetail(obj.row.id)
      } else if (obj.type === 'view') {
        this.viewVehicleDetail(obj.row.id)
      } else if (obj.type === 'edit') {
        this.editVehicleDetail(obj.row.id)
      } else if (obj.type === 'calibration') {
        this.getCalibrationConfig(obj.row)
      } else if (obj.type === 'competitors') {
        this.editVehicleCompetitor(obj.row.id)
      } else if (obj.type === 'fleetInsurance') {
        this.getOptFleetInsurance(obj.row)
      } else if (obj.type === 'position' || obj.type === 'trajectory') {
        this.getVehiclePosition(obj)
      }
    },

    commandObj(type, row) {
      return {
        type,
        row
      }
    },

    // 查看车辆详情
    viewVehicleDetail(id) {
      this.$router.push({
        path: '/fleet/ViewFleetManagement',
        query: {
          id
        }
      })
    },

    // 编辑车辆
    editVehicleDetail(id) {
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          sourceType: 'installRecord'
        }
      })
    },

    // 编辑对手件
    editVehicleCompetitor(id) {
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          sourceType: 'competitor'
        }
      })
    },

    // 删除车辆
    deleteVehicleDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteBsVehicle({id}).then(() => {
          showToast('删除成功')
          this.$emit('reload')
        })
      })
    },

    // 标定参数配置
    getCalibrationConfig(row) {
      this.$refs.CalibrationConfig.show({
        type: 'view',
        vin: row.vin
      })
    },

    // 保险管理
    getOptFleetInsurance(row) {
      this.$refs.OptFleetInsurance.show({
        type: 'view',
        vin: row.vin
      })
    },

    // 重新加载表单数据
    reloadForm() {
      this.$emit('reload')
    },

    // 车辆定位和轨迹
    getVehiclePosition(obj) {
      this.$emit('vehicle-position', obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.vehicle-card-test-design {
 // min-width: 270px;
  width: 100%;
  padding-bottom: 16px;
  position: relative;
  background: white;
  
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);
  border-radius: 2px;
  outline: 1px #DCDFE6 solid;
  outline-offset: -1px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);
  }

  &.selected {
    outline: 1px #5755FF solid;
  
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);

    &:hover {
      
/* Shadow */
box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.10);
    }
  }

  .card-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  // 图片区域
  .image-section {
    padding: 1px;
    background: linear-gradient(0deg, transparent 0%, transparent 100%);
    overflow: hidden;

    .image-container {
      height: 188.86px;
      background: #FAFAFA;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative; // 为绝对定位的标签提供定位上下文
      @media (min-width: 900px) and (max-width: 1600px) {
        height: 167.86px;
      }

      .image-background {
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, #EBEEF5 0%, #EBEEF5 100%);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .image-inner {
          width: 100%;
          height: 100%;
          background: #FAFAFA;
          display: flex;
          flex-direction: column;

          .image-placeholder {
            width: 100%;
            height: 100%;
            background: #E7ECF4;
            display: flex;
            align-items: center;
            justify-content: center;

            .vehicle-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .placeholder-content {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;

              .placeholder-icon {
                font-size: 48px;
                color: #C0C4CC;
              }
            }
          }
        }
      }

      // 信息待完善标签样式
      .info-incomplete-tag {
        width: 74px;
        height: 23.60px;
        left: 0px;
        top: 5px;
        position: absolute;
        z-index: 10;

        .tag-left-border {
          width: 4px;
          height: 19.60px;
          left: 0px;
          top: 4px;
          position: absolute;
          background: #E47A01;
        }

        .tag-background {
          width: 74px;
          height: 20px;
          left: 0px;
          top: 0px;
          position: absolute;
          background: #FFB03A;
          border-top-left-radius: 1px;
          border-top-right-radius: 1px;
          border-bottom-right-radius: 1px;
        }

        .tag-text {
          width: 66px;
          height: 16px;
          left: 4px;
          top: 2px;
          position: absolute;
          text-align: center;
          justify-content: center;
          display: flex;
          flex-direction: column;
          color: white;
          font-size: 12px;
          font-family: 'Bosch Sans Global', sans-serif;
          font-weight: 400;
          word-wrap: break-word;
        }
      }
    }
  }

  // 信息区域
  .info-section {
    padding: 6px 16px;
    @media (min-width: 900px) and (max-width: 1600px) {
      padding: 0 16px;
    }


    .info-content {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .main-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .title-section {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .number-row {
            display: flex;
            align-items: center;
            width: 100%;
            height: 16px;

            .number-group {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;
              width: 100%;

              .number-divider {
                width: 1px;
                height: 16px;
                background-color: #E5E7EB;
                margin: 0 4px;
                flex-shrink: 0;
              }

              .bosch-number {
                max-width: 50%;
                display: flex;
                align-items: center;
                gap: 4px;
                min-width: 0; // 允许flex子项收缩
              }

              .external-number {
                max-width: 50%;
                display: flex;
                align-items: center;
                gap: 4px;
                min-width: 0; // 允许flex子项收缩
              }
              .number-value {
                  color: #5755FF;
                  font-size: 14px;
                  font-family: 'Bosch Sans Global', sans-serif;
                  font-weight: 600;
                  line-height: 14px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  cursor: pointer;
                  max-width: calc(100% - 40px);
                }
                .number-label {
                  flex: 0 0 40px; // 固定40px宽度
                  color: #B2B9C0;
                  font-size: 10px;
                  font-family: 'Bosch Sans Global', sans-serif;
                  font-weight: 400;
                  line-height: 10px;
                  white-space: nowrap;
                }
            }
          }

          .tags-row {
            display: flex;
            align-items: center;

            .tags-group {
              display: flex;
              align-items: flex-start;
              gap: 8px;

              .function-tag {
                background: #F5F5FF;
                border: 1px solid #DDDDFF;
                color: #5755FF;
              }

              .status-tag {
                &.el-tag--danger {
                  border-radius: 2px;
                  border: 1px solid var(--bosch-warning-colors-bosch-warning-colors-bosch-red-90-warning, #FFD9D9);
                  background: var(--bosch-warning-colors-bosch-warning-colors-bosch-red-95-warning, #FFECEC);
                  color: #FF6E6F;
                }

                &.el-tag--warning {
                  border-radius: 2px;
                  border: 1px solid var(--bosch-warning-colors-bosch-warning-colors-bosch-yellow-90-warning, #FFDF95);
                  background: var(--bosch-warning-colors-bosch-warning-colors-bosch-yellow-95-warning, #FFEFD1);
                  color: #E6A23C;
                }

                &.el-tag--success {
                  border-radius: 2px;
                  border: 1px solid var(--bosch-gradations-green-bosch-gradations-green-bosch-green-85, #9BE4B3);
                  background: var(--bosch-gradations-green-bosch-gradations-green-bosch-green-95, #E2F5E7);
                  color: #67C23A;
                }
              }
            }
          }
        }
      }

      .detail-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .detail-section {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .vehicle-info {
            .vehicle-model {
              color: #8A9097;
              font-size: 10px;
              font-family: 'Bosch Sans Global', sans-serif;
              font-weight: 400;
              line-height: 10px;
            }
          }

          .attributes-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right:30px;

            .attribute-item {
              max-width: 25%; 
              color: #8A9097;
              font-size: 10px;
              font-family: 'Bosch Sans Global', sans-serif;
              font-weight: 400;
              line-height: 12px;
              white-space: nowrap; // 不换行
              overflow: hidden; // 隐藏溢出
              text-overflow: ellipsis; // 显示省略号
              min-width: 0; // 允许flex项目缩小到内容以下
            }
            .attribute-item-name{
               flex:1;
              color: #8A9097;
              font-size: 10px;
              font-family: 'Bosch Sans Global', sans-serif;
              font-weight: 400;
              line-height: 12px;
              white-space: nowrap; // 不换行
              overflow: hidden; // 隐藏溢出
              text-overflow: ellipsis; // 显示省略号
              min-width: 0; // 允许flex项目缩小到内容以下
            }

            .attribute-divider {
              width: 1px;
              height: 8px;
              background-color: #DCDFE6;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }

  // 菜单按钮
  .menu-button {
    position: absolute;
    right: 16px;
    bottom: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 10;

    .menu-dots {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;

      .dot {
        width: 3.33px;
        height: 3.33px;
        background: #B2B9C0;
        border-radius: 50%;
        transition: background 0.3s ease;
      }
    }

    &:hover .dot {
      background: #5755FF;
    }
  }
}

// 菜单按钮样式
.menu-dropdown {
  position: absolute;
  right: 0px;
  bottom: 0px;

  .menu-button {
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    border: none;
    background: transparent;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    :deep(.ltw-icon) {
      font-size: 14px;
      color: #606266;
    }
  }
}

// 响应式设计 - 移除缩放效果，保持原始尺寸
@media (min-width: 900px) and (max-width: 1600px) {
  .vehicle-card-test-design {
    // 移除 transform: scale(0.8) 缩放效果
  //  min-width: 249px;
    width: 100%;
  }
}

@media (max-width: 900px) {
  .vehicle-card-test-design {
    // 移除 transform: scale(0.8) 缩放效果
   // min-width: 249px;
    width: 100%;
  }
}

</style>
