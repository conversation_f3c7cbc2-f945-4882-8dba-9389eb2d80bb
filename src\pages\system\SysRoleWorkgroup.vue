<template>
    <div>
        <div class="main-container">
            <div class="org-tree-container">
                <org-tree @org-click="handleOrgClick" ref="orgTree"
                          :default-selected-org="currentUser.currentTenantId"></org-tree>
            </div>
            <div class="content-container">
                <el-card>
                    <div class="ltw-toolbar">
                        <div class="ltw-search-container ltw-tool-container" >
                            <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh">
                                <template #append>
                                                                <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                                </template>
                            </ltw-input>
                        </div>
                        <div class="ltw-tool-container button-group">
                            <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>

                        {{ item.name }}
                    </el-button>                            <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                         v-if="batchingFunctionList && batchingFunctionList.length>0">
                                <el-button type="primary">
                                    批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                            {{item.name}}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                    <el-table :data="pageData.records" 
                              @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                        <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                        <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                        <el-table-column header-align="left" align="left" prop="name" label="名称"></el-table-column>
                        <el-table-column header-align="left" align="left" prop="tenantName" label="组织结构"></el-table-column>
                        <el-table-column header-align="left" align="left" prop="managerEmpName" label="负责人"></el-table-column>
                        <!--                        <el-table-column header-align="left" align="left" prop="sortNum" label="顺序"></el-table-column>-->
                        <!--                        <el-table-column header-align="left" align="left" prop="duty" label="职能"></el-table-column>-->
                        <el-table-column header-align="left" align="left" label="操作" width="240">
                            <template #default="scope">
                                <el-button-group>
                                    <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name" placement="top" :enterable="false"
                                                >
                                <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                </el-button>
                                    </el-tooltip>
                                </el-button-group>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                            background
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="queryParam.current"
                            :page-sizes="[5, 10, 20, 30]"
                            :page-size="queryParam.size"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="pageData.total">
                    </el-pagination>
                </el-card>
            </div>
        </div>
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="名称" prop="name">
                    <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="编码" prop="code">
                    <ltw-input v-model="formData.code" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="职能" prop="duty">
                    <ltw-input v-model="formData.duty" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="组织结构" prop="tenantName">
                    <el-tag type="success" v-if="dialogStatus==='view'">
                        {{formData.tenantName}}
                    </el-tag>
                    <org-selection v-model="formData.tenantId" v-else @change="handleOrgChange"></org-selection>
                </el-form-item>
                <el-form-item label="负责人" prop="managerEmpId">
                    <el-tag type="success" v-if="dialogStatus==='view'">
                        {{formData.managerEmpName}}
                    </el-tag>
                    <employee-selection v-model="formData.managerEmpId" :query-param="empQueryParam"
                                        ref="employeeSelectionRef"
                                        v-else></employee-selection>
                </el-form-item>
                <el-form-item label="顺序" prop="sortNum">
                    <ltw-input v-model="formData.sortNum" :disabled="formReadonly"></ltw-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>

        <privilege-assignment v-model="privilegeAssignmentVisible" :role-id="currentSelectedRoleId"
                              :role-type="currentRoleType"></privilege-assignment>
        <member-mng v-model="empMngVisible" :member-mng-query-param="empMngQueryParam"
                    :member-add-query-param="empAddQueryParam" @close="handleMemberMngClose"
                    @add="handleAddMember" ref="memberMngRef" @singleRemove="handleEmpSingleRemove"
                    @batchRemove="handleEmpBatchRemove"
        ></member-mng>
    </div>
</template>

<script>
    import {
        saveSysRoleWorkgroup,
        updateSysRoleWorkgroup,
        deleteSysRoleWorkgroup,
        pageSysRoleWorkgroup,
        getSysRoleWorkgroup,
        addEmployee,
        deleteEmployee
    } from '@/apis/system/sys-role-workgroup'
    import OrgSelection from "@/components/system/OrgSelection";
    import EmployeeSelection from "@/components/system/EmployeeSelection";
    import OrgTree from "@/components/system/OrgTree"
    import PrivilegeAssignment from "@/components/system/PrivilegeAssignment";
    import ROLE_TYPE from "@/plugins/constants/role-type"
    import MemberMng from "@/components/system/MemberMng";

    

const defaultFormData = {
        sortNum: 1
    }
    export default {
        name: "SysRoleWorkgroup",
        components: {MemberMng, PrivilegeAssignment, EmployeeSelection, OrgSelection, OrgTree},
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10
                },
                empQueryParam: {},
                dialogVisible: false,
                formData: Object.assign({}, defaultFormData),
                formRules: {
                    code: [
                        {required: true, message: '请输入编码', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '请输入名称', trigger: 'blur'}
                    ],
                    managerEmpId: [
                        {required: true, message: '请选择负责人', trigger: 'blur'}
                    ],
                    tenantId: [
                        {required: true, message: '请选择所属机构', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData: [],
                privilegeAssignmentVisible: false,
                currentSelectedRoleId: '',
                currentRoleType: ROLE_TYPE.WORKGROUP,
                empMngVisible: false,
                empMngQueryParam: {},
                empAddQueryParam: {},
                currentUser:{}
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
            }
            this.currentUser = this.$store.state.permission.currentUser
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(funcName,row){
                this[funcName](row)
            },
            refresh() {
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysRoleWorkgroup(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加角色单元_工作组'
                this.dialogStatus = 'add'
                this.dialogVisible = true
                if (this.pageData.total) {
                    let lastSorNum = this.pageData.total;
                    if (lastSorNum) {
                        this.formData.sortNum = lastSorNum + 1
                    }
                }
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveSysRoleWorkgroup(this.formData).then(
                                () => {
                                    this.dialogVisible = false
                                    this.query()
                                }
                            )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateSysRoleWorkgroup(this.formData).then(
                                () => {
                                    this.dialogVisible = false
                                    this.query()
                                }
                            )
                        }
                    }
                )
            },
            edit(row) {
                this.dialogTitle = '修改角色单元_工作组'
                this.dialogStatus = 'edit'
                getSysRoleWorkgroup(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                            this.empQueryParam.orgId = this.formData.tenantId
                        })
                        this.dialogVisible = true
                    }
                )
            },
            view(row) {
                this.dialogTitle = '查看角色单元_工作组'
                this.dialogStatus = 'view'
                getSysRoleWorkgroup(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                    }
                )
            },
            handleCommand(command) {
                if (this.selectedData.length === 0) {
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if (command === 'batchRemove') {
                    this.batchRemove()
                }
            },
            singleRemove(row) {
                this.remove({id:row.id})
            },
            batchRemove() {
                let idList = [];
                this.selectedData.forEach(ele => {
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param) {
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysRoleWorkgroup(param).then(
                        () => {
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    })
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened() {

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value) {
                this.selectedData = value
            },
            initForm() {
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({}, defaultFormData)
            },
            handleOrgChange(orgId) {
                if (orgId) {
                    this.empQueryParam.orgId = orgId
                    this.$refs.employeeSelectionRef.reload()
                } else {
                    this.$refs.employeeSelectionRef.clear()
                }
            },
            handleOrgClick(org) {
                this.queryParam.orgId = undefined
                this.queryParam.orgCodeLink = undefined
                if (org.asLeaf) {
                    this.queryParam.orgId = org.id
                } else {
                    this.queryParam.orgCodeLink = org.codeLink
                }
                this.query()
            },
            privilegeAssign(row) {
                this.currentSelectedRoleId = row.id
                this.privilegeAssignmentVisible = true
            },
            empMng(row) {
                this.currentSelectedRoleId = row.id
                this.empMngQueryParam.workgroupId = row.id
                this.empAddQueryParam.orgId = this.queryParam.orgId
                this.empAddQueryParam.orgCodeLink = this.queryParam.orgCodeLink
                this.empAddQueryParam.workgroupId = row.id
                this.empMngVisible = true
            },
            handleAddMember(value) {
                if (value && value.length > 0) {
                    let data = {}
                    data.parentRoleId = this.currentSelectedRoleId
                    data.roleIdList = []
                    value.forEach(
                        row => {
                            data.roleIdList.push(row.id)
                        }
                    )
                    addEmployee(data).then(
                        () => {
                            this.$refs.memberMngRef.reload(true)
                        }
                    )
                }
            },
            handleEmpSingleRemove(row) {
                deleteEmployee({roleId: row.id, parentRoleId: this.currentSelectedRoleId}).then(
                    () => {
                        this.$refs.memberMngRef.reload(false)
                    }
                )
            },
            handleEmpBatchRemove(rowList) {
                let idList = [];
                rowList.forEach(ele => {
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                deleteEmployee({roleIds: ids, parentRoleId: this.currentSelectedRoleId}).then(
                    () => {
                        this.$refs.memberMngRef.reload(false)
                    }
                )
            },
            handleMemberMngClose() {

            }
        }
    }
</script>

<style scoped lang="scss">

    .main-container {
        display: flex;
        flex-direction: row;

        .org-tree-container {
            width: 280px;
            margin-right: 20px;
        }

        .content-container {
            width: calc(100% - 300px);
        }
    }
</style>
