<template>
  <div>
    <el-upload
      :drag="drag"
      :class="{ 'hide-upload-btn': hideUploadBtn }"
      ref="uploadRef"
      :disabled="disabled"
      :headers="headers"
      :action="uploadServer"
      :file-list="fileList"
      :list-type="listType"
      :show-file-list="showFileList"
      :before-upload="beforeAvatarUpload"
      :limit="limit"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :data="postData"
      :multiple="false"
      :auto-upload="true"
      :http-request="handleUploadForm"
      :accept="accept"
    >
      <ltw-icon v-if="listType === 'picture-card'" icon-code="el-icon-plus"></ltw-icon>
      <template #file="{ file }" v-if="listType === 'picture-card'">
        <!-- <img
						  class="el-upload-list__item-thumbnail"
						  :src="getFileSrc(file)"
						  alt=""
						/> -->
        <el-image class="el-upload-list__item-thumbnail" :src="getFileSrc(file)" fit="cover">
          <template #error>
            <div class="image-slot">
              <ltw-icon icon-code="el-icon-picture"></ltw-icon>
            </div>
          </template>
        </el-image>
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <ltw-icon icon-code="el-icon-zoom-in"></ltw-icon>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
            <ltw-icon icon-code="el-icon-download"></ltw-icon>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="deleteFile(file)">
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
          </span>
          <span class="fileName">{{ file.name }}</span>
        </span>
      </template>
      <el-button v-if="listType === 'text' || listType === 'picture'" :disabled="disabled" type="primary" size="small"
        >{{ $t('点击上传') }}
      </el-button>
      <template #tip v-if="listType === 'text' || listType === 'picture'">
        <div class="el-upload__tip" v-text="tip"></div>
      </template>
    </el-upload>
    <el-image-viewer v-if="dialogVisible" @close="closeViewer" :url-list="srcList" />
  </div>
</template>

<script>
import { i18n } from '@/plugins/lang'
import util from '@/plugins/util'
import { showToast } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { ElButton, ElUpload, ElImageViewer, ElImage } from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
// deleteFile
import {
  uploadFile,
  getFileList,
  uploadCalibrationZipFile,
  uploadFtmPakringLotData,
  uploadWorkReport
} from '@/apis/base/file'

export default {
  name: 'upload',
  components: {
    ElButton,
    ElUpload,
    ElImageViewer,
    ElImage,
    LtwIcon
  },
  data() {
    return {
      $t: i18n.global.t,
      dialogVisible: false,
      uploadServer: '',
      headers: {
        token: util.getToken()
      },
      postData: {},
      srcList: [],
      initialIndex: 0,
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      fileList: [],
      icon: {
        video: require('@/assets/images/uploadFile/video.png'),
        pdf: require('@/assets/images/uploadFile/pdf.png'),
        doc: require('@/assets/images/uploadFile/doc.png'),
        excel: require('@/assets/images/uploadFile/excel.png'),
        other: require('@/assets/images/uploadFile/other.png'),
        ppt: require('@/assets/images/uploadFile/ppt.svg'),
        txt: require('@/assets/images/uploadFile/txt.svg'),
        zip: require('@/assets/images/uploadFile/ZIP.svg')
      }
    }
  },
  props: {
    modelValue: [String, Array],
    index: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    drag: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'img'
    },
    limit: {
      type: Number,
      default: 5
    },
    sourceType: {
      type: String,
      default: ''
    },
    sourceId: {
      type: String,
      default: ''
    },
    listType: {
      type: String,
      default: 'picture-card' //"text" | "picture" | "picture-card"
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    noTenantCode: {
      type: Boolean,
      default: false
    },
    tip: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: ''
    },
    checkType: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  computed: {
    hideUploadBtn() {
      return this.limit <= this.fileList.length || this.disabled
    }
  },
  watch: {
    sourceId: {
      handler() {
        this.downloadFile()
      },
      deep: true,
      immediate: true
    },
    // modelValue(val) {
    //   // 初始化文件列表
    //   if (!val) {
    //     this.fileList = []
    //   }
    // },
    fileList: {
      handler(val) {
        let urls = []
        for (let i = 0; i < val.length; i++) {
          if (val[i].id) {
            urls.push(val[i].id)
          }
        }
        this.$emit('update:modelValue', urls && urls.length ? urls : '')
      },
      deep: true
    }
  },
  methods: {
    downloadFile() {
      if (this.sourceId) {
        let postData = {
          sourceId: this.sourceId,
          sourceType: this.sourceType
        }
        if (this.noTenantCode) {
          postData.tenantCode = 'none'
        }
        getFileList(postData).then(res => {
          res.data.forEach(val => {
            val.name = val.fileName + (val.fileType ? '.' + val.fileType : '')
            val.url = this.downloadUrl + val.id + '?token=' + util.getToken()
            if (this.noTenantCode) {
              val.url += '&tenantCode=none'
            }
          })
          this.fileList = res.data
        })
      } else {
        this.fileList = []
      }
    },
    beforeAvatarUpload(file) {
      const isAcceptFile = this.checkAcceptFile(file)
      if (isAcceptFile) {
        if (this.isVideo(file.name)) {
          const isLt2M = file.size / 1024 / 1024 < 500
          if (!isLt2M) {
            showToast(`上传视频大小不能超过 500MB`, 'error')
          }
          return isLt2M
        } else if (this.isImage(file.name)) {
          // this.postData.fileType = 'C_IMG'
          const isJPG = file.type === 'image/jpeg'
          const isPNG = file.type === 'image/png'
          const isGIF = file.type === 'image/gif'
          const isLt10M = file.size / 1024 / 1024 < 10
          if (!isJPG && !isPNG && !isGIF) {
            showToast(`上传图片必须是JPG/PNG/GIF 格式`, 'error')
          }
          if (!isLt10M) {
            showToast(`上传图片大小不能超过 10MB`, 'error')
          }
          return (isJPG || isPNG || isGIF) && isLt10M
        }
      } else {
        showToast(`仅支持${this.accept}文件格式`, 'error')
        return false
      }
    },
    checkAcceptFile(file) {
      if (this.accept && file.name) {
        const fileType = this.getType(file.name)
        return ~this.accept.indexOf(fileType)
      }
      return true
    },
    handleRemove(file) {
      this.fileList.forEach((val, index) => {
        if (val.url === file.url || val.uid === file.uid) {
          this.fileList.splice(index, 1)
          // deleteFile(val.id)
        }
      })
    },
    handleDownload(file) {
      window.open(file.url)
    },
    deleteFile(file) {
      // let index = this.fileList.findIndex(val => val.url === file.url)
      // if (~index) {
      //   this.fileList.splice(index, 1)
      // } else {
      this.$refs.uploadRef.handleRemove(file)
      // }
    },
    getFilesList(fileList) {
      let urls = []
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].id) {
          urls.push(fileList[i].id)
        }
      }
      return urls
    },
    handleExceed() {
      showToast(`当前限制选择 ${this.limit} 个文件`, 'warning')
    },
    handlePictureCardPreview(file) {
      if (this.isImage(file.fileType || file.name)) {
        if (file.url) {
          this.srcList = [file.url]
        } else {
          const item = this.fileList.find(val => val.name === file.name.split('.')[0])
          this.srcList = [item.url]
        }
        this.dialogVisible = true
      } else if (file.fileType === 'pdf') {
        location.href = file.url
      } else {
        if (this.listType === 'text') {
          if (file.url) {
            window.open(file.url)
          } else {
            let fileItem = this.fileList.find(val => val.uid === file.uid)
            window.open(fileItem.url)
          }
        } else {
          showToast('此文件不支持预览', 'warning')
        }
      }
    },
    // 上传文件
    async handleUploadForm(param) {
      if (param.file) {
        let formData = new FormData()
        // formData.append('sourceId', this.sourceId) // 额外参数
        formData.append('file', param.file) // 额外参数
        formData.append('sourceType', this.sourceType) // 额外参数
        if (this.noTenantCode) {
          formData.append('tenantCode', 'none') // 额外参数
        }
        this.$emit('returnFile', param.file)
        let res
        // 类型只有标定参数zip包需要走特定接口
        if (this.checkType === 'calibation_param_zip') {
          formData.append('variant', this.variant) // 额外参数
          res = await uploadCalibrationZipFile(formData)
        } else if (this.checkType === 'parking_lot_import_file') {
          res = await uploadFtmPakringLotData(formData)
        } else {
          res = await uploadFile(formData)
        }

        showToast('上传成功')
        this.$emit('closeDialog')
        if (typeof res.data === 'object' && res.data !== null) {
          res.data.name = res.data.fileName
          res.data.url = this.downloadUrl + res.data.id + '?token=' + util.getToken()
          res.data.uid = param.file.uid
          this.fileList.push(res.data)
        }
        // this.fileList.splice(this.fileList.length - 1, 0, res.data)
        // uploadFile(formData)
        //   .then(res => {
        //     showToast('上传成功')
        //     res.data.name = res.data.fileName
        //     res.data.url =
        //       this.downloadUrl + res.data.id + '?token=' + util.getToken()
        //     res.data.uid = param.file.uid
        //     this.fileList.splice(this.fileList.length - 1, 0, res.data)
        //   })
        //   .catch(err => {
        //     this.$refs.uploadRef.handleRemove(param)
        //     // this.$refs.uploadRef.abort(param)
        //     // this.fileList.pop()
        //   })
      }
    },
    getFileSrc(file) {
      if (!file.fileType) {
        file.fileType = this.getType(file.raw.name)
      }
      let url
      if (this.isImage(file.fileType)) {
        url = file.url
      } else if (this.isVideo(file.fileType)) {
        url = this.icon.video
      } else if (file.fileType === 'doc' || file.fileType === 'docx') {
        url = this.icon.doc
      } else if (file.fileType === 'xls' || file.fileType === 'xlsx') {
        url = this.icon.excel
      } else if (file.fileType === 'ppt' || file.fileType === 'pptx') {
        url = this.icon.ppt
      } else if (file.fileType === 'pdf') {
        url = this.icon.pdf
      } else if (file.fileType === 'txt' || file.fileType === 'json') {
        url = this.icon.txt
      } else if (file.fileType === 'zip' || file.fileType === 'jar' || file.fileType === 'rar') {
        url = this.icon.zip
      } else {
        url = this.icon.other
      }
      return url
    },
    isImage(name) {
      let type = this.getType(name)
      if (!type) {
        return false
      }
      return (
        type.toLowerCase() === 'jpeg' ||
        type.toLowerCase() === 'jpg' ||
        type.toLowerCase() === 'png' ||
        type.toLowerCase() === 'gif'
      )
    },
    isVideo(name) {
      let type = this.getType(name)
      if (!type) {
        return false
      }
      return (
        type.toLowerCase() === 'mp4' ||
        type.toLowerCase() === 'rmvb' ||
        type.toLowerCase() === 'avi' ||
        type.toLowerCase() === 'ts' ||
        type.toLowerCase() === 'mov'
      )
    },
    getType(url) {
      if (url) {
        let urlArr = url.split('.')
        return urlArr[urlArr.length - 1]
      }
    },
    getName(url) {
      if (url) {
        let urlArr = url.split('-')
        urlArr.splice(0, 1)
        return urlArr.join('')
      }
    },

    closeViewer() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.hide-upload-btn {
  :deep(.el-upload) {
    display: none;
  }
}

:deep(.el-upload-list),
.el-upload-list.el-upload-list--text {
  margin: 0;
}

.el-upload-list__item-thumbnail {
  object-fit: cover;
  height: 148px;
  width: 148px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

.el-upload__tip {
  line-height: 12px;
  margin-top: 0;
}

.fileName {
  width: 100%;
  position: absolute;
  bottom: 0;
  // bottom: -130px;
  background: rgba(45, 45, 45, 0.75);
  color: #ffffff;
  text-align: center;
  font-size: 12px;
  line-height: 25px;
}

:deep(.el-upload-list__item) {
  width: auto;
  margin-bottom: 0;
}
</style>
