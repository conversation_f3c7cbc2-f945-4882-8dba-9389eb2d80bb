<!-- <common-selection
  v-model="form.modality"
  :model-code="'code'"
  :model-name="'name,code'"
  :model-options="modalityData"
  :disabled="formReadonly"
  @change="changeModality"
/> -->
<template>
  <el-select
    v-model="selectValue"
    :placeholder="$t('请选择')"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    :size="size"
  >
    <el-option
      v-for="item in modelOptions"
      :key="item.id"
      :label="item[modelName]"
      :value="item[modelCode]"
      :id="item.vin"
    />
  </el-select>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { ElSelect, ElOption } from 'element-plus'

export default {
  name: 'BsVehicleSelection',
  props: {
    modelValue: [String, Number, Array],
    size: String,
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: false
    },
    modelCode: {
      type: String,
      default: 'id'
    },
    modelName: {
      type: String,
      default: 'name'
    },
    modelOptions: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      queryParam: {},
      $t: i18n.global.t
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    modelOptions: {
      handler(val) {
        if (val && val.length > 0) {
          let map = {}
          val.forEach(item => {
            map[item[this.modelCode]] = JSON.parse(JSON.stringify(item))
            let modelNameKeyList = this.modelName.split(','),
              nameList = []
            for (let i = 0, iLen = modelNameKeyList?.length; i < iLen; i++) {
              if (item[modelNameKeyList[i]]) {
                nameList.push(item[modelNameKeyList[i]])
              }
            }
            item[this.modelName] = nameList.join('-')
            map[item[this.modelCode]][this.modelName] = item[this.modelName]
          })
          this.dataMap = map
        }
      },
      deep: true,
      immediate: true
    }
  },
  components: { ElSelect, ElOption },
  methods: {
    handleChange(value) {
      this.$emit('change', this.dataMap[value])
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped lang="scss">
.select-option {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  div {
    margin-right: 10px;
    padding: 1px;
  }
}
</style>
