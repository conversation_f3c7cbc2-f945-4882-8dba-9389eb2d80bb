<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-tool-container">
          <dictionary-selection
            id="status"
            ref="vehicleStatusSelectionRef"
            v-model="queryParam.status"
            clearable
            dictionaryType="bs_tag_equipment_status"
            :placeholder="$t('根据状态搜索')"
            @change="refresh"
          />
        </div>
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input :placeholder="$t('根据编码搜索')" v-model="queryParam.code" clearable @clear="refresh">
            <template #append>
              <el-button id="refresh" @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :id="item.buttonIconCode"
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item.buttonCode)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量删除') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :id="item.buttonIconCode"
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table :data="pageData.records" @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
        <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
        <el-table-column header-align="left" align="left" prop="code" :label="$t('编码')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="type" :label="$t('类型')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="brand" :label="$t('品牌')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="model" :label="$t('型号')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="system" :label="$t('系统')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="vehicle" :label="$t('车辆')">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.vehicleVariant && scope.row.vehicleVin"
              effect="dark"
              content="unbind"
              placement="top"
              :enterable="false"
            >
              <div>
                <el-link type="primary" :underline="false" @click="unbindBsTagEquipment(scope.row)">
                  <ltw-icon icon-code="el-icon-unlock"></ltw-icon> </el-link
                >&nbsp;
                <span>{{ scope.row.vehicleVariant + '-' + scope.row.vehicleVin }}</span>
              </div>
            </el-tooltip>
            <el-popover v-else placement="right" width="400" trigger="click">
              <bs-vehicle-selection
                :data="vehicleList"
                :auto-load="false"
                v-model="scope.row.vehicleId"
                @change="handleVehilceChange($event, scope.row)"
                :ref="'vehicleSelectionRef' + scope.row.id"
              ></bs-vehicle-selection>
              <template #reference>
                <el-link type="primary" :underline="false">
                  <ltw-icon icon-code="el-icon-connection"></ltw-icon>
                </el-link>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('状态')">
          <template #default="scope">
            <el-tag :type="scope.row.status == equipStatusFree ? 'success' : ''">{{ scope.row.statusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="180" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="item.name"
                placement="top"
                :enterable="false"
              >
                <el-button
                  :id="item.buttonCode"
                  :type="item.buttonStyleType"
                  @click="executeButtonMethod(item.buttonCode, scope.row)"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item :label="$t('编码')" prop="code">
          <ltw-input id="code" v-model="formData.code" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('类型')" prop="type">
          <ltw-input id="type" v-model="formData.type" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('品牌')" prop="brand">
          <ltw-input type="brand" v-model="formData.brand" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('型号')" prop="model">
          <ltw-input id="model" v-model="formData.model" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('系统')" prop="system">
          <ltw-input id="system" v-model="formData.system" :disabled="formReadonly"></ltw-input>
        </el-form-item>
        <el-form-item :label="$t('状态')" prop="status" v-if="dialogStatus === 'view'">
          <dictionary-selection
            id="status"
            v-model="formData.status"
            :disabled="formReadonly"
            dictionaryType="equipment_status_type"
          ></dictionary-selection>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button id="close" @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{
            $t('关闭')
          }}</el-button>
          <template v-else>
            <el-button id="cancel" @click="dialogVisible = false">{{ $t('取消') }}</el-button>
            <el-button id="save" type="primary" @click="save">{{ $t('保存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  saveBsTagEquipment,
  updateBsTagEquipment,
  deleteBsTagEquipment,
  pageBsTagEquipment,
  getBsTagEquipment,
  bindBsTagEquipment,
  unbindBsTagEquipment
} from '@/apis/data-collect/bs-tag-equipment'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { BS_TAG_EQUIP_STATUS } from '@/plugins/constants/data-dictionary'
import { showToast, showConfirmToast } from '@/plugins/util'

const defaultFormData = {}
export default {
  components: { DictionarySelection, BsVehicleSelection },
  name: 'BsTagEquipment',
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      vehicleList: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        code: [{ required: true, message: this.$t('请输入编码'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('请输入类型'), trigger: 'blur' }],
        brand: [{ required: true, message: this.$t('请输入品牌'), trigger: 'blur' }],
        model: [{ required: true, message: this.$t('请输入型号'), trigger: 'blur' }],
        system: [{ required: true, message: this.$t('请输入系统'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('请输入状态'), trigger: 'blur' }]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      equipStatusFree: BS_TAG_EQUIP_STATUS.FREE
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
    this.listVehicle()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageBsTagEquipment(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('添加')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveBsTagEquipment(this.formData).then(() => {
            this.dialogVisible = false
            showToast(this.$t('添加数据成功'), 'success')
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateBsTagEquipment(this.formData).then(() => {
            this.dialogVisible = false
            showToast(this.$t('修改数据成功'), 'success')
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      getBsTagEquipment(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getBsTagEquipment(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        showToast(this.$t('请先选择数据再执行批量操作'), 'warning')
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteBsTagEquipment(param).then(() => {
          showToast(this.$t('删除数据成功'), 'success')
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    handleVehilceChange({ node }, row) {
      this.bindBsTagEquipment({ vehicleId: node.id, id: row.id })
    },
    listVehicle() {
      listBsVehicle().then(res => {
        this.vehicleList = res.data
      })
    },
    bindBsTagEquipment(row) {
      let postData = {
        tagEquipId: row.id,
        vehicleId: row.vehicleId
      }
      bindBsTagEquipment(postData).then(res => {
        showToast(this.$t('绑定成功'), 'success')
        this.query()
        this.listVehicle()
      })
    },
    unbindBsTagEquipment(row) {
      showConfirmToast({
        message: BASE_CONSTANT.UNBIND_CONFIRM_MSG
      }).then(() => {
        let postData = {
          tagEquipId: row.id
        }
        unbindBsTagEquipment(postData).then(res => {
          showToast(this.$t('解绑成功'), 'success')
          this.query()
          this.listVehicle()
        })
      })
    }
  }
}
</script>
