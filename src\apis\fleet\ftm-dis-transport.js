import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getDiskList = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/disk/list', params })
  }

  export const getFullList = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/disk/full_list', params })
  }
  export const pageWebDiskTransport = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/pageWebDiskTransport', params })
  }
  export const pageWebDiskDataTransport = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/pageWebDiskDataTransport', params })
  }

  export const getLogisticsInfo = (params ={}) =>{
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/sf_service/queryRoute', params })
  }

  export const exportDiskTransportExcel = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/exportExcelWebDiskTransport', params,responseType: 'blob' })
  }

  export const exportExcelWebDiskDataTransport = (params = {}) => {
    return httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_delivery_records/exportExcelWebDiskDataTransport', params,responseType: 'blob' })
  }