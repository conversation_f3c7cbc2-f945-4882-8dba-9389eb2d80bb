<template>
  <div>
    <el-card>
      <el-tabs v-model="queryParam.bisType" @tab-click="handleTabClick">
        <el-tab-pane v-for="item in bisTypeList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>
      </el-tabs>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
              :placeholder="$t('请输入关键字')"
              v-model="queryParam.key"
              clearable
              @clear="refresh"
              id="input-search"
          >
            <template #append>
              <el-button @click="refresh" id="el-icon-search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
              :type="item.buttonStyleType"
              :key="item.id"
              v-for="item in permission.outlineFunctionList"
              @click="executeButtonMethod(item.buttonCode)"
              :id="item.buttonCode"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
              @command="handleCommand"
              class="batch-operate-btn"
              v-if="permission.batchingFunctionList && permission.batchingFunctionList.length > 0"
          >
            <el-button type="primary" id="batchOperation">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                    :key="item.id"
                    v-for="item in batchingFunctionList"
                    :command="item.buttonCode"
                    :id="item.buttonIconCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="content">
        <draggable
            :list="groupList"
            tag="div"
            :component-data="{ name: 'fade' }"
            item-key="id"
            @change="handleGroupChange"
            ghost-class="ghost"
            group="tag-group"
            filter=".el-card__body,.el-dropdown,.disable-dragging"
        >
          <template #item="{ element }">
            <bs-tag-group
                :inlineFunctionList="permission.inlineFunctionList"
                :outlineFunctionList="permission.outlineFunctionList"
                :tag-group="element"
                @command="handleTagGroupCommand"
                ref="bsTagGroupRef"
                @update-children="handleUpdateChildren($event, element)"
            ></bs-tag-group>
          </template>
        </draggable>
      </div>
      <bs-tag-group-dialog
          ref="bsTagGroupDialogRef"
          @save="handleTagGroupSave"
          class="bs-tag-group-dialog"
      ></bs-tag-group-dialog>
      <bs-tag-dialog :data-operate-permission="permission.dataOperatePermission" ref="bsTagDialogRef" @save="handleTagSave" class="bs-tag-dialog"></bs-tag-dialog>
    </el-card>
  </div>
</template>

<script>
import { deleteBsTagGroup, treeListBsTagGroup, reorderBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import draggable from 'vuedraggable'
import BsTagGroup from '@/pages/dataCollect/components/BsTagGroup'
import BsTagGroupDialog from '@/pages/dataCollect/dialog/BsTagGroupDialog'
import BsTagDialog from '@/pages/dataCollect/dialog/BsTagDialog'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { getBsTag } from '@/apis/data-collect/bs-tag'

export default {
  name: 'BsTag',
  components: { BsTagGroup, BsTagGroupDialog, BsTagDialog, draggable },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      permission: {},
      queryParam: {
        current: 1,
        size: 10
      },
      groupList: [],
      currentOperateGroup: {},
      currentOperateTag: undefined,
      bisTypeList: []
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.permission.batchingFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.permission.inlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.permission.outlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.permission.dataOperatePermission =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.listBisType()
  },
  computed: {},
  methods: {
    listBisType() {
      listSysDictionary({ typeCode: 'tag_bis_type' }).then(res => {
        this.bisTypeList = res.data
        if (res.data?.length) {
          this.queryParam.bisType = res.data[0].code
        }
        this.query()
      })
    },
    handleTabClick(tab, e) {
      this.$nextTick(() => {
        this.refresh()
      })
    },
    refresh() {
      this.queryParam.current = 1
      this.query()
    },
    query() {
      treeListBsTagGroup(this.queryParam).then(res => {
        this.groupList = res.data
      })
    },
    add() {
      let defaultData = {}
      if (this.groupList && this.groupList.length > 0) {
        defaultData.sortNum = this.groupList[this.groupList.length - 1].sortNum + BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagGroupDialogRef.add(defaultData)
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    edit(row) {
      this.$refs.bsTagGroupDialogRef.edit(row.id)
    },
    view(row) {
      this.$refs.bsTagGroupDialogRef.view(row.id)
    },
    singleRemove(row) {
      this.remove(row)
    },
    remove(row) {
      if (row.children && row.children.length > 0) {
        this.$alert('This group already has sub groups, please delete the sub groups at first!', 'Reminder', {
          confirmButtonText: 'OK'
        })
        return
      }

      let msg = 'This operation will permanently delete the selected group and the tags in it. Do you want to continue?'
      this.$confirm(msg, 'Reminder', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      })
          .then(() => {
            deleteBsTagGroup({ id: row.id }).then(() => {
              this.query()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: 'Cancelled delete'
            })
          })
    },
    handleTagGroupCommand(command, row) {
      this.executeButtonMethod(command, row)
    },
    addSubGroup(row) {
      let defaultData = {}
      defaultData.parentId = row.id
      if (row.children && row.children.length > 0) {
        defaultData.sortNum = row.children[row.children.length - 1].sortNum + BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagGroupDialogRef.addSubGroup(defaultData)
    },
    handleTagGroupSave() {
      this.query()
    },
    addTag(group) {
      this.currentOperateTag = undefined
      this.currentOperateGroup = group
      let defaultData = { groupId: group.id }
      if (this.currentOperateGroup.tagList && this.currentOperateGroup.tagList.length > 0) {
        defaultData.sortNum =
            this.currentOperateGroup.tagList[this.currentOperateGroup.tagList.length - 1].sortNum +
            BASE_CONSTANT.SORT_NUM_STEP
      } else {
        defaultData.sortNum = BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.bisType = this.queryParam.bisType
      this.$refs.bsTagDialogRef.add(defaultData)
    },
    viewTag(tag) {
      this.currentOperateTag = tag
      let data = Object.assign({}, tag)
      getBsTag(data.id).then(res => {
        this.$refs.bsTagDialogRef.view(res.data, this.checkBtnVisible('edit', this.permission.inlineFunctionList))
      })
    },
    checkBtnVisible(code, btnGroup) {
      return btnGroup.find(val => val.buttonCode === code)
    },
    handleTagSave(tag) {
      if (this.currentOperateTag) {
        Object.assign(this.currentOperateTag, tag)
      } else {
        if (!this.currentOperateGroup.tagList) {
          this.currentOperateGroup.tagList = []
        }
        this.currentOperateGroup.tagList.push(tag)
      }
      this.currentOperateTag = undefined
    },
    handleGroupChange(e) {
      if (e.moved) {
        this.handleGroupMove(e.moved)
      }
      if (e.added) {
        this.handleGroupAdd(e.added)
      }
    },
    handleGroupMove(moved) {
      let element = moved.element
      let preSortNum
      let nextSortNum
      let newIndex = moved.newIndex
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortBsTagGroup(element, preSortNum, nextSortNum)
    },
    handleGroupAdd(added) {
      let element = added.element
      let preSortNum
      let nextSortNum
      let newIndex = added.newIndex
      let newParentId = ''
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
        newParentId = this.groupList[newIndex - 1].parentId
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        newParentId = this.groupList[newIndex + 1].parentId
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortBsTagGroup(element, preSortNum, nextSortNum, newParentId)
    },
    sortBsTagGroup(element, preSortNum, nextSortNum, newParentId) {
      reorderBsTagGroup({
        id: element.id,
        preSortNum,
        nextSortNum,
        newParentId
      }).then(res => {
        let newSortNum = res.data
        if (!nextSortNum) {
          element.sortNum = newSortNum
        } else {
          if (!preSortNum) {
            preSortNum = 0
          }
          if (preSortNum < newSortNum && newSortNum < nextSortNum) {
            element.sortNum = newSortNum
          } else {
            this.query()
          }
        }
      })
    },
    handleUpdateChildren(children, element) {
      element.children = children
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;

  .ghost {
    background-color: rgba(241, 239, 239, 0.8);
  }
}

.tag-group-dropdown-menu {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
  }
}

.fade-move {
  transition: transform 0.5s;
}
</style>
