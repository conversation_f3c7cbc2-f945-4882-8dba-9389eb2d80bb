<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      :draggable="true"
      class="tag-list"
  >
    <div style="text-align: right">
      <el-link
          v-if="editTagsFlag && timeEdit"
          @click="continueEditTag()"
          style="margin-right: 10px"
          type="primary"
          :underline="false"
          id="edit"
      >{{ $t('编辑') }}</el-link
      >
      <el-link
          v-if="reChooseFlag"
          @click="reChoose()"
          style="margin-right: 10px"
          type="primary"
          :underline="false"
          id="rechoose"
      >{{ $t('重新选择') }}</el-link
      >
    </div>
    <bs-tag-group-panel
        :continuous-units="continuousUnits"
        :edit-tags="reChooseFlag"
        :data="tagsData"
        :timeEdit="timeEdit"
        :attributes="attributes"
        @tagClose="tagClose"
        @tagSave="tagSave"
    ></bs-tag-group-panel>
  </el-dialog>
  <bs-tag-group-drawer
      @drawerClick="confirmDistributeTags"
      :drawerVisible="tagChooseDrawerVisible"
      :requirementId="requirementId"
      :rowTagList="tagList"
  ></bs-tag-group-drawer>
</template>

<script>
import { showToast } from '@/plugins/util'
import { ElDialog, ElLink } from 'element-plus'
import { i18n } from '@/plugins/lang'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import TAG_ATTRIBUTES from '@/plugins/constants/tag-attributes.js'
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      editTagsFlag: false,
      reChooseFlag: false,
      tagsData: [],
      continuousUnits: [],
      tagList: [],
      tagChooseDrawerVisible: false,
      requirementId: '',
      $t: i18n.global.t,
      attributes: [
        TAG_ATTRIBUTES.SUPPORT_TRIGGER,
        TAG_ATTRIBUTES.SUPPORT_VOICE,
        TAG_ATTRIBUTES.FOLLOWING_DURATION,
        TAG_ATTRIBUTES.PREVIOUS_DURATION
      ]
    }
  },
  components: {
    ElDialog,
    ElLink,
    BsTagGroupDrawer,
    BsTagGroupPanel
  },
  props: {
    timeEdit: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    show(row) {
      this.dialogStatus = row.type
      this.requirementId = row.requirementId
      this.tagList = row.tagList
      this.editTags(row.type === 'edit' ? true : false)
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {
    },
    initForm() {
      this.reChooseFlag = false
      this.editTagsFlag = false
    },
    tagClose() {
      this.dialogVisible = false
      this.dialogClosed()
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        let tagsData = JSON.parse(JSON.stringify(data.tagList))
        this.tagList = []
        this.saveCheckedTagList({children: tagsData})
        this.tagsData = tagsData
        this.reChooseFlag = true
        this.editTags(false)
      }
      this.tagChooseDrawerVisible = false
    },

    saveCheckedTagList(group) {
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          if (group.tagMap[tagId].type === 'continuous') {
            if (group.tagMap[tagId].unit !== 's') {
              let time = this.formatToSec(group.tagMap[tagId])
              group.tagMap[tagId].min = time.num
              group.tagMap[tagId].unit = time.unit
            }
          }
          this.tagList.push({
            ...group.tagMap[tagId]
          })
        })
      }
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        this.tagList = []
        let tagList = JSON.parse(JSON.stringify(dataList))
        this.saveCheckedTagList({children: tagList})
        if (this.tagList?.length) {
          this.cancel(this.tagList)
        } else {
          showToast('请至少保留一个', 'warning')
        }
      }
    },
    editTags(editTags) {
      this.dialogVisible = true
      this.editTagsFlag = editTags
      if (this.editTagsFlag || this.reChooseFlag) {
        this.dialogTitle = i18n.global.t('编辑标签')
      } else {
        this.dialogTitle = i18n.global.t('查看标签')
      }
      if (this.tagsData && this.tagsData.length) {
        this.setCheckedList(this.tagsData)
      } else {
        treeListBsTagGroup().then(res => {
          this.tagsData = res.data
          this.setCheckedList(this.tagsData)
        })
      }
      this.getContinuousUnits()
    },

    setCheckedList(list) {
      let checkedCount = 0
      if (this.tagList && this.tagList.length) {
        let tagMap = {}
        this.tagList.forEach(tag => {
          tagMap[tag.id] = tag
        })
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
              if (tagMap[tag.id]) {
                item.checkedTagIdList.push(tag.id)
                tag.checked = true
                if (tag.type === 'continuous') {
                  //去除单位
                  if (tagMap[tag.id].min || tagMap[tag.id].minDuration) {
                    tag.min = tagMap[tag.id].min
                    tag.minDuration = tagMap[tag.id].minDuration
                    // let time = this.formatToHour(tagMap[tag.id].min)
                    // tag.min = time.num
                    // tag.unit = time.unit
                  } else {
                    tag.min = 0
                    tag.unit = 's'
                  }
                } else if (tag.type === 'transient') {
                  tag.min = tagMap[tag.id].min
                }
              } else {
                tag.checked = false
              }
            })
            if (item.checkedTagIdList.length > 0) {
              checkedCount++
              if (item.checkedTagIdList.length === item.tagList.length) {
                item.checkedAll = true
              } else {
                item.isIndeterminate = true
              }
            }
          }
          if (item.children && item.children.length > 0) {
            checkedCount++
            let childrenCheckedCount = this.setCheckedList(item.children)
            if (childrenCheckedCount === item.children.length) {
              item.checkedAll = true
            } else {
              if (childrenCheckedCount > 0) {
                item.isIndeterminate = true
              }
            }
          }
        })
      } else {
        list.forEach(item => {
          item.checkedAll = false
          item.isIndeterminate = false
          if (item.tagList && item.tagList.length > 0) {
            item.checkedTagIdList = []
            item.tagMap = {}
            item.tagList.forEach(tag => {
              item.tagMap[tag.id] = tag
            })
          }
          if (item.children && item.children.length > 0) {
            this.setCheckedList(item.children)
          }
        })
      }
      return checkedCount
    },
    getContinuousUnits() {
      if (!(this.continuousUnits && this.continuousUnits.length)) {
        listSysDictionary({typeCode: 'continuous_units'}).then(res => {
          this.continuousUnits = res.data
        })
      }
    },
    formatToHour(sec) {
      let num = sec,
          unit = 's'
      if (sec >= 3600) {
        num = Math.floor(sec / 3600)
        unit = 'h'
      } else if (sec >= 60) {
        num = Math.floor(sec / 60)
        unit = 'min'
      }
      return {
        num,
        unit
      }
    },
    formatToSec(time) {
      let num = 0,
          unit = 's'
      if (time.unit === 'h') {
        num = time.min * 3600
      } else if (time.unit === 'min') {
        num = time.min * 60
      } else {
        num = time.min
      }
      return {num, unit}
    },
    continueEditTag() {
      this.editTagsFlag = false
      this.reChooseFlag = true
    },
    reChoose() {
      this.cancel()
      this.tagChoose()
    },
    tagChoose(data) {
      this.tagChooseDrawerVisible = true
      if (data) {
        this.tagList = data.tagList
        this.requirementId = data.requirementId
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
