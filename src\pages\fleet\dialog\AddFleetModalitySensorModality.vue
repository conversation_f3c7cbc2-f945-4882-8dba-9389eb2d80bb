<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="formReadonly"
    @close="dialogClosed"
    @open="dialogOpened"
  >
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
      :hide-required-asterisk="formReadonly"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('名称')" prop="name">
            <ltw-input
              id="name"
              v-model="form.name"
              :disabled="formReadonly"
            ></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('编码')" prop="code">
            <ltw-input
              id="code"
              v-model="form.code"
              :disabled="formReadonly"
            ></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('传感器类型')" prop="sensorType">
            <dictionary-type-selection
              v-model="form.sensorType"
              clearable
              dictionaryType="sensor_type"
              :placeholder="$t('请选择')"
              filterable
              :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('别名')" prop="alias">
            <ltw-input v-model="form.alias" :disabled="formReadonly" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('安装位置')" prop="installationPosition">
            <dictionary-type-selection
              v-model="form.installationPosition"
              clearable
              dictionaryType="install_position"
              :placeholder="$t('请选择')"
              filterable
              :disabled="formReadonly"
            />
            <!-- <ltw-input v-model="form.installationPosition" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('描述')" prop="description">
            <ltw-input
              text-type="description"
              id="description"
              v-model="form.description"
              :disabled="formReadonly"
              type="textarea"
            ></ltw-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button
            id="close"
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >{{ $t('关闭') }}</el-button
          >
        </template>
        <template v-else>
          <el-button id="cancel" @click="dialogVisible = false">{{
            $t('取消')
          }}</el-button>
          <el-button id="save" type="primary" @click="submit">{{
            $t('保存')
          }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { i18n } from '@/plugins/lang'
import {
  getFtmVehicleModality,
  saveFtmVehicleModality,
  updateFtmVehicleModality
} from '@/apis/fleet/ftm-vehicle-modality'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection.vue'
const defaultform = {}
export default {
  name: 'AddModalitySensor',
  emits: ['reload'],
  components: { DictionaryTypeSelection },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        code: [
          { required: true, message: this.$t('请输入编码'), trigger: 'change' }
        ],
        name: [
          { required: true, message: this.$t('请输入名称'), trigger: 'change' }
        ],
        alias: [
          { required: false, message: this.$t('请输入'), trigger: 'change' }
        ],
        sensorType: [
          { required: true, message: this.$t('请输入'), trigger: 'change' }
        ],
        installationPosition: [
          { required: false, message: this.$t('请输入'), trigger: 'change' }
        ]
      },
      $t: i18n.global.t
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = i18n.global.t('新增传感器')
          this.form.sensorType = row.sensorType
          break
        case 'edit':
          this.dialogTitle = i18n.global.t('编辑传感器')
          this.getFtmVehicleModality(row)
          break
        case 'view':
          this.dialogTitle = i18n.global.t('传感器详情')
          this.getFtmVehicleModality(row)
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveFtmVehicleModality(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmVehicleModality(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    getFtmVehicleModality(row) {
      getFtmVehicleModality(row.id).then(res => {
        this.form = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
