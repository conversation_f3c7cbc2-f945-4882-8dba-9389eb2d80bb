<template>
  <div>
    <!-- 嵌入模式 -->
    <div v-if="isEmbedded" class="embedded-content">

    <!-- 标签页区域 -->
    <el-tabs v-model="activeTab" class="vehicle-tabs">
      <el-tab-pane label="车型详情" name="detail">
        <div v-loading="loading" class="detail-content">
          <!-- 操作按钮区域 - 固定在顶部 -->
          <div class="action-buttons-fixed">
            <div>
            <el-button
              type="warning"
              size="small"
              :class="['warning-tag', { 'editing-state': isGlobalEditing }]"
              @click="toggleGlobalEdit"
              :loading="loading"
              class="edit-btn"
            >
              <el-icon><Edit v-if="!isGlobalEditing" /><Check v-else /></el-icon>
              {{ isGlobalEditing ? '保存' : '编辑' }}
            </el-button>
            <el-button
              v-if="isGlobalEditing"
              type="info"
              size="small"
              class="cancel-tag"
              @click="cancelGlobalEdit"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            </div>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete"
              class="delete-btn"
            >
              <el-icon><Delete /></el-icon>
              删除车型
            </el-button>
          </div>

          <!-- 可滚动的内容区域 -->
          <div class="scrollable-content">
            <!-- 基础信息 -->
          <div class="info-section">
            <div class="section-header">
              <h3 class="section-title">基础信息</h3>
            </div>
            <div class="info-content">
              <!-- 编辑模式 -->
              <el-form
                v-if="isGlobalEditing"
                ref="basicInfoForm"
                :model="editingBasicInfo"
                :rules="basicInfoRules"
                label-width="120px"
                size="small"
                class="basic-info-form"
              >
                <el-form-item label="车型编码：" prop="code">
                  <ltw-input
                    v-model="editingBasicInfo.code"
                    placeholder="请输入车型编码"
                    style="max-width:300px"
                  />
                </el-form-item>
                <el-form-item label="供应商：" prop="supplierId">
                  <el-select
                    v-model="editingBasicInfo.supplierId"
                    placeholder="请选择供应商"
                    style="width:300px"
                    @change="handleSupplierChange"
                  >
                    <el-option
                      v-for="item in supplierOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="型号：" prop="vehicleModel">
                  <ltw-input
                    v-model="editingBasicInfo.vehicleModel"
                    placeholder="请输入型号"
                    style="width:300px"
                  />
                </el-form-item>
                <el-form-item label="车辆类型：" prop="vehicleType">
                  <el-select
                    v-model="editingBasicInfo.vehicleType"
                    placeholder="请选择车辆类型"
                    style="width:300px"
                    @change="handleVehicleTypeChange"
                  >
                    <el-option
                      v-for="item in vehicleTypeOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="能源方式：" prop="energyType">
                  <el-select
                    v-model="editingBasicInfo.energyType"
                    placeholder="请选择能源方式"
                    @change="handleEnergyTypeChange"
                     style="width:300px"
                  >
                    <el-option
                      v-for="item in energyTypeOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="装车方案：" prop="projectName">
                  <ltw-input
                    v-model="editingBasicInfo.projectName"
                    placeholder="请输入装车方案"
                    style="width:300px"
                  />
                </el-form-item>
              </el-form>

              <!-- 查看模式 -->
              <div v-else class="info-display">
                <div class="info-row">
                  <span class="info-label">车型编码：</span>
                  <span class="info-value">{{ vehicleData.code || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">供应商：</span>
                  <span class="info-value">{{ vehicleData.supplierName || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">型号：</span>
                  <span class="info-value">{{ vehicleData.vehicleModel || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车辆类型：</span>
                  <span class="info-value">{{ vehicleData.vehicleTypeName || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">能源方式：</span>
                  <span class="info-value">{{ vehicleData.energyTypeName || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">装车方案：</span>
                  <span class="info-value">{{ vehicleData.projectName || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="section-divider"></div>

          <!-- 车身信息 -->
          <div class="info-section">
            <div class="section-header">
              <h3 class="section-title">车身信息</h3>
            </div>
            <div class="info-content">
              <!-- 编辑模式 -->
              <el-form
                v-if="isGlobalEditing"
                ref="bodyInfoForm"
                :model="editingBodyInfo"
                :rules="bodyInfoRules"
                label-width="120px"
                size="small"
                class="body-info-form"
              >
                <el-form-item label="轴距（mm）：" prop="wheelBase">
                  <el-input
                    v-model="editingBodyInfo.wheelBase"
                    placeholder="请输入轴距"
                    style="max-width:300px"
                  />
                </el-form-item>
                <el-form-item label="车长（mm）：" prop="vehicleLength">
                  <el-input
                    v-model="editingBodyInfo.vehicleLength"
                    placeholder="请输入车长"
                    style="max-width:300px"
                  />
                </el-form-item>
                <el-form-item label="车宽（mm）：" prop="vehicleWidth">
                  <el-input
                    v-model="editingBodyInfo.vehicleWidth"
                    placeholder="请输入车宽"
                    style="max-width:300px"
                  />
                </el-form-item>
                <el-form-item label="车高（mm）：" prop="vehicleHeight">
                  <el-input
                    v-model="editingBodyInfo.vehicleHeight"
                    placeholder="请输入车高"
                    style="max-width:300px"
                  />
                </el-form-item>
                <el-form-item label="描述：" prop="description" style="margin-bottom:50px">
                  <el-input
                    v-model="editingBodyInfo.description"
                    type="textarea"
                    maxlength="100"
                    show-word-limit
                    :rows="3"
                    placeholder="请输入描述"
                    style="max-width:300px"
                  />
                </el-form-item>
              </el-form>

              <!-- 查看模式 -->
              <div v-else class="info-display">
                <div class="info-row">
                  <span class="info-label">轴距（mm）：</span>
                  <span class="info-value">{{ vehicleData.wheelBase || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车长（mm）：</span>
                  <span class="info-value">{{ vehicleData.vehicleLength || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车宽（mm）：</span>
                  <span class="info-value">{{ vehicleData.vehicleWidth || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">车高（mm）：</span>
                  <span class="info-value">{{ vehicleData.vehicleHeight || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">描述：</span>
                  <span class="info-value">{{ vehicleData.description || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="section-divider"></div>

          <!-- 照片区域 -->
          <div v-if="vehicleData.id" class="photo-section">
            <!-- 编辑模式 -->
            <div v-if="isGlobalEditing" class="photo-row">
              <div class="photo-label-edit">照片：</div>
              <div class="photo-content">
                <upload-file
                  v-model="editingBodyInfo.fileIdList"
                  source-type="vehicle_variant_attachment"
                  :source-id="vehicleData.id"
                  list-type="picture-card"
                  :disabled="false"
                  :show-file-list="true"
                  :limit="4"
                  accept=".jpg,.jpeg,.png,.gif"
                />
              </div>
            </div>

            <!-- 查看模式 -->
            <div v-else class="photo-row">
              <div class="photo-label">照片：</div>
              <div class="photo-content">
                <div v-if="vehicleData.fileList?.length" class="photo-display">
                  <upload-file
                    source-type="vehicle_variant_attachment"
                    :source-id="vehicleData.id"
                    list-type="picture-card"
                    :disabled="true"
                    :show-file-list="true"
                    :limit="4"
                  />
                </div>
                <div v-else class="no-photo-content">
                  <el-empty description="暂无照片" :image-size="80" />
                </div>
              </div>
            </div>
          </div>
          </div> <!-- 关闭可滚动内容区域 -->
        </div>
      </el-tab-pane>

      <el-tab-pane label="版本信息" name="version">
        <vehicle-version-info ref="vehicleVersionInfo" :vehicle-data="vehicleData" />
      </el-tab-pane>
    </el-tabs>

  </div>

  </div>
</template>

<script>
import { Close, Edit, Delete, Picture, Check } from '@element-plus/icons-vue'
import AddVehicleTypeDialog from './AddVehicleTypeDialog.vue'
import VehicleVersionInfo from './VehicleVersionInfo.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import { getFtmVehicleVariant, updateFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import { deleteFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { createNumberValidationRules } from '@/plugins/util'
export default {
  name: 'VehicleDetailDrawer',
  components: {
    Close,
    Edit,
    Delete,
    Picture,
    Check,
    AddVehicleTypeDialog,
    VehicleVersionInfo,
    UploadFile
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    vehicleId: {
      type: [String, Number],
      default: null
    },
    isEmbedded: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'edit', 'delete', 'save'],
  data() {
    return {
      activeTab: 'detail',
      showEditDialog: false,
      vehicleData: {},
      loading: false,
      isGlobalEditing: false, // 控制全局编辑状态
      supplierOptions: [], // 供应商选项列表
      vehicleTypeOptions: [], // 车型类型选项
      energyTypeOptions: [], // 能源类型选项
      editingBasicInfo: { // 编辑中的基础信息数据
        code: '',
        supplierId: '',
        supplierName: '',
        vehicleModel: '',
        vehicleType: '',
        vehicleTypeName: '',
        energyType: '',
        energyTypeName: '',
        projectName: ''
      },
      editingBodyInfo: { // 编辑中的车身信息数据
        wheelBase: '',
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        description: '',
        fileIdList:[]
      },
      basicInfoRules: { // 基础信息表单验证规则
        code: [
          { required: true, message: '请输入车型编码', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        vehicleModel: [
          { required: true, message: '请输入型号', trigger: 'blur' }
        ],
        vehicleType: [
          { required: true, message: '请选择车辆类型', trigger: 'change' }
        ],
        energyType: [
          { required: true, message: '请选择能源方式', trigger: 'change' }
        ]
      },
      bodyInfoRules: { // 车身信息表单验证规则
        wheelBase: createNumberValidationRules(4,0,false),
        vehicleLength: createNumberValidationRules(5,0,false),
        vehicleWidth: createNumberValidationRules(5,0,false),
        vehicleHeight: createNumberValidationRules(5,0,false)
      }
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },

  },
  watch: {
    vehicleId: {
      handler(newId) {
        if (newId) {
          this.fetchVehicleDetail(newId)
        }
      },
      immediate: true
    },
    activeTab(newTab) {
      // 切换标签页时退出编辑模式
      if (this.isGlobalEditing) {
        this.cancelGlobalEdit()
      }

      // 当切换到版本信息tab时，确保子组件的侧边栏是展开状态
      if (newTab === 'version') {
        this.$nextTick(() => {
          if (this.$refs.vehicleVersionInfo && this.$refs.vehicleVersionInfo.resetSidebarState) {
            this.$refs.vehicleVersionInfo.resetSidebarState()
          }
        })
      }
    }
  },
  async mounted(){
    await Promise.all([
      this.getSupplierOptions(),
      this.getVehicleTypeOptions(),
      this.getEnergyTypeOptions()
    ])

    if (this.vehicleId) {
        this.fetchVehicleDetail(this.vehicleId)
     }
  },
  methods: {
    // 获取供应商选项
    async getSupplierOptions() {
      try {
        const res = await listSysRoleOrg({tagCode: 'vehicle_brand_supplier'})
        this.supplierOptions = res.data || []
      } catch (error) {
        console.error('加载供应商数据失败:', error)
      }
    },

    // 获取车辆类型选项
    async getVehicleTypeOptions() {
      try {
        const res = await listSysDictionary({typeCode: 'variant_type'})
        this.vehicleTypeOptions = res.data || []
      } catch (error) {
        console.error('加载车型类型数据失败:', error)
      }
    },

    // 获取能源类型选项
    async getEnergyTypeOptions() {
      try {
        const res = await listSysDictionary({typeCode: 'energy_type'})
        this.energyTypeOptions = res.data || []
      } catch (error) {
        console.error('加载能源类型数据失败:', error)
      }
    },

    // 处理供应商选择变化
    handleSupplierChange(value) {
      const selectedSupplier = this.supplierOptions.find(item => item.id === value)
      this.editingBasicInfo.supplierName = selectedSupplier ? selectedSupplier.name : ''
    },

    // 处理车辆类型选择变化
    handleVehicleTypeChange(value) {
      const selectedType = this.vehicleTypeOptions.find(item => item.code === value)
      this.editingBasicInfo.vehicleTypeName = selectedType ? selectedType.name : ''
    },

    // 处理能源方式选择变化
    handleEnergyTypeChange(value) {
      const selectedType = this.energyTypeOptions.find(item => item.code === value)
      this.editingBasicInfo.energyTypeName = selectedType ? selectedType.name : ''
    },

    // 获取车辆详情
    async fetchVehicleDetail(vehicleId) {
      if (!vehicleId) return

      this.loading = true
      try {
        const res = await getFtmVehicleVariant(vehicleId)
        this.vehicleData = res.data || {}
        console.log('车辆详情数据:', this.vehicleData)
      } catch (error) {
        console.error('获取车辆详情失败:', error)
        this.vehicleData = {}
      } finally {
        this.loading = false
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      // 退出编辑模式
      if (this.isGlobalEditing) {
        this.cancelGlobalEdit()
      }
      // 清空数据
      this.vehicleData = {}
    },

    handleEdit() {
      // 打开编辑对话框
      this.showEditDialog = true
    },

    handleDelete() {
       showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleVariant({ id: this.vehicleData.id }).then(() => {
          showToast('删除成功','success')
            this.$emit('delete', this.vehicleData)
        })
      })

    },

    handleEditSubmit(formData) {
      // 处理编辑提交
      this.$emit('edit', formData)
      this.showEditDialog = false
      this.handleClose()
    },

    // 切换全局编辑状态
    toggleGlobalEdit() {
      if (this.isGlobalEditing) {
        // 保存编辑
        this.saveAllChanges()
      } else {
        // 进入编辑模式
        this.startGlobalEdit()
      }
    },

    // 开始全局编辑
    startGlobalEdit() {
      if (!this.vehicleData) return

      this.isGlobalEditing = true
      // 初始化基础信息编辑数据
      this.editingBasicInfo = {
        code: this.vehicleData.code || '',
        supplierId: this.vehicleData.supplierId || '',
        supplierName: this.vehicleData.supplierName || '',
        vehicleModel: this.vehicleData.vehicleModel || '',
        vehicleType: this.vehicleData.vehicleType || '',
        vehicleTypeName: this.vehicleData.vehicleTypeName || '',
        energyType: this.vehicleData.energyType || '',
        energyTypeName: this.vehicleData.energyTypeName || '',
        projectName: this.vehicleData.projectName || ''
      }
      // 初始化车身信息编辑数据
      this.editingBodyInfo = {
        wheelBase: this.vehicleData.wheelBase || '',
        vehicleLength: this.vehicleData.vehicleLength || '',
        vehicleWidth: this.vehicleData.vehicleWidth || '',
        vehicleHeight: this.vehicleData.vehicleHeight || '',
        description: this.vehicleData.description || '',
        fileIdList:this.vehicleData.fileIdList|| []
      }
    },

    // 取消全局编辑
    cancelGlobalEdit() {
      this.isGlobalEditing = false
      this.editingBasicInfo = {
        code: '',
        supplierId: '',
        supplierName: '',
        vehicleModel: '',
        vehicleType: '',
        vehicleTypeName: '',
        energyType: '',
        energyTypeName: '',
        projectName: ''
      }
      this.editingBodyInfo = {
        wheelBase: '',
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        description: '',
        fileIdList:[]
      }
      // 清除表单验证
      if (this.$refs.basicInfoForm) {
        this.$refs.basicInfoForm.clearValidate()
      }
      if (this.$refs.bodyInfoForm) {
        this.$refs.bodyInfoForm.clearValidate()
      }
    },

    // 保存所有更改
    async saveAllChanges() {
      if (!this.vehicleData || !this.vehicleData.id) return

      try {
        // 表单验证
        if (this.$refs.basicInfoForm) {
          await this.$refs.basicInfoForm.validate()
        }
        if (this.$refs.bodyInfoForm) {
          await this.$refs.bodyInfoForm.validate()
        }

        // 额外校验必填字段
        if (!this.editingBasicInfo.code || this.editingBasicInfo.code.trim() === '') {
          this.$message.error('请输入车型编码')
          return
        }

        if (!this.editingBasicInfo.supplierId || this.editingBasicInfo.supplierId.trim() === '') {
          this.$message.error('请选择供应商')
          return
        }

        if (!this.editingBasicInfo.vehicleType || this.editingBasicInfo.vehicleType.trim() === '') {
          this.$message.error('请选择车辆类型')
          return
        }

        if (!this.editingBasicInfo.energyType || this.editingBasicInfo.energyType.trim() === '') {
          this.$message.error('请选择能源方式')
          return
        }

        this.loading = true

        // 准备更新数据
        const updateData = {
          id: this.vehicleData.id,
          ...this.editingBasicInfo,
          ...this.editingBodyInfo
        }

        // 调用更新车型的API
        await updateFtmVehicleVariant(updateData)

        // 更新本地数据
        Object.assign(this.vehicleData, this.editingBasicInfo, this.editingBodyInfo)
        this.isGlobalEditing = false
        showToast('保存成功', 'success')

        // 通知父组件保存成功
        this.$emit('save', this.vehicleData)

        // 重新获取最新数据确保数据同步
        await this.fetchVehicleDetail(this.vehicleData.id)

      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 嵌入模式样式
.embedded-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding:0 16px;

  .embedded-header {
    padding: 20px 20px 10px 20px;
    border-bottom: 1px solid #eff1f2;
    background-color: #fafbfc;

    .vehicle-title {
      color: #232628;
      font-size: 18px;
      font-family: 'Bosch Sans', sans-serif;
      font-weight: 600;
      line-height: 18px;
      margin: 0;
    }
  }

  .vehicle-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    :deep(.el-tabs__content) {
      flex: 1;
      
    }

    :deep(.el-tab-pane) {
      height: 100%;
    }

    :deep(.el-tabs__item) {
        font-size: 16px;
        font-family: 'Bosch Sans Global', sans-serif;
        color: #4e5256;
        font-weight: 400;
        padding: 6px 20px 4px 0;
        height: 32px;
        line-height: 22px;
        margin-bottom: 4px;

        &.is-active {
          color: #5755ff;
          font-weight: 700;
        }
      }
     :deep(.el-tabs__active-bar) {
        background-color: #5755ff;
        height: 2px;
      }
  }

  .detail-content {
    padding: 0;
    height: calc(100vh - 330px); // 设置为可视区域高度，减去tab头部等元素的高度
    display: flex;
    flex-direction: column;
  }

  .action-buttons-fixed {
    display: flex;
    gap: 8px;
    padding: 0px 20px 8px 0px;
    justify-content: space-between;
    background: white;
    flex-shrink: 0; // 防止按钮区域被压缩
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  .action-buttons-fixed {
    .warning-tag {
      background-color: #FFB03A;
      border-color: #FFB03A;
      color: white;

      &.editing-state {
        background-color: #32C56A;
        border-color: #32C56A;
      }
    }

    .cancel-tag {
      background-color: #8A9097;
      border-color: #8A9097;
      color: white;
    }

    .edit-btn {
      background: #ffb03a !important;
      border-color: #ffb03a !important;
      color: white !important;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;

      &:hover {
        background: #e69a2e !important;
        border-color: #e69a2e !important;
      }
    }

    .delete-btn {
      background: #ff6e6f !important;
      border-color: #ff6e6f !important;
      color: white !important;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;

      &:hover {
        background: #e55a5b !important;
        border-color: #e55a5b !important;
      }
    }
  }

  .card-title {
    color: #232628;
    font-size: 16px;
    font-family: 'Bosch Sans', sans-serif;
    font-weight: 600;
    line-height: 16px;
  }

  .section-divider {
    height: 1px;
    background-color: #EFF1F2;
    margin: 20px 0;
  }

  .photo-section {
    margin-bottom: 20px;
    .photo-row {
        display: flex;
        align-items: flex-start;

        .photo-label {
          color: #4E5256;
          font-size: 12px;
          font-family: 'Bosch Office Sans', sans-serif;
          font-weight: 400;
          line-height: 23px;
          width: 120px;
          flex-shrink: 0;
        }
        .photo-label-edit {
          color: #4E5256;
          font-size: 12px;
          font-family: 'Bosch Office Sans', sans-serif;
          font-weight: 400;
          line-height: 23px;
          width: 120px;
          flex-shrink: 0;
          padding-right: 12px;
          text-align: right;
        }

        .photo-content {
          flex: 1;

          .photo-display {
            :deep(.el-upload-list--picture-card) {
              .el-upload-list__item {
                border-radius: 2px;
                border: 1px solid #DCDFE6;
              }
            }
          }

          .no-photo-content {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 120px;
            border: 1px dashed #DCDFE6;
            border-radius: 2px;
            background-color: #FAFBFC;

            :deep(.el-empty) {
              .el-empty__description {
                color: #8A9097;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
              }
            }
          }
        }
      }
  }

  .no-data {
    color: #999;
    font-style: italic;
  }

  // 基础信息编辑样式
  .info-section {
    margin-bottom: 20px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .section-title {
        color: #232628;
        font-size: 16px;
        font-family: 'Bosch Sans', sans-serif;
        font-weight: 600;
        line-height: 16px;
        margin: 0;
      }




      
    }

    .info-content {
      .basic-info-form,
      .body-info-form {
        // 覆盖Element UI的small尺寸设置
        &.el-form--small {
          :deep(.el-form-item) {
            margin-bottom: 16px;

            .el-form-item__label {
              color: #4E5256;
              font-size: 12px;
              font-family: 'Bosch Office Sans', sans-serif;
              font-weight: 400;
              line-height: 32px !important; // 调整为32px行高，覆盖small尺寸
              height: 32px;
            }

            .el-form-item__content {
              line-height: 32px !important; // 确保内容区域行高与输入框一致
              height:32px;
              .el-input__wrapper{
                height: 32px !important;
              }
            .el-input {
              :deep(.el-input--small){
                height: 32px;
              }
              :deep(.el-input__wrapper) {
                border-radius: 2px !important;
                border: 1px solid #DCDFE6 !important;
                height: 32px !important; // 设置输入框高度为32px

                &:hover {
                  border-color: #C0C4CC !important;
                }

                &.is-focus {
                  border-color: #409EFF !important;
                }
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            .el-select {
              :deep(.el-input__wrapper) {
                height: 32px !important;
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            .ltw-input {
              :deep(.el-input__wrapper) {
                height: 32px !important;
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            // 其他可能的输入组件32px高度设置
            .el-date-picker {
              :deep(.el-input__wrapper) {
                height: 32px !important;
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            .el-cascader {
              :deep(.el-input__wrapper) {
                height: 32px !important;
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            .el-input-number {
              :deep(.el-input__wrapper) {
                height: 32px !important;
              }

              :deep(.el-input__inner) {
                height: 32px !important;
                line-height: 32px !important;
              }
            }

            .el-textarea {
              :deep(.el-textarea__inner) {
                border-radius: 2px;
                border: 1px solid #DCDFE6;

                &:hover {
                  border-color: #C0C4CC;
                }

                &:focus {
                  border-color: #409EFF;
                }
              }
            }
          }
        }
        }
      }

      .info-display {
        .info-row {
          display: flex;
          margin-bottom: 12px;
          align-items: center;

          .info-label {
            color: #4E5256;
            font-size: 12px;
            font-family: 'Bosch Office Sans', sans-serif;
            font-weight: 400;
            line-height: 23px;
            width: 120px;
            flex-shrink: 0;
          }

          .info-value {
            color: #232628;
            font-size: 12px;
            font-family: 'Bosch Office Sans', sans-serif;
            font-weight: 400;
            line-height: 23px;
            flex: 1;
          }
        }
      }
    }
  }
}

.vehicle-detail-drawer {
  :deep(.el-drawer) {
    border-radius: 2px 0 0 2px;
    box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.10);
    overflow: hidden;
  }

  :deep(.el-drawer__header) {
    padding: 20px 20px 10px 20px !important;
    margin-bottom: 0 !important;
    border-bottom: none !important;
    background: white !important;
  }

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .vehicle-title {
      color: #232628;
      font-size: 18px;
      font-family: 'Bosch Sans', sans-serif;
      font-weight: 400;
      line-height: 18px;
      margin: 0;
    }

    .close-btn {
      padding: 0 !important;
      width: 20px;
      height: 20px;
      border: none !important;
      background: transparent !important;

      &:hover {
        color: #5755ff !important;
        background: transparent !important;
      }

      &:focus {
        background: transparent !important;
      }
    }
  }

  :deep(.el-drawer__body) {
    padding: 0 20px 20px 20px;
    background: white;
  }

  // 标签页样式

}
</style>

<style lang="scss">
// 全局样式，用于覆盖Element Plus的默认样式
.vehicle-detail-drawer {
  .el-drawer__header {
    padding: 20px 20px 10px 20px !important;
    margin-bottom: 0 !important;
    border-bottom: none !important;
    background: white !important;
  }
  .el-drawer__body {
    padding: 0 20px 20px 20px;
    background: white;
  }

  .el-button--text {
    padding: 0 !important;
    border: none !important;
    background: transparent !important;

    &:hover,
    &:focus {
      background: transparent !important;
    }
  }
    .vehicle-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 12px 0;
      height: 30px;

      .el-tabs__nav-wrap {
        &::after {
          background-color: #eff1f2;
        }
      }

      .el-tabs__item {
        font-size: 16px;
        font-family: 'Bosch Sans Global', sans-serif;
        color: #4e5256;
        font-weight: 400;
        padding: 6px 20px 4px 0;
        height: 32px;
        line-height: 22px;

        &.is-active {
          color: #5755ff;
          font-weight: 700;
        }
      }

      .el-tabs__active-bar {
        background-color: #5755ff;
        height: 2px;
      }
    }

    :deep(.el-tabs__content) {
      .detail-content {

           .card-title {
              color: #303133;
              font-size: 16px;
              font-family: 'Bosch Sans', sans-serif;
              font-weight: 700;
              line-height: 16px;
            }

        // 描述列表样式
        :deep(.el-descriptions) {
          .el-descriptions__label {
            color: #4e5256 !important;
            font-size: 12px !important;
            font-family: 'Bosch Office Sans', sans-serif !important;
            font-weight: 400 !important;
            line-height: 23px !important;
            width: 120px !important;
            min-width: 120px !important;
            max-width: 120px !important;
            text-align: left !important;
          }

          .el-descriptions__content {
            color: #232628 !important;
            font-size: 12px !important;
            font-family: 'Bosch Office Sans', sans-serif !important;
            font-weight: 400 !important;
            line-height: 23px !important;

            .no-data {
              color: #b2b9c0 !important;
            }
          }

          .el-descriptions__table {
            .el-descriptions__cell {
              &.is-bordered-label {
                width: 120px !important;
                min-width: 120px !important;
                max-width: 120px !important;
              }
            }
          }
        }

        .photo-grid {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;

          .photo-item {
            width: 148px;
            height: 148px;
            position: relative;
            background: #f8f8ff;
            border: 1px solid #ddddff;
            border-radius: 4px;
            cursor: pointer;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            .photo-image {
              width: 100%;
              height: 100%;
            }

            .photo-placeholder {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;

              .placeholder-icon {
                color: #b5b4eb;
                font-size: 32px;
              }
            }

            &:hover {
              border-color: #5755ff;
            }
          }
        }
      }


    }
  }

  // 确保描述列表标签列宽度一致
  .el-descriptions {
    .el-descriptions__label {
      color: #4e5256 !important;
      font-size: 12px !important;
      font-family: 'Bosch Office Sans', sans-serif !important;
      font-weight: 400 !important;
      line-height: 23px !important;
      width: 120px !important;
      min-width: 120px !important;
      max-width: 120px !important;
      text-align: left !important;
    }

    .el-descriptions__content {
      color: #232628 !important;
      font-size: 12px !important;
      font-family: 'Bosch Office Sans', sans-serif !important;
      font-weight: 400 !important;
      line-height: 23px !important;

      .no-data {
        color: #b2b9c0 !important;
      }
    }

    // 确保表格单元格的宽度设置
    .el-descriptions__table {
      .el-descriptions__cell {
        &.is-bordered-label {
          width: 180px !important;
          min-width: 180px !important;
          max-width: 180px !important;
        }
      }
    }
  }
}
</style>
