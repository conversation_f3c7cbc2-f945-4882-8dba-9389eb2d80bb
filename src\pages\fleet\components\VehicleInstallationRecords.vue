<template>
  <div class="vehicle-version-info">
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <div class="left-actions">
        <el-button class="add-version-btn" @click="handleAddVersion">
          <el-icon><Plus /></el-icon>
          新增装车记录
        </el-button>
        <el-button class="clone-version-btn" @click="handleCloneVersion" :disabled="!selectedVersion">
          <el-icon><DocumentCopy /></el-icon>
          克隆选中记录
        </el-button>
      </div>
      <div class="right-actions">
        <el-button class="delete-version-btn" @click="handleDeleteCurrent" :disabled="!selectedVersion">
          <el-icon><Delete /></el-icon>
          删除当前记录
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 左侧版本列表 -->
      <div class="version-sidebar" :class="{ 'collapsed': sidebarCollapsed }" v-if="versionList.length > 0">
        <!-- 收起/展开按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon>
            <ArrowLeft v-if="!sidebarCollapsed" />
            <ArrowRight v-if="sidebarCollapsed" />
          </el-icon>
        </div>
        <div
          v-for="version in versionList"
          :key="version.id"
          class="version-item"
          :class="{ 'active': selectedVersion?.id === version.id }"
          @click="selectVersion(version)"
        >
          <div class="version-content">
            <el-tooltip
              :content="`${version.name}`"
              placement="top"
              effect="dark"
            >
              <div class="version-name" :title="version.name || ''">
                <span v-if="!sidebarCollapsed">{{ version.name }}</span>
                <span v-else class="collapsed-text">{{version.name? version.name.substring(0, 6):'' }}</span>
              </div>
            </el-tooltip>
          </div>
          <div v-if="!sidebarCollapsed" class="version-time">{{ version.startTime}}</div>
          <div class="version-status-icon"  v-if="!version.perfect && !sidebarCollapsed">
            <div class="info-incomplete-tag">
              <svg class="warning-icon" aria-hidden="true">
                <use xlink:href="#svg-warning-outline" />
              </svg>
              <div class="tag-text">信息待完善</div>
            </div>
          </div>
          <div v-if="sidebarCollapsed && !version.perfect" class="version-status-icon">
            <el-tooltip
              content="信息不全不可用"
              placement="top"
              effect="dark"
            >
             <svg class="warning-icon collapsed" aria-hidden="true">
                <use xlink:href="#svg-warning-outline" />
              </svg>
            </el-tooltip>
          </div>
          <div v-if="selectedVersion?.id === version.id" class="active-indicator"></div>
        </div>
      </div>

      <!-- 右侧详情区域 -->
      <div class="version-details">
        <!-- 空状态 -->
        <div v-if="versionList.length === 0 && !loading" class="empty-state-container">
          <el-empty
            description="暂无装车记录"
            :image-size="120"
          >
            <template #image>
              <ltw-icon :icon-code="'el-icon-document'" style="font-size: 80px; color: #C0C4CC;"></ltw-icon>
            </template>
            <template #description>
              <p style="color: #909399; font-size: 14px; margin: 0;">暂无装车记录</p>
            </template>
          </el-empty>
        </div>

        <!-- 版本详情内容 -->
        <div v-else-if="selectedVersion" class="details-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <div class="section-header">
            <h3 class="section-title">基础信息</h3>
             <div class="header-buttons">
              <!-- 编辑模式按钮 -->
              <template v-if="isBasicInfoEditing">

                <el-button
                  type="success"
                  size="small"
                  @click="saveBasicInfo"
                >
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="cancelBasicInfoEdit"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </template>
              <!-- 查看模式按钮 -->
              <el-button
                v-else
                class="warning-tag"
                size="small"
                @click="startBasicInfoEdit"
              >
                <el-icon><Edit /></el-icon>

              </el-button>
            </div>
            <!-- <div class="header-buttons">
              <el-button
                type="warning"
                :class="['warning-tag', { 'editing-state': isBasicInfoEditing }]"
                @click="toggleBasicInfoEdit"
              >
                <ltw-icon :icon-code="isBasicInfoEditing ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>
              </el-button>
              <el-button
                v-if="isBasicInfoEditing"
                type="info"
                class="cancel-tag"
                @click="cancelBasicInfoEdit"
              >
                <ltw-icon icon-code="el-icon-close"></ltw-icon>
              </el-button>
            </div> -->
          </div>
          <div class="info-content">
            <el-form
              v-if="isBasicInfoEditing"
              ref="basicInfoForm"
              :model="editingBasicInfo"
              :rules="basicInfoRules"
              label-width="100px"
              size="small"
              class="basic-info-form"
            >
            <el-form-item label="版本名称：" prop="name">
            <ltw-input
              v-model="editingBasicInfo.name"
              placeholder="请输入版本名称"
              style="max-width:300px"
            />
          </el-form-item>
              <el-form-item label="车型版本：" prop="versionCascader">
                <el-cascader
                  v-model="editingBasicInfo.versionCascader"
                  :options="vehicleVersionCascaderOptions"
                  :props="cascaderProps"
                  placeholder="请选择车型版本"
                  filterable
                  clearable
                  @change="handleVersionCascaderChange"
                  class="edit-input"
                  :disabled="selectedVersion.status === 'released'"
                  style="width:100%"
                />
              </el-form-item>
               <el-form-item label="功能分类：" prop="useType">
                <el-select
                  v-model="editingBasicInfo.useType"
                  placeholder="请选择功能分类"
                  class="edit-input"
                  clearable
                  :disabled="selectedVersion.status === 'released'"
                  style="width:100%"
                >
                  <el-option
                    v-for="item in functionCategoryOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="开始时间：" prop="startTime">
                <el-date-picker
                  v-model="editingBasicInfo.startTime"
                  type="datetime"
                  placeholder="请选择开始时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="edit-input"
                  :disabled="selectedVersion.status === 'released'"
                  style="width:100%"
                  @change="handleStartTimeChange"
                />
              </el-form-item>
              <el-form-item label="结束时间：" prop="endTime">
                <el-date-picker
                  v-model="editingBasicInfo.endTime"
                  type="datetime"
                  placeholder="请选择结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="edit-input"
                  style="width:100%"
                  @change="handleEndTimeChange"
                />
              </el-form-item>
              <el-form-item label="发布时间：" prop="publishTime" v-if="selectedVersion.releaseTime">
                <el-date-picker
                  v-model="editingBasicInfo.releaseTime"
                  type="datetime"
                  placeholder="请选择发布时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="edit-input"
                  :disabled=" selectedVersion.status === 'released'"
                  style="width:100%"
                />
              </el-form-item>

              <el-form-item label="备注：" prop="description">
                <el-input
                  v-model="editingBasicInfo.description"
                  placeholder="请输入备注信息"
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :rows="2"
                  :disabled="selectedVersion.status === 'released'"
                  class="edit-textarea"
                  style="width:100%"
                />
              </el-form-item>
            </el-form>
            <div v-else class="info-display">
              <div class="info-row">
                <span class="info-label">版本名称：</span>
                <span class="info-value">{{ selectedVersion.name || '-'}}</span>
              </div>
              <div class="info-row">
                <span class="info-label">车型版本：</span>
                <span class="info-value">{{ selectedVersion.code }}({{selectedVersion.version}})</span>
                   <el-tag
                        v-if="selectedVersion.statusName"
                        :type="selectedVersion.status === 'draft' ? 'warning' : 'success'"
                        style="margin-left:10px"
                    >
                      {{ selectedVersion.statusName }}
                    </el-tag>
                <el-tooltip
                        effect="dark"
                        :content="$t('预览')"
                        placement="top"
                        :enterable="false"
                    >
                      <el-button size="small" type="primary" plain @click="previewFile()" style="margin-left:10px; border-radius: 2px; background: #5755FF;color:white">
                        <ltw-icon icon-code="el-icon-view"></ltw-icon>
                      </el-button>
                    </el-tooltip>
              </div>
               <div class="info-row">
                <span class="info-label">功能分类：</span>
                <span class="info-value">{{ getFunctionCategoryName(selectedVersion.useType) || '-'}} </span>
              </div>
              <div class="info-row">
                <span class="info-label">开始时间：</span>
                <span class="info-value">{{ selectedVersion.startTime  || '-'}} </span>
              </div>
              <div class="info-row">
                <span class="info-label">结束时间：</span>
                <span class="info-value">{{ selectedVersion.endTime  || '-'}} </span>
              </div>
              <div class="info-row" v-if="selectedVersion.releaseTime">
                <span class="info-label">发布时间：</span>
                <span class="info-value">{{ selectedVersion.releaseTime  || '-'}} </span>
              </div>
             
              <div class="info-row">
                <span class="info-label">备注：</span>
                <span class="info-value placeholder">{{ selectedVersion.description || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置信息 -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">配置信息</h3>
             <div class="header-buttons">
              <!-- 编辑模式按钮 -->
              <template v-if="isConfigEditing">

                <el-button
                  type="success"
                  size="small"
                  @click="saveConfigEdit"
                >
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="cancelConfigEdit"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </template>
              <!-- 查看模式按钮 -->
              <el-button
                v-else
                class="warning-tag"
                size="small"
                @click="startConfigEdit"
              >
                <el-icon><Edit /></el-icon>

              </el-button>
            </div>
            <!-- <div class="header-buttons">
              <el-button
                type="warning"
                :class="['warning-tag', { 'editing-state': isConfigEditing }]"
                @click="toggleConfigEdit"
              >
                <ltw-icon :icon-code="isConfigEditing ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>
              </el-button>
              <el-button
                v-if="isConfigEditing"
                type="info"
                class="cancel-tag"
                @click="cancelConfigEdit"
              >
                <ltw-icon icon-code="el-icon-close"></ltw-icon>
              </el-button>
            </div> -->
          </div>

          <!-- 传感器配置组件 -->
          <FleetSensorConfigSection
            :version-data="selectedVersion"
            :editable="isConfigEditing"
            @add-config="handleAddConfig"
            @edit-config="handleEditConfig"
            @delete-config="handleDeleteConfig"
            @view-config="handleViewConfig"
            @reload="handleFileReload"
          />

        </div>
        </div>
      </div>
    </div>

    <!-- 新增装车记录弹窗 -->
    <AddVehicleInstallationRecord
      ref="addInstallationRecordDialog"
      :vehicle-data="vehicleData"
      @finish="handleFinish"
      :variant-version-disabled = 'variantVersionDisabled'
    />
     <PreviewFile ref="PreviewFile" @reload="handleReloadPublish"></PreviewFile>
  </div>
</template>

<script>
import { Plus, Delete, DocumentCopy, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import FleetSensorConfigSection from './fleetSensorConfigSection.vue'
import {
  saveFtmVehicleInstallationRecord,
  updateFtmVehicleInstallationRecord,
  deleteFtmVehicleInstallationRecord,
  listFtmVehicleInstallationRecord,
  copyFtmVehicleInstallationRecord
} from '@/apis/fleet/ftm-vehicle-installation-record'
import {
  listFtmVehicleMappingModality,
  deleteFtmVehicleMappingModalitys
} from '@/apis/fleet/ftm-variant-mapping-modality'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { listFtmVehicleVariantCascade } from '@/apis/fleet/ftm-vehicle-variant'
import AddVehicleInstallationRecord from './AddVehicleInstallationRecord.vue'
import PreviewFile from '@/pages/fleet/dialog/PreviewFile.vue'

export default {
  name: 'VehicleVersionInfo',
  components: {
    Plus,
    Delete,
    DocumentCopy,
    ArrowLeft,
    ArrowRight,
    FleetSensorConfigSection,
    AddVehicleInstallationRecord,
    PreviewFile
  },
  props: {
    vehicleData: {
      type: Object,
      default: () => ({})
    }
  },
   emits: ['published'],
  data() {
    return {
      loading: false,
      selectedVersion: null,
      versionList: [],
      sidebarCollapsed: false, // 控制左侧版本列表收起状态
      versionDetail: [], // 存储版本详情数据
      showAddVersionDialog: false, // 控制新增版本对话框显示
      dialogMode: 'add', // 对话框模式：'add' | 'copy'
      cloneVersionData: null, // 要克隆的版本完整数据
      isBasicInfoEditing: false, // 控制基础信息编辑状态
      editingBasicInfo: { // 编辑中的基础信息数据
        version: '',
        startTime: '',
        endTime: '',
        releaseTime: '',
        useType: '',
        description: '',
        versionCascader: null,
        variantCode: '',
        variantVersionId: ''
      },
      basicInfoRules: { // 表单验证规则
        name: [
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ],
        versionCascader: [
          { required: true, message: '请选择车型版本', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
          { validator: this.validateStartTime, trigger: 'change' }
        ],
        endTime: [
          { validator: this.validateEndTime, trigger: 'change' }
        ],
        useType: [
          // 功能分类不是必填项，可以为空
        ]
      },
      functionCategoryOptions: [], // 功能分类选项
      vehicleVariantOptions: [], // 车型选项
      vehicleVersionCascaderOptions: [], // 车型版本级联选项
      cascaderProps: { // 级联选择器配置
        value: 'code',
        label: 'label', // 使用自定义的label字段
        children: 'children',
        emitPath: false // 只返回最后一级的值
      },
      isConfigEditing: false // 控制配置信息编辑状态
    }
  },

  watch: {
    // 监听vehicleData变化，加载版本列表
    'vehicleData.vin': {
      handler(newId, oldId) {
        if (newId !== oldId) {
          if (newId) {
            this.loadVersionList(newId)
          } else {
            this.versionList = []
            this.selectedVersion = null
            this.versionDetail = []
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    variantVersionDisabled() {
      return this.dialogMode === 'copy'
    }
  },
  mounted() {
    // mounted中不再重复调用，因为watch已经处理了初始化
    this.loadDictionaryData()
    this.loadVehicleVersionCascaderOptions()
    // 确保进入装车记录tab时默认是展开状态
    this.sidebarCollapsed = false
  },

  activated() {
    // 当组件被激活时（比如从其他tab切换回来），确保侧边栏是展开状态
    this.sidebarCollapsed = false
  },
  methods: {
    // 切换侧边栏收起状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 重置侧边栏状态为展开（供父组件调用）
    resetSidebarState() {
      this.sidebarCollapsed = false
    },

    // 加载字典数据
    async loadDictionaryData() {
      try {
        // 加载功能分类选项
        const functionCategoryRes = await listSysDictionary({ typeCode: 'use_type' })
        this.functionCategoryOptions = functionCategoryRes.data || []
      } catch (error) {
        console.error('加载字典数据失败:', error)
      }
    },

    // 加载版本列表
    async loadVersionList(vehicleId) {
      if (!vehicleId) return

      this.loading = true
      try {
        const res = await listFtmVehicleInstallationRecord({vin: vehicleId})
        this.versionList = res.data || []

        // 默认选中第一个版本
        if (this.versionList.length > 0) {
          this.selectVersion(this.versionList[0])
        } else {
          this.selectedVersion = null
          this.versionDetail = []
        }

        console.log('版本列表加载完成:', this.versionList)
      } catch (error) {
        console.error('加载版本列表失败:', error)
        //this.$message.error('加载版本列表失败')
        this.versionList = []
        this.selectedVersion = null
        this.versionDetail = []
      } finally {
        this.loading = false
      }
    },

    // 加载版本详情
    async loadVersionDetail(versionId) {
      if (!versionId) return

      this.loading = true
      try {
      let postData = {
        vehicleId:this.vehicleData.id,
        variantVersionId: this.selectedVersion.variantVersionId,
        installationRecordId: this.selectedVersion.id
      }
        const res = await  listFtmVehicleMappingModality(postData)
        this.versionDetail = res.data || []

        // 更新selectedVersion的详情数据
        if (this.selectedVersion) {
          this.selectedVersion = {
            ...this.selectedVersion,
            vehicleId:this.vehicleData.id,
            versionMappingModalityVOS:[...this.versionDetail]
          }
        }

      } catch (error) {
        console.error('加载版本详情失败:', error)
        //this.$message.error('加载版本详情失败')
        this.versionDetail = []
      } finally {
        this.loading = false
      }
    },

    // 选择版本
    selectVersion(version) {
      // 退出所有编辑模式
      this.exitAllEditModes()

      this.selectedVersion = version

      // 加载该版本的详情
      if (version?.id) {
        this.loadVersionDetail(version.id)
      }
    },

    // 退出所有编辑模式
    exitAllEditModes() {
      // 退出基础信息编辑模式
      if (this.isBasicInfoEditing) {
        this.cancelBasicInfoEdit()
      }

      // 退出配置编辑模式
      if (this.isConfigEditing) {
        this.cancelConfigEdit()
      }
    },

    handleVersionCheck() {
      // 处理版本选择
    },

    handleAddVersion() {
      this.dialogMode = 'add';
      // 打开新增装车记录弹窗
      this.$refs.addInstallationRecordDialog.openDialog()
    },

    // 处理新增装车记录完成
    handleFinish() {
      // 重新加载装车记录列表
      this.loadVersionList(this.vehicleData.vin)
    },

    async handleCloneVersion() {
      this.dialogMode = 'copy';
      if (!this.selectedVersion) {
        this.$message.warning('请先选择要克隆的版本')
        return
      }

      try {
        // 准备预填充数据，包含当前选中记录的车型版本信息
        const prefilledData = {
          originalRecordId: this.selectedVersion.id, // 原始记录ID，用于克隆API
          version: this.selectedVersion.version,
          code: this.selectedVersion.code,
          variantVersionId: this.selectedVersion.variantVersionId,
          useType: this.selectedVersion.useType,
          description: this.selectedVersion.description,
          id:this.selectedVersion.id,
          startTime:this.selectedVersion.endTime?this.selectedVersion.endTime:'',
          name:this.selectedVersion.name || '',
          versionCascader: this.selectedVersion.variantVersionId
        }

        // 打开新增装车记录弹窗并传递预填充数据
        this.$refs.addInstallationRecordDialog.openDialog(prefilledData)
      } catch (error) {
        console.error('克隆版本失败:', error)
       // this.$message.error('克隆版本失败，请重试')
      }
    },

    handleDeleteCurrent() {
      if (!this.selectedVersion) {
        this.$message.warning('请先选择要删除的版本')
        return
      }

      this.$confirm(`确定要删除版本 "${this.selectedVersion.version}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
       deleteFtmVehicleInstallationRecord({id:this.selectedVersion.id}).then(res =>{
           this.$message.success('删除成功')
           this.loadVersionList(this.vehicleData.vin)
        })
       
        
      }).catch(() => {
        // 取消删除
      })
    },
    //处理发布后更新
    handleReloadPublish(){
       this.loadVersionDetail(this.selectedVersion.id)
      this.updateSelectedVersion()
      this.$emit('published')
    },

    // 处理新增配置 - 从子组件传递过来的事件
    async handleAddConfig() {
      this.loadVersionDetail(this.selectedVersion.id)
      this.updateSelectedVersion()
    },
    async updateSelectedVersion() {
      const res = await listFtmVehicleInstallationRecord({vin:this.vehicleData.vin})
      this.versionList = res.data || []
      const item = this.versionList.find(item => item.id === this.selectedVersion.id)
      if(item){
        // 使用对象展开运算符确保响应式更新
        this.selectedVersion = {
          ...this.selectedVersion,
          ...item,
          modalityTypeMap: item.modalityTypeMap
        }
      }
    },

    // 处理编辑配置 - 从子组件传递过来的事件
    async handleEditConfig() {
       this.loadVersionDetail(this.selectedVersion.id) 
       this.updateSelectedVersion()
    },

    // 处理删除配置 - 从子组件传递过来的事件
    async handleDeleteConfig(row) {
      this.loadVersionDetail(this.selectedVersion.id)
      this.updateSelectedVersion()
    },

    // 处理查看配置 - 从子组件传递过来的事件
    handleViewConfig() {
    },
    //上传文件
    handleFileReload(){
      this.loadVersionDetail(this.selectedVersion.id)
    },

    // 切换基础信息编辑状态
    toggleBasicInfoEdit() {
      if (this.isBasicInfoEditing) {
        // 保存编辑
        this.saveBasicInfo()
      } else {
        // 进入编辑模式
        this.startBasicInfoEdit()
      }
    },

    // 开始编辑基础信息
    startBasicInfoEdit() {
      if (!this.selectedVersion) return

      console.log('开始编辑基础信息，selectedVersion:', this.selectedVersion)

      this.isBasicInfoEditing = true
      this.editingBasicInfo = {...this.selectedVersion}

      console.log('复制后的editingBasicInfo:', this.editingBasicInfo)

      // 设置级联选择器的值 - 根据当前版本信息查找对应的版本ID
      this.setVersionCascaderValue()
    },

    // 设置级联选择器的值
    setVersionCascaderValue() {
      console.log('设置级联选择器值，selectedVersion:', this.selectedVersion)

      if (!this.selectedVersion.variantVersionId) {
        console.log('variantVersionId不存在，无法设置级联选择器值')
        return
      }

      // 使用版本ID作为级联选择器的值
      this.editingBasicInfo.versionCascader = this.selectedVersion.variantVersionId
      console.log('设置级联选择器值为:', this.selectedVersion.variantVersionId)
    },

    // 取消编辑基础信息
    cancelBasicInfoEdit() {
      this.isBasicInfoEditing = false
      this.editingBasicInfo = {
        version: '',
        startTime: '',
        endTime: '',
        releaseTime: '',
        useType: '',
        description: '',
        versionCascader: null
      }
      // 清除表单验证
      if (this.$refs.basicInfoForm) {
        this.$refs.basicInfoForm.clearValidate()
      }
    },

    // 保存基础信息
    async saveBasicInfo() {
      if (!this.selectedVersion) return

      // 表单验证
      try {
        await this.$refs.basicInfoForm.validate()

      } catch (error) {

        return
      }

      try {
        updateFtmVehicleInstallationRecord(this.editingBasicInfo).then(res => {
          this.selectedVersion = this.editingBasicInfo
          this.isBasicInfoEditing = false
          this.$message.success('保存成功')
          this.loadVersionDetail(this.selectedVersion.id)
          this.updateSelectedVersion()
        })
      } catch (error) {
        console.error('保存基础信息失败:', error)
        //this.$message.error('保存失败，请重试')
      }
    },

    // 切换配置编辑状态
    toggleConfigEdit() {
      if (this.isConfigEditing) {
        // 保存配置编辑
        this.saveConfigEdit()
      } else {
        // 进入配置编辑模式
        this.startConfigEdit()
      }
    },

    // 开始配置编辑
    startConfigEdit() {
      this.isConfigEditing = true
    },

    // 取消配置编辑
    cancelConfigEdit() {
      this.isConfigEditing = false
    },

    // 保存配置编辑
    saveConfigEdit() {
      this.isConfigEditing = false
    },

    // 处理新增版本确认
    handleAddVersionConfirm() {
      this.loadVersionList(this.vehicleData.vin)
    },

    // 获取功能分类名称
    getFunctionCategoryName(code) {
      if (!code || !this.functionCategoryOptions.length) return ''
      const option = this.functionCategoryOptions.find(item => item.code === code)
      return option ? option.name : code
    },

    // 加载车型版本级联选项
    async loadVehicleVersionCascaderOptions() {
      try {
        const res = await listFtmVehicleVariantCascade()
        // 处理数据，为每个节点添加label字段
        this.vehicleVersionCascaderOptions = this.processVehicleVariantData(res.data || [])
      } catch (error) {
        console.error('加载车型版本级联选项失败:', error)
        this.vehicleVersionCascaderOptions = []
      }
    },

    // 处理车型版本数据，添加label字段
    processVehicleVariantData(data) {
      return data.map(variant => {
        const processedVariant = {
          ...variant,
          label: variant.code, // 第一层使用code作为显示标签
          code: variant.code,  // 第一层的值使用code
          children: variant.children ? variant.children.map(version => ({
            ...version,
            label: version.version, // 第二层使用version作为显示标签
            code: version.id   // 第二层的值使用id
          })) : []
        }
        return processedVariant
      })
    },

    // 处理级联选择器变化
    handleVersionCascaderChange(value) {
      if (value) {
        // 根据选中的版本ID，从级联选项中找到对应的车型和版本信息
        this.findVersionInfoById(value)
      } else {
        // 清空时重置相关字段
        this.editingBasicInfo.version = ''
        this.editingBasicInfo.variantCode = ''
        this.editingBasicInfo.variantVersionId = ''
      }
    },

    // 根据版本ID查找车型和版本信息
    findVersionInfoById(versionId) {
      for (const variant of this.vehicleVersionCascaderOptions) {
        if (variant.children) {
          for (const version of variant.children) {
            if (version.id === versionId) {
              // 第一级：将code字段赋值给variantCode
              this.editingBasicInfo.variantCode = variant.code
              // 第二级：记录version字段和id字段
              this.editingBasicInfo.version = version.version
              this.editingBasicInfo.variantVersionId = version.id
              return
            }
          }
        }
      }
    },

    // 处理开始时间变化
    handleStartTimeChange() {
      // 当开始时间改变时，重新校验结束时间
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm && this.editingBasicInfo.endTime) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    // 处理结束时间变化
    handleEndTimeChange() {
      // 当结束时间改变时，立即进行校验
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    // 校验开始时间
    validateStartTime(rule, value, callback) {
      callback()
      // 当开始时间改变时，重新校验结束时间
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm && this.editingBasicInfo.endTime) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    // 校验结束时间
    validateEndTime(rule, value, callback) {
      if (!value) {
        // 结束时间不是必填项，如果为空则通过校验
        callback()
        return
      }

      const startTime = this.editingBasicInfo.startTime
      if (!startTime) {
        // 如果开始时间为空，则不进行比较校验
        callback()
        return
      }

      // 将时间字符串转换为 Date 对象进行比较
      const startDate = new Date(startTime)
      const endDate = new Date(value)

      if (endDate <= startDate) {
        callback(new Error('结束时间不能早于开始时间'))
      } else {
        callback()
      }
    },
    previewFile() {
     const btn ={
      buttonCode: "revoke",
      buttonName: "撤回"
     }
      this.$refs.PreviewFile.show({
        type: 'view',
        id: this.selectedVersion.id,
        vin: this.vehicleData.vin,
        // version: this.formDataRecord.version,
        status: this.selectedVersion.status,
        statusName: this.selectedVersion.statusName,
        btn: this.selectedVersion.status === 'released' && btn
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.vehicle-version-info {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      display: flex;
      gap: 8px;
    }

    .add-version-btn {
      padding: 6px 11px;
      background: #F5F5FF;
      border: 1px solid #DDDDFF;
      border-radius: 2px;
      color: #5755FF;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;

      &:hover {
        background: #EEEEFF;
        border-color: #CCCCFF;
      }
    }

    .clone-version-btn {
      padding: 6px 11px;
      background: #F5F5FF;
      border: 1px solid #DDDDFF;
      border-radius: 2px;
      color: #5755FF;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;

      &:hover {
        background: #EEEEFF;
        border-color: #CCCCFF;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .delete-version-btn {
      padding: 6px 11px;
      background: #FF6E6F;
      border: 1px solid #FF6E6F;
      border-radius: 2px;
      color: white;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;
      &:hover {
        background: #FF5A5B;
        border-color: #FF5A5B;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .main-content {
    display: flex;
    background: white;
    border-radius: 2px;
    gap: 24px;
    margin-bottom: 12px;

    .version-sidebar {
      width: 192px;
      min-width: 192px;
      max-width: 192px;
      border-right: 1px solid #EFF1F2;
      transition: all 0.3s ease;

      // 收起状态
      &.collapsed {
        width: 60px;
        min-width: 60px;
        max-width: 60px;

        .version-item {
          padding: 8px 6px 8px 4px;
          justify-content: center;

          .version-content {
            justify-content: center;

            .version-name {
              text-align: center;
              max-width: 100%;
            }
          }
        }
        .sidebar-toggle{
          left: 71px;
        }
      }

      // 切换按钮
      .sidebar-toggle {
        position: absolute;
        top: 404px;
        left:202px;
        width: 24px;
        height: 24px;
        background: #fff;
        border: 1px solid #EFF1F2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f7fa;
          border-color: #c0c4cc;
        }

        .el-icon {
          font-size: 12px;
          color: #606266;
        }
      }

      .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #909399;
        font-size: 14px;
      }

      .version-item {
        align-items: center;
        position: relative;
        cursor: pointer;
        background: white;
        padding: 8px 12px 8px 6px;
        transition: all 0.3s ease;
        .warning-icon {
            width: 10px;
            height: 10px;
            fill: #E47A01;
            flex-shrink: 0;
            cursor: help;
            margin-left: 4px; // 图标左侧间距
           &.collapsed {
              width: 14px;
              height: 14px;
              margin-right: 10px;
            }
          }

        &.active {
          background: #F5F5FF;

          .version-content .version-name {

            color: #5755FF;
            font-weight: 700;
            text-align: end;
          }

          .active-indicator {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #5755FF;
          }
          .version-time{
              color: #5755FF;
          }
        }

        .version-content {
          display: flex;
          align-items: center;
          justify-content: flex-end; // 整体内容靠右对齐
          flex: 1;
          width: 100%;
          overflow: hidden; // 确保容器能处理溢出
      


          .version-name {
            padding:4px 0;
            color: #4E5256;
            font-size: 12px;
            font-family: 'Bosch Sans Global', sans-serif;
            font-weight: 400;
            line-height: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            flex-shrink: 1; // 允许收缩
            text-align: right; // 文字右对齐

            // 收起状态下的文本样式
            .collapsed-text {
              font-size: 10px;
              font-weight: 600;
              text-align: center;
              display: block;
              width: 100%;
            }
          }
        }
        .version-time{
          text-align: right;
          font-weight: 400;
          font-size: 10px;
          color:#4E5256;
        }

        .version-status-icon {
          display: flex;
          flex-direction: row-reverse;
          margin-top: 3px;
          .info-incomplete-tag {
            width: 68px;
            height: 14px;
            position: relative;
            background: #FFEFD1;
            overflow: hidden;
            border-radius: 8px;
            outline: 1px #FFDF95 solid;
            outline-offset: -1px;
            display: flex;
            align-items: center;


            .tag-text {
              width: 48px;
              height: 13px;
              left: 14px;
              top: 0px;
              position: absolute;
              text-align: center;
              justify-content: center;
              display: flex;
              flex-direction: column;
              color: #E47A01;
              font-size: 9px;
              font-family: Inter;
              font-weight: 400;
              line-height: 13.50px;
              word-wrap: break-word;
            }

            .tag-icon {
              width: 10px;
              height: 10px;
              left: 3px;
              top: 2px;
              position: absolute;
            }
          }
        }
      }
    }

    .version-details {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: 24px;

      .empty-state-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 400px;

        :deep(.el-empty) {
          .el-empty__image {
            margin-bottom: 20px;
          }

          .el-empty__description {
            margin-top: 16px;
          }
        }
      }
      .warning-tag {
            background: #FFB03A;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 22px;
            width: 30px;
            transition: background-color 0.3s ease;

            &:hover {
              background: #E6690A;
            }

            // 编辑状态下的绿色样式
            &.editing-state {
              background: #67C23A;

              &:hover {
                background: #5DAE34;
              }
            }
          }

          .cancel-tag {
            background: #909399;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 22px;
            width: 30px;
            margin-left: 8px;

            &:hover {
              background: #73767A;
            }
          }

      .info-section {

        .section-header {
          display: flex;
          align-items: center;
          margin-bottom:24px;
          gap: 10px;


          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 16px;
            margin: 0;
          }

          .header-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
          }
        }
        


        .info-content {
          padding-bottom: 24px;
          border-bottom: 1px solid #EBEEF5;

          .basic-info-form {
            :deep(.el-form-item) {
              margin-bottom: 16px;
              min-height: 32px;

              .el-form-item__label {
                color: #4E5256;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
                font-weight: 400;
                line-height: 32px;
              }

              .el-form-item__content {
                .edit-input {
                  max-width: 300px;
                  height: 32px;
                  :deep(.el-input__wrapper) {
                    border-radius: 2px;
                    border: 1px solid #DCDFE6;

                    &:hover {
                      border-color: #C0C4CC;
                    }

                    &.is-focus {
                      border-color: #5755FF;
                      box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                    }
                  }
                }

                .edit-textarea {
                  max-width: 300px;

                  :deep(.el-textarea__inner) {
                    border-radius: 2px;
                    border: 1px solid #DCDFE6;
                    font-family: inherit;
                    resize: none;

                    &:hover {
                      border-color: #C0C4CC;
                    }

                    &:focus {
                      border-color: #5755FF;
                      box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                    }
                  }
                }

                :deep(.el-date-editor) {
                  max-width: 300px;

                  .el-input__wrapper {
                    border-radius: 2px;
                    border: 1px solid #DCDFE6;

                    &:hover {
                      border-color: #C0C4CC;
                    }

                    &.is-focus {
                      border-color: #5755FF;
                      box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                    }
                  }
                }

                :deep(.el-select) {
                  max-width: 300px;

                  .el-input__wrapper {
                    border-radius: 2px;
                    border: 1px solid #DCDFE6;

                    &:hover {
                      border-color: #C0C4CC;
                    }

                    &.is-focus {
                      border-color: #5755FF;
                      box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
                    }
                  }
                }
              }
            }
          }

          .info-display {
            .info-row {
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              gap:12px;

              &:last-child {
                margin-bottom: 0;
              }

              .info-label {
                color: #4E5256;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
                font-weight: 400;
                line-height: 23px;
                width:60px;
                text-align: right;
              }

              .info-value {
                color: #232628;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
                font-weight: 400;
                line-height: 23px;

                &.placeholder {
                  color: #B2B9C0;
                }
              }
            }
          }
        }
      }

      .config-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top:20px;


        .section-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          gap:10px;
          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 16px;
            margin: 0;

          }

          .header-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
          }
        }

        // 传感器配置相关样式已移至 SensorConfigSection 组件
      }

      .software-section {
        .section-header {
          margin-bottom: 16px;

          .section-title {
            color: #303133;
            font-size: 16px;
            font-family: 'Bosch Sans', sans-serif;
            font-weight: 700;
            line-height: 16px;
            margin: 0;
          }
        }

        .software-table {
          :deep(.el-table) {
            font-size: 12px;

            .el-table__header {
              th {
                background: #FAFAFC;
                color: #909399;
                font-size: 12px;
                font-family: 'Bosch Office Sans', sans-serif;
                font-weight: 400;
                line-height: 23px;
                border-bottom: 1px solid #EBEEF5;
              }
            }

            .el-table__body {
              td {
                color: #606266;
                border-bottom: 1px solid #EBEEF5;
                padding: 8px 16px;
              }
            }
          }
        }
      }
    }
  }

  // 新增装车记录容器样式
  .add-installation-record-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-top: 20px;
  }
}

/* 全局表单元素32px高度样式 */
:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-select) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

:deep(.el-date-editor) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

:deep(.el-cascader) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

:deep(.el-textarea__inner) {
  line-height: 1.5;
  min-height: 64px; /* 2行的高度 */
}
</style>
