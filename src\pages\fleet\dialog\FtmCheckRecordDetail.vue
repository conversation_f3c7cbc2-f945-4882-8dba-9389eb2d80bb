<template>
  <el-dialog
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    :close-on-click-modal="false"
    @open="dialogOpened"
    append-to-body
    :draggable="false"
  >
    <template #header>
      <span class="bigTitle">{{ dialogTitle }}</span>
    </template>
    <el-form :inline="true" :model="form" ref="formRef" label-width="100px">
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item>
          <template v-slot:label> 车辆 </template>
          {{ form.vin }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template v-slot:label>
            <i class="el-icon-user"></i>
            合规员
          </template>
          {{ form.complianceOfficerEmpName }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template v-slot:label> 时间 </template>
          {{ form.createTime }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template v-slot:label>
            <i class="el-icon-user"></i>
            是否采集
          </template>
          {{ showDataCollected }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template v-slot:label>
            <i class="el-icon-user"></i>
            备注
          </template>
          {{ form.remark }}
        </el-descriptions-item>
      </el-descriptions>
      <el-card v-for="(itemList, groupName) in form.itemVOMap" shadow="hover" :key="groupName" style="margin: 10px 0">
        <template #header>
          <span style="font-weight: bold"> {{ groupName }}</span>
        </template>
        <template v-for="(item, index) in itemList" :key="item.id">
          <el-row class="row" style="display: flex; flex-direction: row; justify-content: space-between">
            <span class="smallTitle">{{ item.name }}</span>
            <span class="smallTitle">{{ index + 1 }}/{{ itemList.length }}</span>
          </el-row>
          <el-row class="row">
            <el-col :span="4" class="columnLabel"> 检查内容 </el-col>
            <el-col :span="16">{{ item.checkContent }}</el-col>
            <el-col :span="4" :offset="0">
              <el-tag type="danger" v-if="item.fException">异常</el-tag>
              <el-tag type="success" v-else>正常</el-tag>
            </el-col>
          </el-row>
          <el-row v-if="item.checkPeriod" class="row">
            <el-col :span="4" class="columnLabel">检查周期</el-col>
            <el-col :span="20">{{ item.checkPeriod }}h </el-col>
          </el-row>

          <el-row class="row" v-if="item.sysFileVOS?.length > 0">
            <el-col :span="4" class="columnLabel">异常附件</el-col>
            <el-col :span="20">
              <upload-file
                  ref="uploadImage"
                  source-type="mini_app_item_image"
                  accept=".jpg,.jpeg,.png,.gif"
                  :source-id="item.detailId"
                  id="photo"
                  disabled
              />
            </el-col>
          </el-row>

          <el-row v-if="item.remark" class="row">
            <el-col :span="4" class="columnLabel">异常原因</el-col>
            <el-col :span="20">
              {{ item.remark }}
            </el-col>
          </el-row>
          <el-divider></el-divider>
        </template>
      </el-card>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import util from '@/plugins/util.js'
import UploadFile from '@/components/system/UploadFile.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import { getFtmCheckRecord } from '@/apis/fleet/ftm-check-record'

const defaultform = {}
export default {
  name: 'FtmCheckRecordDetail',
  components:{UploadFile},
  emits: ['reload'],
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken(),
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform)
    }
  },
  computed: {
    showDataCollected() {
      return this.form.dataCollected ? '是' : '否'
    },
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.getFtmCheckRecord(row)
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    getFtmCheckRecord(row) {
      getFtmCheckRecord(row.id).then(res => {
        this.form = res.data
        if (this.form.itemVOMap) {
          for (const key in this.form.itemVOMap) {
            const itemList = this.form.itemVOMap[key]
            itemList.forEach(item => {
              if (item && item.sysFileVOS?.length > 1) {
                const previewSrcList = item.previewSrcList || []
                item.sysFileVOS.forEach(f => {
                  previewSrcList.push(this.downloadUrl + f.id + this.token)
                })
                item.previewSrcList = previewSrcList
              }
            })
          }
        }
        this.dialogTitle = this.$t(this.form.groupName)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.bigTitle {
  margin-bottom: 20px;
  font-weight: bolder;
  font-size: large;
}

.smallTitle {
  font-weight: bolder;
  font-size: larger;
}

.columnLabel {
  font-weight: bolder;
}

.row {
  margin: 10px;
}
</style>
