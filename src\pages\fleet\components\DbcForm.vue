<template>
  <el-form v-if="form.edit" :model="form" :rules="formRules" ref="itemFormRef" label-width="100px">
    <el-row>
      <el-col :span="12">
        <el-form-item :label="$t('有效日期')" prop="activationDate">
          <el-date-picker
            v-model="form.activationDate"
            type="date"
            :placeholder="$t('有效日期')"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('失效日期')" prop="deactivationDate">
          <el-date-picker
            v-model="form.deactivationDate"
            :disabled-date="disabledEndDate"
            type="date"
            :placeholder="$t('失效日期')"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('版本')" prop="version">
          <ltw-input :limitSize="10" v-model="form.version" :disabled="formReadonly" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('是否启用')" id="enable" prop="enable">
          <el-switch
            v-model="form.enable"
            inline-prompt
            :active-text="$t('是')"
            :inactive-text="$t('否')"
            id="enable"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('DBC文件')">
          <el-button circle size="small" @click="chooseDbc()">
            <el-icon>
              <CirclePlus />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item v-if="fileNameList?.length" :label="$t('已选文件')" id="enable">
      <div v-for="file in fileNameList">
        <span>{{ file }}</span> &nbsp;&nbsp;&nbsp;
      </div>
    </el-form-item>
  </el-form>
  <el-row v-else>
    <el-table :data="item.vehicleDbcVOList">
      <el-table-column header-align="center" align="center" prop="activationDate" :label="$t('生效日期')">
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="deactivationDate" :label="$t('失效日期')">
      </el-table-column>
      <el-table-column header-align="center" align="center" prop="fileName" :label="$t('dbc文件')"> </el-table-column>
      <el-table-column header-align="center" align="center" prop="activationDate" :label="$t('操作')" v-if="operationType!=='view'">
        <template #default="scope">
          <div class="footer">
            <el-link type="primary" @click="downloadFile(scope.row.fileId)" :underline="false">
              {{ $t('下载') }}
            </el-link>
            <el-link type="danger" @click="singleRemoveForm(scope.row.id)" :underline="false">{{ $t('删除') }}</el-link>
            <el-link type="info" @click="getFormDetail(scope.row)" :underline="false">{{ $t('详情') }}</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-row>

  <div class="footer">
    <span v-if="form.edit">
      <el-button :id="index + 'cancel'" size="small" @click="cancelForm()">{{ $t('取消') }}</el-button>
      <el-button :id="index + 'save'" size="small" type="primary" @click="submit">{{ $t('保存') }}</el-button>
    </span>
  </div>
  <ChooseDbc @reloadDbcList="reloadDbcList" ref="ChooseDbc"></ChooseDbc>
  <ftm-dbc-detail ref="FtmDbcDetail" />
</template>

<script>
import FtmDbcDetail from '@/pages/fleet/dialog/FtmDbcDetail.vue'
import { i18n } from '@/plugins/lang'
import LtwInput from '@/components/base/LtwInput'
import UploadFile from '@/components/system/UploadFile.vue'
import ChooseDbc from '@/pages/fleet/dialog/ChooseDbc.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { showConfirmToast, showToast } from '@/plugins/util.js'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { saveFtmVehicleDbc, deleteFtmVehicleDbc } from '@/apis/fleet/ftm-vehicle-dbc'
import {
  ElForm,
  ElFormItem,
  ElButton,
  ElRow,
  ElCol,
  ElInputNumber,
  ElDatePicker,
  ElOption,
  ElSelect

} from 'element-plus'

export default {
  name: 'DbcForm',
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      selectedDbcList: [],
      fileNameList: [],
      $t: i18n.global.t,
      form: {},
      dialogStatus: '',
      formRules: {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        deactivationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        version: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        enable: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
    }
  },
  emits: ['reload'],
  props: {
    operationType:{
      type:String,
      default:''
    },
    item: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    options: {
      type: Array,
      default: []
    },
    status: {
      type: String,
      default: 'add'
    },
    variant: {
      type: String
    },
    vin: {
      type: String
    },
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    item: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
        this.form.enable = true
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElRow,
    ElCol,
    ElInputNumber,
    ElDatePicker,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    UploadFile,
    ChooseDbc,
    FtmDbcDetail
  },
  methods: {
    downloadFile(sourceId) {
      window.open(this.downloadUrl + sourceId + '?token=' + util.getToken())
    },
    reloadDbcList(selectedDbcList) {
      this.selectedDbcList = []
      this.fileNameList = []
      this.selectedDbcList = selectedDbcList
      this.fileNameList = selectedDbcList.map(val => val.fileName)
    },
    chooseDbc() {
      this.$refs.ChooseDbc.show({
        type: 'view',
        data: {
          variant: this.variant
        }
      })
    },
    show(row) {
      this.dialogVisible = true
    },
    submit() {
      if (!this.selectedDbcList?.length) {
        showToast('请先选择dbc文件', 'warning')
        return
      }
      this.$refs.itemFormRef.validate(valid => {
        if (valid) {
          let postData = {
            ...this.form,
            vin: this.vin,
            dbcIdList: this.selectedDbcList.map(val => val.id)
          }
          saveFtmVehicleDbc(postData).then(res => {
            this.saveForm()
          })
        }
      })
    },
    cancelForm() {
      if (this.form.id) {
        this.$emit('reload', {
          index: this.index,
          type: 'cancel'
        })
      } else {
        this.$emit('reload', {
          form: this.form,
          index: this.index,
          type: 'delete'
        })
      }
    },
    saveForm() {
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'add'
      })
    },
    editForm() {
      this.form.edit = true
      this.form.dialogStatus = 'edit'
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'edit'
      })
    },
    getFormDetail(row) {
      this.$refs.FtmDbcDetail.show({ dbcId: row.dbcId, fileName: row.fileName })
    },
    singleRemoveForm(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmVehicleDbc({ id: id }).then(() => {
          this.$emit('reload', {
            type: 'delete'
          })
          showToast('dbc配置已删除')
        })
      })
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return new Date(val) < new Date(this.form.activationDate).getTime() - 1000 * 60 * 60 * 24
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.form-item {
  line-height: 32px;
  // color: #606266;
}

.file-row {
  display: flex;

  .form-item {
    white-space: nowrap;
  }
}

.footer {
  position: absolute;
  bottom: 8px;
  right: 20px;
  text-align: right;

  .el-link {
    margin-left: 6px;
  }
}
</style>
