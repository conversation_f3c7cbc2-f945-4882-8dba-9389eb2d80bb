<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-tool-container">
          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
          </el-input>
        </div>
        <div class="ltw-tool-container">
          <el-date-picker
              clearable
              :disabled="formReadonly"
              v-model="dateRange"
              type="daterange"
              :range-separator="$t('到')"
              :start-placeholder="$t('开始日期')"
              :end-placeholder="$t('结束日期')"
              value-format="YYYY-MM-DD"
          />
        </div>
        <div class="ltw-tool-container">
          <el-cascader
              filterable
              clearable
              placeholder="请选择区域"
              popper-class="canton-list"
              v-model="queryParam.cantonCode"
              :options="cantonCodeList"
              :props="props"
          />
        </div>
        <div class="ltw-tool-container">
          <el-select
              filterable
              v-model="queryParam.type"
              clearable
              placeholder="请选择日报类型"
          >
            <el-option
                v-for="item in workReportTypeList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            />
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button @click="refresh" type="primary">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
            查询
          </el-button>
          <el-button :type="item.buttonStyleType" :key="item.id" v-for="item in outlineFunctionList"
                     @click="executeButtonMethod(item)">
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown @command="handleCommand" class="batch-operate-btn"
                       v-if="batchingFunctionList && batchingFunctionList.length>0">
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table :data="pageData.records" stripe border @selection-change="handleSelectionChange" row-key="id"
                ref="tableRef">
        <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
        <el-table-column header-align="center" fixed="left" align="center" prop="acquisitionDate" width="110"
                         :label="$t('采集日期')">
          <template #default="scope">
            <el-tag type="success"><span>{{ scope.row.acquisitionDate }}</span></el-tag>
          </template>
        </el-table-column>

        <el-table-column header-align="center" fixed="left" align="center" prop="vin" width="160"
                         :label="$t('车辆编号')">
          <template #default="scope">
            <el-tag type="primary"><span>{{ scope.row.vin }}</span>
              <span v-if="scope.row.externalVin">({{ scope.row.externalVin }})</span></el-tag>
          </template>
        </el-table-column>
        <!--        <el-table-column header-align="center" align="center" prop="type" :label="$t('类型')"-->
        <!--                         width="95"></el-table-column>-->
        <!--        <el-table-column header-align="center" align="center" prop="externalVin"-->
        <!--                         :label="$t('外部车架号')"></el-table-column>-->
        <el-table-column header-align="center" align="center" prop="complianceOfficerEmpName" width="90"
                         :label="$t('合规员')">
          <template #default="scope">
            <el-tag type="warning"><span>{{ scope.row.complianceOfficerEmpName }}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="driver" :label="$t('司机')" width="90">
          <template #default="scope">
            <el-tag><span>{{ scope.row.driver }}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="nameLink" :label="$t('区域')" width="180"
                         show-overflow-tooltip>
          <template #default="scope">
            <el-tag><span>{{ scope.row.nameLink }}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="startMileage"
                         :label="$t('开始里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="endMileage"
                         :label="$t('结束里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="drivingMileage"
                         :label="$t('行驶里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="planMileage"
                         :label="$t('规划里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="highwayMileage"
                         :label="$t('高速里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="elevatedMileage"
                         :label="$t('高架里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="effectiveMileage"
                         :label="$t('当日有效里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="workDuration"
                         :label="$t('工作时长')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="planDuration"
                         :label="$t('计划时长')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="effectiveDuration"
                         :label="$t('当日有效时长')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="failureDuration"
                         :label="$t('故障时长')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="chargeDuration"
                         :label="$t('充电时长')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="dataAmount" :label="$t('数据量')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="planRampNumber"
                         :label="$t('规划匝道数')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="rampNumber" :label="$t('匝道数')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="bridgeNumber"
                         :label="$t('桥梁数')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="tunnelNumber"
                         :label="$t('隧道数')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="specialScenario"
                         :label="$t('是否特殊场景')">
          <template #default="scope">
            <el-tag type="primary"><span>{{ scope.row.specialScenario ? '是' : '否' }}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="rainMileage"
                         :label="$t('下雨里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="snowMileage"
                         :label="$t('下雪里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="frogMileage"
                         :label="$t('起雾里程')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="workEfficiency"
                         :label="$t('当日工效')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="defectNumber"
                         :label="$t('defect数量')"></el-table-column>
        <el-table-column header-align="center" align="center" width="150" prop="remark" show-overflow-tooltip
                         :label="$t('OPL')"></el-table-column>
        <el-table-column header-align="center" align="center" :label="$t('操作')" min-width="90" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="$t(item.name)"
                          placement="top" :enterable="false"
              >
                <el-button :type="item.buttonStyleType" size="mini"
                           @click="executeButtonMethod(item,scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParam.current"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="queryParam.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total">
      </el-pagination>
    </el-card>

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
      <el-form :model="formData" :rules="formRules" ref="formRef"
               label-width="100px">
        <el-form-item :label="$t('类型：E2E')" prop="type">
          <el-input v-model="formData.type" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('采集日期')" prop="acquisitionDate">
          <el-input v-model="formData.acquisitionDate" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('车架号')" prop="vin">
          <el-input v-model="formData.vin" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('合规员姓名')" prop="complianceOfficerEmpName">
          <el-input v-model="formData.complianceOfficerEmpName" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('司机')" prop="driver">
          <el-input v-model="formData.driver" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('区域')" prop="cantonCode">
          <el-input v-model="formData.cantonCode" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('开始里程（km）')" prop="startMileage">
          <el-input v-model="formData.startMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('结束里程（km）')" prop="endMileage">
          <el-input v-model="formData.endMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('行驶里程（km）')" prop="drivingMileage">
          <el-input v-model="formData.drivingMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('规划里程（km）')" prop="planMileage">
          <el-input v-model="formData.planMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('高速里程（km）')" prop="highwayMileage">
          <el-input v-model="formData.highwayMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('高架里程（km）')" prop="elevatedMileage">
          <el-input v-model="formData.elevatedMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('当日有效里程：高架+高速（km）')" prop="effectiveMileage">
          <el-input v-model="formData.effectiveMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('工作时长（h）')" prop="workDuration">
          <el-input v-model="formData.workDuration" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('计划时长（h）')" prop="planDuration">
          <el-input v-model="formData.planDuration" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('当日有效时长（h）')" prop="effectiveDuration">
          <el-input v-model="formData.effectiveDuration" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('故障时长（h）')" prop="failureDuration">
          <el-input v-model="formData.failureDuration" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('充电时长（h）')" prop="chargeDuration">
          <el-input v-model="formData.chargeDuration" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('数据量')" prop="dataAmount">
          <el-input v-model="formData.dataAmount" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('规划匝道数')" prop="planRampNumber">
          <el-input v-model="formData.planRampNumber" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('匝道数')" prop="rampNumber">
          <el-input v-model="formData.rampNumber" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('桥梁数')" prop="bridgeNumber">
          <el-input v-model="formData.bridgeNumber" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('隧道数')" prop="tunnelNumber">
          <el-input v-model="formData.tunnelNumber" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('是否特殊场景')" prop="specialScenario">
          <el-input v-model="formData.specialScenario" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('下雨里程（km）')" prop="rainMileage">
          <el-input v-model="formData.rainMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('下雪里程（km）')" prop="snowMileage">
          <el-input v-model="formData.snowMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('起雾里程（km）')" prop="frogMileage">
          <el-input v-model="formData.frogMileage" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('当日工效：有效里程除以有效时长')" prop="workEfficiency">
          <el-input v-model="formData.workEfficiency" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('defect数量')" prop="defectNumber">
          <el-input v-model="formData.defectNumber" :disabled="formReadonly"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
                <span class="dialog-footer">
                    <template v-if="dialogStatus === 'view'">
                        <el-button @click="dialogVisible = false"
                                   v-if=" dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
                        <el-button
                            :type="item.buttonStyleType"
                            @click="executeButtonMethod(currentButton)"
                            v-if="currentButton && currentButton.name"
                        >{{ $t(currentButton.name) }}</el-button>
                    </template>
                    <template v-else>
                        <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
                        <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
                    </template>
                </span>
      </template>
    </el-dialog>
  </div>
  <import-work-report ref="ImportWorkReport" @reload="query"/>
</template>

<script>
import {
  saveFtmDailyWorkReport,
  updateFtmDailyWorkReport,
  deleteFtmDailyWorkReport,
  pageFtmDailyWorkReport,
  getFtmDailyWorkReport,
  exportFtmDailyWorkReport
} from '@/apis/fleet/ftm-daily-work-report'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {showToast, showConfirmToast, downloadFile, dateUtils} from '@/plugins/util'
import ImportWorkReport from '@/pages/fleet/dialog/ImportWorkReport'
import {getSysCantonTree} from '@/apis/system/sys-canton'

const defaultFormData = {}
export default {
  name: "FtmDailyWorkReport",
  data() {
    return {
      workReportTypeList: [
        {
          "code": "urban_e2e",
          "name": "Urban E2E"
        },
        {
          "code": "highway_e2e",
          "name": "Highway E2E"
        }
      ],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click',
        checkStrictly: true
      },
      cantonCodeList: [],
      dateRange: [],
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        type: [
          {required: true, message: this.$t('请输入类型'), trigger: 'blur'}
        ],
        acquisitionDate: [
          {required: true, message: this.$t('请输入采集日期'), trigger: 'blur'}
        ],
        vin: [
          {required: true, message: this.$t('请输入车架号'), trigger: 'blur'}
        ],
        complianceOfficerEmpId: [
          {required: true, message: this.$t('请输入合规官id'), trigger: 'blur'}
        ],
        complianceOfficerEmpName: [
          {required: true, message: this.$t('请输入合规员姓名'), trigger: 'blur'}
        ],
        driver: [
          {required: true, message: this.$t('请输入司机'), trigger: 'blur'}
        ],
        cantonCode: [
          {required: true, message: this.$t('请输入区域'), trigger: 'blur'}
        ],
        startMileage: [
          {required: true, message: this.$t('请输入开始里程（km）'), trigger: 'blur'}
        ],
        endMileage: [
          {required: true, message: this.$t('请输入结束里程（km）'), trigger: 'blur'}
        ],
        drivingMileage: [
          {required: true, message: this.$t('请输入行驶里程（km）'), trigger: 'blur'}
        ],
        planMileage: [
          {required: true, message: this.$t('请输入规划里程（km）'), trigger: 'blur'}
        ],
        highwayMileage: [
          {required: true, message: this.$t('请输入高速里程（km）'), trigger: 'blur'}
        ],
        elevatedMileage: [
          {required: true, message: this.$t('请输入高架里程（km）'), trigger: 'blur'}
        ],
        effectiveMileage: [
          {required: true, message: this.$t('请输入当日有效里程：高架+高速（km）'), trigger: 'blur'}
        ],
        workDuration: [
          {required: true, message: this.$t('请输入工作时长（h）'), trigger: 'blur'}
        ],
        planDuration: [
          {required: true, message: this.$t('请输入计划时长（h）'), trigger: 'blur'}
        ],
        effectiveDuration: [
          {required: true, message: this.$t('请输入当日有效时长（h）'), trigger: 'blur'}
        ],
        failureDuration: [
          {required: true, message: this.$t('请输入故障时长（h）'), trigger: 'blur'}
        ],
        chargeDuration: [
          {required: true, message: this.$t('请输入充电时长（h）'), trigger: 'blur'}
        ],
        dataAmount: [
          {required: true, message: this.$t('请输入数据量'), trigger: 'blur'}
        ],
        planRampNumber: [
          {required: true, message: this.$t('请输入规划匝道数'), trigger: 'blur'}
        ],
        rampNumber: [
          {required: true, message: this.$t('请输入匝道数'), trigger: 'blur'}
        ],
        bridgeNumber: [
          {required: true, message: this.$t('请输入桥梁数'), trigger: 'blur'}
        ],
        tunnelNumber: [
          {required: true, message: this.$t('请输入隧道数'), trigger: 'blur'}
        ],
        specialScenario: [
          {required: true, message: this.$t('请输入是否特殊场景'), trigger: 'blur'}
        ],
        rainMileage: [
          {required: true, message: this.$t('请输入下雨里程（km）'), trigger: 'blur'}
        ],
        snowMileage: [
          {required: true, message: this.$t('请输入下雪里程（km）'), trigger: 'blur'}
        ],
        frogMileage: [
          {required: true, message: this.$t('请输入起雾里程（km）'), trigger: 'blur'}
        ],
        workEfficiency: [
          {required: true, message: this.$t('请输入当日工效：有效里程除以有效时长'), trigger: 'blur'}
        ],
        defectNumber: [
          {required: true, message: this.$t('请输入defect数量'), trigger: 'blur'}
        ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  components: {
    ImportWorkReport
  },
  methods: {
    getSysCantonTree() {
      getSysCantonTree().then(res => {
        this.cantonCodeList = res.data
      })
    },
    export() {
      let postData = {
        ...this.queryParam,
        startDate: this.dateRange?.length ? this.dateRange[0] : null,
        endDate: this.dateRange?.length ? this.dateRange[1] : null
      }
      if (Array.prototype.isPrototypeOf(this.queryParam.cantonCode)) {
        postData.cantonCode = this.queryParam.cantonCode[this.queryParam.cantonCode?.length - 1]
      } else {
        postData.cantonCode = this.queryParam.cantonCode
      }
      delete postData.current
      delete postData.size
      exportFtmDailyWorkReport(postData).then(res => {
        if (res.type === 'application/json') {
          this.$message.warning({
            message: '暂无数据可导出!',
            type: 'warning'
          })
          return
        }
        const fileName = "work_report" + dateUtils.formatDateNormal(new Date())
        downloadFile(res, fileName)
      })
    },
    importData() {
      this.$refs.ImportWorkReport.show({type: 'view'})
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      let postData = {
        ...this.queryParam,
        startDate: this.dateRange?.length ? this.dateRange[0] : null,
        endDate: this.dateRange?.length ? this.dateRange[1] : null
      }
      if (Array.prototype.isPrototypeOf(this.queryParam.cantonCode)) {
        postData.cantonCode = this.queryParam.cantonCode[this.queryParam.cantonCode?.length - 1]
      } else {
        postData.cantonCode = this.queryParam.cantonCode
      }
      pageFtmDailyWorkReport(postData).then(
          res => {
            this.pageData = res.data
          }
      )
      if (!this.cantonCodeList?.length) {
        this.getSysCantonTree()
      }
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate(
          valid => {
            if (!valid) return
            if (this.dialogStatus === 'add') {
              saveFtmDailyWorkReport(this.formData).then(
                  () => {
                    this.dialogVisible = false
                    this.query()
                  }
              )
            }
            if (this.dialogStatus === 'edit') {
              updateFtmDailyWorkReport(this.formData).then(
                  () => {
                    this.dialogVisible = false
                    this.query()
                  }
              )
            }
          }
      )
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getFtmDailyWorkReport(row.id).then(
          res => {
            this.$nextTick(function () {
              this.formData = res.data
            })
            this.dialogVisible = true
          }
      )
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getFtmDailyWorkReport(row.id).then(
          res => {
            this.$nextTick(function () {
              this.formData = res.data
            })
            this.dialogVisible = true
          }
      )
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({id: row.id})
    },
    batchRemove() {
      let idList = [];
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ids})
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmDailyWorkReport(param).then(
            () => {
              this.query()
            }
        )
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {

    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }

  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}

</style>
