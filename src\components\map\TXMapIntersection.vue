<template>
  <div class="map-overview">
    <div id="allmap" class="map-chart" />
  </div>
</template>

<script>
import { TxMap } from '@/plugins/map/TxMap'

let globalMap, globalMarkerLayer, globalPolylineLayer, globalLabel, globalPolygonLayer
export default {
  name: 'TXMapInterscetion',
  emits: ['click-marker', 'click-label'],
  data() {
    return {
      map: '',
      mapId: 'allmap',
      positionList: [],
      routeType: 'position',
      markerLayer: '',
      formReadonly: false,
      centerLatitude: 39.90812,
      centerLongitude: 116.397484
    }
  },
  created() {},
  methods: {
    show(data) {
      this.clearMap()
      this.mapId = data?.id || 'allmap'
      this.routeType = data?.type || 'position'
      // let list = JSON.parse(JSON.stringify(data?.list || []))
      this.positionList = data?.list
      this.formReadonly = data?.formReadonly
      TxMap.init().then(TMap => {
        this.initMap()
      })
    },
    initMap() {
      //定义地图中心点坐标
      let center
      if (this.positionList?.length) {
        center = {
          latitude: this.positionList[0].latitude,
          longitude: this.positionList[0].longitude
        }
      } else {
        center = {
          latitude: this.centerLatitude,
          longitude: this.centerLongitude
        }
      }
      if (!globalMap) {
        //定义map变量，调用 TMap.Map() 构造函数创建地图
        globalMap = new TMap.Map(document.getElementById(this.mapId || 'allmap'), {
          center: new TMap.LatLng(center.latitude, center.longitude), //设置地图中心点坐标
          zoom: 17.2 //设置地图缩放级别
          // baseMap: {  // 设置卫星地图
          //   type: 'satellite'
          // }
          // pitch: 43.5,  //设置俯仰角
          // rotation: 45    //设置地图旋转角度
        })
      } else {
        // this.goCenter(center)
      }

      // this.drawMarkers(TMap, map)

      // if (this.positionList?.length) {
      if (this.routeType === 'trajectory') {
        this.drawRoute()
      } else if (this.routeType === 'position') {
        this.drawMarkers()
        this.drawLabel()
      } else if (this.routeType === 'area') {
        this.drawArea()
        this.drawLabel()
      }
      // }
    },
    goCenter(item) {
      globalMap.panTo(new TMap.LatLng(item.latitude, item.longitude), { duration: 1000 })
    },
    drawMarkers() {
      const _this = this
      //创建并初始化MultiMarker
      let geometries = this.positionList?.map((val, index) => {
        return {
          id: index, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          styleId: val.checked ? 'selectedStyle' : 'myStyle', //指定样式id
          position: new TMap.LatLng(val.latitude, val.longitude), //点标记坐标位置
          // markerAnimation: {
          //   enter:
          // }
          properties: {
            //自定义属性
            title: 'marker' + index
          }
        }
      })
      globalMarkerLayer = new TMap.MultiMarker({
        map: globalMap, //指定地图容器
        //样式定义
        styles: {
          //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
          myStyle: new TMap.MarkerStyle({
            width: 25, // 点标记样式宽度（像素）
            height: 35, // 点标记样式高度（像素）
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            anchor: { x: 16, y: 32 }
          }),
          selectedStyle: new TMap.MarkerStyle({
            width: 25, // 点标记样式宽度（像素）
            height: 35, // 点标记样式高度（像素）
            src: require('/src/assets/images/mapScreen/map-position.png'), //图片路径
            // src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png',
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            anchor: { x: 16, y: 32 }
          })
        },
        //点标记数据数组
        geometries
        // geometries: [
        //   {
        //     id: '1', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        //     styleId: 'myStyle', //指定样式id
        //     position: new TMap.LatLng(39.954104, 116.357503), //点标记坐标位置
        //     properties: {
        //       //自定义属性
        //       title: 'marker1'
        //     }
        //   },
        //   {
        //     //第二个点标记
        //     id: '2',
        //     styleId: 'marker',
        //     position: new TMap.LatLng(39.994104, 116.287503),
        //     properties: {
        //       title: 'marker2'
        //     }
        //   }
        // ]
      })
      //监听marker点击事件
      globalMarkerLayer.on('click', _this.clickHandler)
      // markerLayer.add([
      //   {
      //     id: '3', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
      //     styleId: 'myStyle', //指定样式id
      //     position: new TMap.LatLng(40.954104, 116.357503), //点标记坐标位置
      //     properties: {
      //       //自定义属性
      //       title: 'marker3'
      //     }
      //   },
      //   {
      //     //第4个点标记
      //     id: '4',
      //     styleId: 'marker',
      //     position: new TMap.LatLng(40.994104, 116.287503),
      //     properties: {
      //       title: 'marker4'
      //     }
      //   }
      // ])
      let fitViewList = geometries
        ?.filter((val, index) => index <= 3)
        ?.map(val => {
          return val.position
        })
      this.showFitView(fitViewList)
    },

    clickHandler(evt) {
      // globalMarkerLayer.setStyles({
      //   "myStyle": new TMap.MarkerStyle({
      //     width: 70,
      //     height: 70,
      //     src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png',
      //     opacity: 0.5
      //   })
      // })
      // geometries[evt.geometry.id].src = 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png'
      // globalMarkerLayer.updateGeometries(geometries)
      // globalMarkerLayer.setIcon()
      this.goCenter({
        longitude: evt.geometry.position.lng,
        latitude: evt.geometry.position.lat
      })
      if (!this.formReadonly) {
        globalMarkerLayer.updateGeometries({
          id: evt.geometry.id,
          styleId: evt.geometry.styleId === 'selectedStyle' ? 'myStyle' : 'selectedStyle',
          position: evt.geometry.position
        })
        this.$emit('click-marker', this.positionList[evt.geometry.id])
      }
    },
    clearMarkers() {
      const _this = this
      if (globalMarkerLayer) {
        globalMarkerLayer.off('click', _this.clickHandler)
        globalMarkerLayer.setMap(null)
        // globalMarkerLayer.setGeometries([])
        globalMarkerLayer = null
      }
    },
    clearPolylines() {
      if (globalPolylineLayer) {
        globalPolylineLayer.setMap(null)
        globalPolylineLayer = null
      }
    },
    clearPolygonLayer() {
      if (globalPolygonLayer) {
        globalPolygonLayer.setMap(null)
        globalPolygonLayer = null
      }
    },
    drawRoute() {
      const _this = this
      // list = list.reduce((newValue, oldValue)=>{
      //   return new Date(newValue.collectTime.$date) - new Date(oldValue.collectTime.$date)
      // })
      //小车移动路线
      // var path = [
      //   // new TMap.LatLng(39.98481500648338, 116.30571126937866),
      //   // new TMap.LatLng(39.982266575222155, 116.30596876144409),
      //   // new TMap.LatLng(39.982348784165886, 116.3111400604248),
      //   // new TMap.LatLng(39.978813710266024, 116.3111400604248),
      //   // new TMap.LatLng(39.978813710266024, 116.31699800491333)
      // ]
      // this.positionList.forEach(val => {
      //   path.push(new TMap.LatLng(val.latitude, val.longitude))
      // })
      let path = this.positionList.map(val => {
        return new TMap.LatLng(val.latitude, val.longitude)
      })

      globalPolylineLayer = new TMap.MultiPolyline({
        map: globalMap, //指定地图容器
        // 折线样式定义
        styles: {
          style_blue: new TMap.PolylineStyle({
            color: '#3777FF', // 线填充色
            width: 4, // 折线宽度
            borderWidth: 2, // 边线宽度
            borderColor: '#FFF', // 边线颜色
            lineCap: 'round', // 线端头方式
            eraseColor: 'rgba(190,188,188,1)'
          })
        },
        geometries: [
          {
            id: 'erasePath',
            styleId: 'style_blue',
            paths: path
          }
        ]
      })
      globalMarkerLayer = new TMap.MultiMarker({
        map: globalMap, //指定地图容器
        styles: {
          'car-down': new TMap.MarkerStyle({
            width: 40,
            height: 40,
            anchor: {
              x: 20,
              y: 20
            },
            faceTo: 'map',
            rotate: 180,
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png'
          }),
          start: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/start.png'
          }),
          end: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/end.png'
          })
        },
        geometries: [
          {
            id: 'car',
            styleId: 'car-down',
            position: path[0]
          },
          {
            id: 'start',
            styleId: 'start',
            position: path[0]
          },
          {
            id: 'end',
            styleId: 'end',
            position: path[path.length - 1]
          }
        ]
      })
      this.startCar()
      // // 使用marker 移动接口， https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocMarker
      // globalMarkerLayer.moveAlong(
      //   {
      //     car: {
      //       path,
      //       speed: 250
      //     }
      //   },
      //   {
      //     autoRotation: true
      //   }
      // )
      // globalMarkerLayer.on('moving', e => {
      //   var passedLatLngs = e.car && e.car.passedLatLngs
      //   if (passedLatLngs) {
      //     // 使用路线擦除接口 eraseTo, https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocVector
      //     polylineLayer.eraseTo('erasePath', passedLatLngs.length - 1, passedLatLngs[passedLatLngs.length - 1])
      //   }
      // })

      // //创建mareker（小车）
      // var marker = new TMap.MultiMarker({
      //   map: this.map, //指定地图容器
      //   styles: {
      //     //样式设置
      //     'car-down': new TMap.MarkerStyle({
      //       width: 40, //小车图片宽度（像素）
      //       height: 40, //高度
      //       anchor: {
      //         //图片中心的像素位置（小车会保持车头朝前，会以中心位置进行转向）
      //         x: 20,
      //         y: 20
      //       },
      //       faceTo: 'map', //取’map’让小车贴于地面，faceTo取值说明请见下文图示
      //       rotate: 180, //初始小车朝向（正北0度，逆时针一周为360度，180为正南）
      //       src: require('/src/assets/images/car.png') //小车图片（图中小车车头向上，即正北0度）
      //     })
      //   },
      //   geometries: [
      //     {
      //       //小车marker的位置信息
      //       id: 'car', //因MultiMarker支持包含多个点标记，因此要给小车一个id
      //       styleId: 'car-down', //绑定样式
      //       position: new TMap.LatLng(39.98481500648338, 116.30571126937866) //初始坐标位置
      //     }
      //   ]
      // })
      //
      // //调用moveAlong，实现小车移动
      // marker.moveAlong(
      //   {
      //     car: {
      //       //设置让"car"沿"path"移动，速度70公里/小时
      //       path,
      //       speed: 70
      //     }
      //   },
      //   {
      //     autoRotation: true //车头始终向前（沿路线自动旋转）
      //   }
      // )
    },
    startCar() {
      const _this = this
      let path = this.positionList.map(val => {
        return new TMap.LatLng(val.latitude, val.longitude)
      })
      // 使用marker 移动接口， https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocMarker
      globalMarkerLayer.moveAlong(
        {
          car: {
            path,
            speed: 250
          }
        },
        {
          autoRotation: true
        }
      )
      globalMarkerLayer.on('moving', e => {
        var passedLatLngs = e.car && e.car.passedLatLngs
        if (passedLatLngs) {
          // 使用路线擦除接口 eraseTo, https://lbs.qq.com/webApi/javascriptGL/glDoc/glDocVector
          globalPolylineLayer.eraseTo('erasePath', passedLatLngs.length - 1, passedLatLngs[passedLatLngs.length - 1])
        }
      })
    },
    drawArea() {
      const _this = this
      let geometries = this.positionList.map((val, index) => {
        let paths = val.geomJson.map(position => new TMap.LatLng(position[1], position[0]))
        if (index === 0) {
          this.showFitView(paths)
        }
        return {
          id: index,
          styleId: val.checked ? 'selectedStyle' : val.daqTaskCount ? 'collectedStyle' : 'myStyle',
          paths,
          properties: {
            //自定义属性
            center: {
              longitude: val.longitude,
              latitude: val.latitude
            },
            daqTaskCount: val.daqTaskCount
          }
        }
        // return
      })

      globalPolygonLayer = new TMap.MultiPolygon({
        map: globalMap, //指定地图容器
        // 折线样式定义
        styles: {
          myStyle: new TMap.PolygonStyle({
            color: 'rgba(100, 149, 237, 0.7)', // 面填充色
            showBorder: true, // 是否显示拔起面的边线
            borderColor: '#409eff' // 边线颜色
          }),
          collectedStyle: new TMap.PolygonStyle({
            // color: '#f56c6c', // 面填充色
            color: '#f0f9eb', // 面填充色
            showBorder: true, // 是否显示拔起面的边线
            borderColor: '#c2e7b0' // 边线颜色
          }),
          selectedStyle: new TMap.PolygonStyle({
            // color: '#f56c6c', // 面填充色
            color: 'rgba(245, 108, 108, 0.7)', // 面填充色
            showBorder: true, // 是否显示拔起面的边线
            borderColor: 'rgba(245, 108, 108, 1)' // 边线颜色
          })
        },
        geometries
      })
      //监听marker点击事件
      globalPolygonLayer.on('click', _this.clickHandlerPolygon)
    },

    clickHandlerPolygon(evt) {
      this.goCenter({
        longitude: evt.geometry.properties.center.longitude,
        latitude: evt.geometry.properties.center.latitude
      })
      if (!this.formReadonly) {
        globalPolygonLayer.geometries[evt.geometry.id].styleId =
          globalPolygonLayer.geometries[evt.geometry.id].styleId === 'selectedStyle'
            ? evt.geometry.properties.daqTaskCount
              ? 'collectedStyle'
              : 'myStyle'
            : 'selectedStyle'
        globalPolygonLayer.updateGeometries(globalPolygonLayer.geometries[evt.geometry.id])
        this.$emit('click-marker', this.positionList[evt.geometry.id])
      }
    },
    destroyMap() {
      if (globalMap) {
        globalMap.destroy()
        globalMap = null
        globalMarkerLayer = null
        globalPolylineLayer = null
        globalLabel = null
      }
    },

    //设置自适应显示marker
    showFitView(list) {
      if (list?.length) {
        //初始化
        let bounds = new TMap.LatLngBounds()
        //判断标注点是否在范围内
        list.forEach(item => {
          //若坐标点不在范围内，扩大bounds范围
          bounds.extend(item)
        })
        //设置地图可视范围
        globalMap.fitBounds(bounds, {
          padding: 100 // 自适应边距
        })
      }
    },
    drawLabel() {
      const _this = this
      //创建并初始化MultiMarker
      let geometries = this.positionList
        .filter(val => val.daqTaskCount)
        .map((val, index) => {
          return {
            id: index, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            styleId: 'label', //指定样式id
            position: new TMap.LatLng(val.latitude, val.longitude), //点标记坐标位置
            content: val.daqTaskCount ? val.daqTaskCount.toString() : '', //标注文本
            // markerAnimation: {
            //   enter:
            // }
            properties: {
              //自定义属性
              itemKey: val.itemKey
            }
          }
        })
      globalLabel = new TMap.MultiLabel({
        // id: index,
        map: globalMap,
        styles: {
          label: new TMap.LabelStyle({
            // color: '#f56c6c', //颜色属性
            // color: 'rgba(64, 158, 255, 1)', //颜色属性
            size: 16, //文字大小属性
            // width: 150,
            height: 20,
            width: 20,
            // padding: '5px 15px',
            offset: { x: -4, y: 10 }, //文字偏移属性单位为像素
            angle: 0, //文字旋转属性
            alignment: 'center', //文字水平对齐属性
            verticalAlignment: 'middle', //文字垂直对齐属性
            // backgroundColor: 'rgba(76, 142, 255, 0.1)',
            borderColor: '#606266',
            borderRadius: 100,
            borderWidth: 2
          })
        },
        geometries
      })
      if (geometries?.length) {
        this.bindLabelClick()
      }
      globalMap.on('zoom_changed', _this.eventZoom)
    },
    eventZoom() {
      const currentZoom = globalMap.getZoom()
      if (currentZoom >= 18) {
        globalLabel.setVisible(true)
      } else {
        globalLabel.setVisible(false)
      }
    },
    bindLabelClick() {
      const _this = this
      globalLabel.on('click', _this.eventClick)
    },
    eventClick(evt) {
      this.goCenter({
        longitude: evt.geometry.position.lng,
        latitude: evt.geometry.position.lat
      })
      const item = this.positionList.find(val => val.itemKey === evt.geometry?.properties?.itemKey)
      this.$emit('click-label', item)
    },
    clearLabelClick() {
      const _this = this
      if (globalLabel) {
        globalLabel.off('click', _this.eventClick)
        globalLabel.off('zoom_changed', _this.eventZoom)
        globalLabel.setMap(null)
        globalLabel = null
      }
    },
    clearMap() {
      this.clearLabelClick()
      this.clearMarkers()
      this.clearPolylines()
      this.clearPolygonLayer()
    }
  }
}
</script>

<style lang="scss" scoped>
.map-overview,
.map-chart {
  height: 100%;
  width: 100%;
  z-index: 1;
  user-select: none;

  :deep(.logo-text) {
    display: none !important;
  }

  //:deep(img) {
  //	display: none;
  //}
}
</style>
