<template>
  <div class="fleet-management">
    <div class="query-params">
      <ltw-input
          class="param-item"
          :placeholder="$t('请输入停车场名称或编码')"
          v-model="queryParam.key"
          clearable
          @clear="refresh"
      >
        <template #append>
          <el-button @click="refresh" id="search">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </template>
      </ltw-input>

      <el-cascader
          class="param-item"
          placeholder="区域"
          filterable
          clearable
          popper-class="canton-list"
          v-model="queryParam.cantonCode"
          :options="cantonCodeList"
          style="margin-right: 10px"
          :props="props"
      />
      <el-select
          class="param-item"
          v-model="queryParam.parkingEnvCodeList"
          multiple
          filterable
          collapse-tags
          collapse-tags-tooltip
          clearable
          placeholder="环境类型"
      >
        <el-option v-for="item in parkingEnvList" :key="item.code" :label="item.name" :value="item.code"/>
      </el-select>

      <el-select
          class="param-item"
          v-model="queryParam.isLocked"
          clearable
          placeholder="锁定状态"
      >
        <el-option label="已锁定" :value="true"/>
        <el-option label="未锁定" :value="false"/>
      </el-select>

      <el-button class="filter-btn" @click="filter" id="el-icon-filter">
        <ltw-icon icon-code="el-icon-filter"></ltw-icon>
        <span>{{ $t('标签筛选') }}</span>
      </el-button>
      <el-button class="confirm-btn" color="#5755FF" @click="search">
        <span>{{ $t('确认') }}</span>
      </el-button>
      <el-button class="confirm-btn" type="danger" :disabled="disabledByParams" @click="reset">
        <span>{{ $t('重置') }}</span>
      </el-button>
    </div>
    <div class="line">
      <el-divider/>
    </div>
    <div class="table-opt">
      <div class="table-sort">
        展示
        <common-selection
            size="small"
            class="display-type"
            v-model="displayType"
            model-code="code"
            model-Name="name"
            :model-options="displayList"
            @change="changeDisplayType"
        />
        <div class="table-sort" v-if="displayType === 'map'">
          <el-divider direction="vertical" />
          <ltw-icon icon-code="el-icon-folder"></ltw-icon>
          停车场
          <div class="num">{{ mapData?.length || 0 }}</div>
        </div>
        <!--        <el-select-->
        <!--          class="param-item"-->
        <!--          v-model="tableSort.key"-->
        <!--          multiple-->
        <!--          collapse-tags-->
        <!--          placeholder="环境类型"-->
        <!--        >-->
        <!--          <el-option v-for="item in parkingEnvList" :key="item.code" :label="item.name" :value="item.code" />-->
        <!--        </el-select>-->
      </div>
      <div class="opt-btns">
        <el-button
            plain
            color="#5755FF"
            :type="item.buttonStyleType"
            :key="item.id"
            :id="item.buttonIconCode"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
        >
          <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
          {{ $t(item.name) }}
        </el-button>
        <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
        >
          <el-button type="primary" plain color="#5755FF">
            {{ $t('批量操作') }}
            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                  :id="item.buttonIconCode"
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                {{ $t(item.name) }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div v-if="displayType === 'table'">
      <div class="content">
        <div class="table" v-if="pageData?.records?.length">
          <el-row v-for="rowIndex in Math.ceil(pageData?.records?.length / splitNum)" :key="rowIndex" :gutter="20">
            <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="8"
                :xl="6"
                v-for="colIndex in rowIndex === Math.ceil(pageData?.records?.length / splitNum)
                ? pageData?.records?.length % splitNum || splitNum
                : splitNum"
                :key="colIndex"
                style="margin-bottom: 20px"
            >
              <parkinglot-card
                  @reload="query"
                  @optFunction="optFunction"
                  @show-map-marker="showMapMarker"
                  :key="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)].id"
                  :item="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)]"
              />
            </el-col>
          </el-row>
        </div>
        <el-empty v-if="!pageData?.records?.length" description="暂无数据"></el-empty>
      </div>
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParam.current"
          :page-sizes="[12, 24, 48, 72]"
          :page-size="queryParam.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
      >
      </el-pagination>
    </div>
    <div v-else class="map-content">
      <div class="opt-list">
        <div
            class="opt-btn"
            v-for="item in geoTypes"
            :key="item.value"
            :class="{ active: geoType === item.value }"
            @click="changeGeoType(item.value)"
        >
          <ltw-icon :icon-code="getIconCode(item.value)"></ltw-icon>
          {{ item.name }}
        </div>
      </div>
      <base-map class="base-map" ref="baseMap" @click-marker="clickMarker" @cluster-changed="clusterChanged"></base-map>
    </div>
  </div>
  <filter-parking-lot @reload="reloadFilter" ref="FilterParkingLot"/>
  <add-parking-lot ref="AddParkingLot" @reload="reload"/>
  <data-statistic-drawer ref="DataStatisticDrawer"/>
</template>

<script>
import CommonSelection from '@/components/system/CommonSelection'
import {getSysCantonTree} from '@/apis/system/sys-canton'
import FilterParkingLot from '@/pages/fleet/dialog/FilterParkingLotManagement.vue'
import DataStatisticDrawer from '@/pages/fleet/dialog/DataStatisticDrawer.vue'
import {getFtmParkingLotFieldItems} from '@/apis/fleet/ftm-parking-lot'
import {
  pageMdParkingLots,
  exportParkingLotDetailExcel,
  deleteMdParkingLots,
  listMdParkingLotsSimpleGeo
} from '@/apis/fleet/parking-lot-management'
import ParkinglotCard from '@/pages/fleet/components/ParkinglotCard'
import AddParkingLot from '@/pages/fleet/dialog/AddParkingLotManagement'
import BaseMap from '@/components/map/BaseMap.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {showToast, showConfirmToast, downloadFile, debounce} from '@/plugins/util.js'
import {latLngPoint, initClusterBubble} from '@/plugins/map/TxMap'

const defaultFormData = {
  current: 1,
  size: 12
}
export default {
  name: 'ParkinglotManagement',
  components: {
    FilterParkingLot,
    ParkinglotCard,
    DataStatisticDrawer,
    AddParkingLot,
    BaseMap,
    CommonSelection
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: []
      },
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click',
        checkStrictly: true
      },
      cantonCodeList: [],
      parkingEnvList: [],
      queryParam: Object.assign({}, defaultFormData),
      tableSort: {
        sortKey: 'date',
        sort: 'desc' //asc
      },
      splitNum: 3,
      displayType: 'table',
      mapData: [],
      geoType: 'cluster',
      geoTypes: [
        {
          name: '点',
          value: 'point'
        },
        {
          name: '聚合',
          value: 'cluster'
        }
      ],
      clusterBubbleList: [],
      displayList: [
        {
          name: '地图',
          code: 'map'
        },
        {
          name: '列表',
          code: 'table'
        }
      ],
    }
  },
  computed: {
    // checkAll() {
    //   return this.backupPageData.every(val => val.checked)
    // },
    selectedDataNum() {
      return this.pageData.records.filter(val => val.checked)?.length
    },
    disabledByParams() {
      for (const key in this.queryParam) {
        if (key === 'current' || key === 'size') continue
        if (Object.prototype.toString.call(this.queryParam[key]) === '[object Array]') {
          if (this.queryParam[key]?.length) {
            return false
          }
        } else if (this.queryParam[key]) {
          return false
        }
      }
      return true
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.getSysCantonTree()
    this.getParkingEnvList()
    const _this = this
    window.onresize = debounce(_this.changeSize)
    this.changeSize()
  },
  mounted() {
    this.query()
  },
  methods: {
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.query()
    },
    changeSize() {
      if (window.innerWidth >= 1920) {
        this.splitNum = 4
      } else if (window.innerWidth >= 1200) {
        this.splitNum = 3
      } else if (window.innerWidth >= 992) {
        this.splitNum = 2
      } else if (window.innerWidth >= 768) {
        this.splitNum = 2
      } else {
        this.splitNum = 1
      }
    },
    async query(position) {
      const postData = {...this.queryParam}
      if (this.queryParam?.cantonCode?.length) {
        postData.cantonCode = this.queryParam?.cantonCode[this.queryParam?.cantonCode?.length - 1]
      }
      if (this.displayType === 'table') {
        pageMdParkingLots(postData).then(res => {
          this.pageData = res.data
        })
      } else {
        this.clearMap()
        const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
        if (!globalMap) {
          await this.$refs.baseMap.show()
        }
        delete postData.current
        delete postData.size
        listMdParkingLotsSimpleGeo(postData).then(res => {
          this.mapData = res.data
          const markers = res.data.filter(val => val?.addressPoint?.coordinates?.length)

          switch (this.geoType) {
            case 'point':
              this.drawMarkers(markers, position)
              break
            case 'cluster':
              this.drawCluster(markers)
              break
          }
        })
      }
    },
    drawCluster(markers) {
      const _this = this
      const initMarkers = markers.map((val, index) => {
        return {
          id: val.id,
          style: {
            src: 'data:image/png;base64,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'
          },
          position: latLngPoint([val.addressPoint.coordinates[1], val.addressPoint.coordinates[0]])
        }
      })
      this.$refs.baseMap.initClusterMarkers(initMarkers)
      this.$refs.baseMap.showFitView(initMarkers)
    },
    clearMap() {
      this.$refs.baseMap.clearObjects('globalMarker')
      this.clusterBubbleList.forEach(item => {
        item.destroy()
      })
      this.clusterBubbleList = []
    },
    clusterChanged(e) {
      if (this.geoType === 'cluster') {
        const _this = this
        let markerGeometries = []
        // 销毁旧聚合簇生成的覆盖物
        this.clearMap()
        // this.$refs.baseMap.clearObjects('globalMarker', 'globalPolylineLayer', 'globalPolygonLayer')
        const globalMarkerCluster = this.$refs.baseMap.getGlobalMapObj('globalMarkerCluster')
        const globalMap = this.$refs.baseMap.getGlobalMapObj('globalMap')
        // 根据新的聚合簇数组生成新的覆盖物和点标记图层
        const clusters = globalMarkerCluster.getClusters()
        clusters.forEach(function (item) {
          if (item.geometries.length > 1) {
            const ClusterBubble = initClusterBubble(globalMap)
            let clusterBubble = new ClusterBubble({
              map: globalMap,
              position: item.center,
              content: item.geometries.length
            })
            clusterBubble.on('click', () => {
              globalMap.fitBounds(item.bounds, {
                padding: 100 // 自适应边距
              })
            })
            _this.clusterBubbleList.push(clusterBubble)
          } else {
            markerGeometries.push(item.geometries[0])
          }
        })

        if (markerGeometries?.length) {
          // 创建点标记图层

          const markers = this.mapData
              .filter(val => ~markerGeometries.findIndex(geo => geo.id === val.id))
              .map((val, index) => {
                return {
                  id: val.id,
                  style: {
                    src: 'data:image/png;base64,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'
                  },
                  position: latLngPoint([val.addressPoint.coordinates[1], val.addressPoint.coordinates[0]])
                }
              })
          this.$refs.baseMap.initMarkers(markers)
          // this.drawInfoWindow(globalMarker)
        }
      }
    },
    drawMarkers(data, position) {
      const markers = data.map((val, index) => {
        return {
          id: val.id,
          style: {
            src: 'data:image/png;base64,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'
          },
          position: latLngPoint({lat: val.addressPoint.coordinates[1], lng: val.addressPoint.coordinates[0]})
        }
      })
      this.$refs.baseMap.initMarkers(markers)

      if (position?.addressPoint?.coordinates?.length) {
        this.$refs.baseMap.goCenter(position?.addressPoint?.coordinates)
      } else {
        this.$refs.baseMap.showFitView(markers)
      }
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    getParkingEnvList() {
      // 地址围栏
      if (!this.parkingEnvList?.length) {
        getFtmParkingLotFieldItems({fieldCode: 'parking_env'}).then(res => {
          this.parkingEnvList = res.data
        })
      }
    },
    filter() {
      this.$refs.FilterParkingLot.show()
    },
    search() {
      this.query()
    },
    reset() {
      this.queryParam = Object.assign({}, defaultFormData)
      this.$refs.FilterParkingLot.resetForm()
      this.query()
    },
    export() {
      let postData = {
        ...this.queryParam
      }
      delete postData.current
      delete postData.size
      exportParkingLotDetailExcel(postData).then(res => {
        // downloadTxt('停车场.csv', res)
        downloadFile(res, '停车场列表')
      })
    },
    reloadFilter(val) {
      this.queryParam.fieldItemCodeMap = {...val}
      this.query()
    },
    reload() {
      this.query()
    },
    optFunction(row) {
      if (row.type === 'delete') {
        this.singleRemove({id: row.id})
      } else if (row.type === 'data_statistic') {
        this.$refs.DataStatisticDrawer.show({
          id: row.id
        })
      } else {
        this.$refs.AddParkingLot.show({
          type: row.type,
          id: row.id
        })
      }
    },
    add() {
      this.$refs.AddParkingLot.show({type: 'add'})
    },
    singleRemove(row) {
      this.remove({id: row.id})
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ids})
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteMdParkingLots(param).then(() => {
          this.query()
        })
      })
    },
    handleCommand(command) {
      this.selectedData = this.pageData.records.filter(val => val.checked)
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },

    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    changeDisplayType() {
      this.displayType = this.displayType === 'table' ? 'map' : 'table'
      this.$nextTick(() => {
        this.query()
      })
    },
    clickMarker(e) {
      const {id} = e.geometry
      this.$refs.AddParkingLot.show({
        type: 'map',
        id: id
      })
    },
    showMapMarker(obj) {
      this.displayType = 'map'
      this.$nextTick(() => {
        this.query(obj)
      })
    },
    changeGeoType(val) {
      this.geoType = val
      this.query()
    },
    getIconCode(geoType) {
      let iconCode = ''
      switch (geoType) {
        case 'point':
          iconCode = 'el-icon-location'
          break
        case 'line':
          iconCode = 'el-icon-top-right'
          break
        case 'area':
          iconCode = 'el-icon-help'
          break
        case 'cluster':
          iconCode = 'el-icon-connection'
          break
      }
      return iconCode
    },
  }
}
</script>

<style>
.clusterBubble {
  border-radius: 50%;
  color: #fff;
  font-weight: 500;
  text-align: center;
  opacity: 0.88;
  background-image: linear-gradient(139deg, #4294ff 0%, #295bff 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 0;
  left: 0;
}
</style>
<style scoped lang="scss">
.fleet-management {
  .query-params {
    .param-item {
      width: 240px;
      margin-right: 10px;
    }
  }

  .query-params-btns {
    display: flex;
    justify-content: space-between;
    margin: 16px 0;

    .map {
      display: flex;

      .map-parking-num {
        font-size: 12px;
        display: flex;
        align-items: center;
        margin-left: 10px;
      }
    }
  }

  .num {
    margin-left: 8px;
    color: #5755ff;
    font-size: 12px;
    font-weight: 700;
    line-height: 24px;
  }

  .line {
    padding: 0 12px;

    .el-divider {
      margin: 16px 0;
    }
  }

  .table-opt {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 10px;

    .table-sort {
      display: flex;
      align-items: center;

      .display-type {
        width: 100px;
        margin-left: 10px;
      }

      .view-type-btn {
        margin-right: 10px;
      }

      .ltw-icon {
        margin-right: 4px;
      }
    }

    .opt-btns {
      .el-button {
        margin-right: 10px;
        margin-left: 0;
      }
    }
  }

  .map-content {
    padding-right: 10px;

    .opt-list {
      position: absolute;
      z-index: 9999;
      right: 30px;
      top: 120px;
      background: #fff;
      padding: 5px 0;
      border-radius: 3px;

      .opt-btn {
        padding: 5px 10px;
        text-align: left;
        font-size: 12px;
        cursor: pointer;

        &.active,
        &:hover {
          background-color: rgb(235.9, 245.3, 255);
          color: #409eff;
        }

        .btn-text {
          display: flex;
        }

        //&:not(:first-child) {
        //  margin-top: 12px;
        //}
      }
    }
  }
}
</style>
