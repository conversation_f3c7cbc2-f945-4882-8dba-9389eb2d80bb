import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqReqDetail = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task',
    data,
    params
  })
export const updateDaqReqDetail = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task',
    data,
    params
  })
export const updateKpi = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_data_target/batch',
    data,
    params
  })
export const updateTagSample = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_tag_samples',
    data,
    params
  })

export const deleteDaqReqDetail = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task',
    params
  })
export const listDaqReqDetail = (params = {}, fullLoading) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task',
    params,
    fullLoading
  })
export const listDaqReqDetailSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/selections',
    params
  })
export const pageDaqTaskDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/page',
    params
  })
export const getDaqReqDetail = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id })
export const getLatestDaqReqDetail = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/latest',
    params
  })
export const pageDaqTaskDetailOfCurrentUser = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/page/recipient/current_user',
    params
  })
export const startTaskDetail = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id + '/start' })

export const finishTaskDetail = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id + '/finish' })
export const getTagRecordsChart = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/vt/vt_tag_records/chart',
    params
  })
export const getTaskStatistic = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id + '/statistic' })
export const acceptTaskDetail = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id + '/accept' })
export const publishTaskDetail = id =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/' + id + '/publish' })
export const pageRecipientCurrentUser = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/page/recipient/current_user',
    params
  })
export const batchPublishTaskDetail = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task/batch_publish',
    data
  })
export const getDaqRequirement = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/daq/daq_requirement',
    params
  })
export const getIntersectionList = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + `/geo/bs_intersection/query`,
    params,
    data
  })

export const daqTaskRecommendsBatch = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends/batch',
    data
  })
export const daqTaskRecommends = (data = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends',
    data
  })
export const getDaqTaskRecommends = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends',
    params
  })

export const deleteDaqTaskRecommends = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends',
    params
  })
export const updateDaqTaskRecommends = (data = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends',
    data
  })

export const upsertFileTask = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_task_recommends/upsert_with_file',
    params,
    data
  })
export const getRequirementRecommends = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + '/dm/dm_requirement_recommends',
    params
  })

export const cloneDaqTask = id =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + `/daq/daq_task/clone/${id}`
  })

export const getPoiList = (data = {}, params = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + `/geo/md_poi/list`,
    params,
    data,
    unloading
  })

export const getRecommendItemListRequirement = (data = {}, params = {}, unloading = true) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootJcdcUrl + `/dmi/dm_requirement_recommend_source_items/list`,
    params,
    data,
    unloading
  })
