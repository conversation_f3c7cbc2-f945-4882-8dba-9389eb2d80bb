<template>
  <div class="add-fleet-mangement">
    <el-page-header class="page-header" @back="onBack">
      <!-- Line 1 -->
      <template #icon>
        <ltw-icon icon-code="el-icon-arrow-left"></ltw-icon>
      </template>
      <template #breadcrumb/>
      <!-- Line 2 -->
      <!-- <template #icon /> -->
      <template #title>{{ $t('返回') }}</template>
      <template #content>{{ $t(getTypeNmae(vehicleType)) + (competitor ? $t('对手件') : $t('车辆')) }}</template>
      <!-- <template #extra /> -->
      <!-- Lines after 2 -->
      <!-- <template #default /> -->
    </el-page-header>
    <div class="add-fleet-body">
      <div class="fleet-model" :class="{ 'no-width': collapsed }">
        <el-card shadow="hover" class="fleet-model-card">
          <template #header>
            <div class="card-header">
              基本信息
              <el-button v-if="vehicleType === 'view'&& installRecord" @click="editVehicle()" class="button" text
                         type="warning">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
            </div>
          </template>
          <el-scrollbar>
            <el-form :model="formDataVehicle" :rules="formRulesVehicle" ref="formRefVehicle" label-position="top">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('车架号')" prop="vin">
                    <ltw-input v-if="vehicleType !== 'view'" v-model="formDataVehicle.vin" id="vin"></ltw-input>
                    <el-tag v-else>{{ formDataVehicle.vin || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('外部编号')" prop="externalVin">
                    <ltw-input v-if="vehicleType !== 'view'" v-model="formDataVehicle.externalVin"></ltw-input>
                    <el-tag v-else>{{ formDataVehicle.externalVin || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('负责人')" prop="keeperEmpId">
                    <employee-selection
                        v-if="vehicleType !== 'view'"
                        style="width: 100%"
                        v-model="formDataVehicle.keeperEmpId"
                        id="keeperEmpId"
                        @change="changeKeeperEmp"
                    />
                    <el-tag v-else>{{ formDataVehicle.keeperEmpName || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车牌')" prop="license">
                    <ltw-input v-if="vehicleType !== 'view'" v-model="formDataVehicle.license" id="license"></ltw-input>
                    <el-tag v-else>{{ formDataVehicle.license || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('出厂车架号')" prop="factoryVin">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.factoryVin"
                        id="factoryVin"
                    ></ltw-input>
                    <el-tag v-else>{{ formDataVehicle.factoryVin || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('轴距') + '(' + $t('毫米') + ')'" prop="wheelBase">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.wheelBase"
                        id="wheelBase"
                        :limit-size="10"
                    />
                    <el-tag v-else>{{ formDataVehicle.wheelBase || '-' }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('后轴中心到车辆几何中心')" prop="egoCenterShiftDistance">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.egoCenterShiftDistance"
                        id="egoCenterShiftDistance"
                    >
                      <template #append>
                        <span>{{ $t('米') }}</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.egoCenterShiftDistance || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('采集车辆宽度（考虑反光镜）')" prop="includeRearviewMirrorWidth">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.includeRearviewMirrorWidth"
                        id="includeRearviewMirrorWidth"
                    >
                      <template #append>
                        <span>{{ $t('米') }}</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.includeRearviewMirrorWidth || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('采集车辆宽度（不考虑反光镜）')" prop="excludeRearviewMirrorWidth">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.excludeRearviewMirrorWidth"
                        id="excludeRearviewMirrorWidth"
                    >
                      <template #append>
                        <span>{{ $t('米') }}</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.excludeRearviewMirrorWidth || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车辆质量重心到车头的距离')" prop="toHeadstockDistance">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.toHeadstockDistance"
                        id="toHeadstockDistance"
                    >
                      <template #append>
                        <span>{{ $t('米') }}</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.toHeadstockDistance || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车辆质量重心到车尾的距离')" prop="toTailstockDistance">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.toTailstockDistance"
                        id="toTailstockDistance"
                    >
                      <template #append>
                        <span>米</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.toTailstockDistance || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车辆长度')" prop="vehicleLength">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.vehicleLength"
                        id="vehicleLength"
                    >
                      <template #append>
                        <span>米</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.vehicleLength || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车顶离地高度')" prop="roofHeight">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.roofHeight"
                        id="roofHeight"
                    >
                      <template #append>
                        <span>米</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.roofHeight || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('车底离地高度')" prop="floorHeight">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.floorHeight"
                        id="floorHeight"
                    >
                      <template #append>
                        <span>米</span>
                      </template>
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.floorHeight || '-') + $t('米') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('配置')" prop="configuration">
                    <ltw-input
                        v-if="vehicleType !== 'view'"
                        v-model="formDataVehicle.configuration"
                        id="configuration"
                    >
                    </ltw-input>
                    <el-tag v-else>{{ (formDataVehicle.configuration || '-') }}</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('CAN通信矩阵')" prop="canMatrix">
                      <dictionary-selection
                          id="canMatrix"
                          class="ltw-tool-container"
                          v-model="formDataVehicle.canMatrix"
                          clearable
                          dictionaryType="can_matrix"
                          v-if="vehicleType !== 'view'"
                      />
                    <el-tag v-else>{{ (formDataVehicle.canMatrix || '-') }}</el-tag>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item :label="$t('照片')" prop="photo">
                <!-- listType="text" -->
                <upload-file
                    ref="uploadImage"
                    source-type="vehicle_image"
                    :limit="1"
                    accept=".jpg,.jpeg,.png,.gif"
                    :source-id="formDataVehicle.id"
                    v-model="formDataVehicle.photo"
                    id="photo"
                    :disabled="vehicleType === 'view'"
                />
              </el-form-item>
            </el-form>
            <div class="footer">
              <el-button v-if="vehicleType === 'edit'" @click="cancelVehicle()" id="cancel"
              >{{ $t('取消') }}
              </el-button>
              <el-button v-if="vehicleType !== 'view'" type="primary" @click="submit" id="submit"
              >{{ $t('保存') }}
              </el-button>
            </div>
          </el-scrollbar>
        </el-card>
      </div>
      <div class="card-collapsed">
        <ltw-icon
            @click="toggleCollapsed"
            icon-code="el-icon-d-arrow-left"
            :class="{ 'collapsed-icon': collapsed }"
        ></ltw-icon>
      </div>
      <el-card shadow="hover" class="fleet-version" :class="{ 'full-width': collapsed }">
        <el-scrollbar>
          <el-breadcrumb v-if="formDataVehicle.id" separator="/">
            <el-breadcrumb-item>{{ formDataVehicle.vin }}</el-breadcrumb-item>
            <el-breadcrumb-item><span v-if="installRecord">装车记录</span><span v-if="competitor">对手件</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="getTypeNmae(recordType)">{{ getTypeNmae(recordType) }}</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="card-opt">
            <el-tooltip
                effect="dark"
                :content="$t('新增')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'view'"
            >
              <el-button text type="primary" @click="addInstallationProject">
                <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
                effect="dark"
                :content="$t('编辑')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'view'"
            >
              <el-button text type="warning" @click="editInstallationProject">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
                effect="dark"
                :content="$t('删除')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'view' && formDataRecord.status !== 'released'&& installRecord"
            >
              <el-button text type="danger" @click="deleteInstallationProject">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
                effect="dark"
                :content="$t('删除')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'view' && competitor "
            >
              <el-button text type="danger" @click="deleteVehicleSparePartVersion">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
                effect="dark"
                :content="$t('复制')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'view' && installRecord"
            >
              <el-button text type="info" @click="copyInstallationProject">
                <ltw-icon icon-code="el-icon-document"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
                effect="dark"
                :content="$t('取消')"
                placement="top"
                :enterable="false"
                v-if="recordType === 'edit' && competitor"
            >
              <el-button text type="info" @click="recordType = 'view'">
                <ltw-icon icon-code="el-icon-close"></ltw-icon>
              </el-button>
            </el-tooltip>
          </div>
          <div class="empty-content" v-if="recordType === '' && !recordList?.length">
            <el-empty description="暂无数据"></el-empty>
            <el-button @click="addInstallationProject()" v-if="formDataVehicle.id" type="primary">
              <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              <span v-if="installRecord">{{ $t('新增装车记录') }}</span>
              <span v-if="competitor">{{ $t('新增对手件记录') }}</span>
            </el-button>

          </div>
          <div class="fleet-version-content" v-else>
            <div class="version-list" v-if="recordType === 'view'">
              <el-steps :active="activeStepRecord" process-status="finish" finish-status="wait" align-center>
                <template v-for="(item, index) in recordList" :key="index">
                  <el-step
                      v-if="installRecord"
                      @click="changeRecord(item, index)"
                      :title="item.code + '(' + item.version + ')'"
                      :description="item.startTime"
                  />
                  <el-step
                      v-if="competitor"
                      @click="changeRecord(item, index)"
                      :title="item.name"
                      :description="item.startTime"
                  />
                </template>
              </el-steps>
              <!-- <div
								class="version-item"
								:class="{ active: item.id === formDataVehicle.id }"
								@click="changeVersion(item)"
								v-for="(item, index) in recordList"
								:key="index"
							  >
								<ltw-icon icon-code="el-icon-link"></ltw-icon>V{{
								  item.version
								}}
							  </div> -->
            </div>
            <div class="step-content" v-if="recordType">
              <el-steps
                  v-if="recordType === 'add'"
                  simple
                  :active="activeStep"
                  process-status="finish"
                  finish-status="success"
              >
                <el-step :title="$t('版本信息')"/>
                <el-step v-if="installRecord" :title="$t('传感器信息')"/>
                <el-step v-if="competitor" :title="$t('对手件信息')"/>
              </el-steps>
              <el-tabs v-else v-model="activeStep" @tab-change="getModalityList">
                <el-tab-pane :label="$t('版本信息')" :name="0"></el-tab-pane>
                <el-tab-pane v-if="installRecord" :label="$t('传感器信息')" :name="1"></el-tab-pane>
                <el-tab-pane v-if="competitor" :label="$t('对手件信息')" :name="1"></el-tab-pane>
              </el-tabs>
            </div>
            <div class="version-detail">
              <div class="step1" v-show="activeStep === 0">
                <div v-if="installRecord">
                  <div class="preview-list">
                    <el-tag
                        v-if="formDataRecord.statusName"
                        :type="formDataRecord.status === 'draft' ? 'warning' : 'success'"
                    >
                      {{ formDataRecord.statusName }}
                    </el-tag>
                    <el-tooltip
                        effect="dark"
                        :content="$t('预览')"
                        placement="top"
                        :enterable="false"
                        v-if="recordType === 'view'"
                    >
                      <el-button size="small" type="primary" plain @click="previewFile()">
                        <ltw-icon icon-code="el-icon-document"></ltw-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                  <el-form
                      class="form-record"
                      :model="formDataRecord"
                      :rules="formRulesRecord"
                      ref="formRef"
                      label-width="100px"
                      :hide-required-asterisk="recordType === 'view'"
                  >
                    <el-form-item :label="$t('车型版本')" prop="version">
                      <ltw-input
                          v-if="(recordType !== 'view' && formDataRecord.status !== 'released') || recordType === 'copy'"
                          :limitSize="10"
                          :value="
                        (formDataRecord.code || '') + (formDataRecord.version ? '(' + formDataRecord.version + ')' : '')
                      "
                          readonly
                          placeholder="请选择"
                      >
                        <template #append>
                          <el-button
                              class="choose-btn"
                              :class="{ 'choose-active': !formDataRecord.version }"
                              @click="chooseFleetVersion"
                          >
                            <ltw-icon icon-code="svg-choose"></ltw-icon>
                          </el-button>
                        </template>
                      </ltw-input>
                      <el-tag v-else>{{ formDataRecord.code + '(' + formDataRecord.version + ')' }}</el-tag>
                    </el-form-item>
                    <el-form-item :label="$t('开始时间')" prop="startTime">
                      <el-date-picker
                          v-if="recordType !== 'view' && formDataRecord.status !== 'released'"
                          v-model="formDataRecord.startTime"
                          type="datetime"
                          :disabled-date="disabledStartTime"
                          :placeholder="$t('开始时间')"
                          value-format="YYYY-MM-DD HH:mm:ss"
                      />
                      <el-tag v-else>{{ formDataRecord.startTime }}</el-tag>
                    </el-form-item>
                    <el-form-item :label="$t('结束时间')" prop="endTime">
                      <el-date-picker
                          v-if="recordType !== 'view'"
                          v-model="formDataRecord.endTime"
                          type="datetime"
                          :disabled-date="disabledEndTime"
                          :placeholder="$t('结束时间')"
                          value-format="YYYY-MM-DD HH:mm:ss"
                      />
                      <el-tag v-else>{{ formDataRecord.endTime || '-' }}</el-tag>
                    </el-form-item>
                    <el-form-item
                        v-if="formDataRecord.releaseTime && recordType !== 'copy'"
                        :label="$t('发布时间')"
                        prop="endTime"
                    >
                      <el-tag>{{ formDataRecord.releaseTime || '-' }}</el-tag>
                    </el-form-item>
                    <el-form-item :label="$t('备注')" prop="remark">
                      <ltw-input
                          :disabled="
                        (recordType === 'view' || formDataRecord.status === 'released') && recordType !== 'copy'
                      "
                          v-model="formDataRecord.description"
                          textType="remark"
                          type="textarea"
                      ></ltw-input>
                      <!--                    <div v-else v-text="formDataRecord.description || '-'"></div>-->
                    </el-form-item>
                  </el-form>
                </div>
                <div v-if="competitor">
                  <el-form
                      class="form-record"
                      :model="formDataRecord"
                      :rules="formRulesRecord"
                      ref="formRef"
                      label-width="100px"
                      :hide-required-asterisk="recordType === 'view'"
                  >
                    <el-form-item :label="$t('标题')" prop="name">
                      <ltw-input
                          v-if="recordType !== 'view' && formDataRecord.status !== 'released'"
                          :disabled="(recordType === 'view' || formDataRecord.status === 'released') && recordType !== 'copy'"
                          v-model="formDataRecord.name"
                      ></ltw-input>
                      <el-tag v-else>{{ formDataRecord.name || '-' }}</el-tag>
                    </el-form-item>
                    <el-form-item :label="$t('开始时间')" prop="startTime">
                      <el-date-picker
                          v-if="recordType !== 'view' && formDataRecord.status !== 'released'"
                          v-model="formDataRecord.startTime"
                          type="datetime"
                          :disabled-date="disabledStartTime"
                          :placeholder="$t('开始时间')"
                          value-format="YYYY-MM-DD HH:mm:ss"
                      />
                      <el-tag v-else>{{ formDataRecord.startTime }}</el-tag>
                    </el-form-item>
                    <el-form-item :label="$t('结束时间')" prop="endTime">
                      <el-date-picker
                          v-if="recordType !== 'view'"
                          v-model="formDataRecord.endTime"
                          type="datetime"
                          :disabled-date="disabledEndTime"
                          :placeholder="$t('结束时间')"
                          value-format="YYYY-MM-DD HH:mm:ss"
                      />
                      <el-tag v-else>{{ formDataRecord.endTime || '-' }}</el-tag>
                    </el-form-item>

                    <el-form-item :label="$t('备注')" prop="remark">
                      <ltw-input
                          :disabled="
                        (recordType === 'view' || formDataRecord.status === 'released') && recordType !== 'copy'
                      "
                          v-model="formDataRecord.description"
                          textType="remark"
                          type="textarea"
                      ></ltw-input>
                      <!--                    <div v-else v-text="formDataRecord.description || '-'"></div>-->
                    </el-form-item>
                  </el-form>
                </div>
              </div>
              <div class="step1" v-show="activeStep === 1">
                <div v-if="installRecord">
                  <div class="header-title">
                    <el-button
                        v-if="formDataRecord.status !== 'released'"
                        size="small"
                        type="primary"
                        @click="chooseModality"
                    >
                      <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                      {{ $t('新增') }}
                    </el-button>
                  </div>
                  <el-table
                      :row-class-name="setClassName"
                      :data="formDataRecord.versionMappingModalityVOS"
                      ref="tableRef"
                  >
                    <el-table-column type="expand" fixed="left">
                      <template #default="props">
                        <el-form class="expand-form" label-width="140px">
                          <el-row :gutter="10">
                            <el-col :span="8">
                              <el-form-item :label="$t('分辨率')">
                                {{ props.row.cameraVO?.resolution }}
                              </el-form-item>
                            </el-col>
                            <el-col :span="8">
                              <el-form-item :label="$t('畸变模型')">
                                {{ props.row.cameraVO?.distortionModel }}
                              </el-form-item>
                            </el-col>
                            <el-col :span="8">
                              <el-form-item :label="$t('相邻两行曝光间隔')">
                                {{ props.row.cameraVO?.exposureInterval }}us
                              </el-form-item>
                            </el-col>
                            <el-col :span="8">
                              <el-form-item :label="$t('水平视角')">
                                {{ props.row.cameraVO?.hfov }}
                              </el-form-item>
                            </el-col>
                            <el-col :span="8">
                              <el-form-item :label="$t('垂直视角')">
                                {{ props.row.cameraVO?.vfov }}
                              </el-form-item>
                            </el-col>
                            <!-- <el-col :span="8"
                              ><el-form-item :label="$t('备注')">
                              {{ props.row.cameraVO?.remark }}
                              </el-form-item></el-col
                            > -->
                          </el-row>
                        </el-form>
                      </template>
                    </el-table-column>
                    <el-table-column type="index" width="50" fixed="left"/>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('编码')"
                        prop="modality"
                        show-overflow-tooltip
                        width="150"
                        fixed="left"
                    >
                      <!-- <template #default="scope">
                        <el-link
                        type="primary"
                        @click="viewModality(scope.row)"
                        :underline="false"
                        >{{ scope.row.modality }}</el-link
                        >
                      </template> -->
                    </el-table-column>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('传感器')"
                        show-overflow-tooltip
                        width="130"
                        prop="modalityName"
                    />
                    <el-table-column header-align="left" align="left" :label="$t('传感器类型')" prop="sensorTypeName"/>
                    <el-table-column header-align="left" align="left" :label="$t('供应商')" prop="supplierName"/>
                    <el-table-column header-align="left" align="left" :label="$t('型号')" prop="model"/>
                    <el-table-column header-align="left" align="left" :label="$t('规格')" prop="specification"/>
                    <el-table-column header-align="left" align="left" :label="$t('msop')" prop="msop"/>
                    <el-table-column header-align="left" align="left" :label="$t('difop')" prop="difop"/>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('帧同步偏移值' + '(ms)')"
                        prop="pcdJpgOffset"
                    />
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('间隔差的基准值') + '(ms)'"
                        prop="intervalDif"
                    />
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('备注')"
                        show-overflow-tooltip
                        prop="remark"
                    />
                    <el-table-column header-align="left" align="left" fixed="right" :label="$t('操作')" width="195">
                      <template #default="scope">
                        <el-button-group>
                          <el-popover placement="left" width="400" trigger="hover">
                            <template #reference>
                              <el-button
                                  v-if="formDataRecord.status !== 'released' || scope.row.files?.length"
                                  text
                                  type="primary"
                                  @click="showModalityUploadDialog(scope.row)"
                              >
                                <!-- <ltw-icon icon-code="el-icon-upload"></ltw-icon> -->
                                <ltw-icon icon-code="el-icon-paperclip"></ltw-icon>
                              </el-button>
                            </template>
                            <el-table :data="scope.row.files">
                              <el-table-column width="120" prop="sourceType" label="sourceType"></el-table-column>
                              <el-table-column prop="fileName" label="fileName">
                                <template #default="scope">
                                  <el-link target="_blank" underline :href="scope.row.url">
                                  <span style="display: inline">
                                    <span>{{ scope.row.fileName }}</span>
                                    <span v-if="scope.row.fileType">.{{ scope.row.fileType }}</span>
                                  </span>
                                  </el-link>
                                </template>
                              </el-table-column>
                            </el-table>
                          </el-popover>
                          <el-tooltip effect="dark" :content="$t('编辑')" placement="top" :enterable="false">
                            <el-button
                                text
                                type="warning"
                                @click="editModality(scope.row)"
                                v-if="formDataRecord.status !== 'released'"
                            >
                              <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                            </el-button>
                          </el-tooltip>
                          <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                            <el-button
                                text
                                type="danger"
                                @click="deleteModality(scope.row)"
                                v-if="formDataRecord.status !== 'released'"
                            >
                              <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                            </el-button>
                          </el-tooltip>
                          <el-tooltip effect="dark" :content="$t('查看详情')" placement="top" :enterable="false">
                            <el-button text type="primary" @click="viewModality(scope.row)">
                              <ltw-icon icon-code="el-icon-view"></ltw-icon>
                            </el-button>
                          </el-tooltip>
                        </el-button-group>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-if="competitor">
                  <el-table
                      @row-click="handleCompetitorRowClick"
                      :data="formDataRecord.vehicleSparePartVOList"
                      ref="tableRef"
                  >
                    <el-table-column type="index" width="50" fixed="left"/>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('对手件')"
                        prop="name"
                        show-overflow-tooltip
                        fixed="left"
                    >
                      <template #default="scope">
                        <!--                        <ltw-input-->
                        <!--                            v-if="scope.row.edit"-->
                        <!--                            v-model="scope.row.sparePartName"-->
                        <!--                        ></ltw-input>-->
                        <span>{{ scope.row.sparePartName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('当前版本')"
                        show-overflow-tooltip
                        prop="version"
                    >
                      <template #default="scope">
                        <ltw-input
                            v-if="(scope.row.edit && recordType === 'edit')|| recordType ==='add'"
                            v-model="scope.row.version"
                        ></ltw-input>
                        <span v-else>{{ scope.row.version }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                        header-align="left"
                        align="left"
                        :label="$t('目标版本')"
                        show-overflow-tooltip
                        prop="targetVersion"
                    >
                      <template #default="scope">
                        <ltw-input
                            v-if="(scope.row.edit && recordType === 'edit')|| recordType ==='add'"
                            v-model="scope.row.targetVersion"
                        ></ltw-input>
                        <span v-else>{{ scope.row.targetVersion }}</span>
                      </template>
                    </el-table-column>
                    <!--                    <el-table-column-->
                    <!--                      header-align="left"-->
                    <!--                      align="left"-->
                    <!--                      :label="$t('文件')"-->
                    <!--                      show-overflow-tooltip-->
                    <!--                      prop="fileName"-->
                    <!--                  />-->
                    <el-table-column header-align="left" align="left" fixed="right" :label="$t('操作')" width="150"
                                     v-if="recordType === 'edit'">
                      <template #default="scope">
                        <div class="footer">
                          <el-button type="danger" circle @click="deleteVehicleSparePart(scope.row)">
                            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                          </el-button>
                          <el-button type="success" circle @click="updateVehicleSparePart(scope.row)">
                            <ltw-icon icon-code="el-icon-check"></ltw-icon>
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-descriptions :column="2" v-if="formDataRecord.sourceFileName">
                    <el-descriptions-item label="日期">{{ formDataRecord.startTime }}</el-descriptions-item>
                    <el-descriptions-item label="来源文件" style="margin-left: 50px">
                      <el-link type="primary" @click="downloadFile(formDataRecord.sourceFileId)">
                        {{ formDataRecord.sourceFileName }}.{{ formDataRecord.sourceFileType }}
                      </el-link>
                    </el-descriptions-item>
                    <el-descriptions-item label="描述">{{ formDataRecord.description }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div class="dialog-footer">
                <!-- <el-button
							  @click="type = ''"
							  v-if="type === 'view'"
							  id="view-cancel"
							  >{{ $t('关闭') }}</el-button
							>
							<template v-else> -->
                <!-- <template> -->
                <el-button v-show="activeStep === 0 && recordType !== 'view'" @click="cancel()" id="cancel"
                >{{ $t('取消') }}
                </el-button>
                <el-button v-show="activeStep === 0 && recordType === 'add'" type="primary" @click="next" id="next"
                >{{ $t('下一步') }}
                </el-button>
                <el-button
                    v-show="activeStep === 1 && recordType === 'add'"
                    type="primary"
                    @click="previousStep"
                    id="previousStep"
                >{{ $t('上一步') }}
                </el-button>
                <el-button
                    v-show="activeStep === 0 && (recordType === 'edit' || recordType === 'copy')"
                    type="primary"
                    @click="next"
                >{{ $t('保存') }}
                </el-button>
                <el-button v-show="activeStep === 1 && recordType === 'add' && installRecord" type="primary"
                           @click="cancel('reload')"
                >{{ $t('完成') }}
                </el-button>
                <el-button v-show="activeStep === 1 && recordType === 'add' && competitor" type="primary"
                           @click="submitVehicleSpareParts"
                >{{ $t('保存') }}
                </el-button>
                <!-- </template> -->
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-card>
    </div>
  </div>
  <AddModality ref="AddModality"></AddModality>
  <PreviewFile ref="PreviewFile" @reload="listFtmVehicleInstallationRecord"></PreviewFile>
  <ChooseModality ref="ChooseModality" @reload="listFtmVehicleMappingModality"></ChooseModality>
  <ChooseFleetVersion ref="ChooseFleetVersion" @reload="loadVersion"></ChooseFleetVersion>
  <AddFleetModality ref="AddFleetModality" @reload="loadFleetModality"/>
  <modality-file-upload-dialog ref="ModalityFileUploadDialog"/>
</template>

<script>
import {listSysDictionary} from '@/apis/system/sys-dictionary'
import {deleteFtmVehicleVariant} from '@/apis/fleet/ftm-vehicle-variant'
import {saveBsVehicle, updateBsVehicle, getBsVehicle} from '@/apis/fleet/bs-vehicle'
import {copyFtmVehicleVariantVersion, getFtmVehicleVariantVersion} from '@/apis/fleet/ftm-vehicle-variant-version'
import {
  listFtmVehicleMappingModality,
  deleteFtmVehicleMappingModalitys
} from '@/apis/fleet/ftm-variant-mapping-modality'
import {
  saveFtmVehicleInstallationRecord,
  updateFtmVehicleInstallationRecord,
  deleteFtmVehicleInstallationRecord,
  listFtmVehicleInstallationRecord,
  copyFtmVehicleInstallationRecord
} from '@/apis/fleet/ftm-vehicle-installation-record'
import {
  listFtmSparePart,
  listFtmSparePartVersions,
  listFtmVehicleSpareParts,
  saveFtmSparePartVersion,
  updateFtmSparePartVersion,
  updateFtmVehicleSparePart,
  deleteFtmVehicleSparePart,
  saveFtmVehicleSparePartList,
  deleteFtmSparePartVersion,
  getFtmSparePartVersion
} from '@/apis/fleet/ftm-spare-part'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import {
  showToast,
  showConfirmToast,
  validateFourDecimalValidity,
  validateTwoDigitInteger,
  isPositiveNum,
  isInteger,
  dateUtils
} from '@/plugins/util'
import util from '@/plugins/util'
import ChooseModality from '@/pages/fleet/dialog/ChooseModality.vue'
import AddFleetModality from '@/pages/fleet/dialog/AddFleetModality.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import ChooseFleetVersion from '@/pages/fleet/dialog/ChooseFleetVersion.vue'
import AddModality from '@/pages/fleet/dialog/AddModality.vue'
import PreviewFile from '@/pages/fleet/dialog/PreviewFile.vue'
import ModalityFileUploadDialog from '@/pages/fleet/dialog/ModalityFileUploadDialog.vue'
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  components: {
    ChooseModality,
    Codemirror,
    EmployeeSelection,
    DictionarySelection,
    UploadFile,
    ChooseFleetVersion,
    AddModality,
    PreviewFile,
    AddFleetModality,
    ModalityFileUploadDialog
  },
  name: 'FleetModelManagement',
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      selectedData: [],
      currentOrg: {},
      pageData: {
        // total: 0
      },
      queryParam: {
        // current: 1,
        // size: 10
      },
      useTypeList: [],
      formDataVehicle: {},
      type: '',
      versionPageData: [],
      activeStep: 0,
      activeStepRecord: '',
      //新增车型版本
      formDataRecord: {},
      rawDataBagTypeList: [],
      cmOptions: {},
      activeTab: 'vehicleEndDataTemplate',
      formRulesVehicle: {
        vin: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        excludeRearviewMirrorWidth: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        includeRearviewMirrorWidth: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        toHeadstockDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        toTailstockDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        egoCenterShiftDistance: [
          {
            validator: validateFourDecimalValidity,
            trigger: ['change']
          },
          {
            validator: validateTwoDigitInteger,
            trigger: ['change']
          },
          {
            validator: isPositiveNum,
            trigger: ['change']
          }
        ],
        wheelBase: [
          {
            validator: isInteger,
            trigger: 'change'
          },
          {
            validator: isPositiveNum,
            trigger: 'change'
          }
        ],
        keeperEmpId: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ]
      },
      formRulesRecord: {
        version: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        startTime: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ]
      },
      recordList: [],
      modelCheckAll: false,
      vehicleType: '',
      sourceType: '',
      recordType: '',
      backupFormDataVehicle: {},
      backupformDataRecord: {},
      collapsed: false
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    if (this.$route.query.type) {
      this.vehicleType = this.$route.query.type
      this.sourceType = this.$route.query.sourceType
      this.getBsVehicle(this.$route.query.id)
    } else {
      this.vehicleType = 'add'
    }
  },
  computed: {
    installRecord() {
      return this.sourceType === 'installRecord'
    },
    competitor() {
      return this.sourceType === 'competitor'
    }

  },
  methods: {
    downloadFile(id) {
      const url = this.downloadUrl + id + '?token=' + util.getToken()
      window.open(url)
    },
    updateVehicleSparePart(row) {
      updateFtmVehicleSparePart(row).then(() => {
        showToast('保存成功')
        this.listFtmVehicleSparePart()
      })
    },
    deleteVehicleSparePart(row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleSparePart({
          id: row.id
        }).then(() => {
          showToast('删除成功')
          this.listFtmVehicleSparePart()
        })
      })
    },
    submitVehicleSpareParts() {
      saveFtmVehicleSparePartList(this.formDataRecord.vehicleSparePartVOList).then(res => {
        showToast('保存成功')
        this.recordType = 'view'
        this.formDataRecord.vehicleSparePartVOList = res.data
        this.activeStep = 0
        this.listFtmSparePartVersions()
      })
    }
    ,
    handleCompetitorRowClick(row) {
      row.edit = true
      this.formDataRecord.vehicleSparePartVOList.forEach(vm => {
        if (vm.id !== row.id) {
          vm.edit = false;
        }
      });
    },
    getBsVehicle(id) {
      getBsVehicle(id).then(res => {
        this.formDataVehicle = res.data
        this.backupFormDataVehicle = res.data
        if (this.installRecord) {
          this.listFtmVehicleInstallationRecord()
        } else if (this.competitor) {
          this.listFtmSparePartVersions()
        }
      })
    },
    listFtmSparePartVersions() {
      listFtmSparePartVersions({vin: this.formDataVehicle.vin}).then(res => {
        this.recordList = res.data
        if (this.recordList?.length) {
          this.recordType = 'view'
          // if (this.activeStepRecord === '') {
          this.activeStepRecord = this.recordList?.length - 1
          // }
          this.formDataRecord = JSON.parse(JSON.stringify(this.recordList[this.activeStepRecord]))
          this.backupformDataRecord = JSON.parse(JSON.stringify(this.recordList[this.activeStepRecord]))
          this.getModalityList()
        } else {
          this.recordType = ''
        }
      })
    },
    listFtmVehicleInstallationRecord() {
      listFtmVehicleInstallationRecord({vin: this.formDataVehicle.vin}).then(res => {
        // res.data = res.data.reverse() //yifan后端加了逻辑
        this.recordList = res.data
        if (res.data?.length) {
          this.recordType = 'view'
          // if (this.activeStepRecord === '') {
          this.activeStepRecord = res.data?.length - 1
          // }
          this.formDataRecord = JSON.parse(JSON.stringify(res.data[this.activeStepRecord]))
          this.backupformDataRecord = JSON.parse(JSON.stringify(res.data[this.activeStepRecord]))
          this.getModalityList()
        } else {
          this.recordType = ''
        }
      })
    },
    changeRecord(item, index) {
      this.formDataRecord = JSON.parse(JSON.stringify(item))
      this.activeStepRecord = index
      this.getModalityList()
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleVariant(param).then(() => {
          this.formDataVehicle = {}
        })
      })
    },
    handleCommand(command) {
      if (this.formDataVehicle.id) {
        if (command === 'batchRemove') {
          // if (this.selectedData.length === 0) {
          //   return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
          // }
          this.singleRemove(this.formDataVehicle)
        } else if (command === 'edit') {
          this.vehicleType = 'edit'
          // this.edit(this.formDataVehicle)
        }
      } else {
        this.$message.warning({
          message: BASE_CONSTANT.BATCH_OPERATION_WARNING,
          type: 'warning'
        })
      }
    },
    singleRemove(row) {
      this.remove({id: row.id})
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ids})
    },
    addInstallationProject() {
      this.recordType = 'add'
      this.activeStep = 0
      this.formDataRecord = {}
    },
    copyInstallationProject() {
      this.backupFormDataVehicle = JSON.parse(JSON.stringify(this.formDataRecord))
      this.formDataRecord.startTime = this.formDataRecord.endTime ? dateUtils.parseTime(new Date(this.formDataRecord.endTime), '{y}-{m}-{d} {h}:{i}:{s}') : ''
      this.formDataRecord.endTime = ''
      this.formDataRecord.status = 'draft'
      this.recordType = 'copy'
    },
    editInstallationProject() {
      this.recordType = 'edit'
    },
    deleteInstallationProject() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleInstallationRecord({
          id: this.formDataRecord.id,
          vehicleId: this.formDataVehicle.id
        }).then(() => {
          this.activeStepRecord = ''
          this.listFtmVehicleInstallationRecord()
        })
      })
    },
    deleteVehicleSparePartVersion() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmSparePartVersion({
          id: this.formDataRecord.id
        }).then(() => {
          this.activeStepRecord = ''
          this.listFtmSparePartVersions()
        })
      })
    },
    listRawDataTypeList() {
      if (!this.rawDataBagTypeList?.length) {
        listSysDictionary({typeCode: 'raw_data_bag_type'}).then(res => {
          this.rawDataBagTypeList = res.data
          if (this.formDataRecord.id) {
            this.formDataRecord.rawDataBagTypeNameList = this.formDataRecord.rawDataBagType.map(val =>
                this.getDicName(val, this.rawDataBagTypeList)
            )
          }
        })
      }
    },
    getTypeNmae(type) {
      let typeName = ''
      switch (type) {
        case 'add':
          typeName = '新增'
          break
        case 'edit':
          typeName = '编辑'
          break
        case 'view':
          typeName = '编辑'
          break
        case 'copy':
          typeName = '复制'
          break
      }
      return typeName
    },
    cancel(val) {
      if (this.recordList?.length) {
        this.$refs.formRef.resetFields()
        this.recordType = 'view'
        this.activeStep = 0
        // this.activeTab = 'vehicleEndDataTemplate'
      } else {
        this.recordType = ''
      }
      if (val === 'reload') {
        this.listFtmVehicleInstallationRecord()
      } else {
        this.formDataRecord = JSON.parse(JSON.stringify(this.backupformDataRecord))
      }
    },
    previousStep() {
      this.activeStep = 0
    },
    next() {
      if (this.type !== 'view') {
        this.$refs.formRef.validate(valid => {
          if (!valid) return
          let postData = {
            ...this.formDataRecord,
            vin: this.formDataVehicle.vin,
            vehicleId: this.formDataVehicle.id
          }
          if (this.recordType === 'copy') {
            this.copyFtmVehicleInstallationRecord()
          } else if (this.recordType === 'add') {
            // //防止出现新增多个车型版本的时候，上一个新增的id数据在新增第二个版本时带给后台
            // postData.id = null
            if (this.formDataRecord.id) {
              if (this.installRecord) {
                updateFtmVehicleInstallationRecord(postData).then(() => {
                  this.activeStep++
                  showToast('保存成功')
                })
              } else if (this.competitor) {
                updateFtmSparePartVersion(postData).then(() => {
                  this.activeStep++
                  showToast('保存成功')
                })
              }
            } else {
              if (this.installRecord) {
                saveFtmVehicleInstallationRecord(postData).then(res => {
                  this.formDataRecord = res.data
                  this.activeStep++
                  showToast('保存成功')
                  this.getModalityList()
                })
              } else if (this.competitor) {
                saveFtmSparePartVersion(postData).then(res => {
                  this.formDataRecord = res.data
                  this.activeStep++
                  showToast('保存成功')
                  this.getModalityList()
                })
              }
            }
          } else if (this.recordType === 'edit') {
            if (this.installRecord) {
              updateFtmVehicleInstallationRecord(postData).then(() => {
                showToast('保存成功')
                this.listFtmVehicleInstallationRecord()
              })
            } else if (this.competitor) {
              updateFtmSparePartVersion(postData).then(() => {
                showToast('保存成功')
                this.listFtmSparePartVersions()
              })
            }
          }
        })
      } else {
        this.activeStep++
      }
    },
    chooseModality() {
      this.$refs.ChooseModality.show({
        type: 'view',
        data: {
          vehicleId: this.formDataVehicle.id,
          installationRecordId: this.formDataRecord.id,
          variantVersionId: this.formDataRecord.variantVersionId,
          variant: this.formDataRecord.code
        }
      })
    },
    viewModality(row) {
      this.$refs.AddFleetModality.show({
        type: 'view',
        data: row
      })
    },
    editModality(row) {
      this.$refs.AddFleetModality.show({
        type: 'edit',
        data: {...row, useType: this.formDataVehicle.useType}
      })
    },
    changeVersion(row) {
      this.formDataRecord = row
      this.getFtmVehicleVariantVersion()
    },
    getFtmVehicleVariantVersion() {
      getFtmVehicleVariantVersion(this.formDataRecord.id).then(res => {
        res.data.rawDataBagType = res.data.rawDataBagType?.split(',')
        if (this.rawDataBagTypeList?.length) {
          res.data.rawDataBagTypeNameList = res.data.rawDataBagType.map(val =>
              this.getDicName(val, this.rawDataBagTypeList)
          )
        }
        this.formDataRecord = res.data
        this.backupformDataRecord = JSON.parse(JSON.stringify(this.formDataRecord))
      })
    },
    getDicName(item, list) {
      const dicItem = list.find(val => item === val.code)
      return dicItem.name
    },
    setClassName({row}) {
      // 通过自己的逻辑返回一个class或者空
      return row.sensorType !== 'camera' ? 'no-expand' : ''
    },
    handleModelCheckAllChange(val) {
      this.selectedData = val ? this.pageData.records : []
      this.pageData.records.forEach(item => (item.checked = val))
      // this.modelIsIndeterminate = false
    },
    onBack() {
      this.$router.go(-1)
    },

    // clearWheelBase(data) {
    //   if (data === null) {
    //     this.formDataVehicle.wheelBase = 0
    //   }
    // },
    // clearExcludeRearviewMirrorWidth(data) {
    //   if (data === '') {
    //     this.formDataVehicle.excludeRearviewMirrorWidth = 0
    //   }
    // },
    // clearIncludeRearviewMirrorWidth(data) {
    //   if (data === '') {
    //     this.formDataVehicle.includeRearviewMirrorWidth = 0
    //   }
    // },
    // clearToHeadstockDistance(data) {
    //   if (data === '') {
    //     this.formDataVehicle.toHeadstockDistance = 0
    //   }
    // },
    // clearToTailstockDistance(data) {
    //   if (data === '') {
    //     this.formDataVehicle.toTailstockDistance = 0
    //   }
    // },
    // clearEgoCenterShiftDistance(data) {
    //   if (data === '') {
    //     this.formDataVehicle.egoCenterShiftDistance = 0
    //   }
    // },
    cancelVehicle() {
      this.vehicleType = 'view'
      this.formDataVehicle = JSON.parse(JSON.stringify(this.backupFormDataVehicle))
    },
    submit() {
      this.$refs.formRefVehicle.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.formDataVehicle,
          wheelBase: this.formDataVehicle.wheelBase || 0,
          excludeRearviewMirrorWidth: this.formDataVehicle.excludeRearviewMirrorWidth || 0,
          includeRearviewMirrorWidth: this.formDataVehicle.includeRearviewMirrorWidth || 0,
          toHeadstockDistance: this.formDataVehicle.toHeadstockDistance || 0,
          toTailstockDistance: this.formDataVehicle.toTailstockDistance || 0,
          egoCenterShiftDistance: this.formDataVehicle.egoCenterShiftDistance || 0
        }
        if (!postData.id) {
          saveBsVehicle(postData).then(res => {
            this.formDataVehicle = res.data
            this.backupFormDataVehicle = res.data
            this.vehicleType = 'view'
            showToast('保存成功')
            this.sourceType = 'installRecord'
          })
        } else {
          updateBsVehicle(postData).then(res => {
            this.formDataVehicle = res.data
            this.backupFormDataVehicle = res.data
            this.vehicleType = 'view'
            showToast('保存成功')
          })
        }
      })
    },
    editVehicle() {
      this.vehicleType = 'edit'
    },
    disabledEndTime(val) {
      if (this.formDataRecord.startTime) {
        return new Date(val) < new Date(dateUtils.parseTime(this.formDataRecord.startTime, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    disabledStartTime(val) {
      if (this.formDataRecord.endTime) {
        return new Date(val) > new Date(dateUtils.parseTime(this.formDataRecord.endTime, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    chooseFleetVersion() {
      this.$refs.ChooseFleetVersion.show({
        type: 'choose',
        version: this.formDataRecord.version
      })
    },
    loadVersion(obj) {
      this.formDataRecord.version = obj.version
      this.formDataRecord.code = obj.code
      this.formDataRecord.variantVersionId = obj.versionId
      // this.formDataRecord.modelId = obj.modelId
    },
    loadFleetModality(val) {
      if (val) {
        this.listFtmVehicleMappingModality()
      }
    },
    listFtmVehicleMappingModality() {
      let postData = {
        vehicleId: this.formDataVehicle.id,
        variantVersionId: this.formDataRecord.variantVersionId,
        installationRecordId: this.formDataRecord.id
      }
      listFtmVehicleMappingModality(postData).then(res => {
        this.formDataRecord.versionMappingModalityVOS = res.data
        this.formDataRecord.versionMappingModalityVOS.forEach(val => {
          if (val.files?.length) {
            val.files.forEach(f => {
              f.url = this.downloadUrl + f.id + '?token=' + util.getToken()
            })
          }
        })
      })
    },
    listFtmVehicleSparePart() {
      let postData = {
        vin: this.formDataVehicle.vin,
        versionId: this.formDataRecord.id
      }
      getFtmSparePartVersion(this.formDataRecord.id).then(res => {
        if ((this.recordType === 'add') && res.data.vehicleSparePartVOList?.length === 0) {
          //如果没有车辆对手件，查询基础对手件进行渲染
          listFtmSparePart().then(res => {
            this.formDataRecord.vehicleSparePartVOList = []
            res.data.forEach(sp => {
              this.formDataRecord.vehicleSparePartVOList.push({
                sparePartName: sp.name,
                sparePartCode: sp.code,
                versionId: this.formDataRecord.id,
                vin: this.formDataRecord.vin
              })
            })
          })
        } else {
          this.formDataRecord = res.data
        }
      })
    },
    deleteModality(row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmVehicleMappingModalitys({
          id: row.id
        }).then(() => {
          showToast('删除成功')
          this.listFtmVehicleMappingModality()
        })
      })
    },
    getModalityList() {
      if (this.activeStep === 1) {
        if (this.installRecord) {
          this.listFtmVehicleMappingModality()
        } else if (this.competitor) {
          this.listFtmVehicleSparePart()
        }
      }
    },
    previewFile() {
      const btn = this.$route.query.btn ? JSON.parse(decodeURIComponent(this.$route.query.btn)) : ''
      this.$refs.PreviewFile.show({
        type: 'view',
        id: this.formDataRecord.id,
        vin: this.formDataVehicle.vin,
        // version: this.formDataRecord.version,
        status: this.formDataRecord.status,
        statusName: this.formDataRecord.statusName,
        btn: this.formDataRecord.status === 'released' && btn
      })
    },
    toggleCollapsed() {
      this.collapsed = !this.collapsed
    },
    getBtnList(btnList, code) {
      return !!~btnList.findIndex(val => val.buttonCode === code)
    },
    showModalityUploadDialog(row) {
      this.$refs.ModalityFileUploadDialog.show({
        id: row.id,
        type: this.formDataRecord.status === 'released' ? 'view' : 'edit'
      })
    },
    copyFtmVehicleInstallationRecord() {
      let postData = {
        cloneId: this.formDataRecord.id,
        vin: this.formDataRecord.vin,
        variantVersionId: this.formDataRecord.variantVersionId,
        startTime: this.formDataRecord.startTime,
        endTime: this.formDataRecord.endTime,
        description: this.formDataRecord.description
      }
      copyFtmVehicleInstallationRecord(postData).then(() => {
        showToast('复制成功')
        this.cancel('reload')
      })
    },
    changeKeeperEmp(e) {
      this.formDataVehicle.keeperEmpName = e.node.name
    }
    // getFormData(item) {
    //   this.getFtmVehicleVariantVersion(item)
    //   // this.formData = item
    // }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/var.scss';

.add-fleet-mangement {
  display: flex;
  flex-direction: column;
  height: calc(100vh - $header-height - $footer-height - var(--el-main-padding));

  .page-header {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-light);

    :deep(.el-page-header__breadcrumb) {
      margin: 0;
    }

    :deep(.el-page-header__content) {
      font-weight: 600;
    }
  }

  .add-fleet-body {
    display: flex;
    flex-direction: row;
    height: calc(100% - 45px);
    width: 100%;

    .el-scrollbar {
      width: 100%;
    }

    :deep(.el-scrollbar__view) {
      display: flex;
    }

    // height: calc(100vh - $footer-height - $header-height - 55px - var(--el-main-padding));
    .fleet-model {
      position: relative;
      width: 35%;
      transition: all 0.3s;
      // margin-right: 14px;
      &.no-width {
        width: 0;

        .fleet-model-card {
          border: none;
        }
      }

      .fleet-model-card {
        height: 100%;
        white-space: nowrap;

        & > :deep(.el-card__header) {
          padding: 10px;
        }
      }

      :deep(.el-card__body) {
        height: calc(100% - 42px);

        .el-form {
          padding: 0 10px;
        }
      }

      :deep(.el-scrollbar__view) {
        flex-direction: column;
      }

      // & > :deep(.el-card__body) {
      //   height: 100%;
      //   display: flex;
      //   flex-direction: column;
      // }
      .card-header {
        display: flex;
        justify-content: space-between;
      }

      .footer {
        text-align: right;
      }
    }

    .card-collapsed {
      position: relative;
      z-index: 1;

      .ltw-icon {
        cursor: pointer;
        position: relative;
        top: 50%;
        transition: all 0.3s;
        font-size: 12px;
        color: #73767a;

        &.collapsed-icon {
          transform: rotateY(180deg);
        }
      }
    }

    .fleet-version {
      width: 65%;
      position: relative;

      .el-breadcrumb {
        line-height: 30px;

        :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner, .el-breadcrumb__item:last-child
            .el-breadcrumb__inner
            a, .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover, .el-breadcrumb__item:last-child
            .el-breadcrumb__inner:hover) {
          font-weight: 600;
        }
      }

      &.full-width {
        width: 100%;
      }

      .card-opt {
        position: absolute;
        right: 0;
        top: 0;

        .el-button + .el-button {
          margin-left: 0;
        }
      }

      .empty-content {
        text-align: center;
      }

      :deep(.el-scrollbar__view) {
        flex-direction: column;
      }

      :deep(.el-card__body) {
        height: 100%;
      }

      .fleet-version-content {
        .version-list {
          // display: flex;
          // justify-content: center;
          width: 100%;
          margin: 20px 0 10px 0;

          .el-step {
            cursor: pointer;
          }

          // :deep(.el-step__description){
          //   white-space: nowrap;
          // }
          // .version-item {
          //   flex-shrink: 0;
          //   width: 60px;
          //   text-align: center;
          //   padding: 10px;
          //   cursor: pointer;
          //   margin-right: 20px;
          //   color: #409eff;
          //   border-radius: 4px;
          //   background: #ecf5ff;
          //   border-color: #d9ecff;
          //   transition: all 0.3s;
          //   &:hover {
          //     background: #409eff;
          //     color: white;
          //   }
          //   &.active {
          //     background: #409eff;
          //     color: white;
          //   }
          // }
        }

        .version-detail {
          .step1 {
            position: relative;

            .preview-list {
              position: absolute;
              right: 0;
              top: 0;
              display: flex;
              align-items: center;
            }
          }

          .form-record {
            width: 400px;
            margin: 0 auto;

            .choose-btn {
              &.choose-active {
                .ltw-icon {
                  color: #000;
                }
              }

              .ltw-icon {
                font-size: 16px;
              }
            }
          }

          .el-tag {
            margin-right: 10px;
          }

          .dialog-footer {
            margin-top: 10px;
            text-align: center;
          }

          .expand-form {
            width: 800px;
          }

          :deep(.no-expand) .el-table__expand-column .cell {
            display: none;
          }
        }

        .step-content {
          margin: 10px 0;
          // padding: 0 15%;
        }
      }
    }
  }

  .paperclip-class {
    font-size: large;
  }
}
</style>
