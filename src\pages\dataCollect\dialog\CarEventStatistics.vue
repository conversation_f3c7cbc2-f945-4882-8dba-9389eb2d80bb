<template>
  <el-date-picker
    style="margin-left: 35px"
    clearable
    @change="getTagRecordsChart"
    value-format="YYYY-MM-DD HH:mm:ss"
    v-model="timeRange"
    type="daterange"
    range-separator="To"
    start-placeholder="Start date"
    end-placeholder="End date"
  />
  <div id="car-event-statistics">
    <!-- <div class="no-data">no data</div> -->
  </div>
</template>
<script>
import { ElDialog, ElButton, ElDatePicker } from 'element-plus'
import { getTagRecordsChart } from '@/apis/data-collect/vt-daq-task'
import { dateUtils } from '@/plugins/util.js'
export default {
  name: 'CarEventStatistics',
  data() {
    return {
      dialogTitle: 'EventStatistics',
      dialogVisible: false,
      eventData: [],
      timeRange: [],
      taskCode: ''
    }
  },
  components: {
    ElD<PERSON>og,
    <PERSON><PERSON>utton,
    ElDatePicker
  },
  // mounted() {
  //   this.getCharts();
  // },
  methods: {
    show(row) {
      this.dialogVisible = true
      let endTime
      if (row.endTime) {
        endTime = row.endTime
      } else {
        endTime = new Date()
      }
      endTime =
        dateUtils.parseTime(new Date(endTime), '{y}-{m}-{d}') + ' 23:59:59'
      let startTime = dateUtils.parseTime(
        +new Date(endTime) - 60 * 60 * 24 * 6 * 1000,
        '{y}-{m}-{d} {h}:{i}:{s}'
      )
      this.timeRange = [startTime, endTime]
      // } else {
      //   let now = new Date()
      //   now =
      //     dateUtils.parseTime(
      //       new Date(now.setDate(now.getDate() + 1)),
      //       '{y}-{m}-{d}'
      //     ) +
      //     ' ' +
      //     '23:59:59'
      //   let daysAgo = dateUtils.parseTime(
      //     +new Date(new Date(now).setDate(new Date(now).getDate())) -
      //       60 * 60 * 24 * 6 * 1000,
      //     '{y}-{m}-{d} {h}:{i}:{s}'
      //   )
      //   this.timeRange = [daysAgo, now]
      // }
      this.taskCode = row.code
      this.$nextTick(() => {
        // this.cancel()
        this.$echarts
          .init(document.getElementById('car-event-statistics'))
          .dispose()
      })
      this.getTagRecordsChart()
    },
    getTagRecordsChart() {
      let postData = {
        taskCode: this.taskCode,
        startTime: this.timeRange && this.timeRange[0],
        endTime: this.timeRange && this.timeRange[1]
      }
      getTagRecordsChart(postData).then(res => {
        // if (res.data.tagRecordList && res.data.tagRecordList.length) {
        this.getCharts(res.data)
        // }
      })
    },
    getRandomColor(color) {
      let colorSplit = color.split(',')
      colorSplit.splice(
        color.split(',').length - 1,
        1,
        Number(Number((Math.random() + 1) / 2).toFixed(2)) + ')'
      )
      return colorSplit.join(',')
    },
    getCharts(res) {
      // res.tagRecordList[res.tagRecordList.length-1].endTime = "2022-01-06 23:59:59"
      // res.tagRecordList[res.tagRecordList.length-2].endTime = "2022-01-06 23:59:59"
      let _this = this,
        data = [],
        option,
        tagGroups = [],
        legend = []
      for (let key in res.tagGroupMap) {
        let color = `rgba(${parseInt(Math.random() * 255)}, ${parseInt(
          Math.random() * 255
        )}, ${parseInt(Math.random() * 255)}, 1)`
        let obj = {
          name: key,
          list: []
        }
        res.tagGroupMap[key] &&
          res.tagGroupMap[key].forEach(val => {
            legend.push({ name: val, color: this.getRandomColor(color) })
            obj.list.push({ name: val, color: this.getRandomColor(color) })
          })
        tagGroups.push(obj)
      }
      res.tagRecordList.forEach(val => {
        let index, color
        index = tagGroups.findIndex(item => {
          if (item.name === val.tagGroupName) {
            item.list.findIndex(itemVal => {
              if (itemVal.name === val.tagName) {
                color = itemVal.color
              }
            })
            // color = item.color;
            return true
          }
          return false
        })
        data.push({
          name: val.tagName,
          value: [
            index,
            val.startTime,
            val.endTime,
            new Date(val.endTime) - new Date(val.startTime),
            val.tagType === 'continuous' ? 1 : 0
          ],
          itemStyle: {
            normal: {
              color: color
            }
          }
        })
        color = this.getRandomColor(color)
      })
      this.eventData = data
      let renderItem = (params, api) => {
        var tagGroupsIndex = api.value(0)
        var start = api.coord([api.value(1), tagGroupsIndex])
        var end = api.coord([api.value(2), tagGroupsIndex])
        var height = api.size([0, 1])[1] * 0.6
        var shape
        if (api.value(4) === 0) {
          return {
            type: 'circle',
            shape: {
              cx: start[0],
              cy: start[1] + (Math.random() - 0.5) * 2 * height * 0.5,
              r: 10
            },
            style: api.style()
          }
        } else {
          shape = _this.$echarts.graphic.clipRectByRect(
            {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height
            },
            {
              x: params.coordSys.x,
              y: params.coordSys.y,
              width: params.coordSys.width,
              height: params.coordSys.height
            }
          )

          return (
            shape && {
              type: 'rect',
              shape: shape,
              style: api.style()
            }
          )
        }
      }
      option = {
        axisPointer: {
          link: { xAxisIndex: 'all' },
          label: {
            backgroundColor: '#777'
          }
        },
        tooltip: {
          trigger: 'axis',
          confine: true,
          axisPointer: {
            axis: 'x'
          },
          // axisPointer: {
          //   link: { xAxisIndex: "all" },
          //   label: {
          //     backgroundColor: "#777",
          //   },
          // },
          // order: "valueDesc",
          // trigger: "axis",
          // // trigger: "axis",
          // axisPointer: {
          //   type: "",
          // },
          formatter: function (params) {
            let rangeItems = _this.findRangeItems(params[0].axisValue)

            let str = ''
            rangeItems.forEach(val => {
              let marker = `<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${val.itemStyle.normal.color};\"></span>`
              if (val.value[4] === 1) {
                str +=
                  marker +
                  val.name +
                  ': ' +
                  val.value[3] +
                  ' ms' +
                  '<br />' +
                  'startTime: ' +
                  val.value[1] +
                  '<br/>' +
                  'endTime: ' +
                  val.value[2] +
                  '<br />'
              } else {
                str += marker + val.name + ': ' + val.value[1] + '<br />'
              }
            })
            return str
          }
        },
        legend: {
          type: 'scroll',
          data: legend
        },
        dataZoom: [
          {
            type: 'slider',
            filterMode: 'weakFilter',
            showDataShadow: false,
            bottom: 20,
            height: 10,
            borderColor: 'transparent',
            backgroundColor: '#e2e2e2',
            handleIcon:
              'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z', // jshint ignore:line
            handleSize: 20,
            handleStyle: {
              shadowBlur: 6,
              shadowOffsetX: 1,
              shadowOffsetY: 2,
              shadowColor: '#aaa'
            },
            labelFormatter: ''
          },
          {
            type: 'inside',
            filterMode: 'weakFilter'
          }
        ],
        grid: {
          bottom: 70
          // x: 120,
          // height: 300
        },
        xAxis: {
          type: 'time',
          scale: true,
          minInterval: 1,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          show: res.tagRecordList && res.tagRecordList.length,
          data: tagGroups.map(val => val.name)
        },
        series: [
          {
            type: 'custom',
            renderItem: renderItem,
            itemStyle: {
              opacity: 0.8
            },
            encode: {
              x: [1, 2],
              y: 0
            },
            data: data
          }
        ],
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          silent: true,
          invisible: res.tagRecordList && res.tagRecordList.length,
          style: {
            fill: '#909399',
            // fontWeight: 'bold',
            text: _this.$t('暂无数据')
            // fontSize: '26px'
          }
        }
      }
      legend.forEach(val => {
        option.series.push({
          name: val.name,
          type: 'bar',
          itemStyle: {
            normal: {
              color: val.color //这里的图例要注意，颜色设置和仪表盘的颜色对应起来
            }
          }
        })
      })
      let myChart = this.$echarts.getInstanceByDom(
        document.getElementById('car-event-statistics')
      )
      if (!myChart) {
        myChart = this.$echarts.init(
          document.getElementById('car-event-statistics')
        )
      }
      // let myChart = this.$echarts.init(
      //   document.getElementById('car-event-statistics')
      // )
      window.onresize = function () {
        //适应屏幕的变化，重新计算图形
        myChart.resize()
      }
      myChart.on('legendselectchanged', params => {
        //点击图例可以隐藏对应的机器色块
        var selected = params.selected
        var opt = myChart.getOption()
        opt.series[0].data = data.filter(val => {
          return selected[val.name]
        })
        myChart.setOption(opt, true)
      })
      myChart.setOption(option, true)
    },
    findRangeItems(time) {
      let rangeItems = []
      for (let i = 0, len = this.eventData.length; i < len; i++) {
        let startTime = +new Date(this.eventData[i].value[1])
        let endTime = +new Date(this.eventData[i].value[2])
        let transientFlag =
          this.eventData[i].value[4] === 0 && startTime === time

        let continuousFlag =
          this.eventData[i].value[4] === 1 &&
          ((endTime > time && startTime <= time) ||
            (endTime == time && startTime == time))

        if (transientFlag || continuousFlag) {
          rangeItems.push(this.eventData[i])
        }
      }
      return rangeItems
    },
    cancel() {
      this.$echarts
        .init(document.getElementById('car-event-statistics'))
        .dispose()
      // this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss">
.car-event-dialog {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    height: 100%;
    padding: 0;
  }
}
</style>
<style scoped lang="scss">
#car-event-statistics {
  // height: 100%;
  height: 500px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.no-data {
  color: #909399;
}
</style>
