<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="500"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :destroy-on-close="true"
  >
    <div class="dialog-body">
      <upload-file
        :showFileList="false"
        checkType="parking_lot_import_file"
        ref="uploadImage"
        source-type="parking_lot_import_file"
        :limit="1"
        listType="text"
        accept=".json,.zip"
        id="file"
        tip="limit 1 file"
        :drag="true"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" id="view-cancel">{{ $t('关闭') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'

const defaultFormData = {}
export default {
  name: 'ImportParkingLot',
  emits: ['reload', 'cancel'],
  data() {
    return {
      dialogStatus: '',
      objectValue: {},
      dialogVisible: false,
      dialogTitle: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    UploadFile
  },
  created() {},
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = this.$t('导入数据')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    submit() {},
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.add-form {
  padding: 10px 0;

  .form-footer {
    text-align: right;
  }
}

.sensor-files {
  .json-content {
    position: relative;
    margin-top: 36px;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    // .jsonTxt{
    //   height:200px

    // }
  }

  .sensor-content {
    position: relative;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    .file-type-tabs {
      // position: absolute;
      // top: -10px;
      position: absolute;
      top: -40px;

      :deep(.el-tabs__header) {
        margin: 0;
      }
    }

    .sensor-list-tabs {
      display: flex;
      margin-top: 50px;
      // overflow: visible;
      :deep(.el-tabs__header) {
        margin-right: 0;
        border-right: none;
      }

      :deep(.el-tabs__content) {
        // overflow: visible;
        flex-grow: 1;
        padding: 0;

        .el-tab-pane,
        .el-tab-pane > .border-card-content,
        .el-tab-pane > .border-card-content > .text,
        .el-tab-pane > .border-card-content > .text > .container {
          height: 100%;
        }

        .el-tab-pane > .border-card-content > .text {
          width: 100%;
        }

        // .el-tab-pane > .border-card-content > .text > .container>.dividerStyle {

        //   border: thin solid #3b4969;
        // }
        .el-tab-pane > .border-card-content > .text > .container .jsoneditor {
          border: thin solid #dcdfe6;
        }

        .el-tab-pane > .border-card-content > .text > .container {
          margin: 0 !important;

          .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
            height: 100%;
            margin: 0;
            padding: 0;
          }

          .jsoneditor-menu,
          .jsoneditor-navigation-bar {
            display: none;
          }
        }
      }

      .border-card-content {
        position: relative;

        :deep(.jsoneditor-poweredBy) {
          display: none;
        }
      }
    }
  }
}
</style>
