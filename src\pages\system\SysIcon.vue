<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container" >
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.code" clearable @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>{{item.name}}
                    </el-button>
                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                                                <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                {{ item.name }}</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records" @selection-change="handleSelectionChange" row-key="id"
                      ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                <el-table-column header-align="left" align="left" label="图标" width="180">
                    <template #default="scope">
                        <div class="table-icon-container">
                            <ltw-icon :icon-code="scope.row.code"></ltw-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" label="操作" width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name"
                                        placement="top" :enterable="false"
                            >
                                 <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon></el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="编码" prop="code">
                    <ltw-input v-model="formData.code" :disabled="formReadonly"></ltw-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveSysIcon,
        batchSaveSysIcon,
        updateSysIcon,
        deleteSysIcon,
        pageSysIcon,
        getSysIcon
    } from '@/apis/system/sys-icon'

    

const defaultFormData = {
    }
    export default {
        name: "SysIcon",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],

                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10,
                    fuzzyQuery: true
                },
                dialogVisible: false,
                formData: Object.assign({}, defaultFormData),
                formRules: {
                    code: [
                        {required: true, message: '请输入编码', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData: []
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList

            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(funcName, row) {
                this[funcName](row)
            },
            refresh() {
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysIcon(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加系统图标'
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            batchAdd() {
                this.dialogTitle = '添加系统图标'
                this.dialogStatus = 'batchAdd'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveSysIcon(this.formData).then(
                                () => {
                                    this.dialogVisible = false
                                    this.query()
                                }
                            )
                        }
                        if(this.dialogStatus === 'batchAdd'){
                            batchSaveSysIcon(this.formData).then(
                                () => {
                                    this.dialogVisible = false
                                    this.query()
                                }
                            )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateSysIcon(this.formData).then(
                                () => {
                                    this.dialogVisible = false
                                    this.query()
                                }
                            )
                        }
                    }
                )
            },
            edit({id}) {
                this.dialogTitle = '修改系统图标'
                this.dialogStatus = 'edit'
                getSysIcon(id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                    }
                )
            },
            view({id}) {
                this.dialogTitle = '查看系统图标'
                this.dialogStatus = 'view'
                getSysIcon(id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                    }
                )
            },
            handleCommand(command) {
                if (command === 'batchAdd') {
                    this.batchAdd()
                    return;
                }
                if (this.selectedData.length === 0) {
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if (command === 'batchRemove') {
                    this.batchRemove()
                }

            },
            singleRemove({id}) {
                this.remove({id})
            },
            batchRemove() {
                let idList = [];
                this.selectedData.forEach(ele => {
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param) {
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysIcon(param).then(
                        () => {
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    })
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened() {

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value) {
                this.selectedData = value
            },
            initForm() {
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({}, defaultFormData)
            }

        }
    }
</script>

<style scoped lang="scss">

    .table-icon-container {
        font-size: 21px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

</style>
