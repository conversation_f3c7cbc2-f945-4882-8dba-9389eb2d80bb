<template>
  <el-image
    ref="img"
    :src="imgSrc"
    :alt="alt"
    :fit="fit"
    :hide-on-click-modal="hideOnClickModal"
    :preview-src-list="previewSrcList"
    :referrer-policy="referrerPolicy"
    :scroll-container="scrollContainer"
    :z-index="zIndex"
    :append-to-body="appendToBody"
    @error="handleError"
    :class="['ltw-auth-image', $attrs.class]"
    :style="$attrs.style"
  >
    <template #placeholder>
      <slot name="placeholder"></slot>
    </template>
    <template #error>
      <slot name="error"></slot>
    </template>
  </el-image>
</template>

<script>
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  name: 'LtwAuthImage',
  data() {
    return {
      loading: false,
      hasLoadError: false,
      imgSrc: ''
    }
  },
  props: {
    authSrc: {
      type: String,
      required: false,
      default: ''
    },
    alt: {
      type: String,
      required: false
    },
    fit: {
      type: String,
      required: false,
      validator(value) {
        // 这个值必须匹配下列字符串中的一个
        return ['fill', 'contain', 'cover', 'none', 'scale-down'].includes(
          value
        )
      }
    },
    hideOnClickModal: {
      type: Boolean,
      required: false,
      default: false,
      validator(value) {
        // 这个值必须匹配下列字符串中的一个
        return [true, false].includes(value)
      }
    },
    lazy: {
      type: Boolean,
      required: false,
      default: false
    },
    previewSrcList: {
      type: Array,
      required: false
    },
    referrerPolicy: {
      type: String,
      required: false
    },
    src: {
      type: String,
      required: false
    },
    scrollContainer: {
      type: [String, HTMLElement],
      required: false
    },
    zIndex: {
      type: Number,
      required: false,
      default: 2000
    },
    appendToBody: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  emits: ['load', 'error'],
  mounted() {
    if (this.authSrc) {
      this.loadImage()
    }
  },
  watch: {
    authSrc() {
      this.loadImage()
    }
  },

  methods: {
    handleError(e) {
      this.$emit('error', e)
    },
    loadImage() {
      let token = util.getToken()
      Object.defineProperty(Image.prototype, 'authSrc', {
        writable: true,
        enumerable: true,
        configurable: true
      })
      // let img = this.$refs.img
      let request = new XMLHttpRequest()
      request.responseType = 'blob'
      request.open('get', this.authSrc, true)
      request.setRequestHeader(GLB_CONFIG.tokenKey, token)
      request.onreadystatechange = () => {
        if (
          request.readyState == XMLHttpRequest.DONE &&
          request.status == 200
        ) {
          // img.src = URL.createObjectURL(request.response);
          this.imgSrc = URL.createObjectURL(request.response)
          // console.log(img.src)
          // // this.imgSrc = URL.createObjectURL(request.response);
          // img.onload = () => {
          //     URL.revokeObjectURL(img.src );
          // }
        }
      }
      request.send(null)
      URL.revokeObjectURL(this.imgSrc)
    }
  }
}
</script>

<style scoped lang="scss">
.auth-image__inner,
auth-image__error,
auth-image__placeholder {
  width: 100%;
  height: 100%;
}
</style>
