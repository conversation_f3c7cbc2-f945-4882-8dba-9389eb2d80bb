<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    direction="rtl"
    size="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="sensor-config-form">
      <el-form ref="sensorForm" :model="formData" :rules="formRules" label-width="140px">
        <!-- 传感器基本信息 -->
        <div class="form-section">
          <h3 class="section-title">传感器</h3>

          <el-form-item label="选择传感器类型：" prop="sensorType">
            <dictionary-type-selection
              v-model="formData.sensorType"
              clearable
              dictionaryType="sensor_type"
              placeholder="请选择"
              select-disabled="Vehicle"
              :disabled="mode === 'view'"
              @change="changeSensorType"
              filterable
            />
          </el-form-item>

          <el-form-item label="选择编码：" prop="modality">
            <common-selection
              v-model="formData.modality"
              :model-code="'code'"
              :model-name="'name,code'"
              :model-options="modalityData"
              :disabled="mode === 'view'"
              @change="changeModality"
            />
          </el-form-item>


          <el-form-item label="选择传感器实体：" prop="sensorModelSpecification">
            <el-select
              v-model="formData.sensorModelSpecification"
              placeholder="请选择传感器实体"
              :disabled="mode === 'view'"
              filterable
              @change="handleSensorEntityChange"
              @visible-change="handleSensorDropdownVisible"
              style="width: 100%"
              popper-class="sensor-select-dropdown"
              :teleported="false"
            >
              <!-- 表格头部 -->
              <el-option
                key="header"
                label=""
                value=""
                disabled
                class="table-header-option"
                style="height: auto; padding: 0; cursor: default;"
              >
                <div class="sensor-table-header">
                  <div class="table-row header-row">
                    <div class="table-cell supplier">供应商</div>
                    <div class="table-cell model">型号</div>
                    <div class="table-cell specification">规格</div>
                  </div>
                </div>
              </el-option>

              <el-option
                v-for="sensor in sensorEntityList"
                :key="sensor.id"
                :label="`${sensor.supplierName}-${sensor.model}-${sensor.specification}`"
                :value="sensor.id"
                style="height: auto; padding: 0;"
                :class="{ 'selected-option': formData.sensorId === sensor.id }"
              >
                <div class="sensor-table-option">
                  <div class="table-row data-row" :class="{ 'selected-row': formData.sensorId === sensor.id }">
                    <el-tooltip
                      :content="sensor.supplierName"
                      placement="top"
                      :disabled="!isTextOverflow(sensor.supplierName, 80)"
                    >
                      <div class="table-cell supplier">{{ sensor.supplierName }}</div>
                    </el-tooltip>
                    <el-tooltip
                      :content="sensor.model"
                      placement="top"
                      :disabled="!isTextOverflow(sensor.model, 100)"
                    >
                      <div class="table-cell model">{{ sensor.model }}</div>
                    </el-tooltip>
                    <el-tooltip
                      :content="sensor.specification"
                      placement="top"
                      :disabled="!isTextOverflow(sensor.specification, 120)"
                    >
                      <div class="table-cell specification">{{ sensor.specification }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 安装信息 -->
        <div class="form-section">
          <h3 class="section-title">安装信息</h3>

          <el-form-item label="指导位置x (mm)：" prop="positionX">
            <el-input
              v-model="formData.positionX"
              :disabled="mode === 'view'"
              @change="clearPositionX"
            >
              <template #append>
                <span>mm</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="指导位置y (mm)：" prop="positionY">
            <el-input
              v-model="formData.positionY"
              :disabled="mode === 'view'"
              @change="clearPositionY"
            >
              <template #append>
                <span>mm</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="指导位置z (mm)：" prop="positionZ">
            <el-input
              v-model="formData.positionZ"
              :disabled="mode === 'view'"
              @change="clearPositionZ"
            >
              <template #append>
                <span>mm</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="图片中坐标：" prop="imageCoordinate">
            <ltw-input
              v-model="formData.imageCoordinate"
              :disabled="mode === 'view'"
            />
          </el-form-item>

          <el-form-item label="距地高度 (mm)：" prop="height">
            <el-input
              v-model="formData.height"
              :disabled="mode === 'view'"
              @change="clearHeight"
            >
              <template #append>
                <span>mm</span>
              </template>
            </el-input>
          </el-form-item>
        </div>

        <!-- 软件参数 -->
        <div class="form-section">
          <h3 class="section-title">软件参数</h3>

          <!-- Camera特有字段 -->
          <el-form-item
            label="相邻两行曝光间隔："
            prop="exposureInterval"
            v-if="formData.sensorType === 'camera'"
          >
            <el-input
              v-model="formData.exposureInterval"
              :disabled="mode === 'view'"
            />
          </el-form-item>

          <el-form-item
            label="畸变模型："
            prop="distortionModel"
            v-if="formData.sensorType === 'camera'"
          >
           <dictionary-selection
                v-model="formData.distortionModel"
                clearable
                dictionaryType="dewarping"
                 :disabled="mode === 'view'"
            />
          
          </el-form-item>

          <!-- Lidar特有字段 -->
          <el-form-item label="msop：" prop="msop" v-if="formData.sensorType === 'lidar'">
            <el-input
              v-model="formData.msop"
              :disabled="mode === 'view'"
              @change="clearMsop"
            />
          </el-form-item>

          <el-form-item label="difop：" prop="difop" v-if="formData.sensorType === 'lidar'">
            <el-input
              v-model="formData.difop"
              :disabled="mode === 'view'"
              @change="clearDifop"
            />
          </el-form-item>

          <!-- 通用字段 -->
          <el-form-item label="帧同步偏移值：" prop="pcdJpgOffset">
            <el-input
              v-model="formData.pcdJpgOffset"
              :disabled="mode === 'view'"
            >
              <template #append>
                <span>ms</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="间隔差的基准值：" prop="intervalDif">
            <el-input
              v-model="formData.intervalDif"
              :disabled="mode === 'view'"
            >
              <template #append>
                <span>ms</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="帧同步差前偏移值：" prop="syncFrameOffsetForward">
            <el-input
              v-model="formData.syncFrameOffsetForward"
              :disabled="mode === 'view'"
            >
              <template #append>
                <span>ms</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="帧同步差后偏移值：" prop="syncFrameOffsetBack">
            <el-input
              v-model="formData.syncFrameOffsetBack"
              :disabled="mode === 'view'"
            >
              <template #append>
                <span>ms</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item
            label="雷达时间戳定义角度："
            prop="lidarTimestampAngle"
            v-if="formData.sensorType === 'lidar'"
          >
            <el-input
              v-model="formData.lidarTimestampAngle"
              :disabled="mode === 'view'"
            >
              <template #append>
                <span>°</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="备注：" prop="remark">
            <el-input
              v-model="formData.remark"
              :disabled="mode === 'view'"
              textType="remark"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 抽屉底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>

  </el-drawer>
</template>

<script>
import { listFtmVehicleModality } from '@/apis/fleet/ftm-vehicle-modality'
import { listFtmSensor } from '@/apis/fleet/ftm-sensor'
import { showToast } from '@/plugins/util'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection.vue'
import CommonSelection from '@/components/system/CommonSelection.vue'
import {
  saveFtmVariantVersionMappingModality,
  updateFtmVariantVersionMappingModality,
  getFtmVariantVersionMappingModality
} from '@/apis/fleet/ftm-variant-version-mapping-modality'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import util, { createNumberValidationRules } from '@/plugins/util'

export default {
  name: 'SensorConfigDrawer',
  components: {
    DictionaryTypeSelection,
    CommonSelection,
    DictionarySelection
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add', // add, edit, view
      validator: value => ['add', 'edit', 'view'].includes(value)
    },
    sensorData: {
      type: Object,
      default: () => ({})
    },
    variantVersionId:{
      type:String
    }
  },
  emits: ['update:visible', 'confirm'],
  data() {
    return {
      formData: {
        sensorType: '',
        sensorTypeName: '',
        modality: '',
        modalityName: '',
        sensorId: '',
        sensorModel: '',
        sensorModelSpecification: '',
        cameraVO: {},
        positionX: '',
        positionY: '',
        positionZ: '',
        imageCoordinate: '',
        height: '',
        // Camera特有字段
        exposureInterval: '',
        distortionModel: '',
        // Lidar特有字段
        msop: '',
        difop: '',
        lidarTimestampAngle: '',
        // 通用字段
        pcdJpgOffset: '',
        intervalDif: '',
        syncFrameOffsetForward: '',
        syncFrameOffsetBack: '',
        remark: ''
      },
      formRules: {
        sensorType: [
          { required: true, message: '请选择传感器类型', trigger: 'change' }
        ],
        modality: [
          { required: true, message: '请选择传感器', trigger: 'change' }
        ],
        sensorModelSpecification:[
          { required: true, message: '请选择传感器实体', trigger: 'change' }
        ],
        msop:createNumberValidationRules(5,0,false),
        difop:createNumberValidationRules(5,0,false),
        positionX:createNumberValidationRules(9,2,false),
        positionY:createNumberValidationRules(9,2,false),
        positionZ:createNumberValidationRules(9,2,false),
        height:createNumberValidationRules(9,2,false),
        exposureInterval:createNumberValidationRules(19,6,false),
        pcdJpgOffset:createNumberValidationRules(8,4,false),
        intervalDif:createNumberValidationRules(5,2,false),
        syncFrameOffsetForward:createNumberValidationRules(6,2,false),
        syncFrameOffsetBack:createNumberValidationRules(6,2,false),
        lidarTimestampAngle:createNumberValidationRules(6,2,false)
      },
      modalityData: [],
      sensorEntityList: []
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    drawerTitle() {
      const titles = {
        add: '新增传感器',
        edit: '编辑传感器',
        view: '查看传感器'
      }
      return titles[this.mode] || '传感器配置'
    }
  },
  mounted() {
    // 组件挂载后立即初始化，因为使用v-if时visible已经是true
    if (this.visible) {
      this.initFormData()
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.mode === 'add') {
        this.resetForm()
      } else {
        // 编辑或查看模式，填充现有数据
        this.getFtmVariantVersionMappingModality(this.sensorData)
      }
    },
    getFtmVariantVersionMappingModality(row) {
      getFtmVariantVersionMappingModality(row.id).then(res => {
        res.data.sensorModelSpecification =
            res.data.sensorTypeName +
            (res.data.model ? '-' + res.data.model : '') +
            (res.data.specification ? '-' + res.data.specification : '')
        this.formData = res.data
                // 加载传感器编码列表
        if (this.formData.sensorType) {
          this.listFtmVehicleModality()
        }
      })
    },

    // 重置表单
    resetForm() {
      // 重置数据
      this.formData = {
        sensorType: '',
        sensorTypeName: '',
        modality: '',
        modalityName: '',
        sensorId: '',
        sensorModel: '',
        sensorModelSpecification: '',
        cameraVO: {},
        positionX: '',
        positionY: '',
        positionZ: '',
        imageCoordinate: '',
        height: '',
        // Camera特有字段
        exposureInterval: '',
        distortionModel: '',
        // Lidar特有字段
        msop: '',
        difop: '',
        lidarTimestampAngle: '',
        // 通用字段
        pcdJpgOffset: '',
        intervalDif: '',
        syncFrameOffsetForward: '',
        syncFrameOffsetBack: '',
        remark: ''
      }
      this.modalityData = []
      this.sensorEntityList = []
    },

    // 处理传感器类型变化
    changeSensorType(row) {
      if (row) {
        this.formData.sensorTypeName = row.name
      }
      // 清空关联表单
      this.formData.modality = ''
      this.formData.sensorModelSpecification = ''
      this.formData.sensorId = ''
      this.formData.cameraVO = {}

      // 清空传感器类型特有字段
      this.formData.exposureInterval = ''
      this.formData.distortionModel = ''
      this.formData.msop = ''
      this.formData.difop = ''
      this.formData.lidarTimestampAngle = ''

      this.modalityData = []
      this.sensorEntityList = []

      this.listFtmVehicleModality()
    },

    // 处理传感器编码变化
    changeModality(row) {
      this.formData.modalityName = row.name
    },

    // 获取传感器编码列表
    listFtmVehicleModality() {
      if (this.formData.sensorType) {
        listFtmVehicleModality({ sensorType: this.formData.sensorType }).then(res => {
          this.modalityData = res.data || []
        })
      }
    },

    // 处理传感器实体选择变化
    handleSensorEntityChange(sensorId) {

      const selectedSensor = this.sensorEntityList.find(sensor => sensor.id === sensorId)
      if (selectedSensor) {
        this.formData.sensorModelSpecification =
          selectedSensor.sensorTypeName + (selectedSensor.model ? '-' + selectedSensor.model : '') + (selectedSensor.specification ? '-' + selectedSensor.specification : '')
        this.formData.cameraVO = selectedSensor
        this.formData.sensorId = selectedSensor.id

      }
    },

    // 处理传感器下拉框显示状态变化
    handleSensorDropdownVisible(visible) {
      if (visible) {
        if (!this.formData.sensorType) {
          showToast('请先选择传感器类型', 'warning')
          return false
        }
        this.loadSensorEntityList()
      }
    },

    // 加载传感器实体列表
    loadSensorEntityList() {
      if (this.formData.sensorType) {
        const postData = {
          sensorType: this.formData.sensorType,
        }

        listFtmSensor(postData).then(res => {
          if (res.data && res.data.length) {
            this.sensorEntityList = res.data
          } else {
            this.sensorEntityList = []
          }
        }).catch(error => {
          console.error('获取传感器实体列表失败:', error)
          // showToast('获取传感器实体列表失败', 'error')
          this.sensorEntityList = []
        })
      } else {
        this.sensorEntityList = []
      }
    },

    // 清空位置X
    clearPositionX(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.positionX = 0
        })
      }
    },

    // 清空位置Y
    clearPositionY(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.positionY = 0
        })
      }
    },

    // 清空位置Z
    clearPositionZ(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.positionZ = 0
        })
      }
    },

    // 清空高度
    clearHeight(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.height = 0
        })
      }
    },

    // 清空difop
    clearDifop(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.difop = 0
        })
      }
    },

    // 清空msop
    clearMsop(data) {
      if (!data) {
        setTimeout(() => {
          this.formData.msop = 0
        })
      }
    },

    // 处理关闭抽屉
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 判断文本是否溢出
    isTextOverflow(text, maxWidth) {
      if (!text) return false
      // 考虑中英文字符宽度差异
      let textWidth = 0
      for (let i = 0; i < text.length; i++) {
        const char = text.charAt(i)
        // 中文字符宽度约为英文字符的2倍
        if (/[\u4e00-\u9fa5]/.test(char)) {
          textWidth += 14 // 中文字符宽度
        } else {
          textWidth += 8 // 英文字符宽度
        }
      }
      // 减去padding和一些余量
      return textWidth > (maxWidth - 24)
    },

    // 处理保存
    async handleSave() {
      try {
        const valid = await this.$refs.sensorForm.validate()
        if (!valid) return

        const sensorConfig = {
          ...this.formData,
          variantVersionId:this.variantVersionId
        }
        
        if(this.mode === 'edit'){
          sensorConfig.id = this.sensorData.id
        }
 
        this.$emit('confirm', {
          mode: this.mode,
          data: sensorConfig
        })

        this.handleClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sensor-config-form {
  padding: 20px;

  .form-section {
    margin-bottom: 32px;

    .section-title {
      color: #303133;
      font-size: 16px;
      font-family: 'Bosch Sans', sans-serif;
      font-weight: 700;
      line-height: 16px;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 1px solid #EBEEF5;
    }

    :deep(.el-form-item) {
      margin-bottom: 20px;

      .el-form-item__label {
        color: #4E5256;
        font-size: 12px;
        font-family: 'Bosch Office Sans', sans-serif;
        font-weight: 400;
        line-height: 23px;
      }

      .el-input__wrapper {
        border-radius: 2px;
        border: 1px solid #DCDFE6;

        &:hover {
          border-color: #C0C4CC;
        }

        &.is-focus {
          border-color: #5755FF;
          box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
        }
      }

      .el-select {
        width: 100%;

        .el-input__wrapper {
          border-radius: 2px;
        }
      }

      .el-textarea__inner {
        border-radius: 2px;
        border: 1px solid #DCDFE6;
        font-family: inherit;

        &:hover {
          border-color: #C0C4CC;
        }

        &:focus {
          border-color: #5755FF;
          box-shadow: 0 0 0 2px rgba(87, 85, 255, 0.2);
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #EBEEF5;
  background: #FAFAFA;

  .el-button {
    border-radius: 2px;
    font-size: 12px;
    font-family: 'Bosch Sans Global', sans-serif;
    font-weight: 400;
    padding: 6px 16px;

    &.el-button--primary {
      background: #5755FF;
      border-color: #5755FF;

      &:hover {
        background: #4644DD;
        border-color: #4644DD;
      }
    }
  }
}

.sensor-model-form {
  margin-bottom: 20px;

  .el-form-item {
    margin-bottom: 0;
  }
}

// 传感器选择下拉框表格样式
.sensor-table-header,
.sensor-table-option {
  width: 100%;

  .table-row {
    display: flex;
    width: 100%;

    &.header-row {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      font-weight: 600;
      color: #606266;

      .table-cell {
        padding: 8px 12px;
        font-size: 12px;
      }
    }

    &.data-row {
      border-bottom: 1px solid #f0f0f0;

      &:hover {
        background-color: #f5f7fa;
      }

      .table-cell {
        padding: 10px 12px;
        font-size: 13px;
        color: #303133;
      }
    }
      &.data-row.selected-row {
          background-color: #f0efff !important;

          &:hover {
            background-color: #e8e6ff !important;
          }
        }

    .table-cell {
      flex: 1;
      text-align: left;
      border-right: 1px solid #ebeef5;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: relative;

      &:last-child {
        border-right: none;
      }

      &.supplier {
        flex: 1.2;
        min-width: 80px;
      }

      &.model {
        flex: 1;
        min-width: 100px;
      }

      &.specification {
        flex: 1.3;
        min-width: 120px;
      }
    }
  }
}

// 下拉框弹出层样式
:deep(.sensor-select-dropdown) {
  // 移除固定向下展示，让下拉框自动选择最佳位置

  .el-select-dropdown__item {
    padding: 0 !important;
    height: auto !important;
    line-height: normal !important;

    &:hover {
      background-color: transparent !important;
    }

    // Element UI 自动添加的选中状态，添加紫色边框效果
    &.selected {
      border: 2px solid #5755FF !important;
      background-color: #f8f7ff !important;

      .sensor-table-option {
        .data-row {
          background-color: #f0efff !important;
          border-left: 4px solid #5755FF;

          &:hover {
            background-color: #e8e6ff !important;
          }
        }
      }
    }



    // 表头样式
    &.is-disabled {
      cursor: default !important;
      color: inherit !important;

      &:hover {
        background-color: #f5f7fa !important;
      }

      .sensor-table-header {
        pointer-events: none;
      }
    }

    // 专门针对表头的样式，排除选中效果
    &.table-header-option {
      background-color: transparent !important;
      border: none !important;

      &:hover {
        background-color: transparent !important;
      }

      &.selected {
        background-color: transparent !important;
        border: none !important;
      }

      &.hover {
        background-color: transparent !important;
      }
    }
  }
}

// 额外的选中状态样式，确保显示
:deep(.sensor-select-dropdown .el-select-dropdown__item.selected) {
  border: 2px solid #5755FF !important;
  background-color: #f8f7ff !important;

  .sensor-table-option .data-row {
    background-color: #f0efff !important;
    border-left: 4px solid #5755FF !important;
  }
}

:deep(.el-drawer) {
  .el-drawer__header {
    padding: 20px 20px 0 20px;
    margin-bottom: 0;

    .el-drawer__title {
      color: #303133;
      font-size: 18px;
      font-family: 'Bosch Sans', sans-serif;
      font-weight: 700;
      line-height: 18px;
    }
  }

  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .el-drawer__footer {
    margin-top: auto;
  }
}
</style>
