import {httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const getApplyLevels = (params = {}, fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_login_users/tree/current_user/access_role_type', params, fullLoading})
// export const getApplyLevels = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/tree/current_user/apply_levels', params})
export const getApiPrivileges = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/page/approver/current_user', params})
export const getApplications = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/current_user', params})
export const saveApiPrivileges = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges', data, params})
export const getApiPrivilegeDetail = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/' + id})
export const passApiPrivileges = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/' + id +'/pass'})
export const rejectApiPrivileges = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_api_privileges/' + id +'/reject'})
