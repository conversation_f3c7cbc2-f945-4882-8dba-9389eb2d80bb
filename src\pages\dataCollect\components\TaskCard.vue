<template>
  <el-card class="box-card" :class="getStatusType(form.status).type" id="task-form">
    <div class="card-header">
      <div class="task-type">
        <!--          <div class="name">{{ form.acquisitionTypeName }}</div>-->
        <ltw-icon v-if="form.acquisitionType === 'parking'" icon-code="svg-task-parking" />
        <ltw-icon v-else-if="form.acquisitionType === 'driving'" icon-code="svg-task-driving" />
        <ltw-icon v-else icon-code="svg-task-driving-and-parking" />
      </div>
      <!--        <el-tag class="vin">{{ form.vin }}</el-tag>-->
      <div class="title">
        <div class="name">
          <el-tooltip effect="dark" :content="form.name">{{ form.name }}</el-tooltip>
        </div>
        <div class="acquisition-type-name">{{ form.acquisitionTypeName }}</div>
      </div>
      <el-tag class="status" :type="getStatusType(form.status).type">{{ getStatusType(form.status).name }}</el-tag>
    </div>
    <div class="card-content">
      <div class="task-content">
        <div class="task-item">
          <div class="item-label">
            <ltw-icon icon-code="el-icon-credit-card" />
            车辆
          </div>
          <div class="item-value">{{ form.vin }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">
            <ltw-icon icon-code="el-icon-notification" />
            编号
          </div>
          <div class="item-value">{{ form.code }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">
            <ltw-icon icon-code="el-icon-user" />
            负责人
          </div>
          <div class="item-value">{{ form.recipientEmpName }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">
            <ltw-icon icon-code="el-icon-calendar" />
            期望日期
          </div>
          <div class="item-value">
            <span>
              <el-tag>{{ form.expectedStartTime }}</el-tag>
              &nbsp; ~ &nbsp;
              <el-tag>{{ form.expectedEndTime }}</el-tag>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <el-dropdown placement="top">
        <el-button class="button" text>
          <ltw-icon icon-code="el-icon-more"></ltw-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-if="form.status === 'draft' && (empId === form.recipientEmpId || isAdmin)">
              <el-link
                  class="dropdown-link"
                  type="success"
                  @click="publishTaskDetail(form.id)"
                  :underline="false"
                  id="release"
              >
                {{ $t('发布') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item v-if="form.status !== 'finished' && (empId === form.recipientEmpId || isAdmin)">
              <el-link
                  class="dropdown-link"
                  type="warning"
                  @click="editDaqReqDetail(form.id)"
                  :underline="false"
                  id="edit"
              >
                {{ $t('编辑') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item v-if="form.status === 'draft' && (empId === form.recipientEmpId || isAdmin)">
              <el-link
                  class="dropdown-link"
                  type="danger"
                  @click="deleteDaqReqDetail(form.id)"
                  :underline="false"
                  id="delete"
              >
                {{ $t('删除') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item v-if="form.status === 'published' && (empId === form.recipientEmpId || isAdmin)">
              <el-link class="dropdown-link" type="warning" @click="start(form.id)" :underline="false" id="start">
                {{ $t('开始') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item v-if="form.status === 'executing' && (empId === form.recipientEmpId || isAdmin)">
              <el-link class="dropdown-link" type="warning" @click="finish(form.id)" :underline="false" id="finish">
                {{ $t('完成') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-link
                  class="dropdown-link"
                  type="primary"
                  @click="cloneDaqTask(form.id)"
                  :underline="false"
                  id="detail"
              >
                {{ $t('克隆') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-link
                  class="dropdown-link"
                  type="primary"
                  @click="getTaskDetail(form.id)"
                  :underline="false"
                  id="detail"
              >
                {{ $t('详情') }}
              </el-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-link class="dropdown-link" type="primary" @click="getTaskRecord(form)" :underline="false" id="tag"
              >{{ $t('采集记录') }}
              </el-link>
            </el-dropdown-item>
          </el-dropdown-menu>
          <!--          <el-droppdown-menu>-->
          <!--            <el-link @click="getTaskRecord(form)" type="primary" :underline="false" id="tag"> 采集记录 </el-link>-->
          <!--          </el-droppdown-menu>-->
        </template>
      </el-dropdown>
    </div>
  </el-card>
  
  <!-- 信息弹窗 -->
  <InfoModal
    ref="infoModal"
    :title="infoModalData.title"
    :info-list="infoModalData.infoList"
    :show-cancel-button="true"
    :show-confirm-button="false"
    cancel-button-text="关闭"
  />
</template>

<script>
// task20220928141
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { dateUtils, showToast, showConfirmToast } from '@/plugins/util'
import $store from '@/store'
import {
  publishTaskDetail,
  deleteDaqReqDetail,
  startTaskDetail,
  finishTaskDetail,
  cloneDaqTask
} from '@/apis/data-collect/vt-daq-task'
// import CollectRecordDialog from '@/pages/dataCollect/dialog/CollectRecordDialog.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import InfoModal from '@/components/base/InfoModal.vue'

export default {
  name: 'TaskForm',
  emits: ['reload', 'handleFormChecked', 'showTagList', 'opt-add-task', 'show-req-detail', 'show-task-record'],
  data() {
    return {
      // 配置标签
      tagsData: [],
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      tagsTitle: '',
      empId: $store.state.permission.currentUser.empId,
      parseTime: dateUtils.parseTime,
      // 信息弹窗
      infoModalVisible: false,
      infoModalData: {
        title: '',
        infoList: []
      }
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    },
    batchingFunctionList: {
      type: Array,
      default: []
    },
    outlineFunctionList: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    selectable: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    form() {
      return this.item
    }
  },
  components: {
    LtwIcon,
    InfoModal
  },

  methods: {
    tagClose() {
      this.tagDialogVisible = false
    },
    getTags(row, type) {
      this.$emit('showTagList', {
        type: 'view',
        data: {
          taskType: type,
          groupList: type === 'req' ? row.requirementList : row.classificationList
        }
      })
    },
    getTaskRecord(row) {
      let postData = {
        type: 'edit',
        // type: editTags ? 'edit' : 'view',
        data: {
          ...row,
          taskCode: row.code,
          tagRecordCount: row?.tagList?.length
        }
      }
      this.$emit('show-task-record', postData)
    },
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    },
    cancel(val) {
      this.$emit('reload', val)
    },
    getTaskDetail(id) {
      this.$emit('opt-add-task', { type: 'view', id })
    },
    editDaqReqDetail(id) {
      this.$emit('opt-add-task', { type: 'edit', id })
      // this.$refs.AddTask.show({ type: 'edit', id })
    },
    publishTaskDetail(id) {
      publishTaskDetail(id).then(res => {
        this.handleTaskResponse(res, '发布成功')
      })
    },
    deleteDaqReqDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail({ id }).then(() => {
          showToast('删除成功')
          this.cancel()
        })
      })
      // })
    },
    start(id) {
      startTaskDetail(id).then(res => {
        showToast('已开始')
        this.cancel()
      })
    },
    finish(id) {
      finishTaskDetail(id).then(res => {
        this.handleTaskResponse(res, '已完成')
      })
    },
    cloneDaqTask(id) {

      cloneDaqTask(id).then(res => {
        showToast('已克隆')
        this.cancel()
      })
    },
    getTagsLength(list) {
      return list
          ?.map(val => val.children)
          ?.flat(Infinity)
          ?.map(val => val.tagList)
          ?.flat(Infinity)?.length
    },
    // 显示信息弹窗
    showInfoModal(data) {
      this.infoModalData = data
      this.$nextTick(() => {
        this.$refs.infoModal.show()
      })
    },
    // 处理任务操作响应
    handleTaskResponse(res, successMessage) {
      const data = res.data || {}
      if (data.success) {
        showToast(successMessage)
        this.cancel()
      } else {
        const info = data.unfinishedRecordInfo || {}
        this.showInfoModal({
          title: '请先结束当前采集任务',
          infoList: [
            { label: '区域', value: info.cantonName || '' },
            { label: '编码', value: info.taskCode || '' },
            { label: '操作人', value: info.complianceOfficerEmpName || '' },
            { label: '时间', value: info.startTime || '' }
          ]
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item) {
  padding: 0;
}

:deep(.dropdown-link) {
  height: 100%;
  width: 100%;
  padding: 5px 16px;
}

.el-card {
  //height: 256px;
  position: relative;

  :deep(.el-card__body) {
    padding: 10px var(--el-card-padding) var(--el-card-padding) var(--el-card-padding);
  }

  &.box-card {
    border-left: 3px solid;
  }

  &.primary {
    border-left-color: #409eff;
  }

  &.info {
    border-left-color: #909399;
  }

  &.warning {
    border-left-color: #e6a23c;
  }

  &.danger {
    border-left-color: #f56c6c;
  }

  &.success {
    border-left-color: #67c23a;
  }

  //margin-bottom: 15px;

  //.el-descriptions,
  //:deep(.el-descriptions__body),
  //tbody {
  //  width: 100%;
  //
  //  .el-descriptions__table.is-bordered .el-descriptions__cell {
  //    font-size: 12px;
  //    font-weight: 400;
  //    padding: 2px 11px;
  //    white-space: nowrap;
  //    overflow: hidden;
  //    text-overflow: ellipsis;
  //
  //    &.el-descriptions__label {
  //      font-weight: 600;
  //      white-space: nowrap;
  //    }
  //  }
  //
  //  .el-descriptions__table {
  //    table-layout: fixed;
  //  }
  //
  //  // :deep(.el-descriptions__table) {
  //  //   :deep(.el-descriptions__cell) {
  //  //     font-size: 12px;
  //  //     font-weight: 400;
  //  //     padding: 0 11px;
  //  //     // white-space: nowrap;
  //  //     // overflow: hidden;
  //  //     // text-overflow: ellipsis;
  //  //     // word-break: break-all;
  //  //   }
  //  // :deep(.el-descriptions__label) {
  //  //   font-weight: 600;
  //  // }
  //  // }
  //}

  .card-header {
    display: flex;
    padding: 0 5px 10px 0;
    margin-bottom: 10px;
    position: relative;
    border-bottom: 1px solid #dcdfe6;

    .task-type {
      width: 65px;
      height: 65px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      //padding-right: 20px;
      background: rgba(238, 246, 255, 0.8);
      border-radius: 10px;

      .ltw-icon {
        font-size: 50px;
      }
    }

    .title {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      color: #404245;
      font-weight: 600;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      //margin: 0 10px;
      padding: 4px 10px;
      width: calc(100% - 65px - 50px);

      .name {
        font-size: 15px;
        color: #000000;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }

      .acquisition-type-name {
        font-size: 13px;
        color: #8a9097;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }

    .status {
      position: absolute;
      top: 0;
      right: -10px;
    }

    .vin {
      font-size: 12px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      border-color: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;
      margin-right: 10px;
    }

    .el-link {
      white-space: nowrap;
    }
  }

  .card-footer {
    //position: relative;
    //bottom: -6px;
    position: absolute;
    right: 0;
    bottom: 4px;
    text-align: right;

    .el-link:not(:last-child) {
      margin-right: 8px;
    }
  }

  .card-content {
    display: flex;

    .task-content {
      width: 100%;

      .task-item {
        display: flex;
        font-size: 12px;
        line-height: 24px;
        overflow: hidden;

        .item-label {
          display: flex;
          align-items: center;
          white-space: nowrap;
          width: 80px;
          //font-weight: 600;
          color: #8A9097;
          .ltw-icon{
            margin-right: 4px;
          }
        }

        .item-value {
          white-space: nowrap;
          color: #8A9097;
        }
      }
    }
  }
}
</style>
