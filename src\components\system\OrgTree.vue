<template>
    <div class="tree-container">
        <el-tree
            class="org-tree"
            node-key="id"
            :default-expanded-keys="expandedOrgs"
            :expand-on-click-node="false"
            :highlight-current="true"
            :data="orgs"
            :props="orgTreeProps"
            @node-click="handleNodeClick"
            ref="orgTreeRef"
        >
            <template #default="{ node }">
                <div class="custom-tree-node">{{ node.label }}</div>
            </template>
        </el-tree>
    </div>
</template>

<script>
import { treeListSysRoleOrg } from '@/apis/system/sys-role-org'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
export default {
    name: "OrgTree",
    props: {
        defaultSelectedOrg: [String, Number]
    },
    data() {
        return {
            expandedOrgs: [],
            orgs: [],
            orgTreeProps: {
                children: 'children',
                label: 'name'
            },
            currentUser: {}

        }
    },
    created() {
        this.currentUser = this.$store.state.permission.currentUser

    },
    mounted() {
        this.getOrgs()
    },
    emits: ['orgClick'],
    methods: {
        getOrgs() {
            treeListSysRoleOrg().then(
                res => {
                    this.orgs = res.data
                    if(!this.currentUser.currentTenantId){
                        this.orgs.push({name:'无组织',id:BASE_CONSTANT.NO_ORG,asLeaf:true})
                    }
                    this.$nextTick(function () {
                        if (this.orgs && this.orgs.length > 0) {
                            this.expandedOrgs = [this.orgs[0].id]
                            if (this.defaultSelectedOrg) {
                                this.$refs.orgTreeRef.setCurrentKey(this.defaultSelectedOrg, true)
                                let node = this.$refs.orgTreeRef.getNode(this.defaultSelectedOrg)
                                this.handleNodeClick(node.data)
                            } else {
                                this.$refs.orgTreeRef.setCurrentKey(this.orgs[0].id, true)
                                this.handleNodeClick(this.orgs[0])
                            }
                        }

                    })
                }
            )
        },
        handleNodeClick(org) {
            this.$emit('orgClick', org)
        }
    }
}
</script>

<style lang="scss">
.tree-container {
    .org-tree {
        padding: 10px;

        :deep .el-tree-node__content {
            height: (auto);
            margin-top: 5px;

            .custom-tree-node {
                white-space: pre-wrap;
                width: 100%;
                overflow: hidden;
                word-wrap: break-word;
                word-break: break-all;
                font-size: 14px;
            }
        }
    }

    .el-tree--highlight-current {
        .el-tree-node.is-current > .el-tree-node__content {
            background-color: #99c8ff;
        }
    }
}
</style>
