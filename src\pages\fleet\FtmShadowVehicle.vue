<template>
    <div  class="ltw-page-container">
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container">
                    <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                            <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </el-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button :type="item.buttonStyleType" :key="item.id" v-for="item in outlineFunctionList" @click="executeButtonMethod(item)">
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                        {{$t(item.name)}}
                    </el-button>
                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            {{$t('批量操作')}}
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList"  :command="item.buttonCode">
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                    {{$t(item.name)}}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records" stripe border @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
                                <el-table-column header-align="center" align="center" prop="vin" :label="$t('车架号')"></el-table-column>
<!--                <el-table-column header-align="center" align="center" :label="$t('操作')"  min-width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="$t(item.name)" placement="top" :enterable="false"
                            >
                                <el-button :type="item.buttonStyleType" size="mini"
                                           @click="executeButtonMethod(item,scope.row)">
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                            </el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>-->
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item :label="$t('vin')" prop="vin">
                    <el-input v-model="formData.vin" :disabled="formReadonly"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <template v-if="dialogStatus === 'view'">
                        <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">{{$t('关 闭')}}</el-button>
                        <el-button
                            :type="item.buttonStyleType"
                            @click="executeButtonMethod(currentButton)"
                            v-if="currentButton && currentButton.name"
                        >{{ $t(currentButton.name) }}</el-button>
                    </template>
                    <template v-else>
                        <el-button @click="dialogVisible = false">{{$t('取 消')}}</el-button>
                        <el-button type="primary" @click="save">{{$t('保 存')}}</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveFtmShadowVehicle,
        updateFtmShadowVehicle,
        deleteFtmShadowVehicle,
        pageFtmShadowVehicle,
        getFtmShadowVehicle
    } from '@/apis/fleet/ftm-shadow-vehicle'
    import BASE_CONSTANT from '@/plugins/constants/base-constant'
    import { showToast, showConfirmToast } from '@/plugins/util'

    const defaultFormData = {}
    export default {
        name: "FtmShadowVehicle",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                dataOperatePermission:{},
                pageData: {
                    total: 0
                },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({},defaultFormData),
                formRules: {
                    vin: [
                        {required: true, message: this.$t('请输入vin'), trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData:[],
                currentButton: {}
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
                this.dataOperatePermission = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(button,row){
                this.currentButton = {}
                this[button.buttonCode](row, button)            },
            refresh(){
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageFtmShadowVehicle(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = this.$t('新增')
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveFtmShadowVehicle(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateFtmShadowVehicle(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                    }
                )
            },
            edit(row) {
                this.dialogTitle = this.$t('修改')
                this.dialogStatus = 'edit'
                getFtmShadowVehicle(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            view(row) {
                this.dialogTitle = this.$t('查看')
                this.dialogStatus = 'view'
                getFtmShadowVehicle(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            handleCommand(command){
                if(this.selectedData.length === 0){
                   return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
                }
                if(command === 'batchRemove'){
                    this.batchRemove()
                }
            },
            singleRemove(row) {
                this.remove({id:row.id})
            },
            batchRemove(){
                let idList = [];
                this.selectedData.forEach(ele=>{
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param){
                showConfirmToast({
                    message: BASE_CONSTANT.DELETE_CONFIRM_MSG
                }).then(res => {
                    deleteFtmShadowVehicle(param).then(
                        ()=>{
                            this.query()
                        }
                    )
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened(){

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value){
                this.selectedData = value
            },
            initForm(){
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({},defaultFormData)
            }

        }
    }
</script>

<style scoped lang="scss">
    .button-group{
        .el-button{
            margin-right: 10px;
        }
    }

</style>
