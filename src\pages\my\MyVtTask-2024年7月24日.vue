<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-tool-container">
          <dictionary-selection
            id="status"
            class="ltw-tool-container"
            v-model="queryParam.status"
            clearable
            dictionaryType="daq_task_status"
            @change="refresh"
          />
          <el-button id="filter" @click="filterTask" :type="checkFilterButtonType()">
            <ltw-icon icon-code="el-icon-filter"></ltw-icon>
            <span>{{ $t('筛选') }}</span>
          </el-button>
        </div>
        <div class="ltw-tool-container button-group">
          <template :key="item.id" v-for="item in outlineFunctionList">
            <el-button v-if="item.buttonCode === 'add'" :type="item.buttonStyleType" @click="executeButtonMethod(item)">
              <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
              {{ $t(item.name) }}
            </el-button>
          </template>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <template :key="item.id" v-for="item in batchingFunctionList">
                  <el-dropdown-item v-if="item.buttonCode !== 'acquisitionRecordBatchAdd'" :command="item.buttonCode">
                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                    {{ $t(item.name) }}
                  </el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-row v-for="rowIndex in Math.ceil(pageData?.records?.length / splitNum)" :key="rowIndex" :gutter="20">
        <el-col
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="8"
          v-for="colIndex in rowIndex === Math.ceil(pageData?.records?.length / splitNum)
            ? pageData?.records?.length % splitNum || splitNum
            : splitNum"
          :key="colIndex"
          style="margin-bottom: 20px"
        >
          <task-form
            @reload="query"
            @optAddTask="optAddTask"
            @showTagList="showTagList"
            @showTaskRecord="showTaskRecord"
            :key="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)].id"
            :item="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)]"
          />
        </el-col>
      </el-row>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[9, 18, 36, 72]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      ></el-pagination>
    </el-card>
    <filter-task @reload="reload" ref="FilterTask" />
    <add-task @reload="reload" @showTagList="showTagList" @showTagSamplesDialog="showTagSamplesDialog" ref="AddTask" />
    <tag-samples-dialog ref="TagSamplesDialog" @reload="confirmSamples" />
    <tag-list @tagSave="tagSave" ref="TagList" />
    <collect-record-dialog @reload="query" ref="CollectRecordDialog" />
  </div>
</template>

<script>
import {
  saveDaqReqDetail,
  updateDaqReqDetail,
  deleteDaqReqDetail,
  pageRecipientCurrentUser,
  getDaqReqDetail,
  acceptTaskDetail,
  finishTaskDetail
} from '@/apis/data-collect/vt-daq-task'
import FilterTask from '@/pages/dataCollect/dialog/FilterTask.vue'
import TaskForm from '@/pages/dataCollect/components/TaskForm.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast, debounce } from '@/plugins/util'
import { batchPublishTaskDetail } from '@/apis/data-collect/vt-daq-task'
import AddTask from '@/pages/dataCollect/dialog/AddTask.vue'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import TagSamplesDialog from '@/pages/dataCollect/dialog/TagSamplesDialog.vue'
import CollectRecordDialog from '@/pages/dataCollect/dialog/CollectRecordDialog.vue'

const defaultFormData = {}
export default {
  components: {
    TagList,
    DictionarySelection,
    FilterTask,
    TaskForm,
    AddTask,
    TagSamplesDialog,
    CollectRecordDialog
  },
  name: 'MyDaqReq',
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        records: [],
        total: 0
      },
      queryParam: {
        current: 1,
        size: 9
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},
      selectedFormData: [],
      splitNum: 3
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
    let _this = this
    window.onresize = debounce(_this.changeSize)
    this.changeSize()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.query()
    },
    query() {
      pageRecipientCurrentUser(this.queryParam).then(res => {
        res.data.records.forEach(val => {
          this.splitReqClass(val)
        })
        this.pageData = res.data
      })
    },
    splitReqClass(item) {
      item.tagList.filter((val, tagIndex) => {
        // 组装requirementList列表
        let index = item.requirementList.findIndex(req => req.id === val.classificationId)
        if (~index) {
          item.requirementList[index].tagList ??= []
          item.requirementList[index].tagList.push(val)
        } else {
          // 组装classificationList列表
          item.classificationList ??= []
          let classificationIndex = item.classificationList.findIndex(
            classification => classification.id === val.classificationId
          )
          if (~classificationIndex) {
            item.classificationList[classificationIndex].tagList.push(val)
          } else {
            item.classificationList.push({
              code: val.classificationCode,
              id: val.classificationId,
              name: val.classificationName,
              tagList: [val]
            })
          }
        }
      })
      item.requirementList = this.formatList(item.requirementList)
      item.classificationList = this.formatList(item.classificationList)
    },
    formatList(list) {
      list?.forEach(req => {
        req.children ??= []
        req.tagList?.forEach(tag => {
          let groupIndex = req.children.findIndex(child => child.id === tag.groupId)
          if (~groupIndex) {
            req.children[groupIndex].tagList.push(tag)
          } else {
            req.children.push({
              code: tag.groupCode,
              id: tag.groupId,
              name: tag.groupName,
              nameCn: tag.groupNameCn,
              tagList: [tag]
            })
          }
        })
      })
      return list
    },
    add() {
      this.$refs.AddTask.show({
        type: 'add'
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
      if (command === 'batchPublish') {
        this.batchPublish()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail(param).then(() => {
          this.query()
        })
      })
    },
    handleFormChecked(form, val) {
      if (val) {
        this.selectedData.push(form.id)
      } else {
        let index = this.selectedData.findIndex(item => item === form.id)
        this.selectedData.splice(index, 1)
      }
    },
    batchPublish() {
      batchPublishTaskDetail(this.selectedData).then(res => {
        showToast('发布成功')
        this.selectedData = []
        this.refresh()
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.formData = Object.assign({}, defaultFormData)
    },
    accept(row, button) {
      if (row) {
        this.currentButton = button
        this.view(row)
      } else {
        acceptTaskDetail(this.formData.id).then(() => {
          this.dialogVisible = false
          this.query()
        })
      }
    },
    finish(row, button) {
      if (row) {
        this.currentButton = button
        this.view(row)
      } else {
        finishTaskDetail(this.formData.id).then(() => {
          this.dialogVisible = false
          this.query()
        })
      }
    },
    checkFilterButtonType() {
      // if(Object.keys(this.filterField).length==0){
      //   return
      // }else if(this.filterField.vin!==[] || this.filterField.modality!==[] || this.filterField.measureStartTime !=='' || this.filterField.tags!==[] || this.filterField.measureEndTime!=='' ||this.filterField.name!==''){
      //   return 'primary'
      // }else{
      //   return
      // }
    },
    filterTask() {
      this.$refs.FilterTask.show({ type: 'myTask' })
    },
    reload(val) {
      if (val) {
        this.queryParam = {
          current: this.queryParam.current,
          size: this.queryParam.size,
          status: this.queryParam.status,
          ...JSON.parse(JSON.stringify(val))
        }
      }
      this.query()
    },
    changeSize() {
      if (window.innerWidth >= 1920) {
        this.splitNum = 3
      } else if (window.innerWidth >= 1200) {
        this.splitNum = 2
      } else if (window.innerWidth >= 992) {
        this.splitNum = 2
      } else if (window.innerWidth >= 768) {
        this.splitNum = 1
      } else {
        this.splitNum = 1
      }
    },
    tagSave(row) {
      if (row.data.groupList?.length > 0) {
        if (row.data.taskType === 'req') {
          let postData = { type: row.type === 'view' ? 'onlyView' : row.type, data: row.data }
          this.showTagSamplesDialog(postData)
        } else if (row.data.taskType === 'class') {
          this.$refs.AddTask.confirmClassificationTag(row.data)
        }
      }
    },
    showTagList(item) {
      this.$refs.TagList.show(item)
    },
    confirmSamples(row) {
      this.$refs.AddTask.confirmSamples(row)
    },
    showTagSamplesDialog(row) {
      this.$refs.TagSamplesDialog.show(row)
    },
    showTaskRecord(row) {
      row.data.btnPermission = {
        batchingFunctionList: this.batchingFunctionList,
        outlineFunctionList: this.outlineFunctionList,
        inlineFunctionList: this.inlineFunctionList
      }
      this.$refs.CollectRecordDialog.show(row)
    },
    optAddTask(row) {
      this.$refs.AddTask.show(row)
    }
  }
}
</script>
