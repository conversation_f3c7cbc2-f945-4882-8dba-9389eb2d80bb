<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" label-position="top" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('车架号')" prop="vin">
        <el-select
          :disabled="formReadonly"
          class="form-item"
          v-model="form.vin"
          filterable
          clearable
          placeholder="车架号"
        >
          <el-option v-for="item in bsVehicleList" :key="item.vin" :label="item.vin" :value="item.vin" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('行政区')" prop="cantonCode">
        <el-cascader
          :disabled="formReadonly"
          class="form-item"
          placeholder="行政区"
          filterable
          clearable
          popper-class="canton-list"
          v-model="form.cantonCode"
          :options="cantonCodeList"
          style="width: 100%"
          :props="props"
        />
      </el-form-item>
      <el-form-item :label="$t('行车类型')" prop="categoryCode">
        <el-cascader
          :disabled="formReadonly"
          class="form-item"
          placeholder="行车类型"
          filterable
          clearable
          popper-class="canton-list"
          v-model="form.categoryCode"
          :options="categoryCodeList"
          style="width: 100%"
          :props="props"
        />
      </el-form-item>
      <el-form-item :label="$t('日期')" prop="executingDate">
        <el-date-picker
          :disabled="formReadonly"
          style="width: 100%"
          class="form-item"
          v-model="form.executingDate"
          type="date"
          :placeholder="$t('日期')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('开始时间')" prop="startTime">
            <el-time-picker
              :disabled="formReadonly"
              v-model="form.startTime"
              :placeholder="$t('开始时间')"
              :disabled-hours="disableStartHours"
              :disabled-minutes="disableStartMinutes"
              :disabled-seconds="disableStartSeconds"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('结束时间')" prop="endTime">
            <el-time-picker
              :disabled="formReadonly"
              v-model="form.endTime"
              :placeholder="$t('结束时间')"
              :disabled-hours="disableEndHours"
              :disabled-minutes="disableEndMinutes"
              :disabled-seconds="disableEndSeconds"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item :label="$t('执行人')" prop="complianceOfficerEmpId">
        <employee-selection
          class="form-item"
          clearable
          :query-param="complianceEmployeeQueryParam"
          :disabled="formReadonly"
          v-model="form.complianceOfficerEmpId"
          @change="changeComplianceOfficer"
        ></employee-selection>
      </el-form-item>
      <el-form-item :label="$t('描述')" prop="remark">
        <ltw-input
          v-model="form.remark"
          :disabled="formReadonly"
          textType="remark"
          type="textarea"
          id="remark"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  getTaskDailyReport,
  saveTaskDailyReports,
  updateTaskDailyReports,
  getTreeDailyReportCategoryData
} from '@/apis/fleet/driving-record-management'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import { listBsVehicleSelection } from '@/apis/fleet/bs-vehicle'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import { dateUtils } from '../../../plugins/util'

const defaultform = {}
export default {
  name: 'AddDrivingRecord',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        vin: [{ required: true, message: this.$t('请选择车架号'), trigger: 'change' }],
        cantonCode: [{ required: true, message: this.$t('请选择行政区'), trigger: 'change' }],
        categoryCode: [{ required: true, message: this.$t('请选择行车类型'), trigger: 'change' }],
        executingDate: [{ required: true, message: this.$t('请选择行车类型'), trigger: 'change' }],
        startTime: [{ required: true, message: this.$t('请选择开始时间'), trigger: 'change' }],
        endTime: [{ required: true, message: this.$t('请选择结束时间'), trigger: 'change' }],
        complianceOfficerEmpId: [{ required: true, message: this.$t('请选择执行人'), trigger: 'change' }]
      },
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
      },
      cantonCodeList: [],
      categoryCodeList: [],
      bsVehicleList: [],
      complianceEmployeeQueryParam: {
        orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    EmployeeSelection
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增行车记录')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑行车记录')
          this.getTaskDailyReport(row)
          break
        case 'view':
          this.dialogTitle = this.$t('行车记录详情')
          this.getTaskDailyReport(row)
          break
      }
      this.getSysCantonTree()
      this.getTreeDailyReportCategoryData()
      this.listBsVehicleSelection()
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        if (postData.startTime) {
          postData.startTime = postData.executingDate + ' ' + dateUtils.parseTime(postData.startTime, '{h}:{i}:{s}')
        }
        if (postData.endTime) {
          postData.endTime = postData.executingDate + ' ' + dateUtils.parseTime(postData.endTime, '{h}:{i}:{s}')
        }
        if (Array.isArray(postData.cantonCode)) {
          postData.cantonCode = postData.cantonCode[postData.cantonCode?.length - 1]
        }
        if (Array.isArray(postData.categoryCode)) {
          postData.categoryCode = postData.categoryCode[postData.categoryCode?.length - 1]
        }

        if (this.dialogStatus === 'add') {
          saveTaskDailyReports(postData).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateTaskDailyReports(postData).then(() => {
            this.cancel()
          })
        }
      })
    },
    getTaskDailyReport(row) {
      getTaskDailyReport(row.id).then(res => {
        this.form = res.data
      })
    },
    getTreeDailyReportCategoryData() {
      if (!this.categoryCodeList?.length) {
        getTreeDailyReportCategoryData().then(res => {
          this.categoryCodeList = res.data
        })
      }
    },
    disableStartHours() {
      if (!this.form.endTime) return []
      const endHour = new Date(this.form.endTime).getHours()
      return Array.from({ length: 24 }, (_, i) => i).filter(i => i > endHour)
    },

    disableStartMinutes(hour) {
      if (!this.form.endTime) return []
      const end = new Date(this.form.endTime)
      if (hour !== end.getHours()) return []
      return Array.from({ length: 60 }, (_, i) => i).filter(i => i > end.getMinutes())
    },

    disableStartSeconds(hour, minute) {
      if (!this.form.endTime) return []
      const end = new Date(this.form.endTime)
      if (hour !== end.getHours() || minute !== end.getMinutes()) return []
      return Array.from({ length: 60 }, (_, i) => i).filter(i => i >= end.getSeconds())
    },

    disableEndHours() {
      if (!this.form.startTime) return []
      const startHour = new Date(this.form.startTime).getHours()
      return Array.from({ length: 24 }, (_, i) => i).filter(i => i < startHour)
    },

    disableEndMinutes(hour) {
      if (!this.form.startTime) return []
      const start = new Date(this.form.startTime)
      if (hour !== start.getHours()) return []
      return Array.from({ length: 60 }, (_, i) => i).filter(i => i < start.getMinutes())
    },

    disableEndSeconds(hour, minute) {
      if (!this.form.startTime) return []
      const start = new Date(this.form.startTime)
      if (hour !== start.getHours() || minute !== start.getMinutes()) return []
      return Array.from({ length: 60 }, (_, i) => i).filter(i => i <= start.getSeconds())
    },
    changeComplianceOfficer(val) {
      this.form.complianceOfficerEmpName = val.node.name
    },
    listBsVehicleSelection() {
      if (!this.bsVehicleList?.length) {
        listBsVehicleSelection().then(res => {
          this.bsVehicleList = res.data
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.form-item {
  width: 100%;
}
</style>
