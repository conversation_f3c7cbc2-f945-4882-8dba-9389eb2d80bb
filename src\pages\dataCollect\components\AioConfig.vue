<template>
  <div class="aio-config">
    <div class="catalog">
      <div class="active-line" :style="{ top: catalogIndex + 'px' }"></div>
      <a
        v-for="item in catalogList"
        :key="item.id"
        class="catalog-title"
        :class="{ active: activeId === item.id }"
        @click="scrollToSection(item.id)"
        >{{ item.name }}</a
      >
    </div>
    <div class="form">
      <!--      <el-scrollbar class="selected-container">-->
      <div class="scroll-form" id="scroll-form">
        <div id="vehicle-config" class="form-section vehicle-config">
          <div class="sub-title">出车配置</div>
          <div class="content">
            <el-form :model="form" :rules="formRules" ref="formAIORef" label-width="110px">
              <el-form-item :label="$t('平台')" prop="platform">
                <el-radio-group size="small" v-model="form.platform" :disabled="formReadonly">
                  <el-radio class="select-item" border v-for="item in platformList" :label="item.code" :key="item.id"
                    >{{ $t(item.name) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="$t('MTA')" prop="mta">
                <el-checkbox-group :disabled="formReadonly" size="small" v-model="form.mta">
                  <el-checkbox class="select-item" v-for="item in mtaList" :key="item.id" :label="item.code" border
                    >{{ $t(item.name) }}
                  </el-checkbox>
                </el-checkbox-group>
                <!--                <el-radio-group size="small" v-model="form.mta" :disabled="formReadonly">-->
                <!--                  <el-radio class="select-item" border v-for="item in mtaList" :label="item.code" :key="item.id"-->
                <!--                    >{{ $t(item.name) }}-->
                <!--                  </el-radio>-->
                <!--                </el-radio-group>-->
              </el-form-item>
              <el-form-item :label="$t('Shadow Config')" prop="shadowConfig">
                <el-checkbox-group size="small" v-model="form.shadowConfig" :disabled="formReadonly">
                  <el-checkbox
                    class="select-item"
                    v-for="item in shadowConfigList"
                    :key="item.id"
                    :label="item.code"
                    border
                    >{{ $t(item.name) }}
                  </el-checkbox>
                </el-checkbox-group>
                <!--                <el-radio-group v-model="form.shadowConfig" :disabled="formReadonly">-->
                <!--                  <el-radio-->
                <!--                    class="select-item"-->
                <!--                    border-->
                <!--                    v-for="item in shadowConfigList"-->
                <!--                    :label="item.code"-->
                <!--                    :key="item.id"-->
                <!--                    >{{ $t(item.name) }}-->
                <!--                  </el-radio>-->
                <!--                </el-radio-group>-->
              </el-form-item>
              <el-form-item :label="$t('录制场景')" prop="functionTypeList">
                <el-checkbox
                  :disabled="formReadonly"
                  class="select-item"
                  size="small"
                  border
                  v-model="checkAllFunctionTypeList"
                  :indeterminate="isIndeterminateFunctionTypeList"
                  @change="handleCheckAllChangeFunctionTypeList"
                >
                  {{ $t('全选') }}
                </el-checkbox>
                <el-checkbox-group
                  :disabled="formReadonly"
                  size="small"
                  v-model="form.functionTypeList"
                  @change="handleCheckedChangeFunctionTypeList"
                >
                  <el-checkbox
                    class="select-item"
                    v-for="item in functionTypeList"
                    :key="item.id"
                    :label="item.code"
                    :value="item.code"
                    border
                    >{{ $t(item.name) }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="$t('数据源转换')" prop="dataTypeList">
                <el-checkbox-group :disabled="formReadonly" size="small" v-model="form.dataTypeList">
                  <el-checkbox class="select-item" v-for="item in dataTypeList" :key="item.id" :label="item.code" border
                    >{{ $t(item.name) }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div id="software-update" class="form-section software-update">
          <div class="sub-title">软件更新</div>

          <div v-show="Object.keys(form?.softwarePackage || {})?.length">
            <el-card class="card-item" v-for="(item, key) in form.softwarePackage">
              <el-popconfirm
                v-if="!formReadonly"
                width="250"
                :title="DELETE_TEMPORARY_CONFIRM_MSG"
                @confirm="removeSoftware(key)"
              >
                <template #reference>
                  <el-link class="card-close" :underline="false" type="info" v-if="!formReadonly">
                    <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                  </el-link>
                </template>
              </el-popconfirm>
              <div class="function-bar">
                <el-tag>{{ key }}</el-tag>
                <el-button
                  v-if="!formReadonly"
                  size="small"
                  type="success"
                  class="title-btn"
                  @click="editSoftwarePackage(item, key)"
                >
                  重新选择
                </el-button>
              </div>
              <el-table :data="[item]" stripe row-key="id" ref="tableRef">
                <el-table-column
                  header-align="left"
                  prop="name"
                  :label="$t('名称')"
                  show-overflow-tooltip
                ></el-table-column>
                <!--              <el-table-column-->
                <!--                header-align="left"-->
                <!--                prop="path"-->
                <!--                :label="$t('地址')"-->
                <!--                show-overflow-tooltip-->
                <!--              ></el-table-column>-->
                <!--              <el-table-column-->
                <!--                header-align="left"-->
                <!--                prop="repo"-->
                <!--                :label="$t('仓库')"-->
                <!--                show-overflow-tooltip-->
                <!--              ></el-table-column>-->
                <el-table-column
                  width="90"
                  header-align="left"
                  prop="size"
                  :label="$t('文件大小')"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <el-tag>{{ checkFileSize(scope.row.size) }}</el-tag>
                  </template>
                </el-table-column>

                <!--                <el-table-column header-align="left" align="left" :label="$t('操作')" width="130" fixed="right">-->
                <!--                  <template #default="scope">-->
                <!--                    <el-button-group>-->
                <!--                      <el-tooltip effect="dark" :content="$t('预览')" placement="top" :enterable="false">-->
                <!--                        <el-button type="primary" @click="previewTextFile(scope.row)" size="small">-->
                <!--                          <ltw-icon icon-code="el-icon-view"></ltw-icon>-->
                <!--                        </el-button>-->
                <!--                      </el-tooltip>-->
                <!--                      <el-tooltip effect="dark" :content="$t('下载')" placement="top" :enterable="false">-->
                <!--                        <el-button plain type="primary" @click="downloadFile(scope.row)" size="small">-->
                <!--                          <ltw-icon icon-code="el-icon-download"></ltw-icon>-->
                <!--                        </el-button>-->
                <!--                      </el-tooltip>-->
                <!--                    </el-button-group>-->
                <!--                  </template>-->
                <!--                </el-table-column>-->
              </el-table>
            </el-card>
          </div>
          <el-button size="small" plain type="primary" @click="addSoftware" v-if="!formReadonly">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          </el-button>
          <div v-show="!Object.keys(form?.softwarePackage || {})?.length">
            <el-empty />
          </div>
        </div>
        <el-divider />
        <div id="white-list" class="form-section white-list">
          <div class="sub-title">白名单</div>
          <div v-show="Object.keys(form?.whiteList || {})?.length">
            <el-card class="card-item" v-for="(item, key) in form.whiteList">
              <el-popconfirm
                v-if="!formReadonly"
                width="250"
                :title="DELETE_TEMPORARY_CONFIRM_MSG"
                @confirm="removeWhiteList(key)"
              >
                <template #reference>
                  <el-link class="card-close" :underline="false" type="info" v-if="!formReadonly">
                    <ltw-icon icon-code="el-icon-circle-close"></ltw-icon>
                  </el-link>
                </template>
              </el-popconfirm>
              <div class="function-bar">
                <el-tag>{{ key }}</el-tag>
                <el-button
                  v-if="!formReadonly"
                  size="small"
                  type="success"
                  class="title-btn"
                  @click="editWhiteList(item, key)"
                  >重新选择
                </el-button>
              </div>

              <el-table :data="item" stripe row-key="id" ref="tableRef">
                <el-table-column
                  header-align="left"
                  prop="name"
                  :label="$t('名称')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  header-align="left"
                  prop="path"
                  :label="$t('地址')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  header-align="left"
                  prop="repo"
                  :label="$t('仓库')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  width="100"
                  header-align="left"
                  prop="size"
                  :label="$t('文件大小')"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <el-tag>{{ checkFileSize(scope.row.size) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  width="100"
                  header-align="left"
                  align="center"
                  prop="size"
                  :label="$t('默认白名单')"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <el-link type="primary" v-show="!scope.row.enabled" @click="setDefault(scope.row, key)"
                      >设为默认
                    </el-link>
                    <div v-show="scope.row.enabled">默认白名单</div>
                  </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('操作')" width="70" fixed="right">
                  <template #default="scope">
                    <el-button-group>
                      <!--                      <el-tooltip effect="dark" :content="$t('设为默认')" placement="top" :enterable="false">-->
                      <!--                        <el-button-->
                      <!--                          v-show="!scope.row.enabled"-->
                      <!--                          type="warning"-->
                      <!--                          @click="setDefault(scope.row, key)"-->
                      <!--                          size="small"-->
                      <!--                        >-->
                      <!--                          <ltw-icon icon-code="el-icon-star"></ltw-icon>-->
                      <!--                        </el-button>-->
                      <!--                      </el-tooltip>-->
                      <!--                      <el-tooltip effect="dark" :content="$t('预览')" placement="top" :enterable="false">-->
                      <!--                        <el-button type="primary" @click="previewTextFile(scope.row, key)" size="small">-->
                      <!--                          <ltw-icon icon-code="el-icon-view"></ltw-icon>-->
                      <!--                        </el-button>-->
                      <!--                      </el-tooltip>-->
                      <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                        <el-popconfirm
                          width="250"
                          :title="DELETE_TEMPORARY_CONFIRM_MSG"
                          @confirm="deleteWhiteList(scope.$index, key)"
                        >
                          <template #reference>
                            <el-button type="danger" size="small">
                              <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                            </el-button>
                          </template>
                        </el-popconfirm>
                      </el-tooltip>
                      <!--                      <el-tooltip effect="dark" :content="$t('下载')" placement="top" :enterable="false">-->
                      <!--                        <el-button plain type="primary" @click="downloadFile(scope.row)" size="small">-->
                      <!--                          <ltw-icon icon-code="el-icon-download"></ltw-icon>-->
                      <!--                        </el-button>-->
                      <!--                      </el-tooltip>-->
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
          <el-button size="small" plain type="primary" @click="addWhiteList" v-if="!formReadonly">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          </el-button>
          <div v-show="!Object.keys(form?.whiteList || {})?.length">
            <el-empty />
          </div>
        </div>
        <el-divider />
      </div>
    </div>
    <add-software-update
      ref="AddSoftwareUpdate"
      @reload="reloadSoftwareUpdate"
      @previewTextFile="previewTextFile"
      @downloadFile="downloadFile"
    />
    <add-white-list
      ref="AddWhiteList"
      @reload="reloadWhiteList"
      @previewTextFile="previewTextFile"
      @downloadFile="downloadFile"
    />
    <view-txt-dialog ref="ViewTxtDialog" />
  </div>
</template>
<script>
import {
  saveAioConfig,
  updateAioConfig,
  getAioConfigDetail,
  previewAioConfigDetail
} from '@/apis/data-collect/vt-daq-aio-config'
import LtwIcon from '@/components/base/LtwIcon.vue'
import AddSoftwareUpdate from '@/pages/dataCollect/dialog/AddSoftwareUpdate'
import AddWhiteList from '@/pages/dataCollect/dialog/AddWhiteList'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util, { checkFileSize, downloadTxt, showToast } from '@/plugins/util'
import ViewTxtDialog from '@/pages/dataCollect/dialog/ViewTxtDialog.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

const defaultform = { softwarePackage: {}, whiteList: {} }

export default {
  name: 'AioConfig',
  emits: ['reload'],
  props: {},
  components: { ViewTxtDialog, LtwIcon, AddSoftwareUpdate, AddWhiteList },
  data() {
    return {
      dialogStatus: '',
      activeId: '',
      form: Object.assign({}, defaultform),
      formRules: {
        platform: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        mta: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        shadowConfig: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        functionTypeList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        dataTypeList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ]
      },
      platformList: [],
      mtaList: [],
      shadowConfigList: [],
      functionTypeList: [],
      dataTypeList: [],
      catalogList: [
        {
          name: '出车配置',
          id: 'vehicle-config'
        },
        {
          name: '软件更新',
          id: 'software-update'
        },
        {
          name: '白名单',
          id: 'white-list'
        }
      ],
      DELETE_TEMPORARY_CONFIRM_MSG: BASE_CONSTANT.DELETE_TEMPORARY_CONFIRM_MSG,
      checkFileSize,
      taskId: '',
      checkAllFunctionTypeList: false,
      isIndeterminateFunctionTypeList: false
    }
  },
  created() {},
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    },
    catalogIndex() {
      const index = this.catalogList.findIndex(val => val.id === this.activeId)
      const top = (index < 0 ? 0 : index) * 30 + 8
      return top
    }
  },
  watch: {},
  mounted() {},
  unmounted() {},
  methods: {
    show(row) {
      // setTimeout(()=>{
      this.$nextTick(() => {
        this.bindScrollActive()
      })
      this.dialogStatus = row.type
      this.taskId = row.data.id
      this.getAioConfigDetail()
      this.getPlatformList()
      this.getMtaList()
      this.getShadowConfigList()
      this.getFunctionTypeList()
      this.getDataTypeList()
      // })
    },
    getAioConfigDetail() {
      getAioConfigDetail({ taskId: this.taskId }).then(res => {
        this.form = res.data || {}
      })
    },
    async getPlatformList() {
      if (!this.platformList?.length) {
        this.platformList = await listSysDictionary({ typeCode: 'project_platform' }).then(res => res.data)
      }
    },
    async getMtaList() {
      if (!this.mtaList?.length) {
        this.mtaList = await listSysDictionary({ typeCode: 'mta' }).then(res => res.data)
      }
    },
    async getShadowConfigList() {
      if (!this.shadowConfigList?.length) {
        this.shadowConfigList = await listSysDictionary({ typeCode: 'shadow_config' }).then(res => res.data)
      }
    },
    async getFunctionTypeList() {
      if (!this.functionTypeList?.length) {
        this.functionTypeList = await listSysDictionary({ typeCode: 'function_type' }).then(res => res.data)
      }
    },
    async getDataTypeList() {
      if (!this.dataTypeList?.length) {
        this.dataTypeList = await listSysDictionary({ typeCode: 'aio_data_type' }).then(res => res.data)
      }
    },
    initForm() {
      const _this = this
      const scrollFormDom = document.querySelector('.form')
      scrollFormDom.removeEventListener('scroll', _this.bindFormScrollEvent)
    },
    scrollToSection(sectionId) {
      const sectionElement = document.getElementById(sectionId)
      if (sectionElement) {
        sectionElement.scrollIntoView({ behavior: 'smooth' })
      }
    },
    bindScrollActive() {
      const _this = this
      const scrollFormDom = document.querySelector('.form')
      scrollFormDom.addEventListener('scroll', _this.bindFormScrollEvent)
      this.bindFormScrollEvent()
    },
    bindFormScrollEvent() {
      const _this = this
      const scrollFormDom = document.querySelector('.form')
      const sections = document.querySelectorAll('.form-section')
      const scrollTop = scrollFormDom.scrollTop
      sections.forEach(section => {
        const offsetTop = section.offsetTop - scrollFormDom.offsetTop - 200
        const offsetHeight = section.offsetHeight
        if (scrollTop >= offsetTop && scrollTop < offsetTop + offsetHeight) {
          _this.activeId = section.id
        }
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        this.$refs.formAIORef.validate(valid => {
          if (!valid) return
          let postData = { ...this.form, taskId: this.taskId }
          // if (!postData.softwarePackage?.length) {
          //   showToast(this.$t('请选择软件更新'), 'warning')
          // }
          // if (!postData.whiteList?.length) {
          //   showToast(this.$t('请选择白名单'), 'warning')
          // }
          if (this.dialogStatus !== 'view') {
            if (!this.form.id) {
              saveAioConfig(postData)
                .then(res => {
                  this.form.id = res.data.id
                  resolve(true)
                })
                .catch(err => {
                  reject(err)
                })
            } else {
              updateAioConfig(postData)
                .then(res => {
                  resolve(true)
                })
                .catch(err => {
                  reject(err)
                })
            }
          } else {
            resolve(true)
          }
        })
      })
    },
    addSoftware() {
      this.$refs.AddSoftwareUpdate.show({
        type: 'add',
        data: { checkedList: this.form.softwarePackage ? Object.keys(this.form.softwarePackage) : [] }
      })
    },
    editSoftwarePackage(row, key) {
      this.$refs.AddSoftwareUpdate.show({
        type: 'edit',
        data: { url: row.url, type: key }
      })
    },
    reloadSoftwareUpdate(row) {
      this.form.softwarePackage ??= {}
      this.form.softwarePackage[row.type] = row.data
    },
    removeSoftware(key) {
      delete this.form.softwarePackage[key]
    },
    addWhiteList() {
      this.$refs.AddWhiteList.show({
        type: 'add',
        data: { checkedList: this.form.whiteList ? Object.keys(this.form.whiteList) : [] }
      })
    },
    editWhiteList(tableData, key) {
      this.$refs.AddWhiteList.show({
        type: 'edit',
        data: { urlList: tableData.map(val => val.url), type: key }
      })
    },
    reloadWhiteList(row) {
      this.form.whiteList ??= {}
      this.form.whiteList[row.type] = row.data
    },
    removeWhiteList(key) {
      delete this.form.whiteList[key]
    },
    previewTextFile(row) {
      const arr = ['txt', 'json']
      const isTxt = !!~arr.findIndex(val => val === row.url.split('.')[row.url.split('.')?.length - 1])
      // if(row.url){}

      if (isTxt) {
        previewAioConfigDetail({
          url: row.url,
          token: util.getToken()
        }).then(async res => {
          let data = await this.readFile(res)
          const limitSize = 5
          if (row.fileSize > limitSize * Math.pow(2, 20)) {
            showToast(`暂时仅支持预览${limitSize}M以内的TXT和JSON文件`, 'warning')
          } else {
            this.$refs.ViewTxtDialog.show({ type: 'view', data: data })
          }
        })
      } else {
        showToast('暂时仅支持预览TXT和JSON文件', 'warning')
      }
    },
    downloadFile(row) {
      const limitSize = 10
      if (row.size < limitSize * Math.pow(2, 20).toFixed(2)) {
        previewAioConfigDetail({
          url: row.url,
          token: util.getToken()
        }).then(async res => {
          let data = await this.readFile(res)
          downloadTxt(row.name, data)
        })
      } else {
        showToast(`该文件大于${limitSize}M下载请联系管理员`, 'warning')
      }
    },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = event => {
          // 获取文件的字节数据
          resolve(event.target.result)
        }
        reader.onerror = event => {
          reject(error)
        }
        reader.readAsText(file) // 使用readAsText方法读取文件内容
      })
    },
    setDefault(row, key) {
      this.form.whiteList[key].forEach(val => {
        if (val.url === row.url) {
          val.enabled = true
        } else {
          val.enabled = false
        }
      })
    },
    deleteWhiteList(index, key) {
      this.form.whiteList[key].splice(index, 1)
    },
    handleCheckAllChangeFunctionTypeList(booleanVal) {
      this.form.functionTypeList = booleanVal ? this.functionTypeList.map(val => val.code) : []
      this.isIndeterminateFunctionTypeList = false
    },
    handleCheckedChangeFunctionTypeList(list) {
      this.checkAllFunctionTypeList = list?.length === this.functionTypeList?.length
      this.isIndeterminateFunctionTypeList = list?.length > 0 && list?.length < this.functionTypeList?.length
    }
  }
}
</script>
<style lang="scss" scoped>
.aio-config {
  display: flex;
  height: 100%;
  width: 100%;
  padding-top: 6px;

  .catalog {
    width: 150px;
    position: relative;
    display: flex;
    flex-direction: column;

    .catalog-title {
      color: #909399;
      line-height: 30px;
      font-size: 12px;
      padding-left: 14px;

      &.active,
      &:hover {
        color: #409eff;
        cursor: pointer;
      }
    }

    .active-line {
      position: absolute;
      left: 0;
      background: #409eff;
      width: 4px;
      height: 14px;
      transition:
        top 0.25s ease-in-out,
        opacity 0.25s;
      border-radius: 4px;
      //opacity: 0;
    }
  }

  .form {
    width: calc(100% - 150px);
    overflow: auto;
    height: 100%;

    .form-section {
      .sub-title {
        border-left: 2px solid #409eff;
        padding-left: 15px;
        font-size: 14px;
        font-weight: bold;
        color: #555d66;
        margin-bottom: 10px;
      }

      .function-bar {
        display: flex;
        justify-content: space-between;

        .title-btn {
          margin-left: 10px;
        }
      }
    }

    .scroll-form {
    }

    .form-section {
      padding-top: 10px;
    }

    .card-item {
      position: relative;
      overflow: visible;
      margin-bottom: 10px;
      margin-right: 10px;

      &:hover {
        .card-close {
          opacity: 1;
        }
      }

      .card-close {
        opacity: 0;
        position: absolute;
        right: -11px;
        top: -11px;
        font-size: 22px;
        background: #fff;
        transition: all 0.3s;
      }
    }

    .select-item {
      margin-bottom: 10px;
    }

    .software-update {
    }

    .white-list {
    }
  }
}
</style>
