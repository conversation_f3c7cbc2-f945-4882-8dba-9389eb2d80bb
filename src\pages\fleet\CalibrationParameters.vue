<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh" id="input">
            <template #append>
              <el-button @click="refresh" id="el-icon-search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <el-date-picker
          v-model="queryParam.calibrationDate"
          type="date"
          :placeholder="$t('标定日期')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="refresh"
          clearable
          @clear="queryParam.calibrationDate = undefined"
        />
        <dictionary-selection
          v-model="queryParam.status"
          :placeholder="$t('请选择状态')"
          clearable
          dictionaryType="calibration_status"
          @change="refresh"
        />
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
            :id="item.buttonIconCode"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary" id="batch-button">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                  :id="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table :data="pageData.records" stripe @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
        <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
        <el-table-column header-align="center" align="center" prop="vin" :label="$t('车架号')"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="activationDate"
          :label="$t('有效时间')"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="deactivationDate"
          :label="$t('失效时间')"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="calibrationDate"
          :label="$t('标定日期')"
        ></el-table-column>
        <!--        <el-table-column header-align="center" align="center" prop="toolVersion" :label="$t('标定工具版本')">-->
        <!--        </el-table-column>-->
        <el-table-column header-align="center" align="center" prop="enable" :label="$t('是否有效')" width="100">
          <template #default="scope">
            <el-link :type="scope.row.enable ? 'success' : 'danger'" :underline="false">
              {{ scope.row.enable ? '有效' : '无效' }}
            </el-link>
          </template>
          <template #header>
            <th-header-filter
              column="enable"
              fieldName="是否有效"
              filterType="select"
              :customArrList="enableList"
              @tableFilter="tableFilter"
              ref="enablePopover"
            ></th-header-filter>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="calibrationSyncStatus" :label="$t('COS文件同步')">
          <template #default="scope">
            <el-tag
              v-if="scope.row.calibrationSyncStatus"
              :type="getCalibrationSyncStatusType(scope.row.calibrationSyncStatus)"
            >
              {{ formatToName(scope.row.calibrationSyncStatus, calibrationSyncStatusList) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="160" header-align="center" align="center" prop="version" :label="$t('版本')">
          <template #default="scope">
            <el-tag v-if="scope.row.version">
              {{ scope.row.version }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" prop="status" :label="$t('状态')">
          <template #default="scope">
            <el-tag v-if="scope.row.status" :type="getStatusType(scope.row.status)">
              {{ formatToName(scope.row.status, statusList) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="200" header-align="center" prop="statusMsg" :label="$t('状态信息')">
          <template #default="scope">
            <el-tooltip placement="top">
              <template #content>
                <div style="white-space: pre-line" v-html="scope.row.statusMsg"></div>
              </template>
              <div class="table-column-tooltip">{{ scope.row.statusMsg }}</div>
            </el-tooltip>
            <!--            <div v-html="scope.row.statusMsg"></div>-->
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" :label="$t('操作')" min-width="180" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button
                  :type="item.buttonStyleType"
                  @click="executeButtonMethod(item, scope.row)"
                  :id="item.buttonIconCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <AddCalibration ref="AddCalibration" @reload="query" @cancel="cancelData"></AddCalibration>
  </div>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  listFtmCalibrationRecords,
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  deleteFtmCalibrationRecords,
  pageFtmCalibrationRecords,
  getFtmCalibrationRecords
} from '@/apis/fleet/ftm-calibration-records'
import { listFtmInstallationProjectSelection } from '@/apis/fleet/ftm-installation-project'
import { listSysRoleOrgSelection } from '@/apis/system/sys-role-org'
import AddCalibration from '@/pages/fleet/dialog/AddCalibration.vue'
import AddModality from '@/pages/fleet/dialog/AddModalityNew.vue'
import ThHeaderFilter from '@/components/fleet/thHeaderFilter'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import DictionarySelection from '@/components/system/DictionarySelection.vue'

const defaultFormData = {
  variantModalityList: []
}
export default {
  name: 'CalibrationParameters',
  components: {
    DictionarySelection,
    AddModality,
    AddCalibration,
    ThHeaderFilter
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),

      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},

      // modalityData: [],
      modalityQueryParam: {
        current: 1,
        size: 10
      },
      modalityTags: [],
      modalitySelectedData: [],
      // typeList: [],
      projectList: [],
      supplierList: [],
      enableList: [
        { code: true, name: '有效' },
        { code: false, name: '无效' }
      ],
      calibrationSyncStatusList: [],
      statusList: []
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
    this.listFtmInstallationProjectSelection()
    this.getSupplier()
    this.getCalibrationSyncStatusList()
    this.getStatusList()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    getStatusList() {
      listSysDictionary({ typeCode: 'calibration_status' }).then(res => {
        this.statusList = res.data
      })
    },
    getCalibrationSyncStatusList() {
      listSysDictionary({ typeCode: 'calibration_sync_status' }).then(res => {
        this.calibrationSyncStatusList = res.data
      })
    },
    getCalibrationSyncStatusType(code) {
      let type
      switch (code) {
        case 'WAITING':
          type = 'info'
          break
        case 'SUCCESS':
          type = 'success'
          break
        case 'FAIL':
          type = 'fail'
          break
      }
      return type
    },
    getStatusType(code) {
      let type
      switch (code) {
        case 'NORMAL':
          type = 'primary'
          break
        case 'WARNING':
          type = 'warning'
          break
        case 'EXCEPTION':
          type = 'danger'
          break
      }
      return type
    },
    formatToName(code, list) {
      let item = list.find(val => val.code.toLowerCase() === code.toLowerCase())
      return item?.name
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageFtmCalibrationRecords(this.queryParam).then(res => {
        // res.data.records[0].calibrationSyncStatus = 'WAITING'
        this.pageData = res.data
      })
    },
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = i18n.global.t('新增')
      this.vin = row.vin
      this.listFtmCalibrationRecords()
    },
    cancel() {
      this.dialogVisible = false
      // this.$emit('reload')
    },
    tableFilter(data) {
      if (data.column === 'enable') {
        this.queryParam.enable = data.conditions.select
      }
      this.refresh()
    },

    add() {
      this.$refs.AddCalibration.show({ type: 'add' })
    },
    // save() {
    //   this.$refs.formRef.validate(valid => {
    //     if (!valid) return
    //     if (this.dialogStatus === 'add') {
    //       saveFtmCalibrationRecords(this.formData).then(() => {
    //         this.dialogVisible = false
    //         this.query()
    //       })
    //     }
    //     if (this.dialogStatus === 'edit') {
    //       updateFtmCalibrationRecords(this.formData).then(() => {
    //         this.dialogVisible = false
    //         this.query()
    //       })
    //     }
    //   })
    // },
    edit(row) {
      this.$refs.AddCalibration.show({
        type: 'edit',
        data: {
          row: row
        }
      })
    },
    view(row) {
      this.$refs.AddCalibration.show({
        type: 'view',
        data: {
          row: row
        }
      })
      this.dialogStatus = 'view'
      this.dialogVisible = true
    },
    // //参数标定
    listFtmCalibrationRecords() {
      this.list = []
      listFtmCalibrationRecords({
        vin: this.vin
      }).then(res => {
        this.list = res.data.map(val => {
          val.draggable = false
          return val
        })
        this.edit = false
      })
    },

    reload(obj) {
      if (obj?.type === 'delete') {
        if (obj.form.id) {
          return this.listFtmCalibrationRecords()
        } else {
          this.list.splice(obj.index, 1)
        }
      } else if (obj?.type === 'add') {
        return this.listFtmCalibrationRecords()
      } else if (obj?.type === 'edit' || obj?.type === 'cancel') {
        this.list.splice(obj.index, 1, obj.form)
      } else if (obj?.type === 'view') {
        this.$refs.AddCalibration.show({
          type: 'view',
          data: obj.form
        })
        return
      }
      this.edit = obj.form.edit
      if (obj.form.edit) {
        this.cancelDirective({}, obj.form)
      } else {
        this.registerDirective({}, obj.form, obj.index)
      }
    },
    cancelData() {},

    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmCalibrationRecords(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      if (this.dialogStatus !== 'view') {
        this.$refs.formRef.resetFields()
      }
      this.formData = {
        variantModalityList: []
      }
    },
    handleSelectModalityChange(val) {
      this.formData.modalityList = val
    },
    removeTag(val) {},
    handleClear() {
      this.formData.modalityList = []
    },

    handleNameClick(row) {},
    getFtmCalibrationRecords() {
      getFtmCalibrationRecords(this.formData.id).then(res => {
        this.formData.variantModalityList = res.data.variantModalityList
      })
    },
    listFtmInstallationProjectSelection() {
      if (!this.projectList?.length) {
        listFtmInstallationProjectSelection().then(res => {
          this.projectList = res.data
        })
      }
    },

    getSupplier() {
      if (!this.supplierList?.length) {
        listSysRoleOrgSelection({ tagCode: 'org_supplier' }).then(res => {
          this.supplierList = res.data
        })
      }
    },
    getSupplierName(val) {
      let supplierItem = this.supplierList.find(item => item.id === val)
      this.formData.supplierName = supplierItem.name
    }
  }
}
</script>

<style lang="scss">
.variant-tag.el-popover {
  width: auto !important;
  min-width: auto;
  max-width: 400px;

  .el-tag {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>
<style scoped lang="scss">
.ltw-toolbar {
  margin-bottom: 0;

  :deep(.el-date-editor),
  .el-select {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .ltw-search-container {
    width: auto;
  }
}

.button-group {
  .el-button {
    margin-right: 10px;
  }
}

.header-title {
  padding-left: 10px;
  border-left: 2px solid #409eff;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  align-items: center;
}

.table-column-tooltip {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
</style>
