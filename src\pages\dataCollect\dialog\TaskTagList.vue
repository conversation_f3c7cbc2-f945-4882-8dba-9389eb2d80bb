<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      class="tag-list"
  >
    <bs-tag-group-panel
        :continuous-units="continuousUnits"
        :edit-tags="editTagsFlag"
        :timeEdit="editTagsFlag"
        :attributes="attributes"
        :classificationTag="true"
        :data="tagsData"
        @tagClose="tagClose"
        @tagSave="tagSave"
    ></bs-tag-group-panel>
  </el-dialog>

</template>

<script>
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import TAG_ATTRIBUTES from '@/plugins/constants/tag-attributes.js'

export default {
  name: 'checkPwd',
  emits: ['reload', 'tag-save'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      editTagsFlag: false,
      tagsData: [],
      continuousUnits: [],
      tagList: [],
      requirementList: [],
      treeTagGroupList: [],
      taskType: '',
      attributes: [
        TAG_ATTRIBUTES.SUPPORT_TRIGGER,
        TAG_ATTRIBUTES.SUPPORT_VOICE,
        TAG_ATTRIBUTES.FOLLOWING_DURATION,
        TAG_ATTRIBUTES.PREVIOUS_DURATION
      ]
    }
  },
  components: {
    BsTagGroupPanel
  },
  methods: {
    async show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.taskType = row.data.taskType
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增标签')
          this.editTagsFlag = true
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑标签')
          this.editTagsFlag = true
          break
        case 'view':
          this.dialogTitle = this.$t('查看标签')
          this.editTagsFlag = false
          break
      }
      this.tagsData = JSON.parse(JSON.stringify(row.data.groupList || []))
    },

    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.editTagsFlag = false
    },
    tagClose() {
      this.dialogVisible = false
      this.dialogClosed()
    },
    tagSave(dataList) {
      this.$emit('tag-save', { type: this.dialogStatus, data: { taskType: this.taskType, groupList: dataList } })
      this.tagClose()
    }
  }
}
</script>

<style scoped lang="scss"></style>
