<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="60%" @close="dialogClosed" @open="dialogOpened">
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="form.name" id="name" :disabled="formReadonly"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('地址')" id="address" prop="address">
        <ltw-input v-model="form.address" :disabled="formReadonly" textType="address"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('规模')" v-if="dialogStatus === 'view'" id="scale" prop="scale">
        <ltw-input v-model="form.scale" :disabled="formReadonly"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('编码')" v-if="dialogStatus === 'view'" id="code" prop="code">
        <ltw-input v-model="form.code" :disabled="formReadonly"></ltw-input>
      </el-form-item>
      <!-- <el-form-item :label="$t('位置')" prop="latitude">
        <el-popover :width="400" trigger="hover">
          <template #reference>
            <el-link :disabled="formReadonly && !form.longitude && !form.latitude" :underline="false" @click="getPosition"
              type="success" id="getPosition">
              <ltw-icon icon-code="el-icon-map-location"></ltw-icon></el-link>
          </template>
          <parking-position :position="{ latitude: form.latitude, longitude: form.longitude }" ref="ParkingPosition" />
        </el-popover>
      </el-form-item> -->
      <el-form-item :label="$t('区域')" id="cantonCode" prop="cantonCode">
        <el-cascader
            filterable
            clearable
            popper-class="canton-list"
            v-model="form.cantonCode"
            :options="cantonCodeList"
            :props="props"
            :disabled="formReadonly"
        />
      </el-form-item>
      <el-form-item :label="$t('环境类型')" id="envType" prop="envType">
        <el-select filterable v-model="form.envType" :disabled="formReadonly" clearable>
          <el-option v-for="item in envTypeList" :key="item.code" :label="item.name" :value="item.code" id="envType" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('描述')" id="description" prop="description">
        <ltw-input
            type="textarea"
            v-model="form.description"
            :disabled="formReadonly"
            textType="description"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <div class="parking-list">
      <!-- <div class="header-title">
        {{ $t('停车场明细') }}
        <el-button v-if="!formReadonly" size="small" id="addParkingLot" type="primary" @click="add"><ltw-icon
            icon-code="el-icon-plus"></ltw-icon>{{ $t('新增') }}</el-button>
      </div> -->
      <!-- <el-button v-if="!formReadonly" size="small" id="addParkingLot" type="primary" @click="add"><ltw-icon
            icon-code="el-icon-plus"></ltw-icon>{{ $t('新增') }}</el-button> -->
      <el-tabs v-model="tabValue" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="停车场明细" name="停车场明细">
          <template v-for="(item, index) in form.parkingLotDetailList" :key="index">
            <parking-lot-form
                @reload="reload"
                :ref="'ParkingLotForm' + index"
                :index="index"
                :form-readonly="formReadonly"
                :item="item"
                :parkingInnerColorList="parkingInnerColorList"
                :parkingLotMaterialList="parkingLotMaterialList"
                :groundLevelList="groundLevelList"
                :parkingLotRrightnessList="parkingLotRrightnessList"
                :parkingSpaceTypeList="parkingSpaceTypeList"
                :parkingLotConfig="parkingLotConfig"
                :parkingLotInnerLineColorList="parkingLotInnerLineColorList"
                :parkingLotLineStatusList="parkingLotLineStatusList"
            />
          </template>
        </el-tab-pane>
        <el-tab-pane v-if="dialogStatus !== 'add'" label="停车位" name="停车位">
          <el-tooltip
              effect="dark"
              :content="$t('新增停车位')"
              placement="top"
              :enterable="false"
              v-if="dialogStatus === 'edit'"
          >
            <el-button type="primary" style="margin-right: 20px" @click="addSlot">
              <ltw-icon icon-code="el-icon-circle-plus"></ltw-icon>
            </el-button>
          </el-tooltip>
          <ltw-input
              :placeholder="$t('请输入编码')"
              v-model="queryParam.code"
              clearable
              @clear="queryParkingSlot"
              style="width: 50%"
          >
            <template #append>
              <el-button @click="queryParkingSlot" id="search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
          <el-table :data="pageSlotData.records" row-key="id" ref="tableRef">
            <el-table-column
                width="135"
                header-align="left"
                align="left"
                prop="code"
                :label="$t('编码')"
            ></el-table-column>
            <el-table-column
                width="80"
                header-align="left"
                align="left"
                prop="lineWidth"
                :label="$t('车位线宽')"
            ></el-table-column>
            <el-table-column header-align="left" align="left" prop="typeName" :label="$t('车位类型')"></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="lockTypeName"
                :label="$t('地锁类型')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="stopperTypeName"
                :label="$t('限位器类型')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="lineTypeName"
                :label="$t('车位线类型')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="lineColorName"
                :label="$t('车位线颜色')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="innerColorName"
                :label="$t('车位内部颜色')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="roadSurfaceMaterialName"
                :label="$t('地面材质')"
            ></el-table-column>
            <el-table-column header-align="left" align="left" prop="rtkData" :label="$t('rtk数据')">
              <template #default="scope">
                <el-button v-if="scope.row.rtkData" @click="showRtkData(scope.row.rtkData)" type="primary" size="small">
                  <ltw-icon icon-code="el-icon-view"></ltw-icon>
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="specialTag"
                :label="$t('车位特征')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                :label="$t('操作')"
                width="180"
                fixed="right"
                v-if="dialogStatus === 'edit'"
            >
              <template #default="scope">
                <el-button-group>
                  <el-button type="warning">
                    <ltw-icon icon-code="el-icon-edit" @click="editSlot(scope.row)"></ltw-icon>
                  </el-button>
                  <el-button type="danger">
                    <ltw-icon icon-code="el-icon-delete" @click="removeSlot(scope.row)"></ltw-icon>
                  </el-button>
                  <el-button type="info">
                    <ltw-icon icon-code="el-icon-view" @click="viewSlot(scope.row)"></ltw-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="queryParam.current"
              :page-sizes="[5, 10, 20, 30]"
              :page-size="queryParam.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pageSlotData.total"
          ></el-pagination>
        </el-tab-pane>
        <el-tab-pane v-if="dialogStatus !== 'add'" label="停车场入口" name="停车场入口">
          <el-tooltip
              effect="dark"
              :content="$t('新增停车场入口')"
              placement="top"
              :enterable="false"
              v-if="dialogStatus === 'edit'"
          >
            <el-button type="primary" style="margin-right: 20px" @click="addEntrance">
              <ltw-icon icon-code="el-icon-circle-plus"></ltw-icon>
            </el-button>
          </el-tooltip>
          <ltw-input
              :placeholder="$t('请输入编码')"
              v-model="entranceQueryParam.code"
              clearable
              @clear="queryParkingEntrance"
              style="width: 50%"
          >
            <template #append>
              <el-button @click="queryParkingEntrance" id="search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
          <el-table :data="pageEntranceData.records" row-key="id" ref="tableRef">
            <el-table-column
                width="135"
                header-align="left"
                align="left"
                prop="code"
                :label="$t('编码')"
            ></el-table-column>
            <el-table-column header-align="left" align="left" prop="name" :label="$t('入口名称')"></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="routeName"
                :label="$t('进入口路线')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="barrierTypeName"
                :label="$t('道闸类型')"
            ></el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                prop="rampTypeName"
                :label="$t('入口坡道类型')"
            ></el-table-column>
            <el-table-column header-align="left" align="left" prop="hasBarrier" :label="$t('是否有闸机')">
              <template #default="scope">
                {{ scope.row.hasBarrier ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column
                header-align="left"
                align="left"
                :label="$t('操作')"
                width="160"
                fixed="right"
                v-if="dialogStatus === 'edit'"
            >
              <template #default="scope">
                <el-button-group>
                  <el-button type="warning">
                    <ltw-icon icon-code="el-icon-edit" @click="editEntrance(scope.row)"></ltw-icon>
                  </el-button>
                  <el-button type="danger">
                    <ltw-icon icon-code="el-icon-delete" @click="removeEntrance(scope.row)"></ltw-icon>
                  </el-button>
                  <el-button type="info">
                    <ltw-icon icon-code="el-icon-view" @click="viewEntrance(scope.row)"></ltw-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              background
              @size-change="handleEntranceSizeChange"
              @current-change="handleEntranceCurrentChange"
              :current-page="entranceQueryParam.current"
              :page-sizes="[5, 10, 20, 30]"
              :page-size="entranceQueryParam.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pageEntranceData.total"
          ></el-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{
              $t('关闭')
            }}</el-button>
        </template>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
    <map-search-position @changePosition="changePosition" ref="MapSearchPosition" />
  </el-dialog>
  <el-dialog title="查看rtkData" v-model="rtkDialogVisible" width="600px" append-to-body :draggable="true">
    <div class="text">
      <Codemirror v-model:value="rtkData" :options="cmOptions" :height="300" />
    </div>
    <template #footer>
      <el-button @click="rtkDialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
    </template>
  </el-dialog>
  <add-parking-slot @reload="queryParkingSlot" ref="AddParkingSlot" />
  <add-parking-entrance @reload="queryParkingEntrance" ref="AddParkingEntrance" />
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import { saveFtmParkingLots, updateFtmParkingLots, viewParkingLot } from '@/apis/fleet/ftm-parking-lot-detail'
import { pageFtmParkingSlot, deleteFtmParkingSlot } from '@/apis/fleet/ftm-parking-slot'
import { pageFtmParkingEntrance, deleteFtmParkingEntrance } from '@/apis/fleet/ftm-parking-entrance'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import MapSearchPosition from '@/components/map/MapSearchPosition.vue'
import ParkingPosition from '@/components/map/ParkingPosition.vue'
import ParkingLotForm from '@/pages/fleet/components/ParkingLotForm.vue'
import AddParkingSlot from '@/pages/fleet/dialog/AddParkingSlot.vue'
import AddParkingEntrance from '@/pages/fleet/dialog/AddParkingEntrance.vue'
import { getFtmParkingLotFieldItems } from '@/apis/fleet/ftm-parking-lot'

const defaultform = {
  parkingLotDetailList: []
}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      parkingLotId: '',
      tabValue: '停车场明细',
      queryParam: {
        current: 1,
        size: 10
      },
      entranceQueryParam: {
        current: 1,
        size: 10
      },
      pageSlotData: {
        total: 0
      },
      pageEntranceData: {
        total: 0
      },
      rtkData: '',
      rtkDialogVisible: false,
      cmOptions: {
        mode: 'application/json', // Language mode text/yaml、text/javascript
        theme: 'dracula', // Theme
        // readOnly: 'nocursor'
        indentUnit: 4, // 缩进多少个空格
        tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否默认换行
        // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
        // line: true,
        smartIndent: true // 智能缩进
      },
      booleanList: [
        {
          name: '是',
          code: true
        },
        {
          name: '否',
          code: false
        }
      ],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, JSON.parse(JSON.stringify(defaultform))),

      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('名称'),
            trigger: 'blur'
          }
        ],
        envType: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('环境类型'),
            trigger: 'blur'
          }
        ],
        // latitude: [
        //   {
        //     required: true,
        //     message: this.$t('请选择'),
        //     trigger: 'change'
        //   }
        // ],
        address: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        cantonCode: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        empId: [
          {
            required: true,
            message: this.$t('请选择发布人'),
            trigger: 'change'
          }
        ],
        dateRange: [
          {
            required: true,
            message: this.$t('请选择期望日期'),
            trigger: 'blur'
          }
        ],
        tagAmount: [
          {
            required: true,
            message: this.$t('请选择标签'),
            trigger: 'change'
          }
        ]
      },
      $t: this.$t,
      tagList: [],
      // 配置标签
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      // 选择标签
      tagsData: [],
      tagGroupList: [],
      tagsTitle: '',
      cantonCodeList: [],
      envTypeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
      },
      parkingInnerColorList: [],
      parkingLotMaterialList: [],
      groundLevelList: [],
      parkingLotRrightnessList: [],
      parkingSpaceTypeList: [],
      parkingLotConfig: [],
      parkingLotInnerLineColorList: [],
      parkingLotLineStatusList: [],

      parkingSlotLineStatusList: [],
      parkingSlotLockTypeList: [],
      parkingSlotInnerLineColorList: [],
      parkingSlotWheelBlockTypeList: [],
      parkingSlotInnerColorList: [],
      parkingSlotMaterialList: [],
      parkingEntranceRouteList: [],
      parkingEntranceRampList: [],
      parkingEntranceBarrierList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    MapSearchPosition,
    ParkingLotForm,
    ParkingPosition,
    Codemirror,
    AddParkingSlot,
    AddParkingEntrance
  },
  methods: {
    addSlot() {
      this.$refs.AddParkingSlot.show({ type: 'add', parkingLotId: this.parkingLotId })
    },
    editSlot(row) {
      this.$refs.AddParkingSlot.show({ type: 'edit', id: row.id })
    },
    removeSlot(row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmParkingSlot({
          id: row.id
        }).then(() => {
          this.queryParkingSlot()
        })
      })
    },
    viewSlot(row) {
      this.$refs.AddParkingSlot.show({ type: 'view', id: row.id })
    },

    addEntrance() {
      this.$refs.AddParkingEntrance.show({ type: 'add', parkingLotId: this.parkingLotId })
    },
    editEntrance(row) {
      this.$refs.AddParkingEntrance.show({ type: 'edit', id: row.id })
    },
    removeEntrance(row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmParkingEntrance({
          id: row.id
        }).then(() => {
          this.queryParkingEntrance()
        })
      })
    },
    viewEntrance(row) {
      this.$refs.AddParkingEntrance.show({ type: 'view', id: row.id })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.queryParkingSlot()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.queryParkingSlot()
    },
    handleEntranceSizeChange(value) {
      this.entranceQueryParam.size = value
      this.queryParkingEntrance()
    },
    handleEntranceCurrentChange(value) {
      this.entranceQueryParam.current = value
      this.queryParkingEntrance()
    },
    handleClick(tab) {
      if (tab.props.label === '停车位') {
        this.queryParkingSlot()
      } else if (tab.props.label === '停车场入口') {
        this.queryParkingEntrance()
      }
    },
    showRtkData(rtkData) {
      // this.rtkData = JSON.stringify(
      //   JSON.parse(rtkData || '{}'),
      //   null,
      //   '\t'
      // )
      this.rtkData = rtkData
      this.rtkDialogVisible = true
    },
    getDicName(value, list) {
      if (list?.length) {
        if (Array.isArray(value)) {
          let nameList = []
          value.forEach(val => {
            let item = list.find(listVal => {
              return listVal.code === val
            })
            nameList.push(item?.name)
          })
          return nameList
        } else {
          let item = list.find(val => {
            return val.code === (value || false)
          })
          return item?.name
        }
      }
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.tabValue = '停车场明细'
      this.parkingLotId = row.parkingLotId
      this.queryParam.code = ''
      this.entranceQueryParam.code = ''
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增停车场')
          this.add()
          // this.form.empId = this.$store.state.permission.currentUser.empId
          break
        case 'copy':
          this.dialogTitle = this.$t('新增停车场')
          this.viewParkingLot(row.parkingLotId)
          // this.form.empId = this.$store.state.permission.currentUser.empId
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑停车场')
          this.viewParkingLot(row.parkingLotId)
          break
        case 'view':
          this.dialogTitle = this.$t('查看停车场')
          this.viewParkingLot(row.parkingLotId)
          break
      }
      this.getSysCantonTree()
      this.getEnvTypeList()
      this.getgroundLevelList() //停车场类型
      this.getparkingLotMaterialList() //停车场地面材质
      this.getparkingLotRrightnessList() //停车场亮度
      this.getparkingSpaceTypeList() //停车场车位类型
      this.getparkingLotConfig() //停车场车位配置
      this.getparkingLotInnerLineColorList() //车位线颜色
      this.getparkingInnerColorList() //车位内部颜色
      this.getparkingLotLineStatusList() //车位线状态

      //查询停车位和停车场入口字典
      // this.getparkingSlotLockType() // 地锁类型
      // this.getparkingSlotWheelBlockType() //限位器类型
      // this.getparkingSlotLineStatus()  //车位线类型
      // this.getparkingSlotInnerLineColor()  //车位线颜色
      // this.getparkingSlotInnerColor()  //车位内部颜色
      // this.getparkingSlotMaterial()  //地面材质
      // this.getparkingEntranceRoute()  // 进入口路线
      // this.getparkingEntranceRamp() //入口坡道类型
      // this.getparkingEntranceBarrier() //入口道闸类型
    },
    getSysCantonTree() {
      getSysCantonTree().then(res => {
        this.cantonCodeList = res.data
      })
    },
    getEnvTypeList() {
      getFtmParkingLotFieldItems({
        fieldCode: 'parking_env'
      }).then(res => {
        this.envTypeList = res.data
      })
    },
    add() {
      if (~this.form.parkingLotDetailList.findIndex(val => val.edit === true)) {
        return showToast('请先保存一条停车场明细', 'warning')
      } else {
        if (!this.form.id) {
          this.form.parkingLotDetailList.unshift({
            edit: true,
            roadSurfaceMaterialCodeList: []
          })
        } else {
          this.form.parkingLotDetailList.unshift({
            edit: true,
            parkingLotId: this.form.id,
            roadSurfaceMaterialCodeList: []
          })
        }
      }
    },
    dialogClosed() {
      this.initForm()
      this.cancel()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = { parkingLotDetailList: [] }
    },
    submit() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        if (!this.form?.parkingLotDetailList?.length) {
          return showToast('请至少添加一条停车场明细', 'warning')
        } else {
          let submitFlag = true
          for (let i = 0, iLen = this.form?.parkingLotDetailList?.length; i < iLen; i++) {
            if (this.form?.parkingLotDetailList[i].edit === true) {
              submitFlag = await this.$refs['ParkingLotForm' + i][0].checkValidate()
              if (!submitFlag) {
                return
              }
            }
          }
          if (!submitFlag) {
            return
          }
        }
        let postData = {
          ...this.form
        }
        if (Array.prototype.isPrototypeOf(this.form.cantonCode)) {
          postData.cantonCode = this.form.cantonCode[this.form.cantonCode?.length - 1]
        } else {
          postData.cantonCode = this.form.cantonCode
        }
        if (this.dialogStatus === 'add' || this.dialogStatus === 'copy') {
          saveFtmParkingLots(postData).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmParkingLots(postData).then(() => {
            this.cancel()
          })
        }
      })
    },
    viewParkingLot(parkingLotId) {
      viewParkingLot({ parkingLotId }).then(res => {
        // this.tagList = JSON.parse(JSON.stringify(res.data.tagList || []))
        // res.data.dateRange = [
        //   res.data.expectedStartTime,
        //   res.data.expectedEndTime
        // ]
        // this.tagList = res.data.tagList
        if (this.dialogStatus === 'copy') {
          res.data.id = ''
          if (res.data.parkingLotDetailList?.length) {
            res.data.parkingLotDetailList.forEach(val => {
              val.id = ''
              val.parkingLotId = ''
              val.edit = true
            })
          }
        }
        this.form = res.data
      })
    },
    queryParkingSlot() {
      this.queryParam.parkingLotId = this.form.id
      pageFtmParkingSlot(this.queryParam).then(res => {
        this.pageSlotData = res.data
      })
    },
    queryParkingEntrance() {
      this.entranceQueryParam.parkingLotId = this.form.id
      pageFtmParkingEntrance(this.entranceQueryParam).then(res => {
        this.pageEntranceData = res.data
      })
    },
    getPosition() {
      this.$refs.MapSearchPosition.show({
        lng: this.form.longitude,
        lat: this.form.latitude,
        name: this.form.name,
        address: this.form.address,
        formReadonly: this.formReadonly
      })
    },
    changePosition(obj) {
      if (!this.form.name) {
        this.form.name = obj.name
      }
      if (!this.form.address) {
        this.form.address = obj.address
      }
      this.form.latitude = obj.lat
      this.form.longitude = obj.lng
    },
    reload(obj) {
      if (obj?.type === 'delete') {
        this.form.parkingLotDetailList.splice(obj.index, 1)
      } else if (obj?.type === 'add') {
        this.form.parkingLotDetailList.splice(obj.index, 1, obj.form)
      } else {
        // this.getParkingLotDetailList()
      }
    },
    getParkingLotDetailList() {},
    getparkingInnerColorList() {
      if (!this.parkingInnerColorList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_inner_color'
        }).then(res => {
          this.parkingInnerColorList = res.data
        })
      }
    },
    getparkingLotMaterialList() {
      if (!this.parkingLotMaterialList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_lot_material'
        }).then(res => {
          this.parkingLotMaterialList = res.data
        })
      }
    },
    getgroundLevelList() {
      if (!this.groundLevelList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'ground_level'
        }).then(res => {
          this.groundLevelList = res.data
        })
      }
    },
    getparkingLotRrightnessList() {
      if (!this.parkingLotRrightnessList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_lot_brightness'
        }).then(res => {
          this.parkingLotRrightnessList = res.data
        })
      }
    },
    getparkingSpaceTypeList() {
      if (!this.parkingSpaceTypeList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_space_type'
        }).then(res => {
          this.parkingSpaceTypeList = res.data
        })
      }
    },
    getparkingLotConfig() {
      if (!this.parkingLotConfig?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_lot_config'
        }).then(res => {
          this.parkingLotConfig = res.data
        })
      }
    },
    getparkingLotInnerLineColorList() {
      if (!this.parkingLotInnerLineColorList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_lot_inner_line_color'
        }).then(res => {
          this.parkingLotInnerLineColorList = res.data
        })
      }
    },
    getparkingLotLineStatusList() {
      if (!this.parkingLotLineStatusList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_lot_line_status'
        }).then(res => {
          this.parkingLotLineStatusList = res.data
        })
      }
    }
  }
}
</script>
<style>
.canton-list .el-cascader-panel {
  height: 50vh;
}
</style>
<style scoped lang="scss">
.el-form-item {
  .el-link {
    font-size: 20px;
  }
}

.parking-list {
  padding-left: 15px;

  .header-title {
    padding-left: 15px;
    border-left: 3px solid #409eff;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}
</style>
