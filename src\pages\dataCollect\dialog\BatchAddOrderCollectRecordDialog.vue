<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    draggable
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row>
        <el-col :span="12" v-if="form.code">
          <el-form-item :label="$t('编号')" prop="code">
            <el-tag>{{ form.code }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('任务')" prop="taskCode">
            <el-tag>{{ form.taskCode }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车辆')" prop="vin">
            <el-tag>{{ form.vin }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('采集类型')" id="acquisitionType" prop="acquisitionType">
            <el-radio-group v-model="form.acquisitionType" id="acquisitionType">
              <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
                >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
            <!--            <el-select-->
            <!--              :disabled="formReadonly"-->
            <!--              v-model="form.acquisitionType"-->
            <!--              filterable-->
            <!--              clearable-->
            <!--              :placeholder="$t('请选择')"-->
            <!--              @clear="form.acquisitionType = undefined"-->
            <!--              popper-class="selector-input"-->
            <!--            >-->
            <!--              <el-option-->
            <!--                v-for="item in acquisitionTypeList"-->
            <!--                :key="item.code"-->
            <!--                :label="item.name"-->
            <!--                :value="item.code"-->
            <!--              ></el-option>-->
            <!--            </el-select>-->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('区域')" id="cantonCode" prop="cantonCode">
            <el-cascader
              id="cantonCode"
              clearable
              filterable
              popper-class="canton-list"
              v-model="form.cantonCode"
              :options="cantonCodeList"
              :props="props"
              :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>

        <el-col>
          <el-form-item :label="$t('执行日期')" prop="executingDate">
            <el-date-picker
              :disabled="formReadonly"
              v-model="form.executingDate"
              type="daterange"
              :range-separator="$t('到')"
              :start-placeholder="$t('开始日期')"
              :end-placeholder="$t('结束日期')"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('备注')" prop="remark">
            <ltw-input type="textarea" :limit-size="250" v-model="form.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button id="close" @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{
            $t('关闭')
          }}</el-button>
        </template>
        <template v-else>
          <el-button id="cancel" @click="dialogVisible = false">{{ $t('取消') }}</el-button>
          <el-button id="save" type="primary" @click="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { getDAQTaskRecords, batchAddDaqTaskRecords } from '@/apis/data-collect/acquisition-task-record'
import util, { dateUtils } from '@/plugins/util'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import LtwInput from '@/components/base/LtwInput.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

const defaultform = {}
export default {
  name: 'AddCollectRecordDialog',
  emits: ['reload'],
  components: { LtwInput },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        acquisitionType: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],

        executingDate: [{ required: true, message: this.$t('请输入'), trigger: 'blur' }]
      },

      cantonCodeList: [],
      acquisitionTypeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      if (row?.data?.taskCode) {
        this.form.taskCode = row.data.taskCode
        // this.form.executingDate = row.data.startTime
        this.form.acquisitionType = 'driving'

        this.form.vin = row.data.vehicleVin
        // this.form.acquisitionType = row.data.acquisitionType
      }
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('批量新增采集任务记录')
          break
      }
      // this.getParkingList()
      this.getSysCantonTree()
      this.getAcquisitionTypeList()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        if (Array.prototype.isPrototypeOf(this.form.cantonCode)) {
          postData.cantonCode = this.form.cantonCode[this.form.cantonCode?.length - 1]
        } else {
          postData.cantonCode = this.form.cantonCode
        }
        if (postData.executingDate?.length) {
          postData.startTime = dateUtils.parseTime(postData.executingDate[0], '{y}-{m}-{d}') + ' 00:00:00'
          postData.endTime = dateUtils.parseTime(postData.executingDate[1], '{y}-{m}-{d}') + ' 00:00:00'
        }
        delete postData.executingDate
        batchAddDaqTaskRecords(postData).then(() => {
          this.cancel()
        })
      })
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    getAcquisitionTypeList() {
      if (!this.acquisitionTypeList?.length) {
        listSysDictionary({
          typeCode: 'data_acquisition_type'
        }).then(res => {
          this.acquisitionTypeList = res.data
        })
      }
    }
  }
}
</script>

<style>
.canton-list .el-cascader-panel {
  height: 50vh;
}
</style>
