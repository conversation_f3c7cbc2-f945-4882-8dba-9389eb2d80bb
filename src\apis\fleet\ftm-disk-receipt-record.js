import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDiskReceiptRecord = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records',
    data,
    params
})
export const updateFtmDiskReceiptRecord = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records',
    data,
    params
})
export const deleteFtmDiskReceiptRecord = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records',
    params
})
export const listFtmDiskReceiptRecord = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records',
    params
})
export const listFtmDiskReceiptRecordSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records/selections',
    params
})
export const pageFtmDiskReceiptRecord = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records/page',
    params
})
export const getFtmDiskReceiptRecord = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_disk_receipt_records/' + id})
