import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysApplicationMappingPersonalInfo = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos', data, params})
export const updateSysApplicationMappingPersonalInfo = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos', data, params})
export const deleteSysApplicationMappingPersonalInfo = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos', params})
export const listSysApplicationMappingPersonalInfo = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos', params})
export const listSysApplicationMappingPersonalInfoSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos/selections', params})
export const pageSysApplicationMappingPersonalInfo = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos/page', params})
export const getSysApplicationMappingPersonalInfo = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_personal_infos/' + id})
