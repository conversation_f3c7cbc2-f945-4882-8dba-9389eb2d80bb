import { Shader<PERSON><PERSON>ial, Mesh, Vertex<PERSON><PERSON>er, Material, BoundingInfo, Vector3 } from './trimmed-babylonjs';
import { matrixFromDirection } from './utils';
const vertexSource = `
precision highp float;

attribute vec3 position;
attribute vec3 a_color;
attribute float a_intensity;

uniform float u_size;
uniform int u_useDefaultColor;
uniform vec3 u_color;

uniform mat4 u_transform;
uniform mat4 worldView;
uniform mat4 worldViewProjection;
uniform float u_opacity;
uniform float u_enable_intensity;

varying vec3 v_color;
varying float v_opacity;

void main() {
    bool use_intensity = bool(u_enable_intensity) && bool(a_intensity);

    if (use_intensity) {
        v_color = vec3(1.0, a_intensity, 0.0);
    } else {
        v_color = vec3(bool(u_useDefaultColor) ? u_color : a_color.xyz);
    }
    v_opacity = use_intensity ? a_intensity : u_opacity;

    vec4 viewPosition = worldView * u_transform * vec4(position, 1.0);
    gl_PointSize = 25.0 * u_size / abs(viewPosition.z);
    gl_Position = worldViewProjection * u_transform * vec4(position, 1.0);
}
`;
const fragmentSource = `
precision highp float;

uniform int u_rounded;

varying vec3 v_color;
varying float v_opacity;

void main() {
    float dist = distance(gl_PointCoord, vec2(0.5, 0.5));

    if (bool(u_rounded) && dist > 0.5) {
        discard;
    }

    gl_FragColor = vec4(v_color, v_opacity);
}
`;
export class PointCloud {
    constructor(scene, data, options, matrix) {
        this.material = new ShaderMaterial('pc', scene, { vertexSource, fragmentSource }, {
            attributes: ['position', 'a_color', 'a_intensity'],
            uniforms: [
                'worldView',
                'worldViewProjection',
                'u_color',
                'u_size',
                'u_opacity',
                'u_rounded',
                'u_useDefaultColor',
                'u_transform',
                'u_enable_intensity',
            ],
        });
        matrix = matrix || matrixFromDirection({ direction: { x: 1, y: 0, z: 0 } });
        this.material.setArray3('u_color', (options === null || options === void 0 ? void 0 : options.defaultColor) || [1, 1, 1]);
        this.material.setFloat('u_size', (options === null || options === void 0 ? void 0 : options.size) || 1);
        this.material.setFloat('u_enable_intensity', (options === null || options === void 0 ? void 0 : options.enableIntensity) ? 1 : 0);
        this.material.setInt('u_rounded', (options === null || options === void 0 ? void 0 : options.rounded) ? 1 : 0);
        this.material.setInt('u_useDefaultColor', 1);
        this.material.setFloat('u_opacity', Math.min((options === null || options === void 0 ? void 0 : options.opacity) || 1, 1));
        this.material.setMatrix('u_transform', matrix);
        this.material.fillMode = Material.PointFillMode;
        this.mesh = new Mesh('pc', scene);
        this.mesh.material = this.material;
        this.mesh.isUnIndexed = true;
        this.mesh.isPickable = false;
        this.mesh.hasVertexAlpha = true;
        this.mesh.setBoundingInfo(new BoundingInfo(new Vector3(-Number.MAX_SAFE_INTEGER, -Number.MAX_SAFE_INTEGER, -Number.MAX_SAFE_INTEGER), new Vector3(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)));
        this.mesh.doNotSyncBoundingInfo = true;
        const engine = scene.getEngine();
        if (data.color) {
            this.material.setInt('u_useDefaultColor', 0);
            const colorData = new Float32Array(data.color.value.length);
            data.color.value.forEach((value, index) => {
                colorData[index] = value / 255;
            });
            const colorBuffer = new VertexBuffer(engine, colorData, 'a_color', false, false, data.color.size);
            this.mesh.setVerticesBuffer(colorBuffer);
        }
        if (data.intensity) {
            const buffer = new Float32Array(data.intensity.value.map((i) => Math.min(1, i / 100)));
            const intensityBuffer = new VertexBuffer(engine, buffer, 'a_intensity', false, false, 1);
            this.mesh.setVerticesBuffer(intensityBuffer);
        }
        const positionBuffer = new VertexBuffer(engine, new Float32Array(data.position.value), VertexBuffer.PositionKind, false);
        this.mesh.setVerticesBuffer(positionBuffer);
    }
    get isVisible() {
        return this.mesh.isVisible;
    }
    setVisible(value) {
        this.mesh.isVisible = value;
    }
    setSize(value) {
        this.material.setFloat('u_size', value || 1);
    }
    setOpacity(value) {
        this.material.setFloat('u_opacity', value || 1);
    }
    setColor(value) {
        this.material.setArray3('u_color', value || [1, 1, 1]);
    }
    setEnableIntensity(value) {
        this.material.setFloat('u_enable_intensity', value ? 1 : 0);
    }
    dispose() {
        this.mesh.dispose();
    }
}
