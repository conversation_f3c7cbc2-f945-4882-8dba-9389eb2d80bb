<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      draggable
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row :gutter="5">
        <el-col :span="12" v-if="form.code">
          <el-form-item :label="$t('编号')" prop="code">
            <el-tag>{{ form.code }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="form.code ? 12 : 24">
          <el-form-item :label="$t('任务')" prop="taskCode" v-if="taskReadonly">
            <el-tag>{{ form.taskCode }}</el-tag>
          </el-form-item>
          <el-form-item :label="$t('任务')" prop="taskCode" v-else>
            <el-select
                v-model="form.taskCode"
                filterable
                clearable
                :placeholder="$t('请选择')"
                @clear="form.taskCode = undefined"
                popper-class="selector-input"
                :disabled="formReadonly"
            >
              <el-option
                  v-for="item in taskList"
                  :key="item.code"
                  :label="item.code"
                  :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('采集类型')" id="acquisitionType" prop="acquisitionType">
            <el-select
                :disabled="formReadonly"
                v-model="form.acquisitionType"
                filterable
                clearable
                :placeholder="$t('请选择')"
                @clear="form.acquisitionType = undefined"
                popper-class="selector-input"
                @change="handleAcquisitionTypeChange"
            >
              <el-option
                  v-for="item in acquisitionTypeList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车辆')" prop="vin">
            <bs-vehicle-selection
                modelCode="vin"
                :disabled="formReadonly"
                v-model="form.vin"
                ref="formDataVehicleSelectionRef"
                :clearable="true"
                filterable
                @clear="form.vin = undefined"
                :teleported="false"
            ></bs-vehicle-selection>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('区域')" id="cantonCode" prop="cantonCode">
            <el-cascader
                id="cantonCode"
                clearable
                filterable
                popper-class="canton-list"
                v-model="form.cantonCode"
                :options="cantonCodeList"
                :props="props"
                :disabled="formReadonly"
                @change="handleCantonChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.acquisitionType === 'parking'">
          <el-form-item :label="$t('停车场')" prop="locationId">
            <el-select-v2
                v-model="form.locationId"
                filterable
                clearable
                :options="parkingList"
                @change="changeParkingList"
                :disabled="formReadonly"
                @clear="form.locationId = undefined"
                @focus="handleFocus"
            />
            <!--            <el-select-->
            <!--              :disabled="formReadonly"-->
            <!--              v-model="form.locationId"-->
            <!--              filterable-->
            <!--              clearable-->
            <!--              :placeholder="$t('请选择')"-->
            <!--              @clear="form.locationId = undefined"-->
            <!--              popper-class="selector-input"-->
            <!--            >-->
            <!--              <el-option v-for="item in parkingList" :key="item.id" :label="item.name" :value="item.id"></el-option>-->
            <!--            </el-select>-->
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.acquisitionType === 'driving'">
          <el-form-item :label="$t('地点类型')" id="acquired" prop="locationType">
            <el-radio-group v-model="form.locationType" :disabled="formReadonly">
              <el-radio :value="$t('intersection')" :label="$t('intersection')">{{ $t('路口') }}</el-radio>
              <el-radio :value="$t('route')" :label="$t('route')">{{ $t('路线') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.acquisitionType === 'driving'">
          <el-form-item :label="$t('路线')" prop="locationName" v-if="form.locationType==='route'">
            <ltw-input v-model="form.locationName" :disabled="formReadonly" clearable id="locationName"/>
          </el-form-item>
          <el-form-item :label="$t('路口')" prop="locationId" v-if="form.locationType==='intersection'">
            <el-select
                :disabled="formReadonly"
                v-model="form.locationId"
                filterable
                clearable
                :placeholder="$t('请选择')"
                @clear="form.locationId = undefined"
                popper-class="selector-input"
                @focus="handleFocus"
                @change="handleIntersectionChange"
            >
              <el-option v-for="item in intersectionList" :key="item.id" :label="item.name" :value="item.id">
                {{ item.name }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('是否采集')" id="acquired" prop="acquired">
            <el-switch
                v-model="form.acquired"
                :disabled="formReadonly"
                inline-prompt
                :active-text="$t('是')"
                :inactive-text="$t('否')"
                id="acquired"
                style="--el-switch-on-color: #13ce66"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('合规官')" prop="complianceOfficerEmpId">
            <el-select
                :disabled="formReadonly"
                v-model="form.complianceOfficerEmpId"
                filterable
                clearable
                :placeholder="$t('请选择')"
                @clear="form.complianceOfficerEmpId = undefined"
                popper-class="selector-input"
            >
              <el-option
                  v-for="item in complianceOfficerList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              ></el-option>
            </el-select>
            <!-- :value="`${item.id},${item.name}`" -->
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12" v-if="dialogStatus === 'view'">
          <el-form-item :label="$t('标签数量')" prop="tagRecordCount">
            <el-input-number
                :disabled="formReadonly"
                v-model="form.tagRecordCount"
                :min="0"
                clearable
                @clear="form.tagRecordCount = 0"
                prop="tagRecordCount"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dialogStatus === 'view'">
          <el-form-item class="form-item-line" :label="$t('数据大小')" prop="dataSize">
            <ltw-input
                id="name"
                v-model="form.dataSize"
                :disabled="formReadonly"
                type="number"
                class="data-size"
                :min="0"
            >
              <template #append>
                <el-select
                    :disabled="formReadonly"
                    v-model="form.dataSizeUnit"
                    placeholder="Select"
                    style="width: 60px"
                    popper-class="selector-input"
                >
                  <el-option v-for="item in unitList" :label="item.name" :value="item.code" :key="item.code"/>
                </el-select>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dialogStatus === 'view'">
          <el-form-item :label="$t('数据量')" prop="dataAmount">
            <el-input-number
                :disabled="formReadonly"
                v-model="form.dataAmount"
                :min="0"
                clearable
                @clear="form.dataAmount = 0"
                prop="dataAmount"
            />
          </el-form-item>
        </el-col> -->
        <el-col>
          <el-form-item :label="$t('执行日期')" prop="executingDate">
            <el-date-picker
                :disabled="formReadonly"
                :placeholder="$t('请选择')"
                v-model="form.executingDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
                @clear="form.executingDate = ''"
            />
          </el-form-item>
        </el-col>
        <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('开始时间')" prop="startTime">
            <el-time-picker
                :disabled="formReadonly"
                v-model="form.startTime"
                :placeholder="$t('请选择开始时间')"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('结束时间')" prop="endTime">
            <el-time-picker
                :disabled="formReadonly"
                v-model="form.endTime"
                :placeholder="$t('请选择结束时间')"
                clearable
            />
          </el-form-item>
        </el-col>
        </el-row>
      </el-row>
      <el-form-item :label="$t('描述')" prop="remark">
        <ltw-input
            v-model="form.remark"
            :disabled="formReadonly"
            textType="remark"
            type="textarea"
            id="remark"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button id="close" @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{
              $t('关闭')
            }}</el-button>
        </template>
        <template v-else>
          <el-button id="cancel" @click="dialogVisible = false">{{ $t('取消') }}</el-button>
          <el-button id="save" type="primary" @click="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  getDAQTaskRecords,
  updateDAQTaskRecords,
  saveDAQTaskRecords
} from '@/apis/data-collect/acquisition-task-record'
// import {listFtmParkingLot} from '@/apis/fleet/ftm-parking-lot'
import { listMdParkingLotsSimple } from '@/apis/fleet/parking-lot-management'
import {listDaqReqDetailSelection, getIntersectionList} from '@/apis/data-collect/vt-daq-task'
import {listSysDictionary} from '@/apis/system/sys-dictionary'
import {
  // getDriverList,
  listSysRoleEmployee
} from '@/apis/system/sys-role-employee'
import {SYS_ORG_INNER_CODE} from '@/plugins/constants/data-dictionary'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import util, {dateUtils, showToast} from '@/plugins/util'
import {getSysCantonTree} from '@/apis/system/sys-canton'

const defaultform = {
  dataSizeUnit: 'b'
}
export default {
  name: 'AddCollectRecordDialog',
  props: {
    taskReadonly: {
      type: Boolean,
      default: true
    }
  },
  emits: ['reload'],
  components: {
    BsVehicleSelection
  },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        taskCode: [
          {required: true, message: this.$t('请选择'), trigger: 'change'}
        ],
        acquisitionType: [{required: true, message: this.$t('请选择'), trigger: 'change'}],
        locationId: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        cantonCode: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        locationName: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        locationType: [
          {required: true, message: this.$t('请输入'), trigger: 'blur'}
        ],
        executingDate: [{required: true, message: this.$t('请输入'), trigger: 'blur'}],
        startTime: [
          {required: true, message: this.$t('请选择开始时间'), trigger: 'change'},
          {validator: this.validateStartTime, trigger: 'change'}
        ],
        endTime: [
          {required: true, message: this.$t('请选择结束时间'), trigger: 'change'},
          {validator: this.validateEndTime, trigger: 'change'}
        ],
        complianceOfficerEmpId: [{required: true, message: this.$t('请选择'), trigger: 'change'}],
        copilotEmpId: [{required: true, message: this.$t('请选择'), trigger: 'change'}],
        driverEmpId: [{required: true, message: this.$t('请选择'), trigger: 'change'}],
        vin: [{required: true, message: this.$t('请选择'), trigger: 'change'}]
      },
      unitList: [
        {
          code: 'b',
          name: 'B'
        },
        {
          code: 'kb',
          name: 'KB'
        },
        {
          code: 'm',
          name: 'M'
        },
        {
          code: 'g',
          name: 'G'
        },
        {
          code: 't',
          name: 'T'
        }
      ],
      parkingList: [],
      taskList: [],
      complianceOfficerList: [],
      parseTime: dateUtils.parseTime,
      cantonCodeList: [],
      acquisitionModeList: [],
      acquisitionTypeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click',
        emitPath: true
      },
      groundLevelList: [],
      intersectionList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  watch: {
    'form.complianceOfficerEmpId'(val) {
      if (val) {
        let item = this.complianceOfficerList.find(value => value.id === val)
        if (item?.name) {
          this.form.complianceOfficerEmpName = item.name
        }
      } else {
        this.form.complianceOfficerEmpName = undefined
      }
    },
    'form.cantonCode'(newValue) {
      if (this.form.acquisitionType === 'driving') {
        this.intersectionList = []
        if (newValue) {
          if (Array.isArray(newValue)) {
            this.listIntersection(newValue[newValue?.length - 1])
          } else {
            this.listIntersection(newValue)
          }
        }
      } else if(this.form.acquisitionType === 'parking'){
          this.parkingList=[]
        if (newValue) {
          if (Array.isArray(newValue)) {
            this.getParkingList(newValue[newValue?.length - 1])
          } else {
            this.getParkingList(newValue)
          }
        }
      }
    }
  },

  methods: {
    // 验证开始时间
    validateStartTime(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      if (this.form.endTime && value >= this.form.endTime) {
        callback(new Error(this.$t('开始时间必须小于结束时间')))
      } else {
        // 如果结束时间已经填写，重新验证结束时间
        if (this.form.endTime) {
          this.$refs.formRef.validateField('endTime')
        }
        callback()
      }
    },

    // 验证结束时间
    validateEndTime(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      if (this.form.startTime && value <= this.form.startTime) {
        callback(new Error(this.$t('结束时间必须大于开始时间')))
      } else {
          if (this.form.startTime) {
          this.$refs.formRef.validateField('startTime')
        }
        callback()
      }
    },

    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      if (row?.data?.taskCode) {
        this.form.taskCode = row.data.taskCode
        this.form.locationId = row.data.locationId
        this.form.locationName = row.data.locationName
        if (this.dialogStatus === 'view') {
          this.form.tagRecordCount = row.data.tagRecordCount
        }
        this.form.executingDate = row.data.startTime
        this.form.complianceOfficerEmpId = row.data.complianceOfficerEmpId
        this.form.complianceOfficerEmpName = row.data.complianceOfficerEmpName
        this.form.copilotEmpId = row.data.copilotEmpId
        this.form.copilot = row.data.copilot
        this.form.driverEmpId = row.data.driverEmpId
        this.form.driver = row.data.driver
        this.form.vin = row.data.vin
        // 需求新增从task带过来
        this.form.rawDataBagType = row.data.rawDataBagType
        this.form.acquisitionModeList = row.data.acquisitionMode && row.data.acquisitionMode
        this.form.acquisitionType = row.data.acquisitionType
      }
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增采集任务记录')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑采集任务记录')
          this.getTaskRecordDetail(row.data.id)
          break
        case 'view':
          this.dialogTitle = this.$t('采集任务记录详情')
          this.getTaskRecordDetail(row.data.id)
          break
      }
      if (!this.taskReadonly) {
        this.listTask()
      }
      if(this.form.acquired===undefined) {
        this.form.acquired=true
      }
      this.listComplianceOfficer()
      this.getSysCantonTree()
      this.getacquisitionTypeList()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.firstListEquipment = true
      this.firstListBsVehicle = true
      this.$refs.formRef.resetFields()
      this.form = {...defaultform}
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {...this.form}
        if (Array.prototype.isPrototypeOf(this.form.cantonCode)) {
          postData.cantonCode = this.form.cantonCode[this.form.cantonCode?.length - 1]
        } else {
          postData.cantonCode = this.form.cantonCode
        }
        postData.executingDate = this.parseTime(postData.executingDate, '{y}-{m}-{d}')
        postData.startTime = postData.executingDate + ' ' + this.parseTime(postData.startTime, '{h}:{i}:{s}')
        postData.endTime = postData.executingDate + ' ' + this.parseTime(postData.endTime, '{h}:{i}:{s}')
        postData.dataSize = this.transformToB(this.form, postData.dataSize, 'dataSize')
        postData.locationType = postData.acquisitionType === 'parking' ? 'parking_lot' : postData.locationType
        if (!this.form.id) {
          postData.sourceType = 'web'
          saveDAQTaskRecords(postData).then(() => {
            this.cancel()
          })
        } else {
          updateDAQTaskRecords(postData).then(() => {
            this.cancel()
          })
        }
      })
    },
    // getParkingType(code) {
    //   if (!this.locationTypeList?.length) {
    //     listSysDictionary({
    //       typeCode: 'location_type'
    //     }).then(res => {
    //       this.locationTypeList = res.data
    //       // if (!code && this.dialogStatus === 'add') {
    //       //   this.form.locationType = res.data[0].code
    //       // }
    //     })
    //   }
    // },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    getacquisitionTypeList() {
      if (!this.acquisitionTypeList?.length) {
        listSysDictionary({
          typeCode: 'data_acquisition_type'
        }).then(res => {
          this.acquisitionTypeList = res.data
        })
      }
    },
    handleAcquisitionTypeChange(val) {
      delete this.form.locationId
      delete this.form.locationName
      if (val === 'driving') {
        this.parkingList=[]
        this.form.locationType = 'intersection'
        if(this.form.cantonCode?.length){
          if (Array.isArray(this.form.cantonCode)) {
            this.listIntersection(this.form.cantonCode[this.form.cantonCode?.length - 1])
          } else {
            this.listIntersection(this.form.cantonCode)
          }
        }
      }
      if(val==='parking'){
        this.intersectionList=[]
        if(this.form.cantonCode?.length){
          if (Array.isArray(this.form.cantonCode)) {
            this.getParkingList(this.form.cantonCode[this.form.cantonCode?.length - 1])
          } else {
            this.getParkingList(this.form.cantonCode)
          }
        }
      }
    },
    handleFocus() {
      if (!this.form.cantonCode) showToast('请先选择行政区', 'warning')
    },
    handleCantonChange(val) {
      delete this.form.locationId
    },
    listIntersection(param) {
      getIntersectionList({crs: 1, cantonCode: param}).then(res => {
        this.intersectionList = res.data
      })
    },
    copilotChange(val) {
      let [id, name] = val.split(',')
      this.form.copilotEmpId = id
      this.form.copilot = name
    },
    driverChange(val) {
      let [id, name] = val.split(',')
      this.form.driverEmpId = id
      this.form.driver = name
    },
    getTaskRecordDetail(id) {
      getDAQTaskRecords(id).then(res => {
        // res.data.groundLevel &&
        //   (res.data.groundLevel = JSON.parse(res.data.groundLevel))
        // 处理采集时间回显，直接转换为 Date 对象
        if (res.data?.startTime) {
          res.data.startTime = new Date(res.data.startTime)
        }
        if (res.data?.endTime) {
          res.data.endTime = new Date(res.data.endTime)
        }
        res.data.groundLevelList = util.bitOperators(res.data.groundLevel)
        res.data.acquisitionModeList = res.data.acquisitionMode && util.bitOperators(res.data.acquisitionMode)
        this.form = res.data
        this.form.dataSize = this.transformToUnit(this.form, res.data.dataSize, 'dataSize')
      })
    },
    async getParkingList(cantonCode) {
      await listMdParkingLotsSimple({cantonCode: cantonCode}).then(res => {
        res.data.forEach(val => {
          val.label = val.name
          val.value = val.id
        })
        this.parkingList = res.data
      })
    },
    listTask() {
      if (!this.taskList?.length) {
        listDaqReqDetailSelection().then(res => {
          this.taskList = res.data
        })
      }
    },
    // getDriverList() {
    //   if (!this.driverList?.length) {
    //     getDriverList().then(res => {
    //       this.driverList = res.data
    //     })
    //   }
    // },
    listComplianceOfficer() {
      if (!this.complianceOfficerList?.length) {
        listSysRoleEmployee({
          orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
        }).then(res => {
          this.complianceOfficerList = res.data
        })
      }
    },
    transformToUnit(data, value, name) {
      if (value > 0 && value < Math.pow(2, 10)) {
        data[name + 'Unit'] = 'b'
        return value || 0
      } else if (value >= Math.pow(2, 10) && value < Math.pow(2, 20)) {
        data[name + 'Unit'] = 'kb'

        return parseFloat(parseFloat(value / Math.pow(2, 10)).toFixed(2))
      } else if (value >= Math.pow(2, 20) && value < Math.pow(2, 30)) {
        data[name + 'Unit'] = 'm'

        return parseFloat(parseFloat(value / Math.pow(2, 20)).toFixed(2))
      } else if (value >= Math.pow(2, 30) && value < Math.pow(2, 40)) {
        data[name + 'Unit'] = 'g'

        return parseFloat(parseFloat(value / Math.pow(2, 30)).toFixed(2))
      } else if (value >= Math.pow(2, 40)) {
        data[name + 'Unit'] = 't'
        return parseFloat(parseFloat(value / Math.pow(2, 40)).toFixed(2))
      } else {
        data[name + 'Unit'] = 'b'
        return 0
      }
    },
    transformToB(data, value, name) {
      if (data[name + 'Unit'] !== 'b') {
        switch (data[name + 'Unit']) {
          case 'kb':
            value *= Math.pow(2, 10)
            break
          case 'm':
            value *= Math.pow(2, 20)
            break
          case 'g':
            value *= Math.pow(2, 30)
            break
          case 't':
            value *= Math.pow(2, 40)
            break
        }
      }
      return value
    },
    changeParkingList(val) {
      let item = this.parkingList.find(value => value.id === val)
      item?.name && (this.form.locationName = item?.name)
    },
    handleIntersectionChange(val){
      let item = this.intersectionList.find(value=>value.id === val)
      item?.name && (this.form.locationName = item?.name)
    }
  }
}
</script>

<style>
.canton-list .el-cascader-panel {
  height: 50vh;
}
</style>
