<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    draggable
  >
    <div class="tag-list">
      <el-card class="group-card" v-for="(req, reqIndex) in groupList">
        <template #header>
          <div class="bs-tag-group-card-header">
            <span>{{ req.name }}</span>
          </div>
        </template>
        <div class="tag-body">
          <el-card v-for="(group, groupIndex) in req.children">
            <template #header>
              <div class="bs-tag-group-card-header">
                <span>{{ group.name }}</span>
              </div>
            </template>
            <div class="tag-body">
              <el-card class="tag-item" v-for="(item, index) in group.tagList" :key="item.id">
                <template #header>
                  <div class="header">
                    <div class="header-item">
                      <el-tag class="header-name" :type="checkTagType(item) ? '' : 'info'">
                        {{ item.name || item.tagName }}
                      </el-tag>
                      <slot name="item" v-bind:item="item" />
                    </div>
                    <div class="opt" v-if="!formReadonly">
                      <el-link v-if="item.disabled" type="primary" :underline="false" @click="tagEdit(item)"
                        >编辑
                      </el-link>
                      <el-link
                        v-if="!item.disabled"
                        type="info"
                        :underline="false"
                        @click="tagCancel(item, reqIndex, groupIndex, index)"
                        >取消
                      </el-link>
                      <el-link
                        class="confirm-btn"
                        v-if="!item.disabled"
                        type="success"
                        :underline="false"
                        @click="tagConfirm(item, reqIndex, groupIndex, index)"
                        >确认
                      </el-link>
                    </div>
                  </div>
                </template>
                <div class="tag-body">
                  <el-carousel
                    v-if="item.tagSamples?.length"
                    trigger="click"
                    :autoplay="!!item.disabled"
                    height="200px"
                  >
                    <el-carousel-item v-for="(sample, sampleIndex) in item.tagSamples" :key="sample.id">
                      <div class="img">
                        <el-image
                          :src="sample.filePath ? downloadUrl + token + '&path=' + sample.filePath : ''"
                          fit="contain"
                          @click="previewImg(sample)"
                        >
                          <template #error>
                            <div class="image-slot">
                              <ltw-icon icon-code="el-icon-picture"></ltw-icon>
                            </div>
                          </template>
                        </el-image>
                        <el-button
                          v-if="!item.disabled && !formReadonly"
                          class="del-btn"
                          circle
                          @click="delSample(item, sampleIndex)"
                        >
                          <ltw-icon icon-code="el-icon-close"></ltw-icon>
                        </el-button>
                        <div class="sample-description">
                          <ltw-input
                            :autosize="false"
                            v-if="!(item.disabled || formReadonly)"
                            class="sample-input"
                            type="textarea"
                            textType="remark"
                            v-model="sample.description"
                            id="remark"
                          ></ltw-input>
                          <div v-else class="text">
                            <el-tooltip effect="dark" :content="sample.description"
                              >{{ sample.description }}
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </el-carousel-item>
                  </el-carousel>
                  <div class="tag-form-item">
                    <div class="form-label">标签描述</div>
                    <div class="form-value">
                      <ltw-input
                        type="textarea"
                        v-model="item.description"
                        :disabled="item.disabled || formReadonly"
                        id="remark"
                      ></ltw-input>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-card>
        </div>
      </el-card>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="primary" v-if="formReadonly" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
    <el-image-viewer v-if="imgDialogVisible" @close="closeViewer" :url-list="srcList" />
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import util, { showConfirmToast } from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'

export default {
  name: 'TagSamplesDialog',
  emits: ['reload'],
  components: { UploadFile },
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/preview',
      token: '?token=' + util.getToken(),
      groupList: [],
      currentTag: {},
      imgDialogVisible: false,
      srcList: [],
      originalGroupList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'onlyView'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.dialogTitle = this.$t('标签示例图')
      let groupList = JSON.parse(JSON.stringify(row.data.groupList))
      switch (row.type) {
        case 'edit':
          break
        case 'view':
          groupList.forEach(group => {
            group.children.forEach(tagGroup => {
              tagGroup.tagList.forEach(tag => {
                tag.disabled = true
              })
            })
          })
          break
      }
      this.groupList = groupList
      this.originalGroupList = JSON.parse(JSON.stringify(groupList))
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.groupList = []
      this.currentTag = {}
    },
    submit() {
      this.cancel({ groupList: this.groupList })
    },
    tagEdit(item) {
      this.currentTag = JSON.parse(JSON.stringify(item))
      item.disabled = false
    },
    tagCancel(item, reqIndex, groupIndex, index) {
      this.groupList[reqIndex].children[groupIndex].tagList[index] = JSON.parse(
        JSON.stringify(this.originalGroupList[reqIndex].children[groupIndex].tagList[index])
      )
    },
    tagConfirm(item, reqIndex, groupIndex, index) {
      this.currentTag = {}
      this.originalGroupList[reqIndex].children[groupIndex].tagList[index] = JSON.parse(JSON.stringify(item))
      item.disabled = true
    },
    delSample(tag, index) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        tag.tagSamples.splice(index, 1)
      })
    },
    checkTagType(tag) {
      if (tag.type === 'continuous' || tag.tagType === 'continuous') {
        return 'warning'
      } else if (tag.type === 'transient' || tag.tagType === 'transient') {
        return ''
      }
      return 'info'
    },
    previewImg(item) {
      if (item.filePath) {
        let url = this.downloadUrl + this.token + '&path=' + item.filePath
        this.srcList = [url]
        this.imgDialogVisible = true
      }
    },
    closeViewer() {
      this.srcList = []
      this.imgDialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.tag-list {
  //display: flex;
  //flex-wrap: wrap;
  .group-card {
    margin-bottom: 10px;
  }

  .tag-item {
    //&:nth-child(odd) {
    //  margin-left: 10px;
    //}
    //
    //&:nth-child(even) {
    //  margin-right: 10px;
    //}

    //width: calc(50% - 10px);
    margin-bottom: 20px;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-item {
        display: flex;
        align-items: center;

        .header-name {
          margin-right: 10px;
        }
      }

      .opt {
        display: flex;

        .confirm-btn {
          margin-left: 10px;
        }
      }
    }

    .img {
      height: 100%;
      width: 100%;
      position: relative;
      display: flex;
      flex-direction: column;

      :deep(.el-image__wrapper) {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-image {
        height: 150px;
        //height: 100%;
        width: 100%;
        position: relative;
        cursor: pointer;
      }

      .del-btn {
        position: absolute;
        right: 0;
        top: 0;
      }

      .sample-description {
        width: 100%;
        height: 48px;
        margin-top: 2px;

        .sample-input {
          width: 100%;
          height: 100%;

          :deep(.el-textarea__inner) {
            height: 100%;
          }
        }

        .text {
          padding: 4px 10px;
          font-size: 12px;
          color: #8a9097;
          line-height: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
        }
      }
    }

    .tag-form-item {
      margin-top: 20px;
      display: flex;

      .form-label {
        width: 100px;
      }

      .form-value {
        width: calc(100% - 100px);
      }
    }
  }
}
</style>
