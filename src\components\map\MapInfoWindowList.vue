<template>
  <div class="map-info-window">
    <div class="map-info-list">
      <div class="map-info-item" v-for="(val, index) in item" :key="index">
        <div class="window-title">
          <div class="location" v-text="val.locationName"></div>
          <div class="title-desc">
            <div
              class="desc-item blue"
              v-text="
                parseFloat(
                  parseFloat(val.acquireDuration / 60 / 60).toFixed(1)
                ) + 'H'
              "
            ></div>
            <div
              class="desc-item yellow"
              v-text="val.acquisitionMileage + 'km'"
            ></div>
          </div>
        </div>
        <div class="time">
          {{
            parseTime(new Date(val.startTime), '{h}:{i}:{s}') +
            $t('至') +
            parseTime(new Date(val.endTime), '{h}:{i}:{s}')
          }}
        </div>
        <div class="window-content">
          <div class="item">
            <div class="item-label">标签：</div>
            <div class="item-value">
              <span class="num">{{ val.transientTagCount }}</span>
              <span style="margin-right: 10px">次</span>
              <span class="num">{{
                parseFloat(
                  parseFloat(val.continuousTagDuration / 60).toFixed(1)
                )
              }}</span>
              <span>min</span>
            </div>
          </div>
          <div class="item">
            <div class="item-label">数据：</div>
            <div class="num item-value">{{ checkFileSize(val.dataSize) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { dateUtils, checkFileSize } from '@/plugins/util'
export default {
  name: 'MapInfoWindow',
  data() {
    return {
      $t: i18n.global.t,
      row: {},
      parseTime: dateUtils.parseTime,
      checkFileSize
    }
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    item: {
      handler(list) {
        // list.forEach(val => {
        //   // val.createTime = dateUtils.parseTime(
        //   //   new Date(val.createTime).getTime(),
        //   //   '{h}:{i}:{s}'
        //   // )
        //   // val.endTime = dateUtils.parseTime(
        //   //   new Date(val.endTime).getTime(),
        //   //   '{h}:{i}:{s}'
        //   // )
        //   // 采集时长
        //   // val.continuousTagDuration = parseFloat(
        //   //   parseFloat(val.continuousTagDuration / 60).toFixed(1)
        //   // )
        //   // // 采集时长
        //   // val.acquireDuration = parseFloat(
        //   //   parseFloat(val.acquireDuration / 60 / 60).toFixed(1)
        //   // )
        //   // 采集数据
        //   // val.dataSize = checkFileSize(val.dataSize)
        // })
        this.row = list
      },
      deep: true,
      immediate: true
    }
  },
  components: {},
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
@font-face {
  font-family: 'DOU YU';
  src: url('@/assets/fonts/douyuFont-2.otf');
}
.map-info-window {
  .map-info-item {
    background: url('@/assets/images/FleetListScreen/info-bg.png');
    background-size: 100% 100%;
    height: 140px;
    width: 219px;
    padding: 20px 20px 20px 30px;
    .window-title {
      font-size: 16px;
      font-weight: 400;
      color: #28e7ff;
      line-height: 24px;
      display: flex;
      justify-content: space-between;
      .location {
        color: #28e7ff;
        line-height: 24px;
        font-size: 16px;
      }
      .title-desc {
        display: flex;
        .desc-item {
          border-top: 1px solid #4bbefa;
          border-bottom: 1px solid #4bbefa;
          margin-right: 10px;
          font-size: 14px;
          font-weight: 400;
          color: #4ad2ff;
          &.yellow {
            color: rgba(255, 241, 0, 1);
            border-top: 1px solid rgba(255, 241, 0, 1);
            border-bottom: 1px solid rgba(255, 241, 0, 1);
          }
        }
      }
    }
    .time {
      color: #edf6ff;
      margin-bottom: 10px;
    }
    .window-content {
      .item {
        color: #edf6ff;
        line-height: 22px;
        font-size: 14px;
        display: flex;
        .num {
          font-family: 'DOU YU';
        }
      }
    }
  }
}
</style>
