<template>
  <el-drawer
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="formReadonly"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="modality-drawer"
  >
    <el-table :data="pageData" @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
      <!-- <el-table-column type="expand" fixed="left">
        <template #default="props">
          <el-form class="expand-form" ref="formRef" label-width="140px">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item :label="$t('分辨率')">
                  {{ props.row.cameraVO?.resolution }}
                </el-form-item></el-col
              >
              <el-col :span="8"
                ><el-form-item :label="$t('畸变模型')">
                  {{ props.row.cameraVO?.distortionModel }}
                </el-form-item></el-col
              >
              <el-col :span="8"
                ><el-form-item :label="$t('相邻两行曝光间隔')">
                  {{ props.row.cameraVO?.exposureInterval }}us
                </el-form-item></el-col
              >
              <el-col :span="8">
                <el-form-item :label="$t('水平视角')">
                  {{ props.row.cameraVO?.hfov }}
                </el-form-item></el-col
              >
              <el-col :span="8"
                ><el-form-item :label="$t('垂直视角')">
                  {{ props.row.cameraVO?.vfov }}
                </el-form-item></el-col
              >
              <el-col :span="8"
                ><el-form-item :label="$t('备注')">
                  {{ props.row.cameraVO?.remark }}
                </el-form-item></el-col
              >
            </el-row>
          </el-form>
        </template>
      </el-table-column> -->
      <!-- <el-table-column type="index" width="50" fixed="left" /> -->
      <el-table-column
        header-align="left"
        align="left"
        type="selection"
        width="55"
        reserve-selection
        fixed="left"
      ></el-table-column>
      <el-table-column type="expand" fixed="left">
        <template #default="props">
          <el-form class="expand-form" ref="formRef" label-width="140px">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item :label="$t('分辨率')">
                  <el-tag type="primary">{{ props.row.cameraVO?.resolution }} </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('畸变模型')">
                  <el-tag type="primary">{{ props.row.cameraVO?.distortionModel }} </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('相邻两行曝光间隔')">
                  <el-tag type="primary">{{ props.row.cameraVO?.exposureInterval }}us </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('水平视角')">
                  <el-tag type="primary">{{ props.row.cameraVO?.hfov }}° </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('垂直视角')">
                  <el-tag type="primary">{{ props.row.cameraVO?.vfov }}° </el-tag>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="8"
                ><el-form-item :label="$t('备注')">
                  <el-tag type="primary">{{
                    props.row.cameraVO?.remark
                  }}</el-tag>
                </el-form-item></el-col
              > -->
            </el-row>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('编码')"
        prop="modality"
        show-overflow-tooltip
        width="150"
        fixed="left"
      >
        <template #default="scope">
          <!-- <el-link
            type="primary"
            @click="viewModality(scope.row)"
            :underline="false"
            >{{ scope.row.modality }}</el-link
          > -->
          <el-tag>{{ scope.row.modality }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('传感器')"
        show-overflow-tooltip
        width="130"
        prop="modalityName"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('传感器类型')"
        prop="sensorTypeName"
      ></el-table-column>
      <el-table-column header-align="left" align="left" :label="$t('供应商')" prop="supplierName" />
      <el-table-column header-align="left" align="left" :label="$t('型号')" prop="model" />
      <el-table-column header-align="left" align="left" :label="$t('规格')" prop="specification" />
      <!-- <el-table-column
        header-align="left"
        align="left"
        :label="$t('安装') + $t('位置')"
        prop="installPositionName"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('指导位置x') + '(mm)'"
        prop="positionX"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('指导位置y') + '(mm)'"
        prop="positionY"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('指导位置z') + '(mm)'"
        prop="positionZ"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('距地高度') + '(mm)'"
        prop="height"
      />
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('图片中坐标')"
        prop="imageCoordinate"
      /> -->
      <el-table-column header-align="left" align="left" :label="$t('msop')" prop="msop" />
      <el-table-column header-align="left" align="left" :label="$t('difop')" prop="difop" />
      <el-table-column header-align="left" align="left" :label="$t('帧同步偏移值' + '(ms)')" prop="pcdJpgOffset" />
      <el-table-column header-align="left" align="left" :label="$t('间隔差的基准值') + '(ms)'" prop="intervalDif" />
      <el-table-column header-align="left" align="left" :label="$t('备注')" show-overflow-tooltip prop="remark" />
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button id="cancel" @click="dialogVisible = false">{{ $t('取消') }}</el-button>
        <el-button id="save" type="primary" @click="submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-drawer>
  <AddModality ref="AddModality"></AddModality>
</template>

<script>
import { listFtmVariantVersionAndVehicleMappingModalityDifferentSet } from '@/apis/fleet/ftm-vehicle-installation-record'
import AddModality from '@/pages/fleet/dialog/AddModality.vue'
import { saveFtmVehicleMappingModalitys } from '@/apis/fleet/ftm-variant-mapping-modality'

const defaultform = {}
export default {
  name: 'ChooseModality',
  emits: ['reload'],
  components: {
    AddModality
  },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      pageData: [],
      vehicleId: '',
      installationRecordId: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'view':
          this.dialogTitle = this.$t('选择传感器')
          this.vehicleId = row.data.vehicleId
          this.installationRecordId = row.data.installationRecordId
          this.listFtmVariantVersionAndVehicleMappingModalityDifferentSet(row.data)
          // this.form.sensorType = row.sensorType
          break
      }
    },
    listFtmVariantVersionAndVehicleMappingModalityDifferentSet(row) {
      listFtmVariantVersionAndVehicleMappingModalityDifferentSet(row).then(res => {
        this.pageData = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.tableRef.clearSelection()
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      let postData = {
        vehicleId: this.vehicleId,
        installationRecordId: this.installationRecordId,
        versionMappingModalityIdList: this.selectedData.map(val => val.id)
      }
      saveFtmVehicleMappingModalitys(postData).then(res => {
        this.cancel('reload')
      })
    },
    getFtmVehicleModality(row) {
      getFtmVehicleModality(row.id).then(res => {
        this.form = res.data
      })
    },
    viewModality(row) {
      this.$refs.AddModality.show({
        type: 'view',
        data: row
      })
    },
    handleSelectionChange(value) {
      this.selectedData = value
    }
  }
}
</script>

<style>
.modality-drawer > .el-drawer__header {
  margin-bottom: 0;
}
</style>
<style scoped lang="scss">
.expand-form {
  width: 800px;
}

// .codemirror-container{
//   min-height: 200px;
// }

:deep(.no-expand) .el-table__expand-column .cell {
  display: none;
}
</style>
