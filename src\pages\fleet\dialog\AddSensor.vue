<template>
  <el-dialog :title="dialogTitle" v-model="dialogVisible" width="700px" @close="dialogClosed" @open="dialogOpened">
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="140px" :hide-required-asterisk="formReadonly">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('传感器类型')" prop="sensorType">
            <dictionary-selection
                v-model="form.sensorType"
                :disabled="formReadonly"
                clearable
                dictionaryType="sensor_type"
                @change="changeSensorType"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <supplier-selection
                v-model="form.supplierId"
                :disabled="formReadonly"
                clearable
                @change="changeSupplierName"
            />
          </el-form-item>
        </el-col>
        <!-- 2023年8月2日改动、型号放规格前 -->
        <el-col :span="12">
          <el-form-item :label="$t('传感器型号')" prop="model">
            <ltw-input v-model="form.model" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('规格')" prop="specification">
            <ltw-input v-model="form.specification" :disabled="formReadonly"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="this.form.sensorType === 'camera'">
          <el-form-item :label="$t('分辨率')" prop="resolution">
            <el-row>
              <el-column :span="12">
                <ltw-input
                    class="resolution-input"
                    v-model="form.horizontalResolution"
                    :disabled="formReadonly"
                    id="horizontalResolution"
                >
                  <template #append>
                    <span>px</span>
                  </template>
                </ltw-input>
              </el-column>
              <el-column><span class="point">*</span></el-column>
              <el-column :span="12">
                <ltw-input
                    class="resolution-input"
                    v-model="form.verticalResolution"
                    :disabled="formReadonly"
                    id="verticalResolution"
                >
                  <template #append>
                    <span>px</span>
                  </template>
                </ltw-input>
              </el-column>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="this.form.sensorType === 'camera'">
          <el-form-item :label="$t('水平视角')" prop="hfov">
            <ltw-input v-model="form.hfov" :disabled="formReadonly" id="hfov">
              <template #append>
                <span>°</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="this.form.sensorType === 'camera'">
          <el-form-item :label="$t('垂直视角')" prop="vfov">
            <ltw-input v-model="form.vfov" :disabled="formReadonly" id="vfov">
              <template #append>
                <span>°</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="this.form.sensorType === 'camera'">
          <el-form-item :label="$t('畸变模型')" prop="distortionModel">
            <dictionary-selection
                v-model="form.distortionModel"
                clearable
                dictionaryType="dewarping"
                :disabled="formReadonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="this.form.sensorType === 'camera'">
          <el-form-item :label="$t('相邻两行曝光间隔')" prop="exposureInterval">
            <ltw-input v-model="form.exposureInterval" :disabled="formReadonly" id="positionX">
              <template #append>
                <span>μs</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('备注')">
            <ltw-input textType="remark" type="textarea" :disabled="formReadonly" v-model="form.remark"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="dialogStatus === 'view'">
          <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
          <el-button
              :type="item.buttonStyleType"
              @click="executeButtonMethod(currentButton)"
              v-if="currentButton && currentButton.name"
          >{{ $t(currentButton.name) }}</el-button
          >
        </template>
        <template v-else>
          <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
          <el-button type="primary" @click="submit">{{ $t('保 存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {getFtmSensor, saveFtmSensor, updateFtmSensor} from '@/apis/fleet/ftm-sensor'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import SupplierSelection from '@/components/system/SupplierSelection.vue'
import {
  isPositiveNum,
  validateTwoFloatValidity,  validateThreeDigitInteger, validateThirteenDigitInteger, validateSixDecimalValidity
} from '@/plugins/util'

const defaultform = {}
export default {
  name: 'AddSensor',
  emits: ['reload'],
  components: {DictionarySelection, SupplierSelection},
  data() {
    const checkResolution = (rule, value, callback) => {
      const re = /^(0(\.\d{1,3})?|[1-9]\d*(\.\d{1,3})?)$/
      let checkHorizontalResolution = true,
          checkVerticalResolution = true
      if (this.form.horizontalResolution) {
        checkHorizontalResolution = re.test(this.form.horizontalResolution)
      }
      if (this.form.verticalResolution) {
        checkVerticalResolution = re.test(this.form.verticalResolution)
      }
      if (checkHorizontalResolution && checkVerticalResolution) {
        callback()
      } else {
        callback(new Error(this.$t('请输入最多三位正小数')))
      }
    }
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        sensorType: [
          {
            required: true,
            message: this.$t('请输入传感器类型'),
            trigger: 'blur'
          }
        ],
        supplierName: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ],
        resolution: [{validator: checkResolution, trigger: 'blur'}],
        hfov: [{validator: validateTwoFloatValidity, trigger: 'blur'}, {
          validator: validateThreeDigitInteger,
          trigger: 'blur'
        }],
        vfov: [{validator: validateTwoFloatValidity, trigger: 'blur'},{
          validator: validateThreeDigitInteger,
          trigger: 'blur'
        }],
        exposureInterval: [
          {validator: validateThirteenDigitInteger, trigger: 'blur'},
          {validator: validateSixDecimalValidity, trigger: 'blur'},
          {
            validator: isPositiveNum,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增传感器实体')
          this.form.sensorType = row.sensorType
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑传感器实体')
          this.getFtmSensor(row)
          break
        case 'view':
          this.dialogTitle = this.$t('传感器实体详情')
          this.getFtmSensor(row)
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {
    },
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = {...defaultform}
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        //如果是非摄像头类型，把分辨率，水平垂直视角value置空
        if (this.form.sensorType !== 'camera') {
          this.form.resolution = null
          this.form.vfov = null
          this.form.hfov = null
          this.form.exposureInterval = null
        } else {
          this.form.resolution = (this.form.horizontalResolution === undefined ? '' : this.form.horizontalResolution) +
              '*' +
              (this.form.verticalResolution === undefined ? '' : this.form.verticalResolution)
        }
        if (this.dialogStatus === 'add') {
          saveFtmSensor(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmSensor(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    getFtmSensor(row) {
      getFtmSensor(row.id).then(res => {
        this.form = res.data
        if (this.form.resolution) {
          let urlArr = this.form.resolution.split('*')
          this.form.horizontalResolution = urlArr[0]
          this.form.verticalResolution = urlArr[1]
        }
      })
    },
    changeSensorType(e) {
      this.form.sensorTypeName = e.node?.name
    },
    changeSupplierName(e) {
      this.form.supplierName = e.node?.name
    }
  }
}
</script>

<style scoped lang="scss">
.point {
  margin: 0 10px;
}
</style>
