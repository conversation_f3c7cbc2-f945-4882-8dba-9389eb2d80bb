import { httpGet, httpPost, httpDelete } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const pageFleetApps = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_apps/page', params })
export const listLatestFtmApp = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_apps/listLatestFtmApp', params })
export const saveFleetApp = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_apps', data, params })
export const deleteFtmApps = (params = {}) =>
  httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_apps', params })
