<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2569642" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">info</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">table</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">ipad</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">line chart</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">bar chart</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">tags</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec6d;</span>
                <div class="name">汽车</div>
                <div class="code-name">&amp;#xec6d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe732;</span>
                <div class="name">task</div>
                <div class="code-name">&amp;#xe732;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">075-test</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">团队</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">图标</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">字典管理</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">工作组</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec07;</span>
                <div class="name">模块</div>
                <div class="code-name">&amp;#xec07;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">机构</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">文档</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88a;</span>
                <div class="name">退出</div>
                <div class="code-name">&amp;#xe88a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">接口</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">同步</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">映射</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1638426858566') format('woff2'),
       url('iconfont.woff?t=1638426858566') format('woff'),
       url('iconfont.ttf?t=1638426858566') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-info"></span>
            <div class="name">
              info
            </div>
            <div class="code-name">.icon-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-table"></span>
            <div class="name">
              table
            </div>
            <div class="code-name">.icon-table
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ipad"></span>
            <div class="name">
              ipad
            </div>
            <div class="code-name">.icon-ipad
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-linechart"></span>
            <div class="name">
              line chart
            </div>
            <div class="code-name">.icon-linechart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-barchart"></span>
            <div class="name">
              bar chart
            </div>
            <div class="code-name">.icon-barchart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tags"></span>
            <div class="name">
              tags
            </div>
            <div class="code-name">.icon-tags
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiche"></span>
            <div class="name">
              汽车
            </div>
            <div class="code-name">.icon-qiche
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-task"></span>
            <div class="name">
              task
            </div>
            <div class="code-name">.icon-task
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon--test"></span>
            <div class="name">
              075-test
            </div>
            <div class="code-name">.icon--test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuandui"></span>
            <div class="name">
              团队
            </div>
            <div class="code-name">.icon-tuandui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao"></span>
            <div class="name">
              图标
            </div>
            <div class="code-name">.icon-tubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidianguanli"></span>
            <div class="name">
              字典管理
            </div>
            <div class="code-name">.icon-zidianguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuozu"></span>
            <div class="name">
              工作组
            </div>
            <div class="code-name">.icon-gongzuozu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-module-fill"></span>
            <div class="name">
              模块
            </div>
            <div class="code-name">.icon-module-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-institute"></span>
            <div class="name">
              机构
            </div>
            <div class="code-name">.icon-institute
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendang"></span>
            <div class="name">
              文档
            </div>
            <div class="code-name">.icon-wendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-exit"></span>
            <div class="name">
              退出
            </div>
            <div class="code-name">.icon-exit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiekou"></span>
            <div class="name">
              接口
            </div>
            <div class="code-name">.icon-jiekou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongbu"></span>
            <div class="name">
              同步
            </div>
            <div class="code-name">.icon-tongbu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingshe"></span>
            <div class="name">
              映射
            </div>
            <div class="code-name">.icon-yingshe
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-info"></use>
                </svg>
                <div class="name">info</div>
                <div class="code-name">#icon-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-table"></use>
                </svg>
                <div class="name">table</div>
                <div class="code-name">#icon-table</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ipad"></use>
                </svg>
                <div class="name">ipad</div>
                <div class="code-name">#icon-ipad</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-linechart"></use>
                </svg>
                <div class="name">line chart</div>
                <div class="code-name">#icon-linechart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-barchart"></use>
                </svg>
                <div class="name">bar chart</div>
                <div class="code-name">#icon-barchart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tags"></use>
                </svg>
                <div class="name">tags</div>
                <div class="code-name">#icon-tags</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiche"></use>
                </svg>
                <div class="name">汽车</div>
                <div class="code-name">#icon-qiche</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-task"></use>
                </svg>
                <div class="name">task</div>
                <div class="code-name">#icon-task</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon--test"></use>
                </svg>
                <div class="name">075-test</div>
                <div class="code-name">#icon--test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuandui"></use>
                </svg>
                <div class="name">团队</div>
                <div class="code-name">#icon-tuandui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao"></use>
                </svg>
                <div class="name">图标</div>
                <div class="code-name">#icon-tubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidianguanli"></use>
                </svg>
                <div class="name">字典管理</div>
                <div class="code-name">#icon-zidianguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuozu"></use>
                </svg>
                <div class="name">工作组</div>
                <div class="code-name">#icon-gongzuozu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-module-fill"></use>
                </svg>
                <div class="name">模块</div>
                <div class="code-name">#icon-module-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-institute"></use>
                </svg>
                <div class="name">机构</div>
                <div class="code-name">#icon-institute</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendang"></use>
                </svg>
                <div class="name">文档</div>
                <div class="code-name">#icon-wendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-exit"></use>
                </svg>
                <div class="name">退出</div>
                <div class="code-name">#icon-exit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiekou"></use>
                </svg>
                <div class="name">接口</div>
                <div class="code-name">#icon-jiekou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongbu"></use>
                </svg>
                <div class="name">同步</div>
                <div class="code-name">#icon-tongbu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingshe"></use>
                </svg>
                <div class="name">映射</div>
                <div class="code-name">#icon-yingshe</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
