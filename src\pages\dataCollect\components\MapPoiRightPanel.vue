<template>
  <div class="map-selection-container intersection">
    <div class="map-content">
      <div class="query-param">
        <div class="label">
          <el-checkbox-group v-model="filterForm" size="default">
            <el-checkbox-button :value="true">
              <div class="label">
                筛选
                <ltw-icon :class="{ visible: filterForm?.length }" icon-code="el-icon-d-arrow-right"></ltw-icon>
              </div>
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
        <div class="form" :class="{ visible: filterForm?.length }">
          <el-checkbox class="intersection-check" v-model="intersectionChecked" label="全选" @change="changeCheckAll" />
          <el-cascader
            v-if="!disabled"
            class="canton-select"
            clearable
            filterable
            popper-class="canton-list"
            v-model="form.cantonCode"
            :options="cantonCodeList"
            :props="props"
          />
          <el-select
            v-if="!disabled"
            v-model="form.supplierCode"
            placeholder="请选择供应商"
            class="supplier-code"
            clearable
            filterable
            :disabled="disabled"
          >
            <el-option v-for="item in supplierList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>

          <el-select
            v-if="!disabled"
            v-model="form.intersectionCollected"
            placeholder="请选择"
            class="supplier-code"
            clearable
            filterable
            :disabled="disabled"
            @change="query"
          >
            <el-option
              v-for="item in intersectionCollectedList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
          <ltw-input
            v-if="!disabled"
            class="intersection-name"
            :placeholder="$t('请输入关键字')"
            v-model="form.intersectionName"
            clearable
            @clear="query"
            id="input"
          >
            <template #append>
              <el-button @click="query" id="el-icon-search">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>

        <!--          <el-checkbox-->
        <!--            class="intersection-select"-->
        <!--            v-model="intersectionCollected"-->
        <!--            label="已采集"-->
        <!--            @change="query"-->
        <!--            border-->
        <!--          />-->
      </div>
      <div class="query-param tools">
        <div class="label">
          <el-checkbox-group v-model="filterTools" size="default">
            <el-checkbox-button :value="true">
              <div class="label">
                工具栏
                <ltw-icon :class="{ visible: filterTools?.length }" icon-code="el-icon-d-arrow-right"></ltw-icon>
              </div>
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
        <div class="form" :class="{ visible: filterTools?.length }">
          <div
            @click="setDrawType(item.type)"
            v-for="item in drawTools"
            :key="item.type"
            class="tool-item"
            :class="[item.type, { active: drawType === item.type }]"
          ></div>
          <div @click="deleteMapShape()" class="tool-item delete"></div>
        </div>
        <el-button class="btn-add-poi" @click="addPoi()" type="primary" size="default" plain><ltw-icon class="icon-add-poi" icon-code="el-icon-upload"></ltw-icon></el-button>
      </div>
      <div class="map-overview" :id="mapId" ref="mapContainerRef"></div>
      <!--      <tx-map-intersection ref="TXMapIntersection" @clickMarker="clickMarker" @clickLabel="clickLabel" />-->
    </div>
    <el-card class="choosed-intersection">
      <div class="body">
        <el-scrollbar class="selected-container">
          <el-card
            class="intersection-item"
            :class="{ active: currentMarkerKey === item.itemKey }"
            v-for="(item, index) in markersBoardList"
            :key="item.id"
            @click="getMapCenter(item, index)"
            >{{ item.itemName }}
          </el-card>
          <el-empty v-show="!markersBoardList?.length" description="暂无选择POI"></el-empty>
        </el-scrollbar>
      </div>
      <div class="footer" v-if="!disabled">
        <div class="intersection-num">共{{ markersBoardList?.length || 0 }}路口</div>
        <el-button size="small" type="primary" @click="clearIntersection()">清除</el-button>
        <el-button size="small" type="primary" @click="submitIntersection()">保存</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  createPolylineLayer,
  initMap,
  initMapEditor,
  initMarkers,
  latLngPoint,
  isLatitudeLongitude,
  searchSuggestionAPI,
  showFitView,
  goCenter,
  initDriving,
  drivingSearch,
  addGeo,
  removeGeo,
  initMultiLabel,
  isPointInPolygon
} from '@/plugins/map/TxMap'
import util, { debounce, downloadTxt, getUuid, showConfirmToast, showToast } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { OVER_LAY_TYPE } from '@/plugins/constants/data-dictionary'
import LtwIcon from '@/components/base/LtwIcon.vue'
import { listMapResult } from '@/apis/data-collect/map_results'
import md5 from 'js-md5'
import { jsonp } from 'vue-jsonp'
import MarkerInfoWindow from '@/pages/dataCollect/dialog/MarkerInfoWindow.vue'
import { h, render } from 'vue'
import LtwInput from '@/components/base/LtwInput.vue'
import { ElMessageBox } from 'element-plus'
import { getFilesPreview, uploadFile } from '@/apis/base/file'
import { getSysCantonTree } from '@/apis/system/sys-canton'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import TXMapIntersection from '@/components/map/TXMapIntersection'
import { getIntersectionList, daqTaskRecommends, updateDaqTaskRecommends } from '@/apis/data-collect/vt-daq-task'

let mapObj = {}
// let globalMap,
//   globalMapEditor,
//   globalCenterMarker,
//   globalMarker, //起始点marker
//   globalRoadLine, //路段
//   globalInfoWindow, //信息窗口
//   globalDriving, //驾车
//   globalRoutePlanPolylineLayer, //多驾车路线
//   globalMapEditorMarker, //编辑的marker
//   globalMultiLabel,
//   globalRouteListPolylineLayer // 路线列表

const defaultPath = {
  crs: 1,
  geom: {
    type: 'Polygon',
    coordinates: []
  }
}
export default {
  name: 'MapPreviewRoute',
  components: {
    'tx-map-intersection': TXMapIntersection,
    LtwInput,
    LtwIcon
  },
  props: {
    propDisabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propDisabled: {
      handler(val) {
        this.disabled = val
      },
      deep: true,
      immediate: true
    }
  },
  emits: ['reload'],
  data() {
    return {
      OVER_LAY_TYPE,
      mapId: 'map',
      queryParam: {},
      bounds: [],
      showRoad: false,
      roadList: [],
      queryLoading: false,
      suggestionList: [],
      // windowinfo传值
      currentMarker: {},
      currentPassMarkerId: '',
      routePointList: [],
      clearRoutePoints: [],
      planRouteList: [],
      routeList: [],
      allRouteList: [],
      currentRouteKey: '',
      selectedStyleName: 'selectedStyle',
      unSelectedStyleName: 'unSelectedStyle',
      routeMode: '',
      leftHidden: false,
      rightHidden: false,

      disabled: false,
      intersectionChecked: false,
      baseCheckedIntersectionList: [],
      baseIntersectionRecommendList: [],
      queryIntersectionList: [],
      intersectionCollected: '',
      intersectionCollectedList: [
        {
          name: '全部',
          code: ''
        },
        {
          name: '已采集',
          code: true
        },
        {
          name: '未采集',
          code: false
        }
      ],
      form: {
        cantonCode: '',
        supplierCode: '',
        crs: 1,
        intersectionCollected: '',
        intersectionName: ''
      },
      cantonCodeList: [],
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click'
        // checkStrictly: true
      },
      intersectionName: '',
      mapParkingList: {},
      currentMarkerKey: '',
      supplierList: [],
      intersectionRecommendList: [],

      allIntersectionChecked: [],
      filterForm: [],
      filterTools: [],
      drawType: '',
      drawTools: [
        {
          type: 'polygon'
        },
        {
          type: 'circle'
        },
        {
          type: 'ellipse'
        },
        {
          type: 'rectangle'
        }
      ],
      markersBoardList: [],
      mapMarkers: [],
      selectedStyle: 'selectedStyle',
      activeMarkerList: []
    }
  },
  beforeUnmount() {
    if (mapObj.globalMap) {
      mapObj.globalMap?.destroy()
      mapObj = {}
    }
  },
  methods: {
    async show(row) {
      // this.getFilesPreview(row)
      this.getSysCantonTree()
      this.listSupplier()
      if (!mapObj.globalMap) {
        await this.loadMap()
        mapObj.globalMap.setZoom(18)
        this.initCenterMarkers()
      }
      const idList = row.data[0]?.recommendItems?.map(val => val.itemKey)
      const postData = {
        idList,
        crs: this.form.crs
      }
      this.getIntersectionList(postData)
    },
    async loadMap() {
      const _this = this
      mapObj.globalMap = await initMap(this.$refs.mapContainerRef)
      // const throttledHandleBoundsChange = debounce(this.handleBoundsChange, 1000)
      // mapObj.globalMap.addListener('bounds_changed', event => {
      //   throttledHandleBoundsChange(event)
      // })
      // mapObj.globalMap.addListener('zoom', event => {
      //   let zoom = mapObj.globalMap.getZoom()
      //   if (zoom < 13) {
      //     this.clearObjects(mapObj.globalRoadLine)
      //     showToast('该缩放等级下暂不支持查看真值，请放大地图后再次点击真值轨迹按钮', 'warning')
      //   }
      // })
      // if (!mapObj.globalInfoWindow) {
      //   mapObj.globalInfoWindow = this.initInfoWindow()
      // }
      // this.closeWindow()
      // mapObj.globalMap.addListener('rightclick', _this.handleMapRightClick)
    },
    async handleMapRightClick(e) {
      if (!this.disabled) {
        this.closeWindow()
        await this.clearMarkers()
        await this.changeEditorMode('DRAW')
        let markerList = [
          {
            id: 0, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
            style: {
              width: 17,
              height: 25,
              anchor: { x: 17, y: 50 },
              src: 'data:image/png;base64,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'
            },

            position: e.latLng
          }
        ]
        addGeo(mapObj.globalMapEditorMarker, markerList)
      }
    },
    handleBoundsChange(event) {
      const ne = event.bounds._ne
      const sw = event.bounds._sw
      const nw = { lat: ne.lat, lng: sw.lng }
      const se = { lat: sw.lat, lng: ne.lng }
      this.bounds = [
        [nw.lng, nw.lat],
        [ne.lng, ne.lat],
        [se.lng, se.lat],
        [sw.lng, sw.lat],
        [nw.lng, nw.lat]
      ]
      if (this.showRoad) {
        this.drawerRoadLine()
      }
    },
    handleDisplayRoadChange(val) {
      if (val) {
        let zoom = mapObj.globalMap.getZoom()
        if (zoom < 13) {
          this.showRoad = false
          showToast('该缩放等级下暂不支持查看真值，请放大地图后再次点击真值估计按钮', 'warning')
        } else {
          this.drawerRoadLine()
        }
      } else {
        this.clearObjects(mapObj.globalRoadLine)
      }
    },
    extractedCoordinates() {
      this.roadList.forEach(item => {
        item.paths =
          item.trajectoryGeom?.coordinates?.map(geo => {
            const [longitude, latitude] = geo
            return [latitude, longitude]
          }) || []
      })
    },
    drawerRoadLine() {
      this.clearObjects(mapObj.globalRoadLine)
      listMapResult({
        targetCrs: 1,
        intersectingArea: { type: 'Polygon', userData: { crs: 1 }, coordinates: [this.bounds] }
      }).then(res => {
        this.roadList = res.data
        this.extractedCoordinates()
        const options = {
          styles: {
            color: '#ec4c4c',
            width: 5,
            lineCap: 'round',
            showArrow: true,
            arrowOptions: { width: 8, height: 5, space: 30 }
          }
        }
        mapObj.globalRoadLine = createPolylineLayer(mapObj.globalMap, this.roadList, options, 'roadLine')
      })
    },
    initCenterMarkers() {
      const markerList = [{ id: 'centerMarker', style: { width: 25, height: 35, anchor: { x: 10, y: 30 } } }]
      mapObj.globalCenterMarker = initMarkers(mapObj.globalMap, markerList)
    },
    clearObjects(...objects) {
      objects.forEach(obj => {
        if (obj) {
          obj.setMap(null)
        }
      })
    },
    async initEditor() {
      const _this = this
      const shapeOptions = [
        {
          type: OVER_LAY_TYPE.POLYGON,
          id: OVER_LAY_TYPE.POLYGON
        },
        {
          type: OVER_LAY_TYPE.CIRCLE,
          id: OVER_LAY_TYPE.CIRCLE
        },
        {
          type: OVER_LAY_TYPE.ELLIPSE,
          id: OVER_LAY_TYPE.ELLIPSE
        },
        {
          type: OVER_LAY_TYPE.RECTANGLE,
          id: OVER_LAY_TYPE.RECTANGLE
        }
        // {
        //   type: OVER_LAY_TYPE.DRAGMARKER,
        //   id: OVER_LAY_TYPE.DRAGMARKER, // 可编辑图层
        //   options: {
        //     selectedStyleId: _this.selectedStyleName
        //   }
        // }
      ]
      const { mapEditor, overlayList } = await initMapEditor(mapObj.globalMap, shapeOptions)
      mapObj.globalMapEditor = mapEditor
      mapObj.globalMapEditorPolygon = overlayList[0].overlay
      mapObj.globalMapEditorCircle = overlayList[1].overlay
      mapObj.globalMapEditorEllipse = overlayList[2].overlay
      mapObj.globalMapEditorRectangle = overlayList[3].overlay
      // mapObj.globalMapEditorMarker = overlayList[4].overlay
      // mapObj.globalMapEditor.on('adjust_complete', _this.dragEnd)
      // //监听编辑marker点击事件
      // mapObj.globalMapEditorMarker.on('click', _this.openInfoWindow)
      mapObj.globalMapEditor.on('draw_complete', async geometry => {
        this.drawType = ''
        await this.changeEditorMode('INTERACT')
        this.updateMarkerStatus(geometry)
      })
      mapObj.globalMapEditor.on('delete_complete', geometry => {
        this.removeMarkerStatus(geometry)
      })
      mapObj.globalMapEditor.on('adjust_complete', geometry => {
        this.updateMarkerStatus(geometry)
      })
      mapObj.globalMapEditor.on('delete', geometry => {
      })
    },
    removeMarkerStatus(geometry) {
      let activeMarkerList = []
      mapObj.globalMarker?.geometries?.forEach(val => {
        const indexMarker = this.mapMarkers.findIndex(marker=>marker.itemKey === val.id)
        const indexBoard = this.markersBoardList.findIndex(marker=>marker.itemKey === val.id)
        let isInArea
        if (Array.isArray(geometry)) {
          isInArea = ~geometry.findIndex(geo => isPointInPolygon(val.position, geo.paths))
        } else {
          isInArea = isPointInPolygon(val.position, geometry.paths)
        }
        if (isInArea) {
          //删除当前激活点位
          activeMarkerList.splice(indexMarker, 1)
          if(~indexBoard){
            this.markersBoardList.splice(indexBoard, 1)
          }
          val.styleId = val.id
        }
      })
      mapObj.globalMarker?.updateGeometries(mapObj.globalMarker.geometries)
      this.activeMarkerList = activeMarkerList
    },
    updateMarkerStatus(geometry) {
      let activeMarkerList = []
      mapObj.globalMarker?.geometries?.forEach(val => {
        // isPointInPolygon(val.position, geometry.paths) && val.styleId !== this.selectedStyle
        let isInArea
        if (Array.isArray(geometry)) {
          isInArea = ~geometry.findIndex(geo => isPointInPolygon(val.position, geo.paths))
        } else {
          isInArea = isPointInPolygon(val.position, geometry.paths)
        }
        if (isInArea) {
          //添加当前激活点位
          activeMarkerList.push(this.mapMarkers.find(marker=>marker.itemKey === val.id))
          val.styleId = this.selectedStyle
        } else {
          //是否已选择到右侧面板上
          const isOnBoard = ~this.markersBoardList.findIndex(boardItem=>boardItem.itemKey === val.id)
          if(!isOnBoard){
            val.styleId = val.id
          } else {
            //添加当前激活点位
            activeMarkerList.push(this.mapMarkers.find(marker=>marker.itemKey === val.id))
          }
        }
      })
      mapObj.globalMarker?.updateGeometries(mapObj.globalMarker.geometries)
      this.activeMarkerList = activeMarkerList
    },
    resetEditorMode() {
      this.drawType = null
      mapObj.globalMapEditor?.setSelectable(false)
      mapObj.globalMapEditor?.setSnappable(false)
      mapObj.globalMapEditor?.setActionMode(TMap.tools.constants.EDITOR_ACTION.NONE)
    },
    async changeEditorMode(val) {
      if (!mapObj.globalMapEditor) {
        await this.initEditor()
      }
      if (val === null) {
        this.resetEditorMode()
        return
      }
      const isDrawMode = val === 'DRAW'
      mapObj.globalMapEditor.setSelectable(!isDrawMode)
      mapObj.globalMapEditor.setSnappable(!isDrawMode)
      mapObj.globalMapEditor.setActionMode(
        isDrawMode ? TMap.tools.constants.EDITOR_ACTION.DRAW : TMap.tools.constants.EDITOR_ACTION.INTERACT
      )
      this.activeEditorMode = val
    },

    async positioningByAddress(key) {
      if (!key) return
      const isLatLng = isLatitudeLongitude(key)
      let suggestionList = []
      if (isLatLng) {
        suggestionList = [
          {
            title: key,
            id: key
          }
        ]
      } else {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(key)
        this.queryLoading = false
      }
      this.suggestionList = suggestionList
    },
    selectSuggestionHandle(key) {
      if (!key) return
      const item = this.suggestionList.find(val => val.id === key)
      // this.queryParam.keyword = item?.title
      // this.queryParam.location = item?.location
      this.queryParam.keyword = (item?.province || '') + (item?.city || '') + (item?.district || '') + item?.title
      this.search()
    },
    async search() {
      if (!this.queryParam.key) return
      const isLatLng = isLatitudeLongitude(this.queryParam.key)
      let suggestionList = []
      if (!isLatLng) {
        this.queryLoading = true
        suggestionList = await searchSuggestionAPI(this.queryParam.keyword)
        this.queryLoading = false
      } else {
        suggestionList = [{ location: latLngPoint(this.queryParam.key.split(',')) }]
      }
      let markerList = suggestionList.map((val, index) => {
        // const item = this.convertorItem(val)
        return {
          id: val.id, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          style: {
            width: 34,
            height: 50,
            anchor: { x: 17, y: 50 },
            src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABkCAMAAAAIYWa2AAAC8VBMVEVHcEwAAAAAAAAAAAAAAAAzMzPQRUXlSUnnS0vqS0vrTEzhSEgAAADFRUXmS0vtTk7xTU3yTk7yTk6vPz/oTEzvTk7yTk7MRETrS0vyT0/FQUHFPj7uTU3yTk7sTExVKiroS0vyTk5IJCS4QEDuTU3SRETxTU0AAADhSkreSUkAAADoSkryTk7pSkoAAADoTEzmTEziSUndSEjMRETDQUFtJCTwTU3vTExfHx/mSUkAAADFPz/yTk68PDztTEwAAADMRUXBQUHsTEzqTEyzPj7yTk6lOTnjSUnfSEjuTk7yUVH2iYn6wMD95OT+9vYAAADBPT32hob95ub///+2OTkAAADfSUnyT0/5ra3aR0cAAADqS0vwTU3vTU0AAABmKChbJCTBPT24OjrQQkIAAADXRUXRQ0PVRUUAAADcR0fYRkYAAADXRETQQkLNRETIQkLDQUG9Pj6qNzegNTUhCwvxTk4cCQkAAADrTEwAAAAAAADkSkriSEgAAADLQUFnIiLyTk5gHx/oS0vnSkoAAAAAAADOQ0NLHh7wTk4/FRXaRkbXRUVuJSXxTU1vKCgAAADfSEjZRkYAAADwTU3vTU1XHBzSRETIQEAmDw/rTEzoSkoIBQWhNDSMLS3NQkIAAAAAAAAlDw/oSkrnSkqBKyvwTU3vTU1lISG5PT2qODgAAADURUXKQkIBAQHjSUncRkYAAAAAAAA1FBTkSkoYCgpjISHsS0s/FxcAAABtJSXsTU3pS0tTGhp0JibsTExYHR1bHBzsTExpIyOVMzPuTU19KyujNjbxTk7uTU2MLS2xOTnxTk6bMTHCPj60OTniSEjcR0fuTU07FxefNDTbR0fTRUXpS0uiNDThSEiDKyvXRkZ6JyfhSUmSLi7pS0vIQUFQGBjmSkrGQUGCKSntTEzrTEzdSEjbR0edMzOYMTHZR0eTLy8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA6Ei6pAAAA+3RSTlMAAQIDBAUhRVhlcUYFFlSTzvX/EGC6/B6P8h8YmPiZBmvtBxe7MNwGVlcHhf6PCHx8WlwtLw7T1BCDCSz3LrQLNzqwsSX9KICC2///////DjL///81CnP//3UMtOPkERkcPkFcDWdqdg98fhBvcmFjUlY8Phf4GxTRFxKlqBZsJfopwMIaE3ci7iSRky/5MhimnBvy7C2DeiHVzSBIQoceFSLQxTvw6zVnWx2Qgh+3qSEZJsAkMtssHDbh1jE73zQ14jpL6UFU9e9IXfRScma0p+crU56T0k2vOpQ0rULLdCm4cTHY1JeWPD6aQC42PDkwKCsvMjQsKSUnKiijw/sAAAUASURBVHgB1M8DcoRBEIbhNNbutW3EtnP/E2V/rDUzKeUpo9/q7+gfATA/tSAiORARpjQLiMTs8fr8gWAw4A95PWEmO6SRII5EY/GEzCXisWgkzIodu5BMpTOyIZNOJZmsjEIim8vLDvlcIXwgYw3hYqkse5QrRTuz741qrS4H1GsN3lmx3mimRUGrGabtkwAo3O6Ikk67u3USIIV7eVGU7w82JwEgdYcjUTYablasRk60jCdrFQDqHoumk3ll3jgVbWfWokUDw+d50Za/GND8FUC+vBID1zddt2KNGdyKkdu7MLoR7N4/iJGHR3cQIN89iaGnZ+cVoO7LSAyNXicMYD/yJsben7sI1iMfn2Ls82vC0wjffcsf/DyH8ZdUskbLIIyBcJCSag+BQ4+36+7uisv58d+T6pt23ic6sH1wJ5CEKMmKqiqyJJKIoB3vwM6xTgKGaf3LNEjIdnZh99IlXM+3luR7BBaEB7B3FxGub63IJ7BIO4ZjLSZ2sdZEbJSkl3Ce4UkTzfUipojnLXfgssAbSNaGJJwsQ3Aq3JI3i8g4WdVw1+CWsllEwclWgzDALXWziIqTXQ/1wFpknKC+Z13n4bvII+thn1Kon1lf/DJB/cqxhY17myB8v2aL/cdXKWYNHLcWheGlcmt3r32URP2E1FeqQoatDJrRpI0sJTIzyTEzoxIzY9gMYWY0M1fRXWXOsizdvf18c/6Dv3SNNpBspH8DGMlRhgv8df9WwQ0h1HDaFuXfUopiSQNBR8f4sx5jYvkLhn9ChTh/FnV8AkPImy0xyR9IEidvNstpJjkFn5GSypJWeduHCmn4kPQE5rTFYJL1ZGTiMjKzhFD57hgtBC1m40Ju5vAXrCbkTgLZjFw8Rl4+RxFm+RbLoTBiAR6kMIdXDIo9lKJiHEZJqYACsUPkrHBlMRhmqzzBdsEeCKJYST4nXT+korIqVPFsinsMraqu0cuoLRWZ02CHkeNixLp6fYyGxhweeS2jw5XLgpp02b+AZskhBgSx0i09kNstHA1i0FMqJLS0ame0tXfYKwMQ5RPhAiN2lmhldHUn8IH/gBjntHT09Gpj1PT1s6GODxXntAQm5gwMamEMDUtVFOHCgLTIyc0ZGdXAuCMJNPq+8ISg5Iay/SMhxzF671Z6ZQCFquovb1Bn3LtfydEXrJBUD4oVUR6otu7DRy0c450BlNNUlTQ27psxMYkYUFxfFFqQpnzu3OmZFlGdAZTKWV+QuVLRRqow4K/DaYZrmffOeNzewasygIIGIP+JN8bo05zEwP/NJnUGDEDClJe9EPBMgmZXeTAAbP9zT8iLFoEmjmfAXqCETo9ZfNkN06+JYr1g63jlJihgsj/RY/rV0xKY2P/arTItAkyudkFvXFpufKCDgT8D2gUluOT2rcTqEOM4Re9inK5dj0jrEQOnqOO9A5Ldn2j/oNf1UG6ryoBx8gMKRIcYCMWW8BFOpqQnEHgoKwL4yk8djM5AoEB87F9/0vq5KlBnIBAKxX1RIPM5NjiZekMh2asK5CtHybcb63+5nNpvSm2yWMWaYer5jiA/kBojHsT0D8n+RJBfeGqgVRam5RW/yGLVBlqlanFpaRm8GVYoTPvK6tra+ka7TbcagEibW9s7q6u7G3v7lbgQy8Hh/tbe9vbK1tHhgcWA+Yj+w839o62j/cN+ApeBskIyPM8EQlaxQcd32W/E9Jewb6BBFAAAAABJRU5ErkJggg=='
          },

          position: val.location //点标记坐标位置
        }
      })
      await this.clearMarkers()
      // }
      await this.changeEditorMode('DRAW')
      addGeo(mapObj.globalMapEditorMarker, markerList)
      if (!mapObj.globalInfoWindow) {
        mapObj.globalInfoWindow = this.initInfoWindow()
      }
      this.closeWindow()
      const fitViewList = markerList
        .filter((val, index) => index <= 5)
        .map(val => {
          return val.position
        })
      showFitView(fitViewList, mapObj.globalMap)
    },
    async clearMarkers() {
      this.currentRouteKey && this.getRouteCenter({ id: this.currentRouteKey })
      if (mapObj.globalMapEditorMarker) {
        // 无法获取编辑模式下选中状态的marker，所以把关闭编辑模式
        await this.changeEditorMode(null)
        removeGeo(
          mapObj.globalMapEditorMarker,
          mapObj.globalMapEditorMarker.getGeometries().map(val => val.id)
        )
      }
    },

    async dragEnd(event) {
      mapObj.globalInfoWindow.setPosition(
        latLngPoint({
          lat: event?.position?.lat,
          lng: event?.position?.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: event?.position?.lat,
        lng: event?.position?.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      this.openWindow()
    },
    async openInfoWindow(evt) {
      goCenter([evt.geometry.position.lng, evt.geometry.position.lat], mapObj.globalMap)
      mapObj.globalInfoWindow.setPosition(
        latLngPoint({
          lat: evt.geometry.position.lat,
          lng: evt.geometry.position.lng
        })
      )

      const currentMarker = await this.getLocationName({
        lat: evt.geometry.position.lat,
        lng: evt.geometry.position.lng
      })
      this.currentMarker.lat = currentMarker.lat
      this.currentMarker.lng = currentMarker.lng
      this.currentMarker.title = currentMarker.title
      this.currentMarker.id = currentMarker.id
      this.openWindow()
    },
    goPassCenter(e, routeId) {
      if (this.currentRouteKey === routeId) {
        if (this.currentPassMarkerId === e.id) {
          this.currentPassMarkerId = ''
        } else {
          this.currentPassMarkerId = e.id
        }
        let passPoints = mapObj.globalMultiLabel.getGeometries()
        const clickedPassPoints = passPoints.map((val, index) => {
          return {
            id: val.id,
            styleId:
              this.currentPassMarkerId && val.id === 'routePoint-' + this.currentPassMarkerId
                ? 'selectedStyle'
                : val.id,
            position: val.position,
            content: val.content
          }
        })
        mapObj.globalMultiLabel.setGeometries(clickedPassPoints)

        if (e.lat && e.lng) {
          goCenter([e.lng, e.lat], mapObj.globalMap)
        }
      } else {
        showToast('请先选择对应路线', 'warning')
      }
    },
    initInfoWindow() {
      const _this = this

      let instance = h(MarkerInfoWindow, {
        item: _this.currentMarker,
        onSetPoint: val => {
          this.setPoint(val)
        }
      })
      let infoWindow = new TMap.InfoWindow({
        map: mapObj.globalMap,
        enableCustom: true,
        position: latLngPoint({ lat: 31.380155339677, lng: 121.27259505835 }),
        offset: { y: -50, x: 0 },
        content: '<div id="ref-card"></div>'
      })
      let card = document.querySelector('#ref-card')
      render(instance, card)
      return infoWindow
    },
    openWindow() {
      mapObj.globalInfoWindow?.open()
    },
    closeWindow() {
      mapObj.globalInfoWindow?.close()
    },
    getGeoCoder(param) {
      let sig = md5(
        `/ws/geocoder/v1?callback=jsonpCallback&key=${GLB_CONFIG.TxMapKey}&location=${param.location}&output=jsonp${GLB_CONFIG.TxMapSecretKey}`
      )
      // sig = encodeURI(sig) //url化一下
      let getData = {
        callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
        callbackName: 'jsonpCallback', // 设置callback 参数的值
        key: GLB_CONFIG.TxMapKey,
        location: param.location,
        output: 'jsonp',
        sig
      }
      //签名失败的解决办法 https://lbs.qq.com/faq/serverFaq/webServiceKey
      return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
    },
    async getLocationName(item) {
      // this.currentVehicle = row
      if (item.lat && item.lng) {
        let res = await this.getGeoCoder({
          location: item.lat + ',' + item.lng
        })
        if (res.status === 0) {
          item.title = res.result?.formatted_addresses?.recommend
          item.id = res.request_id
          return item
        } else {
          showToast(res.message, 'warning')
        }
      }
    },
    setPoint(val) {
      const index = this.routePointList.findIndex(point => val.id === point.id)
      if (!~index) {
        const startIndex = this.routePointList.findIndex(val => val.type === 'start')
        const endIndex = this.routePointList.findIndex(val => val.type === 'end')
        if (val.type === 'start') {
          if (~startIndex) {
            this.routePointList[startIndex].type = 'pass'
          }
          this.routePointList.unshift(val)
        } else if (val.type === 'end') {
          if (~endIndex) {
            this.routePointList[endIndex].type = 'pass'
          }
          this.routePointList.push(val)
        } else {
          if (!~endIndex) {
            this.routePointList.push(val)
          } else {
            this.routePointList.splice(-1, 0, val)
          }
        }
        const ids = mapObj.globalMapEditorMarker
          .getGeometries()
          .filter(marker => marker.id !== val.id)
          .map(marker => marker.id)
        removeGeo(mapObj.globalMapEditorMarker, ids)
      } else {
        showToast(this.$t('该点位已添加'), 'warning')
      }
    },
    addRoutePoint() {},
    removeRoutePoint(index) {
      this.routePointList.splice(index, 1)
    },
    async generateRoutes() {
      const _this = this
      if (!mapObj.globalDriving) {
        mapObj.globalDriving = initDriving({
          mp: true,
          noStep: true,
          policy: 'PICKUP,NAV_POINT_FIRST'
        })
      }
      // 校验路径完整性
      let routeCheckList = this.routePointList.filter(val => val.type === 'start' || val.type === 'end')
      if (routeCheckList?.length > 1) {
        this.clearObjects(mapObj.globalRoutePlanPolylineLayer, mapObj.globalMultiLabel, mapObj.globalMarker)
        this.setRoutePointMarkers(this.routePointList)
        const startPoint = this.routePointList[0]
        const endPoint = this.routePointList[this.routePointList?.length - 1]
        const waypoints = this.routePointList.slice(1, this.routePointList?.length - 1)
        const routes = await drivingSearch(mapObj.globalDriving, {
          from: latLngPoint({ lat: startPoint.lat, lng: startPoint.lng }),
          // from: latLngPoint({ lat: startPoint.lat, lng: startPoint.lng }),
          to: latLngPoint({
            lat: endPoint.lat,
            lng: endPoint.lng
          }),
          waypoints: waypoints.map(val => latLngPoint({ lat: val.lat, lng: val.lng })),
          servicesk: GLB_CONFIG.TxMapSecretKey
        })
        this.planRouteList = routes.result.routes
        this.drawRoutePlanLines(routes.result.routes)
        this.closeWindow()
        // this.clearRoutePoints = JSON.parse(JSON.stringify(this.routePointList))
      } else {
        showToast('请至少选择起点和终点', 'warning')
      }
    },
    confirmRoute() {
      const _this = this,
        queryParamItem = this.suggestionList.find(val => val.id === _this.queryParam.key)
      if (this.planRouteList?.length) {
        let routeLines = mapObj.globalRoutePlanPolylineLayer.getGeometries()
        const highlightIndex = routeLines.findIndex(val => val.styleId === _this.selectedStyleName)
        ElMessageBox.prompt('', _this.$t('路线名称'), {
          confirmButtonText: _this.$t('确认'),
          cancelButtonText: _this.$t('取消'),
          inputValue: queryParamItem?.title,
          // inputPattern:
          //   /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
          // inputErrorMessage: 'Invalid Email'
          inputValidator(val) {
            if (!val) {
              return '请输入路线名称'
            }
          }
        }).then(async ({ value }) => {
          this.planRouteList[highlightIndex].name = value
          const route = { markers: this.routePointList, detail: this.planRouteList[highlightIndex], id: getUuid() }

          this.routeList.push(route)
          this.allRouteList.push(route)
          this.clearPlanMap()
          this.clearObjects(mapObj.globalRouteListPolylineLayer)
          this.drawRouteListLines(this.routeList)
        })
      } else {
        showToast(this.$t('请先选择路线'), 'warning')
      }
    },
    async save() {
      // if (this.routeList?.length) {
      this.$emit('reload', this.routeList)
      // } else {
      //   showToast(this.$t('请先选择路线'), 'warning')
      // }
    },
    confirmClearMap() {
      showConfirmToast({
        message: this.$t('确认删除已选路线规划?')
      }).then(() => {
        this.clearPlanMap()
      })
    },
    clearPlanMap() {
      this.planRouteList = []
      this.routePointList = []
      this.clearRoutePoints = []
      this.clearObjects(mapObj.globalRoutePlanPolylineLayer, mapObj.globalMultiLabel, mapObj.globalMarker)

      this.closeWindow()
      this.clearMarkers()
    },
    drawRoutePlanLines(routes) {
      const _this = this
      const routeLines = routes.map((val, index) => {
        return {
          id: index + 1,
          color: 'rgb(159, 206, 255)',
          paths: val.polyline,
          styles: {
            arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
          }
        }
      })
      const options = {
        styles: {
          borderWidth: 2,
          borderColor: '#a8a8a8',
          showArrow: true,
          arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        }
      }
      mapObj.globalRoutePlanPolylineLayer = createPolylineLayer(
        mapObj.globalMap,
        routeLines,
        options,
        'route-plan-polyline-layer',
        2
      )

      //设置第一条线为高亮
      let planRoutes = mapObj.globalRoutePlanPolylineLayer.getGeometries()
      planRoutes[0].styleId = _this.selectedStyleName
      mapObj.globalRoutePlanPolylineLayer.setGeometries(planRoutes)
      mapObj.globalRoutePlanPolylineLayer.on('click', _this.handleRoutePlanClick)
    },
    drawRouteListLines(routes) {
      const _this = this
      const routeLines = routes.map((val, index) => {
        return {
          id: val.id,
          color: '#409eff',
          paths: val.detail.polyline,
          styles: {
            arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 0 }
          }
        }
      })
      const options = {
        styles: {
          borderWidth: 2,
          borderColor: '#a8a8a8',
          showArrow: true,
          lineCap: 'round',
          arrowOptions: { width: 8, height: 5, space: 50, animSpeed: 50 }
        }
      }
      mapObj.globalRouteListPolylineLayer = createPolylineLayer(
        mapObj.globalMap,
        routeLines,
        options,
        'route-list-polyline-layer',
        1
      )
      mapObj.globalRouteListPolylineLayer.on('click', _this.handleRouteClick)
    },
    setRouteChecked(checkedRoutes) {
      this.routeList = checkedRoutes
      //切换高亮
      let routeLines = mapObj.globalRouteListPolylineLayer?.getGeometries() || []
      routeLines.forEach(val => {
        if (!~checkedRoutes.findIndex(checkedRoute => checkedRoute.id === val.id)) {
          val.styleId = this.unSelectedStyleName
        }
      })

      mapObj.globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    handleRouteClick(e) {
      const route = this.routeList.find(val => val.id === e.geometry.id)
      if (route) {
        this.removeRoute(route)
      } else {
        const route = this.allRouteList.find(val => val.id === e.geometry.id)
        this.routeList.push(route)
        //设置列表高亮
        // this.currentRouteKey = route.id
        this.getRouteCenter(route)
      }

      // this.clearObjects(mapObj.globalMultiLabel, mapObj.globalMarker)
      // const route = this.routeList.find(val => val.id === e.geometry.id)
      // if (route) {
      //   this.removeRoute(route)
      // } else {
      //   const route = this.allRouteList.find(val => val.id === e.geometry.id)
      //   this.routeList.push(route)
      //   //设置列表高亮
      //   this.currentRouteKey = route.id
      //   //切换高亮
      //   let routeLines = mapObj.globalRouteListPolylineLayer.getGeometries()
      //   routeLines.forEach(val => {
      //     if (val.id === e.geometry.id) {
      //       val.styleId = val.styleId === this.selectedStyleName ? this.unSelectedStyleName : this.selectedStyleName
      //     } else {
      //       val.styleId = val.styleId === this.selectedStyleName ? 'style_' + val.id : val.styleId
      //     }
      //   })
      //   mapObj.globalRouteListPolylineLayer.setGeometries(routeLines)
      // }
    },
    handleRoutePlanClick(e) {
      //切换高亮
      let routeLines = mapObj.globalRoutePlanPolylineLayer.getGeometries()
      const chooseIndex = routeLines.findIndex(val => val.id === e.geometry.id)
      const highlightIndex = routeLines.findIndex(val => val.styleId === this.selectedStyleName)
      if (chooseIndex !== highlightIndex) {
        let chooseStyle = routeLines[chooseIndex].styleId
        let chooseRank = routeLines[chooseIndex].rank
        routeLines[chooseIndex].styleId = routeLines[highlightIndex].styleId
        routeLines[chooseIndex].rank = routeLines[highlightIndex].rank
        routeLines[highlightIndex].styleId = chooseStyle
        routeLines[highlightIndex].rank = chooseRank
        mapObj.globalRoutePlanPolylineLayer.setGeometries(routeLines)
      }
    },
    setRoutePointMarkers(routePointList) {
      this.removeRoutePointMarkers()
      const markers = routePointList.map((val, index) => {
        let point = {
          id: 'routePoint-' + val.id,
          style: {
            backgroundColor: '#e6a23c'
          },
          position: latLngPoint({ lat: val.lat, lng: val.lng }),
          content: index
        }
        if (index === 0) {
          point.style.src = require('@/assets/images/mapIcon/start.png')
        } else if (index === routePointList?.length - 1) {
          point.style.src = require('@/assets/images/mapIcon/end.png')
        } else {
          point.style.src = require('@/assets/images/mapIcon/selectIntersection.png')
        }
        return point
      })
      // 开始点结束点
      mapObj.globalMarker = initMarkers(mapObj.globalMap, [markers[0], markers[markers?.length - 1]])
      // 途经点
      mapObj.globalMultiLabel = initMultiLabel(mapObj.globalMap, markers.slice(1, markers?.length - 1))
    },
    removeRoutePointMarkers() {
      const removeIds = this.clearRoutePoints.map(val => val.id)
      removeGeo(mapObj.globalMarker, removeIds)
    },
    getRouteCenter(item) {
      // 重置途经点高亮
      this.currentPassMarkerId = ''
      this.clearObjects(mapObj.globalMultiLabel, mapObj.globalMarker)
      let routeLines = mapObj.globalRouteListPolylineLayer.getGeometries()

      this.currentRouteKey = item.id === this.currentRouteKey ? '' : item.id

      routeLines.forEach(val => {
        if (val.id === item.id) {
          if (val.styleId === this.selectedStyleName) {
            val.styleId = 'style_' + val.id
          } else {
            val.styleId = this.selectedStyleName
            this.setRoutePointMarkers(item.markers)
          }

          //置于屏幕中心
          if (val.paths?.length) {
            const fitViewList = [val.paths[0], val.paths[val.paths?.length - 1]]
            showFitView(fitViewList, mapObj.globalMap)
            // goCenter([routeLines[chooseIndex].paths[0].lng, routeLines[chooseIndex].paths[0].lat], mapObj.globalMap)
          }
        } else {
          val.styleId = val.styleId === this.selectedStyleName ? 'style_' + val.id : val.styleId
        }
      })
      mapObj.globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    removeRoute(route) {
      this.currentRouteKey = ''
      this.clearObjects(mapObj.globalMultiLabel, mapObj.globalMarker)
      //切换高亮
      let routeLines = mapObj.globalRouteListPolylineLayer.getGeometries()

      routeLines.forEach(val => {
        if (val.id === route.id) {
          val.styleId = val.styleId === this.unSelectedStyleName ? this.selectedStyleName : this.unSelectedStyleName
        } else {
          val.styleId = val.styleId === this.selectedStyleName ? 'style_' + val.id : val.styleId
        }
      })

      const deleteIndex = this.routeList.findIndex(val => val.id === route.id)
      this.routeList.splice(deleteIndex, 1)
      mapObj.globalRouteListPolylineLayer.setGeometries(routeLines)
    },
    getFilesPreview(row) {
      const _this = this
      if (row.checkedRoutes?.length) {
        row.routes.push(...row.checkedRoutes)
      }
      if (row?.routes?.length) {
        const promises = row?.routes.map(val =>
          getFilesPreview({
            path: val.attachment,
            token: util.getToken()
          }).then(res => _this.readFile(res))
        )
        Promise.all(promises).then(res => {
          let routes, checkedRoutes
          if (row.checkedRoutes?.length) {
            checkedRoutes = JSON.parse(res[res?.length - 1])
            routes = res.map(val => JSON.parse(val)).flat(2)
          } else {
            routes = res.map(val => JSON.parse(val)).flat(2)
          }
          this.allRouteList = routes
          if (this.allRouteList?.length) {
            this.drawRouteListLines(this.allRouteList)
            this.leftHidden = true
          }
          if (checkedRoutes?.length) {
            this.setRouteChecked(checkedRoutes)
            this.getRouteCenter(checkedRoutes[0])
          } else {
            if (routes?.length) {
              this.routeList = routes
              this.getRouteCenter(routes[0])
            }
          }
        })
      } else {
        this.leftHidden = false
      }
    },
    readFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = event => {
          // 获取文件的字节数据
          resolve(event.target.result)
        }
        reader.onerror = event => {
          reject(error)
        }
        reader.readAsText(file) // 使用readAsText方法读取文件内容
      })
    },
    toggleLeft() {
      this.leftHidden = !this.leftHidden
    },
    toggleRight() {
      this.rightHidden = !this.rightHidden
    },
    togglePassPoints(route) {
      if (route.markers?.length > 2) {
        route.togglePassPointsFlag = !route.togglePassPointsFlag
      }
    },

    submitIntersection() {
      const intersectionRecommendList = this.intersectionRecommendList[0]
      if (intersectionRecommendList?.id) {
        updateDaqTaskRecommends(intersectionRecommendList).then(res => {
          showToast('保存成功')
          this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      } else {
        daqTaskRecommends(intersectionRecommendList).then(res => {
          this.intersectionRecommendList[0].id = res.data.id
          showToast('保存成功')
          this.baseIntersectionRecommendList = JSON.parse(JSON.stringify(this.intersectionRecommendList))
        })
      }
    },
    clearIntersection() {
      this.intersectionChecked = false
      // this.queryIntersectionList = []
      this.intersectionRecommendList[0].recommendItems = []
      this.baseCheckedIntersectionList = []
      // this.baseIntersectionRecommendList = []

      this.changeCheckAll(false)
    },
    clickMarker(obj) {
      this.intersectionChecked = false
      let intersectionIndex = -1
      if (this.intersectionRecommendList?.length) {
        this.intersectionRecommendList[0].recommendItems = this.intersectionRecommendList[0].recommendItems || []
        intersectionIndex = this.intersectionRecommendList[0].recommendItems?.findIndex(val => {
          return val.itemKey === obj.itemKey
        })
      } else {
        this.intersectionRecommendList.push({
          type: 'INTERSECTION',
          taskId: this.form.id,
          taskCode: this.form.code,
          recommendItems: []
        })
      }
      if (!~intersectionIndex) {
        this.currentMarkerKey = obj.itemKey
        this.intersectionRecommendList[0].recommendItems.unshift(obj)
      } else {
        this.currentMarkerKey = null
        this.intersectionRecommendList[0].recommendItems.splice(intersectionIndex, 1)
      }
    },
    clickLabel(obj) {
      const postData = {
        type: this.dialogStatus,
        data: {
          locationId: obj.itemKey,
          type: obj.itemType,
          locationName: obj.itemName
        }
      }
      this.$emit('show-task-record', postData)
    },
    getMapCenter(item) {
      this.currentMarkerKey = item.itemKey
      this.$refs.TXMapIntersection.goCenter(item)
    },
    query() {
      const postData = {
        cantonCode: this.form.cantonCode[this.form.cantonCode?.length - 1],
        intersectionName: this.form.intersectionName,
        supplierCode: this.form.supplierCode,
        crs: this.form.crs,
        collected: this.form.intersectionCollected
      }
      if (postData.cantonCode || postData.intersectionName || postData.supplierCode) {
        this.getIntersectionList(postData)
      } else {
        showToast(this.$t('请先选择区域、供应商或者关键字'), 'warning')
      }
    },
    refresh() {
      this.getIntersectionList()
    },
    listSupplier() {
      if (!this.supplierList?.length) {
        listSysRoleOrg({ tagCodeList: 'org_supplier' }).then(res => {
          this.supplierList = res.data
        })
      }
    },
    changeCheckAll(e) {
      // this.baseIntersectionRecommendList
      // this.intersectionRecommendList = e ? JSON.parse(JSON.stringify(this.baseIntersectionRecommendList)) : []
      this.intersectionRecommendList = JSON.parse(JSON.stringify(this.baseIntersectionRecommendList))
      const list = (
        this.queryIntersectionList?.length ? this.queryIntersectionList : this.baseCheckedIntersectionList
      ).map(val => {
        if (this.queryIntersectionList?.length && e) {
          // this.clickMarker(val)
          this.clickMarkerNoClear(val)
        }
        val.checked = e
        return val
      })
      let postData = {
        list,
        type: 'area',
        disabled: this.disabled
      }
      this.$refs.TXMapIntersection.show(postData)
    },
    clickMarkerNoClear(obj) {
      let intersectionIndex = -1
      if (this.intersectionRecommendList?.length) {
        this.intersectionRecommendList[0].recommendItems = this.intersectionRecommendList[0].recommendItems || []
        intersectionIndex = this.intersectionRecommendList[0].recommendItems?.findIndex(val => {
          return val.itemKey === obj.itemKey
        })
      } else {
        this.intersectionRecommendList.push({
          type: 'INTERSECTION',
          taskId: this.form.id,
          taskCode: this.form.code,
          recommendItems: []
        })
      }
      if (!~intersectionIndex) {
        this.currentMarkerKey = obj.itemKey
        this.intersectionRecommendList[0].recommendItems.unshift(obj)
      } else {
        this.currentMarkerKey = null
        // this.intersectionRecommendList[0].recommendItems.splice(intersectionIndex, 1)
      }
    },
    getSysCantonTree() {
      if (!this.cantonCodeList?.length) {
        getSysCantonTree().then(res => {
          this.cantonCodeList = res.data
        })
      }
    },
    changeCanton(val) {
      if (val?.length) {
        this.getIntersectionList()
      }
    },
    getIntersectionList(postData) {
      getIntersectionList(postData)
        .then(async res => {
          this.clearObjects(mapObj.globalMarker)
          let list = res.data.map(val => {
            const intersectionIndex = this.markersBoardList?.findIndex(
              intersectionItem => intersectionItem.itemKey === val.id
            )
            return {
              itemType: 'INTERSECTION',
              itemKey: val.id,
              itemName: val.name,
              checked: ~intersectionIndex ? true : false,
              daqTaskCount: val.daqTaskCount,
              geomJson: val.geomJson.coordinates[0],
              // geomCenterPointJson: val.geomCenterPointJson.coordinates
              longitude: val.geomCenterPointJson.coordinates[0],
              latitude: val.geomCenterPointJson.coordinates[1]
            }
          })
          this.mapMarkers = list
          // 自定义点位样式
          const markers = list.map((val, index) => {
            let point = {
              id: val.itemKey,
              style: {
                backgroundColor: '#e6a23c',
                width: 34,
                height: 50,
                anchor: { x: 17, y: 50 }
              },
              styleId: val.checked ? this.selectedStyle : '',
              position: latLngPoint({ lat: val.latitude, lng: val.longitude })
            }
            point.style.src = require('@/assets/images/mapIcon/position.png')
            return point
          })
          // if (this.disabled) {
          mapObj.globalMarker = initMarkers(mapObj.globalMap, markers)
          mapObj.globalMarker.on('click', this.clickHandlerMarkers)
          // } else {
          //   await this.clearMarkers()
          //   await this.changeEditorMode('DRAW')
          //   let markerList = markers.map((val, index) => {
          //     // const item = this.convertorItem(val)
          //     return {
          //       id: val.id, //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          //       style: {
          //         width: 34,
          //         height: 50,
          //         anchor: { x: 17, y: 50 },
          //         src: 'data:image/png;base64,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'
          //       },
          //
          //       position: val.position //点标记坐标位置
          //     }
          //   })
          //   addGeo(mapObj.globalMapEditorMarker, markerList)
          // }
          const fitViewList = markers
            .filter((val, index) => index <= 5)
            .map(val => {
              return val.position
            })
          showFitView(fitViewList, mapObj.globalMap)
          // this.$refs.TXMapIntersection.show(postData)
          // this.queryIntersectionList = list
        })
        .catch(err => {
          // this.$refs.TXMapIntersection.show()
        })
        .finally(() => {
          this.intersectionChecked = false
        })
    },
    clickHandlerMarkers(evt) {
      goCenter([evt.geometry.position.lng, evt.geometry.position.lat], mapObj.globalMap)
      const indexClickMarker = this.mapMarkers.findIndex(val => val.itemKey === evt.geometry.id)
      const indexMarkerBoard = this.markersBoardList.findIndex(
        val => val?.itemKey === this.mapMarkers[indexClickMarker].itemKey
      )
      if (!~indexMarkerBoard) {
        this.currentMarkerKey = this.mapMarkers[indexClickMarker].itemKey
        this.markersBoardList.unshift(this.mapMarkers[indexClickMarker])
      } else {
        this.markersBoardList.splice(indexMarkerBoard, 1)
        // selectedStyle
      }
      mapObj.globalMarker.geometries[indexClickMarker].styleId =
        mapObj.globalMarker.geometries[indexClickMarker].styleId === this.selectedStyle
          ? evt.geometry.id
          : this.selectedStyle
      mapObj.globalMarker.updateGeometries(mapObj.globalMarker.geometries[indexClickMarker])
    },
    setDrawType(drawType) {
      this.drawType = this.drawType === drawType ? '' : drawType
      this.editMap()
    },
    async editMap() {
      if (!this.disabled && this.drawType) {
        // await this.changeEditorMode('DRAW')
        await this.changeEditorMode('DRAW')
        // this.clearPreviousGeometry(activeOverLay)
        mapObj.globalMapEditor.setActiveOverlay(this.drawType)
      } else {
        await this.changeEditorMode(null)
      }
    },
    deleteMapShape() {
      if(mapObj.globalMapEditor.activeMode === TMap.tools.constants.EDITOR_ACTION.INTERACT){
        mapObj.globalMapEditor.delete()
      }
      // this.resetEditorMode()
      // this.clearObjects(mapObj.globalMapEditorPolygon, mapObj.globalMapEditorCircle, mapObj.globalMapEditorEllipse, mapObj.globalMapEditorRectangle)
    },
    addPoi() {
      if(this.activeMarkerList?.length){
        this.markersBoardList = JSON.parse(JSON.stringify(this.activeMarkerList))
      } else {
        showToast('请先选择poi点位', 'warning')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.map-selection-container.intersection {
  width: 100%;
  height: 100%;
  display: flex;

  .map-content {
    position: relative;
    height: 100%;
    width: calc(100% - 250px);

    .map-overview {
      user-select: none;
      height: 100%;
      width: 100%;
      z-index: 0;
    }

    .query-param {
      width: 100%;
      display: flex;
      z-index: 2;
      position: absolute;
      left: 0;
      top: 0;

      &.tools {
        top: 40px;
      }

      .label {
        display: flex;
        align-items: center;
        white-space: nowrap;

        .ltw-icon {
          transition: all 0.3s;
        }

        .visible {
          transform: rotateZ(180deg);
        }
      }

      .form {
        display: flex;
        align-items: center;
        transform-origin: 0 50%;
        transform: scaleX(0);
        //width: 0;
        //opacity: 0;
        transition: all 0.3s;

        &.visible {
          transform: scaleX(1);
          //transform-origin: left center;
          //opacity: 1;
          //width: auto;
        }

        .intersection-check {
          margin-left: 6px;
        }

        .intersection-name {
          width: 200px;
        }

        .supplier-code {
          width: 150px;
        }

        .tool-item {
          width: 40px;
          height: 40px;
          margin: 2px;
          padding: 4px;
          border-radius: 3px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          box-shadow: 0 1px 2px 0 #e4e7ef;
          background-color: #ffffff;
          border: 1px solid #ffffff;
          cursor: pointer;

          &.active {
            border-color: #d5dff2;
            background-color: #d5dff2;
          }

          &:hover {
            border-color: #789cff;
          }
        }

        .polygon {
          background-image: url('@/assets/images/mapIcon/polygon.png');
        }

        .circle {
          background-image: url('@/assets/images/mapIcon/circle.png');
        }

        .ellipse {
          background-image: url('@/assets/images/mapIcon/ellipse.png');
        }

        .rectangle {
          background-image: url('@/assets/images/mapIcon/rectangle.png');
        }

        .delete {
          background-image: url('@/assets/images/mapIcon/delete.png');
        }
      }
    }
  }

  .choosed-intersection {
    width: 250px;
    margin-left: 10px;

    :deep(.el-card__body) {
      height: 100%;
    }

    .body {
      height: calc(100% - 32px);
    }

    .intersection-item {
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        border-color: #fab6b6;
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: end;
    align-items: center;

    .intersection-num {
      margin-right: 10px;
    }
  }
  .btn-add-poi{
    position: absolute;
    right: 20px;
    top: 0;
  }
  .icon-add-poi{
    transform: rotateZ(90deg);
  }
}
</style>
