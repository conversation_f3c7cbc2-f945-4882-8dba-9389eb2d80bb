<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    @close="dialogClosed"
    @open="dialogOpened"
    width="800px"
    class="api-detail"
  >
    <!-- :disabled="formReadonly" -->
    <el-form
      class="api-form"
      :model="form"
      :rules="formRules"
      ref="formRef"
      :disabled="formReadonly"
      label-position="top"
    >
      <el-row :gutter="60">
        <el-col :span="12">
          <el-form-item :label="$t('申请级别')" prop="roleType">
            <ltw-input v-model="form.roleType" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('申请应用')" prop="applicationName">
            <ltw-input v-model="form.applicationName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item :label="$t('申请原因')" prop="reason">
        <ltw-input v-model="form.reason" type="textarea" />
      </el-form-item>
      <el-card>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('模块 / API名称')" prop="module">
              <ltw-input
                v-model="form.apiName"
                :placeholder="$t('请输入API名称')"
              >
                <template #prepend>
                  <el-select
                    style="width: 110px"
                    v-model="form.apiModule"
                    :placeholder="$t('选择模块')"
                  >
                    <el-option
                      v-for="item in apiMenuList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </ltw-input>
            </el-form-item>
          </el-col> </el-row
        ><el-row>
          <el-col :span="24">
            <el-form-item :label="$t('API地址')" prop="url">
              <ltw-input v-model="form.url" :placeholder="$t('请输入API地址')">
                <template #prepend>
                  <el-select v-model="form.requestType" style="width: 110px">
                    <el-option
                      v-for="item in requestTypes"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                  <el-select
                    v-model="form.httpType"
                    style="width: 110px"
                    @change="changeHttpType"
                  >
                    <el-option
                      v-for="item in httpTypes"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </template>
              </ltw-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('API描述')" prop="description">
              <ltw-input
                v-model="form.description"
                :placeholder="$t('请输入API描述')"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-collapse v-model="activeNames">
          <el-collapse-item :title="$t('请求参数')" name="1">
            <el-row>
              <el-col :span="24">
                <!-- <div class="item-title">{{ $t('请求参数') }}</div> -->

                <el-tabs
                  v-model="form.paramPaneType"
                  class="demo-tabs"
                  :disabled="formReadonly"
                >
                  <el-tab-pane
                    v-if="form.httpType === 'post' || form.httpType === 'put'"
                    :label="$t('请求体')"
                    name="body"
                  >
                    <el-radio-group v-model="form.requestParamType">
                      <el-radio
                        v-for="item in paramTypes"
                        :label="item.code"
                        :key="item.code"
                        >{{ item.name }}</el-radio
                      >
                    </el-radio-group>
                  </el-tab-pane>
                  <el-tab-pane
                    v-if="form.httpType === 'get' || form.httpType === 'delete'"
                    :label="$t('Query参数')"
                    name="query"
                  >
                  </el-tab-pane>
                </el-tabs>
                <el-table
                  :data="form.requestParam"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                  border
                  default-expand-all
                >
                  <el-table-column
                    prop="paramName"
                    :label="$t('参数名')"
                    width="180"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="paramType"
                    :label="$t('类型')"
                    width="180"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="require"
                    :label="$t('必填')"
                    width="80"
                  >
                  </el-table-column>
                  <el-table-column prop="description" :label="$t('说明')">
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item :title="$t('响应内容')" name="2">
            <el-row>
              <el-col :span="24">
                <!-- <div class="item-title">{{ $t('响应内容') }}</div> -->

                <el-tabs
                  v-model="form.resultPaneType"
                  class="demo-tabs"
                  :disabled="formReadonly"
                >
                  <el-tab-pane :label="$t('返回结果')" name="resultBody">
                    <el-radio-group v-model="form.responseType">
                      <el-radio
                        v-for="item in resultTypes"
                        :label="item.code"
                        :key="item.code"
                        >{{ item.name }}</el-radio
                      >
                    </el-radio-group>
                    <el-table
                      :data="form.responseResult"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                      border
                      default-expand-all
                    >
                      <el-table-column
                        prop="paramName"
                        :label="$t('参数名')"
                        width="180"
                      >
                      </el-table-column>
                      <el-table-column
                        prop="paramType"
                        :label="$t('类型')"
                        width="180"
                      ></el-table-column>
                      <el-table-column
                        prop="require"
                        :label="$t('必填')"
                        width="80"
                      ></el-table-column>
                      <el-table-column prop="description" :label="$t('说明')">
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                </el-tabs>
              </el-col> </el-row
          ></el-collapse-item>
        </el-collapse>
      </el-card>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel()" id="cancel">{{ $t('取消') }}</el-button>
        <!-- <el-button type="primary" @click="saveAPI()">{{ $t('保存') }}</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { getApiPrivilegeDetail } from '@/apis/system/api-authorization'
import { getLocale } from '@/plugins/util'

const defaultform = {
  module: '',
  name: '',
  description: '',
  url: '/',
  httpType: 'post',
  requestType: 'http',
  paramPaneType: 'body',
  paramType: 'json',
  paramStructure: [],
  paramDemo: {},
  resultPaneType: 'resultBody',
  resultType: 'json',
  resultDemo: {
    data: {},
    status: 1,
    success: true
  },
  resultStructure: [
    {
      description: '数据主体',
      disabled: true,
      id: '1',
      paramName: 'data',
      paramType: 'object',
      require: true,
      children: []
    },
    {
      description: '请求返回状态',
      disabled: true,
      id: '2',
      paramName: 'status',
      paramType: 'number',
      require: true
    },
    {
      description: '请求成功与否',
      disabled: true,
      id: '3',
      paramName: 'success',
      paramType: 'boolean',
      require: true
    }
  ]
}
export default {
  name: 'ApiDetail',
  data() {
    var validateAPIName = (rule, value, callback) => {
      if (!this.form.module) {
        callback(new Error(this.$t('请选择模块')))
      } else if (!this.form.name) {
        callback(new Error(this.$t('请输入API名称')))
      } else {
        callback()
      }
    }
    return {
      form: Object.assign({}, defaultform),
      formRules: {
        // module: [
        //   { required: true, validator: validateAPIName, trigger: 'blur' }
        // ],
        // url: [
        //   {
        //     required: true,
        //     message: this.$t('请输入API地址'),
        //     trigger: 'blur'
        //   }
        // ],
        // description: [
        //   {
        //     required: true,
        //     message: this.$t('请输入API描述'),
        //     trigger: 'blur'
        //   }
        // ]
      },
      APIFunctionList: [
        {
          id: 1,
          buttonCode: 'api',
          name: '添加API'
        },
        {
          id: 2,
          buttonCode: 'group',
          name: '添加模块'
        }
      ],
      filterText: '',
      defaultProps: { children: 'sysApis', label: 'name' },
      apiMenuList: [],
      requestTypes: [],
      httpTypes: [],
      paramTypes: [
        {
          code: 'form',
          name: 'Form-data'
        },
        {
          code: 'json',
          name: 'JSON'
        }
      ],
      resultTypes: [
        {
          code: 'json',
          name: 'JSON'
        }
      ],
      paramTableData: [],
      paramQueryData: [],
      paramHeaderData: [],
      paramTableItem: {
        paramName: '',
        paramType: 'string',
        require: false,
        description: ''
      },
      resultTableData: [],
      resultTableItem: {
        paramName: '',
        paramType: 'string',
        require: false,
        description: ''
      },
      paramDataTypes: [],
      formReadonly: true,
      jsonLang: getLocale(),
      jsonMode: 'code',
      dialogVisible: false,
      dialogTitle: '',
      activeNames: []

      // props: {
      //   expandTrigger: 'hover',
      //   label: 'name',
      //   value: 'id'
      // },
      // roleOptions: [],
      // applicationOptions: []
    }
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.formReadonly = row.type === 'view' ? true : false
      if (row.id) {
        this.dialogTitle = this.$t('API申请详情')
        this.getApiPrivilegeDetail(row.id)
      } else {
        this.dialogTitle = this.$t('API申请')
        this.form.apiId = row.apiId
      }
      // this.getRoleOptions()
      // this.getApplications()
      // if (!(this.requestTypes && this.requestTypes.length)) {
      //   this.getDictList('request_type').then(res => {
      //     this.requestTypes = res
      //   })
      // }
      // if (!(this.httpTypes && this.httpTypes.length)) {
      //   this.getDictList('http_type').then(res => {
      //     this.httpTypes = res
      //   })
      // }
      // if (!(this.paramDataTypes && this.paramDataTypes.length)) {
      //   this.getDictList('api_data_types').then(res => {
      //     this.paramDataTypes = res
      //   })
      // }
    },
    changeHttpType() {
      if (this.form.httpType === 'get' || this.form.httpType === 'delete') {
        this.form.paramPaneType = 'query'
      } else if (
        this.form.httpType === 'post' ||
        this.form.httpType === 'put'
      ) {
        this.form.paramPaneType = 'body'
      }
    },
    getApiPrivilegeDetail(id) {
      getApiPrivilegeDetail(id).then(res => {
        // 非个人
        // if (res.data.roleType === 'emp') {
        //   res.data.roleId = [res.data.roleId]
        // } else {
        //   res.data.roleId = [res.data.roleType, res.data.roleId]
        // }
        // 格式化响应数据
        let responseResult = JSON.parse(
          JSON.stringify(defaultform.resultStructure)
        )
        responseResult[0].children = JSON.parse(res.data.responseResult)
        res.data.resultPaneType = 'resultBody'
        res.data.responseResult = responseResult

        res.data.requestParam = JSON.parse(res.data.requestParam)
        this.form = res.data
        this.changeHttpType()
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform, module: id }
      // this.resultTableData = [{ ...this.paramTableItem, id: this.uuid() }]
      // this.paramHeaderData = [{ ...this.paramTableItem, id: this.uuid() }]
      // this.paramTableData = [{ ...this.paramTableItem, id: this.uuid() }]
      // this.paramQueryData = [{ ...this.paramTableItem, id: this.uuid() }]
    }
  }
}
</script>

<style scoped lang="scss">
.el-input-group__prepend {
  .el-select {
    margin: 0;
  }
  .el-select:first-child {
    margin-left: -20px;
  }
  .el-select:last-child {
    margin-right: -20px;
  }
}
.api-form {
  :deep(.ltw-input-group__prepend) {
    padding: 0;
    .el-select {
      margin: 0;
    }
  }
  :deep(.el-collapse-item__header){
    padding: 10px 20px;
    border-left: 3px solid rgb(0, 120, 90);
    background: rgb(248, 248, 250);
    margin-top: 20px;
  }
  .el-tabs {
    padding-left: 10px;
  }
  .el-table {
    margin-top: 0;
    .el-link {
      font-size: 12px;
      padding: 0 10px;
      &:not(:last-child) {
        border-right: 1px solid rgb(232, 232, 232);
      }
    }
    :deep(.cell) {
      display: flex;
    }
  }
}
</style>
