# Docker image for arena portal application
# Version 1.0
# Author PMT/DataLoop

### 基础镜像，使用nginx
FROM nginx

#维护者
MAINTAINER PMT/DataLoop

#修改时区为东八
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && apt install -y tzdata \
    && dpkg-reconfigure -f noninteractive tzdata

WORKDIR /app/jcdc-portal
USER root

#系统编码
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8

#复制Nignx配置文件和前端页面到容器中
COPY jcdc/ /usr/share/nginx/html/olympus/jcdc/chenghuang/
COPY portal.conf /etc/nginx/conf.d/default.conf

EXPOSE 8193

