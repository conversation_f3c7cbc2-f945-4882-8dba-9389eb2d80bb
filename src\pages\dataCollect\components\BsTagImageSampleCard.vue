<template>
  <el-card class="vehicle-card">
    <div class="card-content">
      <div class="tag-img" @click="getPreview">
        <el-image :src="imgUrl" fit="cover">
          <template #error>
            <div class="image-slot">
              <ltw-icon icon-code="el-icon-picture"></ltw-icon>
            </div>
          </template>
        </el-image>
      </div>
      <div class="tag-content">
        <div class="tag-name">
          {{ imgInfo.name }}
        </div>
        <div class="tag-type">
          <span class="tag-btn">
            <ltw-icon v-if="imgInfo.sourceType === 'DATA_PLATFORM'" icon-code="el-icon-monitor" />
            <ltw-icon v-if="imgInfo.sourceType === 'CHENGHUANG_APP'" icon-code="el-icon-iphone" />
            <span>{{ imgInfo.sourceTypeName }}</span>
          </span>
        </div>
        <div class="tag-description">
          <el-tooltip effect="dark" :content="imgInfo.description">
            {{ imgInfo.description }}
          </el-tooltip>
        </div>
      </div>
    </div>
    <el-image-viewer v-if="dialogImgVisible" @close="closeViewer" :url-list="srcList" teleported />
  </el-card>
</template>

<script>
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  name: 'ImageSampleCard',
  data() {
    return {
      dialogImgVisible: false,
      srcList: [],
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/preview',
      token: '?token=' + util.getToken()
    }
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    imgInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    imgUrl() {
      return this.imgInfo.filePath ? this.downloadUrl + this.token + '&path=' + this.imgInfo.filePath : ''
    }
  },
  methods: {
    getPreview() {
      this.srcList = [this.imgUrl]
      this.dialogImgVisible = true
    },
    closeViewer() {
      this.dialogImgVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.vehicle-card {
  position: relative;

  :deep(.el-card__header) {
    padding: 4px 10px;
  }

  :deep(.el-card__body) {
    padding: 0;
  }

  .el-tag {
    cursor: pointer;
  }

  .card-header {
    display: flex;
    justify-content: space-between;

    .tags {
      display: flex;

      .title {
        margin-right: 10px;
      }
    }

    .title {
      font-size: 12px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;

      &.external-vin {
        border: 1px solid rgb(16, 41, 106);
        background-color: #c6e2ff;
        color: #000;
      }
    }
  }

  .card-content {
    .tag-img {
      overflow: hidden;
      width: 100%;
      position: relative;

      img,
      .el-image {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 174px;
        //height: 100%;
        width: 100%;
        font-size: 30px;
        // background: var(--el-fill-color-light);
        color: var(--el-text-color-secondary);

        .image-slot {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: var(--el-fill-color-light);
          color: var(--el-text-color-secondary);
        }
      }
    }

    .tag-content {
      padding: 15px;
      height: 140px;

      .tag-name {
        color: #2e3033;
        font-size: 13px;
        font-weight: 600;
        line-height: 16px;
      }

      .tag-type {
        margin: 10px 0;
        display: flex;
        align-items: center;

        .tag-btn {
          display: flex;
          align-items: center;
          padding: 0 8px;
          background: rgba(86, 176, 255, 0.15);
          border-radius: 17px 17px 17px 17px;
          font-size: 12px;
          color: #007bc0;
          line-height: 20px;
          white-space: nowrap;
        }

        .ltw-icon {
          font-size: 12px;
          margin-right: 6px;
          padding-top: 3px;
        }
      }

      .tag-description {
        margin-top: 6px;
        font-size: 12px;
        color: #8a9097;
        line-height: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        height: 40px;
      }
    }

    .tag-img-btn {
      position: absolute;
      right: 0;
      bottom: -6px;
      text-align: right;

      .el-link:not(:last-child) {
        margin-right: 10px;
      }
    }
  }
}
</style>
