<template>
  <div class="add-fleet-mangement">
    <el-page-header class="page-header" @back="onBack">
      <template #icon>
        <ltw-icon icon-code="el-icon-arrow-left"></ltw-icon>
      </template>
      <template #breadcrumb />
      <template #title>{{ $t('返回') }}</template>
      <template #content>{{ $t('车辆详情') }}</template>
    </el-page-header>
    <div class="add-fleet-body">
      <el-card shadow="never" class="fleet-model-card">
        <el-row>
          <div class="left">
            <el-scrollbar>
              <el-menu :default-active="activeMenuIndex" class="el-menu-vertical-demo" @select="handleMenuSelect">
                <el-menu-item index="1">
                  <span>{{ $t('基本信息') }}</span>
                </el-menu-item>
                <el-menu-item index="2">
                  <span>{{ $t('装车记录') }}</span>
                </el-menu-item>
                <el-menu-item index="3">
                  <span>{{ $t('标定参数') }}</span>
                </el-menu-item>
<!--                <el-menu-item index="4">-->
<!--                  <span>{{ $t('DBC文件') }}</span>-->
<!--                </el-menu-item>-->
                <el-menu-item index="5">
                  <span>{{ $t('保险临牌') }}</span>
                </el-menu-item>
                <el-menu-item index="6">
                  <span>{{ $t('软件版本') }}</span>
                </el-menu-item>
                <el-menu-item index="7">
                  <span>{{ $t('对手件') }}</span>
                </el-menu-item>
              </el-menu>
            </el-scrollbar>
          </div>
          <div class="right">
            <el-scrollbar>
              <el-form :model="baseInfo" ref="formRefVehicle" label-position="top" v-show="activeMenuIndex == 1">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="$t('车架号')" prop="vin">
                      <el-tag>{{ baseInfo.vin || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('外部编号')" prop="externalVin">
                      <el-tag>{{ baseInfo.externalVin || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('负责人')" prop="keeperEmpId">
                      <el-tag>{{ baseInfo.keeperEmpName || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('车牌')" prop="license">
                      <el-tag>{{ baseInfo.license || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('出厂车架号')" prop="factoryVin">
                      <el-tag>{{ baseInfo.factoryVin || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('轴距') + '(' + $t('毫米') + ')'" prop="wheelBase">
                      <el-tag>{{ baseInfo.wheelBase || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('后轴中心到车辆几何中心')" prop="egoCenterShiftDistance">
                      <el-tag>{{ (baseInfo.egoCenterShiftDistance || '-') + $t('米') }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('采集车辆宽度（考虑反光镜）')" prop="includeRearviewMirrorWidth">
                      <el-tag>{{ (baseInfo.includeRearviewMirrorWidth || '-') + $t('米') }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('采集车辆宽度（不考虑反光镜）')" prop="excludeRearviewMirrorWidth">
                      <el-tag>{{ (baseInfo.excludeRearviewMirrorWidth || '-') + $t('米') }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('车辆质量重心到车头的距离')" prop="toHeadstockDistance">
                      <el-tag>{{ (baseInfo.toHeadstockDistance || '-') + $t('米') }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('车辆质量重心到车尾的距离')" prop="toTailstockDistance">
                      <el-tag>{{ (baseInfo.toTailstockDistance || '-') + $t('米') }}</el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('CAN通信矩阵')" prop="canMatrix">
                      <el-tag>{{ baseInfo.canMatrix || '-' }}</el-tag>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item :label="$t('照片')" prop="photo">
                  <upload-file
                    ref="uploadImage"
                    source-type="vehicle_image"
                    :limit="1"
                    accept=".jpg,.jpeg,.png,.gif"
                    :source-id="baseInfo.id"
                    v-model="baseInfo.photo"
                    id="photo"
                    disabled
                  />
                </el-form-item>
              </el-form>
              <div class="fleet-version" v-show="activeMenuIndex == 2">
                <el-scrollbar>
                  <div class="empty-content" v-if="!recordList?.length">
                    <el-empty description="暂无数据"></el-empty>
                  </div>
                  <div class="fleet-version-content" v-else>
                    <div class="version-list">
                      <el-steps :active="activeStepRecord" process-status="finish" finish-status="wait" align-center>
                        <template v-for="(item, index) in recordList" :key="index">
                          <el-step
                            @click="changeRecord(item, index)"
                            :title="item.code + '(' + item.version + ')'"
                            :description="item.startTime"
                          />
                        </template>
                      </el-steps>
                    </div>
                    <div class="step-content">
                      <el-tabs v-model="activeStep" @tab-change="getModalityList">
                        <el-tab-pane :label="$t('版本信息')" :name="0"></el-tab-pane>
                        <el-tab-pane :label="$t('传感器信息')" :name="1"></el-tab-pane>
                      </el-tabs>
                    </div>
                    <div class="version-detail">
                      <div class="step1" v-show="activeStep === 0">
                        <div class="preview-list">
                          <el-tag
                            v-if="installationRecord.statusName"
                            :type="installationRecord.status === 'draft' ? 'warning' : 'success'"
                          >
                            {{ installationRecord.statusName }}
                          </el-tag>
                          <el-tooltip effect="dark" :content="$t('预览')" placement="top" :enterable="false">
                            <el-button size="small" type="primary" plain @click="previewFile()">
                              <ltw-icon icon-code="el-icon-document"></ltw-icon>
                            </el-button>
                          </el-tooltip>
                        </div>
                        <el-form
                          class="form-record"
                          :model="installationRecord"
                          ref="formRef"
                          label-width="100px"
                          hide-required-asterisk
                        >
                          <el-form-item :label="$t('车型版本')" prop="version">
                            <el-tag>{{ installationRecord.code + '(' + installationRecord.version + ')' }}</el-tag>
                          </el-form-item>
                          <el-form-item :label="$t('开始时间')" prop="startTime">
                            <el-tag>{{ installationRecord.startTime }}</el-tag>
                          </el-form-item>
                          <el-form-item :label="$t('结束时间')" prop="endTime">
                            <el-tag>{{ installationRecord.endTime || '-' }}</el-tag>
                          </el-form-item>
                          <el-form-item v-if="installationRecord.releaseTime" :label="$t('发布时间')" prop="releaseTime">
                            <el-tag>{{ installationRecord.releaseTime || '-' }}</el-tag>
                          </el-form-item>
                          <el-form-item :label="$t('备注')" prop="remark">
                            <ltw-input
                              :disabled="installationRecord.status === 'released'"
                              v-model="installationRecord.description"
                              textType="remark"
                              type="textarea"
                            ></ltw-input>
                          </el-form-item>
                        </el-form>
                      </div>
                      <div class="step1" v-show="activeStep === 1">
                        <el-table
                          :row-class-name="setClassName"
                          :data="installationRecord.versionMappingModalityVOS"
                          ref="tableRef"
                        >
                          <el-table-column type="expand" fixed="left">
                            <template #default="props">
                              <el-form class="expand-form" ref="formRef" label-width="140px">
                                <el-row :gutter="10">
                                  <el-col :span="8">
                                    <el-form-item :label="$t('分辨率')">
                                      {{ props.row.cameraVO?.resolution }}
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="8">
                                    <el-form-item :label="$t('畸变模型')">
                                      {{ props.row.cameraVO?.distortionModel }}
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="8">
                                    <el-form-item :label="$t('相邻两行曝光间隔')">
                                      {{ props.row.cameraVO?.exposureInterval }}us
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="8">
                                    <el-form-item :label="$t('水平视角')">
                                      {{ props.row.cameraVO?.hfov }}
                                    </el-form-item>
                                  </el-col>
                                  <el-col :span="8">
                                    <el-form-item :label="$t('垂直视角')">
                                      {{ props.row.cameraVO?.vfov }}
                                    </el-form-item>
                                  </el-col>
                                </el-row>
                              </el-form>
                            </template>
                          </el-table-column>
                          <el-table-column type="index" width="50" fixed="left" />
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('编码')"
                            prop="modality"
                            show-overflow-tooltip
                            width="150"
                            fixed="left"
                          >
                          </el-table-column>
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('传感器')"
                            show-overflow-tooltip
                            width="130"
                            prop="modalityName"
                          />
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('传感器类型')"
                            prop="sensorTypeName"
                          />
                          <el-table-column header-align="left" align="left" :label="$t('供应商')" prop="supplierName" />
                          <el-table-column header-align="left" align="left" :label="$t('型号')" prop="model" />
                          <el-table-column header-align="left" align="left" :label="$t('规格')" prop="specification" />
                          <el-table-column header-align="left" align="left" :label="$t('msop')" prop="msop" />
                          <el-table-column header-align="left" align="left" :label="$t('difop')" prop="difop" />
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('帧同步偏移值' + '(ms)')"
                            prop="pcdJpgOffset"
                          />
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('间隔差的基准值') + '(ms)'"
                            prop="intervalDif"
                          />
                          <el-table-column
                            header-align="left"
                            align="left"
                            :label="$t('备注')"
                            show-overflow-tooltip
                            prop="remark"
                          />
                          <el-table-column
                            header-align="left"
                            align="left"
                            fixed="right"
                            :label="$t('操作')"
                            width="190"
                          >
                            <template #default="scope">
                              <el-button-group>
                                <el-popover placement="left" width="400" trigger="hover">
                                  <template #reference>
                                    <el-button
                                      v-if="scope.row.files?.length"
                                      text
                                      type="primary"
                                      @click="showModalityUploadDialog(scope.row)"
                                    >
                                      <!-- <ltw-icon icon-code="el-icon-upload"></ltw-icon> -->
                                      <ltw-icon icon-code="el-icon-paperclip"></ltw-icon>
                                    </el-button>
                                  </template>
                                  <el-table :data="scope.row.files">
                                    <el-table-column width="120" prop="sourceType" label="sourceType"></el-table-column>
                                    <el-table-column prop="fileName" label="fileName">
                                      <template #default="scope">
                                        <el-link target="_blank" underline :href="scope.row.url">
                                          <span style="display: inline">
                                            <span>{{ scope.row.fileName }}</span>
                                            <span v-if="scope.row.fileType">.{{ scope.row.fileType }}</span>
                                          </span>
                                        </el-link>
                                      </template>
                                    </el-table-column>
                                  </el-table>
                                </el-popover>
                                <el-tooltip effect="dark" :content="$t('查看详情')" placement="top" :enterable="false">
                                  <el-button text type="primary" @click="viewModality(scope.row)">
                                    <ltw-icon icon-code="el-icon-view"></ltw-icon>
                                  </el-button>
                                </el-tooltip>
                              </el-button-group>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
              <div v-show="activeMenuIndex === '3'">
                <el-scrollbar>
                  <calibration-config-record ref="CalibrationConfigRecord" @reload="getCalibration()" />
                </el-scrollbar>
              </div>
              <div v-show="activeMenuIndex === '4'">
                <el-scrollbar>
                  <DbcConfigRecord ref="DbcConfig"></DbcConfigRecord>
                </el-scrollbar>
              </div>
              <div v-show="activeMenuIndex === '5'">
                <el-scrollbar>
                  <div class="empty-content" v-if="!insuranceRecord?.length">
                    <el-empty description="暂无数据"></el-empty>
                  </div>
                  <div v-else>
                    <insurance-record ref="insuranceRecord"></insurance-record>
                  </div>
                </el-scrollbar>
              </div>
              <div v-show="activeMenuIndex === '6'">
                <el-scrollbar>
                  <div class="empty-content" v-if="!toolVersionRecord?.length && !softVersionRecord?.length">
                    <el-empty description="暂无数据"></el-empty>
                  </div>
                  <div v-else>
                    <software-version-info ref="softwareRef"></software-version-info>
                  </div>
                </el-scrollbar>
              </div>
              <div v-show="activeMenuIndex === '7'">
                <el-scrollbar>
                  <CompetitorTimeline ref="CompetitorRef"></CompetitorTimeline>
                </el-scrollbar>
              </div>
            </el-scrollbar>
          </div>
        </el-row>
      </el-card>
    </div>
  </div>
  <PreviewFile ref="PreviewFile" @reload="listFtmVehicleInstallationRecord"></PreviewFile>
  <ChooseModality ref="ChooseModality" @reload="listFtmVehicleMappingModality"></ChooseModality>
  <ChooseFleetVersion ref="ChooseFleetVersion" @reload="loadVersion"></ChooseFleetVersion>
  <AddFleetModality ref="AddFleetModality" @reload="loadFleetModality" />
  <modality-file-upload-dialog ref="ModalityFileUploadDialog" />
</template>

<script>
import { getBsVehicle } from '@/apis/fleet/bs-vehicle'
import { listFtmVehicleMappingModality } from '@/apis/fleet/ftm-variant-mapping-modality'
import { listFtmVehicleInstallationRecord } from '@/apis/fleet/ftm-vehicle-installation-record'
import Codemirror from 'codemirror-editor-vue3'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/theme/dracula.css'
import ChooseModality from '@/pages/fleet/dialog/ChooseModality.vue'
import AddFleetModality from '@/pages/fleet/dialog/AddFleetModality.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import UploadFile from '@/components/system/UploadFile.vue'
import ChooseFleetVersion from '@/pages/fleet/dialog/ChooseFleetVersion.vue'
import AddModality from '@/pages/fleet/dialog/AddModality.vue'
import PreviewFile from '@/pages/fleet/dialog/PreviewFile.vue'
import ModalityFileUploadDialog from '@/pages/fleet/dialog/ModalityFileUploadDialog.vue'
import CalibrationConfigRecord from '@/pages/fleet/components/CalibrationConfigRecord.vue'
import { listFtmVehicleInsurance } from '@/apis/fleet/ftm-vehicle-insurance'
import { listFtmVehicleCompetitor } from '@/apis/fleet/ftm-spare-part'
import InsuranceRecord from '@/pages/fleet/components/InsuranceRecord.vue'
import SoftwareVersionInfo from '@/pages/fleet/components/SoftwareVersionInfo.vue'
import DbcConfigRecord from '@/pages/fleet/components/DbcConfigRecord.vue'
import { listSoftwareVersions } from '@/apis/fleet/ftm-software-versions'
import CompetitorTimeline from '@/pages/fleet/dialog/CompetitorTimeline.vue'

export default {
  components: {
    SoftwareVersionInfo,
    InsuranceRecord,
    CalibrationConfigRecord,
    ChooseModality,
    Codemirror,
    EmployeeSelection,
    UploadFile,
    ChooseFleetVersion,
    AddModality,
    PreviewFile,
    AddFleetModality,
    ModalityFileUploadDialog,
    DbcConfigRecord,
    CompetitorTimeline
  },
  name: 'ViewFleetManagement',
  data() {
    return {
      activeMenuIndex: '1',
      vehicleId: '',
      baseInfo: {},
      backupbaseInfo: {},
      backupinstallationRecord: {},
      installationRecord: {},
      recordList: [],
      activeStep: 0,
      activeStepRecord: '',
      insuranceRecord: [],
      competitorRecord: [],
      toolVersionRecord: [],
      softVersionRecord: [],
      type: '',
      versionPageData: [],
      //新增车型版本
      collapsed: false
    }
  },
  created() {
    let routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.vehicleId = this.$route.query.id
    this.getBsVehicle(this.$route.query.id)
  },
  methods: {
    getDbcFile() {
      this.$refs.DbcConfig.show({ vin: this.baseInfo.vin })
    },
    getBsVehicle(id) {
      getBsVehicle(id).then(res => {
        this.baseInfo = res.data
        this.backupbaseInfo = res.data
      })
    },
    handleMenuSelect(index) {
      this.activeMenuIndex = index
      this.getMenuInfo()
    },
    getMenuInfo() {
      if (this.activeMenuIndex == 1) {
        this.getBsVehicle(this.vehicleId)
        return
      }
      if (this.activeMenuIndex == 2) {
        this.getInstallationRecord()
        return
      }
      if (this.activeMenuIndex == 3) {
        this.getCalibration()
        return
      }
      if (this.activeMenuIndex == 4) {
        this.getDbcFile()
        return
      }
      if (this.activeMenuIndex == 5) {
        this.getInsurance()
        return
      }
      if (this.activeMenuIndex == 6) {
        this.getSoftwareVersion()
        return
      }
      if (this.activeMenuIndex == 7) {
        this.getCompetitorList()
        return
      }
    },
    getInstallationRecord() {
      this.listFtmVehicleInstallationRecord()
    },
    listFtmVehicleInstallationRecord() {
      listFtmVehicleInstallationRecord({ vin: this.baseInfo.vin }).then(res => {
        this.recordList = res.data
        if (res.data?.length) {
          if (this.activeStepRecord === '') {
            this.activeStepRecord = res.data?.length - 1
          }
          this.installationRecord = res.data[this.activeStepRecord]
          this.backupinstallationRecord = res.data[this.activeStepRecord]
          this.getModalityList()
        }
      })
    },
    changeRecord(item, index) {
      this.installationRecord = JSON.parse(JSON.stringify(item))
      this.activeStepRecord = index
      this.getModalityList()
    },
    previousStep() {
      this.activeStep = 0
    },
    next() {
      this.activeStep++
    },
    chooseModality() {
      this.$refs.ChooseModality.show({
        type: 'view',
        data: {
          vehicleId: this.baseInfo.id,
          installationRecordId: this.installationRecord.id,
          variantVersionId: this.installationRecord.variantVersionId,
          variant: this.installationRecord.code
        }
      })
    },
    viewModality(row) {
      this.$refs.AddFleetModality.show({
        type: 'view',
        data: row
      })
    },
    setClassName({ row }) {
      return row.sensorType !== 'camera' ? 'no-expand' : ''
    },
    onBack() {
      this.$router.go(-1)
    },
    chooseFleetVersion() {
      this.$refs.ChooseFleetVersion.show({
        type: 'choose',
        version: this.installationRecord.version
      })
    },
    loadVersion(obj) {
      this.installationRecord.version = obj.version
      this.installationRecord.code = obj.code
      this.installationRecord.variantVersionId = obj.versionId
    },
    loadFleetModality(val) {
      if (val) {
        this.listFtmVehicleMappingModality()
      }
    },
    listFtmVehicleMappingModality() {
      let postData = {
        vehicleId: this.baseInfo.id,
        variantVersionId: this.installationRecord.variantVersionId,
        installationRecordId: this.installationRecord.id
      }
      listFtmVehicleMappingModality(postData).then(res => {
        this.installationRecord.versionMappingModalityVOS = res.data
      })
    },
    getModalityList() {
      if (this.activeStep === 1) {
        this.listFtmVehicleMappingModality()
      }
    },
    previewFile() {
      const btn = this.$route.query.btn ? JSON.parse(decodeURIComponent(this.$route.query.btn)) : ''
      this.$refs.PreviewFile.show({
        type: 'view',
        id: this.installationRecord.id,
        vin: this.baseInfo.vin,
        status: this.installationRecord.status,
        statusName: this.installationRecord.statusName,
        btn: this.installationRecord.status === 'released' && btn
      })
    },
    getBtnList(btnList, code) {
      return !!~btnList.findIndex(val => val.buttonCode === code)
    },
    getCalibration() {
      this.$refs.CalibrationConfigRecord.show({
        type: 'view',
        vin: this.baseInfo.vin
      })
    },
    getInsurance() {
      listFtmVehicleInsurance({ vin: this.baseInfo.vin }).then(res => {
        this.insuranceRecord = res.data
        if (this.insuranceRecord?.length) {
          this.$nextTick(() => {
            this.$refs.insuranceRecord?.show(this.insuranceRecord)
          })
        }
      })
    },
    getCompetitorList() {
      this.$refs.CompetitorRef?.show({ vin: this.baseInfo.vin })
    },
    async getSoftwareVersion() {
      await listSoftwareVersions({ enabled: true, vin: this.baseInfo.vin, type: 'tool' }).then(res => {
        this.toolVersionRecord = res.data
      })
      await listSoftwareVersions({ enabled: true, vin: this.baseInfo.vin, type: 'software' }).then(res => {
        this.softVersionRecord = res.data
      })
      if (this.toolVersionRecord?.length || this.softVersionRecord?.length) {
        this.$nextTick(() => {
          this.$refs.softwareRef.show({
            toolInfoDataList: this.toolVersionRecord,
            softwareDataList: this.softVersionRecord
          })
        })
      }
    },
    showModalityUploadDialog(row) {
      this.$refs.ModalityFileUploadDialog.show({
        id: row.id,
        type: 'view'
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/var.scss';

.add-fleet-mangement {
  display: flex;
  flex-direction: column;
  height: calc(100vh - $header-height - $footer-height - var(--el-main-padding));

  .page-header {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-light);

    :deep(.el-page-header__breadcrumb) {
      margin: 0;
    }

    :deep(.el-page-header__content) {
      font-weight: 600;
    }
  }

  .add-fleet-body {
    display: flex;
    flex-direction: row;
    height: calc(100% - 45px);
    width: 100%;

    .el-scrollbar {
      width: 100%;
    }

    :deep(.el-scrollbar__view) {
      display: flex;
    }

    .fleet-model-card {
      width: 100%;
      border: none;

      .el-row {
        width: 100%;
        height: 100%;

        :deep(.left) {
          width: 150px;
          height: 100%;
          border-right: 1px solid #dcdfe6;

          .el-menu {
            border-right: none;
          }
        }

        :deep(.right) {
          height: 100%;
          width: calc(100% - 200px);
          margin-left: 10px;
        }
      }
    }

    .fleet-model-card {
      height: 100%;
      white-space: nowrap;

      & > :deep(.el-card__header) {
        padding: 10px;
      }
    }

    :deep(.el-card__body) {
      height: 100%;

      .el-form {
        padding: 0 10px;
      }
    }

    :deep(.el-scrollbar__view) {
      flex-direction: column;
    }

    // & > :deep(.el-card__body) {
    //   height: 100%;
    //   display: flex;
    //   flex-direction: column;
    // }
    .card-header {
      display: flex;
      justify-content: space-between;
    }

    .card-collapsed {
      position: relative;
      z-index: 1;

      .ltw-icon {
        cursor: pointer;
        position: relative;
        top: 50%;
        transition: all 0.3s;
        font-size: 12px;
        color: #73767a;

        &.collapsed-icon {
          transform: rotateY(180deg);
        }
      }
    }

    .fleet-version {
      width: 100%;
      position: relative;

      .el-breadcrumb {
        line-height: 30px;

        :deep(
            .el-breadcrumb__item:last-child .el-breadcrumb__inner,
            .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
            .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
            .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover
          ) {
          font-weight: 600;
        }
      }

      &.full-width {
        width: 100%;
      }

      .card-opt {
        position: absolute;
        right: 0;
        top: 0;

        .el-button + .el-button {
          margin-left: 0;
        }
      }

      .empty-content {
        text-align: center;
      }

      :deep(.el-scrollbar__view) {
        flex-direction: column;
      }

      :deep(.el-card__body) {
        height: 100%;
      }

      .fleet-version-content {
        .version-list {
          // display: flex;
          // justify-content: center;
          width: 100%;
          margin: 20px 0 10px 0;

          .el-step {
            cursor: pointer;
          }
        }

        .version-detail {
          .step1 {
            position: relative;

            .preview-list {
              position: absolute;
              right: 0;
              top: 0;
              display: flex;
              align-items: center;
            }
          }

          .form-record {
            width: 400px;
            margin: 0 auto;

            .choose-btn {
              &.choose-active {
                .ltw-icon {
                  color: #000;
                }
              }

              .ltw-icon {
                font-size: 16px;
              }
            }
          }

          .el-tag {
            margin-right: 10px;
          }

          .dialog-footer {
            margin-top: 10px;
            text-align: center;
          }

          .expand-form {
            width: 800px;
          }

          :deep(.no-expand) .el-table__expand-column .cell {
            display: none;
          }
        }

        .step-content {
          margin: 10px 0;
          // padding: 0 15%;
        }
      }
    }
  }
}
</style>
