import { getMeshBoundingBox } from './bounding-box';
import { decompressLZF } from './decompress-lzf';
const LITTLE_ENDIAN = true;
export function parsePCD(data) {
    // parse header (always ascii format)
    const textData = new TextDecoder().decode(data);
    const pcdHeader = parsePCDHeader(textData);
    let attributes = {};
    // parse data
    switch (pcdHeader.data) {
        case 'ascii':
            attributes = parsePCDASCII(pcdHeader, textData);
            break;
        case 'binary':
            attributes = parsePCDBinary(pcdHeader, data);
            break;
        case 'binary_compressed':
            attributes = parsePCDBinaryCompressed(pcdHeader, data);
            break;
        default:
            throw new Error(`PCD: ${pcdHeader.data} files are not supported`);
    }
    attributes = getMeshAttributes(attributes);
    const header = getMeshHeader(pcdHeader, attributes);
    return { loaderData: { header: pcdHeader }, header, indices: null, attributes };
}
// Create a header that contains common data for PointCloud category loaders
function getMeshHeader(pcdHeader, attributes) {
    if (typeof pcdHeader.width === 'number' && typeof pcdHeader.height === 'number') {
        const pointCount = pcdHeader.width * pcdHeader.height; // Supports "organized" point sets
        return {
            vertexCount: pointCount,
            boundingBox: getMeshBoundingBox(attributes),
        };
    }
    return pcdHeader;
}
function getMeshAttributes(attributes) {
    const normalizedAttributes = {
        POSITION: { value: new Float32Array(attributes.position), size: 3 },
    };
    if (attributes.normal && attributes.normal.length > 0) {
        normalizedAttributes.NORMAL = { value: new Float32Array(attributes.normal), size: 3 };
    }
    if (attributes.color && attributes.color.length > 0) {
        // TODO - RGBA
        normalizedAttributes.COLOR_0 = { value: new Uint8Array(attributes.color), size: 3 };
    }
    if (attributes.intensity && attributes.intensity.length > 0) {
        normalizedAttributes.INTENSITY = { value: new Float32Array(attributes.intensity), size: 1 };
    }
    return normalizedAttributes;
}
function parsePCDHeader(data) {
    const result1 = data.search(/[\r\n]DATA\s(\S*)\s/i);
    const result2 = /[\r\n]DATA\s(\S*)\s/i.exec(data.substr(result1 - 1));
    const pcdHeader = {};
    pcdHeader.data = result2 && result2[1];
    if (result2 !== null) {
        pcdHeader.headerLen = (result2 && result2[0].length) + result1;
    }
    pcdHeader.str = data.substr(0, pcdHeader.headerLen);
    // remove comments
    pcdHeader.str = pcdHeader.str.replace(/#.*/gi, '');
    // parse
    pcdHeader.version = /VERSION (.*)/i.exec(pcdHeader.str);
    pcdHeader.fields = /FIELDS (.*)/i.exec(pcdHeader.str);
    pcdHeader.size = /SIZE (.*)/i.exec(pcdHeader.str);
    pcdHeader.type = /TYPE (.*)/i.exec(pcdHeader.str);
    pcdHeader.count = /COUNT (.*)/i.exec(pcdHeader.str);
    pcdHeader.width = /WIDTH (.*)/i.exec(pcdHeader.str);
    pcdHeader.height = /HEIGHT (.*)/i.exec(pcdHeader.str);
    pcdHeader.viewpoint = /VIEWPOINT (.*)/i.exec(pcdHeader.str);
    pcdHeader.points = /POINTS (.*)/i.exec(pcdHeader.str);
    if (pcdHeader.version !== null) {
        pcdHeader.version = parseFloat(pcdHeader.version[1]);
    }
    if (pcdHeader.fields !== null) {
        pcdHeader.fields = pcdHeader.fields[1].split(' ');
    }
    if (pcdHeader.type !== null) {
        pcdHeader.type = pcdHeader.type[1].split(' ');
    }
    if (pcdHeader.width !== null) {
        pcdHeader.width = parseInt(pcdHeader.width[1], 10);
    }
    if (pcdHeader.height !== null) {
        pcdHeader.height = parseInt(pcdHeader.height[1], 10);
    }
    if (pcdHeader.viewpoint !== null) {
        pcdHeader.viewpoint = pcdHeader.viewpoint[1];
    }
    if (pcdHeader.points !== null) {
        pcdHeader.points = parseInt(pcdHeader.points[1], 10);
    }
    if (pcdHeader.points === null && typeof pcdHeader.width === 'number' && typeof pcdHeader.height === 'number') {
        pcdHeader.points = pcdHeader.width * pcdHeader.height;
    }
    if (pcdHeader.size !== null) {
        pcdHeader.size = pcdHeader.size[1].split(' ').map((x) => parseInt(x, 10));
    }
    if (pcdHeader.count !== null) {
        pcdHeader.count = pcdHeader.count[1].split(' ').map((x) => parseInt(x, 10));
    }
    else {
        pcdHeader.count = [];
        if (pcdHeader.fields !== null) {
            for (let i = 0; i < pcdHeader.fields.length; i++) {
                pcdHeader.count.push(1);
            }
        }
    }
    pcdHeader.offset = {};
    let sizeSum = 0;
    if (pcdHeader.fields !== null && pcdHeader.size !== null) {
        for (let i = 0; i < pcdHeader.fields.length; i++) {
            if (pcdHeader.data === 'ascii') {
                pcdHeader.offset[pcdHeader.fields[i]] = i;
            }
            else {
                pcdHeader.offset[pcdHeader.fields[i]] = sizeSum;
                sizeSum += pcdHeader.size[i];
            }
        }
    }
    // for binary only
    pcdHeader.rowSize = sizeSum;
    return pcdHeader;
}
function parsePCDASCII(pcdHeader, textData) {
    const pointsCount = Number(pcdHeader.points);
    const offset = pcdHeader.offset;
    const position = new Float32Array(pointsCount * 3);
    const normal = new Float32Array(offset.normal_x ? pointsCount * 3 : 0);
    const color = new Uint8Array(offset.rgb ? pointsCount * 3 : 0);
    const intensity = new Uint8Array(offset.intensity ? pointsCount : 0);
    const pcdData = textData.substr(pcdHeader.headerLen);
    const lines = pcdData.split('\n');
    for (let i = 0; i < lines.length; i++) {
        if (lines[i] !== '') {
            const line = lines[i].split(' ');
            if (offset.x !== undefined) {
                position[i * 3] = parseFloat(line[offset.x]);
                position[i * 3 + 1] = parseFloat(line[offset.y]);
                position[i * 3 + 2] = parseFloat(line[offset.z]);
            }
            if (offset.rgb !== undefined) {
                const floatValue = parseFloat(line[offset.rgb]);
                const binaryColor = new Float32Array([floatValue]);
                const dataview = new DataView(binaryColor.buffer, 0);
                color[i * 3] = dataview.getUint8(0);
                color[i * 3 + 1] = dataview.getUint8(1);
                color[i * 3 + 2] = dataview.getUint8(2);
            }
            if (offset.normal_x !== undefined) {
                normal[i * 3] = parseFloat(line[offset.normal_x]);
                normal[i * 3 + 1] = parseFloat(line[offset.normal_y]);
                normal[i * 3 + 2] = parseFloat(line[offset.normal_z]);
            }
            if (offset.intensity !== undefined) {
                intensity[i] = parseFloat(line[offset.intensity]);
            }
        }
    }
    return { position, normal, color, intensity };
}
function parsePCDBinary(pcdHeader, data) {
    const pointsCount = Number(pcdHeader.points);
    const offset = pcdHeader.offset;
    const position = new Float32Array(pointsCount * 3);
    const normal = new Float32Array(offset.normal_x ? pointsCount * 3 : 0);
    const color = new Uint8Array(offset.rgb ? pointsCount * 3 : 0);
    const intensity = new Uint8Array(offset.intensity ? pointsCount : 0);
    const dataview = new DataView(data, pcdHeader.headerLen);
    for (let i = 0, row = 0; i < pointsCount; i++, row += pcdHeader.rowSize) {
        if (offset.x !== undefined) {
            position[i * 3] = dataview.getFloat32(row + offset.x, LITTLE_ENDIAN);
            position[i * 3 + 1] = dataview.getFloat32(row + offset.y, LITTLE_ENDIAN);
            position[i * 3 + 2] = dataview.getFloat32(row + offset.z, LITTLE_ENDIAN);
        }
        if (offset.rgb !== undefined) {
            color[i * 3] = dataview.getUint8(row + offset.rgb);
            color[i * 3 + 1] = dataview.getUint8(row + offset.rgb + 1);
            color[i * 3 + 2] = dataview.getUint8(row + offset.rgb + 2);
        }
        if (offset.normal_x !== undefined) {
            normal[i * 3] = dataview.getFloat32(row + offset.normal_x, LITTLE_ENDIAN);
            normal[i * 3 + 1] = dataview.getFloat32(row + offset.normal_y, LITTLE_ENDIAN);
            normal[i * 3 + 2] = dataview.getFloat32(row + offset.normal_z, LITTLE_ENDIAN);
        }
        if (offset.intensity !== undefined) {
            intensity[i] = dataview.getFloat32(row + offset.intensity, LITTLE_ENDIAN);
        }
    }
    return { position, normal, color, intensity };
}
function parsePCDBinaryCompressed(PCDheader, data) {
    const pointsCount = Number(PCDheader.points);
    const offset = PCDheader.offset;
    const position = new Float32Array(pointsCount * 3);
    const normal = new Float32Array(offset.normal_x ? pointsCount * 3 : 0);
    const color = new Uint8Array(offset.rgb ? pointsCount * 3 : 0);
    const sizes = new Uint32Array(data.slice(PCDheader.headerLen, PCDheader.headerLen + 8));
    const compressedSize = sizes[0];
    const decompressedSize = sizes[1];
    const decompressed = decompressLZF(new Uint8Array(data, PCDheader.headerLen + 8, compressedSize), decompressedSize);
    const dataview = new DataView(decompressed.buffer);
    for (let i = 0; i < PCDheader.points; i++) {
        if (offset.x !== undefined) {
            position[i * 3] = dataview.getFloat32(PCDheader.points * offset.x + PCDheader.size[0] * i, LITTLE_ENDIAN);
            position[i * 3 + 1] = dataview.getFloat32(PCDheader.points * offset.y + PCDheader.size[1] * i, LITTLE_ENDIAN);
            position[i * 3 + 2] = dataview.getFloat32(PCDheader.points * offset.z + PCDheader.size[2] * i, LITTLE_ENDIAN);
        }
        if (offset.rgb !== undefined) {
            color[i * 3] = dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i) / 255.0;
            color[i * 3 + 1] =
                dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 1) / 255.0;
            color[i * 3 + 2] =
                dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 2) / 255.0;
        }
        if (offset.normal_x !== undefined) {
            normal[i * 3] = dataview.getFloat32(PCDheader.points * offset.normal_x + PCDheader.size[4] * i, LITTLE_ENDIAN);
            normal[i * 3 + 1] = dataview.getFloat32(PCDheader.points * offset.normal_y + PCDheader.size[5] * i, LITTLE_ENDIAN);
            normal[i * 3 + 2] = dataview.getFloat32(PCDheader.points * offset.normal_z + PCDheader.size[6] * i, LITTLE_ENDIAN);
        }
    }
    return { position, normal, color };
}
