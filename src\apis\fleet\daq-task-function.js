import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqTaskFunction = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions',
    data,
    params
})
export const updateDaqTaskFunction = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions',
    data,
    params
})
export const deleteDaqTaskFunction = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions',
    params
})
export const listDaqTaskFunction = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions',
    params
})
export const listDaqTaskFunctionSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions/selections',
    params
})
export const pageDaqTaskFunction = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions/page',
    params
})
export const getDaqTaskFunction = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions/' + id})
export const cloneDaqTaskFunction = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions/clone',
    data,
    params
})
export const shareDaqTaskFunction = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_functions/share',
    data,
    params
})
