<template>
    <div class="icon-selection">
        <transition name="el-fade-in">
            <div v-show="iconPageVisible" class="icon-page">
                <div class="header">
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.code" clearable @clear="refresh">
                        <template #append>
                            <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                    <el-icon @click="close"><close/></el-icon>
                </div>

                <div class="icon-content">
                    <el-tooltip effect="light" :content="icon.code" placement="top" :enterable="false"
                                v-for="icon in pageData.records" :key="icon.id" >
                        <div class="icon-container"
                             :class="{selected:icon.code===modelValue}"
                             @click="handleClickIcon(icon)">
                            <ltw-icon :icon-code="icon.code"></ltw-icon>
                        </div>
                    </el-tooltip>
                </div>
                <el-pagination
                        small
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="queryParam.current"
                        :page-size="queryParam.size"
                        layout="total, sizes, prev, pager, next"
                        :total="pageData.total">
                </el-pagination>
            </div>
        </transition>
        <div>
            <ltw-input class="icon-show-input" :disabled="disabled" @click="handleClickInput" :readonly="true"
                      v-model="iconInputValue">
                <template #append>
                    <div class="append-container">
                        <el-button :style="{color:color}">
                             <ltw-icon :icon-code="modelValue"></ltw-icon>
                        </el-button>
                    </div>
                </template>
            </ltw-input>
        </div>


    </div>
</template>

<script>
    import {
        pageSysIcon
    } from '@/apis/system/sys-icon'
import LtwIcon from '@/components/base/LtwIcon.vue'

    const DEFAULT_QUERY_PARAM = {
        current: 1,
        size: 50,
        fuzzyQuery: true
    }
    export default {
    name: "IconSelection",
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        modelValue: String,
        iconColor: String
    },
    emits: ["update:modelValue"],
    data() {
        return {
            iconPageVisible: false,
            queryParam: Object.assign({}, DEFAULT_QUERY_PARAM),
            pageData: {
                total: 0
            },
            color: ""
        };
    },
    computed: {
        iconInputValue: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit("update:modelValue", val);
            }
        }
    },
    created() {
        if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
            this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList;
            this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList;
            this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList;
        }
        // this.color = this.iconColor
    },
    watch: {
        iconPageVisible(val) {
            if (val) {
                this.handleIconContentOpen();
            }
            else {
                this.handleIconContentClose();
            }
        },
        iconColor(val) {
            this.color = val;
        }
    },
    methods: {
        refresh() {
            this.query();
        },
        query() {
            pageSysIcon(this.queryParam).then(res => {
                this.pageData = res.data;
            });
        },
        handleClickInput() {
            if (this.disabled) {
                return;
            }
            this.iconPageVisible = true;
        },
        handleSizeChange(value) {
            this.queryParam.size = value;
            this.query();
        },
        handleCurrentChange(value) {
            this.queryParam.current = value;
            this.query();
        },
        handleClickIcon(icon) {
            this.$emit("update:modelValue", icon.code);
            this.iconPageVisible = false;
        },
        close() {
            this.iconPageVisible = false;
        },
        handleIconContentOpen() {
            this.query();
        },
        handleIconContentClose() {
            this.queryParam = Object.assign({}, DEFAULT_QUERY_PARAM);
        },
        changeColor(color) {
            this.color = color;
        },
        init() {
            this.close();
            this.color = "";
        }
    },
    components: { LtwIcon }
}
</script>

<style scoped lang="scss">
    .icon-selection {
        position: relative;

        .icon-page {
            position: absolute;
            width: 500px;
            height: auto;
            transform: translate3d(0px, calc(-100% - 10px), 0px);
            z-index: 9999;
            background: #FFF;
            border-radius: 4px;
            border: 1px solid #EBEEF5;
            padding: 12px;
            color: #606266;

            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .header {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                .ltw-input {
                    width: 80%;
                }

                i {
                    font-size: 18px;
                    cursor: pointer;
                }

                i:hover {
                    color: #409EFF;
                }
            }

            .icon-content {
                margin: 10px 0;
                line-height: 1.4;
                text-align: justify;
                font-size: 14px;
                width: 100%;
                height: auto;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                flex-wrap: wrap;

                .icon-container {
                    border: #8c939d solid 1px;
                    margin: 8px 0 0 8px;
                    padding: 8px;
                    font-size: 21px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }

                .icon-container.selected {
                    color: #3a8ee6;
                    border-color: #3a8ee6;
                }
            }
        }

        :deep .ltw-input.icon-show-input {
            cursor: pointer;

            .ltw-input__inner {
                cursor: pointer;
            }
            button {
                font-size: 21px;
            }
            .append-container{
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
            }
        }
        :deep .ltw-input.icon-show-input.is-disabled{
            .ltw-input__inner{
                cursor: not-allowed;
            }
        }
    }

</style>
