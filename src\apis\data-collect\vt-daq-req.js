import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqReq = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement', data, params})
export const updateDaqReq = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement', data, params})
export const deleteDaqReq = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement', params})
export const listDaqReq = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement', params})
export const listDaqReqSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/selections', params})
export const pageDaqReq = (params = {},unloading=true) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/page', params,unloading})
export const getDaqReq = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/' + id})
export const distributeDaqReq = (id) =>httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/'+id+'/distribute'})
export const finishDaqReq = (id) =>httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/' + id + '/finish'})
export const publishDaq = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/' + id + '/publish'})
export const pagePublishCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/page/publish/current_user', params})
export const pageReceiveCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/page/receive/current_user', params})
export const acceptDaq = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/' + id + '/accept'})
export const rejectDaq = (id) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/' + id + '/reject'})
export const getdate = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/daq/daq_requirement/date',params})

