<template>
  <div>
    <div class="main-container">
      <div class="org-tree-container">
        <org-tree
          @org-click="handleOrgClick"
          ref="empOrgTree"
          :default-selected-org="currentUser.currentTenantId"
        ></org-tree>
      </div>
      <div class="content-container">
        <el-card>
          <div class="ltw-toolbar">
            <div class="ltw-search-container ltw-tool-container">
              <ltw-input
                placeholder="请输入关键字"
                v-model="queryParam.key"
                clearable
                @clear="refresh"
              >
                <template #append>
                  <el-button @click="refresh">
                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                  </el-button>
                </template>
              </ltw-input>
            </div>
            <div class="ltw-tool-container button-group">
              <el-button
                :type="item.buttonStyleType"
                :key="item.id"
                v-for="item in outlineFunctionList"
                :icon="item.buttonIconCode"
                @click="executeButtonMethod(item.buttonCode)"
                >{{ item.name }}</el-button
              >
              <el-dropdown
                @command="handleCommand"
                class="batch-operate-btn"
                v-if="batchingFunctionList && batchingFunctionList.length > 0"
              >
                <el-button type="primary">
                  批量操作
                  <ltw-icon
                    icon-code="el-icon-arrow-down"
                    class="el-icon--right"
                  ></ltw-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :key="item.id"
                      v-for="item in batchingFunctionList"
                      :icon="item.buttonIconCode"
                      :command="item.buttonCode"
                      >{{ item.name }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <el-table
            :data="pageData.records"
            @selection-change="handleSelectionChange"
            row-key="id"
            ref="tableRef"
          >
            <el-table-column
              header-align="left"
              align="left"
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="name"
              label="姓名"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="code"
              label="编码"
            ></el-table-column>
            <el-table-column header-align="left" align="left" label="机构">
              <template #default="scope">
                <template v-if="scope.row.roleListMap">
                  <el-tag
                    type="success"
                    v-for="item in scope.row.roleListMap['org']"
                    :key="item.id"
                    >{{ item.name }}</el-tag
                  >
                </template>
              </template>
            </el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="asLoginUser"
              label="是否系统用户"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.asLoginUser"
                  :disabled="true"
                ></el-switch>
              </template>
            </el-table-column>

            <!--                        <el-table-column header-align="left" align="left" prop="mobilePhoneNum" label="手机号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="telephoneNum" label="座机号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="email" label="电子邮箱"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="birthday" label="出生日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="politicsStatus" label="政治面貌"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="maritalStatus" label="婚姻状况"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="identityCardNum" label="身份证号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="educationDegree" label="员工获得的最高学历"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="nationCode" label="民族"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="genderCode" label="性别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="statusCode" label="员工状态"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="residencePropertyCode" label="户口性质"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="residenceLocation" label="户口所在地"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="address" label="居住地址"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="hireDate" label="入职日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="nativePlaceCantonCode" label="籍贯"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="employmentTypeCode" label="用工制度"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="directSuperiorId" label="直属上级"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="resume" label="个人履历"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="partyJoinedDate" label="入党日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="fertilityStatusCode" label="生育情况"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="mainLanguages" label="主要语种"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="workDate" label="首次参加工作日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="graduatedSchool" label="毕业院校"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="schoolSystem" label="毕业学校的类型 全日制，非全日制"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="educationForm" label="员工获得教育的教学形式"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="majorType" label="员工所学专业所属类别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="degreeType" label="员工获得的学位类型"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="facultyName" label="专业教育或高等教育所在系别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="majorName" label="所学专业"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="credentialType" label="证件类型"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="credentialCode" label="证件号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="sortNum" label="顺序"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="tenantId" label="租户id"></el-table-column>-->
            <el-table-column
              header-align="left"
              align="left"
              label="操作"
              width="180"
            >
              <template #default="scope">
                <el-button-group>
                  <el-tooltip
                    :key="item.id"
                    v-for="item in inlineFunctionList"
                    effect="dark"
                    :content="item.name"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      :type="item.buttonStyleType"
                      @click="executeButtonMethod(item.buttonCode, scope.row)"
                    >
                      <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParam.current"
            :page-sizes="[5, 10, 20, 30]"
            :page-size="queryParam.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageData.total"
          ></el-pagination>
        </el-card>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <ltw-input
            v-model="formData.name"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <ltw-input
            v-model="formData.code"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="机构">
          <template
            v-if="
              dialogStatus !== 'add' &&
              formData.roleListMap &&
              formData.roleListMap[ROLE_TYPE_ORG]
            "
          >
            <el-tag
              type="success"
              v-for="org in formData.roleListMap[ROLE_TYPE_ORG]"
              :key="org.id"
              >{{ org.name }}</el-tag
            >
          </template>
          <template v-if="dialogStatus === 'add'">
            <el-tag type="success">{{ currentOrg.name }}</el-tag>
          </template>
        </el-form-item>
        <!--                <el-form-item label="手机号码" prop="mobilePhoneNum">-->
        <!--                    <ltw-input v-model="formData.mobilePhoneNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="座机号码" prop="telephoneNum">-->
        <!--                    <ltw-input v-model="formData.telephoneNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="电子邮箱" prop="email">-->
        <!--                    <ltw-input v-model="formData.email" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="出生日期" prop="birthday">-->
        <!--                    <ltw-input v-model="formData.birthday" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="政治面貌" prop="politicsStatus">-->
        <!--                    <ltw-input v-model="formData.politicsStatus" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="婚姻状况" prop="maritalStatus">-->
        <!--                    <ltw-input v-model="formData.maritalStatus" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="身份证号码" prop="identityCardNum">-->
        <!--                    <ltw-input v-model="formData.identityCardNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工获得的最高学历" prop="educationDegree">-->
        <!--                    <ltw-input v-model="formData.educationDegree" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="民族" prop="nationCode">-->
        <!--                    <ltw-input v-model="formData.nationCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="性别" prop="genderCode">-->
        <!--                    <ltw-input v-model="formData.genderCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工状态" prop="statusCode">-->
        <!--                    <ltw-input v-model="formData.statusCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="户口性质" prop="residencePropertyCode">-->
        <!--                    <ltw-input v-model="formData.residencePropertyCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="户口所在地" prop="residenceLocation">-->
        <!--                    <ltw-input v-model="formData.residenceLocation" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="居住地址" prop="address">-->
        <!--                    <ltw-input v-model="formData.address" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="入职日期" prop="hireDate">-->
        <!--                    <ltw-input v-model="formData.hireDate" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="籍贯" prop="nativePlaceCantonCode">-->
        <!--                    <ltw-input v-model="formData.nativePlaceCantonCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="用工制度" prop="employmentTypeCode">-->
        <!--                    <ltw-input v-model="formData.employmentTypeCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="直属上级" prop="directSuperiorId">-->
        <!--                    <ltw-input v-model="formData.directSuperiorId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="个人履历" prop="resume">-->
        <!--                    <ltw-input v-model="formData.resume" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="入党日期" prop="partyJoinedDate">-->
        <!--                    <ltw-input v-model="formData.partyJoinedDate" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="生育情况" prop="fertilityStatusCode">-->
        <!--                    <ltw-input v-model="formData.fertilityStatusCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="主要语种" prop="mainLanguages">-->
        <!--                    <ltw-input v-model="formData.mainLanguages" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="首次参加工作日期" prop="workDate">-->
        <!--                    <ltw-input v-model="formData.workDate" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="毕业院校" prop="graduatedSchool">-->
        <!--                    <ltw-input v-model="formData.graduatedSchool" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="毕业学校的类型 全日制，非全日制" prop="schoolSystem">-->
        <!--                    <ltw-input v-model="formData.schoolSystem" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工获得教育的教学形式" prop="educationForm">-->
        <!--                    <ltw-input v-model="formData.educationForm" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工所学专业所属类别" prop="majorType">-->
        <!--                    <ltw-input v-model="formData.majorType" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="员工获得的学位类型" prop="degreeType">-->
        <!--                    <ltw-input v-model="formData.degreeType" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="专业教育或高等教育所在系别" prop="facultyName">-->
        <!--                    <ltw-input v-model="formData.facultyName" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="所学专业" prop="majorName">-->
        <!--                    <ltw-input v-model="formData.majorName" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="证件类型" prop="credentialType">-->
        <!--                    <ltw-input v-model="formData.credentialType" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="证件号码" prop="credentialCode">-->
        <!--                    <ltw-input v-model="formData.credentialCode" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="顺序" prop="sortNum">-->
        <!--                    <ltw-input v-model="formData.sortNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="租户id" prop="tenantId">-->
        <!--                    <ltw-input v-model="formData.tenantId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >关 闭</el-button
          >
          <template v-else>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveSysRoleEmployee,
  updateSysRoleEmployee,
  deleteSysRoleEmployee,
  pageSysRoleEmployee,
  getSysRoleEmployee
} from '@/apis/system/sys-role-employee'
import OrgTree from '@/components/system/OrgTree'
import ROLE_TYPE from '@/plugins/constants/role-type'

const defaultFormData = {}
export default {
  name: 'SysRoleEmployee',
  components: { OrgTree },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      ROLE_TYPE_CUSTOM_ACTOR: ROLE_TYPE.CUSTOM_ACTOR,
      ROLE_TYPE_ORG: ROLE_TYPE.ORG,
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [
          { required: true, message: 'Please  input name', trigger: 'blur' }
        ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentUser: {},
      currentOrg: {}
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
    this.currentUser = this.$store.state.permission.currentUser
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageSysRoleEmployee(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加员工'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formData.orgId = this.currentOrg.id
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveSysRoleEmployee(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateSysRoleEmployee(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit({ id }) {
      this.dialogTitle = '修改员工'
      this.dialogStatus = 'edit'
      getSysRoleEmployee(id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          res.data.orgId = this.currentOrg.id
          this.formData = res.data
        })
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove({ id }) {
      this.remove({ id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysRoleEmployee(param).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    view({ id }) {
      this.dialogTitle = '查看员工'
      this.dialogStatus = 'view'
      getSysRoleEmployee(id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          this.formData = res.data
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    handleOrgClick(org) {
      this.currentOrg = org
      this.queryParam.orgId = undefined
      this.queryParam.orgCodeLink = undefined
      if (org.asLeaf) {
        this.queryParam.orgId = org.id
      } else {
        this.queryParam.orgCodeLink = org.codeLink
      }
      this.query()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.main-container {
  display: flex;
  flex-direction: row;

  .org-tree-container {
    width: 280px;
    margin-right: 20px;
  }

  .content-container {
    width: calc(100% - 300px);
  }
}
</style>
