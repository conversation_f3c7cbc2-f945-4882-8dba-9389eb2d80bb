<template>
  <el-card class="vehicle-card">
    <template #header>
      <div class="card-header">
        <span class="title">{{ row.vin }}</span>
        <el-popover :title="$t('状态')" width="300" trigger="click">
          <el-select
            @change="confirmVehicleStatus()"
            :teleported="false"
            v-model="row.status"
            :placeholder="$t('请选择')"
          >
            <el-option
              :disabled="checkVehicleStatus(row.status, item)"
              v-for="item in vehicleStatuses"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            ></el-option>
          </el-select>
          <!-- <div style="text-align: right; margin-top: 4px">
					  <el-button size="small" text @click="visible = false">{{
						$t('取消')
					  }}</el-button>
					  <el-button
						size="small"
						type="primary"
						@click="confirmVehicleStatus()"
						>{{ $t('确认') }}</el-button
					  >
					</div> -->
          <template #reference>
            <!-- @click="showVehicleStatus" -->
            <el-tag :type="row.statusName === 'Working' ? 'danger' : 'success'">{{ row.statusName }} </el-tag>
          </template>
        </el-popover>
        <!-- <el-tag
				  :type="row.statusName === 'Working' ? 'danger' : 'success'"
				  >{{ row.statusName }}</el-tag
				> -->
      </div>
    </template>
    <div class="card-content">
      <div class="vehicle-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item :label="'车型版本'">
            <el-tooltip effect="dark" :content="(row.code || 'N/A') + '(' + (row.version || 'N/A') + ')'" placement="top" :enterable="false">
              {{ (row.code || 'N/A') + '(' + (row.version || 'N/A') + ')' }}
            </el-tooltip>
            <!-- </el-link> -->
          </el-descriptions-item>
          <el-descriptions-item :label="'车辆用途'">{{ row.useTypeName }} </el-descriptions-item>
          <el-descriptions-item :label="'车型类型'">{{ row.vehicleTypeName }} </el-descriptions-item>
          <!-- <el-descriptions-item :label="'车牌'">{{
					  row.license
					}}</el-descriptions-item> -->
          <el-descriptions-item :label="'责任人'">
            <el-tooltip effect="dark" :content="row.keeperEmpName" placement="top" :enterable="false"
              ><span style="cursor: pointer">{{ row.keeperEmpName }}</span></el-tooltip
            >
          </el-descriptions-item>
          <el-descriptions-item :label="'车端设备'">
            <span v-if="row.tagEquipBrand && row.tagEquipCode">
              <el-tooltip effect="dark" :content="$t('解绑')" placement="top" :enterable="false">
                <el-link type="primary" :underline="false" @click="unbindBsTagEquipment(row)">
                  <ltw-icon icon-code="el-icon-unlock"></ltw-icon>
                </el-link>
              </el-tooltip>
              &nbsp;
              <el-tooltip
                effect="dark"
                :content="row.tagEquipBrand + '-' + row.tagEquipCode"
                placement="top"
                :enterable="false"
              >
                <span style="cursor: pointer">{{ row.tagEquipBrand + '-' + row.tagEquipCode }}</span>
              </el-tooltip>
              <!-- <el-link type="primary" :underline="false"> -->

              <!-- </el-link> -->
            </span>

            <el-popover v-else placement="right" width="400" trigger="click">
              <bs-tag-equipment-selection
                :data="equipmentList"
                :auto-load="false"
                v-model="row.tagEquipId"
                @change="handleEquipmentChange($event, row)"
                :ref="'equipmentSelectionRef' + row.id"
              ></bs-tag-equipment-selection>
              <template #reference>
                <el-link type="primary" :underline="false" @click="listEquipment">
                  <ltw-icon icon-code="el-icon-connection"></ltw-icon>
                </el-link>
              </template>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item :label="'传感器'">
            <el-tag @click="getModalityList(row)" v-if="!row.modalityAbnormal" type="success"
              >{{ $t('正常') }}
            </el-tag>
            <el-tag @click="getModalityList(row)" v-else type="danger">{{ $t('异常') }} </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <div class="vehicle-img">
          <!-- <img
					  :src="
						row.photoList && row.photoList.length
						  ? downloadUrl + row.photoList[0].id + token
						  : require('@/assets/images/test-vehicle.png')
					  "
					  :alt="'测试车辆'"
					/> -->
          <el-image
            :src="
              row.photoList?.length
                ? downloadUrl + row?.photoList[0]?.id + token
                : require('@/assets/images/test-vehicle.png')
            "
            :preview-src-list="[
              row.photoList && row.photoList.length
                ? downloadUrl + row.photoList[0].id + token
                : require('@/assets/images/test-vehicle.png')
            ]"
            fit="scale-down"
          >
            <template #error>
              <div class="image-slot">
                <ltw-icon icon-code="el-icon-picture"></ltw-icon>
              </div>
            </template>
          </el-image>
          <div class="vehicle-img-btn">
            <el-dropdown @command="handleCommand" class="batch-operate-btn">
              <el-button class="button" text>
                <ltw-icon icon-code="el-icon-more"></ltw-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="commandObj('edit', row)">
                    <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                    {{ $t('编辑') }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="commandObj('remove', row)">
                    <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                    {{ $t('删除') }}
                  </el-dropdown-item>
                  <!-- <el-dropdown-item :command="commandObj('detail', row)">
                    <ltw-icon icon-code="el-icon-view"></ltw-icon>
                    {{ $t('详情') }}
                  </el-dropdown-item> -->
<!--                  <el-dropdown-item :command="commandObj('fleetVersion', row)">-->
<!--                    {{ $t('软件版本') }}-->
<!--                  </el-dropdown-item>-->
                  <el-dropdown-item :command="commandObj('calibration', row)">
                    {{ $t('标定参数') }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="commandObj('fleetInsurance', row)">
                    {{ $t('保险') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <!-- <el-link type="primary" @click="getFleetVersion(row)"
						  >软件版本</el-link
						>
						<el-link type="primary" @click="getCalibrationConfig(row)"
						  >标定参数</el-link
						>
						<el-link type="primary" @click="getOptFleetInsurance(row)"
						  >保险</el-link
						> -->
          </div>
        </div>
      </div>
    </div>
  </el-card>
  <calibration-config @reload="reloadForm" ref="CalibrationConfig" />
  <modality-list @reload="reloadForm" ref="ModalityList" class="modality-issue" />
  <vehicle-statistic-detail ref="VehicleStatisticDetail" class="vehicle-statistic-detail" />
  <AddCalibration ref="AddCalibration" />
<!--  <AddFleetVersion ref="AddFleetVersion" />-->
  <OptFleetInsurance ref="OptFleetInsurance" />
</template>

<script>
import util from '@/plugins/util'
import AddCalibration from '@/pages/fleet/dialog/AddCalibration.vue'
import BsTagEquipmentSelection from '@/components/basic/BsTagEquipmentSelection.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'
import VehicleStatisticDetail from '@/pages/fleet/dialog/VehicleStatisticDetail.vue'
// import AddFleetVersion from '@/pages/fleet/dialog/AddFleetVersion.vue'
import CalibrationConfig from '@/pages/fleet/dialog/CalibrationConfig.vue'
import OptFleetInsurance from '@/pages/fleet/dialog/OptFleetInsurance.vue'
import ModalityList from '@/pages/fleet/dialog/ModalityList'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { bindBsTagEquipment, unbindBsTagEquipment, listBsTagEquipment } from '@/apis/data-collect/bs-tag-equipment'
// import { listFtmVehicleVariant } from '@/apis/fleet/ftm-vehicle-variant'
import {
  ElButton,
  ElTooltip,
  ElTag,
  ElLink,
  ElDescriptions,
  ElDescriptionsItem,
  ElCard,
  ElPopover,
  ElSelect,
  ElOption,
  ElImage
} from 'element-plus'
import { showToast, showConfirmToast } from '@/plugins/util'
import { i18n } from '@/plugins/lang'
import { getBsVehicle, deleteBsVehicle, changeVehicleStatus } from '@/apis/fleet/bs-vehicle'
import GLB_CONFIG from '@/plugins/glb-constant'
export default {
  name: 'VehicleCard',
  data() {
    return {
      equipmentList: [],
      $t: i18n.global.t,
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken(),
      row: {},
      options: [],
      visible: false
      // vehicleStatuses: [
      // {
      //   code: 'free',
      //   name: 'Free'
      // },
      // {
      //   code: 'working',
      //   name: 'Working'
      // },
      // {
      //   code: 'repairing',
      //   name: 'repairing'
      // }
      // ]
    }
  },
  emits: ['reload'],
  props: {
    type: {
      type: String,
      required: false,
      default: ''
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    vehicleStatuses: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    customFunctionList: {
      type: Array,
      default: []
    }
  },
  watch: {
    item: {
      handler(val) {
        this.row = val
      },
      deep: true,
      immediate: true
    }
  },
  // computed: {
  //   row() {
  //     return this.item
  //     // get() {
  //     //   return this.item
  //     // },
  //     // set(val) {
  //     //   this.row = val
  //     // }
  //   }
  // },
  components: {
    ElButton,
    ElTag,
    ElLink,
    ElDescriptions,
    ElDescriptionsItem,
    ElCard,
    ElTooltip,
    ElPopover,
    ElSelect,
    ElOption,
    BsTagEquipmentSelection,
    LtwIcon,
    VehicleStatisticDetail,
    CalibrationConfig,
    // AddFleetVersion,
    ModalityList,
    AddCalibration,
    OptFleetInsurance,
    ElImage
  },
  mounted() {},
  created() {
    // this.listStatus()
  },
  methods: {
    // getFleetVersion(row) {
    //   this.$refs.AddFleetVersion.show({
    //     type: 'view',
    //     vin: row.vin
    //   })
    // },
    getCalibrationConfig(row) {
      this.$refs.CalibrationConfig.show({
        type: 'view',
        vin: row.vin
      })
      // this.modalityQuery()
    },
    getOptFleetInsurance(row) {
      // if (row.insuranceId) {
      this.$refs.OptFleetInsurance.show({
        type: 'view',
        vin: row.vin
      })
      // } else {
      //   showToast('该车辆暂无保险', 'warning')
      // }
      // this.modalityQuery()
    },
    // modalityQuery() {
    // },
    getModalityList(row) {
      if (this.checkInlineButton('modalityIssue')) {
        this.$refs.ModalityList.show({
          type: 'view',
          vehicleId: row.id,
          variant: row.variant
        })
      }
    },
    getVehicleDetail(row) {
      if (row.variantVersionId) {
        this.$router.push({
          name: 'vehicleVariantVersion',
          query: {
            variantVersionId: row.variantVersionId
          }
        })
      }
    },
    editVehicleDetail(id) {
      const { buttonName, buttonCode } = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          btn: encodeURIComponent(JSON.stringify({ buttonName, buttonCode }))
        }
      })
    },
    deleteVehicleDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteBsVehicle({ id }).then(() => {
          // this.query()
          this.$emit('reload')
        })
      })
    },
    query() {
      this.$emit('reload')
    },
    getVehicleStatisticDetail(id) {
      const { buttonName, buttonCode } = this.getBtnList(this.customFunctionList, 'revoke')
      this.$router.push({
        path: '/fleet/AddFleetManagement',
        query: {
          id,
          type: 'view',
          btn: encodeURIComponent(JSON.stringify({ buttonName, buttonCode }))
        }
      })
      // this.$refs.VehicleStatisticDetail.show({
      //   type: 'view',
      //   id: id
      // })
    },
    getBsVehicle(id) {
      getBsVehicle(id).then(res => {
        this.row = res.data
      })
    },
    listEquipment() {
      if (!(this.equipmentList && this.equipmentList.length)) {
        listBsTagEquipment().then(res => {
          this.equipmentList = res.data
        })
      }
    },
    handleEquipmentChange({ node }, row) {
      this.bindBsTagEquipment({ tagEquipId: node.id, id: row.id })
    },
    bindBsTagEquipment(row) {
      let postData = {
        tagEquipId: row.tagEquipId,
        vehicleId: row.id
      }
      bindBsTagEquipment(postData).then(() => {
        showToast('绑定成功')
        this.getBsVehicle(row.id)
      })
    },
    confirmVehicleStatus() {
      let postData = {
        status: this.row.status,
        id: this.row.id
      }
      // console.log(postData)
      // this.row.statusVisible = false
      changeVehicleStatus(postData).then(() => {
        this.visible = false
        showToast('修改成功')
        this.getBsVehicle(this.row.id)
      })
    },
    checkVehicleStatus(status, item) {
      if (status === 'free') {
        if (['distributed', 'working', 'suspending'].includes(item.code)) {
          return true
        }
      }
      if (status === 'distributed') {
        return true
      }
      if (status === 'working' && item.code !== 'suspending') {
        return true
      }
      if (status === 'repairing' && item.code !== 'free') {
        return true
      }
      if (status === 'suspending' && item.code !== 'working') {
        return true
      }
      if (status === 'calibrating' && item.code !== 'free') {
        return true
      }
      return false
    },
    // showVehicleStatus() {
    //   this.row.statusVisible = true
    // },
    unbindBsTagEquipment(row) {
      showConfirmToast({
        message: BASE_CONSTANT.UNBIND_CONFIRM_MSG
      }).then(() => {
        let postData = {
          tagEquipId: row.tagEquipId
        }
        unbindBsTagEquipment(postData).then(() => {
          showToast('解绑成功')
          this.getBsVehicle(row.id)
        })
      })
    },
    checkType(index) {
      let type
      switch (index % 5) {
        case 1:
          type = 'success'
          break
        case 2:
          type = 'info'
          break
        case 3:
          type = 'warning'
          break
        case 4:
          type = 'danger'
          break
      }
      return type
    },
    reloadForm() {
      this.getBsVehicle(this.row.id)
    },
    checkInlineButton(type) {
      return ~this.inlineFunctionList.findIndex(val => val.buttonCode === type)
    },
    handleCommand(obj) {
      if (obj.type === 'remove') {
        this.deleteVehicleDetail(obj.row.id)
      } else if (obj.type === 'edit') {
        this.editVehicleDetail(obj.row.id)
      } else if (obj.type === 'detail') {
        this.getVehicleStatisticDetail(obj.row.id)
      } else if (obj.type === 'fleetVersion') {
        this.getFleetVersion(obj.row)
      } else if (obj.type === 'calibration') {
        this.getCalibrationConfig(obj.row)
      } else if (obj.type === 'fleetInsurance') {
        this.getOptFleetInsurance(obj.row)
      }
    },
    commandObj(type, row) {
      return {
        type,
        row
      }
    },
    getBtnList(btnList, code) {
      return btnList.find(val => val.buttonCode === code) || {}
    }
  }
}
</script>

<style scoped lang="scss">
.vehicle-card {
  min-width: 300px;
  // min-width: 360px;
  overflow: hidden !important;

  :deep(.variant-detail) {
    .title {
      margin-bottom: 10px;
    }

    .content {
      margin-bottom: 15px;
    }
  }

  // width: 360px;
  :deep(.el-card__header) {
    padding: 4px 10px;
  }

  :deep(.el-card__body) {
    padding: 4px 10px 4px 10px;
  }

  .el-tag {
    cursor: pointer;
  }

  .card-header {
    display: flex;
    justify-content: space-between;

    .title {
      font-size: 12px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;
    }
  }

  .card-content {
    .vehicle-info {
      display: flex;

      .el-descriptions {
        width: 50%;

        :deep(.el-descriptions__body) {
          width: 100%;
          overflow: hidden;
        }

        :deep(.el-descriptions__table) {
          width: 100%;
          table-layout: fixed;
        }

        :deep(.el-descriptions__cell) {
          font-size: 12px;
          font-weight: 400;
          padding: 0 11px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        :deep(.el-descriptions__cell.el-descriptions__label) {
          width: 71px;
        }

        :deep(.el-descriptions__cell.el-descriptions__content) {
          width: calc(100% - 71px);
        }

        // width: 50%;
      }

      .vehicle-img {
        overflow: hidden;
        width: 50%;
        padding-left: 10px;

        img,
        .el-image {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 120px;
          width: 100%;
          font-size: 30px;
          // background: var(--el-fill-color-light);
          color: var(--el-text-color-secondary);

          .image-slot {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          }
        }

        .vehicle-img-btn {
          text-align: right;

          .el-link:not(:last-child) {
            margin-right: 10px;
          }

          // padding-right: 10px;
        }
      }
    }

    .vehicle-opt {
      // padding-right: 10px;
      text-align: right;

      .el-link + .el-link {
        margin-left: 10px;
      }
    }
  }
}
</style>
