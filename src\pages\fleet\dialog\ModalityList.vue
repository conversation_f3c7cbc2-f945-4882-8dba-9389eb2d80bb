<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="cancel"
    @open="dialogOpened"
    append-to-body
    draggable
  >
    <el-table
      :data="calibrationParamsPageData.records"
      stripe
      ref="calibrationParamsRef"
      :row-key="getRowKeys"
    >
      <el-table-column
        header-align="left"
        align="left"
        prop="modalityName"
        :label="$t('传感器')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="status"
        width="100"
        :label="$t('状态')"
      >
        <template #default="scope">
          <el-tag
            :type="scope.row.status === 'abnormal' ? 'danger' : 'success'"
            >{{
              scope.row.status === 'abnormal' ? $t('异常') : $t('正常')
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="model"
        width="100"
        :label="$t('传感器型号')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="supplierName"
        :label="$t('供应商')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('传感器维修次')"
        width="100"
      >
        <template #default="scope">
          <el-link type="primary" @click="getModalityIssue(scope.row)">{{
            scope.row.maintenanceTimes
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="location"
        width="100"
        :label="$t('传感器位置')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="remark"
        :label="$t('备注')"
      >
      </el-table-column>
      <!-- <el-table-column
        header-align="left"
        align="left"
        :label="$t('操作')"
        width="200"
      >
        <template #default="scope">
          <el-button-group>
            <el-tooltip
              effect="dark"
              :content="$t('删除')"
              placement="top"
              :enterable="false"
              v-if="!formReadonly"
            >
              <el-button
                type="danger"
                @click="
                  singleRemoveCalibrationParams(
                    scope.row,
                    scope.$index,
                    calibrationParamsPageData.records
                  )
                "
              >
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column> -->
    </el-table>
    <el-pagination
      background
      @size-change="handlecalibrationSizeChange"
      @current-change="handlecalibrationCurrentChange"
      :current-page="calibrationParamsQueryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      :page-size="calibrationParamsQueryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="calibrationParamsPageData.total"
    >
    </el-pagination>
    <template #footer>
      <el-button @click="cancel()">{{ $t('关闭') }}</el-button>
    </template>
    <modality-issue
      @reload="calibrationParamsQuery"
      ref="ModalityIssue"
      class="modality-issue"
    />
  </el-dialog>
</template>

<script>
import util from '@/plugins/util'
import { i18n } from '@/plugins/lang'
import { dateUtils, showConfirmToast } from '@/plugins/util.js'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import ModalityIssue from '@/pages/fleet/dialog/ModalityIssue'
import {
  ElDialog,
  ElLink,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElSelect
} from 'element-plus'
import {
  saveBsVehicle,
  updateBsVehicle,
  getBsVehicle
} from '@/apis/fleet/bs-vehicle'
import LtwIcon from '@/components/base/LtwIcon'
import { pageFtmVariantMappingModality } from '@/apis/fleet/ftm-variant-mapping-modality'
const defaultFormData = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      $t: i18n.global.t,
      //参数标定
      calibrationVisible: false,
      calibrationParamsPageData: {
        total: 0
      },
      calibrationParamsQueryParam: {
        current: 1,
        size: 10
      },
      calibrationParamsFormVisible: false,
      vehicleId: '',
      calibrationParamsEditFormData: Object.assign({}, defaultFormData),
      calibrationParamsEditFormVisible: false,
      expandKeys: [],
      //文件
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      headerObj: {
        token: util.getToken()
      },
      filePostData: {
        sourceType: 'calibration_parameter_file'
      },
      fileList: [],
      editFileList: [],
      rowsIndex: '',
      dateUtils,
      modalityData: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    ElDialog,
    ElTag,
    ElLink,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    EmployeeSelection,
    ModalityIssue,
    LtwIcon
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      // this.dialogStatus = row.type
      this.dialogTitle = i18n.global.t('传感器')
      this.calibrationParamsQueryParam.vehicleId = row.vehicleId
      this.calibrationParamsQueryParam.variant = row.variant
      this.calibrationParamsQuery()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    // initForm(id) {
    //   this.$refs.calibrationParamsFormRef.resetFields()
    //   this.form = { ...defaultform }
    // },
    submit() {
      this.$refs.calibrationParamsFormRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveBsVehicle(this.form).then(() => {
            this.cancelCalibrationParamsForm()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateBsVehicle(this.form).then(() => {
            this.cancelCalibrationParamsForm()
          })
        }
      })
    },
    getBsVehicle(row) {
      getBsVehicle(row.id).then(res => {
        // this.$nextTick(function () {
        this.form = res.data
        // })
        this.dialogVisible = true
      })
    },
    addCalibrationParams() {
      this.calibrationParamsFormVisible = true
    },

    //参数标定
    calibrationParamsQuery() {
      pageFtmVariantMappingModality(this.calibrationParamsQueryParam).then(
        res => {
          this.calibrationParamsPageData = res.data
          if (!this.calibrationParamsPageData.records) {
            this.calibrationParamsPageData.records = []
          }
        }
      )
    },
    initForm() {
      // this.$refs.calibrationParamsEditFormRef.resetFields()
      // this.$refs.calibrationParamsFormRef.resetFields()
      this.calibrationParamsEditFormData = Object.assign({}, defaultFormData)
    },
    getRowKeys(row) {
      return row.id
    },
    handlecalibrationSizeChange(value) {
      this.calibrationParamsQueryParam.size = value
      this.calibrationParamsQuery()
    },
    getModalityIssue(row) {
      this.$refs.ModalityIssue.show({
        type: 'view',
        id: row.id,
        vehicleId: this.calibrationParamsQueryParam.vehicleId,
        modality: row.modality
      })
    },
    handlecalibrationCurrentChange(value) {
      this.calibrationParamsQueryParam.current = value
      this.calibrationParamsQuery()
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
  .user-name {
    color: #409eff;
  }
}
:deep(.el-table__expand-icon) {
  visibility: hidden;
}
</style>
