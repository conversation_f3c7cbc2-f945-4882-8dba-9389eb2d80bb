<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('车架号')" prop="vin">
        <el-select filterable v-model="form.vin" :disabled="formReadonly" popper-class="variant-input">
          <el-option v-for="item in bsVehicleList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('保单号')" prop="policyCode">
        <ltw-input v-model="form.policyCode" :disabled="formReadonly"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('开始日期')" prop="startDate">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="form.startDate"
          type="date"
          :disabled-date="disabledStartDate"
          :disabled="formReadonly"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('结束日期')" prop="endDate">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="form.endDate"
          type="date"
          :disabled-date="disabledEndDate"
          :disabled="formReadonly"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('是否启用')" prop="enabled">
        <!-- <ltw-input v-model="form.enabled" :disabled="formReadonly"></ltw-input> -->
        <el-radio-group v-model="form.enabled" :disabled="formReadonly">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('描述')" prop="description">
        <ltw-input text-type="description" type="textarea" v-model="form.description" :disabled="formReadonly"></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  getFtmVehicleInsurance,
  saveFtmVehicleInsurance,
  updateFtmVehicleInsurance,
  listVehicleVinList
} from '@/apis/fleet/ftm-vehicle-insurance'

const defaultform = { enabled: true }
export default {
  name: 'AddVehicleInsurance',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        policyCode: [
          {
            required: true,
            message: this.$t('请输入保单号'),
            trigger: 'change'
          }
        ],
        vin: [
          {
            required: true,
            message: this.$t('请输入车架号'),
            trigger: 'change'
          }
        ]
      },
      bsVehicleList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.form.vin = row.vin
          this.dialogTitle = this.$t('新增保险')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑保险')
          this.getFtmVehicleInsurance(row)
          break
        case 'view':
          this.dialogTitle = this.$t('保险详情')
          this.getFtmVehicleInsurance(row)
          break
      }
      this.listVehicleVinList()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveFtmVehicleInsurance(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmVehicleInsurance(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    getFtmVehicleInsurance(row) {
      getFtmVehicleInsurance(row.id).then(res => {
        this.form = res.data
      })
    },
    listVehicleVinList() {
      listVehicleVinList().then(res => {
        this.bsVehicleList = res.data
      })
    },
    disabledEndDate(val) {
      if (this.form.startDate) {
        return new Date(val) < new Date(this.form.startDate).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.form.endDate) {
        return new Date(val) > new Date(this.form.endDate).getTime()
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
