import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysRolePrivilegeRelation = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations', data, params})
export const updateSysRolePrivilegeRelation = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations', data, params})
export const deleteSysRolePrivilegeRelation = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations', params})
export const listSysRolePrivilegeRelation = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations', params})
export const listSysRolePrivilegeRelationSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/selections', params})
export const pageSysRolePrivilegeRelation = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/page', params})
export const getSysRolePrivilegeRelation = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/' + id})
export const assignPrivilege = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/assign', data, params})
export const reassignPrivilege = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/reassign', data, params})
export const assignPrivilegeList = (data = [], params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/assign/list', data, params})
export const reassignPrivilegeList = (data = [], params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_privilege_relations/reassign/list', data, params})
