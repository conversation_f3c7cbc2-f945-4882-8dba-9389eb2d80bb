
.el-table {
  margin-top: 15px;
  // .el-table__expanded-cell {
  //   .el-input {
  //     font-size: 14px;
  //     .el-input__inner {
  //       height: 40px;
  //     }
  //   }
  // }
  // .el-input {
  //   font-size: 12px;
  //   line-height: 30px;
  //   .el-input__inner {
  //     height: 30px;
  //   }
  // }
  .el-input-number {
    line-height: 28px;
    width: auto;
    .el-input-number__decrease,
    .el-input-number__increase {
      width: 30px;
    }
    .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
  }
}

.el-pagination {
  margin-top: 10px;
}

html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

html,
body,
#app {
  height: 100%;
  padding: 0;
  margin: 0;
}
.el-button-group {
  .el-button {
    height: 28px;
    padding: 8px 14px;
  }
}
.el-button:focus-visible {
  outline: none;
}
.el-table--small,
.el-table--medium,
.el-table--default {
  font-size: 12px;
}
.el-table.is-scrolling-none th.el-table-fixed-column--left,
.el-table.is-scrolling-none th.el-table-fixed-column--right,
.el-table th.el-table__cell,
.el-table__header-wrapper tr th.el-table-fixed-column--right,
.el-table__header-wrapper tr th.el-table-fixed-column--left,
.el-table.is-scrolling-left th.el-table-fixed-column--left,
.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background: #f2f3f4;
}
.el-table th {
  font-weight: 600;
  // color: #626976;
}
.el-table.is-scrolling-none th.el-table-fixed-column--left,
.el-table.is-scrolling-none th.el-table-fixed-column--right,
.el-table th.el-table__cell,
.el-table__header-wrapper tr th.el-table-fixed-column--right,
.el-table__header-wrapper tr th.el-table-fixed-column--left,
.el-table.is-scrolling-left th.el-table-fixed-column--left,
.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background: #f2f3f4;
}
#app {
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI,
    Roboto, Helvetica Neue, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.el-dialog {
  .el-dialog__body {
    padding: 0 var(--el-dialog-padding-primary) var(--el-dialog-padding-primary);
  }
  .el-dialog__title {
    font-size: 14px;
  }
}
.el-form {
  .el-form-item__label {
    font-size: 12px;
  }
  .el-input__inner {
    font-size: 12px;
  }
}
.button-group {
  .el-button {
    margin-right: 10px;
    font-size: 12px;
  }
}
.el-form {
  font-size: 12px;
}
.ltw-toolbar {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 10px;

  .ltw-tool-container {
    margin-right: 10px;
  }
  .batch-operate-btn {
    margin-left: 10px;
  }
  .ltw-search-container {
    width: 320px;
  }
}
.el-sub-menu [class^='icon-'] {
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
}
.el-sub-menu [class*=' icon-'] {
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
}
.el-menu-item [class*=' icon-'] {
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
}
.el-menu-item [class^='icon-'] {
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
}
.el-table td,
.el-table th {
  padding: 10px 0px;
}
//[class*=" icon-"], [class^=icon-] {
//  margin-right: 5px;
//  width: 24px;
//  text-align: center;
//  font-size: 18px;
//  vertical-align: middle;
//}
//[class*=" icon-"], [class^=icon-] {
//  font-family: "iconfont" !important;
//  font-size: 18px;
//  font-style: normal;
//  -webkit-font-smoothing: antialiased;
//}
.ltw-loading-mask {
  position: absolute;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.7);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
  &.full-loading {
    position: fixed;
  }
  .ltw-loading-spinner {
    top: 50%;
    margin-top: -21px;
    width: 100%;
    text-align: center;
    position: absolute;
    i {
      color: #409eff;
    }
    .ltw-loading-text {
      color: #409eff;
      margin: 3px 0;
      font-size: 14px;
    }
  }
}

.el-icon {
  font-size: inherit;
}
.el-button {
  .iconfont {
    font-size: inherit;
  }
}

// .ltw-page-container {
//   // height: 100%;
//   > .el-card {
//     height: 100%;
//     width: 100%;
//   }
// }
.ghost {
  background-color: rgba(241, 239, 239, 0.8);
}

.el-cascader-menu__wrap.el-scrollbar__wrap {
  height: inherit;
}
.el-link {
  font-size: 12px;
}
.css_animation {
  height: 120px;
  width: 120px;
  top: 30px;
  border-radius: 50%;
  background: rgba(250, 0, 0, 0.9);
  transform: scale(0);
  animation: myfirst 4s;
  animation-iteration-count: infinite;
  &.green {
    background: rgba(103, 194, 58, 0.9);
  }
}
.css_animation.arrow2 {
  animation-delay: 1s;
}
.css_animation.arrow3 {
  animation-delay: 2s;
}

@keyframes myfirst {
  to {
    transform: scale(0.6);
    background: rgba(0, 0, 0, 0);
  }
}
// *{
//   overflow: overlay;
// }
// *::-webkit-scrollbar {
//   /*滚动条整体样式*/
//   width: 0px;
//   /*高宽分别对应横竖滚动条的尺寸*/
//   height: 0px;
// }
// *:hover::-webkit-scrollbar {
//   /*滚动条整体样式*/
//   width: 4px;
//   /*高宽分别对应横竖滚动条的尺寸*/
//   height: 4px;
// }
// *::-webkit-scrollbar-thumb {
//   /*滚动条里面小方块*/
//   border-radius: 5px;
//   /* -webkit-box-shadow: inset 0 0 4px #a0a0a0 !important; */
//   // background: #a0a0a0;
//   background: #d2d2d2;
// }
// *::-webkit-scrollbar-track {
//   /*滚动条里面轨道*/
//   /* -webkit-box-shadow: inset 0 0 4px #d2d2d2 !important; */
//   border-radius: 0;
//   // background: #d2d2d2;
// }
