<template>
  <div class="map-info-window">
    <div class="window-title">
      <span class="text" @click="getVehicleDetail(row)">
        {{ row.vin }}
        <div class="under-line"></div>
      </span>
    </div>
    <div class="window-content">
      <div class="item">
        <div class="item-label" style="width: 70px">临牌：</div>
        <div class="item-value" v-text="row.license"></div>
      </div>
      <div class="item">
        <div class="item-label">服役时间：</div>
        <div class="item-value" v-text="row.createTime"></div>
      </div>
      <div class="item">
        <div class="item-label">采集时长：</div>
        <div class="num item-value">
          {{ parseFloat(parseFloat(row.acquisitionDuration / 60).toFixed(1)) }}H
        </div>
      </div>
      <div class="item">
        <div class="item-label">采集数据：</div>
        <div
          class="num item-value"
          v-text="checkFileSize(row.acquisitionDataSize)"
        ></div>
      </div>
      <div class="item">
        <div class="item-label">采集次数：</div>
        <div class="num item-value">{{ row.acquisitionTimes }}次</div>
      </div>
      <div class="item">
        <div class="item-label">采集里程：</div>
        <div class="num item-value">{{ row.acquisitionMileage }}km</div>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { dateUtils, checkFileSize } from '@/plugins/util'
export default {
  name: 'MapInfoWindow',
  emits: ['reload'],
  data() {
    return {
      $t: i18n.global.t,
      row: {},
      parseTime: dateUtils.parseTime,
      checkFileSize
    }
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    item: {
      handler(val) {
        val.createTime = dateUtils.parseTime(
          new Date(val.createTime),
          '{y}-{m}-{d}'
        )
        // // 采集时长
        // val.acquisitionDuration = parseFloat(
        //   parseFloat(val.acquisitionDuration / 60).toFixed(1)
        // )
        // // 采集数据
        // val.acquisitionDataSize = checkFileSize(val.acquisitionDataSize)
        this.row = val
      },
      deep: true,
      immediate: true
    }
  },
  components: {},
  mounted() {},
  methods: {
    getVehicleDetail(row) {
      this.$emit('reload', row.id)
    }
  }
}
</script>

<style scoped lang="scss">
@font-face {
  font-family: 'DOU YU';
  src: url('@/assets/fonts/douyuFont-2.otf');
}
.map-info-window {
  background: url('@/assets/images/FleetListScreen/info-bg.png');
  background-size: 100% 100%;
  height: 208px;
  width: 216px;
  padding: 20px 30px;
  .window-title {
    font-size: 16px;
    font-family: 'DOU YU';
    font-weight: 400;
    color: #28e7ff;
    line-height: 24px;
    margin-bottom: 10px;
    .text {
      cursor: pointer;
      position: relative;
      .under-line {
        width: 0;
        height: 1px;
        background: #28e7ff;
        transition: all 0.3s;
        position: absolute;
        left: 0;
        bottom: -3px;
      }
      &:hover {
        .under-line {
          width: 110%;
        }
      }
    }
  }
  .window-content {
    .item {
      color: #edf6ff;
      line-height: 22px;
      font-size: 14px;
      display: flex;
      .num {
        font-family: 'DOU YU';
      }
    }
  }
}
</style>
