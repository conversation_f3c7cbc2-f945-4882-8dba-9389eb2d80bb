<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
  >
    <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="150px"
    >
      <el-form-item :label="$t('编码')" prop="code" v-if="dialogStatus!=='add'">
        <ltw-input
            v-model="formData.code"
            :disabled="dialogStatus!=='add'"
            id="code"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="formData.name" :disabled="formReadonly" id="name" @change="formData.nameCn=formData.name"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('描述')" prop="description">
        <ltw-input
            type="textarea"
            v-model="formData.description"
            :disabled="formReadonly"
            id="description"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('是否有效')" prop="enabled" >
        <el-switch
            v-model="formData.enabled"
            :disabled="formReadonly"
            id="enabled"
        ></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            id="close"
        >{{ $t('关闭') }}</el-button
        >
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="save" id="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  saveBsTagGroup,
  updateBsTagGroup,
  getBsTagGroup
} from '@/apis/data-collect/bs-tag-group'
const defaultFormData = {
  enabled: true
}
export default {
  name: 'BsTagGroupDialog',
  emits: ['save'],
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        name: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' }
        ],
        sortNum: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'blur'
          }
        ],
        enabled: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' }
        ]
      },
      dialogTitle: '',
      dialogStatus: ''
    }
  },
  methods: {
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveBsTagGroup(this.formData).then(() => {
            this.dialogVisible = false
            this.$emit('save')
          })
        }
        if (this.dialogStatus === 'edit') {
          updateBsTagGroup(this.formData).then(() => {
            this.dialogVisible = false
            this.$emit('save')
          })
        }
      })
    },
    edit(id) {
      this.dialogTitle = this.$t('编辑')
      this.dialogStatus = 'edit'
      getBsTagGroup(id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(id) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getBsTagGroup(id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    add(defaultData) {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      Object.assign(this.formData, defaultData)
    },
    addSubGroup(defaultData) {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
      Object.assign(this.formData, defaultData)
    }
  }
}
</script>

<style lang="sass" scoped></style>
