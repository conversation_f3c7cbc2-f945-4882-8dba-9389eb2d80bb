import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmTask = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks', data, params})
export const variantVersionSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_variant_versions/getByVehicleVin', params})
export const updateFtmTask = (data = {}, params = {}, unloading = true) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks', data, params, unloading })
export const deleteFtmTask = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks', params})
export const listFtmTask = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks', params})
export const listFtmTaskSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks/selections', params})
export const pageFtmTask = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks/pageWeb', params})
export const ftmTaskRecords = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_task_records', params})
export const getFtmTask = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_tasks/' + id})
