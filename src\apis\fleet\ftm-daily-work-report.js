import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDailyWorkReport = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports',
    data,
    params
})
export const updateFtmDailyWorkReport = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports',
    data,
    params
})
export const deleteFtmDailyWorkReport = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports',
    params
})
export const listFtmDailyWorkReport = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports',
    params
})
export const listFtmDailyWorkReportSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports/selections',
    params
})
export const pageFtmDailyWorkReport = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports/page',
    params
})


export const exportFtmDailyWorkReport = (data = {}, params = {}) =>
    httpPost({
        url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports/exportFtmDailyWorkReport',
        data,
        params,
        responseType: 'blob'
    })
export const getFtmDailyWorkReport = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_daily_work_reports/' + id})
