<template>
  <el-descriptions :column="4" border id="daq-task-form">
    <el-descriptions-item :span="2" label="车辆" align="center">
      <el-link type="primary" :underline="false">{{ form.vehicleVariant + '_' + form.vehicleVin }} </el-link>
    </el-descriptions-item>
    <el-descriptions-item label="负责人" align="center">{{ form.recipient }} </el-descriptions-item>
    <el-descriptions-item label="司机" align="center">{{ form.driver }} </el-descriptions-item>
    <el-descriptions-item label="设备" :span="2" align="center">
      <!-- <el-link type="primary" :underline="false">{{
        form.equipmentCode
      }}</el-link> -->{{ form.equipmentCode }}
    </el-descriptions-item>
    <el-descriptions-item label="副驾" align="center">
      {{ form.copilot }}
    </el-descriptions-item>
    <el-descriptions-item label="合规官" align="center">
      {{ form.complianceOfficer }}
    </el-descriptions-item>
    <el-descriptions-item :label="form.locationType === 'parking_lot' ? '停车场' : '路线'" :span="4" align="center">
      {{ form.locationName }}
    </el-descriptions-item>
    <el-descriptions-item label="期望日期" :span="2" align="center">
      <el-tag type>{{ form.expectedStartTime }} </el-tag>&nbsp; - &nbsp;
      <el-tag type>{{ form.expectedEndTime }}</el-tag>
    </el-descriptions-item>
    <el-descriptions-item label="采集标签" :span="2" align="center">
      <el-link @click="getTags(form)" type="primary" :underline="false" id="tag"
        >{{ (form.tagList && form.tagList.length) || 0 }}
      </el-link>
    </el-descriptions-item>
    <el-descriptions-item v-if="form.status === 'finished'" label="执行时间" :span="4" align="center">
      <el-tag type>{{ form.startTime }} </el-tag>&nbsp; - &nbsp;
      <el-tag type>{{ form.endTime }}</el-tag>
    </el-descriptions-item>
  </el-descriptions>
  <tag-list @reload="tagSave" ref="TagList" />
</template>

<script>
import { ElDescriptions, ElDescriptionsItem, ElTag, ElLink } from 'element-plus'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import { updateDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import $store from '@/store'

export default {
  name: 'TaskForm',
  emits: ['reload', 'opt-task-detail'],
  data() {
    return {
      // 配置标签
      tagsData: [],
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      tagsTitle: ''
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    }
  },
  computed: {
    form() {
      return this.item
    }
  },
  components: {
    TagList,
    ElDescriptions,
    ElDescriptionsItem,
    ElTag,
    ElLink
  },

  methods: {
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    },
    cancel(val) {
      this.$emit('reload', val)
    },
    getTags(row) {
      let empId = $store.state.permission.currentUser.empId
      let editTags = row.status !== 'finished' && row.recipientEmpId === empId
      this.$refs.TagList.show({
        type: editTags ? 'edit' : 'view',
        tagList: row.tagList,
        requirementId: row.reqId
      })
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        this.form.tagList = dataList
        this.updateDaqReqDetail()
      }
    },
    updateDaqReqDetail() {
      let postData = { ...this.form }
      postData.tagList = this.form.tagList
      updateDaqReqDetail(postData).then(() => {
        this.$emit('reload')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-descriptions,
:deep(.el-descriptions__body),
tbody {
  table-layout: fixed;
  width: 100%;

  .el-descriptions__table.is-bordered .el-descriptions__cell {
    font-size: 12px;
    font-weight: 400;
    padding: 2px 11px;

    &.el-descriptions__label {
      font-weight: 600;
      white-space: nowrap;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px 10px 0;
}

.card-footer {
  text-align: right;

  .el-link:not(:last-child) {
    margin-right: 8px;
  }
}
</style>
