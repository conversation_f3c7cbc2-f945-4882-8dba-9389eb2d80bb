<template>
  <el-container class="ltw-home-container light">
    <el-button @click="goBack" :underline="false" class="back-btn">
      {{ $t('退出') }}
    </el-button>
    <el-scrollbar>
      <router-view></router-view>
    </el-scrollbar>
  </el-container>
</template>

<script>
export default {
  name: 'ScreenHome',
  data() {
    return {}
  },
  created() {},
  methods: {
    goBack() {
      history.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.ltw-home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px 20px;

  & > div,
    // > :deep(.el-scrollbar__wrap > .el-scrollbar__view)
  & > .el-scrollbar {
    height: 100%;
  }

  & > .el-scrollbar > :deep(.el-scrollbar__wrap) {
    position: relative;
  }

  & > div > &:deep(.el-card) {
    height: 100%;
    overflow: auto;
  }

  & > .el-scrollbar {
    border-right: none;
  }

  &.el-loading-parent--relative {
    pointer-events: all;
  }

  .back-btn {
    position: absolute;
    left: 20px;
    top: 4px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 24px;
  }
}
</style>
