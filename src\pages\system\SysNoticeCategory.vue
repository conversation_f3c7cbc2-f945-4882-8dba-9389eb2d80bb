<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container">
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>

                        {{ item.name }}
                    </el-button>                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                    {{item.name}}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records"  @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                                <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
                <el-table-column header-align="left" align="left" prop="name" label="名称"></el-table-column>
                <el-table-column header-align="left" align="left" prop="content" label="分类模板内容"></el-table-column>
                <el-table-column header-align="left" align="left" prop="title" label="分类模板标题"></el-table-column>
                <el-table-column header-align="left" align="left" prop="sendType" label="发送类型"></el-table-column>
                <el-table-column header-align="left" align="left" prop="type" label="通知类型（待办、消息）"></el-table-column>
                <el-table-column header-align="left" align="left" prop="targetPageType" label="跳转目标类型"></el-table-column>
                <el-table-column header-align="left" align="left" prop="target" label="跳转目标"></el-table-column>
                <el-table-column header-align="left" align="left" prop="createUser" label="创建人"></el-table-column>
                <el-table-column header-align="left" align="left" prop="updateUser" label="更新人"></el-table-column>
                <el-table-column header-align="left" align="left" label="操作"  min-width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name" placement="top" :enterable="false"
                            >
                                 <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon></el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="编码" prop="code">
                    <ltw-input v-model="formData.code" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="名称" prop="name">
                    <ltw-input v-model="formData.name" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="分类模板内容" prop="content">
                    <ltw-input v-model="formData.content" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="分类模板标题" prop="title">
                    <ltw-input v-model="formData.title" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="发送类型" prop="sendType">
                    <ltw-input v-model="formData.sendType" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="通知类型（待办、消息）" prop="type">
                    <ltw-input v-model="formData.type" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="跳转目标类型" prop="targetPageType">
                    <ltw-input v-model="formData.targetPageType" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="跳转目标" prop="target">
                    <ltw-input v-model="formData.target" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="创建人" prop="createUser">
                    <ltw-input v-model="formData.createUser" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="更新人" prop="updateUser">
                    <ltw-input v-model="formData.updateUser" :disabled="formReadonly"></ltw-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveSysNoticeCategory,
        updateSysNoticeCategory,
        deleteSysNoticeCategory,
        pageSysNoticeCategory,
        getSysNoticeCategory
    } from '@/apis/system/sys-notice-category'
    

const defaultFormData = {}
    export default {
        name: "SysNoticeCategory",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({},defaultFormData),
                formRules: {
                    code: [
                        {required: true, message: '请输入编码', trigger: 'blur'}
                    ],
                    name: [
                        {required: true, message: '请输入名称', trigger: 'blur'}
                    ],
                    content: [
                        {required: true, message: '请输入分类模板内容', trigger: 'blur'}
                    ],
                    title: [
                        {required: true, message: '请输入分类模板标题', trigger: 'blur'}
                    ],
                    sendType: [
                        {required: true, message: '请输入发送类型', trigger: 'blur'}
                    ],
                    type: [
                        {required: true, message: '请输入通知类型（待办、消息）', trigger: 'blur'}
                    ],
                    targetPageType: [
                        {required: true, message: '请输入跳转目标类型', trigger: 'blur'}
                    ],
                    target: [
                        {required: true, message: '请输入跳转目标', trigger: 'blur'}
                    ],
                    createUser: [
                        {required: true, message: '请输入创建人', trigger: 'blur'}
                    ],
                    updateUser: [
                        {required: true, message: '请输入更新人', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData:[]
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(funcName,row){
                this[funcName](row)
            },
            refresh(){
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysNoticeCategory(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加通知分类'
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveSysNoticeCategory(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateSysNoticeCategory(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                    }
                )
            },
            edit(row) {
                this.dialogTitle = '修改通知分类'
                this.dialogStatus = 'edit'
                getSysNoticeCategory(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            view(row) {
                this.dialogTitle = '查看通知分类'
                this.dialogStatus = 'view'
                getSysNoticeCategory(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            handleCommand(command){
                if(this.selectedData.length === 0){
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if(command === 'batchRemove'){
                    this.batchRemove()
                }
            },
            singleRemove(row) {
                this.remove({id:row.id})
            },
            batchRemove(){
                let idList = [];
                this.selectedData.forEach(ele=>{
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param){
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysNoticeCategory(param).then(
                        ()=>{
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message:'已取消删除'
                    })
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened(){

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value){
                this.selectedData = value
            },
            initForm(){
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({},defaultFormData)
            }

        }
    }
</script>

