@font-face {
    font-family: "IconFont";
    src: url('IconFont.woff2?t=1729558426741') format('woff2'),
        url('IconFont.woff?t=1729558426741') format('woff'),
        url('IconFont.ttf?t=1729558426741') format('truetype');
}

.iconfont {
    font-family: "IconFont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.kl-icon-shadow-mode:before { content: "\e900"; }
.kl-icon-shadow-data:before { content: "\e901"; }
.kl-icon-shadow-analysis-rule:before { content: "\e902"; }
.kl-icon-shadow-data-bag:before { content: "\e903"; }
.kl-icon-shadow-mapping:before { content: "\e904"; }
.kl-icon-shadow-car:before { content: "\e905"; }
.kl-icon-shadow-car-prameter:before { content: "\e906"; }
.kl-icon-shadow-bev:before { content: "\e907"; }


/**
 * Make by Icon Font Exporter
 * https://www.figma.com/community/plugin/1129455674275940478/Icon-Font-Exporter
 */
