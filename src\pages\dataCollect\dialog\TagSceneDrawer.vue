<template>
  <el-drawer
    custom-class="req-drawer-container"
    :title="$t('场景选择')"
    v-model="dialogVisible"
    direction="rtl"
    size="80%"
    append-to-body
    @close="dialogClosed"
  >
    <div class="drawer-body left">
      <div class="title">场景选择</div>
      <el-card class="requirement-list">
        <el-tag
          class="item"
          :type="isChecked(item) ? 'primary' : 'info'"
          :effect="isChecked(item) ? 'dark' : 'light'"
          v-for="item in requirementSelection"
          :key="item.id"
          @click="taskFunctionGroups(item)"
          >{{ item.name }}
        </el-tag>
      </el-card>
    </div>
    <div class="drawer-body">
      <el-divider direction="vertical" style="height: 100%" />
    </div>
    <div class="drawer-body right">
      <div class="title">属性定义</div>
      <el-scrollbar class="selected-container" v-loading="tagLoading">
        <function-scene-list :tagList="checkedTagList" :closeable="false" @close="handleClose" />
        <!----<div v-show="checkedTagList?.length > 0" style="margin-bottom: 20px">
          <el-card
            v-for="req in checkedTagList"
            :key="req.id"
            shadow="always"
            class="bs-tag-group-card"
            style="margin-bottom: 10px"
          >
            <template #header>
              <div class="bs-tag-group-card-header">
                <span>{{ req.name }}</span>
              </div>
            </template>
            <div class="bs-tag-group-card-body">
              <template v-for="group in req.children" :key="group.id">
                <el-card v-if="group.tagList?.length" style="margin-bottom: 10px">
                  <template #header>
                    <div class="bs-tag-group-card-header">
                      <span>{{ group.name }}</span>
                    </div>
                  </template>
                  <template v-for="(tag, tagIndex) in group.tagList" :key="tag.id">
                    <el-tag
                      :closable="true"
                      :type="checkTagType(tag)"
                      @close="handleClose(tagIndex, group.tagList)"
                      style="margin: 0 5px"
                    >
                      {{ tag.name }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                    </el-tag>
                  </template>
                </el-card>
              </template>
            </div>
          </el-card>
        </div>-->
        <!--        <el-empty v-show="checkedTagList?.length == 0" description="暂无选择标签"></el-empty>-->
      </el-scrollbar>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm" id="confirm">{{ $t('确定') }}</el-button>
    </div>
  </el-drawer>
</template>
<script>
import BsTagGroupPanel from '@/components/dataCollect/BsTagGroupPanel.vue'
import BsTagClassificationSelection from '@/pages/dataCollect/components/BsTagClassificationSelection.vue'
import { listTaskFunction, taskFunctionGroups } from '@/apis/data-collect/daq-task-record'
import LtwIcon from '@/components/base/LtwIcon.vue'
import FunctionSceneList from '@/pages/dataCollect/components/FunctionSceneList.vue'
import { showToast } from '../../../plugins/util'

export default {
  name: 'RequirementTagDrawer',
  emits: ['reload'],
  props: {},
  components: {
    LtwIcon,
    BsTagGroupPanel,
    BsTagClassificationSelection,
    FunctionSceneList
  },
  data() {
    return {
      dialogVisible: false,
      checkedTagList: [],
      requirementSelection: [],
      tagLoading: false
    }
  },
  created() {},
  methods: {
    show(row) {
      this.checkedTagList = JSON.parse(JSON.stringify(row.dataList || []))

      this.listTaskFunction()
      this.dialogVisible = true
    },
    listTaskFunction() {
      listTaskFunction().then(res => {
        this.requirementSelection = res.data
      })
    },
    taskFunctionGroups(item) {
      if (this.isChecked(item)) {
        let reqIndex = this.checkedTagList.findIndex(val => val.id === item.id)
        this.checkedTagList.splice(reqIndex, 1)
      } else {
        this.tagLoading = true
        taskFunctionGroups({ functionCode: item.code }).then(res => {
          item.children = res.data
          this.checkedTagList.push(item)
          this.tagLoading = false
        })
      }
    },
    isChecked(item) {
      if (this.checkedTagList?.length) {
        return ~this.checkedTagList.findIndex(val => val.code === item.code)
      }
    },

    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    handleClose(index, tagList) {
      tagList.splice(index, 1)
      this.$emit('requirementTagClose')
    },
    confirm() {
      if (this.checkedTagList?.length === 1) {
        this.dialogVisible = false
        this.$emit('reload', this.checkedTagList)
      } else {
        showToast('暂时仅支持单场景选择功能', 'warning')
      }
    },
    initForm() {
      this.requirementSelection = []
    },
    dialogClosed() {
      this.checkedTagList = []
    }
  }
}
</script>
<style lang="scss">
.req-drawer-container {
  .el-drawer__header {
    margin-bottom: 0;
  }

  .el-drawer__body {
    height: calc(100% - 125px);
    padding-right: 10px;
    display: flex;
    flex-direction: row;

    .drawer-body {
      height: 100%;
      //overflow-y: auto;
    }

    .left {
      width: 500px;
    }

    .title {
      margin-bottom: 10px;
    }

    .requirement-list {
      .item {
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
      }
    }

    .right {
      width: calc(100% - 500px);

      .selected-container {
        height: calc(100% - 31px);
        //padding-top: 15px;
      }
    }
  }
}
</style>
