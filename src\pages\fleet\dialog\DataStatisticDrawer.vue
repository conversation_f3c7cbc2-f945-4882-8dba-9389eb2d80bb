<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    @close="dialogClosed"
    direction="rtl"
    :size="1300"
  >
    <div class="drawer-body">
      <div class="data-list">
        <div class="left-data-list">
          <div class="data-card">
            <div class="data-card-title">采集次数</div>
            <div class="data-card-body">
              <el-link type="primary">{{ form.acquisitionTimes }}</el-link>
            </div>
          </div>
          <div class="data-card">
            <div class="data-card-title">采集需求</div>
            <div class="data-card-body">
              <el-link type="primary">{{ form.requirementCount }}</el-link>
            </div>
          </div>
          <div class="data-card">
            <div class="data-card-title">采集任务</div>
            <div class="data-card-body">
              <el-link type="primary">{{ form.taskCount }}</el-link>
            </div>
          </div>
          <div class="data-card">
            <div class="data-card-title">采集车辆</div>
            <div class="data-card-body">
              <el-link type="primary">{{ form.vehicleCount }}</el-link>
            </div>
          </div>
          <div class="data-card">
            <div class="data-card-title">采集时长</div>
            <div class="data-card-body">
              <el-link type="primary">{{ form.acquisitionDuration }}H</el-link>
            </div>
          </div>
          <div class="data-card">
            <div class="data-card-title">数据转化率</div>
            <div class="data-card-body">
              <el-link type="primary"
                >{{ conversionPercentage(form.conversionRate) }}%</el-link
              >
            </div>
          </div>
        </div>
        <el-divider style="height: 80px" direction="vertical" />
        <div class="right-data-list">
          <div class="data-card">
            <div class="data-card-title">原始数据</div>
            <el-link type="primary"
              >{{ checkFileSize(form.dataSize).data }}
              <span class="unit">{{
                checkFileSize(form.dataSize).unit
              }}</span></el-link
            >
            <ltw-icon icon-code="el-icon-coin"></ltw-icon>
          </div>
          <div class="progress-item">
            <ltw-icon icon-code="el-icon-right"></ltw-icon>
          </div>
          <div class="data-card">
            <div class="data-card-title">抽帧数据</div>
            <el-link type="primary"
              >{{ form.totalMeasurementDataSheets
              }}<span class="unit">张</span></el-link
            >
            <ltw-icon
              style="color: #67c23a"
              icon-code="el-icon-picture"
            ></ltw-icon>
          </div>
          <div class="progress-item">
            <ltw-icon icon-code="el-icon-right"></ltw-icon>
            <el-link type="primary"
              >{{ conversionPercentage(form.measurementToCleanRate) }}%</el-link
            >
          </div>
          <div class="data-card">
            <div class="data-card-title">清洗数据</div>
            <el-link type="primary"
              >{{ form.totalCleanDataSheets
              }}<span class="unit">张</span></el-link
            >
            <ltw-icon icon-code="el-icon-filter"></ltw-icon>
          </div>
          <div class="progress-item">
            <ltw-icon icon-code="el-icon-right"></ltw-icon>
            <el-link type="primary"
              >{{ conversionPercentage(form.cleanToDeliverRate) }}%</el-link
            >
          </div>
          <div class="data-card">
            <div class="data-card-title">送标数据</div>
            <el-link type="primary"
              >{{ form.totalDeliverDataSheets
              }}<span class="unit">张</span></el-link
            >
            <ltw-icon
              style="color: #67c23a"
              icon-code="el-icon-search"
            ></ltw-icon>
          </div>
          <div class="progress-item">
            <ltw-icon icon-code="el-icon-right"></ltw-icon>
            <el-link type="primary"
              >{{ conversionPercentage(form.deliverToAnnoRate) }}%</el-link
            >
          </div>
          <div class="data-card">
            <div class="data-card-title">标注数据</div>
            <el-link type="primary"
              >{{ form.totalAnnotationDataSheets
              }}<span class="unit">张</span></el-link
            >
            <ltw-icon icon-code="el-icon-picture-filled"></ltw-icon>
          </div>
        </div>
      </div>
      <el-table :data="pageData.records">
        <el-table-column
          header-align="left"
          align="left"
          prop="executingDate"
          label="日期"
          width="100"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="code"
          label="任务"
        >
          <template #default="scope">
            <el-link type="primary" :underline="false">{{
              scope.row.code
            }}</el-link></template
          >
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="车辆"
          ><template #default="scope">
            <el-link type="primary" :underline="false">{{
              scope.row.vehicleVariant + '_' + scope.row.vehicleVin
            }}</el-link></template
          ></el-table-column
        >
        <el-table-column
          header-align="left"
          align="left"
          prop="driver"
          label="司机"
          ><template #default="scope">{{
            scope.row.driver + ',' + scope.row.copilot
          }}</template></el-table-column
        >
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="采集时间"
          width="300"
          ><template #default="scope">
            <el-tag>{{ scope.row.startTime }}</el-tag>
            -
            <el-tag>{{ scope.row.endTime }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="采集时长"
          width="110"
        >
          <template #default="scope">
            {{ this.formatSecToDate(scope.row.acquireDuration).time }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="tagRecordCount"
          label="标签记录数"
          width="100"
        >
          <template #default="scope">
            <el-link
              @click="getTags(scope.row)"
              type="primary"
              :underline="false"
              >{{ scope.row.tagRecordCount || 0 }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="数据"
          width="500"
        >
          <template #default="scope">
            <div class="data-progress">
              <div class="progress-result">
                <el-link type="primary" :underline="false">{{
                  checkFileSize(scope.row.dataSize).data
                }}</el-link>
                <span class="unit">{{
                  checkFileSize(scope.row.dataSize).unit
                }}</span>
              </div>
              <div class="progress-item">
                <span class="progress-item-name">抽帧</span>
                <ltw-icon icon-code="el-icon-right"></ltw-icon>
              </div>
              <div class="progress-result">
                <el-link type="primary" :underline="false">{{
                  scope.row.measurementDataSheets
                }}</el-link>
                <span class="unit">张</span>
              </div>
              <div class="progress-item">
                <span class="progress-item-name">自动挑选</span>
                <ltw-icon icon-code="el-icon-right"></ltw-icon>
              </div>
              <div class="progress-result">
                <el-link type="primary" :underline="false">{{
                  scope.row.cleanDataSheets
                }}</el-link>
                <span class="unit">张</span>
              </div>
              <div class="progress-item">
                <span class="progress-item-name">挑选送标</span>
                <ltw-icon icon-code="el-icon-right"></ltw-icon>
              </div>
              <div class="progress-result">
                <el-link type="primary" :underline="false">{{
                  scope.row.deliverDataSheets
                }}</el-link>
                <span class="unit">张</span>
              </div>
              <div class="progress-item">
                <span class="progress-item-name">送标返回</span>
                <ltw-icon icon-code="el-icon-right"></ltw-icon>
              </div>
              <div class="progress-result">
                <el-link type="primary" :underline="false">{{
                  scope.row.annotationDataSheets
                }}</el-link>
                <span class="unit">张</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </div>
    <tag-list @reload="tagSave" ref="TagList" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" id="cancel">{{ $t('关闭') }}</el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script>
import { updateDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import {
  pageDaqTaskRecord,
  getDaqTaskRecordDashboardVO
} from '@/apis/data-collect/daq-task-record'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import { dateUtils } from '@/plugins/util'
export default {
  name: 'AddDriver',
  emits: ['reload'],
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      pageData: {},
      form: {},
      queryParam: {
        current: 1,
        size: 10
      },
      id: '',
      tagInfo: '',
      formatSecToDate: dateUtils.formatSecToDate
    }
  },
  components: {
    TagList
  },

  methods: {
    show(row) {
      this.id = row.id
      this.dialogVisible = true
      this.dialogTitle = this.$t('采集数据统计')
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      this.form = {}
    },
    getDaqTaskRecordDashboardVO() {
      getDaqTaskRecordDashboardVO({ locationId: this.id }).then(res => {
        if (res?.data?.acquisitionDuration) {
          // let time = this.formatSecToDate(res?.data?.acquisitionDuration || 0)
          res.data.acquisitionDuration = (res?.data?.acquisitionDuration/60/60).toFixed(2)
        }
        this.form = res.data || {}
      })
    },
    pageDaqTaskRecord() {
      pageDaqTaskRecord({ locationId: this.id, ...this.queryParam }).then(
        res => {
          this.pageData = res.data
        }
      )
    },
    query() {
      this.getDaqTaskRecordDashboardVO()
      this.pageDaqTaskRecord()
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    getTags(row) {
      let empId = this.$store.state.permission.currentUser.empId
      let editTags = row.status !== 'finished' && row.recipientEmpId === empId
      if (editTags) {
        this.tagInfo = {
          id: row.taskId,
          locationType: row.locationType,
          status: row.status,
          locationId: row.locationId
        }
      }
      this.$refs.TagList.show({
        type: editTags ? 'edit' : 'view',
        requirementId: row.reqId,
        tagList: row.tagList
      })
    },
    conversionPercentage(num) {
      return parseFloat(parseFloat(num * 100).toFixed(2))
    },
    tagSave(dataList) {
      if (dataList && dataList.length) {
        // this.form.tagList = dataList
        this.updateDaqReqDetail(dataList)
      }
    },
    updateDaqReqDetail(dataList) {
      let postData = { ...this.tagInfo, tagList: dataList }
      updateDaqReqDetail(postData).then(() => {
        this.pageDaqTaskRecord(this.id)
        // this.$emit('reload')
      })
    },
    checkFileSize(data) {
      if (data > 0 && data < Math.pow(2, 10)) {
        return { data, unit: 'B' }
      } else if (data >= Math.pow(2, 10) && data < Math.pow(2, 20)) {
        return { data: parseFloat((data / 1024).toFixed(2)), unit: 'KB' }
      } else if (data >= Math.pow(2, 20) && data < Math.pow(2, 30)) {
        return { data: parseFloat((data / 1024 / 1024).toFixed(2)), unit: 'M' }
      } else if (data >= Math.pow(2, 30) && data < Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'G'
        }
      } else if (data >= Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'T'
        }
      } else {
        return { data: 0, unit: '' }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.drawer-body {
  .data-progress {
    line-height: 30px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-around;
    align-items: center;
    .progress-result {
      white-space: nowrap;
    }
    .progress-item {
      position: relative;
      top: 5px;
      width: 80px;
      text-align: center;
      .progress-item-name {
        position: absolute;
        top: -15px;
        left: 0;
        width: 100%;
        text-align: center;
      }
    }
  }
  .unit {
    color: #606266;
    line-height: 30px;
  }
  .data-list {
    display: flex;
    .left-data-list {
      display: flex;
      .data-card {
        margin: 0 12px;
        border: 1px solid rgba(228, 231, 237, 1);
        border-radius: 4px;
        box-shadow: 0px 2px 4px rgb(0 0 0 / 12%);
        height: 80px;
        width: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        .el-link {
          font-size: 24px;
        }
        .data-card-title {
          color: #606266;
        }
      }
    }
    .right-data-list {
      display: flex;
      .progress-item {
        position: relative;
        div {
          line-height: 80px;
        }
        .el-link {
          position: absolute;
          top: 10px;
        }
      }
      .data-card {
        margin: 0 12px;
        border: 1px solid rgba(228, 231, 237, 1);
        border-radius: 4px;
        box-shadow: 0px 2px 4px rgb(0 0 0 / 12%);
        height: 80px;
        width: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        .el-link {
          font-size: 12px;
        }
        .data-card-title {
          font-size: 12px;
        }
        div {
          text-align: center;
          font-size: 24px;
          color: #409eff;
        }
        .data-card-title {
          color: #606266;
        }
      }
    }
  }
}
</style>
