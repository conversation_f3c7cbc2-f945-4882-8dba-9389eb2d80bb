<template>
  <div class="base-info">
    <el-form :model="form" label-position="top" ref="formRef" label-width="100px" :rules="formRules">
      <div class="form-left">
        <div v-if="!formReadonly" class="choose-map" @click="chooseMapPosition()">
          <ltw-icon icon-code="el-icon-location" />
        </div>
        <el-form-item :label="$t('地图')" prop="longitude">
          <base-map class="map-overview" ref="baseMap"></base-map>
          <!--          <div class="map-overview" ref="mapContainerRef"></div>-->
        </el-form-item>
        <el-form-item :label="$t('名称')" prop="name">
          <ltw-input v-model="form.name" :disabled="formReadonly" textType="description" />
        </el-form-item>
        <el-form-item :label="$t('类型')" prop="type">
          <el-select
            class="form-item"
            filterable
            v-model="form.type"
            :disabled="formReadonly"
            clearable
            @change="changeType"
          >
            <el-option v-for="item in groundLevelList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('环境类型')" prop="envType">
          <el-select
            class="form-item"
            filterable
            v-model="form.envType"
            :disabled="formReadonly"
            clearable
            @change="changeEnvType"
          >
            <el-option v-for="item in envTypeList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('总车位数')" prop="totalParkingSpace">
          <el-input-number
            class="form-item"
            v-model="form.totalParkingSpace"
            :precision="0"
            :min="0"
            :disabled="formReadonly"
          />
        </el-form-item>
        <el-form-item :label="$t('层数')" prop="floors">
          <el-input-number
            class="form-item"
            :min="0"
            v-model="form.floors"
            :precision="0"
            :step="1"
            :disabled="formReadonly"
          />
        </el-form-item>
        <el-form-item :label="$t('限高(米)')" prop="limitedHeight">
          <el-input-number
            class="form-item"
            :min="1"
            v-model="form.limitedHeight"
            :precision="2"
            :step="1"
            :disabled="formReadonly"
          />
        </el-form-item>
      </div>
      <div class="form-right">
        <el-form-item class="inline-form-item" :label="$t('是否安装减速带')" prop="hasSpeedBump">
          <el-switch
            v-model="form.hasSpeedBump"
            active-color="rgba(87, 85, 255, 1)"
            :active-value="1"
            :inactive-value="0"
            :disabled="formReadonly"
          />
        </el-form-item>
        <el-form-item class="inline-form-item" :label="$t('是否安装防撞条')" prop="hasCollisionStrip">
          <el-switch
            v-model="form.hasCollisionStrip"
            active-color="rgba(87, 85, 255, 1)"
            :active-value="1"
            :inactive-value="0"
            :disabled="formReadonly"
          />
        </el-form-item>
        <el-form-item class="inline-form-item" :label="$t('是否有RTK车位')" prop="hasRtkSlot">
          <el-switch
            v-model="form.hasRtkSlot"
            active-color="rgba(87, 85, 255, 1)"
            :active-value="1"
            :inactive-value="0"
            :disabled="formReadonly"
          />
        </el-form-item>
        <el-form-item :label="$t('图片')" prop="fileIdList">
          <upload-file
            ref="uploadImage"
            source-type="parking_lots_files"
            accept=".jpg,.jpeg,.png,.gif"
            :source-id="form.id"
            v-model="form.fileIdList"
            :disabled="formReadonly"
            :no-tenant-code="true"
          />
        </el-form-item>
        <el-form-item :label="$t('描述')" prop="description">
          <ltw-input
            type="textarea"
            v-model="form.description"
            :disabled="formReadonly"
            textType="description"
          ></ltw-input>
        </el-form-item>
        <div class="footer">
          <el-button @click="cancel">{{ $t('取消') }}</el-button>
          <el-button v-if="!formReadonly" id="submit" type="primary" @click="submit">{{ $t('保存') }}</el-button>
          <el-button v-if="dialogStatus === 'map' && formReadonly" type="warning" @click="edit"
            >{{ $t('编辑') }}
          </el-button>
        </div>
      </div>
    </el-form>
    <choose-map-position ref="ChooseMapPosition" @reload="reload" />
  </div>
</template>
<script>
import { getFtmParkingLotFieldItems } from '@/apis/fleet/ftm-parking-lot'
import UploadFile from '@/components/system/UploadFile.vue'
import ChooseMapPosition from '@/pages/fleet/dialog/ChooseMapPosition.vue'
import { latLngPoint } from '@/plugins/map/TxMap'
import BaseMap from '@/components/map/BaseMap.vue'

let mapObj = {}
const defaultform = {
  hasSpeedBump: true,
  hasCollisionStrip: true,
  hasRtkSlot: true
}
export default {
  name: 'BaseInfo',
  emits: ['reload', 'save-info', 'cancel-btn', 'update_map_status_edit'],
  components: {
    UploadFile,
    ChooseMapPosition,
    BaseMap
  },
  props: {
    cardInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dialogStatus: {
      type: String,
      default: () => {
        return ''
      }
    },
    mapStatusEdit: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  watch: {
    cardInfo: {
      handler(val) {
        if (
          val.latitude &&
          val.longitude &&
          (val.latitude !== this.form.latitude || val.longitude !== this.form.longitude)
        ) {
          if (mapObj.globalMap) {
            // await this.loadMap()
            this.drawMarker({ lat: val.latitude, lng: val.longitude, id: val.id })
          }
        }
        this.form = JSON.parse(JSON.stringify(val))
        const form = JSON.parse(JSON.stringify(this.form))
        delete form.fileIdList
        this.backupForm = JSON.stringify(form)
      },
      immediate: true
    },
    mapStatusEdit: {
      handler(val) {
        this.mapStatusEditFlag = val
      },
      immediate: true
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view' || (this.dialogStatus === 'map' && !this.mapStatusEditFlag)
    }
  },
  data() {
    return {
      form: Object.assign({}, defaultform),
      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('名称'),
            trigger: 'change'
          }
        ],
        // totalParkingSpace: [
        //   {
        //     required: true,
        //     message: this.$t('请输入') + this.$t('总车位数'),
        //     trigger: 'change'
        //   }
        // ],
        type: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('类型'),
            trigger: 'change'
          }
        ],
        longitude: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('位置'),
            trigger: 'change'
          }
        ],
        envType: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('环境类型'),
            trigger: 'change'
          }
        ]
      },
      groundLevelList: [],
      envTypeList: [],
      mapStatusEditFlag: false,
      backupForm: {}
    }
  },
  created() {
    this.getEnvTypeList() // 环境类型
    this.getGroundLevelList() //停车场类型
    // this.$refs.baseMap.initMarkers()
  },
  async mounted() {
    await this.$refs.baseMap.show({ centerMarker: false })
    if (this.form.latitude && this.form.longitude) {
      this.drawMarker({ lat: this.form.latitude, lng: this.form.longitude, id: this.form.id })
    }
    const form = JSON.parse(JSON.stringify(this.form))
    delete form.fileIdList
    this.backupForm = JSON.stringify(form)
  },
  methods: {
    getEnvTypeList() {
      if (!this.envTypeList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'parking_env'
        }).then(res => {
          this.envTypeList = res.data
        })
      }
    },
    getGroundLevelList() {
      if (!this.groundLevelList?.length) {
        getFtmParkingLotFieldItems({
          fieldCode: 'ground_level'
        }).then(res => {
          this.groundLevelList = res.data
        })
      }
    },
    cancel() {
      if (this.mapStatusEditFlag) {
        this.form.id = ''
        this.$nextTick(() => {
          this.mapStatusEditFlag = false
          this.$emit('update_map_status_edit', this.mapStatusEditFlag)
          this.form = JSON.parse(JSON.stringify(this.cardInfo))
        })
      } else {
        this.$emit('cancel-btn')
      }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          fileIdList: this.form.fileIdList ? this.form.fileIdList : []
        }
        this.$emit('save-info', postData)
      })
    },
    chooseMapPosition() {
      const postData = {}
      if (this.form.longitude && this.form.latitude) {
        postData.center = [this.form.latitude, this.form.longitude]
        postData.markers = [
          {
            id: 'centerMarker',
            style: {
              src: 'data:image/png;base64,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'
            },
            position: latLngPoint({ lat: this.form.latitude, lng: this.form.longitude })
          }
        ]
      }
      this.$refs.ChooseMapPosition.show(postData)
    },
    reload(obj) {
      if (obj.lat && obj.lng) {
        this.form.address = obj.title
        this.form.cantonCode = obj.cantonCode
        this.form.longitude = obj.lng
        this.form.latitude = obj.lat
        this.$refs.formRef.validateField('longitude')
        this.drawMarker(obj)
        
        // 自动设置停车场名称
        this.autoSetParkingLotName(obj)
      }
    },
    drawMarker(obj) {
      this.$refs.baseMap.clearObjects('globalMarker')
      const position = latLngPoint({ lat: obj.lat, lng: obj.lng })
      this.$refs.baseMap.initMarkers([
        {
          id: obj.id,
          style: {
            src: 'data:image/png;base64,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'
          },
          position: position
        }
      ])
      this.$refs.baseMap.goCenter([obj.lng, obj.lat])
    },
    changeType(e) {
      const item = this.groundLevelList.find(val => val.code === e)
      this.form.typeName = item?.name
    },
    changeEnvType(e) {
      const item = this.envTypeList.find(val => val.code === e)
      this.form.envTypeName = item?.name
    },
    edit() {
      this.mapStatusEditFlag = true
      this.$emit('update_map_status_edit', this.mapStatusEditFlag)
    },
    checkFormChanged() {
      if (this.formReadonly) {
        return false
      } else {
        const form = JSON.parse(JSON.stringify(this.form))
        delete form.fileIdList
        return this.backupForm !== JSON.stringify(form)
      }
    },
    autoSetParkingLotName(locationData) {
      debugger
      // 优先使用地图API返回的行政区信息
      if (locationData.adInfo) {
        debugger
        const adInfo = locationData.adInfo
        const province = adInfo.province || ''
        const city = adInfo.city || ''
        const district = adInfo.district || ''
        
        // 从地址中提取地点名称
        const address = locationData.title || ''
        let locationName = address
        
        // 如果地址包含行政区信息，移除行政区部分
        if (province && address.includes(province)) {
          locationName = address.replace(province, '').trim()
        }
        if (city && locationName.includes(city)) {
          locationName = locationName.replace(city, '').trim()
        }
        if (district && locationName.includes(district)) {
          locationName = locationName.replace(district, '').trim()
        }
        
        // 清理地点名称
        locationName = locationName.replace(/^[，,、\s]+/, '').replace(/[，,、\s]+$/, '')
        
        // 生成停车场名称
        const parkingLotName = this.generateParkingLotName(
          locationName || '停车场',
          province,
          city,
          district
        )
        
        // 设置停车场名称
        if (parkingLotName) {
          this.form.name = parkingLotName
        }
      } else {
        // 如果没有行政区信息，使用原来的地址解析方法
        const address = locationData.title || ''
        const addressParts = this.parseAddress(address)
        
        const parkingLotName = this.generateParkingLotName(
          addressParts.locationName || '停车场',
          addressParts.province,
          addressParts.city,
          addressParts.district
        )
        
        if (parkingLotName) {
          this.form.name = parkingLotName
        }
      }
    },
    parseAddress(address) {
      // 腾讯地图API返回的地址格式示例：
      // 1. "上海市宝山区某某路123号"
      // 2. "江苏省苏州市相城区某某路123号"
      // 3. "某某路123号（上海市宝山区）"
      // 4. "某某路123号"
      // 5. "嘉定区南陈小区421弄(和政路西)" - 只有区名，需要推断城市
      
      let province = ''
      let city = ''
      let district = ''
      let locationName = address
      
      // 直辖市列表
      const municipalities = ['北京市', '天津市', '上海市', '重庆市']
      
      // 尝试匹配完整的省市区格式（普通省市）
      const fullAddressRegex = /^(.+?[省市])(.+?市)(.+?区|.+?县)/
      const fullMatch = address.match(fullAddressRegex)
      
      if (fullMatch) {
        province = fullMatch[1]
        city = fullMatch[2]
        district = fullMatch[3]
        // 提取剩余部分作为地点名称
        locationName = address.substring(fullMatch[0].length)
      } else {
        // 尝试匹配直辖市格式
        const municipalityRegex = /^(.+?[省市])(.+?区|.+?县)/
        const municipalityMatch = address.match(municipalityRegex)
        
        if (municipalityMatch) {
          province = municipalityMatch[1]
          city = municipalityMatch[1] // 直辖市中省和市是同一个
          district = municipalityMatch[2]
          locationName = address.substring(municipalityMatch[0].length)
        } else {
          // 尝试从括号中提取行政区信息
          const bracketRegex = /[（(](.+?[省市].+?区|.+?[省市].+?县)[)）]/
          const bracketMatch = address.match(bracketRegex)
          
          if (bracketMatch) {
            const regionInfo = bracketMatch[1]
            // 解析括号内的行政区信息
            const regionParts = this.parseRegionInfo(regionInfo)
            province = regionParts.province
            city = regionParts.city
            district = regionParts.district
          } else {
            // 尝试从地址中提取城市信息
            const cityMatch = address.match(/(.+?市)/)
            if (cityMatch) {
              city = cityMatch[1]
              // 检查是否为直辖市
              if (municipalities.includes(city)) {
                province = city
              }
            } else {
              // 尝试匹配只有区名的情况，如"嘉定区"
              const districtOnlyRegex = /^(.+?区|.+?县)/
              const districtOnlyMatch = address.match(districtOnlyRegex)
              
              if (districtOnlyMatch) {
                district = districtOnlyMatch[1]
                // 根据区名推断城市（这里需要根据实际情况调整）
                // 嘉定区属于上海市
                if (district.includes('嘉定区') || district.includes('宝山区') || 
                    district.includes('松江区') || district.includes('青浦区') ||
                    district.includes('奉贤区') || district.includes('金山区') ||
                    district.includes('崇明区') || district.includes('浦东新区') ||
                    district.includes('黄浦区') || district.includes('徐汇区') ||
                    district.includes('长宁区') || district.includes('静安区') ||
                    district.includes('普陀区') || district.includes('虹口区') ||
                    district.includes('杨浦区') || district.includes('闵行区')) {
                  province = '上海市'
                  city = '上海市'
                }
                // 苏州市的区
                else if (district.includes('相城区') || district.includes('姑苏区') || 
                         district.includes('吴中区') || district.includes('吴江区') ||
                         district.includes('虎丘区') || district.includes('工业园区') ||
                         district.includes('高新区')) {
                  province = '江苏省'
                  city = '苏州市'
                }
                // 提取剩余部分作为地点名称
                locationName = address.substring(districtOnlyMatch[0].length)
              }
            }
          }
        }
      }
      
      // 如果没有提取到地点名称，使用原地址
      if (!locationName || locationName.length < 2) {
        locationName = address
      }
      
      // 清理地点名称，移除多余的标点符号
      locationName = locationName.replace(/^[，,、\s]+/, '').replace(/[，,、\s]+$/, '')
      
      return {
        province,
        city,
        district,
        locationName: locationName.trim()
      }
    },
    parseRegionInfo(regionInfo) {
      let province = ''
      let city = ''
      let district = ''
      
      // 直辖市格式：上海市宝山区
      const municipalityRegex = /^(.+?[省市])(.+?区|.+?县)$/
      const municipalityMatch = regionInfo.match(municipalityRegex)
      
      if (municipalityMatch) {
        province = municipalityMatch[1]
        city = municipalityMatch[1] // 直辖市中省和市是同一个
        district = municipalityMatch[2]
      } else {
        // 普通省市格式：江苏省苏州市相城区
        const normalRegex = /^(.+?[省市])(.+?市)(.+?区|.+?县)$/
        const normalMatch = regionInfo.match(normalRegex)
        
        if (normalMatch) {
          province = normalMatch[1]
          city = normalMatch[2]
          district = normalMatch[3]
        }
      }
      
      return { province, city, district }
    },
    generateParkingLotName(locationName, province, city, district) {
      if (!locationName) return ''
      
      let prefix = ''
      
      // 判断是否为直辖市
      const municipalities = ['北京市', '天津市', '上海市', '重庆市']
      const isMunicipality = municipalities.includes(province)
      
      if (isMunicipality) {
        // 直辖市：上海市宝山区
        prefix = `${province}${district || city}`
      } else if (city && district) {
        // 普通地级市：苏州市相城区
        prefix = `${city}${district}`
      } else if (city) {
        // 只有城市信息：苏州市
        prefix = city
      }
      
      // 如果地点名称已经包含行政区信息，直接返回地点名称
      if (locationName.includes(prefix) || locationName.includes(province) || locationName.includes(city)) {
        return locationName
      }
      
      // 如果地点名称包含"停车场"字样，直接使用
      if (locationName.includes('停车场')) {
        return locationName
      }
      
      // 如果地点名称很短（少于3个字符），添加"停车场"后缀
      if (locationName.length < 3) {
        locationName = locationName + '停车场'
      }
      
      return `${prefix}${locationName}`
    }
  }
}
</script>
<style scoped lang="scss">
.base-info {
  .el-form {
    display: flex;

    .choose-map {
      position: absolute;
      left: 40px;
      top: 0;
      font-size: 20px;
      color: #409eff;
      cursor: pointer;
    }

    .map-overview {
      height: 200px;
      width: 100%;
    }
  }

  .form-left,
  .form-right {
    width: 50%;
    position: relative;
  }

  .form-left {
    padding-right: 24px;
    border-right: 1px solid #ebeef5;
  }

  .form-right {
    padding-left: 24px;
  }

  .inline-form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    :deep(.el-form-item__content) {
      flex: none;
    }
  }

  .form-item {
    width: 100%;
  }

  .footer {
    text-align: right;
  }
}
</style>
