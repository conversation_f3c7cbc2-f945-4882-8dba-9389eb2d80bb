import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDbc = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs', data, params })
export const updateFtmDbc = (data = {}, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs', data, params })
export const deleteFtmDbc = (params = {}) =>
  httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs', params })
export const listFtmDbc = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs', params })
export const listFtmDbcSelection = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs/selections', params })
export const pageFtmDbc = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs/page', params })
export const getFtmDbc = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbcs/' + id })
export const listFtmDbcMessage = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_messages', params })
export const listFtmDbcSignal = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signals', params })

//根据车辆编号以及时间获取对应的DBC文件
export const getDbcFiles = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/api/ftm/vehicle/dbc', params })
export const getDbc = id => httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions/' + id })

export const pageDbc = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions/page', params })
export const saveDbc = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions', data, params })
export const updateDbc = (data = {}, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions', data, params })
export const deleteDbc = (params = {}) =>
    httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions', params })

export const cloneDbc = (data = {}, params = {}) =>
    httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions/copy', data, params })

// 获取车型对应的dbc文件列表
export const getVariantDbcFiles = (params = {}) =>
    httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/dbc/variant_dbc_versions/variant_files', params })
