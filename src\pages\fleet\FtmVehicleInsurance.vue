<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            id="queryKey"
            :placeholder="$t('请输入关键字')"
            v-model="queryParam.key"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button id="refresh" @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            type="primary"
            @click="add()"
          >
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ $t('新增') }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon
                icon-code="el-icon-arrow-down"
                class="el-icon--right"
              ></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="batchRemove"
                >
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                  {{ $t('批量删除') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        border
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
      >
        <el-table-column
          header-align="left"
          align="left"
          type="selection"
          width="55"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="policyCode"
          :label="$t('保单号')"
        >
          <!-- <template #default="scope">
            <el-link
              @click="view(scope.row)"
              type="primary"
              :underline="false"
              >{{ scope.row.policyCode }}</el-link
            >
          </template> -->
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="vin"
          :label="$t('车架号')"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="startDate"
          :label="$t('开始日期')"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="endDate"
          :label="$t('结束日期')"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="enabled"
          :label="$t('是否启用')"
        >
          <template #default="scope">
            <span v-if="scope.row.enabled">是</span>
            <span v-if="!scope.row.enabled">否</span>
          </template>
        </el-table-column>
        <el-table-column
            header-align="left"
            align="left"
            prop="description"
            show-overflow-tooltip
            :label="$t('描述')"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          :label="$t('操作')"
          min-width="180"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                effect="dark"
                :content="$t('编辑')"
                placement="top"
                :enterable="false"
              >
                <el-button
                  type="warning"
                  size="small"
                  @click="edit(scope.row)"
                >
                  <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="$t('删除')"
                placement="top"
                :enterable="false"
              >
                <el-button
                  type="danger"
                  size="small"
                  @click="singleRemove(scope.row)"
                >
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="$t('查看详情')"
                placement="top"
                :enterable="false"
              >
                <el-button
                  type="primary"
                  size="small"
                  @click="view(scope.row)"
                >
                  <ltw-icon icon-code="el-icon-view"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <AddVehicleInsurance ref="AddVehicleInsurance" @reload="query" />
  </div>
</template>

<script>
import {
  deleteFtmVehicleInsurance,
  pageFtmVehicleInsurance
} from '@/apis/fleet/ftm-vehicle-insurance'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import AddVehicleInsurance from '@/pages/fleet/dialog/AddVehicleInsurance'
const defaultFormData = {}
export default {
  name: 'FtmVehicleInsurance',
  components: {
    AddVehicleInsurance
  },
  data() {
    return {
      bsVehicleList: [],
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        policyCode: [
          {
            required: true,
            message: this.$t('请输入保单号'),
            trigger: 'change'
          }
        ],
        vin: [
          {
            required: true,
            message: this.$t('请输入车架号'),
            trigger: 'change'
          }
        ]
        // description: [
        //     {required: true, message: this.$t('请输入描述'), trigger: 'blur'}
        // ],
        // startDate: [
        //     {required: true, message: this.$t('请输入开始日期'), trigger: 'blur'}
        // ],
        // endDate: [
        //     {required: true, message: this.$t('请输入结束日期'), trigger: 'blur'}
        // ],
        // enabled: [
        //     {required: true, message: this.$t('请输入是否启用'), trigger: 'blur'}
        // ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  props: {
    vin: {
      type: String,
      default: ''
    }
  },
  // watch: {
  //   vin(val) {
  //     // this.queryParam.vin = val
  //     this.query()
  //   }
  // },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    // show(row) {
    //   this.queryParam.vin = row.vin
    //   this.query()
    // },
    disabledEndDate(val) {
      if (this.formData.startDate) {
        return new Date(val) < new Date(this.formData.startDate).getTime()
      }
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      let postData = {...this.queryParam,vin:this.vin}
      pageFtmVehicleInsurance(postData).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.$refs.AddVehicleInsurance.show({ type: 'add', vin: this.vin })
    },
    edit(row) {
      this.$refs.AddVehicleInsurance.show({ type: 'edit', id: row.id })
    },
    view(row) {
      this.$refs.AddVehicleInsurance.show({ type: 'view', id: row.id })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmVehicleInsurance(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
</style>
