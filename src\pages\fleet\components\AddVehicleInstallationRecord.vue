<template>
  <!-- 作为对话框使用 -->
  <el-dialog
    v-if="!asSubComponent"
    title="新增装车记录"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleDialogClose"
    class="add-vehicle-installation-record-dialog"
  >
    <div class="add-vehicle-installation-record">

        <!-- 步骤条 -->
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="基础信息" description="填写装车记录基础信息"></el-step>
          <el-step title="传感器配置" description="配置传感器信息"></el-step>
          <el-step title="完成" description="创建完成"></el-step>
        </el-steps>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：基础信息 -->
      <div v-if="currentStep === 0" class="step-basic-info">
        <el-form
          ref="basicInfoForm"
          :model="formData"
          :rules="basicInfoRules"
          label-width="120px"
          class="basic-info-form"
        > 
          <el-form-item label="版本名称" prop="name">
            <ltw-input
              v-model="formData.name"
              placeholder="请输入版本名称"
            />
          </el-form-item>
          <el-form-item label="车型版本：" prop="versionCascader">
            <el-cascader
              v-model="formData.versionCascader"
              :options="vehicleVersionCascaderOptions"
              :props="cascaderProps"
              placeholder="请选择车型版本"
              filterable
              clearable
              @change="handleVersionCascaderChange"
              style="width:100%"
              :disabled='variantVersionDisabled' 
            />
          </el-form-item>

          <el-form-item label="功能分类：" prop="useType">
            <el-select v-model="formData.useType" placeholder="请选择功能分类" style="width:100%">
              <el-option
                v-for="item in functionCategoryOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="开始时间：" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              placeholder="请选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleStartTimeChange"
              style="width:100%"
            />
          </el-form-item>

          <el-form-item label="结束时间：" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="datetime"
              placeholder="请选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleEndTimeChange"
              style="width:100%"
            />
          </el-form-item>

          <el-form-item label="描述：">
            <el-input
              v-model="formData.description"
              type="textarea"
              :maxlength="100"
              show-word-limit
              :rows="3"
              placeholder="请输入描述信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第二步：传感器配置 -->
      <div v-if="currentStep === 1" class="step-sensor-config">
        <FleetSensorConfigSection
          :version-data="sensorConfigData"
          :editable="true"
          @add-config="handleAddConfig"
          @edit-config="handleEditConfig"
          @delete-config="handleDeleteConfig"
          @view-config="handleViewConfig"
          @reload="handleFileReload"
        />
      </div>

      <!-- 第三步：完成提示 -->
      <div v-if="currentStep === 2" class="step-complete">
        <div class="complete-content">
          <el-result
            icon="success"
            title="装车记录创建成功"
            sub-title="您的装车记录已成功创建并保存"
          >
            <template #extra>
              <el-button type="primary" @click="handleFinish">完成</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    </div>

    <!-- 弹窗底部按钮 -->
    <div slot="footer" class="dialog-footer" v-if="currentStep < 2">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="currentStep > 0" @click="handlePrevious">上一步</el-button>
      <el-button
        v-if="currentStep < 2"
        type="primary"
        @click="handleNext"
        :loading="loading"
      >
        下一步
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import FleetSensorConfigSection from './fleetSensorConfigSection.vue'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { listFtmVehicleVariantCascade } from '@/apis/fleet/ftm-vehicle-variant'
import util from '@/plugins/util'
import {
  listFtmVehicleMappingModality,
  deleteFtmVehicleMappingModalitys
} from '@/apis/fleet/ftm-variant-mapping-modality'
import GLB_CONFIG from '@/plugins/glb-constant'
import {
  showToast,
  showConfirmToast,
  validateFourDecimalValidity,
  validateTwoDigitInteger,
  isPositiveNum,
  isInteger,
  dateUtils
} from '@/plugins/util'
import {
  saveFtmVehicleInstallationRecord,
  updateFtmVehicleInstallationRecord,
  deleteFtmVehicleInstallationRecord,
  listFtmVehicleInstallationRecord,
  copyFtmVehicleInstallationRecord
} from '@/apis/fleet/ftm-vehicle-installation-record'


export default {
  name: 'AddVehicleInstallationRecord',
  components: {
    FleetSensorConfigSection
  },
  props: {
    vehicleData: {
      type: Object,
      default: () => ({})
    },
    prefilledData: {
      type: Object,
      default: () => ({})
    },
    // 是否作为子组件使用（不显示对话框）
    asSubComponent: {
      type: Boolean,
      default: false
    },
    variantVersionDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      currentStep: 0,
      loading: false,
      internalPrefilledData: {}, // 内部预填充数据状态
      formData: {
        vin: '',
        version: '',
        versionCascader: null,
        name: '',
        startTime: '',
        endTime: '',
        useType: '',
        description: '',
        code: '',
        variantVersionId: ''
      },
      sensorConfigData: {
        versionMappingModalityVOS: {
          modalityTypeMap: {}
        }
      },
      functionCategoryOptions: [],
      vehicleVersionCascaderOptions: [],
      cascaderProps: {
        value: 'code',
        label: 'label',
        children: 'children',
        emitPath: false
      },
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      basicInfoRules: {
        name: [
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ],
        versionCascader: [
          { required: true, message: '请选择车型版本', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
          { validator: this.validateStartTime, trigger: 'change' }
        ],
        endTime: [
          { validator: this.validateEndTime, trigger: 'change' }
        ],
        useType: [
          { required: true, message: '请选择功能分类', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.initializeData()
  },
  methods: {
    // 初始化数据
    async initializeData() {
      // 如果传入了车辆数据，预填充车架号
      if (this.vehicleData.vin) {
        this.formData.vin = this.vehicleData.vin
      }

      // 加载字典数据和级联选择器数据
      await Promise.all([
        this.loadDictionaryData(),
        this.loadVehicleVersionCascaderOptions()
      ])

      // 如果有预填充数据，则填充表单
      if (this.internalPrefilledData && Object.keys(this.internalPrefilledData).length > 0) {
        this.prefilFormData()
      }
    },

    // 加载字典数据
    async loadDictionaryData() {
      try {
        const res = await listSysDictionary({ typeCode: 'use_type' })
        this.functionCategoryOptions = res.data || []
      } catch (error) {
        console.error('加载功能分类选项失败:', error)
      }
    },

    // 加载车型版本级联选项
    async loadVehicleVersionCascaderOptions() {
      try {
        const res = await listFtmVehicleVariantCascade()
        this.vehicleVersionCascaderOptions = this.processVehicleVariantData(res.data || [])
      } catch (error) {
        console.error('加载车型版本级联选项失败:', error)
      }
    },

    // 处理车型版本数据
    processVehicleVariantData(data) {
      return data.map(variant => ({
        ...variant,
        label: variant.code,
        code: variant.code,
        children: variant.children ? variant.children.map(version => ({
          ...version,
          label: version.version,
          code: version.id
        })) : []
      }))
    },

    // 处理级联选择器变化
    handleVersionCascaderChange(value) {
      if (value) {
        this.findVersionInfoById(value)
      } else {
        this.formData.version = ''
        this.formData.variantVersionId = ''
        this.formData.code = ''
        this.formData.name = ''
      }
    },

    // 根据版本ID查找车型和版本信息
    findVersionInfoById(versionId) {
      for (const variant of this.vehicleVersionCascaderOptions) {
        if (variant.children) {
          for (const version of variant.children) {
            if (version.id === versionId) {
              // 第一级：将code字段赋值给code
              this.formData.code = variant.code
              // 第二级：记录version字段和id字段
              this.formData.version = version.version
              this.formData.variantVersionId = version.id

              return
            }
          }
        }
      }
    },

    // 预填充表单数据
    prefilFormData() {
      if (this.internalPrefilledData.version) {
        this.formData.version = this.internalPrefilledData.version
        this.formData.versionCascader = this.internalPrefilledData.versionCascader
      }
      if (this.internalPrefilledData.code) {
        this.formData.code = this.internalPrefilledData.code
      }
      if (this.internalPrefilledData.variantVersionId) {
        this.formData.variantVersionId = this.internalPrefilledData.variantVersionId
      }
      if (this.internalPrefilledData.name) {
        this.formData.name = this.internalPrefilledData.name
      }
      if (this.internalPrefilledData.useType) {
        this.formData.useType = this.internalPrefilledData.useType
      }
      if (this.internalPrefilledData.description) {
        this.formData.description = this.internalPrefilledData.description
      }
      if(this.internalPrefilledData.startTime){
        this.formData.startTime = this.internalPrefilledData.startTime
      }
      if(this.internalPrefilledData.id){
        this.formData.id = this.internalPrefilledData.id
      }
    },

    // 处理时间变化
    handleStartTimeChange() {
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm && this.formData.endTime) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    handleEndTimeChange() {
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    // 时间验证
    validateStartTime(rule, value, callback) {
      callback()
      this.$nextTick(() => {
        if (this.$refs.basicInfoForm && this.formData.endTime) {
          this.$refs.basicInfoForm.validateField('endTime')
        }
      })
    },

    validateEndTime(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      if (!this.formData.startTime) {
        callback()
        return
      }

      const startDate = new Date(this.formData.startTime)
      const endDate = new Date(value)

      if (endDate <= startDate) {
        callback(new Error('结束时间不能早于开始时间'))
      } else {
        callback()
      }
    },

    // 步骤控制
    async handleNext() {
      if (this.currentStep === 0) {
        // 验证第一步表单
        const valid = await this.validateBasicInfo()
        if (valid) {
          this.prepareSensorConfigData()
        }
      } else if (this.currentStep === 1) {
        // 保存装车记录
        this.currentStep++
       // await this.saveInstallationRecord()
      }
    },

    handlePrevious() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 验证基础信息
    validateBasicInfo() {
      return new Promise((resolve) => {
        this.$refs.basicInfoForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 准备传感器配置数据
    async prepareSensorConfigData() {
        // 判断是克隆还是新建
        const isClone = this.internalPrefilledData && Object.keys(this.internalPrefilledData).length > 0

        let saveData = {
          ...this.formData,
           vin: this.vehicleData.vin,
          vehicleId: this.vehicleData.id,
        }
        // 如果是克隆模式，添加原始记录ID
        if (isClone && this.internalPrefilledData.originalRecordId) {
          saveData.originalRecordId = this.internalPrefilledData.originalRecordId
        }

        let res
        if (isClone) {
          saveData.cloneId = this.internalPrefilledData.originalRecordId
          saveData.id = undefined
          res = await copyFtmVehicleInstallationRecord(saveData)
        } else {
          res = await saveFtmVehicleInstallationRecord(saveData)
        }
         if (res.data) {
           this.formData = res.data
           this.sensorConfigData = {
             ...res.data,
             vehicleId: this.vehicleData.id
           }
           this.currentStep++
           showToast('保存成功')
           this.getModalityList()
           this.$emit('finish')
         }
       

    },
    getModalityList(){
      let postData = {
        vehicleId: this.vehicleData.id,
        variantVersionId: this.formData.variantVersionId,
        installationRecordId: this.formData.id
      }
      listFtmVehicleMappingModality(postData).then(res => {
        this.sensorConfigData.versionMappingModalityVOS = res.data
        this.sensorConfigData.versionMappingModalityVOS.forEach(val => {
          if (val.files?.length) {
            val.files.forEach(f => {
              f.url = this.downloadUrl + f.id + '?token=' + util.getToken()
            })
          }
        })
      })
    
    },

    // 保存装车记录
    async saveInstallationRecord() {
      this.loading = true
      try {
        const saveData = {
          ...this.formData,
           vin: this.vehicleData.vin,
          vehicleId: this.vehicleData.id,
        }

        await saveFtmVehicleInstallationRecord(saveData)
        this.$message.success('装车记录创建成功')

        // 关闭弹窗并触发完成事件
        this.closeDialog()
        this.$emit('finish')
      } catch (error) {
        console.error('保存装车记录失败:', error)
       // this.$message.error('保存失败，请重试')
      } finally {
        this.loading = false
      }
    },

    // 传感器配置事件处理
    handleAddConfig() {
      this.getModalityList()
      this.getModalityCount()
    },

    handleEditConfig() {
      this.getModalityList()
      this.getModalityCount()
    },

    handleDeleteConfig() {
      this.getModalityList()
      this.getModalityCount()

    },
    handleFileReload(){
       this.getModalityList()
    },
    async getModalityCount(){
      const res = await listFtmVehicleInstallationRecord({vin:this.vehicleData.vin})
      this.versionList = res.data || []
      const item = this.versionList.find(item => item.id === this.sensorConfigData.id)
      if(item){
        // 使用对象展开运算符确保响应式更新
        this.sensorConfigData = {
          ...this.sensorConfigData,
          modalityTypeMap: item.modalityTypeMap
        }
      }

    },

    handleViewConfig() {

    },

    // 完成操作
    handleFinish() {
      this.closeDialog()
      this.$emit('finish')
    },

    handleCreateAnother() {
      // 重置表单数据
      this.resetForm()
      this.currentStep = 0
    },

    // 重置表单
    resetForm() {
      this.formData = {
        vin: this.vehicleData.vin || '',
        version: '',
        versionCascader: null,
        name: '',
        startTime: '',
        endTime: '',
        useType: '',
        description: '',
        code: '',
        variantVersionId: ''
      }
      this.sensorConfigData = {
        versionMappingModalityVOS: {
          modalityTypeMap: {}
        }
      }
      this.internalPrefilledData = {} // 重置内部预填充数据
      if (this.$refs.basicInfoForm) {
        this.$refs.basicInfoForm.clearValidate()
      }
    },

    // 打开弹窗
    openDialog(prefilledData = null) {
      this.dialogVisible = true
      this.resetForm()
      this.currentStep = 0

      // 设置内部预填充数据状态
      if (prefilledData) {
        this.internalPrefilledData = { ...prefilledData }
      } else {
        this.internalPrefilledData = {}
      }

      this.$nextTick(() => {
        this.initializeData()
      })
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false
      this.resetForm()
      this.currentStep = 0
    },

    // 处理弹窗关闭事件
    handleDialogClose() {
      this.closeDialog()
    },

    // 处理取消按钮
    handleCancel() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.add-vehicle-installation-record-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.add-vehicle-installation-record {
  .step-content {
    margin: 20px 0;
    min-height: 350px;

    .step-basic-info {
      .basic-info-form {
        max-width: 100%;
      }
    }

    .step-sensor-config {
      padding: 10px 0;
    }

    .step-complete {
      .complete-content {
        text-align: center;
        padding: 50px 0;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>
