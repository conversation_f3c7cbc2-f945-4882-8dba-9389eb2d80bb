<template>
  <div class="map-info-window">
    <div class="window-content">
      <div class="item">
        <div class="item-label">{{ $t('地址') }}：</div>
        <div class="num item-value">{{ row.title }}</div>
      </div>
      <div class="item">
        <div class="item-label">{{ $t('经度') }}：</div>
        <div class="num item-value">{{ row.lat }}</div>
      </div>
      <div class="item">
        <div class="item-label">{{ $t('纬度') }}：</div>
        <div class="num item-value">{{ row.lng }}</div>
      </div>
    </div>
    <div class="window-footer">
      <div v-if="opt === 'choose'">
        <el-button plain @click="setPoint('cancel')" size="small">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="setPoint('confirm')" size="small">{{ $t('确认') }}</el-button>
      </div>
      <div v-else>
        <el-button type="success" @click="setPoint('start')" size="small">{{ $t('设为起点') }}</el-button>
        <el-button type="warning" @click="setPoint('pass')" size="small">{{ $t('设为途经点') }}</el-button>
        <el-button type="danger" @click="setPoint('end')" size="small">{{ $t('设为终点') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { i18n } from '@/plugins/lang'
import { ElButton } from 'element-plus'

export default {
  name: 'MarkerInfoWindow',
  emits: ['setPoint'],
  data() {
    return {
      $t: i18n.global.t,
      row: {},
      opt: ''
    }
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  watch: {
    item: {
      handler(val) {
        this.row = val
      },
      deep: true,
      immediate: true
    },
    type: {
      handler(val) {
        this.opt = val
      },
      deep: true,
      immediate: true
    }
  },
  components: { ElButton },
  mounted() {},
  methods: {
    setPoint(type) {
      this.$emit('setPoint', {
        ...this.row,
        type
      })
    }
  }
}
</script>

<style scoped lang="scss">
.map-info-window {
  background: #fff;
  padding: 20px 30px;
  border: 1px solid #e4e7ed;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);

  .window-content {
    .item {
      display: flex;
      color: #606266;
      font-size: 14px;
      line-height: 20px;

      .item-label {
      }
    }
  }

  .window-footer {
    margin-top: 10px;
  }
}
</style>
