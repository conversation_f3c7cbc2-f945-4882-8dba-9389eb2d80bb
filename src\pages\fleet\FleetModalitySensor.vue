<template>
  <div class="fleet-modality-sensor">
    <div class="has-data" v-if="backupPageData?.length">
      <el-card class="header" shadow="hover">
        <ltw-icon icon-code="svg-sensor-filter"></ltw-icon>
        <div class="button-group">
          <el-button
            class="btn"
            @click="filterPageData(item)"
            v-for="(item, index) in backupPageData"
            :key="index"
            :class="{ active: item.checked }"
          >
            <ltw-icon v-show="item.checked" icon-code="el-icon-check"></ltw-icon>
            <span>{{ item.name }}</span></el-button
          >
          <el-button class="reset" type="primary" @click="resetPageData()">
            <span>{{ $t(checkAll ? '取消全选' : '全选') }}</span></el-button
          >
        </div>
      </el-card>
      <div class="sensor-body">
        <template v-for="sensor in newVersionPageData" :key="sensor.code">
          <el-card
            @mouseenter="cardMouseEnter(sensor)"
            shadow="hover"
            class="sensor-card"
            :class="{ 'sensor-card-hover': sensor.active }"
          >
            <template #header>
              <div class="card-header">
                <div class="title">
                  <!-- <ltw-icon :icon-code="iconCode(sensor.code)"></ltw-icon> -->
                  {{ sensor.name }}
                </div>
                <div class="button-group">
                  <div
                    class="tab-btn"
                    :class="{ active: sensor.currentState === 'modality' }"
                    @click="changeSensorState(sensor, 'modality')"
                  >
                    modality
                  </div>
                  <div
                    class="tab-btn"
                    :class="{ active: sensor.currentState === 'sensor' }"
                    @click="changeSensorState(sensor, 'sensor')"
                  >
                    sensor
                  </div>
                </div>
              </div>
            </template>
            <div v-if="sensor.currentState === 'modality'">
              <div class="bs-tag-container">
                <template v-for="modality in sensor.modalityVOS" :key="modality.id">
                  <el-tooltip :enterable="false" :content="modality.name">
                    <el-tag
                      plain
                      type="success"
                      :closable="sensor.active"
                      @close="singleRemoveModality(modality)"
                      @click="editModality(modality)"
                    >
                      <div class="code-name-container">
                        <span class="code">{{ modality.code }}</span>
                        <!-- <span class="name">{{ modality.name }}</span> -->
                      </div>
                    </el-tag>
                  </el-tooltip>
                </template>
                <el-button
                  type="primary"
                  size="small"
                  plain
                  :class="{ active: sensor.active }"
                  @click="addModality(sensor)"
                >
                  <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                  {{ $t('新增') }}
                </el-button>
              </div>
            </div>
            <div v-if="sensor.currentState === 'sensor'">
              <el-button type="primary" size="small" plain @click="addSensor(sensor)">
                <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                {{ $t('新增') }}
              </el-button>
              <el-table
                :data="sensor.sensorVOS"
                stripe
                border
                @selection-change="handleSelectionChange"
                row-key="id"
                ref="tableRef"
              >
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="supplierName"
                  :label="$t('供应商')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="model"
                  :label="$t('型号')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="specification"
                  show-overflow-tooltip
                  :label="$t('规格')"
                ></el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="remark"
                  show-overflow-tooltip
                  :label="$t('备注')"
                ></el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="110" fixed="right">
                  <template #default="scope">
                    <el-button-group>
                      <el-tooltip effect="dark" :content="$t('编辑')" placement="top" :enterable="false">
                        <el-button type="warning" text size="small" @click="editSensor(scope.row)">
                          <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                        <el-button type="danger" text size="small" @click="singleRemoveSensor(scope.row)">
                          <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                        </el-button>
                      </el-tooltip>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </template>
      </div>
    </div>
    <el-empty v-if="newVersionPageData.length === 0" description="暂无数据"></el-empty>
  </div>
  <AddModality ref="AddModality" @reload="query" />
  <AddSensor ref="AddSensor" @reload="query" />
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util.js'
import { deleteFtmVehicleModality } from '@/apis/fleet/ftm-vehicle-modality'
import { deleteFtmSensor, querySensorListGroupByType } from '@/apis/fleet/ftm-sensor'
import LtwIcon from '@/components/base/LtwIcon.vue'
import AddModality from '@/pages/fleet/dialog/AddFleetModalitySensorModality.vue'
import AddSensor from '@/pages/fleet/dialog/AddSensor.vue'

const defaultFormData = {}
export default {
  name: 'FtmVehicleModality',
  components: {
    LtwIcon,
    AddModality,
    AddSensor
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      newVersionPageData: [],
      backupPageData: [],
      queryParam: {
        current: 1,
        size: 10
      },
      checkSensorCodeList: [],
      sensorTypeCodeList: [],
      version: 1,
      selectedData: []
    }
  },
  computed: {
    checkAll() {
      return this.backupPageData.every(val => val.checked)
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
  },
  methods: {
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      querySensorListGroupByType().then(res => {
        if (this.backupPageData?.length) {
          this.backupPageData.forEach(val => {
            res.data.forEach(item => {
              if (val.code === item.code) {
                item.currentState = val.currentState
                item.checked = val.checked
              }
            })
          })
        } else {
          res.data.forEach(val => (val.checked = true))
        }
        this.backupPageData = res.data
        this.newVersionPageData = res.data
        // this.sensorTypeCodeList = res.data.map(item => item.code)
        // this.checkSensorCodeList = this.sensorTypeCodeList
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemoveModality') {
        this.batchRemoveModality()
      } else if (command === 'batchRemoveSensor') {
        this.batchRemoveSensor()
      }
    },
    singleRemoveModality(row) {
      this.removeModality({ id: row.id })
    },
    batchRemoveModality() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.removeModality({ ids })
    },
    removeModality(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmVehicleModality(param).then(() => {
          this.query()
        })
      })
    },
    addModality(row) {
      this.$refs.AddModality.show({ type: 'add', sensorType: row?.code })
    },
    editModality(row) {
      this.$refs.AddModality.show({ type: 'edit', id: row.id })
    },
    viewModality(row) {
      this.$refs.AddModality.show({ type: 'view', id: row.id })
    },
    singleRemoveSensor(row) {
      this.removeSensor({ id: row.id })
    },
    batchRemoveSensor() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.removeSensor({ ids })
    },
    removeSensor(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmSensor(param).then(() => {
          this.query()
        })
      })
    },
    addSensor(row) {
      this.$refs.AddSensor.show({ type: 'add', sensorType: row?.code })
    },
    editSensor(row) {
      this.$refs.AddSensor.show({ type: 'edit', id: row.id })
    },
    viewSensor(row) {
      this.$refs.AddSensor.show({ type: 'view', id: row.id })
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    changeSensorState(sensor, data) {
      sensor.currentState = data
    },
    switchVersion() {
      if (this.version === 2 || this.version === 3) {
        this.queryParam = {
          current: 1,
          size: 10
        }
      }
      this.query()
    },
    filterPageData(item) {
      this.newVersionPageData = []
      item.checked = !item.checked
      this.backupPageData.forEach(val => {
        if (val.checked) {
          this.newVersionPageData.push(val)
        }
      })
    },
    resetPageData() {
      this.newVersionPageData = []
      const checkAll = this.checkAll
      this.backupPageData.forEach(val => {
        val.checked = !checkAll
        if (val.checked) {
          this.newVersionPageData.push(val)
        }
      })
    },
    // togglePageData(flag){
    //   this.backupPageData.forEach(val=>val.checked = flag)
    //   this.newVersionPageData = JSON.parse(JSON.stringify(this.backupPageData.filter(item => item.checked)))
    // },
    cardMouseEnter(row) {
      this.newVersionPageData.forEach(val => {
        if (val.code === row.code) {
          val.active = true
        } else {
          val.active = false
        }
      })
    }
    // cardMouseLeave(row) {
    //   row.active = false
    // }
  }
}
</script>

<style scoped lang="scss">
// .version-switch {
//   position: absolute;
//   right: 10px;
//   top: 0;
// }
.fleet-modality-sensor {
  padding: 0 10px;

  .has-data {
  }

  .el-empty {
    background: white;
  }

  .header {
    margin-bottom: 10px;

    :deep(.el-card__body) {
      display: flex;
      flex-direction: row;
      padding: var(--el-card-padding) var(--el-card-padding) 10px var(--el-card-padding);

      .svg-icon {
        margin-right: 10px;
        font-size: 30px;
      }

      .button-group {
        // display: flex;
        .btn {
          border-radius: 10px;
          transition: all 0.3s;
          font-weight: 600;
          margin-bottom: 10px;
          margin-left: 0;

          &:focus,
          &:hover {
            background-color: white;
          }

          &:focus {
            color: var(--el-button-text-color);
            border-color: var(--el-button-border-color);
          }

          &:hover {
            border-color: #409eff;
            color: rgb(15, 86, 179);
            font-weight: 600;
          }

          &.active {
            border-color: #409eff;
            color: rgb(15, 86, 179);

            .ltw-icon {
              margin-right: 4px;
              font-size: 18px;
            }
          }
        }

        .reset {
          font-weight: 600;
          margin-bottom: 10px;
          margin-left: 0;
        }
      }
    }
  }

  .sensor-body {
    column-gap: 14px;
    columns: 3;
  }

  @media screen and (min-width: 1600px) {
    .sensor-body {
      columns: 3;
    }
  }
  @media screen and (min-width: 1000px) and (max-width: 1600px) {
    .sensor-body {
      columns: 2;
    }
  }
  @media screen and (max-width: 1000px) {
    .sensor-body {
      columns: 1;
    }
  }

  .sensor-card {
    transition: all 0.3s;
    margin-bottom: 10px;
    border-radius: 10px;
    break-inside: avoid;
    cursor: pointer;

    &.sensor-card-hover {
      transform: scale(1.01);
    }

    // width: 50%;
    :deep(.el-card__header) {
      padding: 10px;
    }

    .card-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .title {
        display: flex;
        align-items: center;

        .ltw-icon {
          margin-right: 4px;
        }
      }

      .button-group {
        .tab-btn {
          line-height: 30px;
          margin-right: 10px;
          cursor: pointer;
          position: relative;
          font-size: 12px;
          display: inline-block;

          &.active {
            color: #409eff;

            &::after {
              transform: scale(1);
            }
          }

          &:hover {
            color: #409eff;

            &::after {
              transform: scale(1);
            }
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 5%;
            width: 90%;
            height: 1px;
            background-color: var(--el-color-primary);
            transition: transform 0.25s ease-in-out;
            transform: scale(0);
          }
        }

        // .el-button {
        //   // margin-bottom: 10px;
        //   margin-left: 0px;
        //   &.active {
        //     background: var(--el-color-primary);
        //     border-color: var(--el-color-primary-light-5);
        //     color: var(--el-color-white);
        //   }
        // }
      }
    }

    .bs-tag-container {
      display: flex;
      flex-wrap: wrap;

      .el-button {
        transition: all 0.3s;
        opacity: 0;

        &.active {
          opacity: 1;
        }
      }

      // .el-button {
      .el-tag {
        cursor: pointer;
        margin: 0 8px 10px 0;

        .code-name-container {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .code {
            // font-weight: 700;
          }
        }
      }
    }
  }
}
</style>
