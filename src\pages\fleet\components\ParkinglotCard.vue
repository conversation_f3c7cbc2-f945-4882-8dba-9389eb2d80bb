<template>
  <el-card class="content-card" :class="{ checked: form.checked }">
    <div class="card-title">
      <div class="title-left">
        <el-checkbox class="title-item" v-model="form.checked" />
        <div class="code-container">
          <el-tooltip 
            v-if="form.isLocked === 1" 
            effect="dark" 
            placement="top" 
            content="该停车场已锁定"
          >
            <ltw-icon icon-code="svg-lock-red" class="lock-icon"></ltw-icon>
          </el-tooltip>
          <div class="code">{{ form.code }}</div>
        </div>
        <el-divider class="title-item" direction="vertical" />
        <div class="title-name">
          <el-tooltip effect="dark" placement="top-start" :content="form.name">{{ form.name }}</el-tooltip>
        </div>
      </div>
      <div class="title-right">
        <el-dropdown placement="top" trigger="click">
          <el-button class="button" text>
            <ltw-icon icon-code="el-icon-more"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-link
                  class="dropdown-link"
                  type="warning"
                  @click="optCard('edit', form.id)"
                  :underline="false"
                  id="edit"
                >
                  {{ $t('编辑') }}
                </el-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-link
                  class="dropdown-link"
                  type="danger"
                  @click="optCard('delete', form.id)"
                  :underline="false"
                  id="delete"
                >
                  {{ $t('删除') }}
                </el-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-link
                  class="dropdown-link"
                  type="primary"
                  @click="optCard('view', form.id)"
                  :underline="false"
                  id="detail"
                >
                  {{ $t('查看详情') }}
                </el-link>
              </el-dropdown-item> 
              <!--              <el-dropdown-item>-->
              <!--                <el-link-->
              <!--                  class="dropdown-link"-->
              <!--                  type="primary"-->
              <!--                  @click="optCard('data_statistic', form.id)"-->
              <!--                  :underline="false"-->
              <!--                  id="detail"-->
              <!--                >-->
              <!--                  {{ $t('数据统计') }}-->
              <!--                </el-link>-->
              <!--              </el-dropdown-item>-->
              <!--              <el-dropdown-item>-->
              <!--                <el-link-->
              <!--                  class="dropdown-link"-->
              <!--                  type="primary"-->
              <!--                  @click="optCard('copy', form.id)"-->
              <!--                  :underline="false"-->
              <!--                  id="detail"-->
              <!--                >-->
              <!--                  {{ $t('复制') }}-->
              <!--                </el-link>-->
              <!--              </el-dropdown-item>-->
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="card-content">
      <div class="content-item">
        <ltw-icon icon-code="el-icon-location" @click="showMapMarker()"></ltw-icon>
        <el-tooltip effect="dark" placement="top-start" :content="form.address">
          <div class="address">{{ form.address }}</div>
        </el-tooltip>
      </div>
    </div>
    <div class="card-footer">
      <el-collapse accordion>
        <el-collapse-item name="1" :class="{ 'no-collapse': !form.parkingLotItems?.length }">
          <template #title>
            <div class="collapse-title">
              <el-button class="tag-button" v-if="form.typeName" plain color="#5EBD82" size="small">
                {{ form.typeName }}
              </el-button>
              <el-button class="tag-button" v-if="form.envTypeName" plain type="warning" size="small">
                {{ form.envTypeName }}
              </el-button>
            </div>
          </template>
          <div>
            <el-button
              plain
              color="#5755FF"
              v-for="item in form.parkingLotItems"
              :key="item.id"
              class="tag-button tag-item"
              size="small"
            >
              {{ item.itemName }}
            </el-button>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-card>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import { deleteDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import { lockMdParkingLot } from '@/apis/fleet/parking-lot-management'
import LtwIcon from '@/components/base/LtwIcon.vue'

export default {
  name: 'TaskForm',
  emits: ['reload', 'opt-function', 'show-map-marker'],
  data() {
    return {
      lockLoading: false
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    },
    batchingFunctionList: {
      type: Array,
      default: []
    },
    outlineFunctionList: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    selectable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    form() {
      return this.item
    }
  },
  components: {
    LtwIcon
  },

  methods: {
    cancel(val) {
      this.$emit('reload', val)
    },
    optCard(type, id) {
      this.$emit('opt-function', { type: type, id })
    },
    delete(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail({ id }).then(() => {
          showToast('删除成功')
          this.cancel()
        })
      })
    },
    showMapMarker() {
      this.$emit('show-map-marker', this.form)
    },
    async toggleLock() {
      this.lockLoading = true
      try {
        const lockData = {
          parkingLotId: this.form.id,
          isLocked: !this.form.isLocked
        }
        
        await lockMdParkingLot(lockData)
        
        // 更新本地状态
        this.form.isLocked = !this.form.isLocked
        
        // 显示成功提示
        const actionText = this.form.isLocked ? '锁定' : '解锁'
        showToast(`${actionText}成功`)
        
        // 通知父组件刷新数据
        this.$emit('reload')
      } catch (error) {
        console.error('锁定操作失败:', error)
        showToast('操作失败，请重试', 'error')
      } finally {
        this.lockLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.content-card {
  transition: all 0.3s;
  margin-bottom: 10px;
  break-inside: avoid;
  cursor: pointer;
  border-radius: 2px;

  &.checked {
    border-color: #5755ff;
  }

  :deep(.el-checkbox__input.is-checked) {
    .el-checkbox__inner {
      background: #5755ff;
    }
  }

  :deep(.el-card__body) {
    padding: 12px;
  }

  &.content-card-hover {
    transform: scale(1.01);
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    //align-items: center;

    .title-left {
      display: flex;
      align-items: center;
      width: calc(100% - 44px);
      font-size: 14px;
      //white-space: nowrap;
      //overflow: hidden;
      //text-overflow: ellipsis;
      //cursor: pointer;

      .title-item {
        margin-right: 8px;
      }

      .title-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        width: calc(100% - 16px - 100px);
      }

      .code-container {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .lock-icon {
          width: 14px;
          height: 14px;
          opacity: 0.8;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .code {
          white-space: nowrap;
          color: #6264d5;
          line-height: 1;
        }
      }
    }

    .title-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  .tag-button{
    pointer-events: none;
  }

  .card-content {
    border-bottom: 1px solid #ebeef5;

    .content-item {
      display: flex;
      align-items: center;
      color: #4e5256;

      .address {
        font-size: 12px;
        width: calc(100% - 20px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        //width: 100%;
      }

      .ltw-icon {
        margin-right: 4px;
      }

      margin-bottom: 14px;
    }
  }

  .card-footer {
    .tag-item {
      margin-right: 12px;
      margin-left: 0;
      //font-size: 12px;
    }

    .collapse-title {
      overflow: hidden;
      width: calc(100% - 40px);
    }

    .el-collapse {
      .no-collapse :deep(.el-icon) {
        display: none;
      }

      :deep(.el-collapse-item__header) {
        border-bottom: none;
        white-space: nowrap;
      }

      :deep(.el-collapse-item__wrap) {
        border-bottom: none;
      }

      :deep(.el-collapse-item__content) {
        padding-bottom: 0;
      }

      .el-button {
        margin-bottom: 6px;
      }
    }
  }
}
</style>
