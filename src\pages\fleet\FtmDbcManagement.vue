<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <el-select
          id="variantId"
          v-model="queryParam.variantId"
          filterable
          :placeholder="$t('请选择') + $t('车型')"
          clearable
          :loading="!isVehicleVariantListReady"
          @change="refresh"
        >
          <el-option v-for="item in vehicleVariantList" :key="item.id" :label="item.code" :value="item.id">
            <div>{{ item.code }}</div>
          </el-option>
        </el-select>
        <!--        <div class="ltw-search-container ltw-tool-container">-->
        <!--          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">-->
        <!--            <template #append>-->
        <!--              <el-button @click="refresh">-->
        <!--                <ltw-icon icon-code="el-icon-search"></ltw-icon>-->
        <!--              </el-button>-->
        <!--            </template>-->
        <!--          </el-input>-->
        <!--        </div>-->
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        border
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
      >
        <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
        <el-table-column header-align="center" align="center" prop="variantName" :label="$t('车型')"></el-table-column>

        <el-table-column :label="$t('版本')" prop="version"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="startTime"
          :label="$t('生效时间')"
          width="140"
        ></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="endTime"
          width="140"
          :label="$t('失效时间')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="enabled" label="是否启用">
          <template #default="scope">
            {{ scope.row.enable ? '启用' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="description"
          :label="$t('描述')"
          show-tooltip-when-overflow
        >
        </el-table-column>
        <el-table-column header-align="center" align="center" :label="$t('操作')" width="200">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" size="small" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
      destroy-on-close
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('车型')" prop="variantId">
              <el-select
                id="variantId"
                v-model="formData.variantId"
                :disabled="formReadonly"
                filterable
                :placeholder="$t('请选择') + $t('车型')"
                clearable
                :loading="!isVehicleVariantListReady"
                @change="changeVariant"
              >
                <el-option
                  v-for="item in vehicleVariantList"
                  :key="item.id"
                  :label="item.code"
                  :value="item.id"
                >
                  <div>{{ item.code }}</div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('版本')" prop="version">
              <ltw-input :limitSize="10" v-model="formData.version" :disabled="formReadonly" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('生效时间')" prop="startTime">
              <el-date-picker
                :disabled="formReadonly"
                v-model="formData.startTime"
                type="datetime"
                :placeholder="$t('生效时间')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('失效时间')" prop="endTime">
              <el-date-picker
                :disabled="formReadonly"
                v-model="formData.endTime"
                :disabled-date="disabledEndDate"
                type="datetime"
                :placeholder="$t('失效时间')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item :label="$t('描述')" prop="description">
              <ltw-input type="textarea" :limit-size="128" v-model="formData.description" :disabled="formReadonly" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('是否启用')" id="enable" prop="enable">
              <el-switch
                :disabled="formReadonly"
                v-model="formData.enable"
                inline-prompt
                :active-text="$t('是')"
                :inactive-text="$t('否')"
                id="enable"
                style="--el-switch-on-color: #13ce66"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item id="calibra_file_form" :label="$t('文件')" prop="dbcFiles">
          <el-button v-if="!formReadonly" type="primary" size="small" class="add-file" @click="addFiles"
            >新增
          </el-button>
          <el-table :data="formData.dbcFiles" style="width: 100%" class="no-header-table">
            <el-table-column prop="sourceType" align="center" header-align="center" width="200">
              <template #header>
                <span>文件类型<span style="color: red;">*</span></span>
              </template>
              <template #default="scope">
                <span v-if="formReadonly || scope.row.dbcVersionId">{{ scope.row.dbcFileType }}</span>
                <ltw-input 
                  v-else 
                  v-model="scope.row.dbcFileType" 
                  placeholder="请输入文件类型"
                  :class="{ 'required-field': !scope.row.dbcFileType }"
                />
              </template>
            </el-table-column>
            <el-table-column label="附件">
              <template #default="scope">
                <!--                {{ scope.row.dbcFileType }}-->
                <!--                {{ JSON.stringify(scope.row.fileList) }}-->
                <el-upload
                  :action="uploadServer"
                  :class="{ 'hide-upload-btn': scope.row.fileList?.length === limit }"
                  :key="scope.$index"
                  :disabled="formReadonly || scope.row.id ? true : false"
                  :limit="limit"
                  :file-list="scope.row.fileList"
                  :http-request="param => handleUploadForm(param, scope.row)"
                  :on-preview="handlePictureCardPreview"
                >
                  <el-button type="primary" size="small" :disabled="formReadonly || scope.row.id ? true : false">
                    上传文件
                  </el-button>
                </el-upload>
                <!--                                <upload-file-->
                <!--                                  :key="scope.$index"-->
                <!--                                  class="upload-file"-->
                <!--                                  :ref="'uploadImage' + scope.row.uuid"-->
                <!--                                  :disabled="formReadonly || scope.row.dbcVersionId"-->
                <!--                                  :source-id="scope.row.id"-->
                <!--                                  source-type="dbc_file"-->
                <!--                                  :limit="1"-->
                <!--                                  listType="text"-->
                <!--                                  :table-file-list="scope.row.dbcFiles"-->
                <!--                                  v-model="scope.row.fileId"-->
                <!--                                />-->
                <!--                @updateFileList="(list)=>updateFileList(list, scope.row)"-->
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="operation" width="160">
              <template #default="scope">
                <el-button-group>
                  <el-popconfirm
                    v-if="!formReadonly"
                    width="250"
                    :title="$t('这个操作会删除当前选择的数据，是否继续？')"
                    @confirm="deleteSysFile(scope.row, scope.$index)"
                  >
                    <template #reference>
                      <!--                      <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">-->
                      <el-button type="danger" size="small">
                        <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                      </el-button>
                      <!--                      </el-tooltip>-->
                    </template>
                  </el-popconfirm>
                  <el-tooltip effect="dark" :content="$t('查看')" placement="top" :enterable="false">
                    <el-button v-if="scope.row.id" type="primary" size="small" @click="viewDbcFile(scope.row)">
                      <ltw-icon icon-code="el-icon-view"></ltw-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="dialogStatus === 'view'">
            <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
            <el-button
              :type="item.buttonStyleType"
              @click="executeButtonMethod(currentButton)"
              v-if="currentButton && currentButton.name"
              >{{ $t(currentButton.name) }}</el-button
            >
          </template>
          <template v-else>
            <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
            <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
  <ftm-dbc-detail ref="FtmDbcDetail" />
  <el-image-viewer v-if="dialogImageVisible" @close="closeViewer" :url-list="srcList" />
</template>

<script>
import FtmDbcDetail from '@/pages/fleet/dialog/FtmDbcDetail.vue'
import { saveDbc, updateDbc, deleteDbc, pageDbc, getDbc, cloneDbc, getVariantDbcFiles } from '@/apis/fleet/ftm-dbc'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast, dateUtils } from '@/plugins/util'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import { getUuid } from '../../plugins/util'
import {
  uploadFile,
  getFileList
} from '@/apis/base/file'

const defaultFormData = {
  enable: true,
  dbcFiles: []
}
export default {
  name: 'FtmDbc',
  components: {
    FtmDbcDetail
  },
  data() {
    return {
      uploadServer: '',
      token: util.getToken(),
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      vehicleVariantList: [],
      // 添加车型dbc文件缓存
      variantDbcFilesCache: {},
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        variantId: null // 确保初始化为null，不默认选中任何车型
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        variantId: [{ required: true, message: this.$t('请输入车型id'), trigger: 'blur' }],
        dbcFiles: [{ required: true, message: this.$t('请上传dbc文件'), trigger: 'blur' }],
        version: [{ required: true, message: this.$t('请输入版本'), trigger: 'blur' }],
        startTime: [{ required: true, message: this.$t('请选择生效时间'), trigger: 'blur' }],
        endTime: [{ 
          validator: (rule, value, callback) => {
            if (value && this.formData.startTime) {
              if (new Date(value) <= new Date(this.formData.startTime)) {
                callback(new Error(this.$t('失效时间必须在生效时间之后')))
              } else {
                callback()
              }
            } else {
              callback()
            }
          },
          trigger: ['blur', 'change']
        }]
      },
      dialogTitle: 'DBC管理',
      dialogStatus: '',
      selectedData: [],
      currentButton: {},
      limit: 1,
      srcList: [],
      dialogImageVisible: false
    }
  },
  async created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    // 先初始化车型列表，再查询数据
    await this.initVariantDbcFilesCache()
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    },
    // 确保车型列表已加载
    isVehicleVariantListReady() {
      return this.vehicleVariantList.length > 0
    }
  },

  methods: {
    copy(row) {
      this.dialogStatus = 'copy'
      this.dialogTitle = this.$t('复制DBC')
      getDbc(row.id).then(res => {
        this.formData = res.data
        // 复制时清空ID，确保是新增操作
        this.formData.id = null
        if (this.formData?.dbcFiles?.length) {
          this.formData?.dbcFiles.map(async val => {
            val.fileList = await this.downloadFile(val.id)
          })
        }
        this.dialogVisible = true
      })
    },
    handleDownload(sourceId) {
      window.open(this.downloadUrl + sourceId + '?token=' + util.getToken())
    },
    checkFileSize(data) {
      if (data > 0 && data < Math.pow(2, 10)) {
        return { data, unit: 'B' }
      } else if (data >= Math.pow(2, 10) && data < Math.pow(2, 20)) {
        return { data: parseFloat((data / 1024).toFixed(2)), unit: 'KB' }
      } else if (data >= Math.pow(2, 20) && data < Math.pow(2, 30)) {
        return { data: parseFloat((data / 1024 / 1024).toFixed(2)), unit: 'M' }
      } else if (data >= Math.pow(2, 30) && data < Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'G'
        }
      } else if (data >= Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'T'
        }
      } else {
        return { data: 0, unit: '' }
      }
    },
    // 初始化车型dbc文件缓存
    async initVariantDbcFilesCache() {
      try {
        // 直接调用getVariantDbcFiles获取所有车型和对应的dbc文件
        const res = await getVariantDbcFiles()
        
        // 清空现有数据，确保重新加载
        this.vehicleVariantList = []
        this.variantDbcFilesCache = {}
        
        if (res.data?.length) {
          // 处理返回的数据，提取车型信息和dbc文件
          res.data.forEach(variantData => {
            // 假设返回的数据结构包含车型信息和dbc文件
            const variantId = variantData.id
            const variantCode = variantData.code
            
            // 添加到车型列表
            this.vehicleVariantList.push({
              id: variantId,
              code: variantCode
            })
            // 缓存dbc文件
            if (variantData.dbcFiles && variantData.dbcFiles.length > 0) {
              this.variantDbcFilesCache[variantId] = variantData.dbcFiles
            } else {
              this.variantDbcFilesCache[variantId] = []
            }
          })
        }
      } catch (error) {
        console.error('初始化车型和dbc文件缓存失败:', error)
      }
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageDbc(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    async save() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          try {
            if (this.formData.endTime && new Date(this.formData.startTime) >= new Date(this.formData.endTime)) {
              showToast(this.$t('失效时间必须在生效时间之后'), 'warning')
              return
            }
            
            // 校验文件类型必填
            const invalidFiles = this.formData.dbcFiles.filter(file => !file.dbcFileType || file.dbcFileType.trim() === '')
            if (invalidFiles.length > 0) {
              showToast(this.$t('请填写文件类型'), 'warning')
              return
            }
            
            // 校验文件必须上传
            const filesWithoutUpload = this.formData.dbcFiles.filter(file => !file.fileList || file.fileList.length === 0)
            if (filesWithoutUpload.length > 0) {
              showToast(this.$t('请上传文件'), 'warning')
              return
            }
            
            // 确保fileId字段正确设置
            const postData = JSON.parse(JSON.stringify(this.formData))
            postData.dbcFiles.forEach(file => {
              if (file.fileList && file.fileList.length > 0 && !file.fileId) {
                file.fileId = file.fileList[0].id
              }
            })
            
            if (this.dialogStatus === 'add') {
              await saveDbc(postData)
            } else if (this.dialogStatus === 'edit') {
              await updateDbc(postData)
            } else if (this.dialogStatus === 'copy') {
              await cloneDbc(postData)
            }
            showToast(this.$t('操作成功'), 'success')
            this.dialogVisible = false
            this.refresh()
          } catch (error) {
            console.error(error)
          }
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getDbc(row.id).then(res => {
        this.formData = res.data
        if (this.formData?.dbcFiles?.length) {
          this.formData?.dbcFiles.map(async val => {
            val.fileList = await this.downloadFile(val.id)
          })
        }
        this.dialogVisible = true
      })
    },

    downloadFile(fileId) {
      return new Promise(resolve => {
        if (fileId) {
          let postData = {
            sourceId: fileId,
            sourceType: 'dbc_file'
          }
          getFileList(postData).then(res => {
            res.data.forEach(val => {
              val.name = val.fileName + (val.fileType ? '.' + val.fileType : '')
              val.url = this.downloadUrl + val.id + '?token=' + util.getToken()
            })
            resolve(res.data)
          })
        } else {
          resolve([])
        }
      })
    },
    view(row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getDbc(row.id).then(res => {
        this.formData = res.data
        if (this.formData?.dbcFiles?.length) {
          this.formData?.dbcFiles.map(async val => {
            val.fileList = await this.downloadFile(val.id)
          })
        }
        this.dialogVisible = true
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDbc(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    async dialogOpened() {
      // 确保车型列表已加载
      if (this.vehicleVariantList.length === 0) {
        await this.initVariantDbcFilesCache()
      }
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      defaultFormData.dbcFiles = []
      this.formData = Object.assign({}, defaultFormData)
    },
    disabledEndDate(val) {
      if (this.formData.startTime) {
        return (
          new Date(val) <
          new Date(dateUtils.parseTime(this.formData.startTime, '{y}-{m}-{d}')).getTime() - 1000 * 60 * 60 * 24
        )
      }
    },
    disabledStartDate(val) {
      if (this.formData.endTime) {
        return new Date(val) > new Date(dateUtils.parseTime(this.formData.endTime, '{y}-{m}-{d}')).getTime()
      }
    },
    addFiles() {
      // const index = this.fileTypeList.findIndex(
      //     val => !~this.form.dbcFiles.findIndex(row => row.sourceType === val.code) && !val.disabled
      // )
      // if (~index) {
      // this.formData.dbcFiles.push({})
      this.formData.dbcFiles.push({ uuid: getUuid() })
      // } else {
      //   showToast(this.$t('每种文件类型仅可上传一次'), 'warning')
      // }
    },
    deleteSysFile(row, index) {
      this.formData.dbcFiles.splice(index, 1)
    },
    async changeVariant(val) {
      const vehicleVariant = this.vehicleVariantList.find(item => item.id === val)
      if (vehicleVariant) {
        this.formData.variantName = vehicleVariant.code
        if (val) {
          await this.getDbcFilesFromCache(val)
        } else {
          this.formData.dbcFiles = []
        }
      }
    },
    // 从缓存中获取车型的dbc文件
    async getDbcFilesFromCache(variantId) {
      if (variantId && this.variantDbcFilesCache[variantId]) {
        const cachedFiles = this.variantDbcFilesCache[variantId]
        
        if (cachedFiles.length > 0) {
          // 将缓存的数据转换为dbcFiles格式，并按照获取详情时的方式处理附件
          this.formData.dbcFiles = cachedFiles
          // 按照获取详情时的方式处理附件
          if (this.formData?.dbcFiles?.length) {
            await Promise.all(this.formData.dbcFiles.map(async val => {
              val.fileList = await this.downloadFile(val.id)
            }))
          }
        } else {
          this.formData.dbcFiles = []
        }
      } else {
        this.formData.dbcFiles = []
      }
    },
    viewDbcFile(row) {
      this.$refs.FtmDbcDetail.show(row)
    },
    async handleUploadForm(param, row) {
      let formData = new FormData()
      formData.append('file', param.file) // 额外参数
      formData.append('sourceType', 'dbc_file') // 额外参数
      let res
      // 类型只有标定参数zip包需要走特定接口
      res = await uploadFile(formData)

      showToast('上传成功')
      if (typeof res.data === 'object' && res.data !== null) {
        res.data.name = res.data.fileName
        res.data.url = this.downloadUrl + res.data.id + '?token=' + util.getToken()
        res.data.uid = param.file.uid
        row.fileList = [res.data]
        // 设置fileId字段
        row.fileId = res.data.id
      }
    },
    isImage(name) {
      let type = this.getType(name)
      if (!type) {
        return false
      }
      return (
        type.toLowerCase() === 'jpeg' ||
        type.toLowerCase() === 'jpg' ||
        type.toLowerCase() === 'png' ||
        type.toLowerCase() === 'gif'
      )
    },
    getType(url) {
      if (url) {
        let urlArr = url.split('.')
        return urlArr[urlArr.length - 1]
      }
    },
    handlePictureCardPreview(file) {
      if (this.isImage(file.fileType || file.name)) {
        if (file.url) {
          this.srcList = [file.url]
          this.dialogVisible = true
        }
      } else if (file.fileType === 'pdf') {
        location.href = file.url
      } else {
        if (file.url) {
          window.open(file.url)
        } else {
          let fileItem = this.fileList.find(val => val.uid === file.uid)
          window.open(fileItem.url)
        }
      }
    },
    closeViewer() {
      this.dialogImageVisible = false
    },
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}

.tag-group {
  .el-tag {
    margin-right: 5px;
  }
}

.ltw-tool-container {
  margin-left: 10px;
}

.hide-upload-btn {
  :deep(.el-upload) {
    display: none;
  }
}

.required-field {
  :deep(.el-input__wrapper) {
    border-color: #f56c6c;
  }
}

.required-field:deep(.el-input__wrapper):hover {
  border-color: #f56c6c;
}
</style>
