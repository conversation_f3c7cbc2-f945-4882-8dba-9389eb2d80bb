<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="500px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="80px">
      <el-form-item :label="$t('姓名')" prop="userName">
        <ltw-input v-model="form.userName"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('账号')" prop="loginName">
        <ltw-input
          v-model="form.loginName"
          placeholder="请使用您的NT账号"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('邮箱')" prop="email">
        <ltw-input text-type="description" v-model="form.email"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('密码')" prop="password">
        <ltw-input type="password" v-model="form.password"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('确认密码')" prop="confirmPwd">
        <ltw-input type="password" v-model="form.confirmPwd"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('组织架构')" prop="orgId">
        <org-selection v-model="form.orgId"></org-selection>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('保存') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import OrgSelection from '@/components/system/OrgSelection'
import { registerUser } from '@/apis/base/index'
import util from '@/plugins/util'
const defaultform = {}
export default {
  name: 'Register',
  emits: ['reload'],
  data() {
    const validateconfirmPwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else {
        if (value.length < 8) {
          callback(new Error('新密码至少8位以上'))
        } else {
          callback()
        }
      }
    }
    const validatepassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认密码'))
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        userName: [
          {
            required: true,
            message: this.$t('请输入名称'),
            trigger: 'blur'
          }
        ],
        loginName: [
          {
            required: true,
            message: this.$t('请输入账号'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            type: 'email',
            required: true,
            message: this.$t('请输入正确的邮箱'),
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            validator: validateconfirmPwd,
            trigger: 'blur'
          },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
            message:
              '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为 8 - 30位',
            trigger: 'change'
          }
        ],
        confirmPwd: [
          {
            required: true,
            validator: validatepassword,
            trigger: 'change'
          }
        ],
        orgId: [
          {
            required: true,
            message: this.$t('请选择组织架构'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  components: {
    OrgSelection
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('用户注册')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        let data = util.encrypt(JSON.stringify(postData))
        registerUser(data).then(() => {
          showToast(
            '注册成功，管理员审核通过后即可正常登陆使用系统，请关注您的注册邮箱!'
          )
          this.cancel()
        })
      })
    },
    getVerificationCode() {}
  }
}
</script>

<style scoped lang="scss"></style>
