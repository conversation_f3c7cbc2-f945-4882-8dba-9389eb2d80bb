<template>
  <el-drawer
    :title="dialogTitle"
    v-model="dialogVisible"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="filter-task"
  >
    <el-scrollbar>
      <el-form class="form-filter" label-position="top" :model="form" :rules="formRules" ref="formRef">
<!--        <el-form-item :label="$t('区域')" id="cantonCode" prop="cantonCode">-->
<!--          <el-cascader-->
<!--            filterable-->
<!--            clearable-->
<!--            popper-class="canton-list"-->
<!--            v-model="form.cantonCode"-->
<!--            :options="cantonCodeList"-->
<!--            :props="props"-->
<!--            :disabled="formReadonly"-->
<!--          />-->
<!--        </el-form-item>-->
        <el-form-item :label="$t('类别')" prop="typeList">
          <el-checkbox-group v-model="form.typeList">
            <el-checkbox-button v-for="item in groundLevelList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('规模')" prop="parkingLotScaleCodeList">
          <el-checkbox-group v-model="form.parkingLotScaleCodeList">
            <el-checkbox-button v-for="item in parkingLotScaleList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
<!--        <el-form-item :label="$t('地址围栏')" prop="parkingEnvCodeList">-->
<!--          <el-checkbox-group v-model="form.parkingEnvCodeList">-->
<!--            <el-checkbox-button v-for="item in parkingEnvList" :key="item.id" :label="item.code">-->
<!--              {{ item.name }}-->
<!--            </el-checkbox-button>-->
<!--          </el-checkbox-group>-->
<!--        </el-form-item>-->
        <el-form-item :label="$t('亮度')" prop="brightnessCodeList">
          <el-checkbox-group v-model="form.brightnessCodeList">
            <el-checkbox-button v-for="item in parkingLotBrightnessList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('柱子类型')" prop="columnTypeCodeList">
          <el-switch
            class="label-item"
            v-model="form.columnTypeFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.columnTypeCodeList">
            <el-checkbox-button v-for="item in columnTypeList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('地面材质')" prop="roadSurfaceMaterialCodeList">
          <el-switch
            class="label-item"
            v-model="form.roadSurfaceMaterialFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.roadSurfaceMaterialCodeList">
            <el-checkbox-button v-for="item in parkingLotMaterialList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('动态场景')" prop="dynamicSceneCodeList">
          <el-switch
            class="label-item"
            v-model="form.dynamicSceneFullMatch"
            :active-value="true"
            :inactive-value="false"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.dynamicSceneCodeList">
            <el-checkbox-button v-for="item in dynamicSceneList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('静态场景')" prop="staticSceneCodeList">
          <el-switch
            class="label-item"
            v-model="form.staticSceneFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.staticSceneCodeList">
            <el-checkbox-button v-for="item in staticSceneList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('配置')" prop="parkingSpaceConfigurationCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingSpaceConfigurationFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingSpaceConfigurationCodeList">
            <el-checkbox-button v-for="item in parkingLotConfigList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('阻轮类型')" prop="chockTypeCodeList">
          <el-switch
            class="label-item"
            v-model="form.chockTypeFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.chockTypeCodeList">
            <el-checkbox-button v-for="item in chockTypeList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('车位线型')" prop="parkingLineTypeCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingLineTypeFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingLineTypeCodeList">
            <el-checkbox-button v-for="item in parkingLineTypeList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('车位线颜色')" prop="parkingLineColorCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingLineColorFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingLineColorCodeList">
            <el-checkbox-button v-for="item in parkingLotInnerLineColorList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('车位内部颜色')" prop="parkingInnerColorCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingInnerColorFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingInnerColorCodeList">
            <el-checkbox-button v-for="item in parkingInnerColorList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('车位线状态')" prop="parkingLineStatusCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingLineStatusFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingLineStatusCodeList">
            <el-checkbox-button v-for="item in parkingLotLineStatusList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('库位类型')" prop="parkingSpaceTypeCodeList">
          <el-switch
            class="label-item"
            v-model="form.parkingSpaceTypeFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.parkingSpaceTypeCodeList">
            <el-checkbox-button v-for="item in parkingSpaceTypeList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('特殊车位')" prop="specialSpaceTypeCodeList">
          <el-switch
            class="label-item"
            v-model="form.specialSpaceTypeFullMatch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
            active-text="全匹配"
            inactive-text="非全匹配"
            inline-prompt
          />
          <el-checkbox-group v-model="form.specialSpaceTypeCodeList">
            <el-checkbox-button v-for="item in specialParkingList" :key="item.id" :label="item.code">
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('确认') }}</el-button>
        </template>
      </span>
    </template>
  </el-drawer>
</template>

<script>
import DictionarySelection from '@/components/system/DictionarySelection.vue'
// import { getSysCantonTree } from '@/apis/system/sys-canton'
import { getFtmParkingLotFieldItems } from '@/apis/fleet/ftm-parking-lot'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

const defaultform = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({
        columnTypeFullMatch: false,
        roadSurfaceMaterialFullMatch: false,
        dynamicSceneFullMatch: false,
        staticSceneFullMatch: false,
        parkingSpaceConfigurationFullMatch: false,
        chockTypeFullMatch: false,
        parkingLineTypeFullMatch: false,
        parkingLineColorFullMatch: false,
        parkingInnerColorFullMatch: false,
        parkingLineStatusFullMatch: false,
        parkingSpaceTypeFullMatch: false,
        specialSpaceTypeFullMatch: false,
        typeList: [],
        parkingLotScaleCodeList: [],
        parkingEnvCodeList: [],
        brightnessCodeList: [],
        columnTypeCodeList: [],
        roadSurfaceMaterialCodeList: [],
        staticSceneCodeList: [],
        dynamicSceneCodeList: [],
        parkingSpaceConfigurationCodeList: [],
        chockTypeCodeList: [],
        parkingLineTypeCodeList: [],
        parkingLineColorCodeList: [],
        parkingInnerColorCodeList: [],
        parkingLineStatusCodeList: [],
        parkingSpaceTypeCodeList: [],
        specialSpaceTypeCodeList: []

      }, defaultform),
      formRules: {},
      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click',
        checkStrictly: true
      },
      cantonCodeList: [],
      groundLevelList: [],
      parkingLotScaleList: [],
      // parkingEnvList: [],
      parkingLotBrightnessList: [],
      columnTypeList: [],
      parkingLotMaterialList: [],
      staticSceneList: [],
      dynamicSceneList: [],
      parkingLotConfigList: [],
      chockTypeList: [],
      parkingLineTypeList: [],
      parkingLotInnerLineColorList: [],
      parkingInnerColorList: [],
      parkingLotLineStatusList: [],
      parkingSpaceTypeList: [],
      specialParkingList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    DictionarySelection
  },

  methods: {
    async show() {
      // // 类型
      // this.groundLevelList = await this.getFtmParkingLotFieldItems('ground_level')
      // // console.log(this.groundLevelList)
      // console.log('规模')
      // // 规模
      // this.parkingLotScaleList = await this.listSysDictionary('parking_lot_scale')
      // console.log('地址围栏')
      // // 地址围栏
      // this.parkingEnvList = await this.getFtmParkingLotFieldItems('parking_env')
      // // 亮度
      // this.parkingLotBrightnessList = await this.getFtmParkingLotFieldItems('parking_lot_brightness')
      // // 柱子类型
      // this.columnTypeList = await this.getFtmParkingLotFieldItems('column_type')
      // // 地面材质
      // this.parkingLotMaterialList = await this.getFtmParkingLotFieldItems('parking_lot_material')
      // // 动态场景
      // this.dynamicSceneList = await this.getFtmParkingLotFieldItems('dynamic_scene')
      // // 静态场景
      // this.staticSceneList = await this.getFtmParkingLotFieldItems('static_scene')
      // // 配置
      // this.parkingLotConfigList = await this.getFtmParkingLotFieldItems('parking_lot_config')
      // // 阻轮类型
      // this.chockTypeList = await this.getFtmParkingLotFieldItems('chock_type')
      // // 车位线型
      // this.parkingLineTypeList = await this.getFtmParkingLotFieldItems('parking_line_type')
      // // 车位线颜色
      // this.parkingLotInnerLineColorList = await this.getFtmParkingLotFieldItems('parking_lot_inner_line_color')
      // // 车位内部颜色
      // this.parkingInnerColorList = await this.getFtmParkingLotFieldItems('parking_inner_color')
      // // 车位线状态
      // this.parkingLotLineStatusList = await this.getFtmParkingLotFieldItems('parking_lot_line_status')
      // // 库位类型
      // this.parkingSpaceTypeList = await this.getFtmParkingLotFieldItems('parking_space_type')
      // // 特殊车位
      // this.specialParkingList = await this.getFtmParkingLotFieldItems('special_parking')
      //

      this.dialogVisible = true
      this.dialogTitle = this.$t('筛选')
      // if (!this.cantonCodeList?.length) {
      //   this.getSysCantonTree()
      // }
      // 类型
      if (!this.groundLevelList?.length) {
        this.getFtmParkingLotFieldItems('ground_level').then(res => {
          this.groundLevelList = res
        })
      }
      // 规模
      if (!this.specialParkingList?.length) {
        this.listSysDictionary('parking_lot_scale').then(res => {
          this.parkingLotScaleList = res
        })
      }
      // // 地址围栏
      // if (!this.specialParkingList?.length) {
      //   this.getFtmParkingLotFieldItems('parking_env').then(res => {
      //     this.parkingEnvList = res
      //   })
      // }
      // 亮度
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_lot_brightness').then(res => {
          this.parkingLotBrightnessList = res
        })
      }
      // 柱子类型
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('column_type').then(res => {
          this.columnTypeList = res
        })
      }
      // 地面材质
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_lot_material').then(res => {
          this.parkingLotMaterialList = res
        })
      }
      // 动态场景
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('dynamic_scene').then(res => {
          this.dynamicSceneList = res
        })
      }
      // 静态场景
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('static_scene').then(res => {
          this.staticSceneList = res
        })
      }
      // 配置
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_lot_config').then(res => {
          this.parkingLotConfigList = res
        })
      }
      // 阻轮类型
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('chock_type').then(res => {
          this.chockTypeList = res
        })
      }
      // 车位线型
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_line_type').then(res => {
          this.parkingLineTypeList = res
        })
      }
      // 车位线颜色
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_lot_inner_line_color').then(res => {
          this.parkingLotInnerLineColorList = res
        })
      }
      // 车位内部颜色
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_inner_color').then(res => {
          this.parkingInnerColorList = res
        })
      }
      // 车位线状态
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_lot_line_status').then(res => {
          this.parkingLotLineStatusList = res
        })
      }
      // 库位类型
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('parking_space_type').then(res => {
          this.parkingSpaceTypeList = res
        })
      }
      // 特殊车位
      if (!this.specialParkingList?.length) {
        this.getFtmParkingLotFieldItems('special_parking').then(res => {
          this.specialParkingList = res
        })
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    getSysCantonTree() {
      getSysCantonTree().then(res => {
        this.cantonCodeList = res.data
      })
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        if (Array.prototype.isPrototypeOf(this.form.cantonCode)) {
          postData.cantonCode = this.form.cantonCode[this.form.cantonCode?.length - 1]
        } else {
          postData.cantonCode = this.form.cantonCode
        }
        this.cancel(postData)
      })
    },
    getFtmParkingLotFieldItems(code) {
      return new Promise(resolve => {
        let postData = {
          fieldCode: code
        }
        getFtmParkingLotFieldItems(postData).then(res => {
          resolve(res.data)
        })
      })
    },
    listSysDictionary(code) {
      return new Promise(resolve => {
        let postData = {
          typeCode: code
        }
        listSysDictionary(postData).then(res => {
          resolve(res.data)
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.form-filter {
  .label-item {
    position: absolute;
    top: -34px;
    left: 80px;
  }
  :deep(.el-form-item__label){
    font-weight: 600;
  }
}
</style>
