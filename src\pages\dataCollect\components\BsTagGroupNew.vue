<template>
    <el-card shadow="always" :class="cardClasses">
      <template #header>
        <div class="bs-tag-group-card-header">
          <span
            >{{ tagGroup[getLocale === 'en' ? 'name' : 'nameCn'] }} {{ tagGroup.code ? '- ' + tagGroup.code : '' }}</span
          >
          <el-dropdown
            v-if="(!sub && outlineFunctionList?.length) || inlineFunctionList?.length"
            @command="handleTagGroupCommand($event)"
          >
            <span class="el-dropdown-link" id="operation-button">
              {{ $t('操作') }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu class="bs-tag-group-dropdown-menu">
                <el-dropdown-item
                  command="addSubGroup"
                  v-if="!isLeafNode && checkBtnVisible('add', outlineFunctionList)"
                  id="el-icon-plus"
                >
                  <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                  {{ $t('新增') }}
                </el-dropdown-item>
                <el-dropdown-item command="edit" id="el-icon-edit" v-if="checkBtnVisible('edit', inlineFunctionList)">
                  <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                  {{ $t('编辑') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="singleRemove"
                  id="el-icon-delete"
                  v-if="checkBtnVisible('singleRemove', inlineFunctionList)"
                >
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                  {{ $t('删除') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
      <div class="bs-tag-group-card-body">
        <draggable
          v-if="tagGroup.children && tagGroup.children.length > 0"
          :list="tagGroup.children"
          tag="div"
          :component-data="{ name: 'fade', class: 'bs-tag-sub-group-container' }"
          item-key="id"
          @change="handleSubGroupChange"
          ghost-class="ghost"
          group="tag-group"
          filter=".el-card__body,.el-dropdown,.disable-dragging"
        >
          <template #item="{ element }">
            <bs-tag-group-new
              :inlineFunctionList="inlineFunctionList"
              :outlineFunctionList="outlineFunctionList"
              :tag-group="element"
              :level="level + 1"
              @command="handleTagGroupCommand"
              @update-children="handleUpdateChildren($event, element)"
              @update-tag-list="handleUpdateTagList($event, element)"
              class="bs-tag-sub-group-card isLeaf"
              sub
            ></bs-tag-group-new>
          </template>
        </draggable>
        
        <div class="bs-tag-tontainer" v-if="isLeafNode">
          <draggable
            :list="tagGroup.tagList || []"
            :component-data="{ name: 'fade' }"
            ghost-class="ghost-tag"
            item-key="id"
            @change="handleTagChange"
            sub
          >
            <template #item="{ element }">
              <el-popover
                :show-arrow="false"
                :popper-class="checkTagType(element)"
                v-if="element.sourceAccuracy"
                placement="bottom"
                trigger="hover"
                :width="pwidth"
                @show="adjustPopoverWidth(element.sourceAccuracy)"
              >
                <el-row
                  v-for="item in sourceList(element.sourceAccuracy)"
                  style="font-size: 12px; display: flex; justify-content: center; white-space: nowrap"
                >
                  <div :style="{ 'text-align': 'right', width: maxWidth }">
                    <span>{{ item.split(':')[0] }}</span>
                    <span v-if="item.split(':')[1]">&nbsp:&nbsp</span>
                  </div>
                  <div style="width: 40px">
                    <span>{{ item.split(':')[1] ? Number(item.split(':')[1]).toFixed(2) : '' }}</span>
                    <span v-if="item.split(':')[1]">%</span>
                  </div>
                </el-row>
                <template #reference>
                  <el-button
                    :type="checkTagType(element)"
                    @click="handleTagCick(element, tagGroup.tagList)"
                    size="small"
                    plain
                    :class="{
                      'tag-active': element.active,
                      'tag-inactive': !element.active,
                      'tag-mapping':element.mappingNewTag
                    }"
                  >
                    <el-row
                      >{{ element[getLocale === 'en' ? 'name' : 'nameCn'] }}
                      <ltw-icon icon-code="el-icon-microphone" v-if="element.supportVoice"></ltw-icon>
                    </el-row>
                  </el-button>
                </template>
              </el-popover>
              <el-button
                v-else
                :type="checkTagType(element)"
                @click="handleTagCick(element, tagGroup.tagList)"
                size="small"
                plain
                :class="{
                  'tag-active': element.active,
                  'tag-inactive': !element.active,
                  'tag-mapping':element.mappingNewTag
                }"
              >
                <el-row
                  >{{ element[getLocale === 'en' ? 'name' : 'nameCn'] }}
                  <ltw-icon icon-code="el-icon-microphone" v-if="element.supportVoice"></ltw-icon>
                </el-row>
              </el-button>
            </template>
          </draggable>
          <el-button
            v-if="checkBtnVisible('add', outlineFunctionList)"
            size="small"
            @click="handleTagGroupCommand('addTag', item)"
            id="tag-add"
          >
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            {{ '新增' }}
          </el-button>
        </div>
      </div>
    </el-card>
  </template>
  
  <script>
import BsTagGroupNew from '@/pages/dataCollect/components/BsTagGroupNew.vue'
import draggable from 'vuedraggable'
import {treeListBsTagGroup, reorderBsTagGroup} from '@/apis/data-collect/bs-tag-group'
import {listBsTag, reorderBsTag} from '@/apis/data-collect/bs-tag'
import {BS_TAG_TYPE} from '@/plugins/constants/data-dictionary'
import {getLocale} from '@/plugins/util'
import LtwIcon from '@/components/base/LtwIcon.vue'
  export default {
    components: { LtwIcon, BsTagGroupNew, draggable },
    name: 'BsTagGroup',
    props: {
      tagGroup: {
        type: Object,
        default: () => {
          return {}
        }
      },
      sub: {
        type: Boolean,
        default: false
      },
      level: {
        type: Number,
        default: 1
      },
      inlineFunctionList: {
        type: Array,
        default: () => {
          return []
        }
      },
      outlineFunctionList: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    emits: ['command', 'updateChildren', 'updateTagList'],
    data() {
      return {
        getLocale: getLocale(),
        pwidth: 120,
        maxWidth: 100
      }
    },
    computed: {
      // 判断当前节点是否是最末级（叶子节点）
      isLeafNode() {
        // 如果当前层级为3，则认为是叶子节点
        if (this.level >= 3) {
          return true
        }
        // 如果有标签列表且不为空，则认为是叶子节点
        return this.tagGroup.tagList && this.tagGroup.tagList.length > 0
      },
      // 动态生成class类名
      cardClasses() {
        return {
          'bs-tag-group-card': true,
          'is-leaf-node': this.isLeafNode,
          'has-children': !this.isLeafNode,
          'is-sub-group': this.sub,
          [`level-${this.level}`]: true,
          'max-level': this.level >= 3
        }
      }
    },

    created() {},
    methods: {
      executeButtonMethod(funcName, row) {
        this[funcName](row)
      },
      handleTagGroupCommand(command, row) {
        if (!row) {
          row = this.tagGroup
        }
        this.$emit('command', command, row)
      },
      checkTagType(tag) {
        if (!tag.enabled) {
          return 'info'
        }
        if (tag.type === 'continuous') {
          return tag.mutuallyExclusive ? 'danger' : 'warning'
        }
        if (tag.type === 'transient') {
          return tag.mutuallyExclusive ? 'success' : 'primary'
        }
        return 'info'
      },
      handleSubGroupChange(e) {
        if (e.moved) {
          this.handleGroupMove(e.moved)
        }
        if (e.added) {
          this.handleGroupAdd(e.added)
        }
      },
      handleGroupMove(moved) {
        let element = moved.element
        let preSortNum
        let nextSortNum
        let newIndex = moved.newIndex
        if (newIndex === 0) {
          preSortNum = undefined
        } else {
          preSortNum = this.tagGroup.children[newIndex - 1].sortNum
        }
        if (newIndex === this.tagGroup.children.length - 1) {
          nextSortNum = undefined
        } else {
          nextSortNum = this.tagGroup.children[newIndex + 1].sortNum
        }
        this.sortSubBsTagGroup(element, preSortNum, nextSortNum)
      },
      handleGroupAdd(added) {
        let element = added.element
        let preSortNum
        let nextSortNum
        let newIndex = added.newIndex
        let newParentId = ''
        if (newIndex === 0) {
          preSortNum = undefined
        } else {
          preSortNum = this.tagGroup.children[newIndex - 1].sortNum
          newParentId = this.tagGroup.children[newIndex - 1].parentId
        }
        if (newIndex === this.tagGroup.children.length - 1) {
          nextSortNum = undefined
        } else {
          newParentId = this.tagGroup.children[newIndex + 1].parentId
          nextSortNum = this.tagGroup.children[newIndex + 1].sortNum
        }
        this.sortSubBsTagGroup(element, preSortNum, nextSortNum, newParentId)
      },
      sortSubBsTagGroup(element, preSortNum, nextSortNum, newParentId) {
        reorderBsTagGroup({
          id: element.id,
          preSortNum,
          nextSortNum,
          newParentId
        }).then(res => {
          let newSortNum = res.data
          if (!nextSortNum) {
            element.sortNum = newSortNum
          } else {
            if (!preSortNum) {
              preSortNum = 0
            }
            if (preSortNum < newSortNum && newSortNum < nextSortNum) {
              element.sortNum = newSortNum
            } else {
              this.querySubGroup(element.parentId)
            }
          }
        })
      },
      querySubGroup(parentId) {
        treeListBsTagGroup({ parentId }).then(res => {
          this.$emit('updateChildren', res.data)
        })
      },
      handleUpdateChildren(children, element) {
        element.children = children
      },
      handleTagChange({ moved }) {
        let element = moved.element
        let preSortNum
        let nextSortNum
        let newIndex = moved.newIndex
        if (newIndex === 0) {
          preSortNum = undefined
        } else {
          preSortNum = this.tagGroup.tagList[newIndex - 1].sortNum
        }
        if (newIndex === this.tagGroup.tagList.length - 1) {
          nextSortNum = undefined
        } else {
          nextSortNum = this.tagGroup.tagList[newIndex + 1].sortNum
        }
        this.sortBsTag(element, preSortNum, nextSortNum)
      },
      sortBsTag(element, preSortNum, nextSortNum) {
        reorderBsTag({ id: element.id, preSortNum, nextSortNum }).then(res => {
          let newSortNum = res.data
          if (!nextSortNum) {
            element.sortNum = newSortNum
          } else {
            if (!preSortNum) {
              preSortNum = 0
            }
            if (preSortNum < newSortNum && newSortNum < nextSortNum) {
              element.sortNum = newSortNum
            } else {
              this.queryTag(element.groupId)
            }
          }
        })
      },
      queryTag(groupId) {
        listBsTag({ groupId }).then(res => {
          this.$emit('updateTagList', res.data)
        })
      },
      handleUpdateTagList(tagList, element) {
        element.tagList = tagList
      },
      handleTagCick(tag, tagList) {
        if (this.simulation) {
          if (tag.type === BS_TAG_TYPE.TRANSIENT) {
            tag.active = true
            setTimeout(() => {
              tag.active = false
            }, 300)
          }
          if (tag.type === BS_TAG_TYPE.CONTINUOUS) {
            tag.active = !tag.active
            if (tag.active && tag.mutuallyExclusive) {
              if (tagList && tagList.length > 0) {
                tagList.forEach(element => {
                  if (tag.id !== element.id && element.mutuallyExclusive) {
                    element.active = false
                    return
                  }
                })
              }
            }
          }
        } else {
          if (this.checkBtnVisible('view', this.inlineFunctionList)) {
            this.handleTagGroupCommand('viewTag', tag)
          }
        }
      },
      sourceList(element) {
        let arr = element.split(',')
        return arr
      },
      adjustPopoverWidth(element) {
        const rows = element.split(',')
        let maxCharCount = 0
        rows.forEach(row => {
          const rowText = row.split(':')[0] // 获取每一行的文本内容
          const charCount = this.getCharacterCount(rowText) // 获取文本的字符个数
  
          if (charCount > maxCharCount) {
            maxCharCount = charCount // 更新最大字符个数
          }
        })
        const charWidth = 12
        this.maxWidth = maxCharCount * charWidth + 20 + 'px'
        this.pwidth = maxCharCount * charWidth + 100 > 150 ? maxCharCount * charWidth + 100 : 150
      },
      getCharacterCount(text) {
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        context.font = '12px Arial' // 设置字体样式和大小，根据实际情况调整
        const metrics = context.measureText(text)
        return metrics.width / 12 // 返回字符宽度除以字体大小，根据实际情况调整
      },
      checkBtnVisible(code, btnGroup) {
        return btnGroup.find(val => val.buttonCode === code)
      }
    }
  }
  </script>
  <style lang="scss">
  .info.el-popover {
    background: #f4f4f5 !important;
  }
  
  .danger.el-popover {
    background: #fef0f0 !important;
  }
  
  .success.el-popover {
    background-color: #f0f9eb !important;
  }
  
  .primary.el-popover {
    background-color: #ecf5ff !important;
  }
  
  .warning.el-popover {
    background-color: #fdf6ec;
  }
  </style>
  <style scoped lang="scss">
  .bs-tag-group-card {
    margin-top: 10px;
  
    .bs-tag-group-card-header {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
  
      .el-dropdown {
        width: 80px;
        font-size: 12px;
  
        .el-dropdown-link {
          cursor: pointer;
          color: var(--el-color-primary);
  
          .el-icon--right {
            font-size: 12px;
            vertical-align: middle;
          }
        }
      }
    }
  
    .bs-tag-group-card-body {
      min-height: 50px;
  
      .bs-tag-sub-group-container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
  
        .bs-tag-group-card {
          margin-right: 10px;
          min-width: 300px;
        }

  
        :nth-child(3n + 1).bs-tag-group-card {
         
        }
  
        :nth-child(3n).bs-tag-group-card {
         margin-left:22px;
        }
  
        .ghost {
          background-color: rgba(241, 239, 239, 0.8);
        }
  
        .fade-move {
          transition: transform 0.5s;
        }
      }
  
      .bs-tag-tontainer {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        // .el-tag {
        //     margin-right: 10px;
        //     margin-bottom: 5px;
        //     cursor: pointer;
        // }
  
        .el-button {
          margin-bottom: 5px;
          margin-right: 5px;
          margin-left: 0px;
        }
  
        .el-button.tag-active {
          background: var(--el-button-bg-color);
          border-color: var(--el-button-border-color);
          color: var(--el-button-text-color);
        }
  
        .el-button.el-button--info.tag-inactive {
          color: var(--el-button-text-color);
          background-color: #f4f4f5;
          border-color: #c8c9cc;
        }
  
        .el-button.el-button--danger.tag-inactive {
          color: var(--el-button-text-color);
          background-color: #fef0f0;
          border-color: #fbc4c4;
        }
  
        .el-button.el-button--primary.tag-inactive {
          color: var(--el-button-text-color);
          background-color: #ecf5ff;
          border-color: #b3d8ff;
        }
  
        .el-button.el-button--warning.tag-inactive {
          color: var(--el-button-text-color);
          background-color: #fdf6ec;
          border-color: #f5dab1;
        }
  
        .el-button.el-button--success.tag-inactive {
          color: var(--el-button-text-color);
          background-color: #f0f9eb;
          border-color: #c2e7b0;
        }
        .tag-mapping{
          border: 2px dashed #73767A !important;
        }

        .ghost {
          background-color: rgba(241, 239, 239, 0.8);
        }

        .fade-move {
          transition: transform 0.5s;
        }
      }
    }
  }

  // 子分组样式
  .bs-tag-group-card.is-sub-group {
    margin-left: 20px;
    border-left-width: 2px;
    &.is-leaf-node {
      width: 30%;
    }
  }

  // 层级样式
  .bs-tag-group-card.level-1 {
    // 第一级样式
    width: 100%;
    border-left-width: 4px;

  }

  .bs-tag-group-card.level-2 {
    // 第二级样式 - 默认宽度100%
    width: 100%;
    margin-left: 15px;
    border-left-width: 3px;
  }

  .bs-tag-group-card.level-3,
  .bs-tag-group-card.max-level {
    // 第三级及以上样式（强制为最末级）
    width: 100%;
    margin-left: 30px;
    border-left-width: 2px;
  

    .bs-tag-group-card-header {
  
    }

    // 三级及以上不允许有子分组，只能有标签
    .bs-tag-sub-group-container {
      display: none; // 隐藏子分组容器
    }
  }

  </style>
  