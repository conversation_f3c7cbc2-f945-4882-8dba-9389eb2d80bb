<template>
  <el-card class="vehicle-card">
    <div class="card-content">
      <div class="tag-img">
        <el-image
          :src="row.filePath ? downloadUrl + token + '&path=' + row.filePath : ''"
          :preview-src-list="[row.filePath ? downloadUrl + token + '&path=' + row.filePath : '']"
          fit="cover"
        >
          <template #error>
            <div class="image-slot">
              <ltw-icon icon-code="el-icon-picture"></ltw-icon>
            </div>
          </template>
        </el-image>
      </div>
      <div class="tag-content">
        <div class="tag-name">
          {{ row.name }}
        </div>
        <div class="tag-type">
          <span class="tag-btn">
            <ltw-icon v-if="row.sourceType === 'DATA_PLATFORM'" icon-code="el-icon-monitor" />
            <ltw-icon v-if="row.sourceType === 'CHENGHUANG_APP'" icon-code="el-icon-iphone" />
            {{ row.sourceTypeName }}
          </span>
        </div>
        <div class="tag-description">
          <el-tooltip effect="dark" :content="row.description">
            {{ row.description }}
          </el-tooltip>
        </div>
      </div>

      <div class="tag-img-btn">
        <el-dropdown @command="handleCommand" class="batch-operate-btn">
          <el-button class="button" text>
            <ltw-icon icon-code="el-icon-more"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="commandObj('view', row)">
                <ltw-icon icon-code="el-icon-view"></ltw-icon>
                {{ $t('详情') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="row.sourceType === 'DATA_PLATFORM'" :command="commandObj('edit', row)">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
                {{ $t('编辑') }}
              </el-dropdown-item>
              <el-dropdown-item :command="commandObj('remove', row)">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                {{ $t('删除') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-card>
</template>

<script>
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  name: 'VehicleCard',
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/preview',
      token: '?token=' + util.getToken(),
      row: {}
    }
  },
  emits: ['reload'],
  props: {
    type: {
      type: String,
      required: false,
      default: ''
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    customFunctionList: {
      type: Array,
      default: []
    }
  },
  watch: {
    item: {
      handler(val) {
        this.row = val
      },
      deep: true,
      immediate: true
    }
  },
  components: {},
  mounted() {},
  created() {
    // this.listStatus()
  },
  methods: {
    view(id) {
      this.$emit('view', { id })
    },
    edit(id) {
      this.$emit('edit', { id })
    },
    singleRemove(id) {
      this.$emit('singleRemove', { id })
    },
    query() {
      this.$emit('reload')
    },
    checkInlineButton(type) {
      return ~this.inlineFunctionList.findIndex(val => val.buttonCode === type)
    },
    handleCommand(obj) {
      if (obj.type === 'remove') {
        this.singleRemove(obj.row.id)
      } else if (obj.type === 'view') {
        this.view(obj.row.id)
      } else if (obj.type === 'edit') {
        this.edit(obj.row.id)
      }
    },
    commandObj(type, row) {
      return {
        type,
        row
      }
    }
  }
}
</script>

<style scoped lang="scss">
.vehicle-card {
  position: relative;

  :deep(.el-card__header) {
    padding: 4px 10px;
  }

  :deep(.el-card__body) {
    padding: 0;
  }

  .el-tag {
    cursor: pointer;
  }

  .card-header {
    display: flex;
    justify-content: space-between;

    .tags {
      display: flex;

      .title {
        margin-right: 10px;
      }
    }

    .title {
      font-size: 12px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;

      &.external-vin {
        border: 1px solid rgb(16, 41, 106);
        background-color: #c6e2ff;
        color: #000;
      }
    }
  }

  .card-content {
    .tag-img {
      overflow: hidden;
      width: 100%;
      position: relative;

      img,
      .el-image {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 174px;
        //height: 100%;
        width: 100%;
        font-size: 30px;
        // background: var(--el-fill-color-light);
        color: var(--el-text-color-secondary);

        .image-slot {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: var(--el-fill-color-light);
          color: var(--el-text-color-secondary);
        }
      }
    }

    .tag-content {
      padding: 15px 24px;

      .tag-name {
        font-size: 16px;
        color: #2e3033;
        line-height: 21px;
      }

      .tag-type {
        margin: 10px 0;
        display: flex;

        .tag-btn {
          display: flex;
          align-items: center;
          padding: 0 6px;
          background: rgba(86, 176, 255, 0.15);
          border-radius: 17px 17px 17px 17px;
          font-size: 8px;
          color: #007bc0;
          line-height: 20px;
        }

        .ltw-icon {
          font-size: 12px;
          margin-right: 7px;
        }
      }

      .tag-description {
        margin-top: 6px;
        font-size: 12px;
        color: #8a9097;
        line-height: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        height: 40px;
      }
    }

    .tag-img-btn {
      position: absolute;
      right: 0;
      bottom: -6px;
      text-align: right;

      .el-link:not(:last-child) {
        margin-right: 10px;
      }
    }
  }
}
</style>
