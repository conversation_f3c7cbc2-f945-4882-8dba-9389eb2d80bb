import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysPrivilegeApplication = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications', data, params})
export const updateSysPrivilegeApplication = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications', data, params})
export const deleteSysPrivilegeApplication = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications', params})
export const listSysPrivilegeApplication = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications', params})
export const listSysPrivilegeApplicationSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/selections', params})
export const pageSysPrivilegeApplication = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/page', params})
export const getSysPrivilegeApplication = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/' + id})
export const listSysPrivilegeApplicationOfCurrentUser = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/current_user', params})
export const pageHasAssignedSysPrivilegeApplication = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/page/has_assigned', params})
export const pageHasNotAssignedSysPrivilegeApplicationVO = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_privilege_applications/page/has_not_assigned', params})
