<template>
  <div class="main">
    <div class="login">
      <img src="@/assets/images/mobile_green.jpg" alt="" class="bg-img" />
      <h3>JCDC</h3>
      <el-form
        ref="loginFormRef"
        :model="loginFormData"
        :rules="loginRules"
        class="login-form"
      >
        <h2 class="title">欢迎登录</h2>
        <el-form-item prop="username">
          <ltw-input
            placeholder="用户名"
            prefix-icon="el-icon-user-solid"
            v-model="loginFormData.loginName"
            type="text"
            @focus="isLoginName = true"
            @blur="isLoginName = false"
          >
          </ltw-input>
        </el-form-item>
        <el-form-item prop="password" @keyup.enter="handleLogin">
          <ltw-input
            placeholder="密码"
            prefix-icon="el-icon-lock"
            v-model="loginFormData.password"
            type="password"
            @focus="isPassword = true"
            @blur="isPassword = false"
          >
          </ltw-input>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width: 100%"
            @click="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import { login } from "@/apis/base/index";
import util from "@/plugins/util";
export default {
  name: "loginBackup",
  data() {
    return {
      isLoginName: false,
      isPassword: false,
      loginFormData: {},
      loginRules: {
        loginName: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
      },
      loading: false,
    };
  },
  methods: {
    handleLogin() {
      this.$refs.loginFormRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          let data = util.encrypt(JSON.stringify(this.loginFormData));
          login(data)
            .then((res) => {
              let token = res.data;
              util.setToken(token)
              this.$router.push("/");
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.main {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #0d1d30;
  position: relative;

  .login {
    position: relative;
    width: 940px;
    height: 495px;
    background: rgba(25, 41, 59, 1);
    border-radius: 12px;
    box-shadow: 0px 2px 12px 0px rgba(9, 23, 39, 1);
    color: rgba(255, 255, 255, 1);

    .bg-img {
      position: absolute;
      top: 63px;
      left: 132px;
      width: 348px;
      height: 299px;
    }

    h3 {
      margin-left: 52px;
      margin-top: 383px;
      margin-bottom: 0;
      font-size: 32px;
    }

    .login-form {
      position: absolute;
      right: 0;
      top: 0;
      border-radius: 6px;
      background: #ffffff;
      width: 299px;
      height: 495px;
      padding: 58px 33px 33px 33px;
    }

    .title {
      width: 85px;
      font-size: 20px;
      margin: 0px auto 62px auto;
      border-bottom: 3px rgba(27, 74, 127, 1) ridge;
      text-align: center;
      color: #333333;
      padding-bottom: 8px;
    }
  }
}
</style>
