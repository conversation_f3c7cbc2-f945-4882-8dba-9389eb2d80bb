<template>
  <el-container class="ltw-home-container" :class="loginTheme === 'dark' ? 'dark' : 'light'">
    <ltw-header
      @switchSkin="switchSkin"
      @reload="getHome"
      @handle-role-change="getModuleList"
      :showTime="false"
      :theme-type="'dark'"
    >
      <!-- <template #title>{{ $t('WAVE3') }}</template> -->
      <!-- <template #title>WAVE3</template> -->
      <template #left>
        <el-menu
          v-if="defaultActiveModule"
          :default-active="defaultActiveModule"
          class="el-menu-demo"
          mode="horizontal"
          @select="handleModuleSelect($event)"
        >
          <template v-for="item in moduleList" :key="item.id">
            <el-menu-item :id="item.code" :index="item.id">
              <template #title>
                <ltw-icon :icon-code="item.iconCode"></ltw-icon>
                <span>{{ item.name }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-menu>
      </template>
    </ltw-header>
    <el-container>
      <ltw-menu-aside ref="ltwMenuAsideRef"></ltw-menu-aside>
      <el-container style="width: calc(100% - 200px); border-left: solid 1px #dad7d7">
        <el-main id="ltwMainContainer">
          <el-scrollbar>
            <router-view></router-view>
          </el-scrollbar>
        </el-main>
        <el-footer>
          Copyright © BOSCH - XC-DX/PJ-W3-PMT <span v-text="year"></span>
          <el-link target="_blank" type="primary" :underline="false" href="https://beian.miit.gov.cn/"
            >苏ICP备2023025490号-1
          </el-link>
        </el-footer>
      </el-container>
    </el-container>
  </el-container>
</template>

<script>
import LtwHeader from '@/components/base/LtwHeader'
import LtwMenuAside from '@/components/base/LtwMenuAside'
import GLB_CONFIG from '@/plugins/glb-constant'
import { listSysPrivilegeModuleOfCurrentUser } from '@/apis/system/sys-privilege-module'
import LtwIcon from '@/components/base/LtwIcon'

export default {
  name: 'LtwHome',
  components: { LtwMenuAside, LtwHeader, LtwIcon },
  data() {
    return {
      applicationId: GLB_CONFIG.applicationId,
      logoLightSrc: require('@/assets/images/login/logo-light.png'),
      logoDarkSrc: require('@/assets/images/login/logo-dark.png'),
      moduleList: [],
      defaultActiveModule: '',
      loginTheme: '',
      year: ''
    }
  },
  created() {
    // this.loginTheme = localStorage.getItem('login-theme') || 'light'
    this.year = new Date().getFullYear()
    this.getModuleList()
  },
  methods: {
    getHome() {
      this.$refs.ltwMenuAsideRef.goHome()
    },
    getModuleList(item) {
      let param = {
        applicationId: this.applicationId
      }
      if (item !== 'all') {
        param.tempRoleIds = item
      }
      listSysPrivilegeModuleOfCurrentUser(param).then(res => {
        this.moduleList = res.data
        if (this.moduleList && this.moduleList.length > 0) {
          if (this.$router.currentRoute.value.meta.moduleId) {
            this.defaultActiveModule = this.$router.currentRoute.value.meta.moduleId
          } else {
            this.defaultActiveModule = this.moduleList[0].id
          }
          this.handleModuleSelect(this.defaultActiveModule, item)
        }
      })
    },
    findDefaultActiveMenu(list) {
      for (let i = 0; i < list.length; i++) {
        let menu = list[i]
        if (menu.asLeaf) {
          this.defaultActiveMenu = menu.pageUrl
          return
        } else {
          this.findDefaultActiveMenu(menu.children)
        }
      }
    },
    handleModuleSelect(index, item) {
      let param = {
        moduleId: index
      }
      if (item !== 'all') {
        param.tempRoleIds = item
      }
      this.$refs.ltwMenuAsideRef.load(param)
    },
    checkElmentIcon(val) {
      if (!val) {
        return false
      }
      return val.startWith('el-icon-')
    },
    substringElementIcon(val) {
      return val.replace('el-icon-', '')
    },
    switchSkin(loginTheme) {
      this.loginTheme = loginTheme
    },
    handleRoleChange(item) {
      this.getModuleList(item)
    }
  }
}
</script>

<style scoped lang="scss">
.ltw-home-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  & > .el-container {
    height: calc(100% - 60px);
    // height: 100%;
  }

  #ltwMainContainer {
    height: calc(100% - 36px);
    padding-bottom: 0;

    & > div,
      // > :deep(.el-scrollbar__wrap > .el-scrollbar__view)
    & > .el-scrollbar {
      height: 100%;
    }

    & > .el-scrollbar > :deep(.el-scrollbar__wrap) {
      position: relative;
    }

    & > div > &:deep(.el-card) {
      height: 100%;
      overflow: auto;
    }

    & > .el-scrollbar {
      border-right: none;
    }

    &.el-loading-parent--relative {
      pointer-events: all;
    }
  }
}

.ltw-header {
  .el-menu {
    transition: all 0.3s;
    flex-grow: 1;
    // width: 100%;
    background: rgba(0, 0, 0, 0);
  }
}

.el-menu--horizontal {
  border-bottom: none;
}

.el-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgb(144, 147, 153);
  font-size: 12px;
  line-height: 36px;
  height: 36px;

  .el-link {
    margin-left: 10px;
  }
}

// :deep(.el-scrollbar__view) {
//   height: 100%;
// }
</style>
