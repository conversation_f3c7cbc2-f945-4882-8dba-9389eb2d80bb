import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqTaskFunctionGroup = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups',
    data,
    params
})
export const updateDaqTaskFunctionGroup = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups',
    data,
    params
})
export const deleteDaqTaskFunctionGroup = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups',
    params
})
export const listDaqTaskFunctionGroup = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups',
    params
})
export const listDaqTaskFunctionGroupSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/selections',
    params
})
export const pageDaqTaskFunctionGroup = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/page',
    params
})
export const getDaqTaskFunctionGroup = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/' + id})

export const treeDaqTaskFunctionGroup = (params = {},fullLoading) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/queryFunctionGroupTree', params,fullLoading})

export const reorderFunctionGroup = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_groups/reorder', data, params})