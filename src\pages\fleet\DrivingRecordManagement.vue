<template>
  <div class="fleet-management">
    <div class="query-params">
      <el-select
        class="param-item"
        v-model="queryParam.vinList"
        multiple
        filterable
        collapse-tags
        collapse-tags-tooltip
        clearable
        placeholder="车架号"
      >
        <el-option v-for="item in bsVehicleList" :key="item.vin" :label="item.vin" :value="item.vin" />
      </el-select>
      <employee-selection
        class="param-item"
        clearable
        multiple
        filterable
        collapse-tags
        collapse-tags-tooltip
        placeholder="执行人"
        :query-param="complianceEmployeeQueryParam"
        v-model="queryParam.complianceOfficerEmpIdList"
      ></employee-selection>

      <el-cascader
        style="margin-right: 10px"
        placeholder="状态"
        clearable
        collapse-tags
        collapse-tags-tooltip
        teleported
        :show-all-levels="false"
        v-model="queryParam.categoryCodeList"
        :options="categoryCodeList"
        :props="props"
        @change="changeCategoryCodeList"
      />
      <div class="param-item date-picker">
        <el-date-picker
          style="margin-right: 10px; width: 100%"
          clearable
          v-model="queryParam.dateRange"
          ref='datePicker'
          type="datetimerange"
          :range-separator="$t('到')"
          :start-placeholder="$t('开始时间')"
          :end-placeholder="$t('结束时间')"
          value-format="YYYY-MM-DD HH:mm:ss"
          popper-class="dateRange"
        />
      </div>

      <!--      startTime-->
      <el-button class="confirm-btn" color="#5755FF" @click="search">
        <span>{{ $t('确认') }}</span>
      </el-button>
      <el-button class="confirm-btn" type="danger" :disabled="disabledByParams" @click="reset">
        <span>{{ $t('重置') }}</span>
      </el-button>
    </div>
    <div class="line">
      <el-divider />
    </div>
    <div class="table-opt">
      <div class="table-sort">
        展示
        <common-selection
          size="small"
          class="display-type"
          v-model="displayType"
          model-code="code"
          model-Name="name"
          :model-options="displayList"
          @change="changeDisplayType"
        />
        <el-divider direction="vertical" />
        排序
        <common-selection
          size="small"
          class="sort-type"
          v-model="sortType"
          model-code="code"
          model-Name="name"
          :model-options="sortList"
          @change="changeSortType"
        />
        <el-divider direction="vertical" />
        <div class="sort-item">
          <ltw-icon icon-code="el-icon-clock"></ltw-icon>
          总时长
          <div class="num">{{ formatSecToDate(allDurationSum || 0) }}H</div>
        </div>
        <el-divider direction="vertical" />
        <div class="sort-item">
          <ltw-icon icon-code="el-icon-folder"></ltw-icon>
          总记录数
          <div class="num">{{ allRecordSum || 0 }}</div>
        </div>
      </div>
      <div class="opt-btns">
        <el-button
          plain
          color="#5755FF"
          :type="item.buttonStyleType"
          :key="item.id"
          :id="item.buttonIconCode"
          v-for="item in outlineFunctionList"
          @click="executeButtonMethod(item)"
        >
          <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
          {{ $t(item.name) }}
        </el-button>
        <el-dropdown
          @command="handleCommand"
          class="batch-operate-btn"
          v-if="batchingFunctionList && batchingFunctionList.length > 0"
        >
          <el-button type="primary" plain color="#5755FF">
            {{ $t('批量操作') }}
            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                :id="item.buttonIconCode"
                :key="item.id"
                v-for="item in batchingFunctionList"
                :command="item.buttonCode"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                {{ $t(item.name) }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="content">
      <div v-if="displayType === 'table'">
        <el-table class="table-compatible" :data="pageData.records" stripe border>
          <el-table-column
            header-align="left"
            align="left"
            prop="vin"
            :label="$t('任务编码')"
            show-tooltip-when-overflow
            width="130"
            fixed="left"
          >
            <template #default="scope">
              <el-link v-if="scope.row.taskId" type="primary" :underline="false" @click="getTaskDetail(scope.row)"
                >{{ scope.row.taskCode || '-' }}
              </el-link>
              <span v-else>{{ scope.row.taskCode || '-' }}</span>
            </template>
          </el-table-column>
           <el-table-column
            header-align="left"
            align="left"
            prop="taskName"
            :label="$t('任务名称')"
            show-tooltip-when-overflow
            width="190"
            fixed="left"
          >
            <template #default="scope">
              <el-button size="small" class="tag-btn" plain color="#409eff">
                {{ scope.row.taskName || '-' }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="vin"
            :label="$t('车架号')"
            show-tooltip-when-overflow
            width="100"
            fixed="left"
          >
            <template #default="scope">
              <el-button size="small" class="tag-btn" plain color="#5755FF">
                {{ scope.row.vin }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="enable"
            :label="$t('执行人')"
            show-tooltip-when-overflow
            width="100"
            fixed="left"
          >
            <template #default="scope">
              <el-button size="small" class="tag-btn" plain color="#C535BC">
                {{ scope.row.complianceOfficerEmpName || '-' }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" :label="$t('行车类型')" show-tooltip-when-overflow>
            <template #default="scope">
              <el-button
                size="small"
                class="tag-btn"
                plain
                :color="scope.row.recordType === 'non_acquisition'?generateRandomColor(scope.row.categoryName):generateRandomColor(scope.row.acquisitionTypeName)"
                show-tooltip-when-overflow
              >
                {{
                  (scope.row.recordType === 'non_acquisition'
                    ? scope.row.categoryName
                    : scope.row.acquisitionTypeName) || '-'
                }}
              </el-button>
            </template>
          </el-table-column>

          <el-table-column
            header-align="left"
            align="left"
            prop="cantonName"
            :label="$t('行政区')"
            show-tooltip-when-overflow
          >
            <template #default="scope">
              {{ scope.row.cantonName || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="locationName"
            show-tooltip-when-overflow
            :label="$t('地点')"
          >
            <template #default="scope">
              {{ scope.row.locationName || '-' }}
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" show-tooltip-when-overflow :label="$t('日期')" width="90">
            <template #default="scope">
              {{ parseTime(scope.row.startTime, '{y}-{m}-{d}') || '-' }}
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" :label="$t('开始时间')">
            <template #default="scope">
              {{ parseTime(scope.row.startTime, '{h}:{i}:{s}') || '-' }}
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" :label="$t('结束时间')">
            <template #default="scope">
              {{ parseTime(scope.row.endTime, '{h}:{i}:{s}') || '-' }}
            </template>
          </el-table-column>
          <el-table-column header-align="left" align="left" :label="$t('持续时长(H)')" width="100">
            <template #default="scope">
              <el-button size="small" class="tag-btn" plain color="#5755FF">
                {{ formatSecToDate(scope.row.duration) || '-' }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            header-align="left"
            align="left"
            prop="remark"
            :label="$t('描述')"
            show-tooltip-when-overflow
          >
            <template #default="scope">
              {{ scope.row.remark || '-' }}
            </template>
          </el-table-column>
          <el-table-column header-align="center" align="center" :label="$t('操作')" width="200" fixed="right">
            <template #default="scope">
              <el-button-group>
                <template :key="item.id" v-for="item in inlineFunctionList">
                  <template v-if="checkVisible(scope.row, item.buttonCode)">
                    <el-tooltip effect="dark" :content="$t(item.name)" placement="top" :enterable="false">
                      <el-button
                        :type="item.buttonStyleType"
                        size="small"
                        @click="executeButtonMethod(item, scope.row)"
                      >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                      </el-button>
                    </el-tooltip>
                  </template>
                </template>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="day">
        <el-collapse v-model="collapseIndex" accordion expand-icon-position="left" @change="changeCollapse">
          <el-collapse-item :name="index" v-for="(item, index) in pageData?.records">
            <template #title>
              <div class="table-header">
                <ltw-icon icon-code="el-icon-calendar" />
                日期
                {{ item.acquisitionDate }}
                <el-divider direction="vertical" />
                <div class="sort-item">
                  <ltw-icon icon-code="el-icon-clock"></ltw-icon>
                  总时长
                  <div class="num">{{ formatSecToDate(item.durationSum || 0) }}H</div>
                </div>
                <el-divider direction="vertical" />
                <div class="sort-item">
                  <ltw-icon icon-code="el-icon-folder"></ltw-icon>
                  总记录数
                  <div class="num">{{ item.recordSum || 0 }}</div>
                </div>
                <el-divider direction="vertical" />
                <div class="sort-item">
                  <ltw-icon icon-code="el-icon-clock"></ltw-icon>
                  采集总时长
                  <div class="num">{{ formatSecToDate(item.acquisitionDurationSum || 0) }}H</div>
                </div>
                <el-divider direction="vertical" />
                <div class="sort-item">
                  <ltw-icon icon-code="el-icon-clock"></ltw-icon>
                  非采集总时长
                  <div class="num">{{ formatSecToDate(item.nonAcquisitionDurationSum || 0) }}H</div>
                </div>
              </div>
            </template>
            <div class="collapse-item-content">
              <el-table class="table-compatible" :data="subPageData.records" stripe border>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="vin"
                  :label="$t('任务编码')"
                  show-tooltip-when-overflow
                  width="130"
                  fixed="left"
                >
                  <template #default="scope">
                    <el-link v-if="scope.row.taskId" type="primary" :underline="false" @click="getTaskDetail(scope.row)"
                      >{{ scope.row.taskCode || '-' }}
                    </el-link>
                    <span v-else>{{ scope.row.taskCode || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="taskName"
                  :label="$t('任务名称')"
                  show-tooltip-when-overflow
                  width="190"
                  fixed="left"
                >
                  <template #default="scope">
                    <el-button size="small" class="tag-btn" plain color="#409eff">
                      {{ scope.row.taskName || '-' }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="vin"
                  :label="$t('车架号')"
                  show-tooltip-when-overflow
                  width="100"
                  fixed="left"
                >
                  <template #default="scope">
                    <el-button size="small" class="tag-btn" plain color="#5755FF">
                      {{ scope.row.vin }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="enable"
                  :label="$t('执行人')"
                  show-tooltip-when-overflow
                  width="100"
                  fixed="left"
                >
                  <template #default="scope">
                    <el-button size="small" class="tag-btn" plain color="#C535BC">
                      {{ scope.row.complianceOfficerEmpName || '-' }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('行车类型')" show-tooltip-when-overflow>
                  <template #default="scope">
                    <el-button
                      size="small"
                      class="tag-btn"
                      plain
                      :color="scope.row.recordType === 'non_acquisition'?generateRandomColor(scope.row.categoryName):generateRandomColor(scope.row.acquisitionTypeName)"
                      show-tooltip-when-overflow
                    >
                      {{
                        (scope.row.recordType === 'non_acquisition'
                          ? scope.row.categoryName
                          : scope.row.acquisitionTypeName) || '-'
                      }}
                    </el-button>
                  </template>
                </el-table-column>

                <el-table-column
                  header-align="left"
                  align="left"
                  prop="cantonName"
                  :label="$t('行政区')"
                  show-tooltip-when-overflow
                >
                  <template #default="scope">
                    {{ scope.row.cantonName || '-' }}
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="locationName"
                  show-tooltip-when-overflow
                  :label="$t('地点')"
                >
                  <template #default="scope">
                    {{ scope.row.locationName || '-' }}
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  show-tooltip-when-overflow
                  :label="$t('日期')"
                  width="90"
                >
                  <template #default="scope">
                    {{ parseTime(scope.row.startTime, '{y}-{m}-{d}') || '-' }}
                  </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('开始时间')">
                  <template #default="scope">
                    {{ parseTime(scope.row.startTime, '{h}:{i}:{s}') || '-' }}
                  </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('结束时间')">
                  <template #default="scope">
                    {{ parseTime(scope.row.endTime, '{h}:{i}:{s}') || '-' }}
                  </template>
                </el-table-column>
                <el-table-column header-align="left" align="left" :label="$t('持续时长(H)')" width="100">
                  <template #default="scope">
                    <el-button size="small" class="tag-btn" plain color="#5755FF">
                      {{ formatSecToDate(scope.row.duration) || '-' }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                  header-align="left"
                  align="left"
                  prop="remark"
                  :label="$t('描述')"
                  show-tooltip-when-overflow
                >
                  <template #default="scope">
                    {{ scope.row.remark || '-' }}
                  </template>
                </el-table-column>
                <el-table-column header-align="center" align="center" :label="$t('操作')" width="160" fixed="right">
                  <template #default="scope">
                    <el-button-group>
                      <template :key="item.id" v-for="item in inlineFunctionList">
                        <template v-if="checkVisible(scope.row, item.buttonCode)">
                          <el-tooltip effect="dark" :content="$t(item.name)" placement="top" :enterable="false">
                            <el-button
                              :type="item.buttonStyleType"
                              size="small"
                              @click="executeButtonMethod(item, scope.row)"
                            >
                              <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                            </el-button>
                          </el-tooltip>
                        </template>
                      </template>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                background
                @size-change="subHandleSizeChange"
                @current-change="subHandleCurrentChange"
                :current-page="subQueryParam.current"
                :page-sizes="[5, 10, 20, 30]"
                :page-size="subQueryParam.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="subPageData.total"
              >
              </el-pagination>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <el-empty v-if="!pageData?.records?.length" description="暂无数据"></el-empty>
    </div>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      :page-size="queryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageData.total"
    >
    </el-pagination>
  </div>
  <add-driving-record ref="AddDrivingRecord" @reload="reload" />
  <add-collect-record-dialog :task-readonly="false" ref="AddCollectRecordDialog" @reload="reload" />
  <add-task
    @show-task-record="showTaskRecord"
    @showTagList="showTagList"
    @showTagSamplesDialog="showTagSamplesDialog"
    ref="AddTask"
  />
  <tag-samples-dialog ref="TagSamplesDialog" @reload="confirmSamples" />
  <task-tag-list @tagSave="tagSave" ref="TaskTagList" />
  <collect-record-dialog @reload="query" ref="CollectRecordDialog" />
</template>

<script>
import AddTask from '@/pages/dataCollect/dialog/addNewTask.vue'
import TagSamplesDialog from '@/pages/dataCollect/dialog/TagSamplesDialog.vue'
import TaskTagList from '@/pages/dataCollect/dialog/TaskTagList.vue'
import CollectRecordDialog from '@/pages/dataCollect/dialog/CollectRecordDialog.vue'
import CommonSelection from '@/components/system/CommonSelection'
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import AddCollectRecordDialog from '@/pages/dataCollect/dialog/AddOrderCollectRecordDialog.vue'
import {
  pageDaqTaskRecordWithDailyReport,
  pageFtmTaskDailyReportDayGroup,
  exportWebTaskDailyReportExcel,
  exportWebTaskDailyReportGroupExcel,
  deleteTaskDailyReports,
  getTreeDailyReportCategoryData,
  statisticsDaqTaskDailyRecordVO
} from '@/apis/fleet/driving-record-management'
import { deleteDAQTaskRecords } from '@/apis/data-collect/acquisition-task-record'
import AddDrivingRecord from '@/pages/fleet/dialog/AddDrivingRecord'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { dateUtils, showToast, showConfirmToast, downloadFile, debounce } from '@/plugins/util.js'
import { listBsVehicleSelection } from '@/apis/fleet/bs-vehicle'
import { generateRandomColor } from '@/plugins/map/TxMap'

const defaultFormData = {
  current: 1,
  size: 10,
  dateRange: (() => {
    const now = new Date()
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    return [
      dateUtils.parseTime(oneWeekAgo, '{y}-{m}-{d} 00:00:00'),
      dateUtils.parseTime(now, '{y}-{m}-{d} 23:59:59')
    ]
  })()
}
export default {
  name: 'DrivingRecordManagement',
  components: {
    CommonSelection,
    EmployeeSelection,
    AddDrivingRecord,
    AddCollectRecordDialog,
    AddTask,
    TagSamplesDialog,
    TaskTagList,
    CollectRecordDialog
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0,
        records: []
      },
      subPageData: {
        total: 0,
        records: []
      },
      confirmQueryParam: {},
      queryParam: Object.assign({}, defaultFormData),
      subQueryParam: Object.assign({}, defaultFormData),
      bsVehicleList: [],
      displayList: [
        {
          name: '按天排列',
          code: 'day'
        },
        {
          name: '列表排列',
          code: 'table'
        }
      ],
      displayType: 'day',
      sortType: 'time_desc',
      complianceEmployeeQueryParam: {
        orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
      },
      complianceOfficerList: [],

      props: {
        value: 'code',
        label: 'name',
        expandTrigger: 'click',
        multiple: true
      },
      categoryCodeList: [],
      defaultCategoryCodeList: [
        {
          code: 'acquisition',
          name: '采集',
          children: [
            {
              code: 'parking',
              name: 'Parking'
            },
            {
              code: 'driving',
              name: 'Driving'
            }
          ]
        },
        {
          code: 'non_acquisition',
          name: '非采集'
        }
      ],
      parseTime: dateUtils.parseTime,
      allDurationSum: 0,
      allRecordSum: 0,
      collapseIndex: '',
      datePickerType: 'daterange'
    }
  },
  computed: {
    sortList() {
      const baseList = [
        {
          name: '按时间倒序',
          code: 'time_desc'
        },
         {
          name: '按时间正序',
          code: 'time_asc'
        },
      ]

      // 当展示形式为列表时，增加按车架号排序选项
      if (this.displayType === 'table') {
        baseList.push(
          {
            name: '按车架号倒序',
            code: 'vehicle_code_desc'
          },
          {
            name: '按车架号正序',
            code: 'vehicle_code_asc'
          }
        )
      }

      return baseList
    },
    disabledByParams() {
      for (const key in this.queryParam) {
        if (key === 'current' || key === 'size') continue
        if (Object.prototype.toString.call(this.queryParam[key]) === '[object Array]') {
          if (this.queryParam[key]?.length) {
            return false
          }
        } else if (this.queryParam[key]) {
          return false
        }
      }
      return true
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.listBsVehicleSelection()
    this.getTreeDailyReportCategoryData()
  },
  mounted() {
    this.query()
  },
  methods: {
    generateRandomColor(type) {
      if (type === 'parking') {
        return '#099957'
      } else if (type === 'driving') {
        return '#E47A01'
      } else if(type && type.includes('运营')){
        return '#FF6E6F'
      }else if(type && type.includes('集成')){
        return '#409eff'
      }else if(type && type.includes('软件')){
        return '#D91AD9'
      }else if(type && type.includes('故障')){
        return '#8D4EDA'
      }else if(type && type.includes('休息')){
        return '#F7BA1E'
      }else if(type && type.includes('标定')){
        return '#F99057'
      }
      else {
        return '#8A9097'
      }
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    add() {
      this.$refs.AddDrivingRecord.show({ type: 'add' })
    },
    edit(row) {
      if (row.recordType === 'non_acquisition') {
        this.$refs.AddDrivingRecord.show({ type: 'edit', id: row.id })
      } else {
        this.$refs.AddCollectRecordDialog.show({
          type: 'edit',
          data: {
            id: row.id
          }
        })
      }
    },
    view(row) {
      if (row.recordType === 'non_acquisition') {
        this.$refs.AddDrivingRecord.show({ type: 'view', id: row.id })
      } else {
        this.$refs.AddCollectRecordDialog.show({
          type: 'view',
          data: {
            id: row.id
          }
        })
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id }, row)
    },
    refresh() {
      this.query()
    },
    getQueryParam(postData, isTableMode = false) {
      if (this.confirmQueryParam?.categoryCodeList?.length) {
        const categoryCodeList = this.queryParam?.categoryCodeList?.map(val => val[val?.length - 1])
        delete postData.categoryCodeList
        if (this.confirmQueryParam.recordType === 'non_acquisition') {
          postData.categoryCodeList = categoryCodeList
        } else {
          postData.acquisitionTypeList = categoryCodeList
        }
      }
      if (this.confirmQueryParam?.dateRange?.length) {
        if (isTableMode) {
          // 列表排列模式使用 crossStartTime 和 crossEndTime
          postData.crossStartTime = this.confirmQueryParam?.dateRange[0]
          postData.crossEndTime = this.confirmQueryParam?.dateRange[1]
        } else {
          // 按天排列模式使用 startTime 和 endTime
          postData.startTime = this.confirmQueryParam?.dateRange[0]
          postData.endTime = this.confirmQueryParam?.dateRange[1]
        }
      }

      // 添加排序参数
      postData.mapSort = this.getSortParams()

      return postData
    },
    getSortParams() {
      const sortParams = {}

      switch (this.sortType) {
        case 'time_asc':
          sortParams.startTime = 'asc'
          break
        case 'time_desc':
          sortParams.startTime = 'desc'
          break
        case 'vehicle_code_asc':
          sortParams.vin = 'asc'
          break
        case 'vehicle_code_desc':
          sortParams.vin = 'desc'
          break
        case 'default':
        default:
          // 默认排序，不添加排序参数
          break
      }

      return sortParams
    },
    async query() {
      this.confirmQueryParam = JSON.parse(JSON.stringify(this.queryParam))
      const param = { ...this.confirmQueryParam }
      
      if (this.displayType === 'table') {
        // 列表排列模式：使用 crossStartTime 和 crossEndTime
        const tablePostData = this.getQueryParam(param, false)
        pageDaqTaskRecordWithDailyReport(tablePostData).then(res => {
          this.pageData = res.data
        })
        
        // 统计数据也使用相同的参数
        const statisticsPostData = this.getQueryParam({ ...param }, false)
        statisticsDaqTaskDailyRecordVO(statisticsPostData).then(res => {
          this.allDurationSum = res.data.durationSum
          this.allRecordSum = res.data.recordSum
        })
      } else {
        // 按天排列模式：使用 startTime 和 endTime
        const dayGroupPostData = this.getQueryParam(param, false)
        pageFtmTaskDailyReportDayGroup(dayGroupPostData).then(res => {
          this.pageData = res.data
          if (res.data?.records?.length) {
            if (!Number.isInteger(this.collapseIndex) || this.collapseIndex > res.data?.records?.length - 1) {
              this.collapseIndex = 0
            }
            this.subQuery(this.collapseIndex)
          }
        })
        
        // 统计数据使用相同的参数
        const statisticsPostData = this.getQueryParam({ ...param }, false)
        statisticsDaqTaskDailyRecordVO(statisticsPostData).then(res => {
          this.allDurationSum = res.data.durationSum
          this.allRecordSum = res.data.recordSum
        })
      }
    },
    search() {
      this.query()
    },
    reset() {
      // 重新生成默认时间范围
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const newDefaultFormData = {
        ...defaultFormData,
        dateRange: [
          dateUtils.parseTime(oneWeekAgo, '{y}-{m}-{d} 00:00:00'),
          dateUtils.parseTime(now, '{y}-{m}-{d} 23:59:59')
        ]
      }
      this.confirmQueryParam = this.queryParam = Object.assign({}, newDefaultFormData)
      this.query()
    },
    export() {
      const param = { ...this.confirmQueryParam }
      let postData
      if (this.displayType === 'day') {
        // 按天排列导出：使用 startTime 和 endTime
        postData = this.getQueryParam(param, false)
      } else {
        // 列表排列导出：使用 crossStartTime 和 crossEndTime
        postData = this.getQueryParam(param, false)
      }
      delete postData.current
      delete postData.size
      
      if (this.displayType === 'day') {
        exportWebTaskDailyReportGroupExcel(postData)
          .then(async res => {
            if (res.type === 'application/json') {
              // 解析blob中的JSON错误信息
              const text = await res.text()
              try {
                const errorData = JSON.parse(text)
                this.$message.error({
                  message: errorData.message || '导出失败',
                  type: 'error'
                })
              } catch (e) {
                this.$message.warning({
                  message: '暂无数据可导出!',
                  type: 'warning'
                })
              }
              return
            }
            const fileName = '行车记录-按天排列' + dateUtils.formatDateNormal(new Date())
            downloadFile(res, fileName)
          })
          .catch(error => {
            console.error('导出失败:', error)
            this.$message.error('网络异常，导出失败')
          })
      } else {
        exportWebTaskDailyReportExcel(postData)
          .then(async res => {
            if (res.type === 'application/json') {
              // 解析blob中的JSON错误信息
              const text = await res.text()
              try {
                const errorData = JSON.parse(text)
                this.$message.error({
                  message: errorData.message || '导出失败',
                  type: 'error'
                })
              } catch (e) {
                this.$message.warning({
                  message: '暂无数据可导出!',
                  type: 'warning'
                })
              }
              return
            }
            const fileName = '行车记录-列表排列' + dateUtils.formatDateNormal(new Date())
            downloadFile(res, fileName)
          })
          .catch(error => {
            console.error('导出失败:', error)
            this.$message.error('网络异常，导出失败')
          })
      }
    },
    reload() {
      this.query()
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param, row) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        if (row.recordType === 'non_acquisition') {
          deleteTaskDailyReports(param).then(() => {
            this.query()
          })
        } else {
          deleteDAQTaskRecords(param).then(() => {
            this.query()
          })
        }
      })
    },
    handleCommand(command) {
      this.selectedData = this.pageData.records.filter(val => val.checked)
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },

    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    subHandleSizeChange(value) {
      this.subQueryParam.size = value
      this.query()
    },
    subHandleCurrentChange(value) {
      this.subQueryParam.current = value
      this.query()
    },
    changeDisplayType(val) {
      this.datePickerType = val.code == 'day' ? 'daterange' : 'datetimerange'
      if (this.queryParam.dateRange != null) {
        this.queryParam.dateRange = [
          dateUtils.parseTime(this.queryParam.dateRange[0], '{y}-{m}-{d} 00:00:00'),
          dateUtils.parseTime(this.queryParam.dateRange[1], '{y}-{m}-{d} 23:59:59')
        ]
      }
      this.$nextTick(() => {
        this.$refs.datePicker.focus()
      })
      this.pageData = {
        total: 0,
        records: []
      }
      this.displayType = val.code

      // 检查当前排序类型是否在新的展示类型中可用
      const availableSortCodes = this.sortList.map(item => item.code)
      if (!availableSortCodes.includes(this.sortType)) {
        // 如果当前排序类型不可用，重置为时间倒序
        this.sortType = 'time_desc'
      }

      this.$nextTick(() => {
        this.query()
      })
    },
    changeSortType(val) {
      this.sortType = val.code
      // 切换排序时重置页码到第1页
      this.queryParam.current = 1
      this.subQueryParam.current = 1
      this.query()
    },

    listBsVehicleSelection() {
      if (!this.bsVehicleList?.length) {
        listBsVehicleSelection().then(res => {
          this.bsVehicleList = res.data
        })
      }
    },
    getTreeDailyReportCategoryData() {
      if (!this.categoryCodeList?.length) {
        getTreeDailyReportCategoryData().then(res => {
          const categoryCodeList = JSON.parse(JSON.stringify(this.defaultCategoryCodeList))
          categoryCodeList.find(val => {
            if (val.code === 'non_acquisition') {
              val.children = res.data
              return true
            }
          })
          this.categoryCodeList = categoryCodeList
        })
      }
    },
    changeCategoryCodeList(val) {
      let key
      if (val?.length) {
        key = val[0][0]
      }
      this.checkDisabledCategory(key)
    },
    checkDisabledCategory(key) {
      this.queryParam.recordType = key
      this.categoryCodeList.forEach(val => {
        if (key && val.code !== key) {
          val.disabled = true
        } else {
          val.disabled = false
        }
      })
    },
    changeCollapse(index) {
      if (Number.isInteger(index)) {
        this.subQueryParam = Object.assign({}, defaultFormData)
        this.subPageData = {
          total: 0,
          records: []
        }
        this.subQuery(index)
      }
    },
    subQuery(index) {
      const param = { ...this.confirmQueryParam, ...this.subQueryParam }
      param.acquisitionDate = this.pageData.records[index].acquisitionDate
      // 子查询时不传时间参数，因为已经有 acquisitionDate
      const postData = this.getQueryParam(param)
      delete postData.startTime
      delete postData.endTime
      delete postData.crossStartTime
      delete postData.crossEndTime
      pageDaqTaskRecordWithDailyReport(postData).then(res => {
        this.subPageData = res.data
      })
    },
    formatSecToDate(seconds) {
      return parseFloat(parseFloat(seconds / 3600).toFixed(2))
    },
    checkVisible(row, buttonIconCode) {
      return !(
        row.recordType === 'acquisition' &&
        (buttonIconCode === 'edit' || buttonIconCode === 'singleRemove') &&
        !row.editable
      )
    },
    showTaskRecord(row) {
      row.data.btnPermission = {
        batchingFunctionList: this.batchingFunctionList,
        outlineFunctionList: this.outlineFunctionList,
        inlineFunctionList: this.inlineFunctionList
      }
      this.$refs.CollectRecordDialog.show(row)
    },
    showTagList(item) {
      this.$refs.TaskTagList.show(item)
    },
    showTagSamplesDialog(row) {
      this.$refs.TagSamplesDialog.show(row)
    },
    confirmSamples(row) {
      this.$refs.AddTask.confirmSamples(row)
    },
    tagSave(row) {
      if (row.data.groupList?.length > 0) {
        if (row.data.taskType === 'req') {
          let postData = { type: row.type === 'view' ? 'onlyView' : row.type, data: row.data }
          this.showTagSamplesDialog(postData)
        } else if (row.data.taskType === 'class') {
          this.$refs.AddTask.confirmClassificationTag(row.data)
        }
      }
    },
    getTaskDetail(row) {
      this.$refs.AddTask.show({
        type: 'view',
        id: row.taskId
      })
    }
  }
}
</script>
<style scoped lang="scss">
.fleet-management {
  .query-params {
    display: flex;

    .param-item {
      width: 240px;
      margin-right: 10px;
      display: inline-block;

      &.date-picker {
        width: 400px;
      }
    }
  }

  .num {
    margin-left: 8px;
    color: #5755ff;
    font-size: 12px;
    font-weight: 700;
    line-height: 24px;
  }

  .line {
    padding: 0 12px;

    .el-divider {
      margin: 16px 0;
      position: static;
    }
  }

  .sort-item {
    display: flex;
    align-items: center;

    .ltw-icon {
      margin-right: 4px;
    }
  }

  .tag-btn {
    pointer-events: none;
  }

  .table-opt {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 10px;

    .table-sort {
      display: flex;
      align-items: center;

      .display-type {
        width: 100px;
        margin-left: 10px;
      }

      .sort-type {
        width: 120px;
        margin-left: 10px;
      }

      .view-type-btn {
        margin-right: 10px;
      }
    }

    .opt-btns {
      .el-button {
        margin-right: 10px;
        margin-left: 0;
      }
    }
  }

  .content {
    .el-collapse {
      border: none;

      .el-collapse-item {
        border: 1px solid #d0d4d8;
        margin-bottom: 17px;

        :deep(.el-collapse-item__header) {
          background: #fafafc;
          padding: 0 20px;
          border: none;
        }

        :deep(.el-collapse-item__content) {
          padding-bottom: 0;
        }

        .table-header {
          display: flex;
          align-items: center;
        }

        .collapse-item-content {
          padding: 0 16px 16px 16px;
        }
      }
    }

    :deep(.table-compatible) {
      position: static;

      div,
      span,
      .el-table__cell {
        position: inherit;
      }

      &::before,
      &::after,
      .el-table__inner-wrapper::before,
      .el-table__inner-wrapper::after {
        display: none;
      }
    }
  }
}
</style>
