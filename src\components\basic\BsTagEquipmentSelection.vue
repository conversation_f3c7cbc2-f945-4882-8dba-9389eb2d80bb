<template>
  <el-select
    v-model="selectValue"
    :disabled="disabled"
    @change="handleChange"
    :clearable="clearable"
    popper-class="bs-tag-equipment-selection"
  >
    <el-option
      v-for="item in list"
      :key="item.id"
      :label="item.code"
      :value="item.id"
      :id="item.code"
    >
      <div class="select-option">
        <div>{{ item.brand }}-{{ item.model }}</div>
        <div>{{ item.code }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { ElSelect, ElOption } from 'element-plus'
import { listBsTagEquipment } from '@/apis/data-collect/bs-tag-equipment'

export default {
  name: 'BsTagEquipmentSelection',
  props: {
    status: String,
    modelValue: [String, Number],
    disabled: Boolean,
    clearable: {
      type: Boolean,
      default: false
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  emits: ['change', 'update:modelValue'],
  data() {
    return {
      map: {},
      list: [],
      queryParam: {}
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  watch: {
    data(val) {
      this.list = val
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  components: {
    ElSelect,
    ElOption
  },
  mounted() {
    this.queryParam.status = this.status
    if (this.autoLoad) {
      this.query()
    } else {
      this.list = this.data
      if (this.list && this.list.length > 0) {
        this.map = {}
        this.list.forEach(item => {
          this.map[item.id] = item
        })
      }
    }
  },
  methods: {
    query() {
      listBsTagEquipment(this.queryParam).then(res => {
        this.list = res.data
        if (this.list && this.list.length > 0) {
          this.map = {}
          this.list.forEach(item => {
            this.map[item.id] = item
          })
        }
      })
    },
    reload() {
      this.query()
    },
    handleChange(value) {
      this.$emit('change', {
        value: value,
        node: this.map[value]
      })
      this.$emit('update:modelValue', value)
    },
    hasEquipment(equipmentId) {
      if (this.map[equipmentId]) {
        return true
      }
      return false
    }
  }
}
</script>

<style scoped lang="scss">
.select-option {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  div {
    margin-right: 10px;
    padding: 1px;
  }
}
</style>
