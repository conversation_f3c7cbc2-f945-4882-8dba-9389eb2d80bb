var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import PcdParseWorker from './pcd-parse.worker';
import { getTransferList } from './get-transfer-list';
import { EventEmitter } from 'events';
class ParserThread {
    constructor(context) {
        this.isIdle = true;
        this.context = context;
        this.worker = new PcdParseWorker({ name: 'pcd' }, { name: 'pcd' });
        this.worker.onmessage = (data) => {
            this.isIdle = true;
            this.context.emitResult(data.data);
        };
    }
    terminate() {
        this.worker.terminate();
    }
    startJob(data, transferList) {
        const transfer = transferList || getTransferList(data);
        this.isIdle = false;
        this.worker.postMessage(data, transfer);
    }
}
export class ParserPool {
    constructor() {
        this.workers = [];
        this.eventEmitter = new EventEmitter();
        this.jobQueue = [];
        this.maxPoolSize = 10;
    }
    get idleWorkers() {
        return this.workers.filter((item) => item.isIdle);
    }
    /**
     * @param data PCD file arraybuffer
     */
    parse(data) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.idleWorkers.length && this.workers.length < this.maxPoolSize) {
                this.workers.push(new ParserThread(this));
            }
            const key = 'key' + Math.random();
            this.emitJob({ key, data });
            return new Promise((resolve, reject) => {
                this.eventEmitter.once(key, ({ data, error }) => {
                    error ? reject(new Error(error)) : resolve(data);
                });
            });
        });
    }
    emitJob(data) {
        this.jobQueue.push(data);
        this.startJob();
    }
    emitResult(data) {
        this.eventEmitter.emit(data.key, data);
        this.startJob();
    }
    startJob() {
        if (!this.jobQueue.length || !this.idleWorkers.length)
            return;
        const job = this.jobQueue.shift();
        const worker = this.idleWorkers[0];
        worker.startJob(job);
    }
    dispose() {
        this.workers.forEach((item) => item.terminate());
        this.eventEmitter.removeAllListeners();
    }
}
