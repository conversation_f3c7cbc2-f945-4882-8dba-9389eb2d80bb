<template>
  <el-input
    :placeholder="$t(placeholder)"
    :disabled="disabled"
    v-model="inputValue"
    :type="type"
    :clearable="clearable"
    :show-password="showPassword"
    :autosize="autosize"
    :readonly="readonly"
    :autofocus="autofocus"
    :label="label"
    :validateEvent="validateEvent"
    :inputStyle="inputStyle"
    :show-word-limit="showWordLimit"
    :prefix-icon="prefixIcon"
    :suffix-icon="suffixIcon"
    :rows="rows"
    @blur="blur"
    @focus="focus"
    @change="change"
    @input="input"
    @clear="clear"
    @keyup.enter="clear(inputValue)"
  >
    <template #prefix v-if="$slots.prefix">
      <slot name="prefix"> </slot>
    </template>
    <template #suffix v-if="$slots.suffix">
      <slot name="suffix"> </slot>
    </template>
    <template #append v-if="$slots.append">
      <slot name="append"> </slot>
    </template>
    <template #prepend v-if="$slots.prepend">
      <slot name="prepend"></slot>
    </template>
  </el-input>
</template>
<script>
/* 组件属性
    textType：
      code : '15个字符长度'
      name(默认) : '30个字符长度'
      description : '60个字符长度'
      remark : '120个字符长度' */
import { ElInput } from 'element-plus'
import { i18n } from '@/plugins/lang'
const specialChar = '%'
const expChar = '[`~' + specialChar + ']'
const specialflag = new RegExp(expChar)
export default {
  name: 'LtwInput',
  props: {
    placeholder: {
      type: String,
      default: '请输入'
    },
    modelValue: [String, Number, Array],
    disabled: Boolean,
    showPassword: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default'
    },
    showWordLimit: {
      type: Boolean,
      default: false
    },
    prefixIcon: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: ''
    },
    rows: {
      type: Number,
      default: 2
    },
    autosize: {
      type: [Boolean, Object],
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    textType: {
      type: String,
      default: 'name'
    },
    autofocus: {
      type: Boolean,
      default: false
    },
    label: String,
    validateEvent: {
      type: Boolean,
      default: true
    },
    inputStyle: {
      type: Boolean,
      default: () => {}
    },
    type: {
      type: String,
      default: 'text'
    },
    limitSize: {
      type: Number,
      default: 0
    }
  },
  emits: ['blur', 'focus', 'input', 'change', 'update:modelValue', 'clear'],
  data() {
    return {
      $t: i18n.global.t
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  components: {
    ElInput
  },
  methods: {
    blur(value) {
      this.$emit('blur', value)
    },
    focus(value) {
      this.$emit('focus', value)
    },
    change(value) {
      let str
      // 判断 even 是否包含特殊字符
      if (specialflag.test(value)) {
        value = value && value.replace(new RegExp(expChar, 'g'), '')
        this.$message({
          message:
            this.$t('请勿输入包含') + specialChar + this.$t('的特殊字符'),
          type: 'warning'
        })
      }
      // 判断 even 是否包含空格
      value = value.trim()
      switch (this.textType) {
        case 'code':
          if (value.length > 15) {
            value = value.substr(0, 15)
            this.$message({
              message: this.$t('请勿输入超过') + 15 + this.$t('个字符'),
              type: 'warning'
            })
          }
          break
        case 'description':
          if (value.length > 60) {
            value = value.substr(0, 60)
            this.$message({
              message: this.$t('请勿输入超过') + 60 + this.$t('个字符'),
              type: 'warning'
            })
          }
          break
        case 'remark':
          if (value.length > 120) {
            value = value.substr(0, 120)
            this.$message({
              message: this.$t('请勿输入超过') + 120 + this.$t('个字符'),
              type: 'warning'
            })
          }
          break
        default:
          let limitSize = this.limitSize || 30
          if (value.length > limitSize) {
            value = value.substr(0, limitSize)
            this.$message({
              message: this.$t('请勿输入超过') + limitSize + this.$t('个字符'),
              type: 'warning'
            })
          }
      }
      // if (spaceFlag.test(value)) {
      //   str = value && value.replace(/\s/g, '')
      //   if (!str) {
      //     value = str
      //   }
      // }
      this.$emit('change', value)
      this.$emit('update:modelValue', value)
    },
    input(value) {
      this.$emit('input', value)
    },
    clear(value) {
      this.$emit('clear', value)
      this.$emit('update:modelValue', value)
    }
  }
}
</script>

<style scoped></style>
