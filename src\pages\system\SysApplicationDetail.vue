<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container" >
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>{{item.name}}
                    </el-button>
                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item icon="el-icon-delete"  command="batchRemove">
                                    批量删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records"  @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                <el-table-column header-align="left" align="left" prop="applicationId" label="应用id"></el-table-column>
                <el-table-column header-align="left" align="left" prop="purpose" label="用途"></el-table-column>
                <el-table-column header-align="left" align="left" prop="domain" label="域名"></el-table-column>
                <el-table-column header-align="left" align="left" prop="hasPersonalInfo" label="是否涉及个人信息"></el-table-column>
                <el-table-column header-align="left" align="left" prop="hasStateSecret" label="是否涉及国家机密"></el-table-column>
                <el-table-column header-align="left" align="left" prop="thirdpartyInteractive" label="是否与外部第三方系统进行数据交互"></el-table-column>
                <el-table-column header-align="left" align="left" prop="hasApp" label="是否有移动app"></el-table-column>
                <el-table-column header-align="left" align="left" prop="appReinforced" label="移动app是否加固"></el-table-column>
                <el-table-column header-align="left" align="left" prop="recordDate" label="登记日期"></el-table-column>
                <el-table-column header-align="left" align="left" label="操作" width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name"
                                        placement="top" :enterable="false"
                            >
                                 <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon></el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="应用id" prop="applicationId">
                    <ltw-input v-model="formData.applicationId" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="用途" prop="purpose">
                    <ltw-input v-model="formData.purpose" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="域名" prop="domain">
                    <ltw-input v-model="formData.domain" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否涉及个人信息" prop="hasPersonalInfo">
                    <ltw-input v-model="formData.hasPersonalInfo" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否涉及国家机密" prop="hasStateSecret">
                    <ltw-input v-model="formData.hasStateSecret" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否与外部第三方系统进行数据交互" prop="thirdpartyInteractive">
                    <ltw-input v-model="formData.thirdpartyInteractive" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否有移动app" prop="hasApp">
                    <ltw-input v-model="formData.hasApp" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="移动app是否加固" prop="appReinforced">
                    <ltw-input v-model="formData.appReinforced" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="登记日期" prop="recordDate">
                    <ltw-input v-model="formData.recordDate" :disabled="formReadonly"></ltw-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveSysApplicationDetail,
        updateSysApplicationDetail,
        deleteSysApplicationDetail,
        pageSysApplicationDetail,
        getSysApplicationDetail
    } from '@/apis/system/sys-application-detail'
    

const defaultFormData = {}
    export default {
        name: "SysApplicationDetail",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],

                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({},defaultFormData),
                formRules: {
                    applicationId: [
                        {required: true, message: '请输入应用id', trigger: 'blur'}
                    ],
                    purpose: [
                        {required: true, message: '请输入用途', trigger: 'blur'}
                    ],
                    domain: [
                        {required: true, message: '请输入域名', trigger: 'blur'}
                    ],
                    hasPersonalInfo: [
                        {required: true, message: '请输入是否涉及个人信息', trigger: 'blur'}
                    ],
                    hasStateSecret: [
                        {required: true, message: '请输入是否涉及国家机密', trigger: 'blur'}
                    ],
                    thirdpartyInteractive: [
                        {required: true, message: '请输入是否与外部第三方系统进行数据交互', trigger: 'blur'}
                    ],
                    hasApp: [
                        {required: true, message: '请输入是否有移动app', trigger: 'blur'}
                    ],
                    appReinforced: [
                        {required: true, message: '请输入移动app是否加固', trigger: 'blur'}
                    ],
                    recordDate: [
                        {required: true, message: '请输入登记日期', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData:[]
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList

            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            refresh(){
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysApplicationDetail(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加应用详情'
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveSysApplicationDetail(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateSysApplicationDetail(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                    }
                )
            },
            edit({id}) {
                this.dialogTitle = '修改应用详情'
                this.dialogStatus = 'edit'
                getSysApplicationDetail(id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            view({id}) {
                this.dialogTitle = '查看应用详情'
                this.dialogStatus = 'view'
                getSysApplicationDetail(id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            handleCommand(command){
                if(this.selectedData.length === 0){
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if(command === 'batchRemove'){
                    this.batchRemove()
                }
            },
            singleRemove({id}) {
                this.remove({id})
            },
            batchRemove(){
                let idList = [];
                this.selectedData.forEach(ele=>{
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param){
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysApplicationDetail(param).then(
                        ()=>{
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message:'已取消删除'
                    })
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened(){

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value){
                this.selectedData = value
            },
            initForm(){
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({},defaultFormData)
            }

        }
    }
</script>