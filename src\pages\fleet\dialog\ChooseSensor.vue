<template>
  <el-drawer
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="formReadonly"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="sensor-drawer"
  >
    <div class="header-tools">
      <div class="header-search">
        <ltw-input
          :placeholder="$t('通过车辆识别代码查询')"
          v-model="queryParam.key"
          clearable
          @clear="refresh"
          class="search-key"
        >
          <template #append>
            <el-button @click="refresh" id="el-icon-search">
              <ltw-icon icon-code="el-icon-search"></ltw-icon>
            </el-button>
          </template>
        </ltw-input>
        <el-button class="add-btn" type="primary" @click="add()">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          {{ $t('新增') }}
        </el-button>
      </div>
<!--      <el-button type="primary" @click="confirm()">-->
<!--        {{ $t('确认') }}-->
<!--      </el-button>-->
    </div>
    <el-table
      :data="pageData.records"
      border
      row-key="id"
      ref="tableRef"
      class="table-height"
    >
      <el-table-column header-align="left" align="left" prop="model" :label="$t('传感器型号')" show-overflow-tooltip>
        <!-- <template #default="scope">
          <el-link @click="view(scope.row)" :underline="false" type="primary">{{
            scope.row.model
          }}</el-link>
        </template> -->
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="sensorTypeName"
        :label="$t('传感器类型')"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="supplierName"
        :label="$t('供应商')"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="specification"
        :label="$t('规格')"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="remark"
        show-overflow-tooltip
        :label="$t('备注')"
      ></el-table-column>
      <el-table-column header-align="left" align="left" :label="$t('操作')" min-width="120" fixed="right">
        <template #default="scope">
          <el-button-group>
            <el-tooltip effect="dark" :content="$t('确认')" placement="top" :enterable="false">
              <el-button type="warning" @click="confirm(scope.row)">
                <ltw-icon icon-code="el-icon-check"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('查看详情')" placement="top" :enterable="false">
              <el-button type="primary" @click="view(scope.row)">
                <ltw-icon icon-code="el-icon-view"></ltw-icon>
              </el-button>
            </el-tooltip>
            <!-- <el-tooltip
              effect="dark"
              :content="$t('编辑')"
              placement="top"
              :enterable="false"
            >
              <el-button type="warning" @click="edit(scope.row)">
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="$t('删除')"
              placement="top"
              :enterable="false"
            >
              <el-button type="danger" @click="singleRemove(scope.row)">
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip> -->
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      :page-size="queryParam.size"
      layout="total, sizes, prev, pager, next, jumper, ->"
      :total="pageData.total"
    >
    </el-pagination>
    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button id="cancel" @click="dialogVisible = false">{{
          $t('取消')
        }}</el-button>
        <el-button id="save" type="primary" @click="submit">{{
          $t('确认')
        }}</el-button>
      </span>
    </template> -->
  </el-drawer>
  <AddSensor ref="AddSensor" @reload="reload"></AddSensor>
</template>

<script>
import { deleteFtmSensor, pageFtmSensor } from '@/apis/fleet/ftm-sensor'
import AddSensor from '@/pages/fleet/dialog/AddSensor.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast } from '@/plugins/util'

const defaultform = {}
export default {
  name: 'ChooseModality',
  emits: ['reload'],
  components: {
    AddSensor
  },
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      sensorType: '',
      installationRecordId: '',
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      selectedData: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'view':
          this.dialogTitle = this.$t('选择传感器实体')
          this.sensorType = row.sensorType
          this.pageFtmSensor()
          // this.form.sensorType = row.sensorType
          break
      }
    },
    pageFtmSensor() {
      let postData = {
        ...this.queryParam,
        sensorType: this.sensorType
      }
      pageFtmSensor(postData).then(res => {
        this.pageData = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      this.cancel('reload')
      // let postData = {
      //   vehicleId: this.vehicleId,
      //   installationRecordId: this.installationRecordId,
      //   versionMappingModalityIdList: this.selectedData.map(val => val.id)
      // }
      // saveFtmVehicleMappingModalitys(postData).then(res => {
      //   this.cancel('reload')
      // })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.pageFtmSensor()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.pageFtmSensor()
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        deleteFtmSensor(param).then(() => {
          this.pageFtmSensor()
        })
      })
    },
    getFtmVehicleModality(row) {
      getFtmVehicleModality(row.id).then(res => {
        this.form = res.data
      })
    },
    viewModality(row, index) {
      this.$refs.AddSensor.show({
        type: 'view',
        index,
        data: row
      })
    },
    handleRowCurrentChange(value) {
      this.selectedData = value
    },
    add() {
      this.$refs.AddSensor.show({ type: 'add', sensorType: this.sensorType })
    },
    edit(row) {
      this.$refs.AddSensor.show({ type: 'edit', id: row.id })
    },
    view(row) {
      this.$refs.AddSensor.show({ type: 'view', id: row.id })
    },
    reload() {
      this.pageFtmSensor()
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.pageFtmSensor()
    },
    confirm(row) {
      // if (Object.getOwnPropertyNames(this.selectedData).length) {
      if (Object.getOwnPropertyNames(row).length) {
        this.cancel(row)
      } else {
        showToast('请先选择传感器实体', 'warning')
      }
    }
  }
}
</script>

<style>
.sensor-drawer > .el-drawer__header {
  margin-bottom: 0;
}

.sensor-drawer > .el-drawer__body {
  height: calc(100% - var(--el-drawer-padding-primary) - 42px);
}
</style>
<style scoped lang="scss">
.header-tools {
  display: flex;
  justify-content: space-between;

  .header-search {
    display: flex;

    .add-btn {
      margin-left: 10px;
    }
  }
}

.table-height {
  height: calc(100% - 32px - 46px - 15px);
}
</style>
