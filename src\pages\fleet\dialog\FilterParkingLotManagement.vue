<template>
  <el-drawer
    :title="dialogTitle"
    v-model="dialogVisible"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="filter-task"
  >
    <el-scrollbar>
      <el-form class="form-filter" label-position="top" :model="form" ref="formRef">
        <template v-for="group in fieldWithItems" :key="group.code">
          <el-form-item :label="group.name">
            <el-checkbox-group v-model="form[group.code]">
              <el-checkbox-button v-for="item in group.itemList" :key="item.itemCode" :label="item.itemCode">
                {{ item.itemName }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
        </template>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="submit" id="submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script>
import { listParkingLotFieldWithItems } from '@/apis/fleet/parking-lot-management'

const defaultform = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: {},
      fieldWithItems: []
    }
  },
  methods: {
    async show() {
      this.dialogVisible = true
      this.dialogTitle = this.$t('筛选')
      if (!this.fieldWithItems?.length) {
        listParkingLotFieldWithItems().then(res => {
          this.fieldWithItems = res.data
        })
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {},
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        this.cancel(postData)
      })
    },
    resetForm() {
      // this.$refs.formRef.resetFields()
      this.form = {}
    }
  }
}
</script>
<style scoped lang="scss">
.form-filter {
  .label-item {
    position: absolute;
    top: -34px;
    left: 80px;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }
}
</style>
