<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <ltw-input
            placeholder="请输入关键字"
            v-model="queryParam.key"
            clearable
            @clear="refresh"
          >
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </ltw-input>
        </div>
        <div class="ltw-tool-container">
          <el-select
            v-model="queryParam.tagId"
            placeholder="请选择分类标签"
            @change="refresh"
            clearable
          >
            <el-option
              v-for="item in tagList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item.buttonCode)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>

            {{ item.name }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              批量操作
              <ltw-icon
                icon-code="el-icon-arrow-down"
                class="el-icon--right"
              ></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
      >
        <el-table-column
          header-align="left"
          align="left"
          type="selection"
          width="55"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="code"
          label="编码"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="name"
          label="名称"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="indexUrl"
          label="首页地址"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          label="图标"
          width="140"
        >
          <template #default="scope">
            <div class="icon-container">
              <ltw-auth-image :auth-src="getIconUrl(scope.row)" fit="fill">
                <template #error>
                  <div class="image-slot">
                    <ltw-icon icon-code="el-picture-outline"></ltw-icon>
                  </div>
                </template>
              </ltw-auth-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="tagList"
          label="标签"
        >
          <template #default="scope">
            <div class="tag-container">
              <el-tag
                type="success"
                :key="tag.id"
                v-for="tag in scope.row.tagList"
                >{{ tag.tagName }}</el-tag
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column
          header-align="left"
          align="left"
          prop="sortNum"
          label="顺序"
          width="100"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          label="状态"
          width="100"
        >
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="changeStatus(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          label="操作"
          width="180"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="item.name"
                placement="top"
                :enterable="false"
              >
                <el-button
                  :type="item.buttonStyleType"
                  @click="executeButtonMethod(item.buttonCode, scope.row)"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon
                ></el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
      @open="dialogOpened"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="编码" prop="code">
          <ltw-input
            v-model="formData.code"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <ltw-input
            v-model="formData.name"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="首页地址" prop="indexUrl">
          <ltw-input
            v-model="formData.indexUrl"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="标签" prop="tagList">
          <!--                    <el-tag type="success" v-for="item in formData.tagList" :closable="!formReadonly">{{item.name}}</el-tag>-->
          <!--                    <el-button v-if="tagSelectVisible" size="small" @click="showInput" icon="el-icon-plus">-->
          <!--                        添加标签</el-button>-->
          <el-select
            v-model="formData.tagIdList"
            multiple
            placeholder="请选择"
            clearable
            filterable
            allow-create
            default-first-option
            @change="handleTagSelectChange"
            :disabled="formReadonly"
          >
            <el-option
              v-for="item in tagList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <upload-file
            list-type="picture"
            source-type="app_icon"
            ref="UploadFile"
            :limit="1"
            v-model="formData.icon"
            :source-id="formData.id"
            accept=".jpg,.jpeg,.png,.gif"
          ></upload-file>
          <!-- <el-upload
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            list-type="picture"
            :data="filePostData"
            :headers="headerObj"
            :http-request="handleUpload"
            ref="uploadRef"
            :file-list="fileList"
            :disabled="formReadonly"
          >
            <el-button size="small" type="primary" v-if="!formReadonly"
              >点击上传</el-button
            >
          </el-upload> -->
        </el-form-item>
        <el-form-item label="展示顺序" prop="sortNum">
          <ltw-input
            v-model="formData.sortNum"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-switch
            v-model="formData.enabled"
            :disabled="formReadonly"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >关 闭</el-button
          >
          <template v-else>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveSysPrivilegeApplication,
  updateSysPrivilegeApplication,
  deleteSysPrivilegeApplication,
  pageSysPrivilegeApplication,
  getSysPrivilegeApplication
} from '@/apis/system/sys-privilege-application'
import { listSysTag } from '@/apis/system/sys-tag'
import { uploadFile } from '@/apis/base/file'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwAuthImage from '@/components/base/LtwAuthImage'
import BILL_CODE from '@/plugins/constants/bill-code'
import util from '@/plugins/util'
import UploadFile from '@/components/system/UploadFile.vue'

const defaultFormData = {
  enabled: true,
  sortNum: 1
}

export default {
  name: 'SysPrivilegeApplication',
  data() {
    return {
      currentTagId: '',
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],

      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
        withTag: true
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        code: [{ required: true, message: '请输入应用编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
        indexUrl: [
          { required: true, message: '请输入首页地址', trigger: 'blur' }
        ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      // headerObj: {
      //   token: util.getToken()
      // },
      filePostData: {
        sourceType: 'app_icon'
      },
      fileList: [],
      tagList: [],
      tagMap: {},
      tagHasChanged: false
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
    this.getTagList()
    this.query()
  },
  components: {
    LtwAuthImage,
    UploadFile
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    },
    tagSelectVisible() {
      return this.dialogStatus !== 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageSysPrivilegeApplication(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加应用'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      if (this.pageData.total) {
        let lastSorNum = this.pageData.total
        if (lastSorNum) {
          this.formData.sortNum = lastSorNum + 1
        }
      }
    },
    save() {
      this.formData.tagList = []
      if (this.tagHasChanged) {
        if (this.formData.tagIdList) {
          this.formData.tagIdList.forEach(tagId => {
            if (this.tagMap[tagId]) {
              this.formData.tagList.push({
                tagId: tagId
              })
            } else {
              this.formData.tagList.push({
                tagName: tagId,
                billCode: BILL_CODE.APPLICATION
              })
            }
          })
        }
      }
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.formData,
          icon: this.formData.icon?.length && this.formData.icon[0]
        }
        if (this.dialogStatus === 'add') {
          saveSysPrivilegeApplication(postData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateSysPrivilegeApplication(postData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = '修改应用'
      this.dialogStatus = 'edit'
      getSysPrivilegeApplication(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
          this.setTagIdList()
          if (this.formData.icon) {
            this.fileList = [
              {
                id: this.formData.icon,
                url:
                  this.formData.icon &&
                  this.downloadUrl +
                    this.formData.icon +
                    '?token=' +
                    util.getToken()
              }
            ]
          }
        })
        this.dialogVisible = true
      })
    },
    setTagIdList() {
      this.formData.tagIdList = []
      if (this.formData.tagList && this.formData.tagList.length > 0) {
        this.formData.tagList.forEach(tag => {
          this.formData.tagIdList.push(tag.tagId)
        })
      }
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysPrivilegeApplication(param).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    view(row) {
      this.dialogTitle = '查看应用'
      this.dialogStatus = 'view'
      getSysPrivilegeApplication(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
          this.setTagIdList()
          if (this.formData.icon) {
            this.fileList = [
              {
                id: this.formData.icon,
                url:
                  this.formData.icon &&
                  this.downloadUrl +
                    this.formData.icon +
                    '?token=' +
                    util.getToken()
              }
            ]
          }
        })
        this.dialogVisible = true
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {
      this.tagHasChanged = false
      this.getTagList()
    },
    getTagList() {
      listSysTag({ billCode: BILL_CODE.APPLICATION }).then(res => {
        this.tagList = res.data
        if (this.tagList && this.tagList.length > 0) {
          this.tagList.forEach(tag => {
            this.tagMap[tag.id] = tag
          })
        }
      })
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    changeStatus(row) {
      updateSysPrivilegeApplication({
        id: row.id,
        enabled: row.enabled
      }).catch(() => {
        row.enabled = !row.enabled
      })
    },
    //图片预览
    handlePreview() {},
    //图片移除
    handleRemove() {
      this.formData.icon = ''
      // this.fileList.pop()
      // if (file.id) {
      //     deleteFile(file.id).then(() => {
      //         this.fileList.pop()
      //         this.formData.icon = ''
      //     })
      // }
    },
    handleUpload(param) {
      let formData = new FormData()
      formData.append('sourceType', this.filePostData.sourceType) // 额外参数
      formData.append('file', param.file)
      let loading = this.$loading({
        lock: true,
        text: '上传中，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      uploadFile(formData)
        .then(res => {
          this.formData.icon = res.data.id
          this.fileList = []
          this.fileList.push({
            id: this.formData.icon,
            url: this.downloadUrl + this.formData.icon
          })
        })
        .finally(() => loading.close())
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
      this.fileList = []
    },
    handleTagSelectChange() {
      this.tagHasChanged = true
    },
    getIconUrl(row) {
      return (
        row.icon && this.downloadUrl + row.icon + '?token=' + util.getToken()
      )
    }
  }
}
</script>

<style scoped lang="scss">
.tag-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  .el-tag {
    margin-top: 5px;
  }
}
.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .ltw-auth-image {
    width: 60px;
    height: 60px;

    .image-slot {
      font-size: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
    }
  }
}
</style>
