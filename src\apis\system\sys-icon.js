import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysIcon = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons', data, params})
export const batchSaveSysIcon = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons/batch', data, params})
export const updateSysIcon = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons', data, params})
export const deleteSysIcon = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons', params})
export const listSysIcon = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons', params})
export const listSysIconSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons/selections', params})
export const pageSysIcon = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons/page', params})
export const getSysIcon = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_icons/' + id})
