<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    @close="dialogClosed"
    @open="dialogOpened"
    custom-class="filter-task"
  >
    <el-scrollbar>
      <el-form class="form-filter" label-width="100px" :model="form" :rules="formRules" ref="formRef">
        <el-row>
          <el-col :span="20">
            <el-form-item :label="$t('日期')" prop="measureTime">
              <el-date-picker
                v-model="form.measureTime"
                type="datetimerange"
                range-separator="To"
                :start-placeholder="$t('开始日期')"
                :end-placeholder="$t('结束日期')"
                format="YYYY/MM/DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
                @clear="form.measureTime = undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('采集类型')" id="cantonCode" prop="acquisitionType">
              <dictionary-selection
                class="select-input"
                v-model="form.acquisitionType"
                clearable
                dictionaryType="data_acquisition_type"
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('车辆')" prop="vinList">
              <el-select
                class="select-input"
                v-model="form.vinList"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                clearable
              >
                <el-option
                  v-for="item in vehicleList"
                  :key="item.id"
                  :label="item.variant + '-' + item.vin"
                  :value="item.vin"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('停车场类型')" prop="parkingLotTypeList">
              <el-select
                class="select-input"
                v-model="form.parkingLotTypeList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
              >
                <el-option v-for="item in groundLevelList" :key="item.id" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('停车场')" prop="name">
              <ltw-input class="select-input-key" id="key" v-model="form.name" clearable></ltw-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('确认') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import { getFtmParkingLotFieldItems } from '@/apis/fleet/ftm-parking-lot'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'

const defaultform = {}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {},
      groundLevelList: [],
      vehicleList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    DictionarySelection
  },

  methods: {
    async show() {
      this.dialogVisible = true
      this.dialogTitle = this.$t('筛选')
      this.getGroundLevelList()
      this.listVehicle()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          startTime: this.form.measureTime?.length && this.form.measureTime[0],
          endTime: this.form.measureTime?.length && this.form.measureTime[1]
        }
        this.cancel(postData)
      })
    },
    getGroundLevelList() {
      if (!this.groundLevelList?.length) {
        let postData = {
          fieldCode: 'ground_level'
        }
        getFtmParkingLotFieldItems(postData).then(res => {
          this.groundLevelList = res.data
        })
      }
    },
    listVehicle() {
      if(!this.vehicleList?.length){
        listBsVehicle().then(res => {
          this.vehicleList = res.data
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.el-select {
  width: 100%;
}
</style>
