<template>
  <div class="main-container">
    <div class="all-app-container">
      <div class="tag-app-container" :key="tag.id" v-for="tag in tagList">
        <el-divider content-position="left">
          <i :class="tag.tagIconCode"></i>
          {{ tag.tagName }}
        </el-divider>
        <div class="applications-container">
          <application v-bind="application" v-for="application in tagApplicationMap[tag.tagId]" :key="application.id" />
        </div>
      </div>
    </div>
    <div class="welcome-favorites-container">
      <div class="welcome-container">
        <img src="@/assets/images/welcome2.png" class="welcome-img" />
        <div class="welcome-text">
          <div class="welcome-login">
            <span>{{ currentUser.userName }}</span
            >,欢迎您登录
          </div>
          <!--                        <div class="welcome-desc">-->
          <!--                            统一内部门户系统，给您高效便捷的操作体验！-->
          <!--                        </div>-->
        </div>
      </div>
      <div class="favorites-container">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>个人收藏夹</span>
              <el-dropdown trigger="click">
                <span>
                  <i class="el-icon-circle-plus-outline"></i>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item icon="el-icon-folder-add" @click="addDirectory">新增目录 </el-dropdown-item>
                    <el-dropdown-item icon="el-icon-document-add" @click="addFavorite">新增书签 </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <el-tree
            :data="favorites"
            draggable
            @node-drop="handleDrop"
            node-key="id"
            :props="props"
            :expand-on-click-node="false"
            class="custom-tree"
            :allow-drop="allowDrop"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span @click="clickFavorite(data)" class="favorite-node">
                  <i :class="data.asDirectory ? 'el-icon-folder' : 'el-icon-tickets'" />
                  <div>{{ data.name }}</div>
                </span>
                <span class="operation-container">
                  <el-dropdown trigger="click" v-if="data.asDirectory">
                    <span>
                      <el-button type="primary" icon="el-icon-plus" circle></el-button>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item icon="el-icon-folder-add" @click="addDirectory(data)"
                          >新增目录
                        </el-dropdown-item>
                        <el-dropdown-item icon="el-icon-document-add" @click="addFavorite(data)"
                          >新增书签
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button type="warning" icon="el-icon-edit" circle @click="edit(data)"></el-button>
                  <el-button type="danger" icon="el-icon-delete" circle @click="remove(node, data)"></el-button>
                </span>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>
    </div>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="30%" @close="dialogClosed">
      <el-form :model="favoriteForm" :rules="favoriteFormRules" ref="favoriteFormRef" label-width="80px">
        <el-form-item label="名称" prop="name">
          <ltw-input v-model="favoriteForm.name"></ltw-input>
        </el-form-item>
        <el-form-item label="所属目录" prop="parentId">
          <el-cascader
            v-model="favoriteForm.parentId"
            :props="favoriteParentSelectProps"
            placeholder="请选择所属目录"
            :options="directories"
            filterable
            @change="handleChange"
            clearable
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="网址" prop="url" v-if="!operatingDirectory">
          <ltw-input v-model="favoriteForm.url"></ltw-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="save">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import Application from '@/components/system/Application'
import { listSysPrivilegeApplicationOfCurrentUser } from '@/apis/system/sys-privilege-application'
import {
  deleteSysPersonalFavorite,
  saveSysPersonalFavorite,
  treeListSysPersonalFavorite,
  updateSysPersonalFavorite
} from '@/apis/system/sys-personal-favorite'

const defaultFormData = {}
import GLB_CONFIG from '@/plugins/glb-constant'

export default {
  name: 'MyDesk',
  components: { application: Application },
  data() {
    return {
      tagList: [],
      tagApplicationMap: [],
      applications: [],
      directories: [],
      applicationId: GLB_CONFIG.applicationId,
      favoriteParentSelectProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        disabled: 'disabled',
        emitPath: false
      },
      favorites: [],
      props: {
        label: 'name',
        children: 'children',
        isLeaf: 'asLeaf'
      },
      operatingDirectory: false,
      favoriteForm: Object.assign({}, defaultFormData),
      favoriteFormRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        url: [{ required: true, message: '请输入地址', trigger: 'blur' }]
      },
      dialogVisible: false,
      dialogStatus: 'add',
      dialogTitle: '添加目录',
      currentUser: {}
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
    }
    this.currentUser = this.$store.state.permission.currentUser
    listSysPrivilegeApplicationOfCurrentUser({ withTag: true }).then(res => {
      // this.applications = res.data
      if (res.data && res.data.length > 0) {
        let others = []
        res.data.forEach(app => {
          if (app.tagList && app.tagList.length > 0) {
            app.tagList.forEach(appTag => {
              if (!this.tagApplicationMap[appTag.tagId]) {
                this.tagApplicationMap[appTag.tagId] = []
              }
              if (this.tagApplicationMap[appTag.tagId].length === 0) {
                this.tagList.push(appTag)
              }
              this.tagApplicationMap[appTag.tagId].push(app)
            })
          } else {
            others.push(app)
          }
        })
        if (others.length > 0) {
          this.tagList.push({
            tagId: 'other',
            tagName: '其他',
            tagIconCode: 'el-icon-more'
          })
          this.tagApplicationMap['other'] = others
        }
      }
    })
    this.getFavorites()
  },
  methods: {
    getFavorites() {
      treeListSysPersonalFavorite().then(res => {
        this.favorites = res.data
      })
    },
    handleChange() {},
    addDirectory(data) {
      this.operatingDirectory = true
      this.dialogVisible = true
      this.dialogStatus = 'add'
      this.dialogTitle = '添加目录'
      this.favoriteForm.asDirectory = true
      this.favoriteForm.sortNum = 1
      if (this.favorites && this.favorites.length > 0) {
        this.favoriteForm.sortNum = this.favorites.slice(-1)[0].sortNum + 1
      }
      if (data) {
        this.favoriteForm.parentId = data.id
        if (data.children && data.children.length > 0) {
          this.favoriteForm.sortNum = data.children.slice(-1)[0].sortNum + 1
        }
      }
      this.queryParentFavorite()
    },
    addFavorite(data) {
      this.operatingDirectory = false
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.dialogTitle = '添加书签'
      this.favoriteForm.asDirectory = false
      this.favoriteForm.sortNum = 1
      if (this.favorites && this.favorites.length > 0) {
        this.favoriteForm.sortNum = this.favorites.slice(-1)[0].sortNum + 1
      }
      if (data) {
        this.favoriteForm.parentId = data.id
        if (data.children && data.children.length > 0) {
          this.favoriteForm.sortNum = data.children.slice(-1)[0].sortNum + 1
        }
      }
      this.queryParentFavorite()
    },
    edit(data) {
      this.dialogVisible = true
      this.dialogStatus = 'edit'
      this.operatingDirectory = data.asDirectory
      if (data.asDirectory) {
        this.dialogTitle = '重命名目录'
      } else {
        this.dialogTitle = '修改书签'
      }
      this.$nextTick(function () {
        for (let dataKey in data) {
          this.favoriteForm[dataKey] = data[dataKey]
        }
        this.queryParentFavorite({ currentSelectedId: data.id })
      })
    },
    queryParentFavorite(param) {
      treeListSysPersonalFavorite(Object.assign({}, param, { asDirectory: true })).then(res => {
        this.directories = res.data
      })
    },
    save() {
      this.$refs.favoriteFormRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveSysPersonalFavorite(this.favoriteForm).then(() => {
            this.dialogVisible = false
            this.getFavorites()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateSysPersonalFavorite(this.favoriteForm).then(() => {
            this.dialogVisible = false
            this.getFavorites()
          })
        }
      })
    },
    dialogClosed() {
      this.$refs.favoriteFormRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    remove(node, data) {
      let msg = '此操作将永久删除该书签, 是否继续?'
      if (data.asDirectory) {
        msg = '此操作将永久删除该目录及该目录下的书签, 是否继续?'
      }
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysPersonalFavorite({ id: data.id, asDirectory: data.asDirectory }).then(() => {
            this.getFavorites()
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    clickFavorite(data) {
      if (data.url) {
        window.open(data.url)
      }
    },
    allowDrop(draggingNode, dropNode, type) {
      if (!dropNode.data.asDirectory) {
        return type !== 'inner'
      }
      return true
    },
    handleDrop(draggingNode, dropNode, dropType) {
      let data = {}
      switch (dropType) {
        case 'inner':
          data.id = draggingNode.data.id
          data.parentId = dropNode.data.id
          if (dropNode.data.children && dropNode.data.children.length > 0) {
            let lastNode = dropNode.data.children.slice(-1)[0]
            data.sortNum = lastNode.sortNum + 1
          }
          break
        case 'before':
          data.id = draggingNode.data.id
          if (draggingNode.data.parentId !== dropNode.data.parentId) {
            data.parentId = dropNode.data.parentId
          }
          data.sortNum = dropNode.data.sortNum - 1
          break
        case 'after':
          data.id = draggingNode.data.id
          if (draggingNode.data.parentId !== dropNode.data.parentId) {
            data.parentId = dropNode.data.parentId
          }
          data.sortNum = dropNode.data.sortNum + 1
      }
      updateSysPersonalFavorite(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.favorites-container {
  .card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    i {
      font-size: 28px;
      cursor: pointer;
    }
  }

  .custom-tree {
    :deep .el-tree-node__content {
      height: (auto);
      padding-bottom: 5px;

      .custom-tree-node {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        font-size: 15px;
        line-height: 25px;

        .favorite-node {
          display: flex;
          flex-direction: row;
          align-items: center;

          i {
            margin-right: 5px;
          }

          div {
            white-space: pre-wrap;
            max-width: 150px;
            width: 80%;
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-all;
          }
        }

        .operation-container {
          display: flex;
          flex-direction: row;
          align-items: center;

          .el-dropdown {
            margin-right: 10px;
          }
        }
      }
    }
  }
}

.el-footer {
  background-color: #b3c0d1;
  color: #333;
  /*text-align: center;*/
  /*line-height: 60px;*/
}

.main-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .all-app-container {
    display: flex;
    flex-direction: column;
    width: 68%;

    .tag-app-container {
      display: flex;
      flex-direction: column;

      .el-divider {
        width: 50%;
      }

      .applications-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
      }
    }
  }

  .welcome-favorites-container {
    /*width: 609px;*/
    width: 32%;

    .welcome-container {
      width: 100%;
      height: 120px;
      background: #ffffff;
      box-shadow: 0px 2px 12px rgba(47, 51, 71, 0.2);
      opacity: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 50px;

      .welcome-img {
        width: 50%;
        /*height: 240px;*/
        transform: translate(0, -5.3%);
      }

      .welcome-text {
        .welcome-login {
          span {
            color: #3b63d4;
          }

          font-size: 20px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          line-height: 34px;
          color: #333333;
        }

        .welcome-desc {
          height: 20px;
          font-size: 14px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          line-height: 24px;
          color: #999999;
          opacity: 1;
        }
      }
    }

    .favorites-container {
      width: 100%;
    }
  }
}

.el-main {
  padding: 100px;
  color: #333;
  /*text-align: center;*/
  /*line-height: 60px;*/
}
</style>
