import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleDbc = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs', data, params })
export const updateFtmVehicleDbc = (data = {}, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs', data, params })
export const deleteFtmVehicleDbc = (params = {}) =>
  httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs', params })
export const listFtmVehicleDbc = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs', params })
export const listFtmVehicleDbcTimeLineData = (params = {}) =>
    httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs/listFtmVehicleDbcTimeLineData', params })
export const listFtmVehicleDbcSelection = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs/selections', params })
export const pageFtmVehicleDbc = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs/page', params })
export const getFtmVehicleDbc = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_dbcs/' + id })
