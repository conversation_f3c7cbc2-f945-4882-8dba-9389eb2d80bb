<template>
  <el-drawer
    v-model="visible"
    title="选择配置文件"
    size="50%"
    direction="rtl"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="jfrog-file-selector">
      <!-- Properties 查询区域 -->
      <div class="properties-section">
        <div class="section-header">
          <div class="header-actions">
            <el-button
              type="primary"
              @click="addProperty"
            >
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              添加条件
            </el-button>
            <el-button 
              type="success" 
              @click="searchFiles"
              :loading="searching"
            >
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
              查询
            </el-button>
          </div>
        </div>

        <!-- Properties 列表 -->
        <div class="properties-list">
          <div
            v-for="(property, index) in properties"
            :key="index"
            class="property-item"
          >
            <el-select
              v-model="property.key"
              placeholder="选择属性"
              style="width: 200px"
              filterable
              allow-create
            >
              <el-option
                v-for="option in propertyKeyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-input
              v-model="property.value"
              placeholder="输入属性值"
              style="width: 300px"
            />
            <el-button
              type="danger"
              style="margin-left:12px"
              @click="removeProperty(index)"
            >
            <ltw-icon icon-code="el-icon-delete"></ltw-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 文件列表区域 -->
      <div class="file-list-section">

        <div v-if="searching" class="loading-section">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>正在查询文件...</p>
        </div>

        <div v-else-if="fileList.length === 0" class="empty-section">
          <el-icon class="empty-icon"><Document /></el-icon>
          <p>{{ hasSearched ? '未找到匹配的文件' : '请设置查询条件并查询' }}</p>
        </div>

        <div v-else class="file-table-container">
          <el-table
            :data="fileList"
            style="width: 100%;"
            @row-click="handleRowClick"
            highlight-current-row
          >
            <!-- 单选复选框列 -->
            <el-table-column width="50" align="center">
              <template #default="{ row }">
                <el-radio
                  v-model="selectedFileId"
                  :label="row.actualMd5"
                  @change="selectFile(row)"
                >
                  <span></span>
                </el-radio>
              </template>
            </el-table-column>

            <!-- 文件名列 -->
            <el-table-column prop="name" label="文件名" min-width="500" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="file-name-cell">
                  <span class="file-name">{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>

            <!-- 文件路径列 -->
            <!-- <el-table-column prop="path" label="文件路径" min-width="100" show-overflow-tooltip>
            </el-table-column> -->

            <!-- 文件大小列 -->
            <el-table-column prop="size" label="文件大小" width="100" align="right" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.size ? formatFileSize(row.size) : '-' }}
              </template>
            </el-table-column>

            <!-- 仓库列
            <el-table-column prop="repo" label="仓库" width="120" show-overflow-tooltip>
            </el-table-column> -->

            <!-- 修改时间列 -->
            <el-table-column prop="modified" label="修改时间" width="160">
              <template #default="{ row }">
                {{ row.modified || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页组件 -->
          <div class="pagination-container">
             <el-pagination
               background
              :current-page="currentPage"
               :page-size="pageSize"
               :page-sizes="[10, 20, 50, 100]"
               :total="total"
               layout="total, sizes, prev, pager, next, jumper"
               @size-change="handleSizeChange"
               @current-change="handleCurrentChange"
             />
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="confirmSelection"
          :disabled="!selectedFile"
        >
          确定选择
        </el-button>
      </div>
    </div>




  </el-drawer>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, 
  Search, 
  Close, 
  Loading, 
  Document, 
  Check 
} from '@element-plus/icons-vue'
import { searchZipPackagesByProperties } from '@/apis/fleet/whitelist-management'
import { listSysDictionary } from '@/apis/system/sys-dictionary'

export default {
  name: 'JFrogFileSelector',
  components: {
    Plus,
    Search,
    Close,
    Loading,
    Document,
    Check
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    defaultProperties: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'file-selected'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })

    // 查询属性
    const properties = ref([])
    const searching = ref(false)
    const hasSearched = ref(false)
    
        // 文件列表
    const fileList = ref([])
    const selectedFile = ref(null)
    const selectedFileId = ref('')
    
    // 分页相关
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    
    // 属性键选项
    const propertyKeyOptions = ref([])

    // 加载属性键选项
    const loadPropertyKeyOptions = async () => {
      try {
        const response = await listSysDictionary({
          typeCode: 'zip_package_properties_type'
        })

        if (response.data) {
          // 格式化显示response.data的结构
          console.log('response.data格式化:', JSON.stringify(response.data, null, 2))

          propertyKeyOptions.value = response.data.map(item => ({
            label: item.name,
            value: item.code
          }))
        }
      } catch (error) {
        console.error('加载属性键选项失败:', error)
      }
    }

    // 初始化默认属性
    const initializeProperties = () => {
      // 默认添加一个空的查询条件
      properties.value = [{ key: '', value: '' }]

      // 如果有传入的默认属性，则使用传入的属性
      Object.entries(props.defaultProperties).forEach(([key, value]) => {
        if (value) {
          properties.value.push({ key, value })
        }
      })
    }

    // 添加属性
    const addProperty = () => {
      properties.value.push({
        key: '',
        value: ''
      })
    }

    // 移除属性
    const removeProperty = (index) => {
      properties.value.splice(index, 1)
    }

    // 搜索文件
    const searchFiles = async () => {
      // if (properties.value.length === 0) {
      //   ElMessage.warning('请至少添加一个查询条件')
      //   return
      // }

      searching.value = true
      hasSearched.value = true
      
      try {
        // 构建查询参数
        const searchParams = {}
        properties.value.forEach(prop => {
          if (prop.key && prop.value) {
            searchParams[prop.key] = prop.value
          }
        })
        
        // 添加分页参数
        searchParams.page = currentPage.value
        searchParams.size = pageSize.value

        //const response = await searchZipPackagesByProperties(searchParams)
        const response = {
          code: 200,
    "data": [
        {
            "actualMd5": "a06c76c25eba4ca35517cf81a0aa5868",
            "actualSha1": "341a6057403dcb913f0b4edf2297e8cf3c630689",
            "created": "2025-05-20 12:13:25",
            "modified": "2025-05-20 12:13:19",
            "name": "J6E-ASW-0506_viper_test_root-202505201119CST-Jetour_D01_J6E-linux_arm_stripped.zip",
            "path": "ASW/J6E-ASW-0506_viper_test_root-202505201119CST/variant",
            "properties": [
                {
                    "key": "branch_name",
                    "value": "0506_viper_test_root"
                },
                {
                    "key": "os_name",
                    "value": "linux_arm"
                },
                {
                    "key": "aos_runtime_prod",
                    "value": "aos_runtime_prod/2.8.0@aos/release"
                },
                {
                    "key": "variant",
                    "value": "Jetour_D01_J6E-linux_arm"
                },
                {
                    "key": "arch_interface_version",
                    "value": "Unify_SWArch_Release_8.2.2_TEMP_FOR_VIPER_POC"
                },
                {
                    "key": "stripped",
                    "value": "true"
                },
                {
                    "key": "root_commitid",
                    "value": "cd6365479"
                },
                {
                    "key": "with_tag",
                    "value": "true"
                },
                {
                    "key": "aos_runtime_dev",
                    "value": "aos_runtime_dev/2.8.0@aos/release"
                },
                {
                    "key": "time_stamp",
                    "value": "202505201119CST"
                },
                {
                    "key": "aos_tools",
                    "value": "aos_tools/2.8.0@aos/release"
                },
                {
                    "key": "aos_dev_tools",
                    "value": "aos_dev_tools/2.8.0@aos/release"
                },
                {
                    "key": "project_config_name",
                    "value": "jetour_d01"
                },
                {
                    "key": "vehicle_platform",
                    "value": "Jetour_D01_J6E"
                },
                {
                    "key": "aos_build_tools",
                    "value": "aos_build_tools/2.8.0@aos/release"
                },
                {
                    "key": "internal_build_id",
                    "value": "J6E-ASW-0506_viper_test_root-202505201119CST"
                },
                {
                    "key": "pr_id",
                    "value": "NONE"
                },
                {
                    "key": "build_type",
                    "value": "Integrate"
                }
            ],
            "repo": "pj_j6e_release_generic_local",
            "size": 752090387,
            "type": "FILE"
        },
        {
            "actualMd5": "58fad33e3185bd3902bae23eb5bc1840",
            "actualSha1": "149d84d5fe5e63b4ab2b1c194ac1993477419256",
            "created": "2025-05-20 12:50:38",
            "modified": "2025-05-20 12:49:53",
            "name": "J6E-ASW-0506_viper_test_root-202505201119CST-Jetour_D01_PC-linux_x86.zip",
            "path": "ASW/J6E-ASW-0506_viper_test_root-202505201119CST/variant",
            "properties": [
                {
                    "key": "aos_dev_tools",
                    "value": "aos_dev_tools/2.8.0@aos/release"
                },
                {
                    "key": "pr_id",
                    "value": "NONE"
                },
                {
                    "key": "root_commitid",
                    "value": "cd6365479"
                },
                {
                    "key": "aos_tools",
                    "value": "aos_tools/2.8.0@aos/release"
                },
                {
                    "key": "os_name",
                    "value": "linux_x86"
                },
                {
                    "key": "project_config_name",
                    "value": "jetour_d01"
                },
                {
                    "key": "with_tag",
                    "value": "true"
                },
                {
                    "key": "internal_build_id",
                    "value": "J6E-ASW-0506_viper_test_root-202505201119CST"
                },
                {
                    "key": "aos_runtime_dev",
                    "value": "aos_runtime_dev/2.8.0@aos/release"
                },
                {
                    "key": "aos_runtime_prod",
                    "value": "aos_runtime_prod/2.8.0@aos/release"
                },
                {
                    "key": "time_stamp",
                    "value": "202505201119CST"
                },
                {
                    "key": "build_type",
                    "value": "Integrate"
                },
                {
                    "key": "vehicle_platform",
                    "value": "Jetour_D01_PC"
                },
                {
                    "key": "arch_interface_version",
                    "value": "Unify_SWArch_Release_8.2.2_TEMP_FOR_VIPER_POC"
                },
                {
                    "key": "aos_build_tools",
                    "value": "aos_build_tools/2.8.0@aos/release"
                },
                {
                    "key": "branch_name",
                    "value": "0506_viper_test_root"
                },
                {
                    "key": "variant",
                    "value": "Jetour_D01_PC-linux_x86"
                }
            ],
            "repo": "pj_j6e_release_generic_local",
            "size": 6792661874,
            "type": "FILE"
        },
        {
            "actualMd5": "d94e7679c6fc81fa8cc0c6d37ba1d221",
            "actualSha1": "6f3a2e7aa8515b80a52786aa208d69cdefb36998",
            "created": "2025-05-20 12:50:59",
            "modified": "2025-05-20 12:50:59",
            "name": "J6E-ASW-0506_viper_test_root-202505201119CST-cd6365479-ROS_Convert_full-Jetour_D01_PC-linux_x86.zip",
            "path": "ASW/J6E-ASW-0506_viper_test_root-202505201119CST/variant",
            "properties": [
                {
                    "key": "vehicle_platform",
                    "value": "Jetour_D01_PC"
                },
                {
                    "key": "aos_runtime_dev",
                    "value": "aos_runtime_dev/2.8.0@aos/release"
                },
                {
                    "key": "branch_name",
                    "value": "0506_viper_test_root"
                },
                {
                    "key": "pr_id",
                    "value": "NONE"
                },
                {
                    "key": "build_type",
                    "value": "Integrate"
                },
                {
                    "key": "aos_dev_tools",
                    "value": "aos_dev_tools/2.8.0@aos/release"
                },
                {
                    "key": "ros_convert_full",
                    "value": "true"
                },
                {
                    "key": "aos_build_tools",
                    "value": "aos_build_tools/2.8.0@aos/release"
                },
                {
                    "key": "root_commitid",
                    "value": "cd6365479"
                },
                {
                    "key": "internal_build_id",
                    "value": "J6E-ASW-0506_viper_test_root-202505201119CST"
                },
                {
                    "key": "aos_tools",
                    "value": "aos_tools/2.8.0@aos/release"
                },
                {
                    "key": "os_name",
                    "value": "linux_x86"
                },
                {
                    "key": "time_stamp",
                    "value": "202505201119CST"
                },
                {
                    "key": "aos_runtime_prod",
                    "value": "aos_runtime_prod/2.8.0@aos/release"
                },
                {
                    "key": "variant",
                    "value": "Jetour_D01_PC-linux_x86"
                },
                {
                    "key": "project_config_name",
                    "value": "jetour_d01"
                },
                {
                    "key": "arch_interface_version",
                    "value": "Unify_SWArch_Release_8.2.2_TEMP_FOR_VIPER_POC"
                },
                {
                    "key": "with_tag",
                    "value": "true"
                }
            ],
            "repo": "pj_j6e_release_generic_local",
            "size": 57134485,
            "type": "FILE"
        },
        {
            "actualMd5": "d88f23f4ee02420622616cc5f200b4cc",
            "actualSha1": "032e2169d61cc24a7bba570cd2756743eed929d1",
            "created": "2025-05-20 12:11:45",
            "modified": "2025-05-20 12:11:19",
            "name": "J6E-ASW-0506_viper_test_root-202505201119CST-Jetour_D01_J6E-linux_arm.zip",
            "path": "ASW/J6E-ASW-0506_viper_test_root-202505201119CST/variant",
            "properties": [
                {
                    "key": "build_type",
                    "value": "Integrate"
                },
                {
                    "key": "variant",
                    "value": "Jetour_D01_J6E-linux_arm"
                },
                {
                    "key": "time_stamp",
                    "value": "202505201119CST"
                },
                {
                    "key": "aos_build_tools",
                    "value": "aos_build_tools/2.8.0@aos/release"
                },
                {
                    "key": "root_commitid",
                    "value": "cd6365479"
                },
                {
                    "key": "pr_id",
                    "value": "NONE"
                },
                {
                    "key": "aos_dev_tools",
                    "value": "aos_dev_tools/2.8.0@aos/release"
                },
                {
                    "key": "aos_runtime_prod",
                    "value": "aos_runtime_prod/2.8.0@aos/release"
                },
                {
                    "key": "vehicle_platform",
                    "value": "Jetour_D01_J6E"
                },
                {
                    "key": "with_tag",
                    "value": "true"
                },
                {
                    "key": "branch_name",
                    "value": "0506_viper_test_root"
                },
                {
                    "key": "aos_runtime_dev",
                    "value": "aos_runtime_dev/2.8.0@aos/release"
                },
                {
                    "key": "internal_build_id",
                    "value": "J6E-ASW-0506_viper_test_root-202505201119CST"
                },
                {
                    "key": "os_name",
                    "value": "linux_arm"
                },
                {
                    "key": "arch_interface_version",
                    "value": "Unify_SWArch_Release_8.2.2_TEMP_FOR_VIPER_POC"
                },
                {
                    "key": "aos_tools",
                    "value": "aos_tools/2.8.0@aos/release"
                },
                {
                    "key": "project_config_name",
                    "value": "jetour_d01"
                }
            ],
            "repo": "pj_j6e_release_generic_local",
            "size": 4080856174,
            "type": "FILE"
        },
        {
            "actualMd5": "d485287cbe50cd216a19476c00f6d11d",
            "actualSha1": "27a20f39a913f8b111ceee2ab3ef22785380a897",
            "created": "2025-05-20 12:50:58",
            "modified": "2025-05-20 12:50:57",
            "name": "J6E-ASW-0506_viper_test_root-202505201119CST-cd6365479-ROS_Convert-Jetour_D01_PC-linux_x86.zip",
            "path": "ASW/J6E-ASW-0506_viper_test_root-202505201119CST/variant",
            "properties": [
                {
                    "key": "aos_dev_tools",
                    "value": "aos_dev_tools/2.8.0@aos/release"
                },
                {
                    "key": "aos_build_tools",
                    "value": "aos_build_tools/2.8.0@aos/release"
                },
                {
                    "key": "root_commitid",
                    "value": "cd6365479"
                },
                {
                    "key": "time_stamp",
                    "value": "202505201119CST"
                },
                {
                    "key": "aos_tools",
                    "value": "aos_tools/2.8.0@aos/release"
                },
                {
                    "key": "with_tag",
                    "value": "true"
                },
                {
                    "key": "ros_convert",
                    "value": "true"
                },
                {
                    "key": "os_name",
                    "value": "linux_x86"
                },
                {
                    "key": "branch_name",
                    "value": "0506_viper_test_root"
                },
                {
                    "key": "variant",
                    "value": "Jetour_D01_PC-linux_x86"
                },
                {
                    "key": "aos_runtime_prod",
                    "value": "aos_runtime_prod/2.8.0@aos/release"
                },
                {
                    "key": "aos_runtime_dev",
                    "value": "aos_runtime_dev/2.8.0@aos/release"
                },
                {
                    "key": "vehicle_platform",
                    "value": "Jetour_D01_PC"
                },
                {
                    "key": "pr_id",
                    "value": "NONE"
                },
                {
                    "key": "internal_build_id",
                    "value": "J6E-ASW-0506_viper_test_root-202505201119CST"
                },
                {
                    "key": "project_config_name",
                    "value": "jetour_d01"
                },
                {
                    "key": "build_type",
                    "value": "Integrate"
                },
                {
                    "key": "arch_interface_version",
                    "value": "Unify_SWArch_Release_8.2.2_TEMP_FOR_VIPER_POC"
                }
            ],
            "repo": "pj_j6e_release_generic_local",
            "size": 9839195,
            "type": "FILE"
        }
    ],
    "status": 1,
    "success": true
}
        // 使用分析函数来格式化显示数据
        analyzeJsonStructure(response, 'Response')
        analyzeJsonStructure(response.data, 'Response.data')

        if (response.code === 200) {
          fileList.value = response.data || []
          // 从接口响应中获取总数，如果没有则使用当前数据长度
          total.value = response.total || response.data?.length || 0
          selectedFile.value = null
          selectedFileId.value = ''
        } else {
       //   ElMessage.error(response.message || '查询失败')
          fileList.value = []
        }
      } catch (error) {
        console.error('搜索文件失败:', error)
     //   ElMessage.error('搜索文件失败')
        fileList.value = []
      } finally {
        searching.value = false
      }
    }

    // 选择文件
    const selectFile = (file) => {
      selectedFile.value = file
      selectedFileId.value = file.actualMd5
    }

    // 处理行点击
    const handleRowClick = (row) => {
      selectFile(row)
    }
    
    // 处理分页大小变化
    const handleSizeChange = (newSize) => {
      pageSize.value = newSize
      currentPage.value = 1 // 重置到第一页
      searchFiles() // 重新搜索
    }
    
    // 处理当前页变化
    const handleCurrentChange = (newPage) => {
      currentPage.value = newPage
      searchFiles() // 重新搜索
    }

    // 确认选择
    const confirmSelection = () => {
      if (selectedFile.value) {
        emit('file-selected', selectedFile.value)
        handleClose()
      }
    }

    // 关闭对话框
    const handleClose = () => {
      visible.value = false
      // 重置状态
      properties.value = []
      fileList.value = []
      selectedFile.value = null
      selectedFileId.value = ''
      currentPage.value = 1
      pageSize.value = 20
      total.value = 0
      hasSearched.value = false
    }

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (!bytes) return ''
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    }

    // 分析JSON数据结构
    const analyzeJsonStructure = (data, name = 'data') => {
      console.log(`\n=== ${name} 数据结构分析 ===`)
      console.log('数据类型:', typeof data)

      if (Array.isArray(data)) {
        console.log('数组长度:', data.length)
        if (data.length > 0) {
          console.log('第一个元素结构:')
          console.log(JSON.stringify(data[0], null, 2))
          console.log('第一个元素的属性:', Object.keys(data[0]))
        }
      } else if (typeof data === 'object' && data !== null) {
        console.log('对象属性:', Object.keys(data))
        console.log('完整对象:')
        console.log(JSON.stringify(data, null, 2))
      }
    }



    // 监听 visible 变化
    watch(() => visible.value, (newValue) => {
      if (newValue) {
        loadPropertyKeyOptions()
        initializeProperties()
      }
    }, { immediate: true })

    return {
      visible,
      properties,
      searching,
      hasSearched,
      fileList,
      selectedFile,
      selectedFileId,
      currentPage,
      pageSize,
      total,
      propertyKeyOptions,
      addProperty,
      removeProperty,
      searchFiles,
      selectFile,
      handleRowClick,
      handleSizeChange,
      handleCurrentChange,
      confirmSelection,
      handleClose,
      formatFileSize,
      analyzeJsonStructure
    }
  }
}
</script>

<style lang="scss" scoped>
.jfrog-file-selector {
  height: 100%;
  display: flex;
  flex-direction: column;

  .properties-section {
    margin-bottom: 24px;
    flex-shrink: 0;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .properties-list {
      .property-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .equals {
          font-weight: bold;
          color: #606266;
        }
      }
    }
  }
  
  .file-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .section-header {
      margin-bottom: 16px;
      flex-shrink: 0;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .loading-section,
    .empty-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #909399;
      
      .loading-icon,
      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      .loading-icon {
        animation: rotate 2s linear infinite;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
    
             .file-table-container {
           flex: 1;
           display: flex;
           flex-direction: column;
           
           .pagination-container {
             padding: 16px 0;
             display: flex;
             border-top: 1px solid #e4e7ed;
           }

      :deep(.el-table) {
        // flex: 1;

        .el-table__body-wrapper {
          max-height: calc(100vh - 350px);
          overflow-y: auto;
        }

        // 单选按钮样式
        .el-radio {
          .el-radio__input {
            .el-radio__inner {
              width: 16px;
              height: 16px;
            }
          }

          .el-radio__label {
            display: none;
          }
        }

        // 文件名单元格样式
        .file-name-cell {
          display: flex;
          align-items: center;
          gap: 8px;

          .file-icon {
            color: #409eff;
            font-size: 16px;
          }

          .file-name {
            font-weight: 500;
          }
        }

        // 行悬停效果
        .el-table__row {
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        // 当前选中行样式
        .current-row {
          background-color: #ecf5ff !important;
        }
      }
    }
  }

  .drawer-footer {
    padding: 16px 0;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    flex-shrink: 0;
    margin-top: auto;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
