<template>
  <div class="fleets-trajectory">
    <el-card class="fleet-list">
      <template #header>
        <div class="card-header">
          <div class="search-content">
            <ltw-input
              :placeholder="$t('通过车辆识别代码查询')"
              v-model="queryParam.key"
              clearable
              @clear="refresh"
              class="search-key"
            >
              <template #append>
                <el-button @click="refresh" id="el-icon-search">
                  <ltw-icon icon-code="el-icon-search"></ltw-icon>
                </el-button>
              </template>
            </ltw-input>
            <!--            <el-button-->
            <!--              :type="item.buttonStyleType"-->
            <!--              :key="item.id"-->
            <!--              v-for="item in outlineFunctionList"-->
            <!--              @click="executeButtonMethod(item.buttonCode)"-->
            <!--              :id="item.buttonIconCode"-->
            <!--            >-->
            <!--              <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>-->
            <!--              {{ $t(item.name) }}-->
            <!--            </el-button>-->
            <!--            <el-dropdown-->
            <!--                @command="handleCommand"-->
            <!--                style="margin-left: 10px"-->
            <!--                class="batch-operate-btn"-->
            <!--                v-if="batchingFunctionList && batchingFunctionList.length > 0"-->
            <!--            >-->
            <!--              <el-button id="batchOperateBtn" type="primary">-->
            <!--                {{ $t('批量操作') }}-->
            <!--                <ltw-icon icon-code="el-icon-arrow-down" class="el-icon&#45;&#45;right"></ltw-icon>-->
            <!--              </el-button>-->
            <!--              <template #dropdown>-->
            <!--                <el-dropdown-menu>-->
            <!--                  <el-dropdown-item-->
            <!--                      :key="item.id"-->
            <!--                      v-for="item in batchingFunctionList"-->
            <!--                      :command="item.buttonCode"-->
            <!--                      :id="item.buttonCode"-->
            <!--                  >-->
            <!--                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>-->
            <!--                    {{ $t(item.name) }}-->
            <!--                  </el-dropdown-item>-->
            <!--                </el-dropdown-menu>-->
            <!--              </template>-->
            <!--            </el-dropdown>-->
          </div>
        </div>
      </template>
      <!-- <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container"></div>
        <div class="ltw-tool-container button-group">
        </div>
      </div> -->
      <div class="table-content">
        <el-radio-group @change="filterPageData" v-model="queryParam.useType">
          <el-radio-button v-for="item in useTypeList" :label="item.code" :key="item.code"
            >{{ item.name }}
          </el-radio-button>
        </el-radio-group>
        <div class="vehicle-list">
          <el-scrollbar>
            <template v-for="item in pageData.records" :key="item.id">
              <vehicle-card
                page-type="map"
                ref="VehicleCard"
                :item="item"
                :customFunctionList="customFunctionList"
                :inlineFunctionList="inlineFunctionList"
                :vehicleStatuses="vehicleStatuses"
                type="edit"
                @reload="query"
                @getVehiclePosition="getVehiclePosition"
              />
            </template>
          </el-scrollbar>
        </div>
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParam.current"
          :page-sizes="[4, 12, 24, 36]"
          :page-size="queryParam.size"
          layout="total, sizes, prev, next, jumper"
          :total="pageData.total"
        >
        </el-pagination>
      </div>
    </el-card>
    <div class="map-content">
      <tx-map-overview ref="TXMapOverview" @clickMarker="clickMarker" />

      <transition name="el-fade-in-linear">
        <el-card v-show="mapType === 'trajectory'" class="fleet-detail filter-fleet-detail">
          <el-date-picker
            clearable
            v-model="filterVehicleTrajectoryForm.dateRange"
            type="datetimerange"
            :range-separator="$t('到')"
            :start-placeholder="$t('开始日期')"
            :end-placeholder="$t('结束日期')"
            value-format="YYYY-MM-DD HH:mm:ss"
            popper-class="dateRange"
          />
          <el-button class="filter-search" @click="filterMapTrajectory">
            <ltw-icon icon-code="el-icon-search"></ltw-icon>
          </el-button>
        </el-card>
      </transition>
      <transition name="el-fade-in-linear">
        <el-card v-show="currentVehicleVisible" class="fleet-detail">
          <div class="header">
            <span class="tags">
              <span class="title" v-show="currentVehicle.vin">{{ currentVehicle.vin }}</span>
              <span class="title external-vin" v-show="currentVehicle.speed"
                >{{ parseFloatNumber(currentVehicle.speed * 3.6) }}km/h</span
              ></span
            >
            <el-tooltip :content="mapType === 'position' ? '轨迹' : '位置'">
              <el-button plain type="primary" size="small" circle @click="changeMapType">
                <ltw-icon v-if="mapType === 'position'" icon-code="el-icon-guide" />
                <ltw-icon v-else-if="mapType === 'trajectory'" icon-code="el-icon-location" />
              </el-button>
            </el-tooltip>
          </div>
          <div class="vehicle-location-name">
            {{ currentVehicle.locationName }}
          </div>
          <div class="vehicle-time">
            {{ currentVehicle.createTime }}
          </div>
          <div class="vehicle-remark" v-show="currentVehicle.latitude && currentVehicle.longitude">
            {{ currentVehicle.latitude + ' , ' + currentVehicle.longitude }}
          </div>
        </el-card>
      </transition>
    </div>
  </div>
  <import-competitor ref="ImportCompetitorRef" />
  <export-competitor ref="ExportCompetitorRef" />
  <!--  <filter-vehicle-trajectory ref="FilterVehicleTrajectory" @reload="filterMapTrajectory" />-->
</template>

<script>
import md5 from 'js-md5'
import GLB_CONFIG from '@/plugins/glb-constant'
import util, { dateUtils, debounce, showToast } from '@/plugins/util'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { pageBsVehicle, getVehicleGpsLatest, getVehicleGpsList } from '@/apis/fleet/bs-vehicle'
import VehicleCard from '@/pages/fleet/components/VehicleCard.vue'
import ImportCompetitor from '@/pages/fleet/dialog/ImportCompetitor.vue'
import ExportCompetitor from '@/pages/fleet/dialog/ExportCompetitor.vue'
import FilterVehicleTrajectory from '@/pages/fleet/dialog/FilterVehicleTrajectory.vue'
import TXMapOverview from '@/components/map/TXMapOverview'
import { jsonp } from 'vue-jsonp'
import LtwIcon from '@/components/base/LtwIcon.vue'
import Convertor from '@/plugins/map/convertor'

export default {
  components: {
    LtwIcon,
    VehicleCard,
    ImportCompetitor,
    ExportCompetitor,
    'tx-map-overview': TXMapOverview,
    FilterVehicleTrajectory
  },
  name: 'BsVehicle',
  data() {
    return {
      loading: true,
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      customFunctionList: [],
      pageData: {
        records: [],
        total: 0
      },
      queryParam: {
        current: 1,
        size: 4
      },
      splitNum: 3,
      vehicleStatuses: [],
      useTypeList: [],
      currentVehicle: {},
      currentVehicleVisible: false,
      mapType: 'position',
      markers: [],
      convertor: '',
      filterVehicleTrajectoryVisible: false,
      filterVehicleTrajectoryForm: {}
    }
  },
  created() {
    const routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.customFunctionList = routerFunctionMap.customFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.getVehicleGpsLatest()
    this.getUseType()
    // this.query()
    // this.listEquipment()
    this.listStatus()
  },
  // computed: {
  //   formReadonly() {
  //     return this.dialogStatus === 'view'
  //   }
  // },
  methods: {
    handleCommand(command) {
      if (command === 'batchCompetitorImport') {
        this.batchCompetitorImport()
      } else if (command === 'batchCompetitorExport') {
        this.batchCompetitorExport()
      }
    },
    batchCompetitorImport() {
      this.$refs.ImportCompetitorRef.show()
    },
    batchCompetitorExport() {
      this.$refs.ExportCompetitorRef.show()
    },
    listStatus() {
      listSysDictionary({ typeCode: 'bs_vehicle_status' }).then(res => {
        this.vehicleStatuses = res.data
      })
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      // this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      this.loading = false
      pageBsVehicle(this.queryParam).then(res => {
        this.pageData = res.data
        this.loading = true
        // this.$refs.TXMapOverview.show()
      })
    },
    add() {
      this.$router.push({
        path: '/fleet/AddFleetManagement'
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    getUseType() {
      listSysDictionary({ typeCode: 'use_type' }).then(res => {
        this.useTypeList = res.data
        res.data.unshift({ name: '全部', value: '' })
        if (res.data?.length) {
          this.queryParam.useType = res.data[0].code

          this.query()
        }
      })
    },
    filterPageData() {
      this.query()
    },
    getVehicleGpsLatest() {
      getVehicleGpsLatest().then(res => {
        this.markers = res.data
        this.$refs.TXMapOverview.show({ list: res.data })
      })
    },
    getVehiclePosition(data) {
      // this.currentVehicle = data.row
      this.currentVehicle.vin = data.row.vin
      this.mapType = data.type
      if (data.type === 'position') {
        let marker = this.markers.find(val => val.vin === data.row.vin)
        if (marker.latitude && marker.longitude) {
          this.mapType = 'position'
          this.$refs.TXMapOverview.resetMarkers(this.markers)
          this.$refs.TXMapOverview.getMapCenter(marker)
          this.showVehicleDetail(marker)
          this.getLocationName()
        } else {
          showToast('该车暂无定位点信息', 'warning')
        }
      } else {
        this.showFilter()
        // })
      }
    },
    getMapTrajectory(params) {
      let postData = {
        vin: this.currentVehicle.vin,
        ...params
      }
      getVehicleGpsList(postData).then(res => {
        this.mapType = 'trajectory'
        this.$refs.TXMapOverview.goDrawRoute(res.data || [])
        let marker = this.markers.find(val => val.vin === this.currentVehicle.vin)
        if (marker.latitude && marker.longitude) {
          this.showVehicleDetail(marker)
          this.getLocationName()
        }
        if (res.data?.length) {
          this.$refs.TXMapOverview.getMapCenter(res.data[0])
          // this.currentVehicle = res.data[res.data?.length - 1]
        } else {
          showToast('该时间段暂无轨迹信息', 'warning')
        }
      })
    },
    async getLocationName() {
      // this.currentVehicle = row
      if (this.currentVehicle.latitude && this.currentVehicle.longitude) {
        if (!this.convertor) {
          this.convertor = new Convertor()
        }
        let item = this.convertor.WGS2GCJ({ lng: this.currentVehicle.longitude, lat: this.currentVehicle.latitude })
        let res = await this.getGeoCoder({
          location: item.lat + ',' + item.lng,
          key: 'K6ZBZ-2FUR6-Q6BSY-M7YWP-UWZY5-R5BCG',
          SK: 'OTIm4yvryZ5XPmrqbiZUQHthvZtdy9A3'
        })
        if (res.status === 0) {
          this.currentVehicle.locationName = res.result.address
        } else {
          showToast(res.message, 'warning')
        }
      }
    },
    getGeoCoder(param) {
      let sig = md5(
        `/ws/geocoder/v1?callback=jsonpCallback&key=${param.key}&location=${param.location}&output=jsonp${param.SK}`
      )
      // sig = encodeURI(sig) //url化一下
      let getData = {
        callbackQuery: 'callback', // 设置callback参数的key  不设置的话callback参数会自动被赋予一个随机值  md5校验无法通过
        callbackName: 'jsonpCallback', // 设置callback 参数的值
        key: param.key,
        location: param.location,
        output: 'jsonp',
        sig
      }
      //签名失败的解决办法 https://lbs.qq.com/faq/serverFaq/webServiceKey
      return jsonp('https://apis.map.qq.com/ws/geocoder/v1', getData)
    },
    changeMapType() {
      this.mapType = this.mapType === 'position' ? 'trajectory' : 'position'
      if (this.mapType === 'position') {
        this.getVehiclePosition({
          row: { vin: this.currentVehicle.vin },
          type: this.mapType
        })
      } else {
        this.showFilter()
      }
    },
    parseFloatNumber(num) {
      return parseFloat(parseFloat(num).toFixed(2))
    },
    showFilter() {
      if (!this.filterVehicleTrajectoryForm.dateRange?.length) {
        let nowDate = dateUtils.parseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00'
        let tomorrowDate = dateUtils.parseTime(new Date().getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}') + ' 00:00:00'
        this.filterVehicleTrajectoryForm.dateRange = [nowDate, tomorrowDate]
      }
      // this.filterVehicleTrajectoryVisible = true
      this.filterMapTrajectory()
      // this.$refs.FilterVehicleTrajectory.show()
    },
    filterMapTrajectory() {
      let postData = { ...this.filterVehicleTrajectoryForm }
      if (postData.dateRange?.length) {
        postData.startTime = postData.dateRange[0]
        postData.endTime = postData.dateRange[1]
        delete postData.dateRange
      }
      this.getMapTrajectory(postData)
      // this.getMapTrajectory(row)
    },
    clickMarker(val) {
      this.queryParam.key = val.vin
      this.query()
      this.showVehicleDetail(val)
    },
    showVehicleDetail(val) {
      this.currentVehicle = val
      this.currentVehicleVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/var.scss';

.fleets-trajectory {
  display: flex;
  height: calc(100vh - $header-height - $footer-height - var(--el-main-padding));

  .fleet-list {
    width: 400px;

    .card-header {
      .search-content {
        display: flex;
        align-items: center;

        .search-key {
          width: 100%;
          margin-right: 10px;
          // margin-bottom: 10px;
        }
      }
    }

    :deep(.el-card__body) {
      $card-header-height: calc((var(--el-card-padding) - 2px) * 2 + var(--el-component-size-small));
      height: calc(100% - $card-header-height);
    }

    .table-content {
      height: 100%;

      .el-radio-group {
        margin-bottom: 10px;
        border-radius: 24px;

        .el-radio-button > :deep(.el-radio-button__inner) {
          border: none;

          &:hover {
            color: rgb(16, 41, 106);
          }
        }

        .el-radio-button.is-active > :deep(.el-radio-button__inner) {
          background-color: rgb(16, 41, 106);
          border-color: rgb(16, 41, 106);
          border-radius: 24px;

          &:hover {
            color: white;
          }
        }
      }

      .vehicle-list {
        //$pagination-height: calc(var(--el-pagination-button-height);
        height: calc(100% - 40px - 42px);
        //overflow: scroll;
      }
    }
  }

  .map-content {
    position: relative;
    margin-left: 10px;
    width: calc(100% - 400px);
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
    background-color: var(--el-fill-color-blank);
    overflow: hidden;
    color: var(--el-text-color-primary);
    transition: var(--el-transition-duration);

    .fleet-detail {
      &.filter-fleet-detail {
        :deep(.el-card__body) {
          align-items: center;
          display: flex;

          .filter-search {
            margin-left: 10px;
          }
        }

        left: 20px;
        width: auto;
        right: auto;
      }

      z-index: 2;
      position: absolute;
      right: 100px;
      top: 20px;
      width: 200px;

      .header {
        display: flex;
        justify-content: space-between;

        .tags {
          display: flex;

          .title {
            font-size: 12px;
            margin-right: 10px;
            padding: 0 4px;
            display: flex;
            align-items: center;
            background: rgb(16, 41, 106);
            color: #fff;
            border-radius: 6px;
            //color: rgb(16, 41, 106);
            //font-weight: 600;
            &.external-vin {
              //background: #1b4a7f;
              border: 1px solid #67c23a;
              background-color: #67c23a;
              color: #000;
            }
          }
        }
      }

      .vehicle-location-name {
        font-size: 12px;
        line-height: 28px;
      }

      .vehicle-time {
        font-size: 10px;
      }

      .vehicle-remark {
        font-size: 10px;
        color: #606266;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }
}
</style>
