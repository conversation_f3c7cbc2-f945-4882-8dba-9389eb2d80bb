<template>
  <div class="empty-content" v-if="!list?.length && !list?.length">
    <el-empty description="暂无数据"></el-empty>
  </div>
  <div v-else>
  <el-timeline>
    <template v-for="(item, index) in list" :key="index">
      <el-timeline-item
          :center="false"
          :timestamp="item.deactivationDate"
          placement="top"
          :v-draggable="item.draggable"
          :v-drag-days="item.dragDays"
          :v-drag-class="'.el-timeline-item__wrapper'"
          :v-drag-visible="sliderVisible"
      >
        <el-card>
          <CompetitorTable
              @reload="reload"
              :index="index"
              :item="item"
              :ref="'CompetitorTable' + index"
          ></CompetitorTable>
        </el-card>
      </el-timeline-item>
    </template>
  </el-timeline>
  <el-slider
      v-if="sliderVisible"
      class="custom-slider"
      vertical
      :min="sliderMin"
      :max="sliderMax"
      :height="sliderHeight + 'px'"
      v-model="sliderValue"
      :marks="marks"
      :style="{ top: offsetTop + 'px' }"
  />
  </div>
</template>

<script>
import CompetitorTable from '@/pages/fleet/components/CompetitorTable.vue'
import { i18n } from '@/plugins/lang'
import {  showToast } from '@/plugins/util.js'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import Drag from '@/directives/drag-timeline'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElTimeline,
  ElTimelineItem,
  ElSlider,
  ElSelect
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
import {
  listVehicleCompetitorTimeLineData
} from '@/apis/fleet/ftm-spare-part'
const defaultFormData = {}
export default {
  name: 'VehicleCompetitorConfig',
  emits: ['reload'],
  data() {
    return {
      vehicleInfo:{},
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      $t: i18n.global.t,
      form: Object.assign({}, defaultFormData),
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      currentRow: '',
      currentRowIndex: '',
      options: [],
      typeList: [],
      //为了上传图片的参数
      variant: '',
      list: [],
      sliderValue: 0,
      marks: {
        // 0: '0°C',
        // 8: '8°C',
        // 37: '37°C',
        // 50: {
        //   style: {
        //     color: '#1989FA'
        //   },
        //   label: '50%'
        // }
      },
      sliderVisible: false,
      sliderMin: 0,
      sliderMax: 100,
      sliderHeight: 300,
      offsetTop: 0,
      vehicleId: '',
      vin: '',
      draggable: false,
      edit: false,
      activeItem: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  directives: {
    Drag
  },
  components: {
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElTimeline,
    ElTimelineItem,
    ElSlider,
    ElSelect,
    LtwInput,
    DictionarySelection,
    EmployeeSelection,
    LtwIcon,
    CompetitorTable
  },
  created() {

  },
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = i18n.global.t('对手件配置')
      this.vin = row.vin
      this.listVehicleCompetitorTimeLine()
    },
    cancel() {
      this.dialogVisible = false
    },
    listVehicleCompetitorTimeLine() {
      this.list = []
      listVehicleCompetitorTimeLineData({
        vin: this.vin,
        enabled: true
      }).then(res => {
        this.list = res.data
      })
    },
    addDbcParams() {
      if (~this.list.findIndex(val => val.edit === true)) {
        return showToast('请先保存dbc记录', 'warning')
      } else {
        this.list.unshift({ edit: true, draggable: false, vin: this.vin, dialogStatus: 'add' })
        this.edit = true
      }
    },
    editItem(item) {
      item.edit = true
    },
    reload(obj) {
      if (obj?.type === 'delete') {
        return this.listVehicleCompetitorTimeLine()
      } else if (obj?.type === 'add') {
        return this.listVehicleCompetitorTimeLine()
      } else if (obj?.type === 'edit' || obj?.type === 'cancel') {
        this.list.splice(obj.index, 1, obj.form)
      } else if (obj?.type === 'view') {
        this.$refs.AddDbcConfig.show({
          type: 'view',
          data: obj.form
        })
        return
      }
    },
    cancelData() {
      this.list.splice(this.activeItem.index, 1, this.activeItem.item)
    }
  }
}
</script>
<style lang="scss">
.calibration-dialog {
  .el-dialog__body {
    position: relative;
  }
}
</style>
<style scoped lang="scss">
.custom-slider {
  position: absolute;
  left: 46px;
  // top: 0;
  display: block;
  z-index: 2;
}
.add-dbc {
  margin-left: 68px;
}
.el-timeline-item {
  :deep(.el-timeline-item__wrapper) {
    width: 100%;
  }
  .form-item {
    line-height: 32px;
    color: #303133;
    // color: #606266;
  }
  .file-row {
    display: flex;
    .form-item {
      white-space: nowrap;
    }
  }
  .footer {
    position: absolute;
    bottom: 8px;
    right: 20px;
    text-align: right;
    .el-link {
      margin-left: 6px;
    }
  }
}
</style>
