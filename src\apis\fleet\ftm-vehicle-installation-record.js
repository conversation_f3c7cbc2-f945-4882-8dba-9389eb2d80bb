import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records',
    data,
    params
  })
export const updateFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records',
    data,
    params
  })
export const deleteFtmVehicleInstallationRecord = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records',
    params
  })
export const listFtmVehicleInstallationRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records',
    params
  })
export const listFtmVehicleInstallationRecordSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/selections',
    params
  })
export const pageFtmVehicleInstallationRecord = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/page',
    params
  })
export const getFtmVehicleInstallationRecord = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/' + id })
export const listFtmVariantVersionAndVehicleMappingModalityDifferentSet = (params = {}) =>
  httpGet({
    url:
      GLB_CONFIG.devUrl.serviceSiteRootUrl +
      '/ftm/ftm_variant_version_mapping_modalitys/listFtmVariantVersionAndVehicleMappingModalityDifferentSet',
    params
  })
export const publishFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/release',
    data,
    params
  })
export const previewFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/preview',
    data,
    params
  })
export const revokeFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/revoke',
    data,
    params
  })
export const copyFtmVehicleInstallationRecord = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_vehicle_installation_records/clone',
    data,
    params
  })
