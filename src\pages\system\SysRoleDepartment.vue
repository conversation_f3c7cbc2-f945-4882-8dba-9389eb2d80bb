<template>
  <div>
    <div class="main-container">
      <div class="org-tree-container">
        <org-tree
          @org-click="handleOrgClick"
          ref="orgTree"
          :default-selected-org="currentUser.currentTenantId"
        ></org-tree>
      </div>
      <div class="content-container">
        <el-card>
          <div class="ltw-toolbar">
            <div class="ltw-search-container ltw-tool-container">
              <ltw-input
                placeholder="请输入关键字"
                v-model="queryParam.key"
                clearable
                @clear="refresh"
              >
                <template #append>
                  <el-button id="query" @click="refresh">
                    <ltw-icon icon-code="el-icon-search"></ltw-icon>
                  </el-button>
                </template>
              </ltw-input>
            </div>
            <div class="ltw-tool-container button-group">
              <el-button
                :id="item.buttonCode"
                :type="item.buttonStyleType"
                :key="item.id"
                v-for="item in outlineFunctionList"
                @click="executeButtonMethod(item.buttonCode)"
              >
                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>

                {{ item.name }}
              </el-button>
              <el-dropdown
                @command="handleCommand"
                class="batch-operate-btn"
                v-if="batchingFunctionList && batchingFunctionList.length > 0"
              >
                <el-button type="primary">
                  批量操作
                  <ltw-icon
                    icon-code="el-icon-arrow-down"
                    class="el-icon--right"
                  ></ltw-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :key="item.id"
                      v-for="item in batchingFunctionList"
                      :command="item.buttonCode"
                    >
                      <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                      {{ item.name }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <el-table
            :data="pageData.records"
            row-key="id"
            @selection-change="handleSelectionChange"
            :tree-pros="{ children: 'children', hasChildren: '!asLeaf' }"
            default-expand-all
            ref="tableRef"
          >
            <el-table-column
              header-align="left"
              align="left"
              type="selection"
              width="55"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="name"
              label="名称"
            ></el-table-column>
            <el-table-column
              header-align="left"
              align="left"
              prop="code"
              label="编码"
            ></el-table-column>
            <!--                <el-table-column header-align="left" align="left" prop="parentId" label="父级部门id"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="codeLink" label="编码层级链"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="type" label="类型"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="phone" label="部门电话"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="duty" label="部门职能"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="sortNum" label="顺序"></el-table-column>-->
            <!--                <el-table-column header-align="left" align="left" prop="tenantId" label="租户id"></el-table-column>-->
            <el-table-column
              header-align="left"
              align="left"
              label="操作"
              width="210"
            >
              <template #default="scope">
                <el-button-group>
                  <el-tooltip
                    :key="item.id"
                    v-for="item in inlineFunctionList"
                    effect="dark"
                    :content="item.name"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      :type="item.buttonStyleType"
                      :name="item.buttonCode"
                      @click="executeButtonMethod(item.buttonCode, scope.row)"
                    >
                      <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon
                    ></el-button>
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParam.current"
            :page-sizes="[5, 10, 20, 30]"
            :page-size="queryParam.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageData.total"
          >
          </el-pagination>
        </el-card>
      </div>
    </div>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="50%"
      @close="dialogClosed"
    >
      <el-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <ltw-input
            v-model="formData.name"
            id="name"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <ltw-input
            v-model="formData.code"
            id="code"
            :disabled="formReadonly"
          ></ltw-input>
        </el-form-item>
        <el-form-item label="父级部门" prop="parentId">
          <el-cascader
            v-model="formData.parentId"
            :props="parentSelectionProps"
            :placeholder="$t('请选择') + $t('父级部门')"
            :options="parentDepartSelections"
            filterable
            :show-all-levels="false"
            clearable
            :disabled="formReadonly"
            @change="handleParentOrgChange"
          ></el-cascader>
        </el-form-item>
        <!--                <el-form-item label="父级部门id" prop="parentId">-->
        <!--                    <ltw-input v-model="formData.parentId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="编码层级链" prop="codeLink">-->
        <!--                    <ltw-input v-model="formData.codeLink" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="类型" prop="type">-->
        <!--                    <ltw-input v-model="formData.type" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="部门电话" prop="phone">-->
        <!--                    <ltw-input v-model="formData.phone" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="部门职能" prop="duty">-->
        <!--                    <ltw-input v-model="formData.duty" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="顺序" prop="sortNum">-->
        <!--                    <ltw-input v-model="formData.sortNum" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
        <!--                <el-form-item label="租户id" prop="tenantId">-->
        <!--                    <ltw-input v-model="formData.tenantId" :disabled="formReadonly"></ltw-input>-->
        <!--                </el-form-item>-->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            id="cancel"
            @click="dialogVisible = false"
            v-if="dialogStatus === 'view'"
            >关 闭</el-button
          >
          <template v-else>
            <el-button id="cancel" @click="dialogVisible = false"
              >取 消</el-button
            >
            <el-button type="primary" id="submit" @click="save"
              >保 存</el-button
            >
          </template>
        </span>
      </template>
    </el-dialog>
    <privilege-assignment
      v-model="privilegeAssignmentVisible"
      :role-id="currentSelectedRoleId"
      :role-type="currentRoleType"
    ></privilege-assignment>
  </div>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  saveSysRoleDepartment,
  updateSysRoleDepartment,
  deleteSysRoleDepartment,
  getSysRoleDepartment,
  PageSysRoleDepartmentTree,
  getSysRoleDepartmentTree
} from '@/apis/system/sys-role-department'
import OrgTree from '@/components/system/OrgTree'
import PrivilegeAssignment from '@/components/system/PrivilegeAssignment'
import ROLE_TYPE from '@/plugins/constants/role-type'

const defaultFormData = {}
export default {
  name: 'SysRoleDepartment',
  components: { OrgTree, PrivilegeAssignment },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {},
      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('姓名'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('编码'),
            trigger: 'blur'
          }
        ]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentUser: {},
      parentSelectionProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true,
        disabled: 'disabled',
        emitPath: false
      },
      privilegeAssignmentVisible: false,
      currentSelectedRoleId: '',
      currentRoleType: ROLE_TYPE.DEPARTMENT,
      parentDepartSelections: []
    }
  },
  created() {
    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
    this.currentUser = this.$store.state.permission.currentUser
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      PageSysRoleDepartmentTree(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = '添加部门'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.formData.tenantId = this.queryParam.orgId
      this.getDepartmentList()
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'add') {
            saveSysRoleDepartment(this.formData).then(() => {
              this.dialogVisible = false
              this.query()
            })
          }
          if (this.dialogStatus === 'edit') {
            updateSysRoleDepartment(this.formData).then(() => {
              this.dialogVisible = false
              this.query()
            })
          }
        }
      })
    },
    edit({ id }) {
      this.dialogTitle = '修改部门'
      this.dialogStatus = 'edit'
      getSysRoleDepartment(id).then(res => {
        this.dialogVisible = true
        this.formData = res.data
        this.getDepartmentList(res.data.id)
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        this.$message.warning({
          message: '请先选择数据再执行批量操作',
          type: 'warning'
        })
        return
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id, containChildren: !row.asLeaf })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      let msg = '此操作将永久删除选中数据，是否继续?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSysRoleDepartment(param).then(() => {
            this.query()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    view({ id }) {
      this.dialogTitle = '查看部门'
      this.dialogStatus = 'view'
      getSysRoleDepartment(id).then(res => {
        this.dialogVisible = true
        this.$nextTick(function () {
          this.formData = res.data
        })
      })
      this.getDepartmentList()
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    handleOrgClick(org) {
      this.queryParam.orgId = org.id
      this.query()
    },
    getDepartmentList(id) {
      let postData = {
        orgId: this.queryParam.orgId
      }
      getSysRoleDepartmentTree(postData).then(res => {
        if (id) {
          this.formatDepartment(res.data, id)
        }
        this.parentDepartSelections = res.data
      })
    },
    formatDepartment(data, id) {
      for (let i = 0, iLen = data.length; i < iLen; i++) {
        if (data[i].id === id) {
          data[i].disabled = true
          if (data[i].children && data[i].children.length) {
            this.setDisabled(data[i].children)
          }
          return
        } else {
          if (data[i].children && data[i].children.length) {
            if (!this.formatDepartment(data[i].children, id)) {
              return
            }
          }
        }
      }
      return true
    },
    setDisabled(data) {
      for (let i = 0, iLen = data.length; i < iLen; i++) {
        data[i].disabled = true
        if (data[i].children && data[i].children.length) {
          this.setDisabled(data[i].chidlren)
        }
      }
    },
    handleParentOrgChange(val) {
      if (!val) {
        this.formData.parentId = BASE_CONSTANT.TREE_ROOT_PARENT
      }
    },
    privilegeAssign(row) {
      this.currentSelectedRoleId = row.id
      this.privilegeAssignmentVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.main-container {
  display: flex;
  flex-direction: row;

  .org-tree-container {
    width: 280px;
    margin-right: 20px;
  }

  .content-container {
    width: calc(100% - 300px);
  }
}
</style>
