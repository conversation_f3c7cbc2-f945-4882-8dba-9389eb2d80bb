<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    draggable
    destroy-on-close
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px" :hide-required-asterisk="formReadonly">
      <el-form-item :label="$t('应用')" prop="name">
        <el-tag>{{ form.name }}</el-tag>
      </el-form-item>
      <el-form-item :label="$t('版本')" prop="version">
        <ltw-input v-model="form.version" />
      </el-form-item>
      <el-form-item :label="$t('备注')" prop="remark">
        <ltw-input
          v-model="form.remark"
          :disabled="formReadonly"
          textType="remark"
          type="textarea"
          id="remark"
        ></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('文件')" :prop="'fileIdList'">
        <upload-file
          :disabled="dialogStatus !== 'add'"
          ref="uploadImage"
          source-type="app_file"
          :limit="1"
          listType="text"
          :source-id="form.id"
          v-model="form.fileIdList"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { listLatestFtmApp, saveFleetApp } from '@/apis/fleet/app-management'
import UploadFile from '@/components/system/UploadFile.vue'

const defaultform = {}
export default {
  name: 'AddApp',
  emits: ['reload'],
  components: { UploadFile },
  data() {
    return {
      visible: false,
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        version: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('版本'),
            trigger: 'change'
          }
        ],
        fileIdList: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('文件'),
            trigger: 'change'
          }
        ]
      },
      appList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    listLatestFtmApp() {
      listLatestFtmApp().then(res => {
        this.appList = res.data
      })
    },
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增') + this.$t('应用')
          this.form.name = row.data.name
          this.form.variant = row.data.variant
          break
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      if (this.visible) {
        this.closeRequirementList()
      }
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form
        }
        if (this.dialogStatus === 'add') {
          saveFleetApp(postData).then(() => {
            this.dialogVisible = false
            this.$emit('reload')
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
