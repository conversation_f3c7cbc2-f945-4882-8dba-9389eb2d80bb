<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <div class="step-indicator">
      <div class="step-item" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <div class="step-label">基本信息</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 1 }"></div>
      <div class="step-item" :class="{ active: currentStep === 2, completed: currentStep > 2 }">
        <div class="step-number">2</div>
        <div class="step-label">配置信息</div>
      </div>
      <div class="step-line" :class="{ completed: currentStep > 2 }"></div>
      <div class="step-item" :class="{ active: currentStep === 3, completed: currentStep > 3 }">
        <div class="step-number">3</div>
        <div class="step-label">完成新增</div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：基本信息 -->
      <div v-if="currentStep === 1" class="step-panel">
        <el-form ref="basicForm" :model="formData" :rules="basicRules" label-width="120px">
          <el-form-item label="版本名称：" prop="version">
            <ltw-input 
              v-model="formData.version" 
              placeholder="请输入版本名称"
              style="width: 400px;"
            />
          </el-form-item>
          <el-form-item label="描述：" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              maxlength="100"
              show-word-limit
              :rows="6"
              placeholder="请输入版本描述"
              style="width: 600px;"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第二步：配置信息 -->
      <div v-if="currentStep === 2" class="step-panel">
        <!-- 复用传感器配置组件 -->
        <SensorConfigSection
          :editable="true"
          :version-data="versionDataForConfig"
          @add-config="handleAddConfig"
          @edit-config="handleEditConfig"
          @delete-config="handleDeleteConfig"
          @view-config="handleViewConfig"
        />
      </div>

      <!-- 第三步：完成确认 -->
      <div v-if="currentStep === 3" class="step-panel">
        <div class="completion-summary">
          <el-icon class="success-icon"><Check /></el-icon>
          <h3>版本创建完成</h3>
          <p>版本 "{{ formData.version }}" 已成功创建</p>
        </div>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="currentStep > 1" @click="handlePrevStep">上一步</el-button>
        <el-button v-if="currentStep < 3" type="primary" @click="handleNextStep">下一步</el-button>
        <el-button v-if="currentStep === 3" type="primary" @click="handleComplete">完成创建</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Check } from '@element-plus/icons-vue'
import SensorConfigSection from './SensorConfigSection.vue'
import {
  saveFtmVehicleVariantVersion,
  updateFtmVehicleVariantVersion,
  getFtmVehicleVariantVersion,
  copyFtmVehicleVariantVersion
} from '@/apis/fleet/ftm-vehicle-variant-version'


export default {
  name: 'AddVersionDialog',
  components: {
    Check,
    SensorConfigSection
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    vehicleData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'add', // 'add' | 'copy'
      validator: value => ['add', 'copy'].includes(value)
    },
    versionData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'confirm'],
  data() {
    return {
      currentStep: 1,
      formData: {
        version: '',
        remark: '',
        sensorConfigs: []
      },
      basicRules: {
        version: [
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ]
      },
      versionDataForConfig: null, // 存储接口返回的版本数据
      currentVersionId:null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    dialogTitle() {
      return this.mode === 'copy' ? '复制版本' : '新增版本'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    },
    versionData: {
      handler(newVal) {
        if (newVal && this.mode === 'copy') {
          this.fillFormWithVersionData(newVal)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      // 确保步骤重置到第一步
      this.currentStep = 1

      if (this.mode === 'copy' && this.versionData) {
        this.fillFormWithVersionData(this.versionData)
      } else {
        this.resetForm()
      }
    },

    // 用版本数据填充表单
    fillFormWithVersionData(versionData) {
      // 确保步骤在第一步
      this.currentStep = 1

      this.formData = {
        version: versionData.version ? `${versionData.version}` : '',
        remark: versionData.remark || '',
        sensorConfigs: []
      }
      this.currentVersionId = versionData.id

      // 复制传感器配置数据
      if (versionData.versionMappingModalityVOS && versionData.versionMappingModalityVOS.length > 0) {
        this.formData.sensorConfigs = versionData.versionMappingModalityVOS.map(config => ({
          ...config,
        }))
      }

      // 设置versionDataForConfig用于第二步显示
      this.versionDataForConfig = {
        ...versionData,
        versionMappingModalityVOS: this.formData.sensorConfigs
      }
    },
    // 重置表单
    resetForm() {
      this.currentStep = 1
      this.formData = {
        version: '',
        remark: '',
        sensorConfigs: []
      }
      this.versionDataForConfig = null // 重置版本数据
      this.currentVersionId = null
      if (this.$refs.basicForm) {
        this.$refs.basicForm.clearValidate()
      }
    },



    // 处理关闭对话框
    handleClose() {
      // 确保关闭时重置到第一步
      this.currentStep = 1
      this.resetForm()
      this.$emit('update:visible', false)
    },

    // 处理下一步
    async handleNextStep() {
      if (this.currentStep === 1) {
        // 验证基本信息
        const valid = await this.$refs.basicForm.validate().catch(() => false)
        if (!valid) return

     

        // 新增模式或没有配置数据时，调用API
        const param = {
          ...this.formData,
          variantId: this.vehicleData.id
        }

           if (this.mode === 'copy' ) {
          const param ={
            id:this.versionData.id,
            ...this.formData
          }
          const res =  await copyFtmVehicleVariantVersion(param)
          this.versionDataForConfig = res.data
          this.formData = res.data
          this.currentVersionId = res.data.id
        }else if (!this.currentVersionId) {
          const res = await saveFtmVehicleVariantVersion(param)
          this.versionDataForConfig = res.data
          this.formData = res.data
          this.currentVersionId = res.data.id
        } else {
          await updateFtmVehicleVariantVersion(param)
        }
        this.$message.success('保存成功')
        this.$emit('updated')
      }

      if (this.currentStep < 3) {
        this.currentStep++
      }
    },

    // 处理上一步
    handlePrevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // 处理完成创建
    handleComplete() {
      this.$emit('confirm', {
        ...this.formData,
        vehicleId: this.vehicleData.id
      })
      this.handleClose()
    },

    // 处理新增传感器配置
    handleAddConfig() {
      this.loadVersionDetail(this.currentVersionId)
    },

    // 处理编辑配置
    handleEditConfig() {
      this.loadVersionDetail(this.currentVersionId)
    },

        // 加载版本详情
    async loadVersionDetail(versionId) {
      if (!versionId) return

      try {
        const res = await getFtmVehicleVariantVersion(versionId)
        this.versionDataForConfig = res.data || {}




      } catch (error) {
        console.error('加载版本详情失败:', error)
        //this.$message.error('加载版本详情失败')
        this.versionDataForConfig = null
      } 
    },

    // 处理删除配置
    handleDeleteConfig(row) {
       this.loadVersionDetail(this.currentVersionId) 
    },

    // 处理查看配置
    handleViewConfig(row) {
      this.$message.info(`查看配置: ${row.modality}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  padding: 20px 0;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      background: #F5F5F5;
      color: #909399;
      border: 2px solid #E4E7ED;

      &.active {
        background: #5755FF;
        color: white;
        border-color: #5755FF;
      }

      &.completed {
        background: #67C23A;
        color: white;
        border-color: #67C23A;
      }
    }

    .step-label {
      font-size: 12px;
      color: #909399;
      font-weight: 400;

      &.active {
        color: #5755FF;
        font-weight: 600;
      }

      &.completed {
        color: #67C23A;
        font-weight: 600;
      }
    }

    &.active {
      .step-number {
        background: #5755FF;
        color: white;
        border-color: #5755FF;
      }

      .step-label {
        color: #5755FF;
        font-weight: 600;
      }
    }

    &.completed {
      .step-number {
        background: #67C23A;
        color: white;
        border-color: #67C23A;
      }

      .step-label {
        color: #67C23A;
        font-weight: 600;
      }
    }
  }

  .step-line {
    width: 120px;
    height: 2px;
    background: #E4E7ED;
    margin: 0 20px;

    &.completed {
      background: #67C23A;
    }
  }
}

.step-content {
  min-height: 400px;

  .step-panel {
    padding: 20px 0;
  }
}

.completion-summary {
  text-align: center;
  padding: 40px 20px;

  .success-icon {
    font-size: 64px;
    color: #67C23A;
    margin-bottom: 20px;
  }

  h3 {
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  p {
    color: #606266;
    font-size: 14px;
    margin-bottom: 20px;
  }

  .summary-info {
    background: #F5F7FA;
    border-radius: 4px;
    padding: 20px;
    text-align: left;
    max-width: 400px;
    margin: 0 auto;

    p {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}





.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
