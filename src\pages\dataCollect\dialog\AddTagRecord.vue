<template>
  <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="dialogClosed"
      @open="dialogOpened"
      append-to-body
      draggable
      destroy-on-close
  >
    <el-form :model="form" ref="formRef" label-width="110px">
      <el-form-item :label="$t('车辆')" prop="vin">
        <el-link type="primary">{{ form.vin }}</el-link>
      </el-form-item>
      <el-form-item :label="$t('任务')" prop="taskCode">
        <el-link type="primary">{{ form.taskCode }}</el-link>
      </el-form-item>
      <el-form-item :label="$t('标签组')" prop="tag">
        <el-tag>{{ form.tagGroupName }}</el-tag>
      </el-form-item>
      <el-form-item :label="$t('标签')" prop="tag">
        <el-tag :type="checkTagType(form)">{{ locale === 'zh' ? form.tagNameCn : form.tagName }}</el-tag>
      </el-form-item>
      <el-form-item :label="$t('时间')" prop="time">
        <el-tag>{{ form.startTime }}</el-tag
        >&nbsp;&nbsp;To&nbsp;&nbsp;
        <el-tag>{{ form.endTime }}</el-tag>
      </el-form-item>
      <el-form-item :label="$t('文本')" prop="remark">
        <el-button link plain v-loading="loading" class="microphone-btn" @click="playVoice()">
          <!--        <el-link type="primary" class="microphone-btn" :underline="false" @click="playVoice()">-->
          <div class="voice-animation">
            <ltw-icon icon-code="el-icon-microphone"></ltw-icon>
            <div class="voice-symbol" v-loading="loading">
              <!--              <div v-show="loading" class="icon-loading">-->
              <!--                <ltw-icon icon-code="el-icon-loading"></ltw-icon>-->
              <!--              </div>-->
              <div class="voice-circle first" :class="{ 'fade-in-out': isPlay }"></div>
              <div class="voice-circle second" :class="{ 'fade-in-out': isPlay }"></div>
            </div>
          </div>
          <audio controls id="txt-audio">您的浏览器不支持 audio 标签。</audio>
          <!--        </el-link>-->
        </el-button>
        <ltw-input
            v-model="form.remark"
            :disabled="formReadonly"
            textType="remark"
            type="textarea"
            id="remark"
        ></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
            $t('关闭')
          }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { saveVtTagRecord, getVtTagRecord, updateVtTagRecord } from '@/apis/data-collect/vt-tag-record'
import { getLocale, checkTagType } from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'
import util from '@/plugins/util'
import {showToast} from "../../../plugins/util";

const defaultform = {}
export default {
  name: 'AddTagRecord',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {},
      checkTagType: checkTagType,
      locale: getLocale(),
      audioSrc: '',
      isPlay: false,
      loading: false,
      audioElement: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增') + this.$t('语音记录')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑') + this.$t('语音记录')
          this.getVtTagRecord(row.data.id)
          break
        case 'view':
          this.dialogTitle = this.$t('查看') + this.$t('语音记录')
          this.getVtTagRecord(row.data.id)
          break
      }
    },
    getVtTagRecord(id) {
      getVtTagRecord(id).then(res => {
        this.form = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form
        }
        if (this.dialogStatus === 'add') {
          saveVtTagRecord(postData).then(() => {
            this.cancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateVtTagRecord(postData).then(() => {
            this.cancel()
          })
        }
      })
    },

    playVoice() {
      const _this = this
      this.loading = true
      // this.audioSrc = 'https://room.ylzmjd.com/shiting/%E3%80%8A%E5%B9%B3%E5%87%A1%E7%9A%84%E4%B8%80%E5%A4%A9%E3%80%8BCD2/03%20%E5%83%8F%E6%88%91%E8%BF%99%E6%A0%B7%E7%9A%84%E4%BA%BA%20(Inst).mp3'
      // this.audioSrc =
      //   'https://fleet.arena.bosch-szh.com.cn/server/test/files/preview?path=' +
      //   filePath +
      //   '&token=70c1dc7630554eae9d78f1569be0ea2d'
      if (this.audioElement) {
        const audioElement = document.querySelector('#txt-audio')
        if (_this.isPlay) {
          this.loading = false
          this.isPlay = false
          audioElement.pause()
        } else {
          audioElement.play()
        }
      } else {
        const audioElement = document.querySelector('#txt-audio')
        this.audioElement = audioElement
        audioElement.src = GLB_CONFIG.devUrl.serviceSiteRootUrl + '/files/preview?path=' + this.form.filePath + '&token=' + util.getToken()
        // const filePath = 'cos://public-data-pipeline-1309779946/chenghuang_pad/audio_tag/sample-15s.wav'
        // audioElement.src = 'https://fleet.arena.bosch-szh.com.cn/server/test/files/preview?path=' + filePath + '&token=70c1dc7630554eae9d78f1569be0ea2d'
        audioElement.addEventListener('play', function (e) {
          // console.log('开始播放')
          _this.loading = false
          _this.isPlay = true
        })
        audioElement.addEventListener('pause', function (e) {
          // console.log('暂停')
          _this.isPlay = false
        })
        audioElement.addEventListener('ended', function (e) {
          // console.log('停止')
          _this.isPlay = false
        })
        audioElement.addEventListener('waiting', function (e) {
          // console.log('加载等待中')
          _this.loading = true
        })
        audioElement.addEventListener('canplay', function (e) {
          // console.log('加载完可以播放')
          _this.loading = false
          _this.isPlay = true
          audioElement.play()
        })
        audioElement.addEventListener('error', function (e) {
          _this.loading = false
          _this.isPlay = false
          showToast(_this.$t('加载错误'), 'warning')
        })
        // 1. 创建上下文
        // const audioCtx = new (window.AudioContext || window.webkitAudioContext)()
        // 2. 获取音频源（MediaElementAudioSourceNode）
        // 这里音频源可以使用 OscillatorNode 创建，也使用麦克风源等
        // var track = audioContext.createMediaElementSource(audioElement)
        // ...这里我们已经可以将声音连接到系统扬声器
        // track.connect(audioContext.destination);
        // 3. 需要先启动音频环境，否则可能会提示需要手势触发的警告
        // resume() 和 suspend() 返回的都是异步对象，最好使用then等待异步处理结束
        // if (audioCtx.state === 'suspended') {
        //   audioCtx.resume()
        // }
      }
      // setTimeout(() => {
      //   // 4. 播放控制
      //   audioElement.play()
      //   // audioElement.pause()
      // })
    }
  }
}
</script>

<style scoped lang="scss">
.microphone-btn {
  position: relative;
  font-size: 18px;
  margin-bottom: 10px;
  overflow: hidden;

  #txt-audio {
    position: absolute;
    left: 100%;
    top: 100%;
  }

  .voice-animation {
    display: flex;
    align-items: center;
    height: 100%;

    .voice-symbol {
      position: relative;
      margin-left: 4px;
      width: 32px;
      height: 32px;
      box-sizing: border-box;
      overflow: hidden;
      transform: rotate(135deg);

      .voice-circle {
        border: 2px solid #409eff;
        border-radius: 50%;
        position: absolute;
        @keyframes fadeInOut {
          0% {
            opacity: 0;
            /*初始状态 透明度为0*/
          }

          100% {
            opacity: 1;
            /*结尾状态 透明度为1*/
          }
        }

        &.first {
          width: 16px;
          height: 16px;
          top: 25px;
          left: 25px;

          &.fade-in-out {
            animation: fadeInOut 1s infinite 0.2s;
          }
        }

        &.second {
          width: 25px;
          height: 25px;
          top: 15px;
          left: 15px;

          &.fade-in-out {
            animation: fadeInOut 1s infinite 0.4s;
          }
        }
      }
    }
  }
}
</style>
