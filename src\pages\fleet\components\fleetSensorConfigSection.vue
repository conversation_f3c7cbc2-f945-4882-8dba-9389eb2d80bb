<template>
  <div class="sensor-config-section">
    <!-- 传感器统计 -->
    <div class="sensor-stats">
      <div class="sensor-stats-left">
       
        <div class="sensor-item">
          <ltw-icon :icon-code="'el-icon-notebook'"></ltw-icon>
          <span class="sensor-label">Lidar</span>
          <span class="sensor-count">{{ versionData?.modalityTypeMap?.lidar || '0' }}</span>
        </div>
        <div class="divider"></div>
        <div class="sensor-item">
          <ltw-icon :icon-code="'el-icon-camera'"></ltw-icon>
          <span class="sensor-label">Camera</span>
          <span class="sensor-count">{{ versionData?.modalityTypeMap?.camera || '0' }}</span>
        </div>
        <div class="divider"></div>
         <div class="sensor-item">
          <ltw-icon :icon-code="'el-icon-tickets'"></ltw-icon>
          <span class="sensor-label">Radar</span>
          <span class="sensor-count">{{ versionData?.modalityTypeMap?.radar || '0' }}</span>
        </div>
      </div>

      <div class="sensor-stats-right">
        <el-button v-if="editable" type="primary" size="small" @click="handleAddSensor">
          <ltw-icon :icon-code="'el-icon-plus'"></ltw-icon>
          新增传感器
        </el-button>
      </div>
    </div>

    <!-- 配置表格 -->
    <div class="config-table">
      <el-table :data="versionData?.versionMappingModalityVOS" style="width: 100%" @expand-change="handleExpandChange">
        <!-- 展开列 -->
        <el-table-column type="expand" width="35" align="center">
          <template #default="props">
            <div class="expand-content">
              <!-- Tab切换 -->
              <el-tabs
                :model-value="expandActiveTabs[getRowKey(props.row)]"
                class="expand-tabs"
                @tab-click="handleTabClick(props.row, $event)"
                @update:model-value="handleTabChange(props.row, $event)"
              >
                <!-- 软件信息Tab -->
                <el-tab-pane label="软件信息" name="software" style="font-size:12px;font-weight:700">
                  <div class="software-info">
                    <div class="install-row">
                      <!-- 根据传感器类型动态显示软件参数 -->
                      <template v-if="props.row.sensorType === 'camera'">
                        <!-- Camera类型参数 -->
                        <div class="install-item">
                          <span class="install-label">相邻两行曝光间隔</span>
                          <span class="install-value">{{ props.row.exposureInterval || '-' }}</span>
                        </div>
                        <div class="install-item">
                          <span class="install-label">畸变模型</span>
                          <span class="install-value">{{ props.row.distortionModel || '-' }}</span>
                        </div>
                      </template>

                      <template v-else-if="props.row.sensorType === 'lidar'">
                        <!-- Lidar类型参数 -->
                        <div class="install-item">
                          <span class="install-label">msop</span>
                          <span class="install-value">{{ props.row.msop || '-' }}</span>
                        </div>
                        <div class="install-item">
                          <span class="install-label">difop</span>
                          <span class="install-value">{{ props.row.difop || '-' }}</span>
                        </div>
                        <div class="install-item">
                          <span class="install-label">雷达时间戳定义角度</span>
                          <span class="install-value">{{ props.row.lidarTimestampAngle || '-' }}</span>
                        </div>
                      </template>

                      <!-- 通用参数 (所有类型都显示) -->
                      <div class="install-item">
                        <span class="install-label">帧同步偏移值</span>
                        <span class="install-value">{{ props.row.pcdJpgOffset || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">间隔差的基准值</span>
                        <span class="install-value">{{ props.row.intervalDif || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">帧同步差前偏移值 </span>
                        <span class="install-value">{{ props.row.syncFrameOffsetForward || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">帧同步差后偏移值</span>
                        <span class="install-value">{{ props.row.syncFrameOffsetBack || '-' }}</span>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                      <!-- 安装信息Tab -->
                <el-tab-pane label="安装信息" name="install" style="font-size:12px;font-weight:700">
                  <div class="install-info">
                    <!-- 第一行安装信息 -->
                    <div class="install-row">
                      <div class="install-item">
                        <span class="install-label">指导位置x (mm)</span>
                        <span class="install-value">{{ props.row.positionX || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">指导位置y (mm)</span>
                        <span class="install-value">{{ props.row.positionY || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">指导位置z (mm)</span>
                        <span class="install-value">{{ props.row.positionZ || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">图片中坐标</span>
                        <span class="install-value">{{ props.row.imageCoordinate || '-' }}</span>
                      </div>
                      <div class="install-item">
                        <span class="install-label">距地高度 (mm)</span>
                        <span class="install-value">{{ props.row.height || '-' }}</span>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 附件信息Tab -->
                <el-tab-pane label="附件信息" name="attachment" style="font-size:12px;font-weight:700">
                  <div class="attachment-info">
                    <el-table
                      :data="props.row?.files || []"
                      class="attachment-table"
                      stripe
                      border
                      size="small"
                    >
                      <el-table-column
                        prop="sourceType"
                        label="附件类型"
                        align="center"
                        min-width="120"
                      >
                        <template #default="scope">
                          {{ scope.row.sourceType || '-' }}
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="fileName"
                        label="附件名称"
                        align="center"
                        min-width="200"
                      >
                        <template #default="scope">
                          {{ scope.row.fileName || '-' }}
                        </template>
                      </el-table-column>
                    </el-table>
                  
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </template>
        </el-table-column>

        <!-- 警告图标列 -->
        <el-table-column label="传感器编码" min-width="140" align="left">
          <template #default="scope">
            <div class="sensor-code-container">
              <el-tooltip :content="`${scope.row.modality || ''}`.trim()" placement="top">
                <el-tag :type="scope.row.perfect?'primary':'warning'">{{ scope.row.modality }}</el-tag>
              </el-tooltip>
              <el-tooltip v-if="!scope.row.perfect" content="软件信息待补全" placement="top" effect="dark">
                <svg class="warning-icon" aria-hidden="true">
                  <use xlink:href="#svg-warning-outline" />
                </svg>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="传感器类型" min-width="110" align="center">
          <template #default="scope">
            <span class="sensor-type">{{ scope.row.sensorTypeName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="供应商"  align="center">
          <template #default="scope">
            <el-tag type="success">{{ scope.row.supplierName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="型号规格" align="center" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <div class="model-spec-container">
              <el-tooltip :content="`${scope.row.model || ''} ${scope.row.specification || ''}`.trim()" placement="top">
                <span class="model-arch">{{ scope.row.model }} {{ scope.row.specification }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="editable && versionData.status !== 'released'" label="操作" width="200"  align="center" fixed="right" >
          <template #default="scope">
            <div class="action-buttons-group">
                  <el-button
                    v-if="versionData.status !== 'released' || scope.row.files?.length"
                    size="small"
                    class="upload-btn"
                    @click="handleUpload(scope.row)"
                  >
                    <ltw-icon icon-code="el-icon-paperclip"></ltw-icon>
                  </el-button>
              <!-- <el-popover placement="left" width="400" trigger="hover">
                <template #reference>
                  <el-button
                    v-if="versionData.status !== 'released' || scope.row.files?.length"
                    size="small"
                    class="upload-btn"
                    @click="handleUpload(scope.row)"
                  >
                    <ltw-icon icon-code="el-icon-upload"></ltw-icon>
                    <ltw-icon icon-code="el-icon-paperclip"></ltw-icon>
                  </el-button>
                </template>
                <el-table :data="scope.row.files">
                  <el-table-column width="120" prop="sourceType" label="sourceType"></el-table-column>
                  <el-table-column prop="fileName" label="fileName">
                    <template #default="scope">
                      <el-link target="_blank" underline :href="scope.row.url">
                        <span style="display: inline">
                          <span>{{ scope.row.fileName }}</span>
                          <span v-if="scope.row.fileType">.{{ scope.row.fileType }}</span>
                        </span>
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </el-popover> -->
              <el-button class="edit-btn" size="small" @click="handleEditConfig(scope.row)"  v-if="versionData.status !== 'released' ">
                <ltw-icon :icon-code="'el-icon-edit'"></ltw-icon>
              </el-button>
              <el-button class="view-btn" size="small" @click="handleViewConfig(scope.row)">
                <ltw-icon :icon-code="'el-icon-view'"></ltw-icon>
              </el-button>
              <el-button class="delete-btn" size="small" @click="handleDeleteConfig(scope.row)"  v-if="versionData.status !== 'released' ">
                <ltw-icon :icon-code="'el-icon-delete'"></ltw-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 传感器配置抽屉 -->
    <FleetSensorConfigDrawer
      v-if="showSensorDrawer"
      v-model:visible="showSensorDrawer"
      :mode="drawerMode"
      :sensor-data="currentSensorData"
      :variantVersionId="versionData?.variantVersionId"
      @confirm="handleSensorConfirm"
    />
    <modality-file-upload-dialog ref="ModalityFileUploadDialog" @reload="handleFileReload"/>

    <!--新增传感器-->
    <ChooseModality ref="ChooseModality" @reload="handleSensorConfirm"></ChooseModality>
  </div>
</template>

<script>
import FleetSensorConfigDrawer from './fleetSensorConfigDrawer.vue'
import {
  saveFtmVariantVersionMappingModality,
  updateFtmVariantVersionMappingModality,
  getFtmVariantVersionMappingModality
} from '@/apis/fleet/ftm-variant-version-mapping-modality'
import {
  updateFtmVehicleMappingModalitys,
  deleteFtmVehicleMappingModalitys,
  listFtmVehicleMappingModality
} from '@/apis/fleet/ftm-variant-mapping-modality'
import {
  listFtmVariantVersionMappingModality,
  deleteFtmVariantVersionMappingModality
} from '@/apis/fleet/ftm-variant-version-mapping-modality'
import ModalityFileUploadDialog from '@/pages/fleet/dialog/ModalityFileUploadDialog.vue'
import ChooseModality from '@/pages/fleet/dialog/ChooseModality.vue'

export default {
  name: 'SensorConfigSection',
  components: {
    FleetSensorConfigDrawer,
    ModalityFileUploadDialog,
    ChooseModality
  },
  props: {
    versionData: {
      type: Object,
      default: () => ({})
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      expandActiveTabs: {}, // 存储每个行的活跃tab状态，key为行的唯一标识
      showSensorDrawer: false, // 控制传感器配置抽屉显示
      drawerMode: 'add', // 抽屉模式：add, edit, view
      currentSensorData: {} // 当前操作的传感器数据
    }
  },
  watch: {
    // 监听版本数据变化，初始化Tab状态
    'versionData.versionMappingModalityVOS': {
      handler() {
        this.$nextTick(() => {
          this.initializeTabStates()
        })
      },
      immediate: true
    }
  },
  methods: {
    // 获取行的唯一标识
    getRowKey(row) {
      return row.id || row.modality || `${row.sensorTypeName}_${row.model}_${row.specification}`
    },

    // 处理Tab点击事件
    handleTabClick(row, tab) {
      const rowKey = this.getRowKey(row)
      // Vue 3 中直接赋值即可触发响应式更新
      debugger
      this.expandActiveTabs[rowKey] = tab.name
    },

    // 处理Tab变化事件（用于model-value双向绑定）
    handleTabChange(row, activeTab) {
      const rowKey = this.getRowKey(row)
      // Vue 3 中直接赋值即可触发响应式更新
      this.expandActiveTabs[rowKey] = activeTab
    },

    // 初始化所有配置行的Tab状态
    initializeTabStates() {
      if (this.versionData && this.versionData.versionMappingModalityVOS) {
        this.versionData.versionMappingModalityVOS.forEach(row => {
          const rowKey = this.getRowKey(row)
          // 只在没有设置过的情况下初始化
          if (!this.expandActiveTabs[rowKey]) {
            this.expandActiveTabs[rowKey] = 'software'
          }
        })
      }
    },

    // 处理表格行展开/收起事件
    handleExpandChange(row, expandedRows) {
      const rowKey = this.getRowKey(row)
      const isExpanded = expandedRows.some(expandedRow => this.getRowKey(expandedRow) === rowKey)

      if (isExpanded) {
        // 只在第一次展开时设置默认tab，避免覆盖用户的选择
        if (!this.expandActiveTabs[rowKey]) {
          this.expandActiveTabs[rowKey] = 'software'
        }

        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    },

    // 处理新增传感器
    handleAddSensor() {
      this.drawerMode = 'add'
      this.currentSensorData = {}

      this.$refs.ChooseModality.show({
        type: 'view',
        data: {
          vehicleId: this.versionData.vehicleId,
          installationRecordId: this.versionData.id,
          variantVersionId: this.versionData.variantVersionId,
          variant: this.versionData.code
        }
      })
    },
    //上传传感器附件
    handleUpload(row) {
      this.$refs.ModalityFileUploadDialog.show({
        id: row.id,
        type: this.drawerMode === 'view' ? 'view' : 'edit'
      })
    },

    // 处理编辑配置
    handleEditConfig(row) {
      this.drawerMode = 'edit'
      this.currentSensorData = { ...row }
      this.currentSensorData.useType = this.versionData.useType
      this.showSensorDrawer = true
    },

    // 处理删除配置
    handleDeleteConfig(row) {
      this.$confirm(`确定要删除该配置吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteFtmVehicleMappingModalitys({ id: row.id }).then(() => {
            this.$emit('delete-config', row)
          })
        })
        .catch(() => {
          // 取消删除
        })
    },

    // 处理查看配置
    handleViewConfig(row) {
      this.drawerMode = 'view'
      this.currentSensorData = { ...row }
      this.showSensorDrawer = true
    },

    // 处理传感器配置确认
    async handleSensorConfirm(result) {
      if (this.drawerMode === 'add') {
        this.$emit('add-config', result.data)
      } else if (result.mode === 'edit') {
        await updateFtmVehicleMappingModalitys(result.data)
        this.$emit('edit-config', result.data)
      }
    },

    // 判断文本是否会溢出
    isTextOverflow(model, specification) {
      const text = `${model || ''} ${specification || ''}`.trim()
      // 简单的长度判断，可以根据实际需要调整
      return text.length > 20 // 假设超过20个字符就可能溢出
    },
    handleFileReload(){
      this.$emit('reload')
    }
  }
}
</script>

<style lang="scss" scoped>
.sensor-config-section {
  .sensor-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .sensor-stats-left {
      display: flex;
      align-items: center;
      gap: 10px;

      .sensor-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .sensor-icon {
          width: 16px;
          height: 16px;
          background: #232628;
        }

        .sensor-label {
          color: #4e5256;
          font-size: 12px;
          font-family: 'Bosch Sans Global', sans-serif;
          font-weight: 400;
          line-height: 24px;
        }

        .sensor-count {
          color: #5755ff;
          font-size: 12px;
          font-family: 'Bosch Sans Global', sans-serif;
          font-weight: 400;
          line-height: 24px;
        }
      }

      .divider {
        width: 1px;
        height: 13px;
        background: #d0d4d8;
        border-radius: 1px;
      }
    }

    .sensor-stats-right {
      .el-button {
        padding: 6px 11px;
        background: #f5f5ff;
        border: 1px solid #ddddff;
        border-radius: 2px;
        color: #5755ff;
        font-size: 12px;
        font-family: 'Bosch Sans Global', sans-serif;
        font-weight: 400;
        line-height: 12px;

        &:hover {
          background: #eeeeff;
          border-color: #ccccff;
        }
      }
    }
  }

  .config-table {
    flex: 1;

    .expand-content {
      padding:0 20px 20px;
      background: #fafafa;
      border-radius: 4px;
      margin: 10px 0;

      .expand-tabs {
        :deep(.el-tabs__header) {
          margin-bottom: 20px;
        }

        :deep(.el-tabs__nav-wrap) {
          &::after {
            display: none;
          }
        }

        :deep(.el-tabs__nav) {
          border: none;
          // 确保nav容器稳定，不会因为内容变化而位移
          display: flex;
          align-items: center;
        }

      
        :deep(.el-tabs__item) {
          color: #606266;
          font-size: 14px !important;
          font-weight: 400;
          margin-bottom: 4px;
          padding: 0 20px 8px 20px; // 为所有tab设置统一的padding
          transition: color 0.3s ease; // 只对颜色添加过渡效果

          &.is-active {
            color: #5755FF;
            font-weight: 600; // 激活状态下加粗
            // 移除padding变化，避免位移
          }
        }

        :deep(.el-tabs__active-bar) {
          background-color: #5755ff;
          bottom: 0px; // 增加2px距离，与padding的6px保持一致
        }
      }

      .install-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 4px;
        border: 1px solid #ebeef5;

        &:last-child {
          margin-bottom: 0;
        }

        .install-item {
          min-width: 130px;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .install-label {
            color: #909399;
            font-size: 12px;
            font-weight: 400;
            line-height: 1.2;
          }

          .install-value {
            color: #303133;
            font-size: 12px;
            font-weight: 400;
            line-height: 1.2;
          }
        }
      }

      // 附件信息样式
      .attachment-info {
        .attachment-table {
          border-radius: 4px;
          overflow: hidden;

          :deep(.el-table__header) {
            background-color: #f5f7fa;

            th {
              background-color: #f5f7fa;
              color: #606266;
              font-weight: 600;
              font-size: 12px;
            }
          }

          :deep(.el-table__body) {
            tr {
              &:hover {
                background-color: #f5f7fa;
              }
            }

            td {
              font-size: 12px;
              color: #606266;
              padding: 8px 0;
            }
          }
        }

        .no-data {
          padding: 40px 0;
          text-align: center;

          :deep(.el-empty__description) {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__header {
        th {
          background: #fafafc;
          color: #909399;
          font-size: 12px;
          font-family: 'Bosch Office Sans', sans-serif;
          font-weight: 400;
          line-height: 23px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .el-table__body {
        td {
          color: #606266;
          border-bottom: 1px solid #ebeef5;
          padding: 8px 0;
        }
      }

      // 展开列样式 - 确保箭头居中对齐
      .el-table__expand-column {
        .cell {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 !important;
          text-align: center;
        }

        .el-table__expand-icon {
          margin: 0 !important;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;

          // 确保展开/收起状态下都居中
          &.el-table__expand-icon--expanded {
            justify-content: center;
            align-items: center;
          }
        }
      }

      // 通用的居中对齐样式，适用于所有设置了align="center"的列
      .el-table__body td {
        &[style*='text-align: center'] {
          .cell {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 40px; // 确保有足够的高度进行垂直居中
          }
        }
      }

      .sensor-model-tag {
        padding: 2px 10px;
        background: #f5f5ff;
        border: 1px solid #ddddff;
        border-radius: 2px;
        color: #5755ff;
        font-size: 12px;
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        line-height: 20px;
      }

      .action-buttons-group {
        display: flex;
        .upload-btn {
          padding: 6px 11px;
          background: #12953e;
          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
          border-right: 1px solid rgba(255, 255, 255, 0.5);
          border: none;
          min-height: auto;
          color: white;

          &:hover {
            background: #1b9142;
          }
        }

        .edit-btn {
          padding: 6px 11px;
          background: #ffaf2e;
          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;
          border-right: 1px solid rgba(255, 255, 255, 0.5);
          border: none;
          min-height: auto;
          color: white;

          &:hover {
            background: #ff9f1e;
          }
        }

        .delete-btn {
          padding: 6px 11px;
          background: #ff6164;
          border-right: 1px solid rgba(255, 255, 255, 0.5);
          border: none;
          min-height: auto;
          color: white;

          &:hover {
            background: #ff5154;
          }
        }

        .view-btn {
          padding: 6px 11px;
          background: #5755ff;
          border: none;
          min-height: auto;
          color: white;

          &:hover {
            background: #4644dd;
          }
        }
      }
    }
  }
}

// 传感器编码容器样式
.sensor-code-container {
  display: flex;
  align-items: center; // 垂直居中对齐
  gap: 6px; // 警告图标和编码之间的间距
  min-height: 20px; // 确保容器有足够高度

  .warning-icon {
    width: 16px;
    height: 16px;
    fill: #ffb03a;
    cursor: pointer;
    flex-shrink: 0; // 防止图标被压缩
    display: flex; // 确保SVG内部对齐
    align-items: center;
    justify-content: center;

    &:hover {
      fill: #e6690a;
    }
  }

  .model-arch {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; // 强制单行显示，不换行
    line-height: 16px; // 与图标高度保持一致
    font-size: 14px; // 确保字体大小合适
    vertical-align: baseline; // 基线对齐
  }

  // el-tag 边框样式
  :deep(.el-tag) {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 型号规格容器样式
.model-spec-container {
  width: 100%;
  overflow: hidden;

  .model-arch {
    display: block;
    width: 100%;
    white-space: nowrap; // 强制单行显示
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
