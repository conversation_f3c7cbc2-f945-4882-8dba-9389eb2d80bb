<template>
  <div class="main-container">
    <el-tabs v-model="accessRoleTypeCode" @tab-change="handleTabClick" class="tab-container">
      <el-tab-pane :label="$t(item.name)" :name="item.code" v-for="item in tabsData" :key="item.id"> </el-tab-pane>
    </el-tabs>
    <el-scrollbar>
      <!--      <el-card-->
      <!--          v-for="roleType in classificationList"-->
      <!--          :key="roleType.roleTypeCode"-->
      <!--          class="card-margin"-->
      <!--          :id="roleType.roleTypeCode"-->
      <!--      >-->
      <el-card>
        <!--        <el-button type="success" class="title-name">{{-->
        <!--            roleType.roleTypeName-->
        <!--          }}-->
        <!--        </el-button>-->
        <div>
          <el-check-tag
            v-for="classification in classificationList.tagClassificationList"
            :key="classification.id"
            :checked="classification.checked"
            @change="handleClassificationChecked($event, classification)"
          >
            {{ classification.name }}
          </el-check-tag>
        </div>
      </el-card>
    </el-scrollbar>
  </div>
</template>
<script>
export default {
  name: 'BsTagClassificatonSelection',
  emits: ['update:modelValue', 'handleClassificationChecked', 'handleTabClick'],
  props: {
    modelValue: {
      type: Object,
      default: {}
    },
    tabsData: {
      type: Array,
      default: []
    },
    accessRoleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // classificationList: [],
      accessRoleTypeCode: '',
      // tabsData: [],
      // tagList: {},
      // tagGroupList: [],
      filterTagGroupList: []
    }
  },
  watch: {
    accessRoleType: {
      handler(val) {
        this.accessRoleTypeCode = val
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    classificationList: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  created() {},
  mounted() {
    // this.accessRoleTypeCode=this.accessRoleType
    // if (this.tabsData?.length) {
    //   this.accessRoleTypeCode = this.tabsData[0].code
    // }
  },
  methods: {
    // listTagClassification() {
    //   listUserTagClassification().then(res => {
    //     this.classificationList = res.data
    //     this.classificationList.forEach(item => {
    //       item.tagClassificationList.forEach(classification => {
    //         classification.checked = false
    //       })
    //     })
    //     this.tabsData = res.data.map(item => {
    //       return {
    //         name: item.roleTypeName,
    //         code: item.roleTypeCode
    //       }
    //     })
    //   })
    // },
    handleTabClick(tab) {
      // this.$nextTick(() => {
      //   let dom = document.getElementById(this.accessRoleType)
      //   if (dom) {
      //     dom.scrollIntoView({
      //       behavior: 'smooth'
      //     })
      //   }
      // })
      //
      this.$nextTick(() => {
        this.accessRoleTypeCode = tab
        this.$emit('handleTabClick', this.accessRoleTypeCode)
      })
    },
    close(data) {
      let obj = this.classificationList.filter(cla => cla.roleTypeCode === data.accessRoleType)
      let item = obj[0].tagClassificationList.filter(tag => tag.id === data.id)
      item[0].checked = false
    },
    handleClassificationChecked(val, item) {
      item.checked = val
      this.$emit('handleClassificationChecked', item)
    }
    // handleTagDistribute(val) {
    //   this.tagList = JSON.parse(JSON.stringify(val))
    //   if (!this.tagGroupList || this.tagGroupList.length === 0) {
    //     treeListBsTagGroup().then(res => {
    //       this.tagGroupList = res.data
    //       filterTagGroup(this.tagGroupList)
    //       setCheckedList(this.filterTagGroupList)
    //     })
    //   } else {
    //     filterTagGroup(this.tagGroupList)
    //     setCheckedList(this.filterTagGroupList)
    //   }
    // },

    // setCheckTag(list) {
    //   let treeResult = []
    //   list.forEach(item => {
    //     treeResult.push({
    //       name: item.tagList[0].groupName,
    //       id: item.tagList[0].groupId,
    //       nameCn: item.tagList[0].groupNameCn,
    //       tagList: item.tagList
    //     })
    //   })
    //   return treeResult
    // },

    // handleGroupTagsAllChecked(val, group, parentGroup) {
    //   if (group.tagList?.length > 0) {
    //     group.isIndeterminate = false
    //     group.checkedTagIdList = []
    //     group.checkedAll = val
    //     if (val) {
    //       group.tagList.forEach(tag => {
    //         group.checkedTagIdList.push(tag.id)
    //       })
    //     }
    //   }
    //   if (group.children?.length > 0) {
    //     group.isIndeterminate = false
    //     group.children.forEach(subGroup => {
    //       this.handleGroupTagsAllChecked(val, subGroup, group)
    //     })
    //   }
    //   if (parentGroup) {
    //     let parentGroupCheckedAll = true
    //     let parentGroupIsIndeterminate = false
    //     if (parentGroup.children && parentGroup.children.length > 0) {
    //       for (let sub of parentGroup.children) {
    //         if (!sub.checkedAll) {
    //           parentGroupCheckedAll = false
    //         } else {
    //           parentGroupIsIndeterminate = true
    //         }
    //         if (sub.isIndeterminate) {
    //           parentGroupIsIndeterminate = true
    //         }
    //         if (!parentGroupCheckedAll && parentGroupIsIndeterminate) {
    //           break
    //         }
    //       }
    //     }
    //     parentGroup.checkedAll = parentGroupCheckedAll
    //     parentGroup.isIndeterminate = parentGroupCheckedAll
    //       ? false
    //       : parentGroupIsIndeterminate
    //   }
    // },
    // handleTagChange(val, group, parentGroup) {
    //   const checkedCount = val.length
    //   group.checkedAll = checkedCount === group.tagList.length
    //   group.isIndeterminate =
    //     checkedCount > 0 && checkedCount < group.tagList.length
    //   if (parentGroup) {
    //     let parentGroupCheckedAll = true
    //     let parentGroupIsIndeterminate = false
    //     if (parentGroup.children && parentGroup.children.length > 0) {
    //       for (let sub of parentGroup.children) {
    //         if (!sub.checkedAll) {
    //           parentGroupCheckedAll = false
    //         } else {
    //           parentGroupIsIndeterminate = true
    //         }
    //         if (sub.isIndeterminate) {
    //           parentGroupIsIndeterminate = true
    //         }
    //         if (!parentGroupCheckedAll && parentGroupIsIndeterminate) {
    //           break
    //         }
    //       }
    //     }
    //     parentGroup.checkedAll = parentGroupCheckedAll
    //     parentGroup.isIndeterminate = parentGroupCheckedAll
    //       ? false
    //       : parentGroupIsIndeterminate
    //   }
    // },
    // saveCheckedTagList(group) {
    //   if (group.children && group.children.length > 0) {
    //     group.children.forEach(subGroup => {
    //       saveCheckedTagList(subGroup)
    //     })
    //   } else if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
    //     group.checkedTagIdList.forEach(tagId => {
    //       if (!~tagList.findIndex(val => val.id === tagId)) {
    //         tagList.push({
    //           ...group.tagMap[tagId]
    //         })
    //       }
    //     })
    //   }
    // },
    // setCheckedList(list) {
    //   let checkedCount = 0
    //   if (tagList && tagList.length) {
    //     let tagMap = {}
    //     tagList.forEach(tag => {
    //       tagMap[tag.id] = tag
    //     })
    //     list.forEach(item => {
    //       item.checkedAll = false
    //       item.isIndeterminate = false
    //       if (item.tagList && item.tagList.length > 0) {
    //         item.checkedTagIdList = []
    //         item.tagMap = {}
    //         item.tagList.forEach(tag => {
    //           item.tagMap[tag.id] = tagMap[tag.id] || tag
    //           // item.tagMap[tag.id] = tag
    //           if (tagMap[tag.id]) {
    //             item.checkedTagIdList.push(tag.id)
    //             tag.checked = true
    //           } else {
    //             tag.checked = false
    //           }
    //         })
    //         if (item.checkedTagIdList.length > 0) {
    //           checkedCount++
    //           if (item.checkedTagIdList.length === item.tagList.length) {
    //             item.checkedAll = true
    //           } else {
    //             item.isIndeterminate = true
    //           }
    //         }
    //       }
    //       if (item.children && item.children.length > 0) {
    //         checkedCount++
    //         let childrenCheckedCount = setCheckedList(item.children)
    //         if (childrenCheckedCount === item.children.length) {
    //           item.checkedAll = true
    //         } else {
    //           if (childrenCheckedCount > 0) {
    //             item.isIndeterminate = true
    //           }
    //         }
    //       }
    //     })
    //   } else {
    //     list.forEach(item => {
    //       item.checkedAll = false
    //       item.isIndeterminate = false
    //       if (item.tagList && item.tagList.length > 0) {
    //         item.checkedTagIdList = []
    //         item.tagMap = {}
    //         item.tagList.forEach(tag => {
    //           item.tagMap[tag.id] = tag
    //         })
    //       }
    //       if (item.children && item.children.length > 0) {
    //         setCheckedList(item.children)
    //       }
    //     })
    //   }
    //   return checkedCount
    // },
    // refresh() {
    //   saveCheckedTagList({ children: filterTagGroupList.value })
    //   filterTagGroup(tagGroupList.value)
    //   setCheckedList(filterTagGroupList.value)
    // }
  }
}
</script>
<style lang="scss">
.main-container {
  width: 95%;
  height: 100%;
  margin: 0 auto;

  :deep(.el-card__body) {
    :nth-child(1) {
      height: calc(100vh - 120px);
    }
  }

  .tab-container {
    position: relative;
    top: 15px;
  }
}

.card-margin {
  margin: 15px 0;

  .title-name {
    margin-left: -20px;
    margin-bottom: 10px;
  }

  .class-header {
    display: flex;
    justify-content: center;
  }
}
</style>
<style lang="scss" scoped>
.el-card {
  :deep(.el-check-tag) {
    transition: all 0.3s;
    cursor: pointer;
    margin-bottom: 5px;
    margin-right: 5px;
    user-select: none;
  }
}
</style>
