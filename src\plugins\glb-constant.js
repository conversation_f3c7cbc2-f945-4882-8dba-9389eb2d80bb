const GLB_CONFIG = {
  devUrl: {
    loginUrl: process.env.VUE_APP_LOGIN_URL,
    serviceSiteRootUrl: process.env.VUE_APP_SERVICE_ROOT_URL,
    serviceSiteRootSystemUrl: process.env.VUE_APP_SERVICE_ROOT_SYSTEM_URL,
    serviceSiteRootAnalyticsUrl: process.env.VUE_APP_SERVICE_ANALYTICS_URL,
    serviceSiteRootJcdcUrl: process.env.VUE_APP_JCDC_URL,
    serviceSiteRootMINIAPPUrl: process.env.VUE_APP_MINI_APP_URL,
    fileServer: process.env.VUE_APP_FILE_SERVER_URL,
    systemUrl: process.env.VUE_APP_SERVICE_SYSTEM_URL
    // serviceSiteRootUrl: '/server',
    // fileServer: '/file_server'
    // serviceSiteRootUrl: 'http://10.201.4.175:8086',
    // fileServer:'http://10.201.4.175:8086/files'
  },
  usingCase: process.env.VUE_APP_CAS === 'true',
  applicationId: '4aa3fc639e794d469c6445aeaff175a5',
  noAuthServerUrl: [
    process.env.VUE_APP_SERVICE_ROOT_URL + '/login',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/login',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/token',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/register',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/register/org/tree',
    process.env.VUE_APP_SERVICE_ROOT_SYSTEM_URL + '/register/org/tree',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/forget/verify_code',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/password/reset',
    process.env.VUE_APP_SERVICE_SYSTEM_URL + '/forget/auth'
  ],
  noAuthRouterPath: ['/login', '/token', '/forgetPassword', '/register'],
  tokenKey: 'token',
  defaultDataOperatePermission: {
    add: true,
    edit: true,
    remove: true,
    view: true
  },
  TxMapKey: 'K6ZBZ-2FUR6-Q6BSY-M7YWP-UWZY5-R5BCG',
  TxMapSecretKey: 'OTIm4yvryZ5XPmrqbiZUQHthvZtdy9A3'
}

export default GLB_CONFIG
