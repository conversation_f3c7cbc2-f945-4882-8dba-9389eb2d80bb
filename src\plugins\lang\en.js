// en.js
export default {
  'WAVE3': 'WAVE3',
  '请输入关键字': 'Key Words',
  '关闭': 'Close',
  '取消': 'Cancel',
  '保存': 'Save',
  '批量操作': 'Batch Operation',
  '新增': 'Add',
  '批量删除': 'Batch Remove',
  '编辑': 'Edit',
  '删除': 'Remove',
  '查看详情': 'View Detail',
  '重置': 'Reset',
  '操作': 'Operation',
  '提示': 'Reminder',
  '标定参数': 'CalibrationParameters',
  '添加': 'Add',
  '请先选择数据再执行批量操作': 'Please select the data for batch operation.',
  '这个操作会永久的删除当前选择的数据，你确定继续吗？': 'This operation will permanently delete the selected data. Are you sure you want to proceed?',
  '这个操作会删除当前选择的数据，你确定继续吗？': 'This operation will delete the selected data. Are you sure you want to proceed?',
  '这个操作会解绑当前选择的数据，你确定继续吗？': 'This operation will unbind the selected data. Do you want to continue?',
  '这个操作会授权当前的请求，你确定继续吗？': 'This operation will pass the current request. Do you want to continue?',
  '这个操作会驳回当前的请求，你确定继续吗？': 'This operation will reject the current request. Do you want to continue?',
  '绑定成功': 'Bind Successfully',
  '保存成功': 'Save Successfully',
  '更新成功': 'Update Successfully',
  '操作已取消': 'Operation Cancelled',
  '任务编号': 'Task Code',
  '任务名称': 'Task Name',
  '发起者': 'Initiator',
  '设备': 'Equipment',
  '合规官': 'Compliance Officer',
  '预计日期': 'Expected Date',
  '执行时间': 'Executing Time',
  '状态': 'Status',
  '车辆': 'Vehicle ',
  '添加打标签任务明细': 'Add Tagging Task Details',
  '修改打标签任务明细': 'Edit Tagging Task Details',
  '查看打标签任务明细': 'Tagging Task Details',
  '请输入任务编号': 'Please enter the task ID',
  '请输入车辆编号': 'Please enter the vehicle ID',
  '请输入设备编号': 'Please enter the equipment ID',
  '请输入接收人员编号': 'Please enter the recipient  employee ID',
  '请输入合规官编号': 'Please enter the compliance officer emp ID',
  '请输入开始时间': 'Please enter the start time',
  '请输入结束时间': 'Please enter the end time',
  '通过状态查询': 'Search by Status ...',
  '通过车辆识别代码查询': 'Search by Varient or Vin ...',
  '通用类型': 'Variant',
  '车辆识别代码': 'Vin',
  '持有者': 'Keeper',
  '停车位': 'Parking Slot',
  '标签设备': 'Tag Equip',
  '新增车辆': 'Add Vehicle',
  '编辑车辆': 'Edit Vehicle',
  '车辆详情': 'Vehicle Detail',
  '选择人员': 'Select Employee',
  '解绑': 'unbind',
  '请输入通用类型': 'Please enter the variant',
  '请输入车辆识别代码': 'Please enter the vin',
  '请输入状态': 'Please enter the status',
  '请输入持有者': 'Please enter the keeper',
  '请输入停车位': 'Please enter the parking slot',
  '过滤关键字': 'Filter keyword',
  '添加API': 'Add API',
  '添加模块': 'Add Module',
  '编辑模块': 'Edit Module',
  '选择模块': 'Select the module',
  '模块 / API名称': 'Module / API Name',
  'API地址': 'API Path',
  'API描述': 'API Description',
  '描述': 'Description',
  '请选择模块': 'Please select module',
  '请输入API名称': 'Please input the API Name',
  '请输入API地址': 'Please input the API Path',
  '请输入API描述': 'Please input the API Description',
  '参数': 'Parameters',
  '请求参数': 'Request Parameters',
  '请求参数示例': 'Request Parameters Example',
  '请求头部': 'Request header',
  '请求体': 'Request body',
  '请求': 'Request',
  '参数名': 'Name',
  '类型': 'Type',
  '必填': 'Required',
  '说明': 'Description',
  '插入': 'Insert',
  '添加子字段': 'Add subfield',
  'Query参数': 'Query parameters',
  '响应': 'Response',
  '响应内容': 'Response Content',
  '响应内容示例': 'Response Content Example',
  '示例': 'Example',
  '返回结果': 'Response result',
  '名称': 'Name',
  '请输入名称': 'Please enter the Name',
  '请输入排序字段': 'Please enter the Sort field',
  '请输入是否启用': 'Please enter the Enable',
  '排序字段': 'Sort field',
  '是否启用': 'Enable',
  '通过数据名称查询': 'Search by Name ...',
  '多条件搜索': 'Filter',
  '同步至数据集': 'Sync DataSet',
  '传感器': 'Modality ',
  '文件类型': 'File Type',
  '大小': 'File Size',
  '测量时间': 'Measure Time',
  '标签': 'Tags',
  '标签预览': 'Preview Tags',
  '基本信息': 'Basic Detail',
  '文件信息': 'File Detail',
  '文件路径': 'File Path',
  '标签信息': 'Tag Detail',
  '测量信息': 'Measure Detail',
  '条件选择': 'Filter Pannel',
  '任务': 'Task',
  '接收人': 'Recipient',
  '编号': 'Code',
  '搜索': 'Search',
  '选择数据集': 'Select an existing dataset',
  '创建数据集': 'Create a dataset',
  '数据集': 'Dataset',
  '请选择数据集...': 'please select dataset',
  '请选择数据集名称...': 'please input new dataset name',
  '确认': 'Confirm',
  '复制': 'Copy',
  '下载': 'Download',
  '全选': 'All',
  '请勿输入包含空格的特殊字符': 'Do not enter special characters containing spaces',
  '请勿输入包含': 'Do not enter special characters containing ',
  '请勿输入超过': 'Do not enter more than ',
  '个字符': ' characters',
  '的特殊字符': '',
  '全部': 'All',
  '待授权': 'Pending',
  '已授权': 'Passed',
  '已驳回': 'Rejected',
  '驳回': 'Reject',
  '授权': 'Pass',
  '申请': 'Apply',
  '我的数据集': 'My Dataset',
  '工作组': 'WorkGroup',
  '数据类型': 'Data Type',
  '数量': 'Amount',
  '请选择数据集类型': 'please select dataset type',
  '可访问角色': 'access role',
  '请选择可访问角色': 'please select acessRole',
  '拥有者': 'owner',
  '备注': 'remark',
  '分享数据集': 'Share Pannel',
  '是否保留数据集': 'Dataset keep',
  '是否可更新': 'Dataset updatable',
  '是': 'Yes',
  '否': 'No',
  '分享': 'Share',
  '版本': 'Version',
  '标定参数配置': 'Calibration parameters configuration',
  '标定日期': 'Calibration date',
  '选择日期': 'Pick a date',
  '点击上传': 'Click to upload',
  '只能上传xml/json文件': 'xml/json files.',
  '标定描述': 'Calibration Description',
  '标定文件': 'Calibration File',
  '编码': 'Code',
  '请输入编码': 'please input code',
  '请选择传感器': 'please select modality',
  '修改': 'Edit',
  '传感器维护记录': 'Sensor maintenance record',
  '传感器事件': 'Modality Issue',
  '日期': 'occurTime',
  '时间': 'occurTime',
  '申请级别': 'Apply Level',
  '申请应用': 'Apply Application',
  '申请原因': 'Apply Reason',
  '请选择申请级别': 'Please select Apply Level',
  '请选择申请应用': 'Please select Apply Application',
  'API申请': 'API Application',
  'API申请详情': 'API Application Detail',
  'API详情': 'API Detail',
  '模块': 'Moudle',
  '请求类型': 'Request Type',
  '请求地址': 'Request Address',
  '申请人': 'Applicant',
  '场景名称': 'Scenario Name',
  '版本号': 'Scenario Version',
  '功能场景描述': 'Function Description',
  '逻辑场景描述': 'Logic Description',
  '场景样例图': 'Scenario Instance',
  '评价指标': 'Evaluation Indicators',
  '筛选': 'Filter',
  '无数据': 'no data',
  '请输入正整数': 'Please input a positive integer',
  '详情': 'More',
  '清除': 'Reset',
  '回放': 'playback',
  '文件名': 'FileName',
  '请选择车辆': 'please select vehicle',
  '请输入文件名称': 'please input file name',
  '开始时间': 'start time',
  '结束时间': 'end time',
  '目录展示': 'catalogue',
  '文件展示': 'file',
  '列表展示': 'List',
  '次': 'Times',
  '试验车数量': 'Number of test vehicles',
  '环比': 'Month on month',
  '上月路试里程': 'Last month road test mileage',
  '上月标签统计': 'Last month tag statistics',
  '千米': 'km',
  '上月传感器异常事件': 'Last month sensor abnormal events',
  '执行中的任务': 'Tasks in progress',
  '日间标签统计': 'Daytime tag statistics',
  '目标数量': 'Target quantity',
  '选择标签': 'Select tag',
  '标签趋势统计': 'Tag trend statistics',
  '开始日期': 'Start date',
  '结束日期': 'End date',
  '到': 'To',
  '请选择': 'Please select',
  '持续性(时长)': 'Continuity(Duration)',
  '持续性(次数)': 'Continuity(Times)',
  '事件': 'Event',
  '上传成功': 'Upload successful',
  '暂无位置信息': 'No location information',
  '发布人': 'Initiator',
  '期望日期': 'Expected date',
  '请输入车牌': 'Please input vin',
  '进入': 'more',
  '请输入登录密码': 'please input password',
  '我发布的': 'publish',
  '我接收的': 'received',
  '请输入': 'please input',
  '发布': 'publish',
  '我的': 'Mine',
  '我的任务': 'My Task',
  '我的Api': 'My API',
  '我的需求': 'My Requirement',
  '车队管理': 'Fleet Management',
  '传感器管理': 'Modality Management',
  '车型管理': 'Variant management',
  '标签设备管理': 'Tag Equipment Management',
  '司机管理': 'Driver Management',
  '车辆管理': 'Vehicle Management',
  '车队': 'Fleet',
  '测试页面': 'Test Page',
  '车队场景': 'Fleet scene',
  '目录': 'Catalog',
  '数据采集': 'Data Acquisition',
  '采集需求': 'Acquisition Requirement',
  '标签记录': 'Tag Records',
  '采集任务': 'Acquisition Task',
  '数据市场': 'Data Market',
  '原始数据': 'Raw Data',
  '抽帧数据': 'Extracted Data',
  '采集项目': 'Acquisition Project',
  'V&V': 'V&V',
  '场景数据库': 'Scene Database',
  '支持': 'Support',
  '文档管理': 'Document Management',
  'API管理': 'API Management',
  'API授权': 'API Authorization',
  '系统管理': 'System Management',
  '用户名': 'User name',
  '不能为空': ' cannot be empty',
  '密码': 'Password',
  '登录': 'Sign In',
  '登 录': 'Sign In',
  '皮肤切换': 'Skin Switch',
  '忘记密码': 'Forget Password',
  '快速注册': 'Register Now',
  '请等待': 'please wait...',
  '场景定义': 'scenario definition',
  '测试用例': 'test case',
  '确定': 'confirm',
  '请输入场景名称': 'please input scenario name',
  '请输入描述': 'please input Description',
  '请选择接收人': 'please input Recipient',
  '请选择期望日期': 'please select Expected date',
  '请选择标签': 'please select Tags',
  '编辑标签': 'Edit Tags',
  '拆解': 'disassemble',
  '编辑需求': 'Edit Requirements',
  '任务拆解': 'Task Disassembly',
  '重新选择': 'Reselect',
  '需求': 'Requirements',
  '时序图': 'Sequence chart',
  '统计图': 'Statistical chart',
  '编辑任务': 'Edit Task',
  '负责人': 'Person in charge',
  '路线': 'Route',
  '司机': 'Driver',
  '副驾': 'Copilot',
  '选择': 'Choose',
  '姓名': 'User Name',
  '账号': 'Login Name',
  '邮箱': 'Email',
  '确认密码': 'Confirm Password',
  '组织架构': 'Organizational Structure',
  '请选择组织架构': 'please select Organizational Structure',
  '请输入账号': 'please input Login Name',
  '请输入正确的邮箱': 'please input correct Email',
  '用户注册': 'User Register',
  '请使用您的NT账号': 'please input your NT account',
  '请输入验证码': 'please input Verification Code ',
  '去': 'Go ',
  '欢迎登录': 'Welcome to Login',
  '注册': 'Register',
  '还没有账号': 'No account',
  '科技成就生活之美': 'Science and technology make life beautiful',
  '博世为你的美好生活，再添一份自在': 'Bosch adds a sense of freedom to your beautiful life',
  '新密码重置成功，请重新登录！': 'The new password is reset successfully. Please login again！',
  '验证码': 'Verification Code',
  '获取邮箱验证码': 'Get email verification code',
  '清空': 'clear',
  '下一步': 'next',
  '新密码': 'New Password',
  '确认新密码': 'Confirm New Password',
  '重新登录': 'ReLogin',
  '请输入新密码': 'please input New Password',
  '开始': 'Start',
  '完成': 'Finish',
  '通过数据集名称查询': 'please input dataset name',
  '修改场景成功': 'update scenario successfully',
  '修改场景分类成功': 'update scenario successfully',
  '创建者': 'Creator',
  '数据量': 'Data Amount',
  '解绑成功': 'Unbind successfully',
  '这个操作会解绑当前选择的数据，是否继续？': 'This operation will unbind the currently selected data. Continue？',
  '用户已通过': 'User passed',
  '用户已驳回': 'User rejected',
  '标签筛选': 'Tags filtering',
  '单一匹配': 'Match single',
  '全部匹配': 'Match all',
  '下载选择项': 'Download Options',
  '查看数据': 'Data View',
  '访问级别': 'Access role type',
  '共享用户是否可对数据更新': 'the dataset is updatable or not',
  '所有者': 'owner',
  '这个操作会永久的删除当前选择的数据，是否继续？': 'This operation will delete the currently selected data. Continue？',
  '标签匹配方式': 'Tag match',
  '场景': 'scenario',
  '辆': 'vehicles',
  '修改数据集成功': 'update dataset successfully',
  '请输入密码': 'please input password',
  '查看token': 'check token',
  '个人': 'Private',
  '部门': 'Department',
  '公司': 'Company',
  '请选择需要下载的数据': 'please select the data you want to download',
  '停车场': 'Car Park',
  '采集标签': 'Collection Tags',
  '发起人': 'publisher',
  '车型': 'varianr',
  '车牌': 'vin',
  '责任人': 'coordinator',
  '车端设备': 'equipment',
  '当前任务': 'current task',
  '当前位置': 'current position',
  '标签统计': 'Tag Statistics',
  '标签趋势': 'Tag Trends',
  '需求编号': 'Code',
  '需求名称': 'Name',
  '需求发起人': 'Publisher',
  '已完成': 'finished',
  '草稿': 'draft',
  '已发布': 'published',
  '查看标签': 'View Tags',
  '用户校验': 'User Verification',
  '请输入当前用户': 'Please enter the login password of the current user ',
  '的登录密码进行二次校验：': ' for seco-ndary verification:',
  '停车场类型': 'Parking Type',
  '人防的': 'Civil Air Defense',
  '限高(米)': 'Limited Height(meter)',
  '层数': 'Floors',
  '地面材质': 'Pavement Material',
  '总车位': 'Total Parking Space',
  '停车数': 'ParkingVolume',
  '走道是否有箭头标识': 'Arrow Sign in the Corridor',
  '是否安装减速带': 'Speed Bump',
  '亮度': 'Brightness',
  '是否有人防工程标识': 'Civil Air Logo',
  '是否有升降车位': 'Lift Parking Space',
  '停车位类型': 'Parking Space Type',
  '停车位配置': 'Parking Space Configuration',
  '停车位长度(米)': 'Parking Space Length(meter)',
  '停车位宽度(米)': 'Parking Space Width(meter)',
  '车位线颜色': 'Parking Line Color',
  '车位线状态': 'Parking Line Status',
  '车位内部颜色': 'Parking Inner Color',
  '请选择发布人': 'please select publisher',
  '编辑停车场': 'Edit Parking Lot',
  '停车场明细': 'Parking Lot Details',
  '位置': 'Position',
  '停车场信息': 'Parking Lot Information',
  '停车位信息': 'Parking Spacing Information',
  '是否人防': 'Civil Air Defense',
  '车位类型': 'Parking Space Type',
  '长度(米)': 'Parking Space Length(meter)',
  '配置': 'Parking Space Configuration',
  '宽度(米)': 'Parking Space Width(meter)',
  '长度': 'Parking Space Length(meter)',
  '宽度': 'Parking Space Width(meter)',
  '问题描述': 'Description',
  '参数类型': 'Parameters Type',
  '生效日期': 'Effective Date',
  '数据大小': 'Data Size',
  '落盘时间': 'Record Time',
  '场景：': 'scene：',
  '请选择场景': 'please select scene',
  '暂无数据': 'no data',
  '修改数据集': 'Edit Dataset',
  '请选择数据集': 'please select dataset',
  '请输入数据集名称': 'please input new dataset name',
  '请选择数据集或者输入新的数据集名称': 'please select dataset or input new dataset name',
  '请选择数据类型': 'please select data type',
  '请选择访问级别': 'please select access role',
  '请确认共享用户是否可对数据更新': 'please confirm the dataset is updatable or not',
  '请选择可访问级别': 'please select access role',
  '请确认是否保留数据': 'please confirm whether to keep the data',
  '复制成功': 'Finished copy',
  '开始下载': 'Start downloading',
  '传感器：': 'modality：',
  '文件大小': 'File Size',
  '源数据': 'raw data',
  '返回': 'Back',
  '请至少保留一个': 'Please keep at least one',
  '请输入类型': 'please input type',
  '请输入品牌': 'please input brand',
  '请输入型号': 'please input model',
  '请输入系统': 'please input system',
  '根据状态搜索': 'search by status',
  '根据编码搜索': 'search by code',
  '品牌': 'brand ',
  '型号': 'model',
  '系统': 'system',
  '编辑数据成功': 'edit data successfully',
  '添加数据成功': 'add data successfully',
  '删除数据成功': 'delete data successfully',
  '5syncFrame统计数据数量': 'Frame Set*No.(1 SyncFrame/5s,Movingonly)',
  '5SyncFrame统计数据大小': '1 SyncFrame/5second(Moving+Door)',
  '1syncFrame统计数据数量': 'Frame Set*No.(1 SyncFrame/s)',
  '1syncFrame统计数据大小': '1 SyncFrame/second',
  '数据日期': 'Data Date',
  '数据包类型': 'Bags Type',
  '文件地址': 'File Path',
  '采集时间': 'Measurement Time',
  '注入时间': 'Ingest Time',
  '原始数据包': 'Raw Data Bag',
  '数据处理状态': 'Data Processing Status',
  '电话号码格式不正确': 'The phone number format is incorrect',
  '单位': 'unit',
  '查看': 'view',
  '我的桌面': 'My Desktop',
  '数据面板': 'Data Panel',
  '采集任务记录': 'Acquisition Task Record',
  '数据一览大屏': 'Data Screen',
  '场景测试': 'Scenario Test',
  'TagV配置': 'TagV Configuration',
  'TagV回放': 'TagV PlayBack',
  '开发者支持': 'Developer Support',
  '数据相关': 'Data Correlation',
  '机构管理': 'Organizational Management',
  '用户管理': 'User Management',
  '人员管理': 'Employee Management',
  '应用管理': 'Application Management',
  '菜单管理': 'Menu management',
  '角色管理': 'Role Management',
  '计量单位': 'Unit of Measure',
  '字典管理': 'Dictionary Management',
  '工作组管理': 'WorkGroup Management',
  '模块管理': 'Module Management',
  '图标管理': 'Icon Management',
  '部门管理': 'Department Management',
  '标签管理': 'Tag Management',
  '按钮管理': 'Button Management',
  '角色类型管理': 'Role Type Management',
  '车辆该日数据统计成功': 'Vehicle data statistics on that day succeeded',
  '数据包数量': 'Bag Amount',
  '数据数量': 'Data Amount',
  '数据标记': 'Data Mark',
  '中文名称': 'Chinese Name',
  '前置时长': 'Previous Duration',
  '后置时长': 'Following Duration',
  '是否有效': 'Enabled',
  '互斥的': 'Mutually Exclusive',
  '最小持续时长': 'Min Continuous Duration',
  '最大持续时长': 'Max Continuous Duration',
  '瞬时': 'transient',
  '持续': 'continuous',
  '次数': 'Times',
  '时长': 'Duration',
  '是否异常': 'Is abnormal',
  '标签类型': 'Tag type',
  '标签次数': 'Tag times',
  '标签时间': 'Tag time',
  '最小次数': 'Min times',
  '最小时间': 'Min time',
  '异常标签统计': 'Abnormal label statistics',
  '标签名': 'Tag name',
  '来源': 'source',
  '高度': 'height',
  '米': 'm',
  '车型类型': 'Variant type',
  '安装': 'Installation ',
  '供应商': 'supplier',
  '数': 'count',
  '弹出窗': 'Pop up window',
  '回标结果': 'Back result',
  '数据回放': 'data replay',
  '数据加载中请稍后': 'Data loading, please wait',
  '退出': 'exit',
  '序号': 'No',
  '车辆状态': 'Vehicle status',
  '地点': 'Place',
  '数据统计': 'Data statistics',
  '采集次数': 'Collection times',
  '采集时长': 'Collection duration',
  '正常': 'normal',
  '预览pcd': 'preview pcd',
  '该帧无pcd文件': 'There is no pcd file in this frame',
  '多帧pcd解析功能稍后开放': 'Multi frame pcd parsing function will be opened later',
  '暂存': 'Staging',
  '批量送标': 'Batch Send Labeling',
  '选择供应商': 'Select supplier',
  '回标任务记录': 'Labeled Data Receive Record',
  '区域': 'Area',
  '环境类型': 'Environment type',
  '地面': 'Ground',
  '地上': 'Aboveground',
  '地下': 'Underground',
  '垂直库位': 'Vertical storage location',
  '斜向库位': 'Oblique storage location',
  '平行库位': 'Parallel storage location',
  '明亮': 'Bright',
  '一般': 'Common',
  '油漆': 'Paint',
  '水泥': 'Cement',
  '柏油': 'Tar',
  '环氧地坪': 'Epoxy floor',
  '草地': 'Grassland',
  '砖块': 'Brick',
  '轮胎挡板': 'Tire baffle',
  '无': 'Nothing',
  '黄色': 'Yellow',
  '白色': 'White',
  '灰色': 'Grey',
  '绿色': 'Green',
  '蓝色': 'Blue',
  '红色': 'Red',
  '粉色': 'Pink',
  '橙色': 'Orange',
  '紫色': 'purple',
  '花色': 'Decor',
  '反光': 'Reflective',
  '实线': 'Solid line',
  '有损坏': 'Damaged',
  '地址': 'Address',
  '预览': 'preview',
  'Parking': 'Parking',
  'Driving': 'Driving',
  '失效日期': 'Expiration Date',
  '存储类型': 'Storage Type',
  '修改时间': 'Update Time',
  '送标任务记录': 'Sendlabeling Task Record',
  '标注项目': 'Labeled Project',
  'BEV数据': 'BEV Frame Data',
  '回标数据': 'Labeled Data',
  '文件存储管理': 'File Storage Management',
  '您选的记录已开始下载': 'The record you selected has started downloading',
  '请确认是否发布该装车记录': 'Please confirm whether to publish the loading record',
  '通过传感器编码/名称查询': 'Query by sensor code/name',
  '传感器类型': 'Sensor Type',
  '车辆信息': 'Vehicle Info',
  '传感器配置': 'Modality Config',
  '软件版本': 'Software Version',
  '照片': 'Photo',
  '采集数据包类型': 'Collection Packet Type',
  '车端数据落盘模板': 'Car end data transfer template',
  '云端数据存储模板': 'Cloud Data Storage Template',
  '雷达时间戳定义角度': 'Radar Timestamp Defined Angle',
  '出厂车架号': 'factory frame number',
  '轴距': 'wheelbase',
  '采集车辆宽度（不考虑反光镜）': 'Capture vehicle width (reflectors are not considered)',
  '采集车辆宽度（考虑反光镜）': 'Capture vehicle width (consider reflectors)',
  '车辆质量重心到车头的距离': 'The distance from the center of gravity of the vehicle to the front of the vehicle',
  '车辆质量重心到车尾的距离': 'The distance from the center of gravity of the vehicle to the rear of the vehicle',
  '后轴中心到车辆几何中心的距离': 'Distance from the center of the rear axle to the geometric center of the vehicle',
  '规格': 'Specification',
  '位置x': 'position x',
  '位置y': 'position y',
  '位置z': 'position z',
  '距地高度': 'Height from ground',
  'calibrationKey': 'calibrationKey',
  'msop': 'msop',
  'difop': 'difop',
  'ASW版本': 'ASW version',
  'BSW版本': 'BSW version',
  '上一步': 'Previous',
  '上传附件': 'Upload File',
  '硬盘流转': 'hard disk transfer',
  '新车辆管理': 'new vehicle management',
  '车端故障': 'Vehicle failure',
  '点检记录': 'Check record',
  '检查项管理': 'Check item management',
  '无效原因类型': 'invalid reason type',
  '无效原因': 'invalid reason',
  '投影配置': 'projection configuration',
  '质量检查记录': 'Quality Check Record',
  '下载标签数据': 'Download label data',
  '数据处理记录': 'data processing records',
  'HOL数据': 'HOL Data',
  '指导位置x': 'guide position x',
  '指导位置y': 'guide position y',
  '指导位置z': 'guide position z',
  '图片中坐标': 'Coordinates in the picture',
  '标签选择': 'label selection',
  '标定工具版本': 'Calibration tool version',
  '标定记录': 'Calibration record',
  'dbc参数配置': 'dbc parameter configuration',
  '车辆配置': 'vehicle configuration',
  '传感器型号': 'Sensor model',
  '请输入传感器类型': 'Please enter sensor type',
  '取 消': 'cancel',
  '保 存': 'save',
  '请输入车架号': 'Please enter the frame number',
  '请输入车型版本': 'Please enter the model version',
  '发布日期': 'release date',
  '车辆用途': 'vehicle use',
  '车辆型号': 'vehicle model',
  '请选择车型': 'Please select a model',
  '别名': 'alias',
  '请输入保单号': 'Please enter policy number',
  '保单号': 'Policy number',
  '请输入车辆vin': 'Please enter the vehicle vin',
  '请输入硬盘编号': 'Please enter the hard disk number',
  '请输入硬盘序列号': 'Please enter the hard disk serial number',
  '请输入硬盘容量': 'Please enter the hard disk capacity',
  '车辆vin': 'vehicle vin',
  '硬盘编号': 'hard disk number',
  '硬盘容量': 'hard disk capacity',
  '硬盘序列号': 'hard disk serial number',
  '硬盘流转记录': 'Hard Disk Flow Record',
  '新增车型版本': 'New model version',
  '车型配置': 'Vehicle configuration',
  'mbag': 'mbag',
  'rosbag': 'rosbag',
  'sensor_data': 'sensor_data',
  '帧同步差前偏移值': 'Offset value before frame synchronization difference',
  '帧同步差后偏移值': 'Offset value after frame synchronization difference',
  '抽帧有效运行gap值': 'Frame extraction effectively runs the gap value',
  '帧同步偏移值(ms)': 'frame sync offset(ms)',
  '间隔差的基准值': 'Baseline value for interval difference',
  '文件列表': 'File list',
  '安装位置': 'installation location',
  '关 闭': 'close',
  '查看流转记录': 'View transfer records',
  '硬盘编码': 'Hard disk encoding',
  '目标位置': 'target location',
  '快递单号': 'tracking number',
  '操作类型': 'operation type',
  '操作时间': 'operating time',
  '操作人': 'Operator',
  '请输入备注': 'Please enter a note',
  '司机姓名': 'driver name',
  '合规员姓名': 'Compliance Officer Name',
  '事件类型': 'event type',
  '事件描述': 'event description',
  '第三方对接人': 'third party contact person',
  '点检分类': 'Check classification',
  '合规员': 'Compliance officer',
  '是否数据采集': 'Whether to collect data',
  '车辆的数据录制': 'vehicle data recording',
  '车辆sensor连接情况': 'Vehicle sensor connection',
  '请输入分组名': 'Please enter a group name',
  '车型版本详情': 'Model Version Details',
}
