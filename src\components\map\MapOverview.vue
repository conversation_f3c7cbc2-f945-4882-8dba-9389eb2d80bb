<template>
  <div style="position: relative">
    <div id="allmap" ref="allmap" />
    <div class="category-list">
      <div class="category-item">
        <span class="name">时间:</span
        ><span
          class="num"
          v-text="list && list.length && list[pointIndex].collectTime"
        ></span>
      </div>
      <div class="category-item">
        <span class="name">经度:</span>
        <span
          class="num working"
          v-text="list && list.length && list[pointIndex].latitude"
        ></span>
      </div>
      <div class="category-item">
        <span class="name">纬度:</span>
        <span
          class="num working"
          v-text="list && list.length && list[pointIndex].longitude"
        ></span>
      </div>
    </div>
    <el-slider
      @input="changePoint"
      v-model="pointIndex"
      :max="pointIndexMax"
    ></el-slider>
  </div>
</template>

<script>
import myMap from '@/plugins/map/baiduMap'
import { ElSlider } from 'element-plus'
// import LuShu from 'bmaplib.lushu'

export default {
  name: 'Map',
  data() {
    return {
      map: '',
      BMap: '',
      list: [],
      convertorPoints: [],
      lushu: '',
      pointIndex: 0,
      pointStart: 0,
      pointEnd: 0,
      pointIndexMax: 0,
      points: []
    }
  },
  components: {
    ElSlider
  },
  mounted() {},
  methods: {
    async show(list, theme) {
      this.list = list
      // this.list = list && list.reverse()
      await myMap.init(this.$refs.allmap, theme)
      if (!list?.length) {
        return
      }
      this.pointIndexMax = list.length - 1
      this.drawPolyline()
    },
    drawPolyline() {
      myMap.doLushu(this.list, [])
      this.doEvtLer()
    },
    doEvtLer(type = 'add') {
      if (type !== 'add') {
        document.removeEventListener('moveNext', this.eventFun, false)
      } else {
        document.addEventListener('moveNext', this.eventFun, false)
      }
    },
    eventFun(evt) {
      this.pointIndex = evt.index
      myMap.playGreenLine(evt.index, 1)
    },
    changePoint() {
      myMap.moveNext(this.pointIndex)
    }
  }
}
</script>

<style lang="scss" scoped>
#allmap {
  height: 100%;
  :deep(.anchorBL) {
    display: none;
  }
}

#mapContent {
  border: none;
  height: 100%;
  width: 100%;
  margin: 0px;
  padding: 0px;
}
.category-list {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(6, 30, 93, 0.6);
  padding: 10px 15px;
  // font-weight: bold;
  color: #fff;
  .category-item {
    display: flex;
    line-height: 16px;
    .name {
      display: flex;
      margin-right: 10px;
      // width: 150px;
    }
    .num {
      font-size: 12px;
      // &.free {
      //   color: rgb(50, 94, 218);
      // }
      // &.working {
      //   color: rgb(237, 45, 45);
      // }
    }
  }
}
</style>
