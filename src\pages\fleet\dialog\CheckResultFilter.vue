<template>
  <el-dialog
    v-model="visible"
    width="45%"
    @close="dialogClosed"
    @open="dialogOpened"
    draggable
    top="20vh"
  >
    <el-form
      :model="queryParam"
      label-width="80px"
      ref="formRef"
      style="width: 100%"
    >
      <el-form-item :label="$t('车辆')" prop="vin">
        <bs-vehicle-selection
          :data="vehicleList"
          :auto-load="false"
          modelCode="vin"
          v-model="queryParam.vin"
          ref="vehicleSelectionRef"
          :clearable="true"
          filterable
          @clear="queryParam.vin = undefined"
        ></bs-vehicle-selection>
      </el-form-item>
      <!-- <el-form-item :label="$t('开始日期')" prop="startTime">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="queryParam.startTime"
          type="date"
          :disabled-date="disabledStartDate"
          clearable
          @clear="queryParam.startTime = undefined"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('结束日期')" prop="endTime">
        <el-date-picker
          value-format="YYYY-MM-DD"
          v-model="queryParam.endTime"
          type="date"
          :disabled-date="disabledEndDate"
          clearable
          @clear="queryParam.endTime = undefined"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item :label="$t('时间')" prop="measureTime">
        <el-date-picker
          v-model="queryParam.measureTime"
          type="datetimerange"
          range-separator="To"
          :start-placeholder="$t('开始时间')"
          :end-placeholder="$t('结束时间')"
          value-format="YYYY-MM-DD HH:mm:ss"
          clearable
          @clear="queryParam.measureTime = undefined"
          teleported
          placement="top"
        />
      </el-form-item>
      <el-form-item :label="$t('状态')" prop="status">
        <el-select v-model="status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="true"></el-option>
          <el-option label="异常" value="false"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogClosed">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="filter">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import BsVehicleSelection from '@/components/fleet/BsVehicleSelection.vue'
import { showToast } from '@/plugins/util'
const defaultFormData = {}
export default {
  components: {
    BsVehicleSelection
  },
  name: 'CheckResultFilter',
  emits: ['update:modelValue', 'filter', 'filterClose', 'filterOpen'],
  props: {
    // visible: {
    //   type: Boolean,
    //   default: false
    // },
    modelValue: {
      type: Object,
      default: {
        updatable: false,
        markSingleMatch: false,
        tagMatch: true
      }
    }
  },
  data() {
    return {
      visible: false,
      status: '',
      vehicleList: [],
      tagList: undefined
    }
  },
  watch: {},
  computed: {
    queryParam: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.listVehicle()
      this.visible = true
    },
    listVehicle() {
      listBsVehicle().then(res => {
        this.vehicleList = res.data
      })
    },
    handleToArray() {
      if (this.queryParam.startTime) {
        this.queryParam.measureTime = []
        this.queryParam.measureTime.push(
          this.queryParam.startTime,
          this.queryParam.endTime
        )
      }
    },
    dialogClosed() {
      this.visible = false
    },
    dialogOpened() {
      this.handleToArray()
    },
    disabledEndDate(val) {
      if (this.queryParam.startTime) {
        return (
          new Date(val) <
          new Date(this.queryParam.startTime).getTime() - 1000 * 60 * 60 * 24
        )
      }
    },
    disabledStartDate(val) {
      if (this.queryParam.endTime) {
        return new Date(val) > new Date(this.queryParam.endTime).getTime()
      }
    },

    handleParam() {
      this.queryParam.startTime =
        this.queryParam.measureTime && this.queryParam.measureTime[0]
      this.queryParam.endTime =
        this.queryParam.measureTime && this.queryParam.measureTime[1]
      delete this.queryParam.measureTime
      this.queryParam.status =this.status
    },
    filter() {
      this.handleParam()
      this.visible = false
      this.$emit('filter')
    }
  }
}
</script>
<style>
.cascader-container {
  height: 190px;
}
.cascader-container > .el-cascader-panel {
  height: 100%;
}
</style>
<style lang="scss" scoped></style>
