<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item :label="$t('传感器')" prop="sensor_id">
            <el-select
              v-model="form.sensor_id"
              filterable
              :placeholder="$t('请选择') + $t('传感器')"
              clearable
              id="modality"
              @change="getModalityName"
            >
              <el-option v-for="item in modalityData" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车架号')" prop="vin">
            <el-select v-model="form.vin" filterable :placeholder="$t('请选择') + $t('车架号')" clearable id="vin">
              <el-option v-for="item in bsVehicleList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('型号')" prop="model">
            <ltw-input v-model="form.model" :disabled="formReadonly" id="model"></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('配置信息端口')" prop="difop">
            <ltw-input v-model="form.difop" :disabled="formReadonly" id="difop" type="number"> </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('数据端口')" prop="msop">
            <ltw-input v-model="form.msop" :disabled="formReadonly" id="msop" type="number"> </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('安装') + $t('高度')" prop="height">
            <ltw-input v-model="form.height" :disabled="formReadonly" id="height" type="number">
              <template #append>
                <span>{{ $t('毫米') }}</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'x'" prop="positionX">
            <ltw-input v-model="form.positionX" :disabled="formReadonly" id="positionX" type="number">
              <template #append>
                <span>{{ $t('毫米') }}</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'y'" prop="positionY">
            <ltw-input v-model="form.positionY" :disabled="formReadonly" id="positionY" type="number">
              <template #append>
                <span>{{ $t('毫米') }}</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('指导位置') + 'z'" prop="positionZ">
            <ltw-input v-model="form.positionZ" :disabled="formReadonly" id="positionZ" type="number">
              <template #append>
                <span>{{ $t('毫米') }}</span>
              </template>
            </ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              filterable
              :placeholder="$t('请选择') + $t('供应商')"
              clearable
              @change="getSupplierName"
              id="supplierId"
            >
              <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('安装') + $t('位置')" prop="installationPosition">
            <ltw-input
              v-model="form.installationPosition"
              :disabled="formReadonly"
              id="installationPosition"
            ></ltw-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('备注')" prop="remark">
            <ltw-input
              v-model="form.remark"
              :disabled="formReadonly"
              textType="remark"
              type="textarea"
              id="remark"
            ></ltw-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  saveFtmVariantMappingModality,
  updateFtmVariantMappingModality
} from '@/apis/fleet/ftm-variant-mapping-modality'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import { listFtmVehicleModality } from '@/apis/fleet/ftm-vehicle-modality'
import { isTwoFloorValidity } from '@/plugins/util'
import { listVehicleVinList } from '@/apis/fleet/ftm-vehicle-insurance'

const defaultform = {}
export default {
  name: 'AddModalityNew',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        modality: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('传感器'),
            trigger: 'change'
          }
        ],
        model: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('型号'),
            trigger: 'blur'
          }
        ],
        installationHeight: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('安装') + this.$t('高度'),
            trigger: 'blur'
          },
          { validator: isTwoFloorValidity, trigger: 'blur' }
        ],
        supplierId: [
          {
            required: true,
            message: this.$t('请选择') + this.$t('供应商'),
            trigger: 'change'
          }
        ],
        location: [
          {
            required: true,
            message: this.$t('请输入') + this.$t('安装') + this.$t('位置'),
            trigger: 'blur'
          }
        ]
      },
      id: '',
      supplierList: [],
      modalityData: [],
      index: '',
      bsVehicleList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      // 保存后修改父级数据
      this.index = row.index
      // 判断是否更新调用接口
      this.id = row.data.id
      this.form.variant = row.data.variant
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增') + this.$t('传感器')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑') + this.$t('传感器')
          this.form = JSON.parse(JSON.stringify(row.data))
          // this.getSysDriver(row)
          break
        case 'view':
          this.dialogTitle = this.$t('传感器') + this.$t('详情')
          this.form = JSON.parse(JSON.stringify(row.data))
          // this.getSysDriver(row)
          break
      }
      this.listFtmVehicleModality()
      this.listSysRoleOrg()
      this.listVehicleVinList()
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.id) {
          if (this.dialogStatus === 'add') {
            saveFtmVariantMappingModality(this.form).then(res => {
              this.cancel()
            })
          }
          if (this.dialogStatus === 'edit') {
            updateFtmVariantMappingModality(this.form).then(res => {
              this.cancel()
            })
          }
        } else {
          this.cancel({ form: this.form, index: this.index })
        }
      })
    },
    listSysRoleOrg() {
      if (!this.supplierList?.length) {
        listSysRoleOrg({
          tagCode: 'org_supplier_fleet'
        }).then(res => {
          this.supplierList = res.data
        })
      }
    },

    listFtmVehicleModality() {
      if (!this.modalityData?.length) {
        listFtmVehicleModality().then(res => {
          this.modalityData = res.data
        })
      }
    },
    getSupplierName(val) {
      let supplierItem = this.supplierList.find(item => item.id === val)
      this.form.supplierName = supplierItem.name
    },
    getModalityName(val) {
      let modalityItem = this.modalityData.find(item => item.code === val)
      this.form.modalityName = modalityItem.name
    },

    listVehicleVinList() {
      if (!this.bsVehicleList?.length) {
        listVehicleVinList().then(res => {
          this.bsVehicleList = res.data
        })
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
