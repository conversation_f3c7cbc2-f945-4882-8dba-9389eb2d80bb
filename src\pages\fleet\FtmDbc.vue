<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
              :type="item.buttonStyleType"
              :key="item.id"
              v-for="item in outlineFunctionList"
              @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
              @command="handleCommand"
              class="batch-operate-btn"
              v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
          :data="pageData.records"
          stripe
          border
          @selection-change="handleSelectionChange"
          row-key="id"
          ref="tableRef"
      >
        <el-table-column header-align="center" align="center" type="selection" width="55"></el-table-column>
        <el-table-column header-align="center" align="center" prop="variantName" :label="$t('车型')"></el-table-column>
        <el-table-column header-align="center" align="center" prop="fileName" :label="$t('文件名')">
          <template #default="scope">
            <el-link type="primary" :underline="false" @click="handleDownload(scope.row.fileId)">
              <span> {{ scope.row.fileName }}</span>
            </el-link>
          </template
          >
        </el-table-column>
<!--        <el-table-column label="文件大小" prop="size" show-tooltip-when-overflow>-->
<!--          <template #default="scope">-->
<!--            <el-tag>{{ checkFileSize(scope.row.fileSize).data + checkFileSize(scope.row.fileSize).unit }}</el-tag>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
            header-align="center"
            align="center"
            prop="createTime"
            :label="$t('上传日期')"
        ></el-table-column>
        <el-table-column header-align="center" align="center" prop="enable" :label="$t('是否有效')">
          <template #default="scope">
            <span v-if="scope.row.enable">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center"  min-width="200" :label="$t('绑定车辆')">
          <template #default="scope" >
            <div class="tag-group">
              <el-tag v-for="(item, index) in scope.row.vinList" :key="index">{{ item }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column header-align="center" align="center" prop="remark" :label="$t('备注')"></el-table-column>-->
        <el-table-column header-align="center" align="center" :label="$t('操作')" width="200">
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                  :key="item.id"
                  v-for="item in inlineFunctionList"
                  effect="dark"
                  :content="$t(item.name)"
                  placement="top"
                  :enterable="false"
              >
                <el-button :type="item.buttonStyleType" size="small" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParam.current"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="queryParam.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
      >
      </el-pagination>
    </el-card>

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened" destroy-on-close>
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item :label="$t('车型')" prop="variantId">
          <!-- <el-input v-model="formData.variantId" :disabled="formReadonly"></el-input> -->
          <el-select
              id="variantId"
              v-model="formData.variantId"
              :disabled="formReadonly"
              filterable
              :placeholder="$t('请选择') + $t('车型')"
              clearable
          >
            <el-option
                v-for="item in vehicleVariantList"
                :key="item.id"
                :label="item.code + '(' + item.useTypeName + ')'"
                :value="item.id"
            >
              <div>{{ item.code }}({{ item.useTypeName }})</div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('dbc文件')" prop="fileIds">
          <upload-file
              accept=".dbc"
              ref="uploadDbc"
              :disabled="formReadonly"
              :limit="1"
              :source-id="formData.id"
              source-type="dbc_file"
              listType="text"
              v-model="formData.fileIds"
              class="file-btn"
          />
        </el-form-item>
        <el-form-item :label="$t('是否有效')" prop="enable">
          <el-radio-group v-model="formData.enable" :disabled="formReadonly">
            <el-radio :label="true">{{ $t('启用') }}</el-radio>
            <el-radio :label="false">{{ $t('禁用') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="dialogStatus === 'view'">
            <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
            <el-button
                :type="item.buttonStyleType"
                @click="executeButtonMethod(currentButton)"
                v-if="currentButton && currentButton.name"
            >{{ $t(currentButton.name) }}</el-button
            >
          </template>
          <template v-else>
            <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
            <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
  <ftm-dbc-detail ref="FtmDbcDetail"/>
</template>

<script>
import FtmDbcDetail from '@/pages/fleet/dialog/FtmDbcDetail.vue'
import {saveFtmDbc, updateFtmDbc, deleteFtmDbc, pageFtmDbc, getFtmDbc} from '@/apis/fleet/ftm-dbc'
import {listFtmVehicleVariant} from '@/apis/fleet/ftm-vehicle-variant'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {showToast, showConfirmToast} from '@/plugins/util'
import UploadFile from '@/components/system/UploadFile.vue'
import util from '@/plugins/util'
import GLB_CONFIG from '@/plugins/glb-constant'

const defaultFormData = {
  enable: true
}
export default {
  name: 'FtmDbc',
  components: {
    UploadFile,
    FtmDbcDetail
  },
  data() {
    return {
      token: util.getToken(),
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      vehicleVariantList: [],
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      formData: Object.assign({}, defaultFormData),
      formRules: {
        variantId: [{required: true, message: this.$t('请输入车型id'), trigger: 'blur'}],
        enable: [{required: true, message: this.$t('请输入是否有效'), trigger: 'blur'}],
        fileIds: [{required: true, message: this.$t('请上传dbc文件'), trigger: 'blur'}]
      },
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
    this.listVehicleVariant()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    handleDownload(sourceId) {
      window.open(this.downloadUrl + sourceId + '?token=' + util.getToken())
    },
    checkFileSize(data) {
      if (data > 0 && data < Math.pow(2, 10)) {
        return {data, unit: 'B'}
      } else if (data >= Math.pow(2, 10) && data < Math.pow(2, 20)) {
        return {data: parseFloat((data / 1024).toFixed(2)), unit: 'KB'}
      } else if (data >= Math.pow(2, 20) && data < Math.pow(2, 30)) {
        return {data: parseFloat((data / 1024 / 1024).toFixed(2)), unit: 'M'}
      } else if (data >= Math.pow(2, 30) && data < Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'G'
        }
      } else if (data >= Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'T'
        }
      } else {
        return {data: 0, unit: ''}
      }
    },
    listVehicleVariant() {
      listFtmVehicleVariant().then(res => {
        this.vehicleVariantList = res.data
      })
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageFtmDbc(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    save() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          saveFtmDbc(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmDbc(this.formData).then(() => {
            this.dialogVisible = false
            this.query()
          })
        }
      })
    },
    edit(row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getFtmDbc(row.id).then(res => {
        this.$nextTick(function () {
          this.formData = res.data
        })
        this.dialogVisible = true
      })
    },
    view(row) {
      this.$refs.FtmDbcDetail.show(row)
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({id: row.id})
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ids})
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmDbc(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {
    },
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
.tag-group {
  .el-tag {
    margin-right: 5px;
  }
}
</style>
