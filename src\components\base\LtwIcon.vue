<template>
  <div class="ltw-icon">
    <el-icon :class="iconClass" v-if="isElIcon === 'el'">
      <component :is="elIconName"></component>
    </el-icon>
    <svg v-else-if="isElIcon === 'svg'" class="svg-icon" aria-hidden="true">
      <use :xlink:href="'#svg-' + elIconName" />
    </svg>
    <i :class="iconCode" v-else></i>
  </div>
</template>

<script>
import {
  Download,
  Unlock,
  Connection,
  Edit,
  Delete,
  Finished,
  Timer,
  Histogram,
  CopyDocument,
  Pointer,
  AddLocation
} from '@element-plus/icons'
import { ElIcon } from 'element-plus'

export default {
  name: 'LtwIcon',
  data() {
    return {}
  },
  components: {
    ElIcon,
    Download,
    Unlock,
    Connection,
    Edit,
    Delete,
    Finished,
    Timer,
    Histogram,
    CopyDocument,
    Pointer,
    AddLocation
  },
  computed: {
    isElIcon() {
      // 新增svg格式
      if (this.iconCode) {
        if (~this.iconCode.indexOf('el-icon-')) {
          return 'el'
        } else if (~this.iconCode.indexOf('svg-')) {
          return 'svg'
        }
      }
      return false

      // // 原代码
      // if (!this.iconCode) {
      //   return false
      // }
      // return this.iconCode.indexOf('el-icon-') !== -1
    },
    elIconName() {
      return this.iconCode.trim().replace('el-icon-', '')
    }
  },
  props: {
    iconCode: String,
    iconClass: String
  }
}
</script>

<style scoped lang="scss">
.ltw-icon {
  display: inline-block;
  line-height: 100%;
  .svg-icon {
    width: 1em;
    height: 1em;
    fill: currentColor;
    vertical-align: middle;
  }
}
</style>
