<template>
    <el-container>
        <el-aside :width="getAsideWidth">
            <div class="toggle-button" @click="toggle">|||</div>
            <el-menu
                    :uniqueOpened="true"
                    :default-active="defaultActiveMenu"
                    background-color="#2E323A"
                    text-color="#fff"
                    active-text-color="#ffd04b"
                    :collapse="collapsed"
                    :collapse-transition="false"
                    router
                    ref="menuRef"
                    :key="eleKey"
            >
                <template v-for="menu in menuList" :key="menu.id">
                    <el-sub-menu :index="menu.pageUrl?menu.pageUrl:menu.id" v-if="!menu.asLeaf">
                        <template #title>
                            <i :class="menu.iconCode"></i>
                            <span>{{menu.name}}</span>
                        </template>
                        <el-menu-item :index="child.pageUrl" v-for="child in menu.children" :key="child.id">
                            <template #title>
                                <i :class="child.iconCode"></i>
                                <span>{{child.name}}</span>
                            </template>
                        </el-menu-item>
                    </el-sub-menu>
                    <el-menu-item :index="menu.pageUrl" v-if="menu.asLeaf">
                        <template #title>
                            <i :class="menu.icon"></i>
                            <span>{{menu.name}}</span>
                        </template>
                    </el-menu-item>
                </template>
            </el-menu>

        </el-aside>
        <el-container>
            <el-main>
                <router-view></router-view>
            </el-main>
<!--            <el-footer>Footer</el-footer>-->
        </el-container>
    </el-container>
</template>

<script>
    import {treeListSysPrivilegeMenuOfCurrentUser} from '@/apis/system/sys-privilege-menu'
    import GLB_CONFIG from '@/plugins/glb-constant'

    export default {
        name: "SystemManage",
        components: {},
        data() {
            return {
                menuList: [],
                collapsed:false,
                defaultActiveMenu:'',
                eleKey:1000001
            }
        },
        created() {
            this.getMenuList()
            if(this.menuList && this.menuList.length>0&&!this.menuList[0].asLeaf){
                this.$refs.menuRef.open(this.menuList[0].id)
            }

        },
        watch:{
            '$route' (to){
                if(to.path === '/management'){
                    this.$router.push(this.defaultActiveMenu)
                    this.eleKey++
                }
            }
        },
        computed:{
          getAsideWidth(){
              return this.collapsed ? '64px':'200px'
          }
        },
        methods: {
            getMenuList() {
                treeListSysPrivilegeMenuOfCurrentUser({
                    moduleCode:'management',
                    applicationId:GLB_CONFIG.applicationId
                }).then(
                    res => {
                        this.menuList = res.data;
                        this.$nextTick(function () {
                            if(this.$router.currentRoute.value.path==='/management'){
                                this.findDefaultActiveMenu(this.menuList)
                                this.$router.push(this.defaultActiveMenu)
                            }else{
                                this.defaultActiveMenu = this.$router.currentRoute.value.path
                            }
                        })

                    }
                )

            },
            toggle(){
                this.collapsed = !this.collapsed
            },
            findDefaultActiveMenu(list){
                for(let i=0;i<list.length;i++){
                    let menu = list[i]
                    if(menu.asLeaf){
                        if(menu.pageUrl){
                            this.defaultActiveMenu = menu.pageUrl
                            return
                        }
                    }else{
                        this.findDefaultActiveMenu(menu.children)
                        if(this.defaultActiveMenu){
                            return;
                        }
                    }
                }
            },
        }
    }
</script>

<style scoped lang="scss">
    .el-footer {
        background-color: #B3C0D1;
        color: #333;
        /*text-align: center;*/
        /*line-height: 60px;*/
    }

    .el-main {
        background-color: #E9EEF3;
        /*padding: 50px;*/
        /*color: #333;*/
        /*line-height: 60px;*/
    }

    .el-aside {
        background-color: #2E323A;
        .el-menu {
            border-right: none;
        }
    }

    .toggle-button{
        background-color: #4A5064;
        font-size: 10px;
        color: #ffffff;
        text-align: center;
        line-height: 24px;
        letter-spacing: 0.2em;
        cursor: pointer;
    }


</style>
