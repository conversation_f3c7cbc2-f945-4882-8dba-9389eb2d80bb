* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* font-size: 100px; */
}
body {
    padding-top: 48px;
    font-size: 14px;
    color: #333;
}
.container {
    max-width: 1000px;
    /* width: 1200px; */
    width: 100%;
    margin: 0 auto;
}
.iconList {
    display: flex;
    flex-wrap: wrap;
}
.iconItem {
    width: 120px;
    height: 240px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.iconItem i {
    display: block;
    font-size: 80px;
    line-height: 1;
    outline: 1px dashed #ccc;
}
.name {
    margin-top: 12px;
}
.class {
    font-size: 12px;
    color: #888;
}
.unicode {
    font-size: 12px;
    color: #888;
}
.siteName {
    text-align: center;
    font-size: 64px;
}
.footer {
    padding-top: 64px;
    text-align: center;
}
.article {

}
.article h1 {
    font-size: 32px;
    margin-bottom: 24px;
}
.article p {
    margin-bottom: 16px;
}
.code {
    background-color: #f5f2f0;
    border-radius: 4px;
    padding: 16px;
}