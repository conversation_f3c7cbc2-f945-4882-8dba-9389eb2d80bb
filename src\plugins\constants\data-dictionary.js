export const BS_TAG_TYPE = {
  TRANSIENT: 'transient',
  CONTINUOUS: 'continuous'
}

export const BS_TAG_EQUIP_TYPE = {
  PAD: 'pad',
  MOBILEPHONE: 'mobilephone'
}

export const SYS_ORG_INNER_CODE = {
  COMPLIANCE: 'compliance'
}

export const VT_TAG_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive'
}

export const BS_TAG_EQUIP_STATUS = {
  FREE: 'free',
  DISTRIBUTED: 'distributed'
}

export const BS_VEHICLE_STATUS = {
  FREE: 'free',
  DISTRIBUTED: 'distributed'
}

export const DAQ_REQUIREMENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  REJECTED: 'rejected',
  ACCEPTED: 'accepted',
  BROKENDOWN: 'broken_down',
  EXECUTING: 'executing',
  FINISHED: 'finished'
}

export const DAQ_TASK_STATUS = {
  DRAFT: 'draft',
  // DISTRIBUTED: "distributed",
  // ACCEPTED:'accepted',
  PUBLISHED: 'published',
  EXECUTING: 'executing',
  FINISHED: 'finished'
}
export const AUTO_CHECK_ITEM = {
  soc: 'soc',
  can: 'can',
  rtk: 'rtk',
  frameSyncCheck: 'frameSyncCheck'
}
export const QA_TAG_TASK_STATUS = {
  NOT_START: 'not_start',
  EXECUTING: 'executing',
  FINISHED: 'finished'
}
export const DATA_SET_STATUS = {
  TO_BE_SELECTED: 'to_be_selected',
  SELECTING: 'selecting',
  SELECTED: 'selected',
  CHECKING: 'checking',
  TO_BE_ACCEPTANCE: 'to_be_acceptance',
  UNDER_ACCEPTANCE: 'under_acceptance',
  ACCEPTANCE_REJECTED: 'acceptance_rejected',
  TO_BE_SENT: 'to_be_sent',
  TO_BE_MERGED: 'to_be_merged',
  SENT: 'sent',
  RETURNED: 'returned',
  STAGING: 'staging',
  STAGING_FAIL: 'staging_fail',
  SENDING: 'sending',
  SENT_FAIL: 'sent_fail',
  DE_DISTORTING: 'de_distorting',
  SLAM_MAP_SUCCESS: 'slam_map_success'
}
export const DATA_SET_PULL_TYPE = {
  SYNC_FRAME: 'sync_frame',
  MODALITY_COMBINATION: 'modality_combination',
  CLIP: 'clip',
  MAP: 'map'
}

export const AUTOX_TASK_TYPE = {
  AUTOSELECTION: 'auto_selection',
  AUTOTAG: 'auto_tag'
}
export const DATA_DELETE_TASK_STATUS = {
  DRAFT: 'draft',
  TO_BR_APPROVED: 'to_be_approved',
  DELETING: 'deleting',
  FAILED: 'failed',
  FINISHED: 'finished',
  REJECTED: 'rejected'
}

export const TAG_BIS_TYPE = {
  STANDARD: 'standard',
  CUSTOM: 'custom'
}

export const TAG_RELATED_TYPE = {
  COMBINATION: 'combination',
  EXTEND: 'extend'
}

export const DM_REQUIREMENT_STATUS = {
  DRAFT: 'draft',
  COMMITTED: 'committed',
  REJECTED: 'rejected',
  ACCEPTED: 'accepted',
  DELIVERING: 'delivering',
  DELIVERED: 'delivered'
}
export const PRIORITY = {
  P00: 'P00',
  P0: 'P0',
  P1: 'P1',
  P2: 'P2 '
}
export const SLICE_DATA_TAG_STATUS = {
  VALID: 'valid',
  INVALID: 'invalid',
  TO_BE_CHECK: 'to_be_check'
}

export const DATA_DISPLAY_TYPE_LIST = [
  {
    index: 0,
    content: '目录展示',
    iconCode: 'el-icon-folder',
    autofocus: true,
    type: 'folder'
  },
  {
    index: 1,
    content: '文件展示',
    iconCode: 'el-icon-tickets',
    autofocus: false,
    type: 'data'
  }
]
export const DATA_DISPLAY_TYPE = {
  FOLDER: 'folder',
  DATA: 'data'
}

export const AUTO_CHECK_BAG_COLUMNS = [
  { label: '名称', prop: 'name', width: 300 },
  { label: '车辆', prop: 'vin' },
  { label: '时间', prop: 'measurement', width: 200 },
  { label: '检查项', prop: 'status', width: 150, slot: 'status' },
  { label: '报警项', prop: 'warningList', width: 300, slot: 'warningList' },
  { label: '错误项', prop: 'details', width: 300, slot: 'details' }
]
export const FAIL_BAG_COLUMNS = [
  { label: '名称', prop: 'name', minWidth: 300, showOnType: 'merged' },
  { label: '车辆', prop: 'vin' },
  { label: '时间', prop: 'measurement', minWidth: 200 },
  { label: '失败原因', prop: 'remark', minWidth: 150, showOnType: 'transfer' },
  { label: '状态', prop: 'status', minWidth: 150 }
]

export const DIALOG_BAG_COLUMNS = [
  { label: '名称', prop: 'name', width: 300 },
  { label: '车辆', prop: 'vin' },
  { label: '时间', prop: 'measurement', width: 200 },
  { label: '文件路径', prop: 'filePath', width: 300, slot: 'filePath' },
  { label: '功能类型', prop: 'functionType' }
]

export const SCENARIO_PUSHED_WR_COLUMNS = [
  { label: '名称', prop: 'name', width: 300 },
  { label: '车辆', prop: 'vin' },
  { label: '时间', prop: 'measurement', width: 200 },
  { label: '开始时间', prop: 'measureStartTime', width: 200 },
  { label: '结束时间', prop: 'measureEndTime', width: 200 },
  { label: '备注', prop: 'remark', width: 100 }
]

export const TAG_RECORD_COLUMNS = [
  { label: '车辆', prop: 'vin', minWidth: 150 },
  { label: '任务', prop: 'taskCode', minWidth: 150 },
  { label: '时间', prop: 'time', minWidth: 350, slot: 'time' },
  { label: '标签', prop: 'tagName', minWidth: 200, slot: 'tagName' },
  { label: '文本', prop: 'remark', minWidth: 200, ShowTooltipWhenOverflow: true, slot: 'remark' },
  { label: '标签组', prop: 'tagGroupName', minWidth: 150 },
  { label: '原因', prop: 'reason', minWidth: 150, showOnType: 'tagGap' }
]

export const RAW_DATA_BAG_INLINE_FUNC = {
  PLAY_BACK: 'playback',
  PREVIEW: 'preView',
  VIEW_POSE: 'viewPose',
  POSITION_TRAJECTORY: 'positionTrajectory',
  PREVIEW_TEXT_FILE: 'previewTextFile',
  DATA_LINK: 'dataLink'
}
export const RAW_DATA_INLINE_FUNC = {
  PREVIEW: 'preView',
  POSITION_TRAJECTORY: 'positionTrajectory',
  PREVIEW_TEXT_FILE: 'previewTextFile',
  DOWNLOAD_ZIP_FILE: 'downloadZipFile'
}

export const QA_TASK_INLINE_FUNC = {
  RECEIVE_TASK: 'receiveTask',
  QUALITY_CHECK: 'qualityCheck',
  CHECK: 'check',
  EXPORT_FILE: 'exportFile'
}

export const BIA_PROJECTION_QA_TASK_STATUS = {
  CREATING: 'creating',
  PROJECTION: 'projecting',
  TO_BE_INSPECTED: 'to_be_inspected',
  INSPECTING: 'inspecting',
  TO_BE_CHECKED: 'to_be_checked',
  CHECKING: 'checking',
  FINISHED: 'finished',
  ERROR: 'error',
  CREATED: 'created',
  SPLIT: 'split',
  SPLITTING: 'splitting',
  PROJECTION_QUEUING: 'projection_queuing',
  REJECTED: 'rejected'
}

export const BIA_PROJECTION_QA_TASK_INLINE_FUNC = {
  INCREASE_PRIORITY: 'increasePriority',
  QUALITY_CHECK: 'qualityCheck',
  CHECK: 'check',
  VIEW: 'view',
  GENERATE_QA_DATASET: 'generateQaDataset',
  SINGLE_REMOVE: 'singleRemove',
  INSPECT_CORRECT: 'inspectCorrect',
  PREVIEW: 'preView',
  PREVIEW_TEXT_FILE: 'previewTextFile'
}
export const BIA_PROJECTION_QA_TASK_OUTLINE_FUNC = {
  FINISH_INSPECT: 'finishInspect',
  FINISH_CHECK: 'finishCheck',
  MARK: 'mark',
  ADD: 'add',
  CHECK_REJECT: 'checkReject'
}

export const BIA_PROJECTION_QA_TASK_DETAIL_STATUS = {
  PROJECTED: 'projected',
  TO_BE_PROJECTED: 'to_be_projected',
  PROJECTION_ERROR: 'projection_error',
  NO_DATA: 'no_data'
}

export const DATASET_INNER_DATA_OUTLINE_FUNC = {
  START_SELECT: 'startSelect',
  FINISH_SELECT: 'finishSelect',
  START_CHECK: 'startCheck',
  FINISH_CHECK: 'finishCheck',
  // START_ACCEPTANCE: 'startAcceptance',
  // FINISH_ACCEPTANCE: 'finishAcceptance',
  ADD_DATA_TO_CART: 'addDataToCart',
  ADD_DATA_TO_RECYCLE_BIN: 'addDataToRecycleBin',
  UPDATE_TAG: 'updateTag',
  DOWNLOAD: 'download',
  FULL_UPDATE_SCENARIO: 'fullUpdateScenario',
  SCENARIO_CONFIG: 'scenarioConfig',
  PREVIEW_ROUTES: 'previewRoutes'
}

export const CLIP_OUTLINE_FUNC = {
  UPDATE_SCENARIO: 'updateScenario',
  INVALID_CLIP: 'invalidClip',
  AUTO_SPLIT_CLIP: 'autoSplitClip'
}

export const CLIP_BATCH_FUNC = {
  BATCH_REMOVE_LIST: 'batchRemoveList',
  BATCH_UPDATE_CLIP_SCENARIO: 'batchUpdateClipScenario'
  // BATCH_PREVIEW_ROUTES: 'batchPreviewRoutes'
}

export const REQ_INLINE_FUNC = {
  EDIT: 'edit',
  SINGLE_REMOVE: 'singleRemove',
  PUBLISH: 'publish',
  ACCEPT: 'accept',
  REJECT: 'reject',
  EXECUTE: 'execute',
  FINISH: 'finish'
}
export const SEARCH_BY_IMAGE_TYPE = [
  { label: '文本搜图', name: 'text' },
  { label: '以图搜图', name: 'pic' }
]
export const LABELED_DATA_SET_BATCH_FUNC = {
  Batch_CREATE_POSE: 'batchCreatePose',
  BATCH_SEND_LABELING: 'batchSendLabeling',
  BATCH_DATA_COPY: 'batchDataCopy',
  BATCH_UPDATE_SCENARIO: 'batchUpdateScenario',
  BATCH_DEMOTION: 'batchDemotion',
  BATCH_FUSION_LIDAR: 'batchFusionLidar',
  BATCH_FILTER: 'batchFilter',
  BATCH_PRE_PROCESS_FRAME_EXTRACTION: 'batchPreProcessFrameExtraction',
  BATCH_START_ICP: 'batchStartIcp',
  BATCH_TEN_HZ_DEMOTION: 'batchTenHZDemotion',
  BATCH_TEN_HZ_FUSION_LIDAR: 'batchTenHZFusionLidar',
  BATCH_SLAM_POSE: 'batchSlamPose',
  BATCH_SUBMIT_ACCEPTANCE: 'batchSubmitAcceptance',
  // BATCH_START_ACCEPTANCE: 'batchStartAcceptance',
  BATCH_TRANSFER_ACCEPTANCE: 'batchTransferAcceptance',
  BATCH_START_TEN_HZ_ICP: 'batchStartTenHZIcp',
  BATCH_ROS_BAG_FRAME: 'batchRosBagFrame',
  BATCH_CONVERT: 'batchConvert',
  BATCH_TEN_HZ_MAINLIDAR_FRAME_EXTRACTION: 'batchTenHZMainLidarFrameExtraction',
  COLD_HANDLE_DATASET: 'coldHandleDataset',
  HOT_HANDLE_DATASET: 'hotHandleDataset',
  BATCH_COMPLETE_TEN_HZ_DATA: 'batchCompleteTenHzData',
  BATCH_DATASET_DEDUPLICATION: 'batchDatasetDeduplication',
  BATCH_START_DS_SLAM: 'batchStartDsSlam',
  BATCH_DOWNLOAD_INPUT: 'batchDownloadInput',
  BATCH_RV_SYNC: 'batchRvSync',
  BATCH_ORIROS: 'batchOriros',
  BATCH_PULL_DATA: 'batchPullData'
}

export const LABELED_DATA_SET_OUT_FUNC = {
  GENERATE_DATA_SET: 'generateDataset',
  ADD: 'add'
}

export const CUSTOM_DATA_SET_BATCH_FUNC = {
  Batch_CREATE_POSE: 'batchCreatePose',
  BATCH_SEND_LABELING: 'batchSendLabeling',
  BATCH_DATA_COPY: 'batchDataCopy',
  BATCH_ROS_BAG_FRAME: 'batchRosBagFrame',
  BATCH_UPDATE_SCENARIO: 'batchUpdateScenario',
  BATCH_START_AI_SLAM: 'batchStartAISlam',
  BATCH_CONVERT: 'batchConvert',
  COLD_HANDLE_DATASET: 'coldHandleDataset',
  HOT_HANDLE_DATASET: 'hotHandleDataset',
  BATCH_START_DS_SLAM: 'batchStartDsSlam',
  BATCH_PULL_DATA: 'batchPullData',
  BATCH_DOWNLOAD_INPUT: 'batchDownloadInput',
  BATCH_RV_SYNC: 'batchRvSync',
  BATCH_ORIROS: 'batchOriros'
}

export const DATA_SET_INLINE_FUNC = {
  EDIT: 'edit',
  SINGLE_REMOVE: 'singleRemove',
  VIEW: 'view',
  COPY: 'copy',
  CLONE: 'clone',
  DE_DISTORTION: 'dedistortion',
  Ten_HZ_DE_DISTORTION: 'tenHZDedistortion',
  MERGE_DATA_SET: 'mergeDataset',
  AUTOX_MERGE: 'autoXMerge',
  DATA_COMPLETE: 'dataComplete',
  AUTO_SELECTION: 'autoSelection',
  STRAT_ICP: 'startIcp',
  FUSION_BLIND_LIDAR: 'fusionBlindLidar',
  FUSION_TEN_HZ_BLIND_LIDAR: 'fusionTenHZBlindLidar',
  CONFIG_SIMULATION_TYPE: 'configSimulationType',
  OPENX_TRANSLATION: 'openxTranslation',
  ABNORMAL_LABEL_DELETE: 'abnormalLabelDelete',
  FILTER_DATA: 'filterData',
  PULL_DATA: 'pullData',
  FILTER_INVALID_DATA: 'filterInvalidData',
  DELETE_FILE: 'deleteFile',
  RELOCATION: 'relocation',
  STARTAISLAM: 'startAISlam',
  DS_WORKFLOW_TRIGGER: 'dsWorkflowTrigger',
  DATASET_RV_SYNC: 'datasetRvSync',
  DATASET_ORIROS: 'datasetOriros',
  DATASET_TYPE_CONVERT: 'datasetTypeConvert'
}

export const ANON_QA_TASK_INLINE_FUNC = {
  RECEIVE_TASK: 'receiveTask',
  SINGLE_REMOVE: 'singleRemove'
}

export const ANON_QA_TASK_OUTLINE_FUNC = {
  ADD: 'add',
  QUALITY_CHECK: 'qualityCheck',
  FINISH_INSPECT: 'finishInspect',
  CHECK: 'check',
  FINISH_CHECK: 'finishCheck',
  SINGLE_REMOVE: 'singleRemove'
}

export const ANON_QA_TASK_STATUS = {
  SELECTING: 'selecting',
  TO_BE_CLAIMED: 'to_be_claimed',
  TO_BE_CHECKED: 'to_be_checked',
  CHECKING: 'checking',
  TO_BE_SPOT_CHECKED: 'to_be_spot_checked',
  SPOT_CHECKING: 'spot_checking',
  FINISHED: 'finished'
}

export const OVER_LAY_TYPE = {
  POLYGON: 'polygon',
  CIRCLE: 'circle',
  RECTANGLE: 'rectangle',
  ELLIPSE: 'ellipse',
  DRAGMARKER: 'dragMarkers'
}

export const INTERSECTION_INLINE_FUNC = {
  EDIT: 'edit',
  SINGLE_REMOVE: 'singleRemove',
  VIEW: 'view',
  PLAY_BACK: 'playback'
}

export const INTERSECTION_MAP_ANNOTATION_INLINE_FUNC = {
  QUALITY_CHECK: 'qualityCheck',
  PREVIEW: 'preView'
}

export const PROCESS_RESOURCE_TYPE = {
  DATASET: 'DATASET',
  DATA_BAG: 'DATA_BAG',
  DATA_CLIP: 'DATA_CLIP'
}
