<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="765px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="filter-task"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('日期')" prop="DateRange">
        <el-date-picker
          clearable
          :disabled="formReadonly"
          v-model="form.dateRange"
          type="datetimerange"
          :range-separator="$t('到')"
          :start-placeholder="$t('开始日期')"
          :end-placeholder="$t('结束日期')"
          value-format="YYYY-MM-DD HH:mm:ss"
          popper-class="dateRange"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="confirm">{{ $t('确认') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { dateUtils } from '@/plugins/util'

const defaultform = {}
export default {
  name: 'FilterVehicleTrajectory',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  mounted() {
    let nowDate = dateUtils.parseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00'
    let tomorrowDate = dateUtils.parseTime(new Date().getTime() + 24 * 60 * 60 * 1000, '{y}-{m}-{d}') + ' 00:00:00'
    this.form.dateRange = [nowDate, tomorrowDate]
  },
  components: {},

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('轨迹筛选')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      // this.$refs.formRef.resetFields()
      // this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        if (postData.dateRange?.length) {
          postData.startTime = postData.dateRange[0]
          postData.endTime = postData.dateRange[1]
          delete postData.dateRange
        }
        this.cancel(postData)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin: 5px 3px;
}

.el-select {
  width: 100%;
}

:deep(.el-form-item__content) > .el-button {
  margin-left: 12px;
}
</style>
