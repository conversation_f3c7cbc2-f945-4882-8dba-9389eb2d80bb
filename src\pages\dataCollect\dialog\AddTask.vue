<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="560px"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :draggable="true"
    class="add-task"
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-form-item :label="$t('采集任务类型')" prop="type">
        <el-radio-group v-model="form.type" :disabled="formReadonly">
          <el-radio v-for="item in typeList" :label="item.code" :key="item.code">{{ $t(item.name) }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name">
        <ltw-input v-model="form.name" :disabled="formReadonly" id="name" text-type="remark"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('期望日期')" prop="dateRange" id="dateRange">
        <el-date-picker
          clearable
          :disabled="formReadonly"
          v-model="form.dateRange"
          type="daterange"
          :range-separator="$t('到')"
          :start-placeholder="$t('开始日期')"
          :end-placeholder="$t('结束日期')"
          value-format="YYYY-MM-DD"
          @change="getFreeOption()"
          popper-class="dateRange"
        />
      </el-form-item>
      <el-form-item v-if="formReadonly" :label="$t('执行时间')" prop="executionTimeRange" id="executionTimeRange">
        <el-date-picker
          clearable
          :disabled="formReadonly"
          v-model="form.executionTimeRange"
          type="datetimerange"
          :range-separator="$t('到')"
          :start-placeholder="$t('开始时间')"
          :end-placeholder="$t('结束时间')"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="getFreeOption()"
          popper-class="dateRange"
        />
      </el-form-item>
      <el-form-item :label="$t('类型')" prop="acquisitionType">
        <el-radio-group
          :disabled="formReadonly"
          v-model="form.acquisitionType"
          id="acquisitionType"
          @change="handleAcquisitionTypeChange"
        >
          <el-radio v-for="item in acquisitionTypeList" :label="item.code" :key="item.code" :id="item.code"
            >{{ $t(item.name) }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('车辆')" prop="vehicleId" id="vehicleId">
        <bs-vehicle-selection
          clearable
          filterable
          :data="vehicleList"
          :auto-load="false"
          modelCode="id"
          v-model="form.vehicleId"
          :disabled="formReadonly"
          @change="handleVehilceChange($event)"
          ref="vehicleSelectionRef"
        ></bs-vehicle-selection>
      </el-form-item>
      <el-form-item :label="$t('设备')" prop="equipmentId" id="equipmentId">
        <bs-tag-equipment-selection
          clearable
          filterable
          :data="equipmentList"
          :auto-load="false"
          :disabled="formReadonly"
          v-model="form.equipmentId"
        ></bs-tag-equipment-selection>
      </el-form-item>
      <el-form-item :label="$t('负责人')" prop="recipientEmpId" id="recipientEmpId">
        <employee-selection clearable :disabled="formReadonly" v-model="form.recipientEmpId"></employee-selection>
      </el-form-item>
      <el-form-item v-if="form.locationType === 'route'" :label="$t('路线')" prop="locationName">
        <ltw-input v-model="form.locationName" :disabled="formReadonly" id="locationName"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('合规官')" prop="complianceOfficerEmpId" id="complianceOfficerEmpId">
        <employee-selection
          clearable
          :data="complianceOfficerList"
          :auto-load="false"
          :query-param="complianceEmployeeQueryParam"
          :disabled="formReadonly"
          v-model="form.complianceOfficerEmpId"
        ></employee-selection>
      </el-form-item>
      <el-form-item v-if="form.type === 'special'" :label="$t('需求标签')" prop="requirementList">
        <el-link
          type="primary"
          @click="getTags(form.requirementList, 'req')"
          style="margin-right: 10px"
          :underline="false"
          >{{ form.requirementList?.length || 0 }}
        </el-link>
        <el-button v-if="!formReadonly" @click="chooseRequirementTags()" type="primary" id="tag">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
          <!--          form.requirementList?.length > 0 ? $t('选择') : $t('选择')-->
        </el-button>
        <div class="group-list">
          <div class="group-item" v-for="group in form.requirementList" :key="group.id">
            <el-tag type="success">{{ group.name }}</el-tag>
            <el-link @click="getTags([group], 'req')" class="tag-num" :underline="false">
              <ltw-icon icon-code="el-icon-discount"></ltw-icon>
              <span class="tag-text">{{ getTagListLength(group.children) || 0 }}</span></el-link
            >
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('分类标签')"
        prop="classificationList"
        :rules="
          form.type === 'daily'
            ? {
                required: true,
                message: this.$t('请选择'),
                trigger: 'change'
              }
            : {
                required: false
              }
        "
      >
        <el-link
          type="primary"
          @click="getTags(form.classificationList, 'class')"
          style="margin-right: 10px"
          :underline="false"
          >{{ form.classificationList?.length || 0 }}
        </el-link>
        <el-button v-if="!formReadonly" @click="chooseTags()" type="primary" id="tag">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
        </el-button>
        <div class="group-list">
          <div class="group-item" v-for="group in form.classificationList" :key="group.id">
            <el-tag>{{ group.name }}</el-tag>
            <el-link @click="getTags([group], 'class')" class="tag-num" :underline="false">
              <ltw-icon icon-code="el-icon-discount"></ltw-icon>
              <span class="tag-text">{{ getTagListLength(group.children) || 0 }}</span></el-link
            >
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="$t('备注')" prop="remark">
        <ltw-input type="textarea" v-model="form.remark" :disabled="formReadonly" id="remark"></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="save">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
  <!--  <tag-list ref="AddTagList" @tagSave="tagSave"></tag-list>-->
  <tag-requirement-drawer ref="TagRequirementDrawer" @reload="confirmRequirementTag" />

  <tag-classification-drawer ref="TagClassificationDrawer" @reload="confirmClassification"></tag-classification-drawer>
  <!--  <tag-samples-dialog ref="TagSamplesDialog" @reload="confirmSamples"></tag-samples-dialog>-->
</template>

<script>
import { SYS_ORG_INNER_CODE } from '@/plugins/constants/data-dictionary'
import { listBsTagEquipment } from '@/apis/data-collect/bs-tag-equipment'
import { saveDaqReqDetail, updateDaqReqDetail, getDaqReqDetail } from '@/apis/data-collect/vt-daq-task'
import { listBsVehicle } from '@/apis/fleet/bs-vehicle'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import { treeListBsTagGroup } from '@/apis/data-collect/bs-tag-group'
import { getDriverList, listSysRoleEmployee } from '@/apis/system/sys-role-employee'
import TagRequirementDrawer from '@/pages/dataCollect/dialog/TagRequirementDrawer.vue'
import TagList from '@/pages/dataCollect/dialog/TagList.vue'
import BsVehicleSelection from '@/components/dataCollect/BsVehicleSelection.vue'
import BsTagEquipmentSelection from '@/components/basic/BsTagEquipmentSelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import TagClassificationDrawer from '@/pages/dataCollect/dialog/TagClassificationDrawer.vue'
import TagSamplesDialog from '@/pages/dataCollect/dialog/TagSamplesDialog.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'

const defaultform = {
  type: 'daily',
  acquisitionType: 'parking'
}
export default {
  name: 'AddTask',
  emits: ['reload', 'show-tag-list', 'show-tag-samples-dialog'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      acquisitionTypeList: [],
      formRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入'),
            trigger: 'change'
          }
        ],
        acquisitionType: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        vehicleId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        equipmentId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        recipientEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        driverEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        copilotEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        complianceOfficerEmpId: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        dateRange: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        type: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        requirementList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ]
        // classificationList: [
        //   {
        //     required: true,
        //     message: this.$t('请选择'),
        //     trigger: 'change'
        //   }
        // ]
      },
      complianceEmployeeQueryParam: {
        orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE
      },
      // 配置标签
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      // 选择标签
      tagsData: [],
      tagGroupList: [],
      tagsTitle: '',
      vehicleList: [],
      driverList: [],
      complianceOfficerList: [],
      equipmentList: [],
      parkingList: [],
      tagList: [],
      reqTagList: [],
      tagCardVisible: false,
      checkedTag: [],
      flag: false,
      requirementList: [],
      chooseTagType: '',
      typeList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    LtwIcon,
    DictionarySelection,
    EmployeeSelection,
    BsTagEquipmentSelection,
    BsVehicleSelection,
    TagList,
    TagRequirementDrawer,
    TagClassificationDrawer,
    TagSamplesDialog
  },
  created() {},
  methods: {
    getDataTypes() {
      listSysDictionary({
        typeCode: 'acquisition_task_type'
      }).then(res => {
        this.acquisitionTypeList = res.data
      })
    },
    getDaqTaskType() {
      listSysDictionary({
        typeCode: 'daq_task_type'
      }).then(res => {
        this.typeList = res.data
      })
    },
    show(row) {
      this.getDataTypes()
      this.getDaqTaskType()
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('采集任务')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑任务')
          this.getDaqReqDetail(row.id)
          break
        case 'view':
          this.dialogTitle = this.$t('任务详情')
          this.getDaqReqDetail(row.id)
          break
      }
      // this.getDriverList()
      this.listComplianceOfficer()
      if (!this.treeTagGroupList?.length) {
        this.treeListTagGroup()
      }
    },

    treeListTagGroup() {
      treeListBsTagGroup().then(res => {
        this.treeTagGroupList = res.data
      })
    },
    handleAcquisitionTypeChange() {
      if (this.form.acquisitionType === 'parking') {
        this.form.locationType = 'parking_lot'
      } else if (this.form.acquisitionType === 'driving') {
        this.form.locationType = 'route'
      } else {
        this.form.locationType = ''
      }
    },
    getFreeOption() {
      this.form.equipmentId = undefined
      this.form.vehicleId = undefined
      this.listBsVehicle()
      this.listEquipment()
    },
    listComplianceOfficer() {
      listSysRoleEmployee({ orgInnerCode: SYS_ORG_INNER_CODE.COMPLIANCE }).then(res => {
        this.complianceOfficerList = res.data
      })
    },
    listEquipment() {
      let postData = {
        expectedStartDate: this.form.dateRange && this.form.dateRange[0],
        expectedEndDate: this.form.dateRange && this.form.dateRange[1],
        taskId: this.form.id
      }
      listBsTagEquipment(postData).then(res => {
        this.equipmentList = res.data
      })
    },
    listBsVehicle() {
      let postData = {
        expectedStartDate: this.form.dateRange && this.form.dateRange[0],
        expectedEndDate: this.form.dateRange && this.form.dateRange[1],
        taskId: this.form.id
      }
      listBsVehicle(postData).then(res => {
        this.vehicleList = res.data
      })
    },
    getDriverList() {
      getDriverList().then(res => {
        this.driverList = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm() {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = { ...this.form }
        // if (this.form.type === 'special') {
        let classificationTags = [],
          requirementTags = []
        if (this.form.requirementList?.length) {
          requirementTags = this.form.requirementList
            .map(val => val.children)
            .flat(Infinity)
            .map(val => val.tagList)
            .flat(Infinity)
          postData.requirementList = postData.requirementList.map(val => {
            return { code: val.code }
          })
        }
        if (this.form.classificationList?.length) {
          classificationTags = this.form.classificationList
            .map(val => val.children)
            .flat(Infinity)
            .map(val => val.tagList)
            .flat(Infinity)
        }
        postData.tagList = [...classificationTags, ...requirementTags]
        // } else {
        //   delete postData.requirementList
        // }
        postData.expectedStartTime = postData.dateRange[0]
        postData.expectedEndTime = postData.dateRange[1]
        if (!this.form.id) {
          saveDaqReqDetail(postData).then(() => {
            this.cancel()
          })
        } else {
          updateDaqReqDetail(postData).then(() => {
            this.cancel()
          })
        }
      })
    },
    getDaqReqDetail(id) {
      getDaqReqDetail(id).then(res => {
        res.data.dateRange = [res.data.expectedStartTime, res.data.expectedEndTime]
        res.data.executionTimeRange = [res.data.startTime, res.data.endTime]
        // if (res.data.type === 'special') {
        if (res.data.tagList?.length) {
          res.data.tagList.filter((val, tagIndex) => {
            // 组装requirementList列表
            let index = res.data.requirementList.findIndex(req => req.id === val.classificationId)
            if (~index) {
              res.data.requirementList[index].tagList ??= []
              res.data.requirementList[index].tagList.push(val)
            } else {
              // 组装classificationList列表
              res.data.classificationList ??= []
              let classificationIndex = res.data.classificationList.findIndex(
                classification => classification.id === val.classificationId
              )
              if (~classificationIndex) {
                res.data.classificationList[classificationIndex].tagList.push(val)
              } else {
                res.data.classificationList.push({
                  code: val.classificationCode,
                  id: val.classificationId,
                  name: val.classificationName,
                  tagList: [val]
                })
              }
            }
          })
          res.data.requirementList = this.formatList(res.data.requirementList)
          // }
          res.data.classificationList = this.formatList(res.data.classificationList)
        }
        this.form = res.data
        this.listBsVehicle()
        this.listEquipment()
      })
    },
    formatList(list) {
      list?.forEach(req => {
        req.children ??= []
        req.tagList?.forEach(tag => {
          let groupIndex = req.children.findIndex(child => child.id === tag.groupId)
          if (~groupIndex) {
            req.children[groupIndex].tagList.push(tag)
          } else {
            req.children.push({
              code: tag.groupCode,
              id: tag.groupId,
              name: tag.groupName,
              nameCn: tag.groupNameCn,
              tagList: [tag]
            })
          }
        })
      })
      return list
    },
    handleVehilceChange({ node }) {
      if (node.tagEquipId && !this.form.equipmentId) {
        if (~this.equipmentList.findIndex(val => val.id === node.tagEquipId)) {
          this.form.equipmentId = node.tagEquipId
        }
      }
    },
    getTags(groupList, taskType) {
      let postData = {
        type: this.dialogStatus,
        data: {
          taskType: taskType,
          groupList: groupList
        }
      }
      this.$emit('show-tag-list', postData)
      // this.$refs.AddTagList.show(postData)
    },
    tagSave(row) {
      if (row.data.groupList?.length > 0) {
        if (row.data.taskType === 'class') {
          this.form.classificationList = row.data.groupList
        } else if (row.data.taskType === 'req') {
          let postData = {
            type: this.dialogStatus === 'view' ? 'onlyView' : 'view',
            data: row.data
          }
          this.$emit('show-tag-samples-dialog', postData)
          // this.$refs.TagSamplesDialog.show({
          //   type: this.dialogStatus === 'view' ? 'onlyView' : 'view',
          //   data: row.data
          // })
        }
      }
    },
    chooseRequirementTags() {
      this.$refs.TagRequirementDrawer.show({
        dataList: this.form.requirementList
      })
    },
    chooseTags() {
      this.$refs.TagClassificationDrawer.show({
        dataList: this.form.classificationList
      })
    },
    confirmClassification(tagList) {
      this.form.classificationList = tagList
      this.$refs.formRef.validateField('classificationList')
      this.getTags(tagList, 'class')
    },
    confirmRequirementTag(row) {
      this.form.requirementList = row
      this.$refs.formRef.validateField('requirementList')
      this.getTags(row, 'req')
    },
    confirmSamples(row) {
      this.form.requirementList = row.groupList
    },
    confirmClassificationTag(row) {
      this.form.classificationList = row.groupList
    },
    getTagListLength(group) {
      return group?.reduce((total, val) => {
        return total + (val.tagList?.length || 0)
      }, 0)
    }
  }
}
</script>

<style scoped lang="scss">
.group-list {
  width: 100%;

  .group-item {
    display: flex;
    align-items: center;

    .tag-num {
      display: flex;
      margin-left: 10px;

      //.ltw-icon {
      //  color: #909399;
      //}

      .tag-text {
        margin-left: 5px;
        color: #409eff;
      }
    }
  }
}
</style>
