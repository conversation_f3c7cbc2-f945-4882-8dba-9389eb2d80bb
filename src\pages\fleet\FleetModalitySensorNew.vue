<template>
  <div class="sensor-management">
    <el-card shadow="never" class="main-card">
      <template #header>
        <div class="search-filter-section">
          <div class="search-container">
            <ltw-input
              v-model="searchKeyword"
              :placeholder="$t('请输入名称查找')"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <ltw-icon icon-code="el-icon-search"></ltw-icon>
                </el-button>
              </template>
            </ltw-input>
          </div>
        </div>
      </template>

      <!-- 主要内容区域 -->
      <div class="main-content" :class="{ 'with-drawer': showEditDrawer }">
        <!-- 顶部选项卡和统计 -->
        <div class="content-header">
          <!-- 主选项卡 -->
          <div class="main-tabs">
            <el-tabs v-model="activeMainTab" class="main-tab-container">
              <el-tab-pane label="传感器编码" name="modality"></el-tab-pane>
              <el-tab-pane label="传感器实体" name="sensor"></el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 控制栏
      <div class="control-bar">
        <div class="left-controls">
          <div class="statistics">
            <i class="stats-icon el-icon-data-line"></i>
            <span class="stats-label">传感器总数</span>
            <span class="stats-value">{{ totalCount }}</span>
          </div>
        </div>
      </div> -->

        <!-- 内容卡片 -->
        <el-card class="content-card" shadow="hover" :class="{ shrink: showEditDrawer }">
          <!-- 传感器类型筛选标签 -->
          <!-- 品牌筛选标签 -->
          <div class="brand-filter-section">
            <div class="filter-tags">
              <div class="filter-tag" :class="{ active: selectedBrands.length === 0 }" @click="selectAllBrands">
                {{ $t('全部') }}
              </div>
              <div
                v-for="brand in brandList"
                :key="brand.code"
                class="filter-tag"
                :class="{ active: selectedBrands.includes(brand.code) }"
                @click="toggleBrand(brand.code)"
              >
                {{ brand.name }}
              </div>
            </div>
          </div>

          <!-- 传感器内容区域 -->
          <div class="sensor-content">
            <!-- Sensor tab - 按传感器类型分组显示 -->
            <div v-if="activeMainTab === 'sensor'" class="sensors-by-type">
              <div v-for="typeGroup in sensorsByType" :key="typeGroup.code" class="sensor-type-group">
                <!-- 传感器类型标签 -->
                <div class="sensor-type-label">
                  <span class="type-name">{{ typeGroup.name }}</span>
                  <el-button class="add-button" @click="openAddDrawer(typeGroup)"
                    ><ltw-icon icon-code="el-icon-plus"></ltw-icon
                  ></el-button>
                </div>

                <!-- 该类型下的传感器卡片网格 -->
                <div v-if="typeGroup.sensors && typeGroup.sensors.length">
                  <div class="sensor-grid">
                    <div
                      v-for="sensor in typeGroup.sensors"
                      :key="sensor.id"
                      class="sensor-card"
                      @click="openViewDrawer(sensor)"
                    >
                      <!-- 传感器图片 -->
                      <div class="sensor-image-section">
                        <img
                          v-if="sensor.photoList && sensor.photoList.length > 0"
                          :src="downloadUrl + sensor?.photoList[0]?.id + token"
                          class="sensor-image"
                        />
                        <img v-else class="sensor-image" :src="getSensorImage(sensor)" :alt="sensor.name" />
                      </div>

                      <!-- 传感器信息 -->
                      <div class="sensor-info-section">
                        <!-- 标题行：传感器类型 + 品牌 -->
                        <div class="sensor-title-row">
                          <div class="sensor-brand">{{ sensor.supplierName }}</div>
                        </div>

                        <!-- 型号规格 -->
                        <div class="sensor-spec-row">
                          <el-tooltip
                            :content="getSensorSpec(sensor)"
                            placement="top"
                            :disabled="!getSensorSpec(sensor)"
                          >
                            <span class="spec-value">{{ getSensorSpec(sensor) || '-' }}</span>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <el-empty v-else description="暂无传感器"></el-empty>
              </div>
            </div>

            <!-- Modality tab - 按传感器类型显示卡片布局 -->
            <div v-else-if="activeMainTab === 'modality'" class="modality-by-type">
              <div
                v-for="typeGroup in sensorsByType"
                :key="typeGroup.code"
                class="modality-type-card"
                @mouseenter="hoveredCardType = typeGroup.code"
                @mouseleave="hoveredCardType = null"
              >
                <el-card shadow="hover" class="type-card">
                  <!-- 卡片头部：类型标签和新增按钮 -->
                  <template #header>
                    <div class="card-header">
                      <span class="type-title">{{ typeGroup.name }}</span>
                      <el-button class="add-button" @click="openAddDrawer(typeGroup)">
                        <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                      </el-button>
                    </div>
                  </template>

                  <!-- 卡片内容：传感器标签 -->
                  <div class="sensor-tags-container">
                    <div v-if="typeGroup.sensors && typeGroup.sensors.length" class="sensor-tags">
                      <el-tag
                        v-for="sensor in typeGroup.sensors"
                        :key="sensor.id"
                        class="sensor-tag"
                        :class="getSensorTagClass(sensor)"
                        :closable="hoveredCardType === typeGroup.code"
                        @click="openViewDrawer(sensor)"
                        @close="handleTagClose(sensor, typeGroup)"
                        style="height:38px"
                      >
                        <div class="sensor-tag-content">
                          <div class="sensor-code">{{ sensor.code }}</div>
                          <div class="sensor-name">{{ sensor.name }}</div>
                        </div>
                      </el-tag>
                    </div>
                    <div v-else class="empty-state">
                      <span class="empty-text">暂无传感器</span>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 编辑抽屉 -->
        <div v-if="showEditDrawer" class="edit-drawer">
          <div class="drawer-container">
            <!-- 抽屉头部 -->
            <div class="drawer-header">
              <div class="drawer-title">
                <span>{{ drawerTitle }}</span>
              </div>
              <div class="drawer-close" @click="closeEditDrawer">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M15 5L5 15M5 5L15 15" stroke="#666" stroke-width="1.5" stroke-linecap="round" />
                </svg>
              </div>
          
            </div>
                 <div class="header-button-group">
                <!-- 查看模式 -->
                <template v-if="drawerMode === 'view'">
                  <div class="confirm-buttons">
                    <el-button
                      type="warning"
                      :class="['warning-tag']"
                      @click="enterEditMode"
                    >
                      <ltw-icon :icon-code="drawerMode === 'edit' ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>
                      编辑
                    </el-button>
                  </div>
                  <el-button
                    class="delete-version-btn"
                    @click="deleteSensor"
                    v-if="editSensorData.id || editModalityData.id"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </template>

                <!-- 编辑模式和新增模式 -->
                <template v-else>
                  <div class="confirm-buttons">
                    <el-button
                      type="success"
                      @click="saveSensor"
                      class="warning-tag editing-state"
                    >
                      <ltw-icon :icon-code="drawerMode === 'edit' ? 'el-icon-check' : 'el-icon-edit'"></ltw-icon>
                      保存
                    </el-button>
                    <el-button type="info" class="cancel-tag" @click="cancelEdit">
                      <ltw-icon icon-code="el-icon-close"></ltw-icon>
                      取消
                    </el-button>
                  </div>
                  <el-button
                    class="delete-version-btn"
                    @click="deleteSensor"
                    v-if="drawerMode === 'edit' && (editModalityData.id || editSensorData.id)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </template>
              </div>

            <!-- 抽屉内容 -->
            <div class="drawer-content">
 
              <!-- Sensor模式的表单 -->
              <el-form
                v-if="activeMainTab === 'sensor'"
                ref="sensorFormRef"
                :model="editSensorData"
                :rules="sensorFormRules"
                label-width="100px"
              >
                <div class="form-container">
                  <!-- 基本信息区域 -->
                  <div class="form-section">
                    <el-form-item label="传感器类型：" prop="sensorType" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{
                        editSensorData.sensorTypeName || editSensorData.sensorType || '-'
                      }}</span>
                      <dictionary-selection
                        v-else
                        v-model="editSensorData.sensorType"
                        clearable
                        dictionaryType="sensor_type"
                        @change="changeSensorType"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="供应商：" prop="supplierName" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.supplierName || '-' }}</span>
                      <supplier-selection
                        v-else
                        v-model="editSensorData.supplierId"
                        clearable
                        @change="changeSupplierName"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="传感器型号：" prop="model" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.model || '-' }}</span>
                      <ltw-input
                        v-else
                        v-model="editSensorData.model"
                        clearable
                        class="form-input"
                        placeholder="请输入"
                      ></ltw-input>
                    </el-form-item>
                  </div>

                  <!-- 规格信息区域 -->
                  <div class="form-section">
                    <el-form-item label="规格：" prop="specification" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.specification || '-' }}</span>
                      <ltw-input
                        v-else
                        v-model="editSensorData.specification"
                        clearable
                        class="form-input"
                        placeholder="请输入"
                      ></ltw-input>
                    </el-form-item>

                    <el-form-item
                      label="分辨率(px)："
                      prop="resolution"
                      class="form-row"
                      v-if="editSensorData.sensorType === 'camera'"
                    >
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.resolution || '-' }}</span>
                      <div v-else class="resolution-container">
                        <ltw-input
                          v-model="editSensorData.horizontalResolution"
                          clearable
                          class="resolution-input"
                          placeholder="请输入"
                        >
                        </ltw-input>
                        <span class="resolution-separator">×</span>
                        <ltw-input
                          v-model="editSensorData.verticalResolution"
                          class="resolution-input"
                          clearable
                          placeholder="请输入"
                        >
                        </ltw-input>
                      </div>
                    </el-form-item>

                    <el-form-item
                      label="水平视角(°)："
                      prop="hfov"
                      class="form-row"
                      v-if="editSensorData.sensorType === 'camera'"
                    >
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.hfov || '-' }}</span>
                      <ltw-input v-else v-model="editSensorData.hfov" class="form-input" clearable placeholder="请输入">
                      </ltw-input>
                    </el-form-item>

                    <el-form-item
                      label="垂直视角(°)："
                      prop="vfov"
                      class="form-row"
                      v-if="editSensorData.sensorType === 'camera'"
                    >
                      <span v-if="isReadonly" class="form-value">{{ editSensorData.vfov || '-' }}</span>
                      <ltw-input v-else v-model="editSensorData.vfov" class="form-input" clearable placeholder="请输入">
                      </ltw-input>
                    </el-form-item>
                  </div>

                  <!-- 描述区域 -->
                  <div class="form-section">
                    <el-form-item label="描述：" prop="remark" class="form-row-textarea">
                      <span v-if="isReadonly" class="form-value form-value-multiline">{{
                        editSensorData.remark || '-'
                      }}</span>
                      <el-input
                        v-else
                        v-model="editSensorData.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入"
                        maxlength="100"
                        show-word-limit
                        class="form-textarea"
                      ></el-input>
                    </el-form-item>
                  </div>

                  <!-- 图片上传区域 -->
                  <div class="form-section">
                    <el-form-item label="图片：" prop="photoList" class="form-row-upload">
                      <upload-file
                        v-model="editSensorData.photo"
                        source-type="sensor_image"
                        :source-id="editSensorData.id"
                        list-type="picture-card"
                        :limit="4"
                        class="sensor-image-upload"
                        :disabled="isReadonly"
                      />
                    </el-form-item>
                  </div>
                </div>
              </el-form>

              <!-- Modality模式的表单 -->
              <el-form
                v-else-if="activeMainTab === 'modality'"
                ref="modalityFormRef"
                :model="editModalityData"
                :rules="modalityFormRules"
                label-width="100px"
              >
                <div class="form-container">
                  <!-- 基本信息区域 -->
                  <div class="form-section">
                    <el-form-item label="名称：" prop="name" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editModalityData.name || '-' }}</span>
                      <ltw-input
                        v-else
                        v-model="editModalityData.name"
                        class="form-input"
                        clearable
                        placeholder="请输入名称"
                      ></ltw-input>
                    </el-form-item>

                    <el-form-item label="编码：" prop="code" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editModalityData.code || '-' }}</span>
                      <ltw-input
                        v-else
                        v-model="editModalityData.code"
                        class="form-input"
                        clearable
                        placeholder="请输入编码"
                      ></ltw-input>
                    </el-form-item>

                    <el-form-item label="传感器类型：" prop="sensorType" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{
                        editModalityData.sensorTypeName || editModalityData.sensorType || '-'
                      }}</span>
                      <dictionary-type-selection
                        v-else
                        v-model="editModalityData.sensorType"
                        clearable
                        dictionaryType="sensor_type"
                        placeholder="请选择"
                        filterable
                        @change="changeModalitySensorType"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="别名：" prop="alias" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{ editModalityData.alias || '-' }}</span>
                      <ltw-input
                        v-else
                        v-model="editModalityData.alias"
                        class="form-input"
                        clearable
                        placeholder="请输入别名"
                      ></ltw-input>
                    </el-form-item>

                    <el-form-item label="安装位置：" prop="installationPosition" class="form-row">
                      <span v-if="isReadonly" class="form-value">{{
                        editModalityData.installationPositionName || '-'
                      }}</span>
                      <dictionary-type-selection
                        v-else
                        v-model="editModalityData.installationPosition"
                        clearable
                        dictionaryType="install_position"
                        placeholder="请选择"
                        filterable
                        @change="changeModalityInstallationPosition"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>

                  <!-- 描述区域 -->
                  <div class="form-section">
                    <el-form-item label="描述：" prop="description" class="form-row-textarea">
                      <span v-if="isReadonly" class="form-value form-value-multiline">{{
                        editModalityData.description || '-'
                      }}</span>
                      <el-input
                        v-else
                        v-model="editModalityData.description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入描述"
                        maxlength="100"
                        show-word-limit
                        class="form-textarea"
                      ></el-input>
                    </el-form-item>
                  </div>
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import UploadFile from '@/components/system/UploadFile.vue'
import {
  querySensorModalityListGroupByType,
  saveFtmSensor,
  updateFtmSensor,
  deleteFtmSensor,
  getFtmSensor
} from '@/apis/fleet/ftm-sensor'
import {
  getFtmVehicleModality,
  saveFtmVehicleModality,
  updateFtmVehicleModality,
  deleteFtmVehicleModality
} from '@/apis/fleet/ftm-vehicle-modality'
import LtwIcon from '../../components/base/LtwIcon.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import DictionaryTypeSelection from '@/components/system/DictionaryTypeSelection.vue'
import SupplierSelection from '@/components/system/SupplierSelection.vue'
import GLB_CONFIG from '@/plugins/glb-constant'
import util, { createNumberValidationRules } from '@/plugins/util'

export default {
  name: 'SensorManagement',
  components: {
    UploadFile,
    LtwIcon,
    DictionarySelection,
    DictionaryTypeSelection,
    SupplierSelection
  },
  data() {
    return {
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken(),
      // 搜索相关
      searchKeyword: '',

      // 选项卡相关
      activeMainTab: 'modality',
      activeSensorTab: 'all', // 默认显示所有类型

      // 显示控制
      displayMode: 'card',
      sortBy: 'date',

      // 品牌筛选
      selectedBrands: [], // 默认查全部，空数组表示全部
      brandList: [], // 动态生成的品牌列表

      // 传感器子选项卡 - 动态生成
      sensorTabs: [],

      // 传感器数据 - 按类型分组的原始数据
      sensorGroupData: [],

      // 当前选中的传感器类型数据
      currentSensorList: [],

      // 默认图片
      defaultImage: 'https://placehold.co/146x107',

      // 编辑抽屉相关
      showEditDrawer: false, // 是否显示编辑抽屉
      drawerMode: 'view', // 抽屉模式：'view'查看、'edit'编辑、'add'新增

      // Modality模式下的卡片悬停状态
      hoveredCardType: null, // 当前悬停的卡片类型
      editSensorData: {
        // 编辑的传感器数据
        id: '',
        sensorType: '',
        sensorTypeName: '',
        supplierId: '',
        supplierName: '',
        model: '',
        alias: '',
        specification: '',
        resolution: '',
        hfov: '',
        vfov: '',
        remark: '',
        photoList: []
      },

      // Modality模式的编辑数据
      editModalityData: {
        id: '',
        name: '',
        code: '',
        sensorType: '',
        sensorTypeName: '',
        alias: '',
        installationPosition: '',
        installationPositionName: '',
        description: ''
      },

      // 表单验证规则
      sensorFormRules: {
        sensorType: [
          {
            required: true,
            message: '请选择传感器类型',
            trigger: 'change'
          }
        ],
        supplierName: [
          {
            required: true,
            message: '请选择供应商',
            trigger: 'change'
          }
        ],
        resolution: [
          {
            validator: this.checkResolution,
            trigger: 'blur'
          }
        ],
        hfov: createNumberValidationRules(5, 2, false),
        vfov: createNumberValidationRules(5, 2, false)
      },

      // Modality表单验证规则
      modalityFormRules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入编码',
            trigger: 'blur'
          }
        ],
        sensorType: [
          {
            required: true,
            message: '请选择传感器类型',
            trigger: 'change'
          }
        ]
      },
      originSensorData: {},
      originModalityData: {}
    }
  },

  computed: {
    // 统计总数
    totalCount() {
      return this.sensorsByType.reduce((total, group) => total + group.sensors.length, 0)
    },

    // 过滤后的传感器列表
    filteredSensors() {
      let filtered = this.currentSensorList

      // 根据传感器类型筛选
      if (this.selectedBrands.length > 0) {
        filtered = filtered.filter(sensor => this.selectedBrands.includes(sensor.category))
      }

      // 搜索关键词筛选由后端接口处理，前端不再处理

      return filtered
    },

    // 抽屉标题
    drawerTitle() {
      if (this.activeMainTab === 'modality') {
        switch (this.drawerMode) {
          case 'add':
            return '新增传感器'
          case 'edit':
            return `${this.originModalityData.code || '编辑传感器'}`
          case 'view':
          default:
            return `${this.originModalityData.code || '传感器详情'}`
        }
      } else {
        switch (this.drawerMode) {
          case 'add':
            return '新增传感器'
          case 'edit':
            return `${this.originSensorData.sensorType} | ${this.originSensorData.supplierName}`
          case 'view':
          default:
            return `${this.originSensorData.sensorType} | ${this.originSensorData.supplierName}`
        }
      }
    },

    // 是否为只读模式
    isReadonly() {
      return this.drawerMode === 'view'
    },

    // 按传感器类型分组的数据
    sensorsByType() {
      if (this.activeSensorTab !== 'all') {
        // 如果选择了特定类型，只显示该类型
        const selectedGroup = this.sensorGroupData.find(group => group.code === this.activeSensorTab)
        if (selectedGroup && selectedGroup.modalitySensorVOS.length > 0) {
          let sensors = selectedGroup.modalitySensorVOS.map(sensor => ({
            ...sensor,
            category: selectedGroup.code,
            categoryName: selectedGroup.name
          }))

          // 应用传感器类型筛选
          if (this.selectedBrands.length > 0) {
            // 如果选择了特定类型，检查当前分组是否在选中的类型中
            if (!this.selectedBrands.includes(selectedGroup.code)) {
              return [] // 如果当前类型未被选中，返回空数组
            }
          }

          return sensors.length > 0
            ? [
                {
                  code: selectedGroup.code,
                  name: selectedGroup.name,
                  sensors: sensors
                }
              ]
            : []
        }
        return []
      } else {
        // 显示所有类型，按类型分组
        let filteredGroups = this.sensorGroupData

        // 应用传感器类型筛选
        if (this.selectedBrands.length > 0) {
          filteredGroups = this.sensorGroupData.filter(group => this.selectedBrands.includes(group.code))
        }

        return filteredGroups.map(group => {
          let sensors = group.modalitySensorVOS.map(sensor => ({
            ...sensor,
            category: group.code,
            categoryName: group.name
          }))

          return {
            code: group.code,
            name: group.name,
            sensors: sensors
          }
        })
      }
    }
  },

  methods: {
    // 分辨率验证函数
    checkResolution(rule, value, callback) {
      const re = /^(0(\.\d{1,3})?|[1-9]\d*(\.\d{1,3})?)$/
      let checkHorizontalResolution = true,
        checkVerticalResolution = true

      if (this.editSensorData.horizontalResolution) {
        checkHorizontalResolution = re.test(this.editSensorData.horizontalResolution)
      }
      if (this.editSensorData.verticalResolution) {
        checkVerticalResolution = re.test(this.editSensorData.verticalResolution)
      }

      if (checkHorizontalResolution && checkVerticalResolution) {
        callback()
      } else {
        callback(new Error('请输入最多三位小数'))
      }
    },

    // 加载传感器数据
    async loadSensorData() {
      try {
        const params = {
          key: this.searchKeyword, // 输入框的key值
          type: this.activeMainTab // 当前tab选项type值
        }

        const response = await querySensorModalityListGroupByType(params)

        this.sensorGroupData = response.data || []

        // 生成传感器类型选项卡
        this.generateSensorTabs()

        // 生成品牌列表
        this.generateBrandList()

        // 设置当前显示的传感器列表
        this.updateCurrentSensorList()
      } catch (error) {
        console.error('加载传感器数据失败:', error)
        //this.$message.error('加载传感器数据失败')
      }
    },

    // 生成传感器类型选项卡
    generateSensorTabs() {
      this.sensorTabs = this.sensorGroupData.map(group => ({
        name: group.code,
        label: group.name
      }))

      // 如果当前选中的tab不存在且不是'all'，设置为第一个
      if (
        this.sensorTabs.length > 0 &&
        this.activeSensorTab !== 'all' &&
        !this.sensorTabs.find(tab => tab.name === this.activeSensorTab)
      ) {
        this.activeSensorTab = this.sensorTabs[0].name
      }
    },

    // 生成品牌列表（使用传感器类型作为筛选项）
    generateBrandList() {
      // 将传感器类型作为品牌筛选项
      this.brandList = this.sensorGroupData.map(group => ({
        code: group.code,
        name: group.name
      }))
    },

    // 更新当前显示的传感器列表
    updateCurrentSensorList() {
      if (this.activeSensorTab === 'all') {
        // 显示所有传感器
        this.currentSensorList = []
        this.sensorGroupData.forEach(group => {
          this.currentSensorList.push(
            ...group.modalitySensorVOS.map(sensor => ({
              ...sensor,
              category: group.code,
              categoryName: group.name
            }))
          )
        })
      } else {
        // 显示特定类型的传感器
        const selectedGroup = this.sensorGroupData.find(group => group.code === this.activeSensorTab)
        this.currentSensorList = selectedGroup
          ? selectedGroup.modalitySensorVOS.map(sensor => ({
              ...sensor,
              category: selectedGroup.code,
              categoryName: selectedGroup.name
            }))
          : []
      }
    },

    // 搜索处理 - 点击搜索图标或回车键时调用查询接口
    handleSearch() {
      this.loadSensorData()
    },

    // 选择全部品牌
    selectAllBrands() {
      // 点击"全部"，清空其他选择
      this.selectedBrands = []
    },

    // 切换品牌选择
    toggleBrand(brandCode) {
      const index = this.selectedBrands.indexOf(brandCode)
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedBrands.splice(index, 1)
      } else {
        // 如果未选中，则添加选择
        this.selectedBrands.push(brandCode)
      }
    },

    // 获取传感器图片地址
    getSensorImage(sensor) {
      switch (sensor.sensorType) {
        case 'camera':
          return require('@/assets/images/camera.png')
        case 'lidar':
          return require('@/assets/images/lidar.png')
        case 'radar':
          return require('@/assets/images/radar.png')
        case 'CAN':
          return require('@/assets/images/can.png')
        case 'ECU':
          return require('@/assets/images/ecu.png')
        case 'USS':
          return require('@/assets/images/uss.png')
        case 'IMU':
          return require('@/assets/images/imu.png')
        case 'TBOX':
          return require('@/assets/images/tbox.png')
        case '100C':
          return require('@/assets/images/100c.png')
        case 'VehicleHardware':
          return require('@/assets/images/whole-car.png')
        case 'domain_controller':
          return require('@/assets/images/control.png')
        default:
          return require('@/assets/images/whole-car.png')
      }
    },

    // 获取传感器型号规格
    getSensorSpec(sensor) {
      const model = sensor.model || ''
      const specification = sensor.specification || ''
      return `${model}${specification}`.trim()
    },

    // 获取传感器标签文本（用于modality模式）
    getSensorTagText(sensor) {
      // 显示供应商名称和型号
      const supplierName = sensor.supplierName || ''
      const model = sensor.model || ''
      return `${supplierName} ${model}`.trim() || sensor.id
    },

    // 获取传感器标签样式类（用于modality模式）
    getSensorTagClass(sensor) {
      // 根据传感器类型返回不同的样式类
      const typeClassMap = {
        lidar: 'tag-lidar',
        camera: 'tag-camera',
        radar: 'tag-radar',
        can: 'tag-can',
        ecu: 'tag-ecu',
        uss: 'tag-uss'
      }
      return typeClassMap[sensor.sensorType?.toLowerCase()] || 'tag-default'
    },

    // 打开查看抽屉（点击卡片）
    openViewDrawer(sensor) {
      this.$nextTick(() => {
        if (this.$refs.sensorFormRef) {
          this.$refs.sensorFormRef.clearValidate()
        }
        if (this.$refs.modalityFormRef) {
          this.$refs.modalityFormRef.clearValidate()
        }
      })
      if (this.activeMainTab === 'modality') {
        getFtmVehicleModality(sensor.id).then(res => {
          this.editModalityData = res.data
          this.originModalityData = { ...res.data }
          this.drawerMode = 'view'
          this.showEditDrawer = true
        })
      } else {
        getFtmSensor(sensor.id).then(res => {
          this.editSensorData = res.data
          // 拆分分辨率字符串为水平和垂直分辨率
          if (this.editSensorData.resolution) {
            const resolutionParts = this.editSensorData.resolution.split('*')
            this.editSensorData.horizontalResolution = resolutionParts[0] || ''
            this.editSensorData.verticalResolution = resolutionParts[1] || ''
          }
          this.originSensorData = { ...res.data }
          this.drawerMode = 'view'
          this.showEditDrawer = true
        })
      }
    },

    // 打开新增抽屉（点击新增按钮）
    openAddDrawer(typeGroup) {
      // 如果抽屉已经打开，先关闭再重新打开
      if (this.showEditDrawer) {
        this.closeEditDrawer()
        // 等待关闭动画完成后再打开
        this.$nextTick(() => {
          this.openAddDrawerInternal(typeGroup)
        })
      } else {
        this.openAddDrawerInternal(typeGroup)
      }
    },

    // 内部方法：实际打开新增抽屉
    openAddDrawerInternal(typeGroup) {
      // 重置数据
      if (this.activeMainTab === 'modality') {
        this.editModalityData = {
          id: '',
          name: '',
          code: '',
          sensorType: typeGroup ? typeGroup.code : '',
          sensorTypeName: typeGroup ? typeGroup.name : '',
          alias: '',
          installationPosition: '',
          installationPositionName: '',
          description: ''
        }
      } else {
        this.editSensorData = {
          id: '',
          sensorType: typeGroup ? typeGroup.code : '',
          sensorTypeName: typeGroup ? typeGroup.name : '',
          supplierId: '',
          supplierName: '',
          model: '',
          alias: '',
          specification: '',
          resolution: '',
          hfov: '',
          vfov: '',
          remark: '',
          photo: []
        }
      }

      this.drawerMode = 'add'
      this.showEditDrawer = true

      // 确保表单校验被清除
      this.$nextTick(() => {
        if (this.$refs.sensorFormRef) {
          this.$refs.sensorFormRef.clearValidate()
        }
        if (this.$refs.modalityFormRef) {
          this.$refs.modalityFormRef.clearValidate()
        }
      })
    },

    // 进入编辑模式（从查看模式点击编辑按钮）
    enterEditMode() {
      this.drawerMode = 'edit'
      // 清除表单校验
      this.$nextTick(() => {
        if (this.$refs.sensorFormRef) {
          this.$refs.sensorFormRef.clearValidate()
        }
        if (this.$refs.modalityFormRef) {
          this.$refs.modalityFormRef.clearValidate()
        }
      })
    },

    // 关闭抽屉
    closeEditDrawer() {
      this.showEditDrawer = false
      this.drawerMode = 'view'
      // 清除表单校验
      this.$nextTick(() => {
        if (this.$refs.sensorFormRef) {
          this.$refs.sensorFormRef.clearValidate()
        }
        if (this.$refs.modalityFormRef) {
          this.$refs.modalityFormRef.clearValidate()
        }
      })
      this.editSensorData = {
        id: '',
        sensorType: '',
        sensorTypeName: '',
        supplierId: '',
        supplierName: '',
        model: '',
        alias: '',
        specification: '',
        resolution: '',
        horizontalResolution: '',
        verticalResolution: '',
        hfov: '',
        vfov: '',
        remark: '',
        photo: []
      }
      this.editModalityData = {
        id: '',
        name: '',
        code: '',
        sensorType: '',
        sensorTypeName: '',
        alias: '',
        installationPosition: '',
        installationPositionName: '',
        description: ''
      }
    },

    // 取消编辑（从编辑/新增模式返回）
    cancelEdit() {
      if (this.drawerMode === 'add') {
        // 新增模式直接关闭
        this.closeEditDrawer()
      } else {
        // 编辑模式返回查看模式
        this.drawerMode = 'view'
        // 清除表单校验
        this.$nextTick(() => {
          if (this.$refs.sensorFormRef) {
            this.$refs.sensorFormRef.clearValidate()
          }
          if (this.$refs.modalityFormRef) {
            this.$refs.modalityFormRef.clearValidate()
          }
        })
        // 重新加载原始数据，恢复未保存的修改
        this.reloadCurrentSensorData()
      }
    },

    // 重新加载当前传感器数据
    reloadCurrentSensorData() {
      if (this.activeMainTab === 'modality') {
        // Modality模式：重新获取modality数据
        if (this.editModalityData.id) {
          getFtmVehicleModality(this.editModalityData.id)
            .then(res => {
              this.editModalityData = res.data
            })
            .catch(error => {
              console.error('重新加载modality数据失败:', error)
              //this.$message.error('重新加载数据失败')
            })
        }
      } else {
        // Sensor模式：重新获取sensor数据
        if (this.editSensorData.id) {
          getFtmSensor(this.editSensorData.id)
            .then(res => {
              this.editSensorData = res.data
              // 拆分分辨率字符串为水平和垂直分辨率
              if (this.editSensorData.resolution) {
                const resolutionParts = this.editSensorData.resolution.split('*')
                this.editSensorData.horizontalResolution = resolutionParts[0] || ''
                this.editSensorData.verticalResolution = resolutionParts[1] || ''
              }
            })
            .catch(error => {
              console.error('重新加载sensor数据失败:', error)
              //this.$message.error('重新加载数据失败')
            })
        }
      }
    },

    // 保存传感器
    saveSensor() {
      if (this.activeMainTab === 'modality') {
        // Modality模式
        this.$refs.modalityFormRef.validate(valid => {
          if (!valid) return

          if (this.drawerMode === 'add') {
            // 新增模式
            saveFtmVehicleModality(this.editModalityData)
              .then(() => {
                this.$message.success('新增成功')
                this.closeEditDrawer()
                this.loadSensorData()
              })
              .catch(error => {
                console.error('新增传感器失败:', error)
                //this.$message.error('新增失败')
              })
          } else if (this.drawerMode === 'edit') {
            // 编辑模式
            updateFtmVehicleModality(this.editModalityData)
              .then(() => {
                this.$message.success('更新成功')
                this.originModalityData = { ...this.editModalityData }
                console.log('***', this.originModalityData)
                // this.reloadCurrentSensorData()
                this.drawerMode = 'view' // 保存后回到查看模式
                this.loadSensorData()
              })
              .catch(error => {
                console.error('更新传感器失败:', error)
                // this.$message.error('更新失败')
              })
          }
        })
      } else {
        // Sensor模式
        this.$refs.sensorFormRef.validate(valid => {
          if (!valid) return

          // 如果是非摄像头类型，把分辨率，水平垂直视角value置空
          if (this.editSensorData.sensorType !== 'camera') {
            this.editSensorData.resolution = null
            this.editSensorData.vfov = null
            this.editSensorData.hfov = null
            this.editSensorData.exposureInterval = null
          } else {
            // 摄像头类型处理分辨率：合并水平和垂直分辨率
            this.editSensorData.resolution =
              (this.editSensorData.horizontalResolution || '') + '*' + (this.editSensorData.verticalResolution || '')
          }

          if (this.drawerMode === 'add') {
            // 新增模式
            saveFtmSensor(this.editSensorData)
              .then(() => {
                this.$message.success('新增成功')
                this.closeEditDrawer()
                this.loadSensorData()
              })
              .catch(error => {
                console.error('新增传感器失败:', error)
                // this.$message.error('新增失败')
              })
          } else if (this.drawerMode === 'edit') {
            // 编辑模式
            updateFtmSensor(this.editSensorData)
              .then(() => {
                this.$message.success('更新成功')
                this.originSensorData = { ...this.editSensorData }
                this.drawerMode = 'view' // 保存后回到查看模式
                this.loadSensorData()
              })
              .catch(error => {
                console.error('更新传感器失败:', error)
                // this.$message.error('更新失败')
              })
          }
        })
      }
    },

    // 传感器类型变化回调
    changeSensorType(e) {
      this.editSensorData.sensorTypeName = e.node?.name
    },

    // 供应商变化回调
    changeSupplierName(e) {
      this.editSensorData.supplierName = e.node?.name
    },

    // Modality模式下传感器类型变化回调
    changeModalitySensorType(e) {
      this.editModalityData.sensorTypeName = e?.name
    },

    // Modality模式下安装位置变化回调
    changeModalityInstallationPosition(e) {
      this.editModalityData.installationPositionName = e?.name
    },

    // 处理tag关闭事件（删除传感器）
    handleTagClose(sensor, typeGroup) {
      this.$confirm(`确定要删除传感器 "${sensor.code}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 根据当前模式调用相应的删除API
          if (this.activeMainTab === 'modality') {
            // 调用modality删除API
            const param = { id: sensor.id }
            deleteFtmVehicleModality(param)
              .then(() => {
                this.$message.success('删除成功')
                this.closeEditDrawer()
                this.loadSensorData() // 重新加载数据
              })
              .catch(error => {
                console.error('删除传感器失败:', error)
                //this.$message.error('删除失败')
              })
          } else {
            // 调用sensor删除API
            const param = { id: sensor.id }
            deleteFtmSensor(param)
              .then(() => {
                this.$message.success('删除成功')
                this.loadSensorData() // 重新加载数据
              })
              .catch(error => {
                console.error('删除传感器失败:', error)
                //this.$message.error('删除失败')
              })
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    // 删除传感器
    deleteSensor() {
      this.$confirm('确定要删除这个传感器吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 这里添加删除逻辑
          if (this.activeMainTab === 'modality') {
            const param = { id: this.editModalityData.id }
            deleteFtmVehicleModality(param)
              .then(() => {
                this.$message.success('删除成功')
                this.closeEditDrawer()
                this.loadSensorData()
              })
              .catch(error => {
                console.error('删除传感器失败:', error)
                //this.$message.error('删除失败')
              })
            return
          } else {
            const param = { id: this.editSensorData.id }
            deleteFtmSensor(param).then(() => {
              this.$message.success('删除成功')
              this.closeEditDrawer()
              this.loadSensorData()
            })
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    // 添加传感器
    handleAddSensor() {
      this.$message.info('添加传感器功能')
    },

    // tab切换处理
    handleTabChange() {
      this.loadSensorData()
    }
  },

  // 生命周期钩子
  created() {
    this.loadSensorData()
  },

  // 监听器
  watch: {
    // 监听主选项卡变化
    activeMainTab() {
      // 切换tab时关闭drawer
      if (this.showEditDrawer) {
        this.closeEditDrawer()
      }
      this.loadSensorData()
    },

    // 监听传感器选项卡变化
    activeSensorTab() {
      this.updateCurrentSensorList()
    }
  }
}
</script>

<style lang="scss" scoped>
.sensor-management {
  height: calc(100vh - 130px);
  // 主卡片样式
  .main-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    :deep(.el-card__body) {
      padding-top: 0;
      padding-bottom: 0;
      flex:1;
      height: 0;
    }
  }
  // 搜索区域
  .search-container {
    width: 400px;
    height: 32px;
    .search-input-group {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;

      .search-input {
        flex: 1;

        :deep(.el-input-group__append) {
          background: #f8f8fb;
          border-color: #d0d4d8;
        }
      }

      .filter-btn {
        width: 24px;
        height: 24px;
        padding: 0;
        border: none;
        background: transparent;
      }
    }
  }

  // 主要内容区域
  .main-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 16px 16px 16px;

    position: relative;
    transition: all 0.3s ease;
     .warning-tag {
            background: #FFB03A;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 24px;
            transition: background-color 0.3s ease;

            &:hover {
              background: #E6690A;
            }

            // 编辑状态下的绿色样式
            &.editing-state {
              background: #67C23A;

              &:hover {
                background: #5DAE34;
              }
            }
          }

          .cancel-tag {
            background: #909399;
            border-radius: 2px;
            color: white;
            font-size: 10px;
            border: none;
            height: 24px;
            margin-left: 8px;

            &:hover {
              background: #73767A;
            }
          }
    .delete-version-btn {
      padding: 6px 11px;
      background: #FF6E6F;
      border: 1px solid #FF6E6F;
      border-radius: 2px;
      color: white;
      font-size: 12px;
      font-family: 'Bosch Sans Global', sans-serif;
      font-weight: 400;
      line-height: 12px;
      height: 24px;
      &:hover {
        background: #FF5A5B;
        border-color: #FF5A5B;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    &.with-drawer {
      .content-card.shrink {
        width: calc(100% - 460px - 16px);
        margin-right: 16px;
      }
    }

    .content-header {
      margin-bottom: 8px; // 减少底部间距

      .main-tabs {
        display: flex;
        gap: 8px;
        :deep(.main-tab-container) {
          .el-tabs__header {
            margin: 0;
            border-bottom: 1px solid #eff1f2;
          }

          .el-tabs__nav-wrap {
            &::after {
              display: none;
            }
          }

          .el-tabs__item {
            color: #4e5256;
            font-size: 16px;
            font-weight: 400;
            padding: 6px 30px 4px 0; // 减少上下内边距，增加右侧间距
            margin-right: 20px;

            &.is-active {
              color: #5755ff;
              font-weight: 700;
            }
          }

          .el-tabs__active-bar {
            background: #5755ff;
            height: 2px;
          }
        }
      }
    }

    .control-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left-controls {
        display: flex;
        align-items: center;
        gap: 10px;

        .display-mode {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            color: #4e5256;
            font-size: 12px;
          }

          .mode-select {
            width: 80px;

            :deep(.el-input__inner) {
              font-size: 12px;
              padding: 6px 12px;
              border-radius: 2px;
            }
          }
        }

        .control-divider {
          width: 1px;
          height: 13px;
          background: #d0d4d8;
          border-radius: 1px;
        }

        .sort-control {
          display: flex;
          align-items: center;
          gap: 8px;

          .sort-select {
            width: 100px;

            :deep(.el-input__inner) {
              font-size: 12px;
              padding: 6px 12px;
              border-radius: 2px;
            }
          }

          .sort-direction-btn {
            width: 24px;
            height: 24px;
            padding: 0;
          }
        }

        .statistics {
          display: flex;
          align-items: center;
          gap: 8px;

          .stats-icon {
            color: #232628;
            font-size: 16px;
          }

          .stats-label {
            color: #4e5256;
            font-size: 12px;
          }

          .stats-value {
            color: #5755ff;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }

    // 内容卡片
    .content-card {
      flex: 1;
      background: white;
      box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      transition: all 0.3s ease;
      height: calc(100vh - 300px);

      &.shrink {
        flex: 1;
      }

      :deep(.el-card__body) {
        padding: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      // 品牌筛选区域 - 参考FleetsManagementNew.vue的设计
      .brand-filter-section {
        padding: 20px;
        border-bottom: 1px solid #eff1f2;
        background-color: white;

        .filter-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .filter-tag {
            padding: 7px 15px;
            background: #f5f5ff;
            border: 1px solid #ddddff;
            border-radius: 2px;
            color: #5755ff;
            font-size: 14px;
            font-weight: 400;
            line-height: 14px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: #e8f1ff;
              border-color: #b3d4ff;
            }

            &.active {
              background: #5755ff;
              border-color: #5755ff;
              color: white;
            }
          }
        }
      }

      .sensor-content {
        padding: 12px 16px 16px;
        overflow: auto;
        flex: 1; // 使用flex布局占据剩余空间

        .sensor-tabs {
          margin-bottom: 16px;

          :deep(.sensor-tab-container) {
            .el-tabs__header {
              margin: 0;
              border-bottom: 1px solid #eff1f2;
            }

            .el-tabs__nav-wrap {
              &::after {
                display: none;
              }
            }

            .el-tabs__item {
              color: #4e5256;
              font-size: 16px;
              font-weight: 400;
              padding: 9px 20px 7px 0;
              position: relative;

              &.is-active {
                color: #5755ff;
                font-weight: 700;
              }

              .tab-label {
                margin-right: 8px;
              }

              .add-btn {
                width: 20px;
                height: 20px;
                padding: 0;
                border-radius: 2px;
                border: 1px solid #5755ff;

                :deep(.el-icon-plus) {
                  font-size: 10px;
                }
              }
            }

            .el-tabs__active-bar {
              background: #5755ff;
              height: 2px;
            }
          }
        }

        // 按传感器类型分组显示 - Sensor模式
        .sensors-by-type {
          .sensor-type-group {
            margin-bottom: 32px;

            &:last-child {
              margin-bottom: 0;
            }

            // 传感器类型标签
            .sensor-type-label {
              display: flex;
              align-items: center;
              margin-bottom: 16px;
              padding: 8px 12px;
              width: 100%;
              border-bottom: 1px solid #eff1f2;
              justify-content: space-between;

              .type-name {
                color: #495057;
                font-size: 16px;
                font-weight: 500;
                margin-right: 8px;
              }
              .add-button {
                width: 30px;
                height: 20px;
              }
            }
          }
        }

        // 按传感器类型显示卡片 - Modality模式
        .modality-by-type {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 24px;
          padding: 0 12px;

          .modality-type-card {
            .type-card {
              border-radius: 8px;
              border: 1px solid #e4e7ed;
              transition: all 0.3s ease;

              &:hover {
                box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
                border-color: #c0c4cc;
              }

              :deep(.el-card__header) {
                padding: 16px 20px;
                border-bottom: 1px solid #e4e7ed;
                background-color: #fafafa;

                .card-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .type-title {
                    color: #303133;
                    font-size: 16px;
                    font-weight: 600;
                  }

                  .add-button {
                    width: 30px;
                    height: 20px;
                  }
                }
              }

              :deep(.el-card__body) {
                padding: 20px;
                min-height: 120px;
              }

              .sensor-tags-container {
                .sensor-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 8px;

                  .sensor-tag {
                    cursor: pointer;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 12px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    border: 1px solid;
                    width: auto; // 不限制宽度
                    min-width: auto; // 不限制最小宽度
                    max-width: none; // 不限制最大宽度

                    &:hover {
                      transform: translateY(-1px);
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    }

                    .sensor-tag-content {
                      display: flex;
                      flex-direction: column;
                      align-items: flex-start;
                      line-height: 1.2;

                      .sensor-code {
                        font-weight: 600;
                        font-size: 12px;
                        margin-bottom: 2px;
                      }

                      .sensor-name {
                        font-weight: 400;
                        font-size: 11px;
                        opacity: 0.8;
                      }
                    }

                    // 关闭按钮样式
                    :deep(.el-tag__close) {
                      color: #666666; /* 浅黑色 */
                      background-color: transparent; /* 无背景颜色 */
                      border: none;
                      font-size: 12px;
                      margin-left: 4px;
                      transition: color 0.3s ease;

                      &:hover {
                        color: #333333; /* hover时稍微深一点 */
                        background-color: transparent; /* 保持无背景 */
                      }
                    }

                    // 不同传感器类型的颜色
                    &.tag-lidar {
                      background: #fff7e6;
                      border-color: #ffd591;
                      color: #d46b08;
                    }

                    &.tag-camera {
                      background: #e6f7ff;
                      border-color: #91d5ff;
                      color: #6991d1;
                    }

                    &.tag-radar {
                      background: #f6ffed;
                      border-color: #b7eb8f;
                      color: #389e0d;
                    }

                    &.tag-can {
                      background: #fff1f0;
                      border-color: #ffadd2;
                      color: #cf1322;
                    }

                    &.tag-ecu {
                      background: #f9f0ff;
                      border-color: #d3adf7;
                      color: #722ed1;
                    }

                    &.tag-uss {
                      background: #e6fffb;
                      border-color: #87e8de;
                      color: #08979c;
                    }

                    &.tag-default {
                      background: #f5f5f5;
                      border-color: #d9d9d9;
                      color: #595959;
                    }
                  }
                }

                .empty-state {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 80px;

                  .empty-text {
                    color: #909399;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }

        .sensor-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          padding: 0 12px;

          .sensor-card {
            min-width: 290px; // 最小宽度限制
            height: 230px; // 固定高度
            flex: 1; // 在超大屏时平均分配空间
            max-width: calc(20% - 16px); // 超大屏时最多5列
            padding-bottom: 16px;
            background: white;
            border-radius: 2px;
            box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            gap: 12px;
            display: flex;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.15);
              transform: translateY(-2px);
            }

            .sensor-image-section {
              flex-direction: column;
              justify-content: flex-end;
              align-items: flex-start;
              display: flex;
              width: 100%;
              height: 150px;

              .sensor-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .sensor-info-section {
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              gap: 8px;
              display: flex;
              width: 100%;
              padding: 0 16px;

              .sensor-title-row {
                justify-content: flex-start;
                align-items: center;
                gap: 8px;
                display: inline-flex;

                .sensor-type {
                  color: black;
                  font-size: 16px;
                  font-family: 'Bosch Sans', sans-serif;
                  font-weight: 700;
                  line-height: 24px;
                  word-wrap: break-word;
                }

                .sensor-brand {
                  color: #5755FF;
                  font-size: 14px;
                  font-family: 'Bosch Sans', sans-serif;
                  font-weight: 600;
                  line-height: 24px;
                  word-wrap: break-word;
                }
              }

              .sensor-spec-row {
                justify-content: flex-start;
                align-items: center;
                gap: 8px;
                display: inline-flex;

                .spec-label {
                  color: #8a9097;
                  font-size: 12px;
                  font-family: 'Bosch Sans Global', sans-serif;
                  font-weight: 400;
                  line-height: 12px;
                  word-wrap: break-word;
                }

                .spec-value {
                  color: #8a9097;
                  font-size: 12px;
                  font-family: 'Bosch Sans Global', sans-serif;
                  font-weight: 400;
                  line-height: 12px;
                  word-wrap: break-word;
                  max-width: 170px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  flex: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sensor-management {
    .page-header {
      padding: 12px;

      .breadcrumb-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }

    .search-section {
      padding: 12px;

      .search-container {
        flex-direction: column;
        gap: 12px;

        .search-input-group {
          width: 100%;
        }
      }
    }

    .main-content {
      padding: 12px;

      .control-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .left-controls {
          flex-wrap: wrap;
        }
      }

      .content-card {
        .sensor-content {
          .sensor-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }
        }
      }
    }
  }
}

// 超大屏响应式设计 - sensor模式
@media (min-width: 1600px) {
  .sensor-management .main-content .content-card .sensor-content .sensor-grid {
    .sensor-card {
      flex: 0 0 calc(20% - 16px); // 5列布局，每列占20%减去间距
      max-width: calc(20% - 16px);
      min-width: 290px; // 保持最小宽度限制
      height: 230px; // 保持固定高度
    }
  }
}

// 大屏响应式设计 - sensor模式
@media (min-width: 1200px) and (max-width: 1599px) {
  .sensor-management .main-content .content-card .sensor-content .sensor-grid {
    .sensor-card {
      flex: 0 0 calc(25% - 15px); // 4列布局
      max-width: calc(25% - 15px);
      min-width: 290px;
      height: 230px;
    }
  }
}

// 中屏响应式设计 - sensor模式
@media (min-width: 900px) and (max-width: 1199px) {
  .sensor-management .main-content .content-card .sensor-content .sensor-grid {
    .sensor-card {
      flex: 0 0 calc(33.333% - 14px); // 3列布局
      max-width: calc(33.333% - 14px);
      min-width: 290px;
      height: 230px;
    }
  }
}
</style>
<style>
.sensor-management .main-content .content-card[data-v-59a19b08] .el-card__body {
  height: 100%;
}

/* 编辑抽屉样式 */
.edit-drawer {
  width: 460px;
  position: absolute;
  right: 0;
  top: 47px;
  height: calc(100% - 68px);
  z-index: 100;
  animation: slideInRight 0.3s ease-out;
}

.edit-drawer .drawer-container {
  width: 100%;
  height: 100%;
  box-shadow: 0px 2px 24px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  background: white;
  display: flex;
  flex-direction: column;
}

.edit-drawer .drawer-header {
  height: 60px;
  padding: 20px 20px 10px 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.edit-drawer .drawer-title {
  flex: 1;
  color: #232628;
  font-size: 18px;
  font-family: 'Bosch Sans', sans-serif;
  font-weight: 400;
  line-height: 18px;
}

.edit-drawer .header-button-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
  padding:0 20px;
}

.edit-drawer .header-button-group .confirm-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.edit-drawer .drawer-close {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-drawer .drawer-close:hover {
  opacity: 0.7;
}

.edit-drawer .drawer-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.edit-drawer .form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.edit-drawer .form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e4e7ed;
}

.edit-drawer .form-section .el-form-item {
  margin-bottom: 0;
  align-content: center;
}

.edit-drawer .form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.edit-drawer .form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-drawer .form-row .el-form-item__label {
  width: 100px !important;
  text-align: left;
  color: #4e5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 23px;
  display: flex;
  align-items: center;
  height: 32px; /* 确保固定高度 */
}

.edit-drawer .form-row .el-form-item__content {
  width: 214px;
  margin-left: 0 !important;
  display: flex;
  align-items: center;
  min-height: 32px; /* 确保最小高度与label一致 */
}

.edit-drawer .form-row-textarea .el-form-item__label {
  width: 100px !important;
  text-align: left;
  color: #4e5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 23px;
  display: flex;
  align-items: center;
  height: 32px;
}

.edit-drawer .form-row-textarea .el-form-item__content {
  width: 100%;
  margin-left: 0 !important;
  display: flex;
  align-items: flex-start;
}

.edit-drawer .form-row-upload .el-form-item__label {
  width: 100px !important;
  text-align: left;
  color: #4e5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 23px;
  display: flex;
  align-items: flex-start;
}

.edit-drawer .form-row-upload .el-form-item__content {
  width: 100%;
  margin-left: 0 !important;
}

.edit-drawer .form-label {
  color: #4e5256;
  font-size: 12px;
  font-family: 'Bosch Office Sans', sans-serif;
  font-weight: 400;
  line-height: 23px;
  min-width: 80px;
}

.edit-drawer .form-value {
  color: #232628;
  font-size: 12px;
  font-family: 'Bosch Sans Global', sans-serif;
  font-weight: 400;
  line-height: 23px;
  text-align: left;
  display: flex;
  align-items: center;
  min-height: 32px; /* 确保与label高度一致 */
  width: 100%; /* 确保占满容器宽度 */
}

.edit-drawer .form-value-multiline {
  white-space: pre-wrap;
  word-break: break-word;
}

.edit-drawer .form-row-full {
  display: flex;
  align-items: center;
}

.edit-drawer .form-textarea {
  width: 100%;
}

.edit-drawer .drawer-footer {
  padding: 10px 20px 20px 20px;
  background: white;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}

.edit-drawer .button-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
}

.edit-drawer .button-group .el-button[type='danger'] {
  position: absolute;
  left: 0;
}

.edit-drawer .confirm-buttons {
  display: flex;
  gap: 8px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 分辨率输入框样式 */
.resolution-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.resolution-container .resolution-input {
  flex: 1;
  min-width: 0;
}

.resolution-container .resolution-separator {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 0 4px;
}
</style>
<style>
.el-upload-list.el-upload-list--text,
.el-upload-list {
  width: 320px;
}
</style>
