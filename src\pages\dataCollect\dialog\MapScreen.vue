<template>
  <div class="map-screen" :style="{ transform: 'scale(' + transform + ')' }">
    <header><div class="header-text">Task Record</div></header>
    <main>
      <div class="left-aside">
        <div class="left-item content-border">
          <div class="title">
            <span class="symbol">[</span>
            <span class="title-text">Event Statistic</span>
            <span class="symbol">]</span>
          </div>
          <div class="item-content">
            <div id="event-statistic"></div>
          </div>
        </div>
      </div>
      <div class="middle-main">
        <div class="middle-top content-border">
          <div class="middle-top-item">
            <div class="name">Duration：</div>
            <div class="num" v-if="durationTime.d">
              <count-to
                :separator="''"
                :start-val="durationTimeStart.d"
                :end-val="durationTime.d"
                :duration="1000"
              />
            </div>
            <div v-if="durationTime.d" class="unit">Day</div>
            <div class="num" v-if="durationTime.d || durationTime.h">
              <count-to
                :separator="''"
                :start-val="durationTimeStart.h"
                :end-val="durationTime.h"
                :duration="1000"
              />
            </div>
            <div v-if="durationTime.d || durationTime.h" class="unit">Hour</div>
            <div class="num">
              <count-to
                :separator="''"
                :start-val="durationTimeStart.m"
                :end-val="durationTime.m"
                :duration="1000"
              />
            </div>
            <div class="unit">Min</div>
          </div>
          <div class="middle-top-item">
            <span class="icon iconfont icon-cunweih"></span>
            <div class="name">Effective：</div>
            <div class="num" v-if="effectiveTime.d">
              <count-to
                :separator="''"
                :start-val="effectiveTimeStart.d"
                :end-val="effectiveTime.d"
                :duration="1000"
              />
            </div>
            <div v-if="effectiveTime.d" class="unit">Day</div>
            <div class="num" v-if="effectiveTime.d || effectiveTime.h">
              <count-to
                :separator="''"
                :start-val="effectiveTimeStart.h"
                :end-val="effectiveTime.h"
                :duration="1000"
              />
            </div>
            <div v-if="effectiveTime.d || effectiveTime.h" class="unit">
              Hour
            </div>
            <div class="num">
              <count-to
                :separator="''"
                :start-val="effectiveTimeStart.m"
                :end-val="effectiveTime.m"
                :duration="1000"
              />
            </div>
            <div class="unit">Min</div>
          </div>
          <div class="middle-top-item">
            <span class="icon iconfont icon-ruguan"></span>
            <div class="name">Records：</div>
            <div class="num">
              <count-to
                :separator="''"
                :start-val="recordsNumStart"
                :end-val="recordsNum"
                :duration="1000"
              />
            </div>
            <!-- <div class="unit">个</div> -->
          </div>
        </div>
        <div class="middle-bottom content-border">
          <div class="map">
            <v-map id="mapContent" ref="mapContent" />
          </div>
        </div>
      </div>
      <div class="right-aside">
        <div
          class="content-border"
          v-for="(item, index) in continuesTagRecordStatisticList"
          :key="index"
        >
          <div class="title">
            <span class="symbol">[</span>
            <span class="title-text" v-text="item.title"></span>
            <span class="symbol">]</span>
          </div>
          <div class="item-content">
            <div class="right-charts" :id="item.title"></div>
          </div>
        </div>
        <!-- <div class="content-border">
            <div class="title">
              <span class="symbol">[</span>
              <span class="title-text">Road Surface</span>
              <span class="symbol">]</span>
            </div>
            <div class="item-content">
              <div id="circle-static2"></div>
            </div>
          </div> -->
      </div>
    </main>
  </div>
  <!-- </div> -->
  <!-- </el-dialog> -->
</template>

<script>
// import Map from "@/components/map/MapOverview-static";
import Map from '@/components/map/MapOverview.vue'
import CountTo from '@/components/vue-count-to/vue-countTo.vue'
import { getTaskStatistic } from '@/apis/data-collect/vt-daq-task'
import { dateUtils } from '@/plugins/util.js'
import { ElDialog } from 'element-plus'
import * as $echarts from 'echarts'
export default {
  name: 'mapScreen',
  data() {
    return {
      dialogTitle: '',
      dialogVisible: false,
      mapIndex: 0,
      recordsNumStart: 0,
      recordsNum: 0,
      durationTimeStart: {
        d: 0,
        h: 0,
        m: 0
      },
      durationTime: {
        d: 0,
        h: 0,
        m: 0
      },
      effectiveTimeStart: {
        d: 0,
        h: 0,
        m: 0
      },
      effectiveTime: {
        d: 0,
        h: 0,
        m: 0
      },
      continuesTagRecordStatisticList: [],
      transform: 1
    }
  },
  components: {
    'v-map': Map,
    CountTo,
    ElDialog
  },
  mounted() {},
  methods: {
    show(row) {
      this.dialogVisible = true
      this.getTaskRecord(row)
      this.$nextTick(() => {
        if (this.transform === 1) {
          let parentWidth =
            document.querySelector('.map-screen').parentNode.offsetWidth
          let domwidth = document.querySelector('.map-screen').offsetWidth
          this.transform = parentWidth / domwidth
          document.querySelector('.map-screen').parentNode.style.height =
            document.querySelector('.map-screen').parentNode.offsetHeight *
              this.transform +
            'px'
        }
      })
      // this.$nextTick(() => {
      //   this.getEventStatistic();
      //   this.getCircleStatic1();
      //   this.getCircleStatic2();
      // });
      // setTimeout(() => {
      //   this.durationTime = dateUtils.dateCalculation(50000);
      //   this.effectiveTime = dateUtils.dateCalculation(100000);
      //   this.recordsNum = 566;
      // }, 1000);
    },
    getTaskRecord(row) {
      getTaskStatistic(row.id).then(res => {
        res.data.duration &&
          (this.durationTime = dateUtils.dateCalculation(res.data.duration))
        res.data.effectiveDuration &&
          (this.effectiveTime = dateUtils.dateCalculation(
            res.data.effectiveDuration
          ))
        res.data.recordCount && (this.recordsNum = res.data.recordCount)
        res.data.transientTagRecordStatisticList &&
          res.data.transientTagRecordStatisticList.length &&
          this.getEventStatistic(res.data.transientTagRecordStatisticList)
        res.data.trackDataList &&
          res.data.trackDataList.length &&
          this.getMapStatistics(res.data.trackDataList.reverse())
        if (
          res.data.continuesTagRecordStatisticList &&
          res.data.continuesTagRecordStatisticList.length
        ) {
          this.continuesTagRecordStatisticList =
            res.data.continuesTagRecordStatisticList
          // this.continuesTagRecordStatisticList();
          this.$nextTick(() => {
            res.data.continuesTagRecordStatisticList.forEach(val => {
              this.drawContinuesCharts(val)
            })
          })
        }
      })
    },
    getMapStatistics(list) {
      this.$refs.mapContent.show(list)
    },
    // continuesTagRecordStatisticList() {
    //   this.continuesTagRecordStatisticList.forEach((val) => {
    //     this.drawContinuesCharts(val);
    //   });
    // },
    getEventStatistic(tagGroups) {
      let colors = [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ]
      // let tagGroups = [
      //   "Shock",
      //   "Abnormal noise",
      //   "Brake",
      //   "Accelerate",
      //   "Turn left",
      //   "Turn right",
      // ];
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          x: 70,
          // y: 45,
          x2: 20
          // y2: 20,
          // borderWidth: 1,
        },
        xAxis: {
          // axisLabel: {
          //   // inside: true,
          //   color: '#fff'
          // },
          axisLine: {
            // show: false,
            lineStyle: {
              color: '#fff'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff'
          },
          type: 'value'
        },
        yAxis: {
          axisLine: {
            // show: false,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            // inside: true,
            color: '#fff'
          },
          type: 'category'
          // data: tagGroups,
          // z: 10,
        },
        series: [
          {
            data: tagGroups,
            // data: [2, 8, 17, 4, 10, 3],
            type: 'bar',
            // showBackground: true,
            // backgroundStyle: {
            //   color: "rgba(180, 180, 180, 0.2)",
            // },
            itemStyle: {
              color: function (param) {
                return colors[param.dataIndex % colors.length]
              }
            },
            barMaxWidth: 100
          }
        ]
      }
      let myChart = $echarts.init(
        document.getElementById('event-statistic'),
        'light'
      )
      myChart.setOption(option)
    },
    getCircleStatic1() {
      let tagGroups = [
        { name: 'Sunny day', value: 1066 },
        { name: 'Rainy day', value: 3066 },
        { name: 'Snowy day', value: 7066 },
        { name: 'Thunderstorm day', value: 2066 },
        { name: 'Foggy day', value: 4066 },
        { name: 'Hail days', value: 2066 }
      ]
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          top: '5%',
          left: 'center',
          textStyle: {
            //图例文字的样式
            color: '#fff'
            // fontSize: 16,
          }
        },
        series: [
          {
            name: 'Weather',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              // borderColor: "#fff",
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: tagGroups
          }
        ]
      }
      let myChart = $echarts.init(
        document.getElementById('circle-static1'),
        'light'
      )
      myChart.setOption(option)
    },
    drawContinuesCharts(tagGroups) {
      // let tagGroups = [
      //   { name: "White dotted line", value: 1066 },
      //   { name: "Solid white line", value: 735 },
      //   { name: "Yellow dotted line", value: 580 },
      //   { name: "Solid yellow line", value: 484 },
      //   { name: "Double white dotted line", value: 300 },
      //   { name: "Double yellow dotted line", value: 603 },
      // ];
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          top: '5%',
          left: 'center',
          textStyle: {
            //图例文字的样式
            color: '#fff'
            // fontSize: 16,
          }
        },
        series: [
          {
            name: 'Road Surface',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderRadius: 10,
              // borderColor: "#fff",
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: tagGroups.dataList
          }
        ]
      }
      let myChart = $echarts.init(
        document.getElementById(tagGroups.title),
        'light'
      )
      myChart.setOption(option)
    },
    cancel() {
      this.dialogVisible = false
      this.reset()
    },
    reset() {}
  }
}
</script>
<style lang="scss">
.map-dialog {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    height: 100%;
    padding: 0;
  }
}
</style>
<style scoped lang="scss">
.map-screen {
  * {
    &::-webkit-scrollbar {
      display: none;
    }
    box-sizing: border-box;
  }
  overflow: hidden;
  // height: 100%;
  height: 1080px;
  width: 1920px;
  transform-origin: left top;
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/mapScreen/blue-bg.png');
  header {
    height: 76px;
    margin-bottom: 19px;
    background: url('@/assets/images/mapScreen/blue-header.png') 100% 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .header-text {
      line-height: 55px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      text-align: center;
      font-size: 42px;
      color: #3fffff;
      // background-image: linear-gradient(
      //   0deg,
      //   #ff6e02 0%,
      //   #ffff00 50%,
      //   #ff6d00 100%
      // );
      background-clip: text;
      // -webkit-text-fill-color: transparent;
      letter-spacing: 3px;
    }
  }
  main {
    display: flex;
    flex-direction: row;
    // height: calc(500px - 95px);
    height: calc(100% - 95px);
    width: 100%;
    padding: 0 20px 27px 20px;
    .left-aside,
    .right-aside {
      overflow: auto;
      height: 100%;
      width: 400px;
      #event-statistic {
        height: 100%;
      }
      .right-charts {
        height: 300px;
      }
      // #circle-static1 {
      //   height: 300px;
      // }
      // #circle-static2 {
      //   height: 300px;
      // }
    }

    .left-item {
      height: 100%;
      .item-content {
        height: calc(100% - 34px);
      }
    }
    .middle-main {
      width: calc(100% - 800px);
      margin: 0 20px;
      // background: pink;
      display: flex;
      flex-direction: column;
      .middle-top {
        height: 60px;
        line-height: 60px;
        display: flex;
        justify-content: space-between;
        padding: 0 15px;
        margin-bottom: 12px;
        overflow-x: auto;
        .middle-top-item {
          display: flex;
          position: relative;
          line-height: 58px;
          .icon {
            font-size: 22px;
            color: #3fffff;
          }
          .name {
            white-space: nowrap;
            color: #3fffff;
            font-size: 20px;
            font-style: italic;
            margin: 0 5px 0 5px;
          }
          .num {
            color: #e0e404;
            font-size: 30px;
            font-weight: bold;
            font-style: italic;
            letter-spacing: 2px;
            white-space: nowrap;
          }
          .unit {
            white-space: nowrap;
            font-size: 14px;
            color: #e0e404;
            padding: 0 5px;
            // position: absolute;
            // bottom: -4px;
            // right: -23px;
          }
        }
      }
      .middle-bottom {
        height: calc(100% - 60px - 12px);
        width: 100%;
        .map {
          height: 100%;
          width: 100%;
        }
      }
    }
    .content-border {
      padding: 12px;
      background: rgba($color: #081626, $alpha: 0.3);
      border: 1px solid rgba($color: #436fa6, $alpha: 0.3);
      .title {
        color: #3fffff;
        font-size: 24px;
        margin-bottom: 10px;
        .title-text {
          margin: 0 10px;
        }
        .symbol {
          color: #007eff;
        }
      }
      &:not(:last-child) {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
