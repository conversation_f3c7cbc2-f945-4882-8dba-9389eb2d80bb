<template>
  <div>
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-tool-container">
          <div class="ltw-search-container ltw-tool-container">
            <ltw-input
              :placeholder="$t('只支持名称和编码搜索')"
              v-model="queryParam.key"
              clearable
              @clear="refresh"
            >
              <template #append>
                <el-button @click="refresh" id="search">
                  <ltw-icon icon-code="el-icon-search"></ltw-icon>
                </el-button>
              </template>
            </ltw-input>
          </div>
        </div>
<!--        <el-button class="filter-btn" @click="filterParkingLot" id="el-icon-filter">-->
<!--          <ltw-icon icon-code="el-icon-filter"></ltw-icon>-->
<!--          <span>{{ $t('筛选') }}</span>-->
<!--        </el-button>-->
        <div class="ltw-tool-container button-group row-display">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            :id="item.buttonIconCode"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <!-- <el-upload accept=".json,.zip" :action= uploadUrl :before-upload="beforeUpload" :on-error="handleError" :http-request="handleUploadForm">
            <el-button type="primary">点击上传</el-button>
          </el-upload> -->
          <!--          <upload-file-->
          <!--            :showFileList="false"-->
          <!--            checkType="parking_lot_import_file"-->
          <!--            ref="uploadImage"-->
          <!--            source-type="parking_lot_import_file"-->
          <!--            :limit="1"-->
          <!--            listType="text"-->
          <!--            accept=".json,.zip"-->
          <!--            id="file"-->
          <!--          />-->
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :id="item.buttonIconCode"
                  :key="item.id"
                  v-for="item in batchingFunctionList"
                  :command="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-table
        :data="pageData.records"
        stripe
        border
        @selection-change="handleSelectionChange"
        row-key="id"
        ref="tableRef"
      >
        <el-table-column
          width="150"
          header-align="left"
          align="left"
          prop="cantonName"
          :label="$t('区域')"
          show-overflow-tooltip
          fixed
        ></el-table-column>
        <el-table-column
          width="200"
          header-align="left"
          align="left"
          prop="name"
          :label="$t('名称')"
          show-overflow-tooltip
          fixed
        >
          <template #default="scope">
            <el-link @click="view(scope.row)" type="primary" :underline="false">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          width="100"
          header-align="left"
          align="left"
          prop="code"
          :label="$t('编码')"
          show-overflow-tooltip
          fixed
        ></el-table-column>
        <el-table-column
          width="200"
          header-align="left"
          align="left"
          prop="address"
          :label="$t('地址')"
          show-overflow-tooltip
          fixed
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="scale"
          :label="$t('规模')"
          show-overflow-tooltip
          fixed
        ></el-table-column>
        <!-- <el-table-column header-align="left" align="center" prop="position" :label="$t('位置')" fixed>
          <template #default="scope">
            <el-link style="font-size: 20px" :disabled="!scope.row.latitude && !scope.row.longitude"
              @click="getPosition(scope.row)" type="success" :underline="false"><ltw-icon
                icon-code="el-icon-map-location"></ltw-icon></el-link>
          </template>
        </el-table-column> -->

        <el-table-column
          header-align="left"
          align="left"
          prop="envTypeName"
          :label="$t('环境类型')"
          show-overflow-tooltip
          fixed
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="typeName" :label="$t('停车场类型')">
          <!--          <template #default="scope">-->
          <!--            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.typeNameList" :key="item">{{ item }}</el-tag>-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="civilAirDefense" :label="$t('人防的')">
          <template #default="scope">
            {{ scope.row.civilAirDefense ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="limitedHeight"
          :label="$t('限高(米)')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="floors" :label="$t('层数')"></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="roadSurfaceMaterial"
          :label="$t('地面材质')"
          width="140"
        >
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.roadSurfaceMaterialNameList" :key="item"
              >{{ item }}
            </el-tag>
            <!-- <div
              class="flex-display"
              v-if="scope.row.roadSurfaceMaterialNameList?.length >= 1"
            >
              <el-tag>{{ scope.row.roadSurfaceMaterialNameList[0] }}</el-tag>
              <el-popover :width="200" trigger="hover">
                <template #reference>
                  <el-link
                    v-if="scope.row.roadSurfaceMaterialNameList?.length > 1"
                    :underline="false"
                    >...</el-link
                  >
                </template>
                <el-tag
                  style="margin: 0 10px 10px 0"
                  v-for="item in scope.row.roadSurfaceMaterialNameList"
                  :key="item"
                  >{{ item }}</el-tag
                >
              </el-popover>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="totalParkingSpace"
          :label="$t('总车位')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="parkingVolume" :label="$t('停车数')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="hasArrow" :label="$t('走道是否有箭头标识')">
          <template #default="scope">
            {{ scope.row.hasArrow ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="hasSpeedBump" :label="$t('是否安装减速带')">
          <template #default="scope">
            {{ scope.row.hasSpeedBump ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="brightnessName" :label="$t('亮度')"></el-table-column>
        <el-table-column header-align="left" align="left" prop="hasCivilAirLogo" :label="$t('是否有人防工程标识')">
          <template #default="scope">
            {{ scope.row.hasCivilAirLogo ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="hasLiftCarport" :label="$t('是否有升降车位')">
          <template #default="scope">
            {{ scope.row.hasLiftCarport ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="hasLiftCarport" :label="$t('是否有rtk车位')">
          <template #default="scope">
            {{ scope.row.hasRtkSlot ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column header-align="left" align="left" prop="parkingSpaceType" :label="$t('停车位类型')" width="164">
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.parkingSpaceTypeNameList" :key="item"
              >{{ item }}
            </el-tag>
            <!-- <div
              class="flex-display"
              v-if="scope.row.parkingSpaceTypeNameList?.length >= 1"
            >
              <el-tag>{{ scope.row.parkingSpaceTypeNameList[0] }}</el-tag>
              <el-popover :width="200" trigger="hover">
                <template #reference>
                  <el-link
                    v-if="scope.row.parkingSpaceTypeNameList?.length > 1"
                    :underline="false"
                    >...</el-link
                  >
                </template>
                <el-tag
                  style="margin: 0 10px 0 0"
                  v-for="item in scope.row.parkingSpaceTypeNameList"
                  :key="item"
                  >{{ item }}</el-tag
                >
              </el-popover>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="parkingSpaceConfigurationNameList"
          :label="$t('停车位配置')"
        >
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.parkingSpaceConfigurationNameList" :key="item"
              >{{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="parkingSpaceLength"
          :label="$t('停车位长度(米)')"
        ></el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="parkingSpaceWidth"
          :label="$t('停车位宽度(米)')"
        ></el-table-column>
        <el-table-column header-align="left" align="left" prop="parkingLineColor" :label="$t('车位线颜色')" width="140">
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.parkingLineColorNameList" :key="item"
              >{{ item }}
            </el-tag>
            <!-- <div
              class="flex-display"
              v-if="scope.row.parkingLineColorNameList?.length >= 1"
            >
              <el-tag>{{ scope.row.parkingLineColorNameList[0] }}</el-tag>
              <el-popover :width="200" trigger="hover">
                <template #reference>
                  <el-link
                    v-if="scope.row.parkingLineColorNameList?.length > 1"
                    :underline="false"
                    >...</el-link
                  >
                </template>
                <el-tag
                  style="margin: 0 10px 0 0"
                  v-for="item in scope.row.parkingLineColorNameList"
                  :key="item"
                  >{{ item }}</el-tag
                >
              </el-popover>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="parkingLineStatusName"
          :label="$t('车位线状态')"
          width="150"
        >
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.parkingLineStatusNameList" :key="item"
              >{{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="parkingInnerColor"
          :label="$t('车位内部颜色')"
          width="200"
        >
          <template #default="scope">
            <el-tag style="margin: 0 10px 0 0" v-for="item in scope.row.parkingInnerColorNameList" :key="item"
              >{{ item }}
            </el-tag>
            <!-- <div
              class="flex-display"
              v-if="scope.row.parkingInnerColorNameList?.length >= 1"
            >
              <el-tag>{{ scope.row.parkingInnerColorNameList[0] }}</el-tag>
              <el-popover :width="200" trigger="hover">
                <template #reference>
                  <el-link
                    v-if="scope.row.parkingInnerColorNameList?.length > 1"
                    :underline="false"
                    >...</el-link
                  >
                </template>
                <el-tag
                  style="margin: 0 10px 0 0"
                  v-for="item in scope.row.parkingInnerColorNameList"
                  :key="item"
                  >{{ item }}</el-tag
                >
              </el-popover>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column
          header-align="left"
          align="left"
          prop="operation"
          :label="$t('操作')"
          fixed="right"
          min-width="210"
        >
          <template #default="scope">
            <el-button-group>
              <el-tooltip
                :key="item.id"
                v-for="item in inlineFunctionList"
                effect="dark"
                :content="$t(item.name)"
                placement="top"
                :enterable="false"
              >
                <el-button :type="item.buttonStyleType" @click="executeButtonMethod(item, scope.row)">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <filter-parking-lot @reload="reload" ref="FilterParkingLot" />
    <add-parking-lot @reload="query" ref="AddParkingLot" />
    <data-statistic-drawer ref="DataStatisticDrawer" />
    <map-search-position ref="MapSearchPosition" />
    <import-parking-lot ref="ImportParkingLot" @reload="query" />
  </div>
</template>

<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util, { showToast, showConfirmToast, downloadFile } from '@/plugins/util'
import { pageFtmParkingLotDetail, exportParkingLotDetailExcel } from '@/apis/fleet/ftm-parking-lot-detail'
import { deleteFtmParkingLot } from '@/apis/fleet/ftm-parking-lot'
import MapSearchPosition from '@/components/map/MapSearchPosition.vue'
import AddParkingLot from '@/pages/fleet/dialog/AddParkingLot.vue'
import FilterParkingLot from '@/pages/fleet/dialog/FilterParkingLot.vue'
import DataStatisticDrawer from '@/pages/fleet/dialog/DataStatisticDrawer.vue'
import ImportParkingLot from '@/pages/fleet/dialog/ImportParkingLot.vue'

const defaultFormData = {}
export default {
  components: {
    AddParkingLot,
    MapSearchPosition,
    DataStatisticDrawer,
    FilterParkingLot,
    ImportParkingLot
  },
  name: 'FtmParkingLotDetail',
  data() {
    return {
      uploadUrl:
        GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/importSlotEntrance?token=' + util.getToken(),
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  created() {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
        this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    const isCollapseMenu = this.$route?.query?.isCollapseMenu,
        showBackButton = this.$route?.query?.showBackButton
    if (isCollapseMenu === '1') {
      this.$store.commit('setCollapsed', isCollapseMenu === '1' ? true : false)
    }
    if (showBackButton === '1') {
      this.$store.commit('setBackButton', showBackButton === '1' ? true : false)
    }
    this.query()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    handleError(err, file, fileList) {
      showToast(err, 'error')
    },
    beforeUpload(file) {
      // Define the allowed file formats (extensions)
      const allowedFormats = ['json', 'zip']

      // Get the file's extension
      const fileExtension = file.name.split('.').pop().toLowerCase()

      // Check if the file format is allowed
      if (!allowedFormats.includes(fileExtension)) {
        showToast('只能上传 JSON 或 ZIP 格式的文件！', 'warning')
        return false // Prevent file from uploading
      }

      // Continue with the upload
      return true
    },
    executeButtonMethod(button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query() {
      pageFtmParkingLotDetail(this.queryParam).then(res => {
        this.pageData = res.data
      })
    },
    add() {
      this.$refs.AddParkingLot.show({ type: 'add' })
    },
    edit(row) {
      this.$refs.AddParkingLot.show({
        type: 'edit',
        parkingLotId: row.parkingLotId
      })
    },
    export() {
      let postData = {
        ...this.queryParam
      }
      delete postData.current
      delete postData.size
      exportParkingLotDetailExcel(postData).then(res => {
        // downloadTxt('停车场.csv', res)
        downloadFile(res, '停车场列表')
        // // 创建一个下载链接
        // var url = window.URL.createObjectURL(res);
        //
        // // 创建一个 <a> 元素，设置下载链接和文件名
        // var a = document.createElement('a');
        // a.href = url;
        // a.download = 'your_excel_file.csv';
        //
        // // 将 <a> 元素添加到 DOM 中
        // document.body.appendChild(a);
        //
        // // 模拟点击下载链接
        // a.click();
        //
        // // 从 DOM 中移除 <a> 元素
        // document.body.removeChild(a);
        //
        // // 释放 Blob 对象的 URL
        // window.URL.revokeObjectURL(url);

        // console.log(res)
        // const url = window.URL.createObjectURL(new Blob([res]))
        // let link = document.createElement('a')
        // link.style.display = 'none'
        // link.href = url
        // link.setAttribute('download', '学生信息列表.xlsx')
        // document.body.appendChild(link)
        // link.click()
        // URL.revokeObjectURL(link.href) // 释放URL 对象
        // document.body.removeChild(link)
        // link = null
      })
    },
    dataStatistic(row) {
      this.$refs.DataStatisticDrawer.show({
        id: row.parkingLotId
      })
    },
    view(row) {
      this.$refs.AddParkingLot.show({
        type: 'view',
        parkingLotId: row.parkingLotId
      })
    },
    copy(row) {
      this.$refs.AddParkingLot.show({
        type: 'copy',
        parkingLotId: row.parkingLotId
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.parkingLotId })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmParkingLot(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    handleSelectionChange(value) {
      this.selectedData = value
    },
    initForm() {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    },
    getPosition(row) {
      this.$refs.MapSearchPosition.show({
        lng: row.longitude,
        lat: row.latitude,
        name: row.name,
        address: row.address,
        formReadonly: true
      })
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      switch (columnIndex) {
        case 0:
          return this.mergeCol('name', rowIndex)
        case 1:
          return this.mergeCol('name', rowIndex)
        case 2:
          return this.mergeCol('name', rowIndex)
        case 3:
          return this.mergeCol('name', rowIndex)
        case 23:
          return this.mergeCol('name', rowIndex)
      }
    },
    mergeCol(id, rowIndex) {
      var idName = this.pageData.records[rowIndex][id]
      if (rowIndex === 0 || this.pageData.records[rowIndex][id] != this.pageData.records[rowIndex - 1][id]) {
        var i = rowIndex
        var num = 0
        while (i < this.pageData.records.length) {
          if (this.pageData.records[i][id] === idName) {
            i++
            num++
          } else {
            i = this.pageData.records.length
          }
        }
        return {
          rowspan: num,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 1
        }
      }
    },
    filter() {
      this.$refs.FilterParkingLot.show()
    },
    reload(val) {
      if (val) {
        this.queryParam = { ...this.queryParam, ...JSON.parse(JSON.stringify(val)) }
      }
      this.query()
    },
    importData() {
      this.$refs.ImportParkingLot.show({ type: 'view' })
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped lang="scss">
// .ltw-toolbar {
//   justify-content: space-between;
// }
// .el-tag {
//   margin: 0 10px 0 0;
// }
.filter-btn {
  margin-right: 10px;
}

.flex-display {
  display: flex;
  justify-content: center;
}

.row-display {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
</style>
