<template>
  <el-row class="row-container">
    <el-col :span="12" class="form-item">
      <span class="title" v-text="$t('保单编号')"></span>
      <el-tag><span v-text="item.policyCode"></span></el-tag>
    </el-col>
    <el-col :span="12" class="file-row">
      <span class="form-item title" v-text="$t('保单文件')"></span>
      <template v-if="item.fileId">
        <upload-file
            :disabled="true"
            ref="uploadImage"
            source-type="calibration_parameter_file"
            :limit="1"
            listType="text"
            :source-id="item.id"
            v-model="item.fileId"
        />
      </template>
      <template v-else>
        <el-tag type="warning"><span v-text="'暂无保单'"></span></el-tag>
      </template>
    </el-col>
    <el-col :span="12">
      <span class="title" v-text="$t('生效日期')"></span>
      <el-tag><span v-text="item.startDate || '-'"></span></el-tag>
    </el-col>
    <el-col :span="12">
      <span class="title" v-text="$t('失效日期')"></span>
      <el-tag><span v-text="item.endDate || '-'"></span></el-tag>
    </el-col>
    <el-col :span="12">
      <span class="title" v-text="$t('备注')"></span>
      <el-tag type="warning"><span v-text="item.description || '-'"></span></el-tag>
    </el-col>
  </el-row>
</template>

<script>
import {i18n} from '@/plugins/lang'
import LtwInput from '@/components/base/LtwInput'
import UploadFile from '@/components/system/UploadFile.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import {showConfirmToast, showToast} from '@/plugins/util.js'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  deleteFtmCalibrationRecords
} from '@/apis/fleet/ftm-calibration-records'
import {
  ElForm,
  ElFormItem,
  ElButton,
  ElRow,
  ElCol,
  ElInputNumber,
  ElDatePicker,
  ElOption,
  ElSelect
} from 'element-plus'

export default {
  name: 'InsuranceForm',
  data() {
    return {
      $t: i18n.global.t,
      form: {},
      backupForm: {}
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    }
  },
  watch: {
    item: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
      },
      deep: true,
      immediate: true
    }
  },
  components: {
    ElForm,
    ElFormItem,
    ElButton,
    ElRow,
    ElCol,
    ElInputNumber,
    ElDatePicker,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    UploadFile
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.row-container {
  .el-col {
    margin-bottom: 5px;
    .title{
      width: 70px;
      display: inline-block;
    }
  }
}

.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.form-item {
  line-height: 32px;
  // color: #606266;
}

.file-row {
  display: flex;

  .form-item {
    white-space: nowrap;
  }
}

</style>
