<template>
  <el-dialog :title="title" :model-value="visible" width="40%" @close="dialogClosed" @open="dialogOpened">
    <el-form :model="form" :rules="formRules" ref="shareFormRef" class="ltw-middle-content" label-width="150px">
      <!-- 分享租户 -->
      <el-form-item v-if="isTagShare" :label="$t('租户')" prop="targetTenant">
        <el-select v-model="form.targetTenant" :placeholder="$t('请选择租户')" clearable filterable>
          <el-option v-for="item in tenantList" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :label="$t('是否保留分组')" prop="shareIsKeep" v-if="type=='tag'">
        <el-radio-group v-model="form.shareIsKeep">
          <el-radio :label="true">{{ $t('是') }}</el-radio>
          <el-radio :label="false">{{ $t('否') }}</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item :label="isTagShare ? $t('是否保留分组') : $t('是否保留数据集')" prop="shareIsKeep">
        <el-radio-group v-model="form.shareIsKeep">
          <el-radio :label="true">{{ $t('是') }}</el-radio>
          <el-radio :label="false">{{ $t('否') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('名称')" prop="name" v-if="rename">
        <ltw-input v-model="form.name"></ltw-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogClosed">{{ $t('取消') }}</el-button>
      <el-button type="primary" @click="confirm">{{ $t('分享') }}</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { getLoginUserList } from '@/apis/base/index'
import { showToast } from '@/plugins/util'
import { shareDaqTaskFunction } from '@/apis/fleet/daq-task-function'

const DEFAULT_FORM_DATA = {}

export default {
  components: {},
  name: 'ShareDialog',
  emits: ['update:modelValue', 'shareDialogClose', 'shareConfirm'],
  props: {
    rename: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object,
      default: {
        updatable: false
      }
    },
    code: {
      type: String
    }
  },
  data() {
    return {
      form: Object.assign({}, DEFAULT_FORM_DATA),
      formRules: {
        targetTenant: [
          {
            required: true,
            message: this.$t('请选择租户'),
            trigger: 'change'
          }
        ],
        access_role_id: [
          {
            required: true,
            message: this.$t('请选择可访问级别'),
            trigger: 'change'
          }
        ],
        shareIsKeep: [
          {
            required: true,
            message: this.$t('请确认是否保留'),
            trigger: 'change'
          }
        ],
        updatable: [
          {
            required: true,
            message: this.$t('请确认数据集是否可更新'),
            trigger: 'change'
          }
        ]
      },
      accessRoleOptions: [],
      accessRoleTypeProps: {
        expandTrigger: 'hover',
        value: 'id',
        label: 'name',
        children: 'roleList',
        checkStrictly: true,
        emitPath: false
      },
      tenantList: [], // 租户列表
      visible: false,
      title: '',
      type: ''
    }
  },
  computed: {
    // 是否为tag分享
    isTagShare() {
      return this.type === 'tag'
    }
  },
  methods: {
    show(data) {
      this.form = data.form
      this.title = data.title
      this.type = data.type
      this.visible = true
    },
    dialogOpened() {
      this.getTenantList()
    },
    // 获取租户列表
    async getTenantList() {
      const currentTenantCode = this.$store.state.permission.currentUser?.currentTenantCode
      const res = await getLoginUserList()
      this.tenantList = res.data != null ? res.data.filter(item => item.code != currentTenantCode) : []

      this.form.targetTenant = this.tenantList.length > 0 ? this.tenantList[0].code : null
    },
    initForm() {
      this.$refs.shareFormRef.resetFields()
      this.form = Object.assign({}, DEFAULT_FORM_DATA)
    },
    dialogClosed() {
      this.visible = false
      this.initForm()
    },
    handleChange() {
      this.form.access_role_type = this.$refs.cascaderRef.getCheckedNodes()[0].data.type
    },
    confirm() {
      this.$refs.shareFormRef.validate(valid => {
        if (!valid) return
        if (this.type === 'dataset') {
          shareDmDataset(this.form).then(() => {
            this.$emit('shareConfirm', this.form)
            showToast(this.$t('分享数据集成功', 'success'))
            this.visible = false
          })
        } else {
          shareDaqTaskFunction(this.form).then(() => {
            this.$emit('share-confirm')
            this.visible = false
          })
        }

        // this.visible = false
        // this.$emit('shareConfirm', this.form)
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
