<template>
  <div class="app-management">
    <el-scrollbar>
      <div class="app-list">
        <template v-for="item in appList" :key="item.id">
          <div class="app-item" @click="handleApp(item.name)">
            <el-card
              :class="['app-item-card', activeApp === item.name && 'active']"
              :shadow="activeApp === item.name ? 'always' : 'never'"
            >
              <el-image :src="item.url || require('@/assets/images/AppManagement/app.png')"></el-image>
              <div class="app-name">{{ item.name }}</div>
              <div class="card-info-item">
                <div class="item-label">最新版本：</div>
                <div class="item-value">{{ item.version }}</div>
              </div>
              <div class="card-info-item">
                <div class="item-label">上传时间：</div>
                <div class="item-value">{{ item.time || '-' }}</div>
              </div>
            </el-card>
          </div>
        </template>
      </div>
    </el-scrollbar>
    <el-divider />
    <div class="app-content">
      <el-button type="primary" @click="add">
        <ltw-icon icon-code="el-icon-plus"></ltw-icon>
        {{ $t('新增') }}
      </el-button>
      <el-table :data="pageData.records" style="width: 100%" border>
        <el-table-column label="应用名称" prop="name" show-tooltip-when-overflow></el-table-column>
        <el-table-column label="版本" prop="version" show-tooltip-when-overflow></el-table-column>
        <el-table-column label="文件大小" prop="size" show-tooltip-when-overflow>
          <template #default="scope">
            <el-tag>{{ checkFileSize(scope.row.size).data + checkFileSize(scope.row.size).unit }}</el-tag>
          </template>
        </el-table-column>
        <!--        <el-table-column label="MD5" prop="md5" show-tooltip-when-overflow></el-table-column>-->
        <el-table-column label="url" prop="url" show-tooltip-when-overflow></el-table-column>
        <el-table-column label="上传时间" prop="createTime" show-tooltip-when-overflow></el-table-column>
        <el-table-column label="备注" prop="remark" show-tooltip-when-overflow></el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button-group>
              <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                <el-button type="danger" size="small" @click="deleteRow(scope.row)">
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" :content="$t('下载')" placement="top" :enterable="false">
                <el-button type="primary" size="small" plain @click="downloadFile(scope.row)" class="download-btn">
                  <ltw-icon icon-code="svg-download"></ltw-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </div>
  </div>
  <!--  <div class="img-list">-->
  <!--    <div class="img-item" v-for="(item, index) in new Array(200)">-->
  <!--      <img-->
  <!--        class="lazy-image"-->
  <!--        src="https://via.placeholder.com/200"-->
  <!--        alt="image"-->
  <!--        :data-src="`https://picsum.photos/200/${180 + index}`"-->
  <!--      />-->
  <!--    </div>-->
  <!--  </div>-->
  <AddApp ref="AddApp" @reload="query" />
</template>
<script>
import GLB_CONFIG from '@/plugins/glb-constant'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import util, { showConfirmToast } from '@/plugins/util'
import { listLatestFtmApp, pageFleetApps, deleteFtmApps } from '@/apis/fleet/app-management'
import AddApp from '@/pages/dataCollect/dialog/AddApp'
import { pageBsVehicle } from '@/apis/fleet/bs-vehicle'

const defaultForm = {}

export default {
  data() {
    return {
      activeApp: '',
      appList: [],
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10
      }
    }
  },
  components: {
    AddApp
  },
  created() {
    this.query()
    // this.pageFleetApps()
    // 使用
    // let curryingAdd = this.currying(function (a, b, c) {
    //   return a + b + c
    // })
    // console.log(curryingAdd(1, 2)(3)) // 6
    // console.log(curryingAdd(1)(2)(3)) // 6
    // console.log(curryingAdd(1, 2, 3)) // 6
  },
  mounted() {
    // const intersectionObserver = new IntersectionObserver((entries, observer) => {
    //   entries.forEach(val => {
    //     if (val.isIntersecting) {
    //       const img = val.target
    //       const src = img.getAttribute('data-src')
    //       img.setAttribute('src', src)
    //       img.onload = () => {
    //         img.setAttribute('class', 'fade-in')
    //       }
    //       observer.unobserve(img)
    //     }
    //   })
    // })
    // const lazyImages = document.querySelectorAll('.lazy-image')
    //
    // lazyImages.forEach(val => {
    //   intersectionObserver.observe(val)
    // })
  },
  methods: {
    query() {
      listLatestFtmApp().then(res => {
        this.appList = res.data
        if (res.data?.length) {
          this.activeApp = this.activeApp || res.data[0].name
          this.pageFleetApps()
        }
      })
    },
    pageFleetApps() {
      pageFleetApps({ name: this.activeApp }).then(res => {
        this.pageData = res.data
      })
    },
    handleApp(val) {
      this.activeApp = val
      this.pageFleetApps()
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    deleteRow(row) {
      this.remove({ id: row.id })
    },
    downloadFile(row) {
      const url = this.downloadUrl + row.fileId + '?token=' + util.getToken()
      window.open(url)
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmApps(param).then(() => {
          this.query()
        })
      })
    },
    add() {
      const item = this.appList.find(val => val.name === this.activeApp)
      this.$refs.AddApp.show({ type: 'add', data: { name: item.name, variant: item.variant } })
    },
    checkFileSize(data) {
      if (data > 0 && data < Math.pow(2, 10)) {
        return { data, unit: 'B' }
      } else if (data >= Math.pow(2, 10) && data < Math.pow(2, 20)) {
        return { data: parseFloat((data / 1024).toFixed(2)), unit: 'KB' }
      } else if (data >= Math.pow(2, 20) && data < Math.pow(2, 30)) {
        return { data: parseFloat((data / 1024 / 1024).toFixed(2)), unit: 'M' }
      } else if (data >= Math.pow(2, 30) && data < Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'G'
        }
      } else if (data >= Math.pow(2, 40)) {
        return {
          data: parseFloat((data / 1024 / 1024 / 1024 / 1024).toFixed(2)),
          unit: 'T'
        }
      } else {
        return { data: 0, unit: '' }
      }
    }
    // handleDownload() {
    //   let url = ''
    //   if (this.appName === 'arena_monitor') {
    //     this.$refs.formRef.validate(valid => {
    //       if (!valid) return
    //       // this.showMask = true
    //       let len = 6 //任务数
    //       let num = 0
    //       let i = len - 1 //因为满足条件之后num++还会执行一次，percentage的值会超过100，所以这里减1处理下
    //       this.timer = setInterval(() => {
    //         if (num === i) {
    //           clearInterval(this.timer)
    //           this.timer = null
    //         }
    //         num++
    //         this.percentage = parseInt((num / len) * 100 - 1)
    //       }, 500)
    //
    //       url =
    //         GLB_CONFIG.devUrl.serviceSiteRootUrl +
    //         '/arena-fleet/app/initialization?appName=ARENA_MONITOR' +
    //         '&vin=' +
    //         this.param.vin +
    //         '&variant=' +
    //         this.param.variant +
    //         '&diskPath=' +
    //         this.param.diskPath +
    //         '&dataInterVal=' +
    //         this.param.dataInterVal
    //
    //       // FileSaver.saveAs(url)
    //       this.percentage = 100
    //       this.showMask = false
    //     })
    //   } else {
    //     url = GLB_CONFIG.devUrl.serviceSiteRootUrl + '/arena-fleet/app/initialization?appName=PMT_TAG'
    //     // FileSaver.saveAs(url)
    //     this.percentage = 100
    //     this.showMask = false
    //   }
    // },
    // 支持多参数传递，原理是：使用闭包保存历史参数，使用递归解决多参数问题
    // currying(fun, initArgs) {
    //   let _this = this
    //   let len = fun.length // 被改造函数参数的个数
    //   let args = initArgs || []
    //
    //   return function () {
    //     let _args = [...args, ...arguments] // 参数
    //
    //     // 如果参数个数小于最初的fun.length，则递归调用，继续收集参数
    //     if (_args.length < len) {
    //       return _this.currying.call(_this, fun, _args)
    //     }
    //
    //     // 参数收集完毕，则执行函数，返回结果
    //     return fun.apply(this, _args)
    //   }
    // }
  }
}
</script>
<style lang="scss" scoped>
.app-management {
  //background: #000;

  .app-list {
    display: flex;
    @keyframes rotate {
      100% {
        transform: rotate(1turn);
      }
    }

    .app-item {
      position: relative;
      margin: 20px;
      cursor: pointer;

      .app-item-card {
        &:hover{
          .el-image {
            filter: grayscale(0);
          }
        }
        &.active {
          .el-image {
            filter: grayscale(0);
          }
        }

        .el-image {
          transition: all .3s;
          width: 200px;
          filter: grayscale(1);
        }

        .app-name {
          margin-bottom: 10px;
          font-weight: 600;
          text-align: center;
        }

        .card-info-item {
          display: flex;
          font-size: 14px;

          .item-label {
            font-weight: 600;
            color: #303133;
            margin-right: 2px;
          }

          .item-value {
            color: #606266;
          }
        }
      }
    }
  }

  .app-content {
  }
}

//.fade-in {
//  animation: fadeIn 0.6s ease-in;
//}
//
//@keyframes fadeIn {
//  from {
//    opacity: 0;
//  }
//  to {
//    opacity: 1;
//  }
//}
</style>
