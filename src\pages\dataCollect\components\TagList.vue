<template>
  <div>
    <div v-show="tagList?.length > 0" style="margin-bottom: 20px">
      <el-card
        v-for="item in tagList"
        :key="item"
        shadow="always"
        class="bs-tag-group-card"
        style="margin-bottom: 10px"
      >
        <template #header>
          <div class="bs-tag-group-card-header">
            <span>{{ item.name }}</span>
          </div>
        </template>
        <div class="bs-tag-group-card-body">
          <template v-if="item?.children?.length">
            <tag-list
              :tagList="item.children"
              :checkedTagList="checkedTagList"
              :closeable="closeable"
              @close="handleClose"
            ></tag-list>
          </template>
          <template v-else>
            <template v-for="(tag, tagIndex) in item.tagList" :key="tag">
              <el-tag
                class="tag-item"
                @click="chooseTag(tag)"
                :closable="closeable"
                :type="checkTagType(tag)"
                :effect="isChecked(tag) ? 'dark' : 'light'"
                @close="handleClose(tagIndex, item.tagList)"
              >
                {{ tag.name }}
                <ltw-icon class="tag-attribute" icon-code="el-icon-microphone" v-if="tag.supportVoice"></ltw-icon>
                <ltw-icon class="tag-attribute" icon-code="el-icon-pointer" v-if="tag.supportTrigger"></ltw-icon>
                <div
                  class="tag-attribute"
                  v-if="tag.type === 'transient' && (tag.previousDuration === 0 || tag.previousDuration)"
                >
                  {{ tag.previousDuration }}
                  <span class="tag-unit">秒</span>
                </div>
                <div
                  v-if="
                    tag.type === 'transient' &&
                    (tag.previousDuration === 0 ||
                      tag.previousDuration ||
                      tag.followingDuration === 0 ||
                      tag.followingDuration)
                  "
                >
                  ~
                </div>
                <div
                  class="tag-attribute"
                  v-if="tag.type === 'transient' && (tag.followingDuration === 0 || tag.followingDuration)"
                >
                  {{ tag.followingDuration }}
                  <span class="tag-unit">秒</span>
                </div>
              </el-tag>
            </template>
            <!--            </el-card>-->
            <!--        </template>-->
          </template>
        </div>
      </el-card>
    </div>
    <el-empty v-show="tagList?.length == 0" description="暂无选择标签"></el-empty>
  </div>
</template>

<script>
import LtwIcon from '@/components/base/LtwIcon.vue'
import TagList from '@/pages/dataCollect/components/TagList.vue'

export default {
  components: {
    LtwIcon,
    TagList
  },
  props: {
    tagList: {
      type: Array,
      default: []
    },
    checkedTagList: {
      type: Array,
      default: []
    },
    closeable: {
      type: Boolean,
      default: true
    },
    choosenable: {
      type: Boolean,
      default: false
    }
  },
  // computed: {
  //   checkedTagList() {
  //     return this.propsCheckedTagList
  //   }
  // },
  methods: {
    checkTagType(tag) {
      if (tag.type === 'continuous') {
        return tag.mutuallyExclusive ? 'danger' : 'warning'
      }
      if (tag.type === 'transient') {
        return tag.mutuallyExclusive ? 'success' : ''
      }
      return 'info'
    },
    handleClose(index, tagList) {
      this.$emit('close', index, tagList)
    },
    chooseTag(row) {
      if (!this.choosenable) return
      const index = this.checkedTagList.findIndex(val => val.id === row.id)
      if (!~index) {
        this.checkedTagList.push(row)
      } else {
        this.checkedTagList.splice(index, 1)
      }
    },
    isChecked(item) {
      if (this.checkedTagList?.length) {
        return ~this.checkedTagList.findIndex(val => val.code === item.code)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tag {
  margin: 0 5px 5px 0;
  cursor: pointer;
}

:deep(.el-card__header, .el-card__body) {
  padding: 10px;
}

.tag-item {
  margin: 0 5px 5px 0;

  :deep(.el-tag__content) {
    display: flex;
    align-items: center;

    .tag-attribute {
      margin-left: 4px;

      &.ltw-icon .el-icon {
        font-size: 12px;
      }
    }
  }
}
</style>
