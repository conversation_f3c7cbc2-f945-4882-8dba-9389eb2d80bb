<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
  >
    <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('有效时间')" prop="activationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.activationDate"
              :disabled-date="disabledStartDate"
              type="datetime"
              :placeholder="$t('有效时间')"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <el-tag v-else><span v-text="form.activationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('失效时间')" prop="deactivationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.deactivationDate"
              :disabled-date="disabledEndDate"
              type="datetime"
              :placeholder="$t('失效时间')"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <el-tag v-else><span v-text="form.deactivationDate || '-'"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('标定日期')" prop="calibrationDate">
            <el-date-picker
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.calibrationDate"
              type="date"
              :placeholder="$t('标定日期')"
              value-format="YYYY-MM-DD"
            />
            <el-tag v-else><span v-text="form.calibrationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('版本')" prop="version">
            <el-input-number
              v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
              v-model="form.version"
              :precision="1.0"
              :step="0.1"
            />
            <el-tag v-else><span v-text="form.version"></span></el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="12">
        <el-form-item
          :label="$t('是否有效')"
          id="enable"
          prop="enable"
        >
          <el-switch
            v-if="dialogStatus === 'edit' || dialogStatus === 'view'"
            v-model="form.enable"
            :disabled="formReadonly"
            inline-prompt
            :active-text="$t('是')"
            :inactive-text="$t('否')"
            id="enable"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
          <el-tag v-else><span v-text="form.enable"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="20">
        <el-form-item
            :label="$t('描述')"
            prop="description"
            :rules="[
            {
              required: form.dialogStatus === 'edit',
              message: $t('请输入'),
              trigger: 'change'
            }
          ]"
        >
          <ltw-input
              v-if="form.edit"
              textType="remark"
              type="textarea"
              v-model="form.description"
              :disabled="formReadonly"
          />
          <el-tag v-else><span v-text="form.description"></span></el-tag>
        </el-form-item>
      </el-col>
      <el-form-item :label="$t('文件')" :prop="'fileId'" v-if="dialogStatus === 'add' || dialogStatus === 'edit'">
        <upload-file
          :disabled="dialogStatus !== 'add'"
          ref="uploadImage"
          source-type="calibration_parameter_file"
          :limit="1"
          listType="text"
          :source-id="form.id"
          v-model="form.fileId"
        />
      </el-form-item>
    </el-form>
    <div class="sensor-files" v-if="dialogStatus === 'view'">
      <el-divider content-position="left">传感器文件</el-divider>
      <div class="sensor-content">
        <el-button type="primary" plain @click="downloadFiles()" class="download-btn">
          <ltw-icon icon-code="svg-download"></ltw-icon>
          {{ $t('下载') }}
        </el-button>
        <el-tabs
          :style="{ left: elTabHeaderWidth + 'px' }"
          v-model="fileType"
          class="file-type-tabs"
          @tab-change="changeFileType"
        >
          <el-tab-pane label="JSON" name="JSON"></el-tab-pane>
          <el-tab-pane label="YAML" name="YAML"></el-tab-pane>
        </el-tabs>
        <el-tabs tab-position="left" v-model="sensorModel" class="sensor-list-tabs" @tab-change="changeSubTab">
          <template v-for="(item, index) in tabList[fileType]" :key="item.modalityCode">
            <el-tab-pane lazy :label="item.modalityCode" :name="item.modalityCode">
              <!--              v-if="sensorModel === item.modalityCode"-->
              <div class="border-card-content">
                <div class="text">
                  <json-editor-vue
                    v-if="fileType === 'JSON'"
                    style="margin-top: 10px"
                    :currentMode="jsonMode"
                    :language="jsonLang"
                    v-model="item.content"
                    :modeList="modeList"
                  ></json-editor-vue>
                  <!--                  <Codemirror-->
                  <!--                    v-if="fileType === 'YAML'"-->
                  <!--                    v-model:value="item.content"-->
                  <!--                    border-->
                  <!--                    :options="cmOptions"-->
                  <!--                  ></Codemirror>-->
                  <el-scrollbar v-else>
                    <div v-for="(val, key) in item.content" :key="key">
                      <div class="divider-style">
                        <el-divider>
                          <el-icon>
                            <star-filled />
                          </el-icon>
                          {{ key }}
                        </el-divider>
                      </div>
                      <Codemirror
                        v-if="fileType === 'YAML'"
                        v-model:value="item.content[key]"
                        border
                        :options="cmOptions"
                      ></Codemirror>
                      <!--                      <div v-html="trans(value)" style="margin-left: 20px"></div>-->
                    </div>
                  </el-scrollbar>
                </div>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="close">{{ $t('关闭') }}</el-button>
        <template v-else>
          <el-button @click="cancel" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="confirm">{{ $t('确认') }}</el-button>
        </template>
      </span>
<!--      <el-button @click="cancel">{{ $t('取消') }}</el-button>-->
<!--      <el-button v-if="dialogStatus === 'add' || dialogStatus === 'edit'" id="submit" type="primary" @click="submit"-->
<!--        >{{ $t('保存') }}-->
<!--      </el-button>-->
    </template>
  </el-dialog>
</template>

<script>
import util, { showToast, downloadTxt, getLocale, dateUtils } from '@/plugins/util'
// import { Codemirror } from 'vue-codemirror'
import Codemirror from 'codemirror-editor-vue3'
// language
// import 'codemirror/mode/yaml-frontmatter/yaml-frontmatter.js'
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import GLB_CONFIG from '@/plugins/glb-constant'
import JsonEditorVue from 'json-editor-vue3'
import { i18n } from '@/plugins/lang'
import UploadFile from '@/components/system/UploadFile.vue'
import JsonViewer from 'vue-json-viewer'
import {
  ElDialog,
  ElTag,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElSelect
} from 'element-plus'
import {
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  getFtmCalibrationRecords,
  getFtmCalibrationResults
} from '@/apis/fleet/ftm-calibration-records'

const defaultFormData = { enable: true }
export default {
  name: 'checkPwd',
  emits: ['reload', 'cancel'],
  data() {
    return {
      activeName: 'JsonFile',
      objectValue: {},
      contentValue: '',
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      form: Object.assign({}, defaultFormData),
      formRules: {},
      fileType: 'JSON',
      sensorModel: '',
      sensorList: [],
      jsonData: [],
      jsonLang: getLocale() === 'en' ? 'en' : 'zh-CN',
      jsonMode: 'view',
      modeList: ['view'],
      // modeList: ['tree', 'code', 'form', 'text', 'view']
      cmOptions: {
        // mode: 'text/yaml', // Language mode text/yaml、text/javascript、application/json
        // theme: 'dracula', // Theme
        // // readOnly: 'nocursor'
        // indentUnit: 4, // 缩进多少个空格
        // tabSize: 4, // 制表符宽度
        // lineNumbers: true, // 是否显示行号
        // lineWrapping: true, // 是否默认换行
        // // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
        // readOnly: true, // 禁止用户编辑编辑器内容
        // // line: true,
        // smartIndent: true // 智能缩进
      },
      elTabHeaderWidth: 0,
      tabList: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    ElDialog,
    ElTag,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElSelect,
    UploadFile,
    JsonEditorVue,
    Codemirror,
    JsonViewer
  },
  created() {},
  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      let formRules = {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        calibrationDate: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        toolVersion: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        fileId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      }
      debugger
      switch (row.type) {
        case 'add':
          this.formRules = formRules
          // this.form.vehicleId = row.vehicleId
          this.form.vin = row.vin
          this.dialogTitle = this.$t('新增') + ' ' + this.$t('标定参数')
          break
        case 'edit':
          this.formRules = formRules
          this.dialogTitle = this.$t('编辑') + ' ' + this.$t('标定参数')
          this.form = JSON.parse(JSON.stringify(row.data))
          break
        case 'view':
          this.formRules = []
          this.dialogTitle = this.$t('标定参数') + ' ' + this.$t('详情')
          this.form = JSON.parse(JSON.stringify(row.data))
          // this.removeArr()
          // this.getJsonData()
          this.getFtmCalibrationResults()
          break
      }

      // this.variant = row.variant
      // this.calibrationParamsQuery()
      // this.listFtmCalibrationVariantMappingModalityVO({ variant: row.variant })
      // this.listSysDictionary('calibration_parameter_type')
    },
    getFtmCalibrationResults() {
      getFtmCalibrationResults({ recordId: this.form.id }).then(res => {
        res.data.forEach(val => {
          val.content = JSON.parse(val.content || '{}')
        })
        let tabList = {
          JSON: [
            {
              modalityCode: '整车',
              content: JSON.parse(this.form.content || '{}')
            }
          ],
          YAML: res.data
        }
        this.tabList = tabList
        this.changeFileType('JSON')
        setTimeout(() => {
          let elTabHeaderDom = document.querySelector('.sensor-list-tabs .el-tabs__header.is-left')
          this.elTabHeaderWidth = elTabHeaderDom.clientWidth
        })
      })
    },
    getFtmCalibrationRecords(row) {
      getFtmCalibrationRecords(row.id).then(res => {
        // res.data.fileId = [res.data.fileId]
        this.form = res.data
      })
    },
    dialogClosed() {
      this.initForm()
    },
    submitCancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    dialogOpened() {},
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          fileId: this.form.fileId?.length && this.form.fileId[0]
        }
        if(this.form.deactivationDate && this.form.activationDate){
          if(new Date(this.form.deactivationDate).getTime() < new Date(this.form.activationDate).getTime()){
            showToast('生效时间不可大于失效时间', 'warning')
            return
          }
        }
        if (this.dialogStatus === 'add') {
          saveFtmCalibrationRecords(postData).then(() => {
            // this.cancelCalibration()
            this.submitCancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmCalibrationRecords(postData).then(() => {
            // this.cancelCalibration()
            this.submitCancel()
          })
        }
      })
    },
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    },
    downloadJson() {
      showToast(this.$t('开始下载'), 'success')
      downloadTxt('vin_data.json', JSON.stringify(this.jsonData))
      this.jsonDialogVisible = false
    },
    downloadFiles() {
      let item = this.tabList[this.fileType].find(val => val.modalityCode === this.sensorModel)
      if (this.fileType === 'JSON') {
        downloadTxt('JSON', JSON.stringify(item.content))
      } else {
        downloadTxt(item.modalityCode, JSON.stringify(item.content))
      }
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return new Date(val) < new Date(dateUtils.parseTime(this.form.activationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    disabledStartDate(val) {
      if (this.form.deactivationDate) {
        return new Date(val) > new Date(dateUtils.parseTime(this.form.deactivationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
      }
    },
    trans(value) {
      // alert(value);
      return value.replace(/\\n/g, '\n')
    },
    // sensorChange() {
    //   this.fileType = 'JSON'
    // },
    getJsonData() {
      if (this.form.content !== undefined) {
        this.jsonData.push(JSON.parse(this.form.content))
      }
    },
    removeArr() {
      this.jsonData.splice(0, this.jsonData.length)
    },
    changeFileType(e) {
      if (this.tabList[e]?.length) {
        this.sensorModel = this.tabList[e][0].modalityCode
        if (e === 'YAML') {
          setTimeout(() => {
            this.cmOptions = {
              mode: 'application/json', // Language mode text/yaml、text/javascript
              // mode: 'text/yaml', // Language mode text/yaml、text/javascript、application/json
              // mode: 'text/javascript', // Language mode text/yaml、text/javascript、application/json
              theme: 'dracula', // Theme
              // readOnly: 'nocursor'
              indentUnit: 4, // 缩进多少个空格
              tabSize: 4, // 制表符宽度
              lineNumbers: true, // 是否显示行号
              lineWrapping: true, // 是否默认换行
              // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
              readOnly: true, // 禁止用户编辑编辑器内容
              // viewportMargin: Infinity, // 高度自适应
              // line: true,
              smartIndent: true // 智能缩进
            }
          })
        }
      }
    },
    changeSubTab(e) {}
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;

  .user-name {
    color: #409eff;
  }
}

:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.add-form {
  padding: 10px 0;

  .form-footer {
    text-align: right;
  }
}

.sensor-files {
  .json-content {
    position: relative;
    margin-top: 36px;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
    }
  }

  .sensor-content {
    position: relative;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    .file-type-tabs {
      // position: absolute;
      // top: -10px;
      position: absolute;
      top: -40px;

      :deep(.el-tabs__header) {
        margin: 0;
      }
    }

    .sensor-list-tabs {
      display: flex;
      margin-top: 50px;
      // overflow: visible;
      :deep(.el-tabs__header) {
        margin-right: 0;
        border-right: none;
      }

      :deep(.el-tabs__content) {
        // overflow: visible;
        flex-grow: 1;
        padding: 0;
        .divider-style{
          padding: 0 10px;
        }

        .el-tab-pane,
        .el-tab-pane > .border-card-content,
        .el-tab-pane > .border-card-content > .text,
        .el-tab-pane > .border-card-content > .text > .container {
          height: 100%;
        }

        .el-tab-pane > .border-card-content > .text {
          width: 100%;
        }

        .el-tab-pane > .border-card-content > .text > .container .jsoneditor {
          border: thin solid #dcdfe6;
        }

        .el-tab-pane > .border-card-content > .text > .container {
          margin: 0 !important;

          .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
            height: 100%;
            margin: 0;
            padding: 0;
          }

          .jsoneditor-menu,
          .jsoneditor-navigation-bar {
            display: none;
          }
        }
      }

      .border-card-content {
        position: relative;

        :deep(.jsoneditor-poweredBy) {
          display: none;
        }
      }
    }
  }
}
</style>
