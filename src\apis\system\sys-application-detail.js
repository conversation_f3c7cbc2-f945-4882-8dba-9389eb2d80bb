import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysApplicationDetail = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details', data, params})
export const updateSysApplicationDetail = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details', data, params})
export const deleteSysApplicationDetail = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details', params})
export const listSysApplicationDetail = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details', params})
export const listSysApplicationDetailSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details/selections', params})
export const pageSysApplicationDetail = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details/page', params})
export const getSysApplicationDetail = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_details/' + id})
