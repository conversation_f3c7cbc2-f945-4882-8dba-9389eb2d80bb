<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :destroy-on-close="true"
  >
    <el-form
      :model="form"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
      :require-asterisk-position="formReadonly"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('有效时间')" prop="activationDate">
            <el-date-picker
              v-if="!formReadonly"
              v-model="form.activationDate"
              :disabled-date="disabledStartDate"
              type="datetime"
              :placeholder="$t('有效时间')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="formReadonly"
            />
            <el-tag v-else><span v-text="form.activationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('失效时间')" prop="deactivationDate">
            <el-date-picker
              v-if="!formReadonly"
              v-model="form.deactivationDate"
              :disabled-date="disabledEndDate"
              type="datetime"
              :placeholder="$t('失效时间')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="formReadonly"
            />
            <el-tag v-else><span v-text="form.deactivationDate || '-'"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('标定日期')" prop="calibrationDate">
            <el-date-picker
              v-if="!formReadonly"
              v-model="form.calibrationDate"
              type="date"
              :placeholder="$t('标定时间')"
              value-format="YYYY-MM-DD"
              :disabled="formReadonly"
            />
            <el-tag v-else><span v-text="form.calibrationDate"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('车架号')" prop="vin">
            <el-select
              filterable
              v-if="!formReadonly"
              v-model="form.vin"
              :disabled="formReadonly"
              clearable
              popper-class="variant-input"
            >
              <el-option v-for="item in bsVehicleList" :key="item.vin" :label="item.vin" :value="item.vin" />
            </el-select>
            <el-tag v-else><span v-text="form.vin"></span></el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.version">
          <el-form-item :label="$t('版本')" prop="version">
            <el-tag><span v-text="form.version"></span></el-tag>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-form-item :label="$t('备注')" :prop="'remark'">
              <ltw-input v-model="form.remark" type="textarea" :disabled="formReadonly"/>
            </el-form-item> -->

      <el-form-item :label="$t('文件')" :prop="'sysFileVOList'">
        <el-button v-if="dialogStatus !== 'view'" size="small" type="primary" class="add-file" @click="addFiles">
          <ltw-icon icon-code="el-icon-plus"></ltw-icon>
        </el-button>
        <el-table :data="form.sysFileVOList" style="width: 100%; margin-top: -8px" class="no-header-table">
          <el-table-column width="200" prop="sourceType" label="文件类型">
            <template #default="scope">
              <el-select
                v-if="dialogStatus === 'add'"
                v-model="scope.row.sourceType"
                @change="handleChange"
                @clear="scope.row.sourceType = undefined"
              >
                <el-option
                  v-for="item in fileTypeList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  :disabled="item.disabled || getSourceTypeDisabled(scope.row.sourceType, item.code)"
                />
              </el-select>
              <el-col v-else :span="12">
                <span> {{ getNameByCode(scope.row.sourceType) }}</span>
              </el-col>
            </template>
          </el-table-column>
          <el-table-column label="附件" prop="fileId">
            <template #default="scope">
              <div>
                <upload-file
                  class="upload-file"
                  ref="uploadImage"
                  :disabled="dialogStatus === 'view' || scope.row.sourceId"
                  :source-id="scope.row.sourceId"
                  :source-type="scope.row.sourceType"
                  :limit="1"
                  listType="text"
                  v-model="scope.row.fileId"
                  accept=".zip"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="operation" width="80" v-if="dialogStatus !== 'view'">
            <template #default="scope">
              <el-tooltip effect="dark" :content="$t('删除')" placement="top" :enterable="false">
                <el-popconfirm
                  width="250"
                  :title="$t('这个操作会删除当前选择的数据，是否继续？')"
                  @confirm="deleteSysFile(scope.row, scope.$index)"
                >
                  <template #reference>
                    <el-button type="danger" size="small">
                      <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                    </el-button>
                  </template>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <!--        <upload-file-->
        <!--          class="upload-demo"-->
        <!--          :disabled="dialogStatus !== 'add'"-->
        <!--          ref="uploadImage"-->
        <!--          source-type="calibration_parameter_file"-->
        <!--          :limit="1"-->
        <!--          listType="text"-->
        <!--          :source-id="form.id"-->
        <!--          v-model="form.fileId"-->
        <!--          accept=".zip"-->
        <!--        />-->
      </el-form-item>
      <el-col :span="12">
        <el-form-item :label="$t('是否有效')" id="enable" prop="enable">
          <el-switch
            v-model="form.enable"
            :disabled="formReadonly"
            inline-prompt
            :active-text="$t('是')"
            :inactive-text="$t('否')"
            id="enable"
            style="--el-switch-on-color: #13ce66"
          ></el-switch>
          <!-- <el-tag v-else><span v-text="form.enable"></span></el-tag> -->
        </el-form-item>
      </el-col>
      <el-form-item :label="$t('描述')" :prop="'description'">
        <ltw-input v-model="form.description" type="textarea" :disabled="formReadonly" />
      </el-form-item>
    </el-form>
    <div class="sensor-files" v-if="dialogStatus === 'view'">
      <el-divider content-position="left">传感器文件</el-divider>
      <div class="sensor-content">
        <el-button type="primary" plain @click="downloadFiles()" class="download-btn">
          <ltw-icon icon-code="svg-download"></ltw-icon>
          {{ $t('下载') }}
        </el-button>
        <el-tabs
          :style="{ left: elTabHeaderWidth + 'px' }"
          v-model="fileType"
          class="file-type-tabs"
          @tab-change="changeFileType"
        >
          <el-tab-pane label="JSON" name="JSON"></el-tab-pane>
          <el-tab-pane label="YAML" name="YAML"></el-tab-pane>
        </el-tabs>
        <el-tabs tab-position="left" v-model="sensorModel" class="sensor-list-tabs">
          <template v-for="(item, index) in tabList[fileType]" :key="index">
            <el-tab-pane lazy :label="item.modalityCode" :name="item.modalityCode">
              <div class="border-card-content">
                <div class="text">
                  <json-editor-vue
                    v-if="fileType === 'JSON'"
                    style="margin-top: 10px"
                    :currentMode="jsonMode"
                    :language="jsonLang"
                    v-model="item.content"
                    :modeList="modeList"
                  ></json-editor-vue>
                  <el-scrollbar height="800px" v-else>
                    <div v-for="(value, key) in item.content" :key="key">
                      <div class="divider-style">
                        <el-divider>
                          <el-icon>
                            <star-filled />
                          </el-icon>
                          {{ key }}
                        </el-divider>
                      </div>
                      <Codemirror
                        v-if="fileType === 'YAML'"
                        v-model:value="item.content[key]"
                        border
                        :options="cmOptions"
                      ></Codemirror>
                      <!--                          <div-->
                      <!--                            v-html="trans(value)"-->
                      <!--                            style="margin-left: 20px"-->
                      <!--                          ></div>-->
                    </div>
                  </el-scrollbar>
                </div>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>
      </div>
      <!--        </el-tab-pane>-->
      <!--      </el-tabs>-->
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="cancel">{{ $t('取消') }}</el-button>
          <el-button
            v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
            id="submit"
            type="primary"
            @click="submit"
            >{{ $t('保存') }}</el-button
          >
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { showToast, downloadTxt } from '@/plugins/util'
import { listBsVehicleSelection } from '@/apis/fleet/bs-vehicle'
import {
  saveFtmCalibrationRecords,
  updateFtmCalibrationRecords,
  getFtmCalibrationRecords,
  getFtmCalibrationResults
} from '@/apis/fleet/ftm-calibration-records'
// import { getFileList } from "@/apis/base/file";
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'
import GLB_CONFIG from '@/plugins/glb-constant'
import JsonEditorVue from 'json-editor-vue3'
import UploadFile from '@/components/system/UploadFile.vue'
import util, { getLocale, dateUtils } from '@/plugins/util'
import JsonViewer from 'vue-json-viewer'
import { deleteFile } from '@/apis/base/file'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import LtwIcon from '@/components/base/LtwIcon.vue'
import BASE_CONSTANT from '@/plugins/constants/base-constant'

const defaultFormData = { enable: true }
export default {
  name: 'AddCalibration',
  emits: ['reload', 'cancel'],
  data() {
    return {
      fileTypeList: [
        {
          code: 'calibration_parameter_factory_file',
          name: '产线',
          disabled: false
        },
        {
          code: 'calibration_parameter_file',
          name: '标定间',
          disabled: false
        },
        {
          code: 'calibration_parameter_trans_file',
          name: '标定间trans',
          disabled: true
        }
      ],
      activeName: 'JsonFile',
      dialogStatus: '',
      objectValue: {},
      dialogVisible: false,
      dialogTitle: '',
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      form: Object.assign({}, defaultFormData),
      formRules: {
        activationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        calibrationDate: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        // toolVersion: [
        //   { required: true, message: this.$t('请选择'), trigger: 'change' }
        // ],
        vin: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        sysFileVOList: [{ required: true, message: this.$t('请选择'), trigger: 'change' }],
        enable: [{ required: true, message: this.$t('请选择'), trigger: 'change' }]
      },
      fileType: 'JSON',
      sensorModel: '',
      tabList: [],
      jsonLang: getLocale() === 'en' ? 'en' : 'zh-CN',
      jsonMode: 'view',
      modeList: ['view'],
      // modeList: ['tree', 'code', 'form', 'text', 'view']
      cmOptions: {},
      elTabHeaderWidth: 0,
      bsVehicleList: [],
      queryParam: {
        current: 1,
        size: 10
      },
      jsonData: [],
      contentValue: '',
      pageData: {
        total: 0
      },
      records: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    LtwIcon,
    UploadFile,
    JsonEditorVue,
    Codemirror,
    JsonViewer
  },
  created() {},
  methods: {
    getNameByCode(code) {
      const fileType = this.fileTypeList.find(item => item.code === code)
      return fileType ? fileType.name : ''
    },
    handleChange(val) {
      if (this.form.fileId) {
        this.deleteAttachment(this.form.fileId)
      }
    },
    deleteAttachment(id) {
      deleteFile(id).then(() => {
        this.form.id = null
      })
    },
    async show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      switch (row.type) {
        case 'add':
          this.form.vin = row.vin
          this.form.sysFileVOList = []
          this.form.sysFileVOList.push({
            sourceType: 'calibration_parameter_factory_file'
          })
          this.dialogTitle = this.$t('新增') + ' ' + this.$t('标定参数')
          break
        case 'edit':
          this.form.id = JSON.parse(JSON.stringify(row.data.row.id))
          this.dialogTitle = this.$t('编辑') + ' ' + this.$t('标定参数')
          this.form = await this.getFtmCalibrationRecords()
          break
        case 'view':
          this.form.id = JSON.parse(JSON.stringify(row.data.row.id))
          this.dialogTitle = this.$t('标定参数') + ' ' + this.$t('详情')
          this.form = await this.getFtmCalibrationRecords()
          this.getFtmCalibrationResults()
          this.cmOptions = {
            // mode: "text/javascript", // Language mode
            // readOnly: "nocursor",
            mode: 'application/json', // Language mode text/yaml、text/javascript
            theme: 'dracula', // Theme
            // readOnly: 'nocursor'
            indentUnit: 4, // 缩进多少个空格
            tabSize: 4, // 制表符宽度
            // lineNumbers: true, // 是否显示行号
            lineWrapping: true, // 是否默认换行
            // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
            readOnly: this.formReadonly, // 禁止用户编辑编辑器内容
            // line: true,
            smartIndent: true // 智能缩进
          }

          // this.jsonData.push(JSON.parse(this.form.content));

          // if(this.jsonData.length){
          //   this.jsonData.splice(0,this.jsonData.length);
          // }

          this.contentValue = this.sourceType == 'calibration_parameter_file' ? '最终文件' : 'trans文件'
          break
      }
      this.listBsVehicleSelection()
      this.listFileTypes()
    },
    listFileTypes() {
      listSysDictionary({ typeCode: 'calibration_file_type' }).then(res => {
        this.fileTypeList = res.data.map(val => {
          return {
            code: val.code,
            name: val.name,
            disabled: val.code === 'calibration_parameter_trans_file'
          }
        })
      })
    },
    getFtmCalibrationResults() {
      getFtmCalibrationResults({ recordId: this.form.id }).then(res => {
        // res.data.forEach(val => {
        //   // val.content = val.content.replace(/(\\r)*\\n/g, "\n");
        //   val.jsonContent = JSON.parse(val.jsonContent || '{}')
        //   val.content = JSON.parse(val.content || '{}')
        //   // this.jsonData.push(val.content);
        // })
        // res = {
        //   "data": [
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "b4c985f8a1bf4430b81e23428592a440",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "BlindLidar01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "74d05195a5074d0daddd8b86e464af32",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "BlindLidar02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "30aa0abf341044daa972dd571a53171a",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "BlindLidar03",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "d386bbe6af114a0b9ae88ae64a51ee59",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "BlindLidar04",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"pinhole\": \"%YAML:1.0\\n---\\nmodel_type: PINHOLE\\ncamera_name: front_tele\\nimage_width: 3840\\nimage_height: 2160\\ndistortion_parameters:\\n   k1: -1.7091148151737365e-01\\n   k2: -2.2295258745582589e+00\\n   k3: 1.0713374512698682e+01\\n   k4: 0.\\n   k5: 0.\\n   k6: 0.\\n   p1: -4.8689034245949888e-04\\n   p2: -8.5743338990464682e-05\\nprojection_parameters:\\n   fx: 7.3175542515044999e+03\\n   fy: 7.3178049759946080e+03\\n   cx: 1.9733897925434603e+03\\n   cy: 1.0582899343867759e+03\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00687, 0.00074, 0.99998, 2.00117, \\n          -0.99989, 0.01304, -0.00687, -0.06048, \\n          -0.01305, -0.99991, 0.00065, 1.74757, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.00163, -0.00067, -1.00000, -5.75814, \\n          0.99991, -0.01304, 0.00164, 0.13429, \\n          -0.01305, -0.99991, 0.00065, -124.87043, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "437fd9ce170a4005830bfffb28f974f7",
        //       "jsonContent": "{\"pinhole\":\"\\n---\\nmodel_type: PINHOLE\\ncamera_name: front_tele\\nimage_width: 3840\\nimage_height: 2160\\ndistortion_parameters:\\n   k1: -1.7091148151737365e-01\\n   k2: -2.2295258745582589e+00\\n   k3: 1.0713374512698682e+01\\n   k4: 0.\\n   k5: 0.\\n   k6: 0.\\n   p1: -4.8689034245949888e-04\\n   p2: -8.5743338990464682e-05\\nprojection_parameters:\\n   fx: 7.3175542515044999e+03\\n   fy: 7.3178049759946080e+03\\n   cx: 1.9733897925434603e+03\\n   cy: 1.0582899343867759e+03\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00687, 0.00074, 0.99998, 2.00117, \\n          -0.99989, 0.01304, -0.00687, -0.06048, \\n          -0.01305, -0.99991, 0.00065, 1.74757, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.00163, -0.00067, -1.00000, -5.75814, \\n          0.99991, -0.01304, 0.00164, 0.13429, \\n          -0.01305, -0.99991, 0.00065, -124.87043, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "FrontCam01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_wide\\nimage_width: 3840\\nimage_height: 2160\\nprojection_parameters:\\n   k2: -3.6313698105215599e-02\\n   k3: 4.2180224781801431e-03\\n   k4: -8.4081405728390467e-03\\n   k5: 3.9803146235891330e-03\\n   mu: 1.9029389810413661e+03\\n   mv: 1.9029775522353277e+03\\n   u0: 1.9209885542286031e+03\\n   v0: 1.0735513073473442e+03\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00845, -0.00121, 0.99996, 1.99823, \\n          -0.99985, 0.01538, -0.00843, -0.09614, \\n          -0.01537, -0.99988, -0.00134, 1.74872, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.00322, 0.00129, -0.99999, -5.75538, \\n          0.99988, -0.01537, 0.00320, 0.16996, \\n          -0.01537, -0.99988, -0.00134, -124.86928, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "c6cca3594d7e4a349812cb8d46c32768",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_wide\\nimage_width: 3840\\nimage_height: 2160\\nprojection_parameters:\\n   k2: -3.6313698105215599e-02\\n   k3: 4.2180224781801431e-03\\n   k4: -8.4081405728390467e-03\\n   k5: 3.9803146235891330e-03\\n   mu: 1.9029389810413661e+03\\n   mv: 1.9029775522353277e+03\\n   u0: 1.9209885542286031e+03\\n   v0: 1.0735513073473442e+03\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00845, -0.00121, 0.99996, 1.99823, \\n          -0.99985, 0.01538, -0.00843, -0.09614, \\n          -0.01537, -0.99988, -0.00134, 1.74872, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.00322, 0.00129, -0.99999, -5.75538, \\n          0.99988, -0.01537, 0.00320, 0.16996, \\n          -0.01537, -0.99988, -0.00134, -124.86928, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "FrontCam02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "5819777d615f4b02851a11bccbb41eb0",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "MainLidar01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"world\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\", \"vehicle\": \"%YAML:1.0\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "id": "ec1349d025134ae9affdf56a75000e9d",
        //       "jsonContent": "{\"world\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.0180698 -0.476749  -179.977\\nT_w_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.999961, 0.000400834, -0.00832072, -4.12599,\\n        -0.000398045, -1, -0.000315366, 0.0887498,\\n        -0.00832151, -0.000307753, 0.999964, -124.606,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  0.81435 -34.4207  -179.42\\nT_w_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.824867, 0.00834653, -0.565265, -7.45411,\\n        -0.00208361, -0.999929, -0.0117241, 0.0688845,\\n        -0.565323, -0.00849303, 0.824826, -125.792,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:  -28.3204 -0.890728   89.9884\\nT_w_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.000201688, -0.999879, -0.0155455, -4.11352,\\n        0.88031, -0.00719726, 0.474345, 0.867666,\\n        -0.474399, -0.0137805, 0.880202, -124.759,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree: -1.67372  32.5994  2.58669\\nT_w_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.841599, -0.0380208, 0.538762, -2.51048,\\n        0.0293916, 0.999265, 0.0246063, 0.107034,\\n        -0.539302, -0.00487359, 0.842098, -125.753,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   27.9701 0.0995226  -89.5514\\nT_w_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.00782864, 0.999968, 0.001737, -4.10696,\\n        -0.883159, 0.00772885, -0.469011, -0.687548,\\n        -0.469009, 0.00213767, 0.883191, -124.76,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to World:\\n# XYZ Euler Angle in degree:   1.11691  47.2014 -1.78673\\nT_w_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.679255, 0.0184972, 0.733666, -2.51635,\\n        -0.0172006, 0.99981, -0.00928944, 0.103392,\\n        -0.733699, -0.00631641, 0.67945, -124.982,\\n        0, 0, 0, 1]\\n\",\"vehicle\":\"\\n\\n\\n# [Lidar Calib Tool Version] V1.2.1\\n# calibration date: 2025/3/31  time: 5 : 54\\n\\n# Vehicle: workspace\\n# Wheel Base: 2800 mm\\n\\n# main Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -0.0155732   0.476837  -0.277054\\nT_v_l0: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.99995, 0.00483519, 0.00832225, 0.369288,\\n        -0.00483776, 0.999992, 0.000271794, -0.00639526,\\n        -0.00832151, -0.000307753, 0.999964, 2.0121,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_front Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree: -0.608776   34.4244  0.216076\\nT_v_l11: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [0.824867, -0.00311079, 0.565319, 3.69747,\\n        -0.00223542, 0.999959, 0.00876422, -0.0039563,\\n        -0.565323, -0.00849303, 0.824826, 0.826329,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_right Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  28.3242 0.748398 -90.2757\\nT_v_l12: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00481099, 0.999903, 0.0130616, 0.352745,\\n        -0.880297, 0.00196179, -0.47442, -0.785236,\\n        -0.474399, -0.0137805, 0.880202, 1.85919,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  1.48191 -32.6077 -177.769\\nT_v_l13: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.841742, 0.0327882, -0.538884, -1.24629,\\n        -0.0249846, -0.99945, -0.021785, -0.0162206,\\n        -0.539302, -0.00487359, 0.842098, 0.86509,\\n        0, 0, 0, 1]\\n\\n\\n# qt128_left Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -27.9703 0.0411823   90.1836\\nT_v_l14: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.00320432, -0.999995, 0.000718767, 0.354322,\\n        0.883188, -0.00249292, 0.469013, 0.769992,\\n        -0.469009, 0.00213767, 0.883191, 1.8582,\\n        0, 0, 0, 1]\\n\\n# main_rear Lidar\\n# Extrinsic relative to Vehicle:\\n# XYZ Euler Angle in degree:  -1.44078 -47.1947  177.772\\nT_v_l15: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [-0.679156, -0.0237319, -0.733607, -1.24041,\\n        0.020757, -0.9997, 0.0131308, -0.0126094,\\n        -0.733699, -0.00631641, 0.67945, 1.63581,\\n        0, 0, 0, 1]\\n\"}",
        //       "modalityCode": "MainLidar02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"pinhole\": \"%YAML:1.0\\n---\\nmodel_type: PINHOLE\\ncamera_name: rear\\nimage_width: 1920\\nimage_height: 1280\\ndistortion_parameters:\\n   k1: -5.0589173719113112e-01\\n   k2: 2.7612999490525397e-01\\n   k3: -8.8322922497417286e-02\\n   k4: 0.\\n   k5: 0.\\n   k6: 0.\\n   p1: -5.9594055210701537e-04\\n   p2: -1.1466710636688827e-04\\nprojection_parameters:\\n   fx: 1.9477889798735698e+03\\n   fy: 1.9476674553508665e+03\\n   cx: 9.5621962754479773e+02\\n   cy: 6.3106353864729431e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.04184, -0.00059, -0.99912, -1.37639, \\n          0.99887, -0.02238, 0.04185, -0.01268, \\n          -0.02238, -0.99975, -0.00035, 1.21417, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03661, 0.00047, 0.99933, -2.38037, \\n          -0.99908, 0.02238, -0.03662, 0.10417, \\n          -0.02238, -0.99975, -0.00035, -125.40383, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "fedd8d99ac5943cebcfeb766a4d29623",
        //       "jsonContent": "{\"pinhole\":\"\\n---\\nmodel_type: PINHOLE\\ncamera_name: rear\\nimage_width: 1920\\nimage_height: 1280\\ndistortion_parameters:\\n   k1: -5.0589173719113112e-01\\n   k2: 2.7612999490525397e-01\\n   k3: -8.8322922497417286e-02\\n   k4: 0.\\n   k5: 0.\\n   k6: 0.\\n   p1: -5.9594055210701537e-04\\n   p2: -1.1466710636688827e-04\\nprojection_parameters:\\n   fx: 1.9477889798735698e+03\\n   fy: 1.9476674553508665e+03\\n   cx: 9.5621962754479773e+02\\n   cy: 6.3106353864729431e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.04184, -0.00059, -0.99912, -1.37639, \\n          0.99887, -0.02238, 0.04185, -0.01268, \\n          -0.02238, -0.99975, -0.00035, 1.21417, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03661, 0.00047, 0.99933, -2.38037, \\n          -0.99908, 0.02238, -0.03662, 0.10417, \\n          -0.02238, -0.99975, -0.00035, -125.40383, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "RearCam01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_left\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: 4.1768941912076767e-04\\n   k3: -1.4982963780509891e-02\\n   k4: 2.2355027611059248e-02\\n   k5: -7.1520432127118718e-03\\n   mu: 1.0982893803557374e+03\\n   mv: 1.0988819477041036e+03\\n   u0: 9.6104185342943254e+02\\n   v0: 6.4249224710393582e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.88379, 0.00623, 0.46783, 2.15338, \\n          -0.46774, -0.01243, 0.88378, 0.96000, \\n          0.01132, -0.99990, -0.00807, 1.26729, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.88623, -0.00629, -0.46320, -5.90500, \\n          0.46311, 0.01239, -0.88622, -0.88698, \\n          0.01132, -0.99990, -0.00807, -125.35071, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "b1d2d8f7024c4b01a9b8151c04ddcd7f",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_left\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: 4.1768941912076767e-04\\n   k3: -1.4982963780509891e-02\\n   k4: 2.2355027611059248e-02\\n   k5: -7.1520432127118718e-03\\n   mu: 1.0982893803557374e+03\\n   mv: 1.0988819477041036e+03\\n   u0: 9.6104185342943254e+02\\n   v0: 6.4249224710393582e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.88379, 0.00623, 0.46783, 2.15338, \\n          -0.46774, -0.01243, 0.88378, 0.96000, \\n          0.01132, -0.99990, -0.00807, 1.26729, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.88623, -0.00629, -0.46320, -5.90500, \\n          0.46311, 0.01239, -0.88622, -0.88698, \\n          0.01132, -0.99990, -0.00807, -125.35071, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SideFrontCam01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_right\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -1.9219250002772827e-02\\n   k3: 2.6291300241945044e-02\\n   k4: -1.4854588548931938e-02\\n   k5: 3.8355087293678995e-03\\n   mu: 1.1079039224446776e+03\\n   mv: 1.1069026550260153e+03\\n   u0: 9.5904226367343017e+02\\n   v0: 6.4051903684547324e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.87311, 0.02795, 0.48672, 2.12759, \\n          -0.48689, 0.00077, -0.87346, -0.97054, \\n          -0.02478, -0.99961, 0.01294, 1.26937, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.87055, -0.02794, -0.49129, -5.88931, \\n          0.49146, -0.00091, 0.87090, 1.04367, \\n          -0.02478, -0.99961, 0.01294, -125.34863, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "0436311849804b2ab6906b8a33e5b9a8",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: front_right\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -1.9219250002772827e-02\\n   k3: 2.6291300241945044e-02\\n   k4: -1.4854588548931938e-02\\n   k5: 3.8355087293678995e-03\\n   mu: 1.1079039224446776e+03\\n   mv: 1.1069026550260153e+03\\n   u0: 9.5904226367343017e+02\\n   v0: 6.4051903684547324e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.87311, 0.02795, 0.48672, 2.12759, \\n          -0.48689, 0.00077, -0.87346, -0.97054, \\n          -0.02478, -0.99961, 0.01294, 1.26937, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.87055, -0.02794, -0.49129, -5.88931, \\n          0.49146, -0.00091, 0.87090, 1.04367, \\n          -0.02478, -0.99961, 0.01294, -125.34863, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SideFrontCam02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: rear_left\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -1.5912132062100175e-02\\n   k3: 1.7663460037255285e-02\\n   k4: -1.3155751069587114e-02\\n   k5: 1.1621098464877522e-02\\n   mu: 1.1083299811369372e+03\\n   mv: 1.1089742873928631e+03\\n   u0: 9.5287013563616733e+02\\n   v0: 6.3415985476538913e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.73118, -0.08249, -0.67718, 2.30261, \\n          0.67967, 0.00290, 0.73351, 0.98469, \\n          -0.05854, -0.99659, 0.05818, 0.97336, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.72761, 0.08250, 0.68101, -6.05409, \\n          -0.68349, -0.00247, -0.72996, -0.91245, \\n          -0.05854, -0.99659, 0.05818, -125.64464, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "6c090e35ca8c47148b69c3de4b3b6616",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: rear_left\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -1.5912132062100175e-02\\n   k3: 1.7663460037255285e-02\\n   k4: -1.3155751069587114e-02\\n   k5: 1.1621098464877522e-02\\n   mu: 1.1083299811369372e+03\\n   mv: 1.1089742873928631e+03\\n   u0: 9.5287013563616733e+02\\n   v0: 6.3415985476538913e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.73118, -0.08249, -0.67718, 2.30261, \\n          0.67967, 0.00290, 0.73351, 0.98469, \\n          -0.05854, -0.99659, 0.05818, 0.97336, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.72761, 0.08250, 0.68101, -6.05409, \\n          -0.68349, -0.00247, -0.72996, -0.91245, \\n          -0.05854, -0.99659, 0.05818, -125.64464, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SideRearCam01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: rear_right\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -6.1782212623962525e-03\\n   k3: -1.8927866406763551e-02\\n   k4: 4.9500713142383433e-02\\n   k5: -2.7943069996999820e-02\\n   mu: 1.1035024124949639e+03\\n   mv: 1.1037079402800637e+03\\n   u0: 9.5844817772949466e+02\\n   v0: 6.3635238507135421e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.72037, -0.05752, -0.69120, 2.30881, \\n          0.69343, -0.03836, -0.71950, -1.00925, \\n          0.01487, -0.99761, 0.06751, 0.97475, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.72399, 0.05731, 0.68742, -6.07074, \\n          -0.68965, 0.03866, 0.72311, 1.08144, \\n          0.01487, -0.99761, 0.06751, -125.64325, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "8a1f921022944c628d65839f81869c78",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: rear_right\\nimage_width: 1920\\nimage_height: 1280\\nprojection_parameters:\\n   k2: -6.1782212623962525e-03\\n   k3: -1.8927866406763551e-02\\n   k4: 4.9500713142383433e-02\\n   k5: -2.7943069996999820e-02\\n   mu: 1.1035024124949639e+03\\n   mv: 1.1037079402800637e+03\\n   u0: 9.5844817772949466e+02\\n   v0: 6.3635238507135421e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.72037, -0.05752, -0.69120, 2.30881, \\n          0.69343, -0.03836, -0.71950, -1.00925, \\n          0.01487, -0.99761, 0.06751, 0.97475, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.72399, 0.05731, 0.68742, -6.07074, \\n          -0.68965, 0.03866, 0.72311, 1.08144, \\n          0.01487, -0.99761, 0.06751, -125.64325, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SideRearCam02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_left\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.4886290613613246e-01\\n   k3: -6.7804966289301022e-02\\n   k4: 1.9444685358556254e-02\\n   k5: -2.2001545289851193e-03\\n   mu: 4.4553156295289250e+02\\n   mv: 4.4493884647898466e+02\\n   u0: 9.6136256892525239e+02\\n   v0: 7.6850266237764606e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99972, 0.02335, -0.00397, 2.00189, \\n          0.01716, -0.59854, 0.80091, 1.13605, \\n          0.01632, -0.80075, -0.59877, 1.27532, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99962, -0.02648, 0.00817, -5.75258, \\n          -0.02240, 0.59841, -0.80088, -1.06223, \\n          0.01632, -0.80075, -0.59877, -125.34268, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\", \"ocam\": \"%YAML:1.0\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_left\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4753812468744741e+02\\n   p1: 0.\\n   p2: 4.1756859862838975e-04\\n   p3: 2.3664310924205635e-07\\n   p4: 9.6110103777116947e-11\\ninv_poly_parameters:\\n   p0: 8.1475672710976096e+02\\n   p1: 5.9920250256373731e+02\\n   p2: 7.6618456507639877e+01\\n   p3: 7.6884454982297200e+01\\n   p4: 6.2197051449753552e+01\\n   p5: 1.0146265825048530e+01\\n   p6: 9.8734819118271169e+00\\n   p7: 2.1121959624260789e+01\\n   p8: 1.1082970881333326e+01\\n   p9: 1.8365128117534408e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 1.0007685081996285e+00\\n   ad: 4.0346892443821657e-03\\n   ae: -3.0771073024352543e-03\\n   cx: 9.6123559035311575e+02\\n   cy: 7.6852675024466714e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99964, 0.02640, -0.00410, 1.99924, \\n          0.01911, -0.59914, 0.80041, 1.13284, \\n          0.01868, -0.80021, -0.59943, 1.27857, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99953, -0.02954, 0.00830, -5.74995, \\n          -0.02435, 0.59900, -0.80038, -1.05900, \\n          0.01868, -0.80021, -0.59943, -125.33943, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "45518d6884f5404cb26cb615086f50f5",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_left\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.4886290613613246e-01\\n   k3: -6.7804966289301022e-02\\n   k4: 1.9444685358556254e-02\\n   k5: -2.2001545289851193e-03\\n   mu: 4.4553156295289250e+02\\n   mv: 4.4493884647898466e+02\\n   u0: 9.6136256892525239e+02\\n   v0: 7.6850266237764606e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99972, 0.02335, -0.00397, 2.00189, \\n          0.01716, -0.59854, 0.80091, 1.13605, \\n          0.01632, -0.80075, -0.59877, 1.27532, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99962, -0.02648, 0.00817, -5.75258, \\n          -0.02240, 0.59841, -0.80088, -1.06223, \\n          0.01632, -0.80075, -0.59877, -125.34268, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\",\"ocam\":\"\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_left\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4753812468744741e+02\\n   p1: 0.\\n   p2: 4.1756859862838975e-04\\n   p3: 2.3664310924205635e-07\\n   p4: 9.6110103777116947e-11\\ninv_poly_parameters:\\n   p0: 8.1475672710976096e+02\\n   p1: 5.9920250256373731e+02\\n   p2: 7.6618456507639877e+01\\n   p3: 7.6884454982297200e+01\\n   p4: 6.2197051449753552e+01\\n   p5: 1.0146265825048530e+01\\n   p6: 9.8734819118271169e+00\\n   p7: 2.1121959624260789e+01\\n   p8: 1.1082970881333326e+01\\n   p9: 1.8365128117534408e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 1.0007685081996285e+00\\n   ad: 4.0346892443821657e-03\\n   ae: -3.0771073024352543e-03\\n   cx: 9.6123559035311575e+02\\n   cy: 7.6852675024466714e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99964, 0.02640, -0.00410, 1.99924, \\n          0.01911, -0.59914, 0.80041, 1.13284, \\n          0.01868, -0.80021, -0.59943, 1.27857, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99953, -0.02954, 0.00830, -5.74995, \\n          -0.02435, 0.59900, -0.80038, -1.05900, \\n          0.01868, -0.80021, -0.59943, -125.33943, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SurCam01",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_front\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.5062284352640157e-01\\n   k3: -7.2689830504267722e-02\\n   k4: 2.2683394934598547e-02\\n   k5: -2.8665590404787218e-03\\n   mu: 4.4459181891321947e+02\\n   mv: 4.4506284172568456e+02\\n   u0: 9.4129115085155229e+02\\n   v0: 7.8025726007217963e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.02824, -0.25371, 0.96687, 3.73399, \\n          -0.99901, 0.02605, 0.03601, -0.00327, \\n          -0.03432, -0.96693, -0.25272, 1.02551, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03347, 0.25384, -0.96667, -7.49063, \\n          0.99885, -0.02472, -0.04107, 0.06801, \\n          -0.03432, -0.96693, -0.25272, -125.59249, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\", \"ocam\": \"%YAML:1.0\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_front\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4678573064171076e+02\\n   p1: 0.\\n   p2: 4.1820039464902345e-04\\n   p3: 2.3337709092825610e-07\\n   p4: 1.0174540287119173e-10\\ninv_poly_parameters:\\n   p0: 8.1337304795823184e+02\\n   p1: 5.9696349071671091e+02\\n   p2: 7.4194366116921671e+01\\n   p3: 7.5095359399824730e+01\\n   p4: 6.1523537622120728e+01\\n   p5: 1.2645930398996782e+01\\n   p6: 1.3562966669764009e+01\\n   p7: 1.8469362858683908e+01\\n   p8: 3.4186305412789575e+00\\n   p9: -3.0408317309932436e+00\\n   p10: -1.0168332497460386e+00\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 9.9923137643677207e-01\\n   ad: 1.3599673178711455e-03\\n   ae: 1.7454247878819165e-04\\n   cx: 9.4352575576011577e+02\\n   cy: 7.8014456690649240e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.02557, -0.25358, 0.96698, 3.73426, \\n          -0.99910, 0.02636, 0.03333, -0.00909, \\n          -0.03394, -0.96695, -0.25268, 1.02620, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03080, 0.25372, -0.96679, -7.49093, \\n          0.99895, -0.02503, -0.03840, 0.07383, \\n          -0.03394, -0.96695, -0.25268, -125.59180, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "f7ebd08f0b1c41bba2e78244eedb633f",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_front\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.5062284352640157e-01\\n   k3: -7.2689830504267722e-02\\n   k4: 2.2683394934598547e-02\\n   k5: -2.8665590404787218e-03\\n   mu: 4.4459181891321947e+02\\n   mv: 4.4506284172568456e+02\\n   u0: 9.4129115085155229e+02\\n   v0: 7.8025726007217963e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.02824, -0.25371, 0.96687, 3.73399, \\n          -0.99901, 0.02605, 0.03601, -0.00327, \\n          -0.03432, -0.96693, -0.25272, 1.02551, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03347, 0.25384, -0.96667, -7.49063, \\n          0.99885, -0.02472, -0.04107, 0.06801, \\n          -0.03432, -0.96693, -0.25272, -125.59249, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\",\"ocam\":\"\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_front\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4678573064171076e+02\\n   p1: 0.\\n   p2: 4.1820039464902345e-04\\n   p3: 2.3337709092825610e-07\\n   p4: 1.0174540287119173e-10\\ninv_poly_parameters:\\n   p0: 8.1337304795823184e+02\\n   p1: 5.9696349071671091e+02\\n   p2: 7.4194366116921671e+01\\n   p3: 7.5095359399824730e+01\\n   p4: 6.1523537622120728e+01\\n   p5: 1.2645930398996782e+01\\n   p6: 1.3562966669764009e+01\\n   p7: 1.8469362858683908e+01\\n   p8: 3.4186305412789575e+00\\n   p9: -3.0408317309932436e+00\\n   p10: -1.0168332497460386e+00\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 9.9923137643677207e-01\\n   ad: 1.3599673178711455e-03\\n   ae: 1.7454247878819165e-04\\n   cx: 9.4352575576011577e+02\\n   cy: 7.8014456690649240e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.02557, -0.25358, 0.96698, 3.73426, \\n          -0.99910, 0.02636, 0.03333, -0.00909, \\n          -0.03394, -0.96695, -0.25268, 1.02620, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.03080, 0.25372, -0.96679, -7.49093, \\n          0.99895, -0.02503, -0.03840, 0.07383, \\n          -0.03394, -0.96695, -0.25268, -125.59180, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SurCam02",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_right\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.3012470450802249e-01\\n   k3: -5.2662538578690181e-02\\n   k4: 1.4284819331796384e-02\\n   k5: -1.5647417505388688e-03\\n   mu: 4.4851385652047475e+02\\n   mv: 4.4796877954785600e+02\\n   u0: 9.5849525510361468e+02\\n   v0: 7.6719291675737861e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99922, 0.02893, 0.02706, 1.96351, \\n          -0.00547, 0.57579, -0.81758, -1.13874, \\n          -0.03923, -0.81708, -0.57518, 1.27869, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99917, -0.02591, -0.03134, -5.72612, \\n          0.01070, -0.57594, 0.81742, 1.21273, \\n          -0.03923, -0.81708, -0.57518, -125.33931, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\", \"ocam\": \"%YAML:1.0\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_right\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4803000802225222e+02\\n   p1: 0.\\n   p2: 4.2369328765131791e-04\\n   p3: 2.2029162876743062e-07\\n   p4: 1.0761671665410242e-10\\ninv_poly_parameters:\\n   p0: 8.1490324774452620e+02\\n   p1: 5.9811500372712419e+02\\n   p2: 7.3761447526643977e+01\\n   p3: 7.4635383294885543e+01\\n   p4: 6.1843263450273867e+01\\n   p5: 9.5505713050939178e+00\\n   p6: 8.8083416664150853e+00\\n   p7: 2.0918251192457966e+01\\n   p8: 1.1332940082197332e+01\\n   p9: 1.9247513591432250e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 1.0008484134761089e+00\\n   ad: 7.2785387368609134e-04\\n   ae: -7.1854881717701373e-04\\n   cx: 9.5962957499731465e+02\\n   cy: 7.6719052615790531e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99927, 0.02815, 0.02598, 1.95865, \\n          -0.00502, 0.57630, -0.81722, -1.13406, \\n          -0.03798, -0.81675, -0.57574, 1.28333, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99923, -0.02513, -0.03026, -5.72123, \\n          0.01025, -0.57644, 0.81707, 1.20807, \\n          -0.03798, -0.81675, -0.57574, -125.33467, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "aac522a6ac254d8ea1801b1c4a82e81b",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_right\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.3012470450802249e-01\\n   k3: -5.2662538578690181e-02\\n   k4: 1.4284819331796384e-02\\n   k5: -1.5647417505388688e-03\\n   mu: 4.4851385652047475e+02\\n   mv: 4.4796877954785600e+02\\n   u0: 9.5849525510361468e+02\\n   v0: 7.6719291675737861e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99922, 0.02893, 0.02706, 1.96351, \\n          -0.00547, 0.57579, -0.81758, -1.13874, \\n          -0.03923, -0.81708, -0.57518, 1.27869, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99917, -0.02591, -0.03134, -5.72612, \\n          0.01070, -0.57594, 0.81742, 1.21273, \\n          -0.03923, -0.81708, -0.57518, -125.33931, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\",\"ocam\":\"\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_right\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4803000802225222e+02\\n   p1: 0.\\n   p2: 4.2369328765131791e-04\\n   p3: 2.2029162876743062e-07\\n   p4: 1.0761671665410242e-10\\ninv_poly_parameters:\\n   p0: 8.1490324774452620e+02\\n   p1: 5.9811500372712419e+02\\n   p2: 7.3761447526643977e+01\\n   p3: 7.4635383294885543e+01\\n   p4: 6.1843263450273867e+01\\n   p5: 9.5505713050939178e+00\\n   p6: 8.8083416664150853e+00\\n   p7: 2.0918251192457966e+01\\n   p8: 1.1332940082197332e+01\\n   p9: 1.9247513591432250e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 1.0008484134761089e+00\\n   ad: 7.2785387368609134e-04\\n   ae: -7.1854881717701373e-04\\n   cx: 9.5962957499731465e+02\\n   cy: 7.6719052615790531e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.99927, 0.02815, 0.02598, 1.95865, \\n          -0.00502, 0.57630, -0.81722, -1.13406, \\n          -0.03798, -0.81675, -0.57574, 1.28333, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.99923, -0.02513, -0.03026, -5.72123, \\n          0.01025, -0.57644, 0.81707, 1.20807, \\n          -0.03798, -0.81675, -0.57574, -125.33467, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SurCam03",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     },
        //     {
        //       "activationDate": "2025-03-31",
        //       "content": "{\"kb8\": \"%YAML:1.0\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_rear\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.5315107725201169e-01\\n   k3: -7.3800728550812225e-02\\n   k4: 2.2787060563731056e-02\\n   k5: -2.8285059523105506e-03\\n   mu: 4.4526329328078975e+02\\n   mv: 4.4581887399037686e+02\\n   u0: 9.4504391823594938e+02\\n   v0: 7.7561769609868793e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.01439, 0.33921, -0.94060, -1.36177, \\n          0.99960, 0.01815, 0.02184, -0.00600, \\n          0.02448, -0.94054, -0.33881, 1.02866, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00915, -0.33911, 0.94070, -2.39496, \\n          -0.99966, -0.01992, -0.01691, 0.09742, \\n          0.02448, -0.94054, -0.33881, -125.58934, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\", \"ocam\": \"%YAML:1.0\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_rear\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4791717680151521e+02\\n   p1: 0.\\n   p2: 4.2498896041484997e-04\\n   p3: 2.1196071003873628e-07\\n   p4: 1.1258193876346269e-10\\ninv_poly_parameters:\\n   p0: 8.1589267379246417e+02\\n   p1: 5.9927313978469397e+02\\n   p2: 7.2598722073063726e+01\\n   p3: 7.3334320584657107e+01\\n   p4: 6.2098186960157555e+01\\n   p5: 9.2391142299155185e+00\\n   p6: 8.0399198036449029e+00\\n   p7: 2.0961244451093780e+01\\n   p8: 1.1665682610736859e+01\\n   p9: 2.0218044125239647e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 9.9847606061162197e-01\\n   ad: 2.5509328909691337e-03\\n   ae: -2.9027756444126077e-03\\n   cx: 9.4330998877425270e+02\\n   cy: 7.7618230366173088e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.01107, 0.33997, -0.94037, -1.36391, \\n          0.99959, 0.02119, 0.01943, -0.01162, \\n          0.02653, -0.94020, -0.33959, 1.02621, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: !!opencv-matrix\\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00584, -0.33985, 0.94046, -2.39284, \\n          -0.99963, -0.02297, -0.01451, 0.10305, \\n          0.02653, -0.94020, -0.33959, -125.59179, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "id": "e8a51d105a3c4424bff581b07c7125a9",
        //       "jsonContent": "{\"kb8\":\"\\n---\\nmodel_type: KANNALA_BRANDT\\ncamera_name: nrcs_rear\\nimage_width: 1920\\nimage_height: 1536\\nprojection_parameters:\\n   k2: 1.5315107725201169e-01\\n   k3: -7.3800728550812225e-02\\n   k4: 2.2787060563731056e-02\\n   k5: -2.8285059523105506e-03\\n   mu: 4.4526329328078975e+02\\n   mv: 4.4581887399037686e+02\\n   u0: 9.4504391823594938e+02\\n   v0: 7.7561769609868793e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.01439, 0.33921, -0.94060, -1.36177, \\n          0.99960, 0.01815, 0.02184, -0.00600, \\n          0.02448, -0.94054, -0.33881, 1.02866, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00915, -0.33911, 0.94070, -2.39496, \\n          -0.99966, -0.01992, -0.01691, 0.09742, \\n          0.02448, -0.94054, -0.33881, -125.58934, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\",\"ocam\":\"\\n---\\nmodel_type: SCARAMUZZA\\ncamera_name: nrcs_rear\\nimage_width: 1920\\nimage_height: 1536\\npoly_parameters:\\n   p0: -4.4791717680151521e+02\\n   p1: 0.\\n   p2: 4.2498896041484997e-04\\n   p3: 2.1196071003873628e-07\\n   p4: 1.1258193876346269e-10\\ninv_poly_parameters:\\n   p0: 8.1589267379246417e+02\\n   p1: 5.9927313978469397e+02\\n   p2: 7.2598722073063726e+01\\n   p3: 7.3334320584657107e+01\\n   p4: 6.2098186960157555e+01\\n   p5: 9.2391142299155185e+00\\n   p6: 8.0399198036449029e+00\\n   p7: 2.0961244451093780e+01\\n   p8: 1.1665682610736859e+01\\n   p9: 2.0218044125239647e+00\\n   p10: 0.\\n   p11: 0.\\n   p12: 0.\\n   p13: 0.\\n   p14: 0.\\n   p15: 0.\\n   p16: 0.\\n   p17: 0.\\n   p18: 0.\\n   p19: 0.\\naffine_parameters:\\n   ac: 9.9847606061162197e-01\\n   ad: 2.5509328909691337e-03\\n   ae: -2.9027756444126077e-03\\n   cx: 9.4330998877425270e+02\\n   cy: 7.7618230366173088e+02\\n\\n# back axis coordinate (origin is on the ground, z = 0) to camera\\nT_v_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ 0.01107, 0.33997, -0.94037, -1.36391, \\n          0.99959, 0.02119, 0.01943, -0.01162, \\n          0.02653, -0.94020, -0.33959, 1.02621, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\\n# calib_room coordinate to camera (deprecated, don't use)\\nT_w_c: \\n  rows: 4\\n  cols: 4\\n  dt: d\\n  data: [ -0.00584, -0.33985, 0.94046, -2.39284, \\n          -0.99963, -0.02297, -0.01451, 0.10305, \\n          0.02653, -0.94020, -0.33959, -125.59179, \\n          0.00000, 0.00000, 0.00000, 1.00000]\\n\"}",
        //       "modalityCode": "SurCam04",
        //       "recordId": "95d87d25d3774a7c910633f95eaacabb",
        //       "vin": "15122C"
        //     }
        //   ],
        //   "status": 1,
        //   "success": true
        // }
        res.data.forEach(val => {
          val.content = JSON.parse(val.content || '{}')
        })
        let tabList = {
          JSON: [
            {
              modalityCode: '整车',
              content: JSON.parse(this.form.content || '{}')
            }
          ],
          YAML: res.data
        }
        this.tabList = tabList
        this.changeFileType('JSON')
        setTimeout(() => {
          let elTabHeaderDom = document.querySelector('.sensor-list-tabs .el-tabs__header.is-left')
          this.elTabHeaderWidth = elTabHeaderDom.clientWidth
        })
      })
    },
    getFtmCalibrationRecords() {
      return new Promise(resolve => {
        getFtmCalibrationRecords(this.form.id).then(res => {
          resolve(res.data)
        })
      })
    },
    dialogClosed() {
      this.initForm()
    },
    submitCancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    // getObject(value) {
    //   let obj = JSON.parse(value);
    //   return obj;
    // },
    trans(value) {
      return value.replace(/\n/g, '<br>')
    },
    listBsVehicleSelection() {
      listBsVehicleSelection().then(res => {
        this.bsVehicleList = res.data
      })
    },
    dialogOpened() {},
    submit() {
      const fileIdList = this.form.sysFileVOList
          .filter(val => val.fileId?.[0] || val.id)
          .map(val => val.fileId?.[0] || val.id)
      if (!fileIdList?.length) {
        showToast('请先上传文件', 'warning')
        return
      }
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = {
          ...this.form,
          fileIdList: fileIdList
          // fileId: this.form.fileId?.length && this.form.fileId[0]
        }
        if (this.form.deactivationDate && this.form.activationDate) {
          if (new Date(this.form.deactivationDate).getTime() < new Date(this.form.activationDate).getTime()) {
            showToast('生效时间不可大于失效时间', 'warning')
            return
          }
        }
        if (this.dialogStatus === 'add') {
          saveFtmCalibrationRecords(postData).then(() => {
            // this.dialogVisible = false;
            // this.$emit("reload", this.form.id);
            // this.cancelCalibration()
            this.submitCancel()
          })
        } else if (this.dialogStatus === 'edit') {
          updateFtmCalibrationRecords(postData).then(() => {
            this.dialogVisible = false
            // this.$emit('reload', this.form.id)
            // this.cancelCalibration()
            this.submitCancel()
          })
        }
      })
    },
    initForm() {
      this.tabList = []
      this.fileType = 'JSON'
      this.form = Object.assign({}, defaultFormData)
    },
    downloadFiles() {
      let item = this.tabList[this.fileType].find(val => val.modalityCode === this.sensorModel)
      if (this.fileType === 'JSON') {
        downloadTxt('JSON', JSON.stringify(item.content))
      } else {
        downloadTxt(item.modalityCode, JSON.stringify(item.content))
      }
    },
    disabledEndDate(val) {
      if (this.form.activationDate) {
        return (
          new Date(val) < new Date(dateUtils.parseTime(this.form.activationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
        )
        // return new Date(val) < new Date(this.form.activationDate).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.form.deactivationDate) {
        return (
          new Date(val) >
          new Date(dateUtils.parseTime(this.form.deactivationDate, '{y}-{m}-{d}') + ' 00:00:00').getTime()
        )
      }
    },
    addFiles() {
      const index = this.fileTypeList.findIndex(
        val => !~this.form.sysFileVOList.findIndex(row => row.sourceType === val.code) && !val.disabled
      )
      if (~index) {
        this.form.sysFileVOList.push({
          sourceType: this.fileTypeList[index].code
        })
      } else {
        showToast(this.$t('每种文件类型仅可上传一次'), 'warning')
      }
    },
    deleteSysFile(row, index) {
      this.form.sysFileVOList.splice(index, 1)
    },
    getSourceTypeDisabled(sourceType, code) {
      const disabledList = this.form.sysFileVOList.filter(val => val.sourceType !== sourceType)
      const index = disabledList.findIndex(val => val.sourceType === code)
      return !!~index
    },
    changeFileType(e) {
      if (this.tabList[e]?.length) {
        this.sensorModel = this.tabList[e][0].modalityCode
        if (e === 'YAML') {
          setTimeout(() => {
            this.cmOptions = {
              mode: 'application/json', // Language mode text/yaml、text/javascript
              // mode: 'text/yaml', // Language mode text/yaml、text/javascript、application/json
              // mode: 'text/javascript', // Language mode text/yaml、text/javascript、application/json
              theme: 'dracula', // Theme
              // readOnly: 'nocursor'
              indentUnit: 4, // 缩进多少个空格
              tabSize: 4, // 制表符宽度
              lineNumbers: true, // 是否显示行号
              lineWrapping: true, // 是否默认换行
              // firstLineNumber: 1, // 在哪个数字开始计数行。默认值为1
              readOnly: true, // 禁止用户编辑编辑器内容
              // viewportMargin: Infinity, // 高度自适应
              // line: true,
              smartIndent: true // 智能缩进
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-table__expand-icon) {
  visibility: hidden;
}

.add-file {
  margin-bottom: 10px;
}

.upload-file {
  padding: 8px 0;
}

.sensor-files {
  .json-content {
    position: relative;
    margin-top: 36px;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    // .jsonTxt{
    //   height:200px

    // }
  }

  .sensor-content {
    position: relative;

    .download-btn {
      position: absolute;
      right: 0;
      top: -36px;
      // :deep(.svg-icon){
      //   font-size: 14px;
      // }
    }

    .file-type-tabs {
      // position: absolute;
      // top: -10px;
      position: absolute;
      top: -40px;

      :deep(.el-tabs__header) {
        margin: 0;
      }
    }

    .sensor-list-tabs {
      display: flex;
      margin-top: 50px;
      // overflow: visible;
      :deep(.el-tabs__header) {
        margin-right: 0;
        border-right: none;
      }

      :deep(.el-tabs__content) {
        // overflow: visible;
        flex-grow: 1;
        padding: 0;

        .divider-style {
          padding: 0 10px;
        }

        .el-tab-pane,
        .el-tab-pane > .border-card-content,
        .el-tab-pane > .border-card-content > .text,
        .el-tab-pane > .border-card-content > .text > .container {
          height: 100%;
        }

        .el-tab-pane > .border-card-content > .text {
          width: 100%;
        }

        // .el-tab-pane > .border-card-content > .text > .container>.dividerStyle {

        //   border: thin solid #3b4969;
        // }
        .el-tab-pane > .border-card-content > .text > .container .jsoneditor {
          border: thin solid #dcdfe6;
        }

        .el-tab-pane > .border-card-content > .text > .container {
          margin: 0 !important;

          .jsoneditor-outer.has-main-menu-bar.has-nav-bar {
            height: 100%;
            margin: 0;
            padding: 0;
          }

          .jsoneditor-menu,
          .jsoneditor-navigation-bar {
            display: none;
          }
        }
      }

      .border-card-content {
        position: relative;

        :deep(.jsoneditor-poweredBy) {
          display: none;
        }
      }
    }
  }
}

.no-header-table {
  :deep(.el-table__header-wrapper, .el-table__column, .el-table__row) {
    display: none;
  }
}
</style>
