<template>
  <div class="ltw-page-container">

    <el-card>
      <div class="container">
        <div class="tree-container" style="width: 30%;">
          <el-row style="margin-bottom: 10px;">
            <el-button @click="addRootGroup" size="small">
              <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              {{ '新增' }}
            </el-button>
          </el-row>
          <el-tree :data="treeData" :render-content="renderContent" @node-click="handleNodeClick"
            :expand-on-click-node="false" default-expand-all node-key="id" itemid="">
          </el-tree>
        </div>
        <div class="list-container" style="width: 70%;"> 
          <div class="ltw-tool-container button-group">
            <el-button  :key="item.id" size="small" v-for="item in outlineFunctionList"
              @click="executeButtonMethod(item)">
              <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
              {{ $t(item.name) }}
            </el-button>
          </div>
          <template v-if="pageData.records&&pageData.records.length>0" >
            <el-table :data="pageData.records" stripe border @selection-change="handleSelectionChange" row-key="id"
            ref="tableRef">
            <!-- 列定义 -->
            <el-table-column header-align="center" align="center" type="index" width="55"></el-table-column>
            <el-table-column prop="name" label="检查项名称"></el-table-column>
            <el-table-column prop="checkContent" label="检查内容" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column header-align="center" align="center" :label="$t('操作')" width="160">
              <template #default="scope">
                <el-button-group>
                  <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="$t(item.name)"
                    placement="top" :enterable="false">
                    <el-button :type="item.buttonStyleType" size="mini" @click="executeButtonMethod(item, scope.row)">
                      <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="queryParam.current" :page-sizes="[5, 10, 20, 30]" :page-size="queryParam.size"
            layout="total, sizes, prev, pager, next, jumper" :total="pageData.total">
          </el-pagination>
          </template> 
          <el-empty v-if="!pageData.records||pageData.records.length==0" description="暂无数据"></el-empty>
        </div>
      </div>
    </el-card>

    <el-dialog :title="groupDialogTitle" v-model="groupDialogVisible" width="40%">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item :label="$t('编码')" prop="code">
          <el-input v-model="formData.code" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('检查项分组名')" prop="name">
          <el-input v-model="formData.name" :disabled="formReadonly"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="groupDialogStatus === 'view'">
            <el-button @click="groupDialogVisible = false" v-if="groupDialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
            <el-button :type="item.buttonStyleType" @click="executeButtonMethod(currentButton)"
              v-if="currentButton && currentButton.name">{{ $t(currentButton.name) }}</el-button>
          </template>
          <template v-else>
            <el-button @click="groupDialogVisible = false">{{ $t('取 消') }}</el-button>
            <el-button type="primary" @click="saveGroup">{{ $t('保 存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%">
      <el-form :model="formData" :rules="formRules" ref="formRef2" label-width="100px">
        <el-form-item :label="$t('名称')" prop="name">
          <el-input v-model="formData.name" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('检查内容')" prop="checkContent">
          <el-input v-model="formData.checkContent" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('检查方式')" prop="checkType">
          <el-input v-model="formData.checkType" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('检查周期')" prop="checkPeriod">
          <el-input v-model="formData.checkPeriod" :disabled="formReadonly"></el-input>
        </el-form-item>
        <el-form-item :label="$t('检查工具')" prop="checkTool">
          <el-input v-model="formData.checkTool" :disabled="formReadonly"></el-input>
        </el-form-item>

        <!-- <el-form-item :label="$t('需求方')" prop="demander">
          <el-input v-model="formData.demander" :disabled="formReadonly"></el-input>
        </el-form-item> -->
        <!-- <el-form-item :label="$t('顺序')" prop="sortNum">
          <el-input v-model="formData.sortNum" :disabled="formReadonly"></el-input>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="dialogStatus === 'view'">
            <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'">{{ $t('关 闭') }}</el-button>
            <el-button :type="item.buttonStyleType" @click="executeButtonMethod(currentButton)"
              v-if="currentButton && currentButton.name">{{ $t(currentButton.name) }}</el-button>
          </template>
          <template v-else>
            <el-button @click="dialogVisible = false">{{ $t('取 消') }}</el-button>
            <el-button type="primary" @click="save">{{ $t('保 存') }}</el-button>
          </template>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>


import LtwIcon from '@/components/base/LtwIcon'
import {
  saveFtmCheckGroup,
  updateFtmCheckGroup,
  deleteFtmCheckGroup,
  pageFtmCheckGroup,
  getFtmCheckGroup,
  treeWebCheckGroupList
} from '@/apis/fleet/ftm-check-group'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast } from '@/plugins/util'
import {
  saveFtmCheckItem,
  updateFtmCheckItem,
  deleteFtmCheckItem,
  pageFtmCheckItem,
  getFtmCheckItem
} from '@/apis/fleet/ftm-check-item'

const defaultFormData = {}
export default {
  name: "FtmCheckGroup",
  data () {
    return {
      hoveredNodeId: '',
      showRowButtons: false,
      selecetedNodeId: '',
      nodeKey: "id",
      selectedItems: [],
      treeData: [],
      checked: [], // 保存选中节点的值
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        total: 0
      },
      queryParam: {
        current: 1,
        size: 10,
      },
      formData: Object.assign({}, defaultFormData),
      formRules: {
        code: [
          { required: true, message: this.$t('请输入编码'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('请输入分组名'), trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: this.$t('请输入父id'), trigger: 'blur' }
        ],
        checkContent: [
          { required: true, message: this.$t('请输入检查内容'), trigger: 'blur' }
        ],
        sortNum: [
          { required: true, message: this.$t('请输入顺序'), trigger: 'blur' }
        ]
      },
      groupDialogVisible: false,
      groupDialogTitle: '',
      groupDialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      dialogStatus: '',
      selectedData: [],
      currentButton: {}
    }
  },
  created () {
    if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }
    this.query()
  },
  computed: {
    formReadonly () {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    handleNodeHover (node) {
      this.hoveredNodeId = node ? node.id : null;
    },
    queryItem () {
      if (!this.queryParam.groupId) {
        this.queryParam.groupId = this.selecetedNodeId
      }
      pageFtmCheckItem(this.queryParam).then(
        res => {
          this.pageData = res.data
        }
      )
    },
    add () {
      this.formData = {}
      this.dialogTitle = this.$t('新增')
      this.dialogStatus = 'add'
      this.dialogVisible = true
    },
    view (row) {
      this.dialogTitle = this.$t('查看')
      this.dialogStatus = 'view'
      getFtmCheckItem(row.id).then(
        res => {
          this.$nextTick(function () {
            this.formData = res.data
          })
          this.dialogVisible = true
        }
      )
    },
    save () {
      this.$refs.formRef2.validate(
        valid => {
          if (!valid) return
          this.formData.groupId = this.selecetedNodeId
          if (this.dialogStatus === 'add') {
            saveFtmCheckItem(this.formData).then(
              () => {
                this.dialogVisible = false
                this.queryItem()
              }
            )
          }
          if (this.dialogStatus === 'edit') {
            updateFtmCheckItem(this.formData).then(
              () => {
                this.dialogVisible = false
                this.queryItem()
              }
            )
          }
        }
      )
    },
    edit (row) {
      this.dialogTitle = this.$t('修改')
      this.dialogStatus = 'edit'
      getFtmCheckItem(row.id).then(
        res => {
          this.$nextTick(function () {
            this.formData = res.data
          })
          this.dialogVisible = true
        }
      )
    },
    renderContent (h, { node, data }) {
      return (
        <div style="width:100%;display:flex;flex-direction: row; justify-content: space-between" onmouseenter={() => this.handleNodeHover(data)}
          onmouseleave={() => this.handleNodeHover(null)}>
          <div>
            <el-radio v-model={this.selecetedNodeId} label={data.id}><span></span></el-radio>
          <span></span> {/* 将label部分留空 */}
          <span  >{data.label}</span>
          </div>
          {this.hoveredNodeId === data.id && (
            <div>
              <el-button type="text" size="small" onClick={() => this.handleAdd(data)}>
                <ltw-icon icon-code="el-icon-plus"></ltw-icon>
              </el-button>
              <el-button   type="text" size="small" onClick={() => this.handleEdit(data)}>
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
              <el-button type="text" size="small" onClick={() => this.handleRemove(data)}>
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </div>
          )}

        </div>
      );
    },
    handleEdit (data) {
      this.editGroup(data)
    },
    handleAdd (data) {
      // 处理新增逻辑
      this.formData = {};
      this.formData.parentId = data.id
      this.addGroup()
    },
    handleRemove (data) {
      // 处理新增逻辑
      this.removeGroup(data)
    },
    handleNodeClick (data) {
      if (data.children) return; // 点击的是非叶子节点，不做处理
      this.selecetedNodeId = data.id
      this.queryParam.groupId = data.id
      this.queryItem()
    },
    executeButtonMethod (button, row) {
      this.currentButton = {}
      this[button.buttonCode](row, button)
    },
    refresh () {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query () {
      // pageFtmCheckGroup(this.queryParam).then(
      //   res => {
      //     this.pageData = res.data
      //   }
      // )
      treeWebCheckGroupList(this.queryParam).then(
        res => {
          this.treeData = res.data
        }
      )
    },
    addRootGroup () {
      this.formData = {}
      this.formData.parentId = 'root'
      this.addGroup()
    },
    addGroup () {
      this.groupDialogTitle = this.$t('新增')
      this.groupDialogStatus = 'add'
      this.groupDialogVisible = true
    },
    saveGroup () {
      this.$refs.formRef.validate(
        valid => {
          if (!valid) return
          if (this.groupDialogStatus === 'add') {
            saveFtmCheckGroup(this.formData).then(
              () => {
                this.groupDialogVisible = false
                this.query()
              }
            )
          }
          if (this.groupDialogStatus === 'edit') {
            updateFtmCheckGroup(this.formData).then(
              () => {
                this.groupDialogVisible = false
                this.query()
              }
            )
          }
        }
      )
    },
    editGroup (row) {
      this.groupDialogTitle = this.$t('修改')
      this.groupDialogStatus = 'edit'
      getFtmCheckGroup(row.id).then(
        res => {
          this.$nextTick(function () {
            this.formData = res.data
          })
          this.groupDialogVisible = true
        }
      )
    },
    // view (row) {
    //   this.groupDialogTitle = this.$t('查看')
    //   this.groupDialogStatus = 'view'
    //   getFtmCheckGroup(row.id).then(
    //     res => {
    //       this.$nextTick(function () {
    //         this.formData = res.data
    //       })
    //       this.groupDialogVisible = true
    //     }
    //   )
    // },
    handleCommand (command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove (row) {
      this.remove({ id: row.id, groupId: this.selecetedNodeId })
    },
    batchRemove () {
      let idList = [];
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids: ids, groupId: this.selecetedNodeId })
    },
    removeGroup (data) {
      let param = {
        id: data.id
      }
      showConfirmToast({
        message: data.asLeaf ? BASE_CONSTANT.DELETE_CONFIRM_MSG : '这个操作会永久的删除当前选择的数据和下面的叶子分组，是否继续？'
      }).then(res => {
        deleteFtmCheckGroup(param).then(
          () => {
            this.query()
          }
        )
      })
    },
    remove (param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteFtmCheckItem(param).then(
          () => {
            this.queryItem()
          }
        )
      })
    },
    handleSizeChange (value) {
      this.queryParam.size = value
      this.queryItem()
    },
    handleCurrentChange (value) {
      this.queryParam.current = value
      this.queryItem()
    },
    dialogOpened () {

    },
    dialogClosed () {
      this.initForm()
    },
    handleSelectionChange (value) {
      this.selectedData = value
    },
    initForm () {
      this.$refs.formRef.resetFields()
      this.formData = Object.assign({}, defaultFormData)
    }

  }
}
</script>

<style scoped > .container {
   display: flex;
 }

 .tree-container {
   flex: 2;
   padding: 20px;
 }

 .list-container {
   flex: 3;
   padding: 20px; 
 }

 .rightButtons {
   float: right;
 }

 .el-radio__label {
   display: none;
 } 
</style>
