<template>
  <div v-if="data.length > 0">
    <div v-for="item in data" :key="item.id">
      <bs-tag-group-card
        :tag-group="item"
        :time-edit="editTags"
        :continuous-units="continuousUnits"
        ref="bsTagGroupCardRef"
        @tag-click="handleTagClick"
        :classificationTag="true"
        :attributes="attributes"
      ></bs-tag-group-card>
    </div>
  </div>
  <el-empty v-else description="暂无选择标签"></el-empty>
</template>

<script>
import bsTagGroupCard from '@/components/dataCollect/BsTagGroupCard.vue'
import TAG_ATTRIBUTES from '@/plugins/constants/tag-attributes.js'

export default {
  components: { bsTagGroupCard },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    editTags: {
      type: Boolean,
      default: false
    },
    continuousUnits: {
      type: Array,
      default: () => []
    }
  },
  // watch: {
  //   data: {
  //     handler(newval, old) {
  //       this.kpiData = newval
  //     },
  //     immediate: true,
  //     deep: true
  //   }
  // },
  data() {
    return {
      // kpiData: '',
      attributes: [
        TAG_ATTRIBUTES.SUPPORT_TRIGGER,
        TAG_ATTRIBUTES.SUPPORT_VOICE,
        TAG_ATTRIBUTES.FOLLOWING_DURATION,
        TAG_ATTRIBUTES.PREVIOUS_DURATION
      ]
    }
  },
  methods: {
    // checkGroupVisible(group) {
    //   if (!(group.children?.length || group.tagList?.length)) {
    //     return false
    //   }
    //   return true
    // },
    handleTagClick(param) {
      this.$emit('tag-click', param)
    }
  }
}
</script>
