<template>
  <el-dialog
    class="search-position-dialog"
    v-model="dialogVisible"
    width="800px"
    :show-close="false"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
  >
    <template #header>
      <el-button id="cancel" circle @click="cancel">
        <ltw-icon icon-code="el-icon-close"></ltw-icon>
      </el-button>
    </template>
    <div class="map-form">
      <div id="r-result">
        <el-input
          v-if="!formReadonly"
          placeholder="请输入内容"
          id="suggestId"
          v-model="searchValue"
          clearable
        >
          <template #append>
            <el-button @click="searchFunc">
              <ltw-icon icon-code="el-icon-search"></ltw-icon> </el-button
          ></template>
        </el-input>
      </div>
      <div
        id="searchResultPanel"
        style="
          border: 1px solid #c0c0c0;
          width: 150px;
          height: auto;
          display: none;
        "
      ></div>
    </div>
    <div id="allmap" ref="allmap"></div>
  </el-dialog>
</template>

<script>
import { showToast, getUuid } from '@/plugins/util'
import { h, render } from 'vue'
import { BaiduMap } from '@/plugins/map/map'
import AddressMapWindow from '@/components/map/AddressMapWindow'

export default {
  name: 'MapSearchPosition',
  data() {
    return {
      dialogVisible: false,
      map: '',
      searchValue: '',
      searchKey: {
        value: '苏州博士汽车配件有限公司',
        range: '苏州',
        latitude: '',
        longitude: ''
      },
      formReadonly: false,
      mySquareList: []
    }
  },
  components: {
    // VehicleCard
    // 'v-fleet-detail-screen': FleetDetailScreen
  },
  emits: ['changePosition', 'reload'],
  mounted() {},
  methods: {
    show(data) {
      this.dialogVisible = true
      this.formReadonly = data.formReadonly
      // this.total = data.total
      // this.working = data.working
      // this.free = data.free
      // this.distributed = data.distributed
      // this.list = data.data
      // this.$nextTick(() => {
      BaiduMap.init().then(BMap => {
        // window.BMap = BMap;
        this.map = new BMap.Map(this.$refs.allmap, { enableMapClick: false }) // 创建Map实例
        this.map.centerAndZoom(
          new BMap.Point(120.59168154789141, 31.305976454846554),
          11
        ) // 初始化地图,设置中心点坐标和地图级别
        if (data?.disabledScrollWheelZoom) {
          this.map.disableScrollWheelZoom()
          //平移缩放控件
          this.map.addControl(new BMap.NavigationControl())
        } else {
          this.map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
        }
        this.map.disableDoubleClickZoom()
        if (!this.formReadonly) {
          var ac = new BMap.Autocomplete({
            // 建立一个自动完成的对象
            input: 'suggestId',
            location: this.map
          })
        }
        if (data.lng && data.lat) {
          this.getPosition(data)
        } else if (data.address) {
          this.setPlace(data.address)
        } else {
          this.mapInit(ac)
        }
      })
      // });
    },

    mapInit(ac) {
      let _this = this
      // BaiduMap.init().then(BMap => {
      // let range
      // // if (_this.searchKey.range !== '市辖区' && _this.searchKey.range !== '县') {
      // //   range = _this.searchKey.range
      // // } else {
      // range = ''
      // // }
      // _this.map = new BMap.Map(this.$refs.allmap, { enableMapClick: false }) // 创建Map实例
      //   _this.map.centerAndZoom(range || new BMap.Point(116.404, 39.915), 11) // 初始化地图,设置中心点坐标和地图级别
      // map.setCurrentCity('北京') // 设置地图显示的城市 此项是必须设置的
      // _this.map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
      // _this.map.disableDoubleClickZoom() // 禁止双击切换缩放

      // if (this.searchKey.value) {
      //   // var myValue = this.searchKey.value || ''
      //   this.searchValue = this.searchKey.value
      //   // 偶然情况下v-model赋值失败使用原生进行兼容
      //   // document.getElementById('suggestId').value = _this.searchKey.value
      // }
      var myValue = this.searchKey.value || ''

      // ac.addEventListener('onhighlight', function (e) {
      //   // 鼠标放在下拉列表上的事件
      //   var str = ''
      //   var _value = e.fromitem.value
      //   var value = ''
      //   if (e.fromitem.index > -1) {
      //     value =
      //       _value.province +
      //       _value.city +
      //       _value.district +
      //       _value.street +
      //       _value.business
      //   }
      //   str =
      //     'FromItem<br />index = ' + e.fromitem.index + '<br />value = ' + value

      //   value = ''
      //   if (e.toitem.index > -1) {
      //     _value = e.toitem.value
      //     value =
      //       _value.province +
      //       _value.city +
      //       _value.district +
      //       _value.street +
      //       _value.business
      //   }
      //   str +=
      //     '<br />ToItem<br />index = ' +
      //     e.toitem.index +
      //     '<br />value = ' +
      //     value
      //   _this.G('searchResultPanel').innerHTML = str
      //   // })

      ac.addEventListener('onconfirm', function (e) {
        // 鼠标点击下拉列表后的事件
        var _value = e.item.value
        myValue =
          _value.province +
          _value.city +
          _value.district +
          _value.street +
          _value.business
        _this.G('searchResultPanel').innerHTML =
          'onconfirm<br />index = ' +
          e.item.index +
          '<br />myValue = ' +
          myValue
        myValue && _this.setPlace(myValue)
      })
      //   if (_this.searchKey.lat && _this.searchKey.lng) {
      //     let myIcon = new BMap.Icon(
      //       require('@/assets/images/mapScreen/map-position.png'),
      //       new BMap.Size(26, 36)
      //     )
      //     let point = new BMap.Point(_this.searchKey.lng, _this.searchKey.lat)
      //     // 创建标注，为要查询的地方对应的经纬度
      //     let marker = new BMap.Marker(point, { icon: myIcon }) // 自定义标记
      //     // alert("["+poi.point.lng+","+poi.point.lat+"]");
      //     // 在地图上添加标识
      //     _this.map.addOverlay(marker)
      //     _this.map.centerAndZoom(point, 18)
      //     marker.setAnimation(BMAP_ANIMATION_DROP) // 跳动的动画BMAP_ANIMATION_DROP BMAP_ANIMATION_BOUNCE
      //     // _this.drawOverlay(marker, poi)
      //   } else {
      //     myValue && _this.setPlace(myValue)
      //   }
      //   // _this.initSearch(_this.searchKey)
      // })
    },
    setPlace(myValue) {
      let _this = this
      function myFun() {
        var pp = _this.local.getResults().getPoi(0).point // 获取第一个智能搜索的结果
        _this.map.centerAndZoom(pp, 18)
        _this.map.addOverlay(new BMap.Marker(pp)) // 添加标注
      }

      this.local = new BMap.LocalSearch(_this.map, {
        // 智能搜索
        onSearchComplete: myFun
      })
      // let address = {}
      let searchResult = ''

      this.local.setSearchCompleteCallback(function (searchResults) {
        _this.map.clearOverlays() // 清除地图上所有覆盖物
        if (typeof searchResults === 'undefined') {
          showToast('百度API没有搜索到该地址', 'error')
          // alert('百度API没有搜索到该地址')
          return
        }
        if (searchResults.length > 0) {
          searchResult = searchResults[0]
        } else {
          searchResult = searchResults
        }

        // var contents = []
        // let firstPoint = {}
        // if (!searchResult.getCurrentNumPois()) {
        //   showToast('百度API没有搜索到该地址', 'warning')
        //   // _this.map.centerAndZoom(new BMap.Point(116.404, 39.915), 11) // 初始化地图,设置中心点坐标和地图级别
        // } else {
        let searchResultLength = searchResult.getCurrentNumPois()
        for (var i = 0; i < searchResultLength; i++) {
          // 获得需要得到的坐标
          var poi = searchResult.getPoi(i)
          if (!poi) {
            showToast('百度API没有搜索到该地址', 'error')
          }

          _this.drawMarker(poi, i, searchResultLength)
          // // 点击标识后显示的内容
          // contents[i] = '您选择的地点是:【' + poi.title + '】地址：' + poi.address// +"<br/>经度："+poi.point.lng+"<br/>纬度："+poi.point.lat;
          //
          // marker.setTitle(contents[i])
          // // 添加点击事件监听
          // marker.addEventListener('click', makerClick)
          // let infoWindow
          // if (i === 0) {
          //   // 设置中心点
          //   _this.map.centerAndZoom(poi.point, 13)
          //   infoWindow = new BMap.InfoWindow('<p style=\'font-size:14px;\'>' + contents[0] + '</p>')
          //   marker.openInfoWindow(infoWindow)
          //   address.lng = poi.point.lng
          //   address.lat = poi.point.lat
          //   infoWindow.addEventListener('close', function (type) {
          //     address = {}
          //   })
          // }
          // if ((_this.searchKey.latitude === poi.point.lat) && (_this.searchKey.longitude === poi.point.lng)) {
          //   // 设置中心点
          //   _this.map.centerAndZoom(poi.point, 13)
          //   infoWindow = new BMap.InfoWindow('<p style=\'font-size:14px;\'>' + contents[0] + '</p>')
          //   marker.openInfoWindow(infoWindow)
          //   address.lng = poi.point.lng
          //   address.lat = poi.point.lat
          //   infoWindow.addEventListener('close', function (type) {
          //     address = {}
          //   })
          // }
        }
        // }
      })
      this.local.search(myValue)
      // this.local.search(myValue)
      // var makerClick = function (e) {
      //   var infoWindow = new BMap.InfoWindow('<p style=\'font-size:14px;\'>' + this.getTitle() + '</p>')
      //   this.openInfoWindow(infoWindow)
      //   address.lng = e.target.point.lng
      //   address.lat = e.target.point.lat
      //   // infoWindow.close(function (type, target) {
      //   // })
      //   infoWindow.addEventListener('close', function (type) {
      //     address = {}
      //   })
      // }

      // $('#search-button').click(function () {
      //   var keys = document.getElementById('suggestId').value
      //   local.search(keys)
      // })
    },
    drawMarker(poi, i, iLen) {
      let myIcon = new BMap.Icon(
        require('@/assets/images/mapScreen/map-position.png'),
        new BMap.Size(26, 36)
      )
      let point = new BMap.Point(poi.point.lng, poi.point.lat)
      // this.map.centerAndZoom(point, 16)
      // 创建标注，为要查询的地方对应的经纬度
      let marker = new BMap.Marker(point, { icon: myIcon }) // 自定义标记
      // alert("["+poi.point.lng+","+poi.point.lat+"]");
      // 在地图上添加标识
      this.map.addOverlay(marker)
      marker.setAnimation(BMAP_ANIMATION_DROP) // 跳动的动画BMAP_ANIMATION_DROP BMAP_ANIMATION_BOUNCE
      let resultAddress = {
        lng: poi.point.lng,
        lat: poi.point.lat,
        name: poi.title,
        address: poi.address,
        uid: poi.uid
      }
      if (!i) {
        // firstPoint = new BMap.Point(val.longitude, val.latitude);
        let firstPoint = {
          longitude: poi.point.lng,
          latitude: poi.point.lat
        }
        this.drawOverlay(marker, resultAddress, iLen, firstPoint)
      } else {
        this.drawOverlay(marker, resultAddress, iLen)
      }
    },
    // 配置点位信息
    drawOverlay(marker, val, iLen, firstPoint) {
      let _this = this

      function SquareOverlay(center, width, height, color) {
        this._center = center
        this._width = width
        this._height = height
        this._color = color
      }

      // 继承API的BMap.Overlay
      SquareOverlay.prototype = new BMap.Overlay()

      SquareOverlay.prototype.initialize = function (map) {
        // 保存map对象实例
        this._map = map
        // 创建div元素，作为自定义覆盖物的容器
        let div = document.createElement('div')
        div.style.position = 'absolute'
        div.id = val.uid
        // div.entType = val.entType
        // 可以根据参数设置元素外观
        // div.style.width = this._length + "px";
        // div.style.height = this._length + "px";
        // div.style.background = this._color;
        // div.onclick=function(e){
        //     this.style.display = "none"
        // }
        // 将div添加到覆盖物容器中
        map.getPanes().markerPane.appendChild(div)
        // 保存div实例
        this._div = div
        // 需要将div元素作为方法的返回值，当调用该覆盖物的show、
        // hide方法，或者对覆盖物进行移除时，API都将操作此元素。
        return div
      }
      SquareOverlay.prototype.draw = function () {
        // 根据地理坐标转换为像素坐标，并设置给容器
        let position = this._map.pointToOverlayPixel(this._center)
        this._div.style.left = position.x + 'px'
        this._div.style.top = position.y + 'px'
      }

      // 实现显示方法
      SquareOverlay.prototype.show = function () {
        if (this._div) {
          this._div.style.display = ''
        }
      }
      // 实现隐藏方法
      SquareOverlay.prototype.hide = function () {
        if (this._div) {
          this._div.style.display = 'none'
        }
      }
      // 添加自定义方法
      SquareOverlay.prototype.toggle = function () {
        if (this._div) {
          if (this._div.style.display === '') {
            this.hide()
          } else {
            this.show()
          }
        }
      }
      let point = new BMap.Point(val.lng, val.lat)
      // 添加自定义覆盖物
      let mySquare = new SquareOverlay(point, 350, 163)
      _this.map.addOverlay(mySquare)
      _this.mySquareList.push(mySquare)
      mySquare.hide()
      let id = mySquare._div.id
      let node = document.getElementById('my-node')
      if (!node) {
        node = document.createElement('div', { id: 'my-node' })
      }
      let parentNode = document.getElementById(id)
      marker.addEventListener('click', e => {
        if (parentNode.children && parentNode.children.length) {
          // mySquare.toggle()
          // 关闭弹出层逻辑
          _this.mySquareList.forEach(mySquareItem => {
            if (mySquareItem._div.id === id) {
              if (mySquareItem._div.style.display === 'none') {
                _this.map.panTo(point)
                mySquareItem.show()
              } else {
                mySquareItem.hide()
              }
            } else {
              mySquareItem.hide()
            }
          })
        } else {
          _this.mySquareList.forEach(mySquareItem => {
            if (mySquareItem._div.id === id) {
              if (mySquareItem._div.style.display === 'none') {
                _this.map.panTo(point)
                mySquareItem.show()
              } else {
                mySquareItem.hide()
              }
            } else {
              mySquareItem.hide()
            }
          })
          let instance = h(AddressMapWindow, {
            item: val,
            formReadonly: _this.formReadonly,
            onReload: reloadVal => {
              _this.saveAddress(reloadVal)
            }
          })
          instance.appContext = app._context
          render(instance, node)
          mySquare._div.appendChild(node)
          // mySquare.show()
          // vm = createApp({
          //   template: `<div class="map-address">
          //   <div class="map-form-item">
          //       名称：{{poi.title}}
          //     </div>
          //     <div class="map-form-item">
          //       地址：{{poi.address}}
          //     </div>
          //     <div class="map-form-item">
          //       经度: {{poi.point.lng}}
          //     </div>
          //     <div class="map-form-item">
          //       纬度: {{poi.point.lat}}
          //     </div>
          //     <div class="map-form-item">
          //       <el-button type="primary" @click="saveAddress()">保存</el-button>
          //       <el-button @click="closeInfo">取消</el-button>
          //     </div>
          //   </div>`,
          //   data() {
          //     return {
          //       poi: val
          //     }
          //   },
          //   methods: {
          //     closeInfo: function () {
          //       mySquare.hide()
          //     },
          //     saveAddress() {
          //       _this.saveAddress({
          //         lng: this.poi.point.lng,
          //         lat: this.poi.point.lat,
          //         address: this.poi.address
          //       })
          //     }
          //   }
          // })
          // vm.use(ElButton)
          // vm.mount(node)
          // mySquare._div.appendChild(node)
        }
      })
      if (firstPoint) {
        let point = new BMap.Point(firstPoint.longitude, firstPoint.latitude)
        this.map.centerAndZoom(point, 18)
        if (iLen <= 1) {
          mySquare.show()
          let instance = h(AddressMapWindow, {
            item: val,
            formReadonly: _this.formReadonly,
            onReload: reloadVal => {
              _this.saveAddress(reloadVal)
            }
          })
          instance.appContext = app._context
          render(instance, node)
          mySquare._div.appendChild(node)
        }
      }
    },
    // focusPosition(id) {
    //   this.list.forEach(val => {
    //     if (val.id === id) {
    //       let point = new BMap.Point(val.longitude, val.latitude)
    //       this.map.centerAndZoom(point, 16)
    //     }
    //   })
    // },
    G(id) {
      return document.getElementById(id)
    },
    searchFunc() {
      if (this.searchValue) {
        this.setPlace(this.searchValue)
      } else {
        showToast('请输入地址内容', 'warning')
      }
    },
    saveAddress(obj) {
      this.$emit('changePosition', obj)
      this.cancel()
    },
    dialogOpened() {},
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    initForm() {
      this.searchValue = ''
    },
    getPosition(obj) {
      this.drawMarker(
        {
          point: {
            lng: obj.lng,
            lat: obj.lat
          },
          title: obj.name,
          address: obj.address,
          uid: getUuid()
        },
        0,
        1
      )
      // let myIcon = new BMap.Icon(
      //   require('@/assets/images/mapScreen/map-position.png'),
      //   new BMap.Size(26, 36)
      // )
      // let point = new BMap.Point(obj.lng, obj.lat)
      // this.map.centerAndZoom(point, 16)
      // // 创建标注，为要查询的地方对应的经纬度
      // let marker = new BMap.Marker(point, { icon: myIcon }) // 自定义标记
      // let firstPoint = {
      //   longitude: obj.lng,
      //   latitude: obj.lat
      // }
      // this.drawOverlay(marker, obj, firstPoint)
      // this.map.addOverlay(marker)
      // marker.setAnimation(BMAP_ANIMATION_DROP) // 跳动的动画BMAP_ANIMATION_DROP BMAP_ANIMATION_BOUNCE
    }
  }
}
</script>
<style lang="scss">
.search-position-dialog {
  .el-dialog__header {
    position: relative;
    padding: 0;
    margin-bottom: 0;
    .el-button {
      position: absolute;
      right: -32px;
      top: -16px;
      z-index: 1;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>

<style lang="scss" scoped>
.map-form {
  position: absolute;
  width: 100%;
  z-index: 1;
  top: 5%;
  #r-result {
    width: 60%;
    margin: 0 auto;
  }
}
// }

#allmap {
  height: 500px;
  :deep(.anchorBL) {
    display: none;
  }
}
</style>
