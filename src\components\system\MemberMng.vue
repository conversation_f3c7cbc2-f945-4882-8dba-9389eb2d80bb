<template>
    <el-dialog title="成员管理" v-model="visible" width="85%" @close="dialogClosed" @open="dialogOpened">
        <div>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container" >
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable
                              @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        @click="add"
                        type="primary"
                    >
                    <el-icon>
                            <plus/>
                        </el-icon>
                        {{ $t('新增') }}
                    </el-button>  
                         <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 >
                        <el-button type="primary">
                            {{ $t('批量操作')}}
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="batchRemove">
                                    <el-icon>
                                        <delete/>
                                    </el-icon>
                                    {{ $t('批量删除')}} 
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>

            </div>
        </div>
        <el-table :data="pageData.records" border stripe
                  @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
            <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
            <el-table-column header-align="left" align="left" prop="name" label="姓名"></el-table-column>
            <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
            <el-table-column header-align="left" align="left" label="机构">
                <template #default="scope">
                    <template v-if="scope.row.roleListMap">
                        <el-tag type="success" v-for="item in scope.row.roleListMap['org']"
                                :key="item.id">
                            {{item.name}}
                        </el-tag>
                    </template>
                </template>
            </el-table-column>
            <!--                        <el-table-column header-align="left" align="left" prop="mobilePhoneNum" label="手机号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="telephoneNum" label="座机号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="email" label="电子邮箱"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="birthday" label="出生日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="politicsStatus" label="政治面貌"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="maritalStatus" label="婚姻状况"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="identityCardNum" label="身份证号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="educationDegree" label="员工获得的最高学历"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="nationCode" label="民族"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="genderCode" label="性别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="statusCode" label="员工状态"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="residencePropertyCode" label="户口性质"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="residenceLocation" label="户口所在地"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="address" label="居住地址"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="hireDate" label="入职日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="nativePlaceCantonCode" label="籍贯"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="employmentTypeCode" label="用工制度"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="directSuperiorId" label="直属上级"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="resume" label="个人履历"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="partyJoinedDate" label="入党日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="fertilityStatusCode" label="生育情况"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="mainLanguages" label="主要语种"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="workDate" label="首次参加工作日期"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="graduatedSchool" label="毕业院校"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="schoolSystem" label="毕业学校的类型 全日制，非全日制"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="educationForm" label="员工获得教育的教学形式"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="majorType" label="员工所学专业所属类别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="degreeType" label="员工获得的学位类型"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="facultyName" label="专业教育或高等教育所在系别"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="majorName" label="所学专业"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="credentialType" label="证件类型"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="credentialCode" label="证件号码"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="sortNum" label="顺序"></el-table-column>-->
            <!--                        <el-table-column header-align="left" align="left" prop="tenantId" label="租户id"></el-table-column>-->
            <el-table-column header-align="left" align="left" label="操作" width="180">
                <template #default="scope">
                    <el-button-group>
                        <el-tooltip effect="dark" content="删除" placement="top" :enterable="false"
                        >
                        <el-button type="danger" 
                                       @click="singleRemove(scope.row)">
                                <el-icon>
                                    <delete/>
                                </el-icon>
                            </el-button>
                            <!-- <el-button type="danger" icon="el-icon-delete"
                                       @click="singleRemove(scope.row)"></el-button> -->
                        </el-tooltip>
                    </el-button-group>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryParam.current"
                :page-sizes="[5, 10, 20, 30]"
                :page-size="queryParam.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageData.total">
        </el-pagination>

        <member-add v-model="memberAddVisible" :add-query-param="memberAddQueryParam" @confirm="handleConfirmAddMember" ref="memberAddRef"></member-add>

        <template #footer>
        <span class="dialog-footer">
            <el-button @click="cancel">关闭</el-button>
        </span>
        </template>
    </el-dialog>
</template>

<script>
    import {
        pageSysRoleEmployee
    } from '@/apis/system/sys-role-employee'
    import MemberAdd from "@/components/system/MemberAdd";
    const DEFAULT_QUERY_PARAM = {
        current: 1,
        size: 10
    }
    export default {
        name: "MemberMng",
        components: {MemberAdd},
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],

                pageData: {
                    total:0
                },
                memberAddVisible:false,
                queryParam: DEFAULT_QUERY_PARAM,
                selectedData:[]
            }
        },
        props: {
            modelValue: Boolean,
            data: Array,
            memberAddQueryParam:Object,
            memberMngQueryParam:Object
        },
        computed:{
            visible:{
                                get(){
                    return this.modelValue
                },
                set(val){
                    this.$emit('update:modelValue', val)
                }            }
        },
        emits: ['open', 'close', 'update:modelValue', 'size-change', 'current-change','add','singleRemove','batchRemove'],
        methods: {
            reload(reloadAdd){
                this.queryParam = Object.assign(DEFAULT_QUERY_PARAM,this.memberMngQueryParam)
                this.refresh()
                if(reloadAdd === true){
                    this.$refs.memberAddRef.reload()
                }
            },
            refresh() {
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysRoleEmployee(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            handleSelectionChange(value) {
                this.selectedData = value
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
                this.$emit('size-change', value)
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
                this.$emit('current-change', value)
            },
            dialogClosed() {
                this.$refs.tableRef.clearSelection()
                this.queryParam = DEFAULT_QUERY_PARAM
                this.pageData = {total:0}
                this.$emit('close')
                this.$emit('update:modelValue', false)
            },
            dialogOpened() {
                this.queryParam = Object.assign(this.queryParam,this.memberMngQueryParam)
                this.query()
                this.$emit('open')
                this.$emit('update:modelValue', true)
            },
            cancel() {
                this.dialogClosed()
            },
            handleCommand(command) {
                if (this.selectedData.length === 0) {
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if (command === 'batchRemove') {
                    this.batchRemove()
                }
            },
            add() {
                this.memberAddVisible = true
            },
            handleConfirmAddMember(value){
                this.$emit('add', value)
            },
            singleRemove(row){
                this.$emit('singleRemove',row)
            },
            batchRemove(){
                this.$emit('batchRemove',this.selectedData)
            }
        }
    }
</script>

