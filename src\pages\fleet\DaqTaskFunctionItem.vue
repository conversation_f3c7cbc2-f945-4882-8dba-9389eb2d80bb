<template>
  <div>
    <el-page-header class="page-header" @back="onBack">
      <!-- Line 1 -->
      <template #icon>
        <ltw-icon icon-code="el-icon-arrow-left"></ltw-icon>
      </template>
      <template #breadcrumb/>
      <!-- Line 2 -->
      <!-- <template #icon /> -->
      <template #title>{{ $t('返回') }}</template>
      <!-- <template #extra /> -->
      <!-- Lines after 2 -->
      <!-- <template #default /> -->
    </el-page-header>
    <el-card>
<!--      <el-tabs v-model="queryParam.bisType" @tab-click="handleTabClick">-->
<!--        <el-tab-pane v-for="item in bisTypeList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>-->
<!--      </el-tabs>-->
      <div class="ltw-toolbar">
        <div class="ltw-tool-container button-group">
          <el-button
              type="primary" @click="add">
            <ltw-icon icon-code="el-icon-plus"></ltw-icon>
            新增配置组
          </el-button>
        </div>
      </div>
      <div class="content">
        <draggable
            :list="groupList"
            tag="div"
            :component-data="{ name: 'fade' }"
            item-key="id"
            @change="handleGroupChange"
            ghost-class="ghost"
            group="tag-group"
            filter=".el-card__body,.el-dropdown,.disable-dragging"
        >
          <template #item="{ element }">
            <daq-task-function-group
                :tag-group="element"
                @command="handleTagGroupCommand"
                ref="bsTagGroupRef"
                @update-children="handleUpdateChildren($event, element)"
            ></daq-task-function-group>
          </template>
        </draggable>
      </div>
      <daq-task-function-group-dialog
          ref="bsTagGroupDialogRef"
          @save="handleTagGroupSave"
          class="bs-tag-group-dialog"
      ></daq-task-function-group-dialog>
      <daq-task-function-item ref="functionItemDialog" @save="handleTagSave" @remove="handleTagRemove"
                              class="bs-tag-dialog"></daq-task-function-item>
    </el-card>
  </div>
</template>

<script>
import {
  deleteDaqTaskFunctionGroup,
  treeDaqTaskFunctionGroup,
  reorderFunctionGroup
} from '@/apis/fleet/daq-task-function-group'
import draggable from 'vuedraggable'
import DaqTaskFunctionItem from '@/pages/fleet/DaqTaskFunctionItemDialog.vue'
import DaqTaskFunctionGroup from "@/pages/fleet/DaqTaskFunctionGroup.vue";
import DaqTaskFunctionItemDialog from "@/pages/fleet/DaqTaskFunctionItemDialog.vue";
import DaqTaskFunctionGroupDialog from "@/pages/fleet/DaqTaskFunctionGroupDialog.vue";
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import {
  getDaqTaskFunctionItem
} from '@/apis/fleet/daq-task-function-item'

export default {
  name: 'BsTag',
  components: {
    DaqTaskFunctionGroup,
    DaqTaskFunctionItemDialog,
    DaqTaskFunctionGroupDialog,
    DaqTaskFunctionItem,
    draggable
  },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      dataOperatePermission: {},
      permission: {},
      queryParam: {
        current: 1,
        size: 10
      },
      groupList: [],
      currentOperateGroup: {},
      currentOperateTag: undefined,
      // bisTypeList: []
    }
  },
  created() {
    /*if (this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]) {
      this.batchingFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
      this.inlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
      this.outlineFunctionList =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
      this.dataOperatePermission =
          this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].dataOperatePermission
    }*/
    this.queryParam.functionCode = this.$route.query.functionCode
    treeDaqTaskFunctionGroup(this.queryParam).then(res => {
      this.groupList = res.data
    })
  },
  computed: {},
  methods: {
    onBack() {
      this.$router.go(-1)
    },

    // listBisType() {
    //   listSysDictionary({typeCode: 'tag_bis_type'}).then(res => {
    //     this.bisTypeList = res.data
    //     this.queryParam.bisType = res.data[0].code
    //     this.query()
    //   })
    // },
    handleTabClick(tab, e) {
      this.$nextTick(() => {
        this.refresh()
      })
    },
    refresh() {
      this.queryParam.current = 1
      this.query()
    },
    query() {
      treeDaqTaskFunctionGroup(this.queryParam).then(res => {
        this.groupList = res.data
      })
    },
    add() {
      let defaultData = {}
      let groupList = this.groupList;
      if (groupList && groupList.length > 0) {

        defaultData.sortNum = groupList[groupList.length - 1].sortNum + BASE_CONSTANT.SORT_NUM_STEP

      }
      defaultData.functionCode = this.queryParam.functionCode
      this.$refs.bsTagGroupDialogRef.add(defaultData)
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    edit(row) {
      let top =  false
      if (!row.parentId){
        top=true
      }
      this.$refs.bsTagGroupDialogRef.edit(row.id,top)
    },
    view(row) {
      this.$refs.bsTagGroupDialogRef.view(row.id)
    },
    singleRemove(row) {
      this.remove(row)
    },
    remove(row) {
      let msg =
          '该操作会永久删除数据，确定继续嘛?'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            deleteDaqTaskFunctionGroup({id: row.id}).then(() => {
              this.query()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '取消删除'
            })
          })
    },
    handleTagGroupCommand(command, row) {
      this.executeButtonMethod(command, row)
    },
    addSubGroup(row) {

      let defaultData = {}
      defaultData.parentId = row.id
      if (row.children && row.children.length > 0) {
        defaultData.sortNum =
            row.children[row.children.length - 1].sortNum +
            BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.functionCode = this.queryParam.functionCode
      this.$refs.bsTagGroupDialogRef.addSubGroup(defaultData)
    },
    handleTagGroupSave() {
      this.query()
    },
    addTag(row) {

      this.currentOperateTag = undefined
      this.currentOperateGroup = row
      let defaultData = {groupId: row.id}
      if (
          this.currentOperateGroup.items &&
          this.currentOperateGroup.items.length > 0
      ) {
        defaultData.sortNum =
            this.currentOperateGroup.items[
            this.currentOperateGroup.items.length - 1
                ].sortNum +
            BASE_CONSTANT.SORT_NUM_STEP
      }
      defaultData.functionCode= row.functionCode
      this.$refs.functionItemDialog.add(defaultData, row.items)
    },
    viewTag(row) { 
      const {tag, group} = row
      this.currentOperateTag = tag
      this.currentOperateGroup = group
      let data = Object.assign({}, tag)
      getDaqTaskFunctionItem(data.id).then(res => {
        this.$refs.functionItemDialog.view(res.data, group.items)
      })
    },
    handleTagRemove(tag) {
      const index = this.currentOperateGroup.items.findIndex(item => item.id === tag.id);
      if (index !== -1) {
        this.currentOperateGroup.items.splice(index, 1);
      }
    },
    handleTagSave(tag) {
      if (this.currentOperateTag) {
        Object.assign(this.currentOperateTag, tag)
      } else {
        if (!this.currentOperateGroup.items) {
          this.currentOperateGroup.items = []
        }
        this.currentOperateGroup.items.push(tag)
      }
      this.currentOperateTag = undefined
    },
    handleGroupChange(e) {
      if (e.moved) {
        this.handleGroupMove(e.moved)
      }
      if (e.added) {
        this.handleGroupAdd(e.added)
      }
    },
    handleGroupMove(moved) {
      let element = moved.element
      let preSortNum
      let nextSortNum
      let newIndex = moved.newIndex
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortSubFunctionGroup(element, preSortNum, nextSortNum)
    },
    handleGroupAdd(added) {
      let element = added.element
      let preSortNum
      let nextSortNum
      let newIndex = added.newIndex
      let newParentId = ''
      if (newIndex === 0) {
        preSortNum = undefined
      } else {
        preSortNum = this.groupList[newIndex - 1].sortNum
        newParentId = this.groupList[newIndex - 1].parentId
      }
      if (newIndex === this.groupList.length - 1) {
        nextSortNum = undefined
      } else {
        newParentId = this.groupList[newIndex + 1].parentId
        nextSortNum = this.groupList[newIndex + 1].sortNum
      }
      this.sortSubFunctionGroup(element, preSortNum, nextSortNum, newParentId)
    },
    sortSubFunctionGroup(element, preSortNum, nextSortNum, newParentId) {
      reorderFunctionGroup({
        id: element.id,
        preSortNum,
        nextSortNum,
        newParentId
      }).then(res => {
        let newSortNum = res.data
        if (!nextSortNum) {
          element.sortNum = newSortNum
        } else {
          if (!preSortNum) {
            preSortNum = 0
          }
          if (preSortNum < newSortNum && newSortNum < nextSortNum) {
            element.sortNum = newSortNum
          }
          // else {
          //   this.querySubGroup(element.parentId)
          // }
        }
      })
    },
    handleUpdateChildren(children, element) {
      element.children = children
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;

  .ghost {
    background-color: rgba(241, 239, 239, 0.8);
  }
}

.tag-group-dropdown-menu {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
  }
}

.fade-move {
  transition: transform 0.5s;
}

.page-header {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-light);

  :deep(.el-page-header__breadcrumb) {
    margin: 0;
  }

  :deep(.el-page-header__content) {
    font-weight: 600;
  }
}

</style>
