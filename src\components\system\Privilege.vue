<template>
    <el-card>
        <template #header>
            <el-row :gutter="20">
                <el-col :span="4" class="title-text-container">
                    <div>权限</div>
                </el-col>
                <el-col :span="2" class="title-text-container">
                </el-col>
                <el-col :span="12" v-if="roleSelected">
                    <ltw-input placeholder="请输入名称" v-model="queryParam.name" clearable
                              @clear="getCurrentActorApplications">
                        <template #append>
                            <el-button icon="el-icon-search" @click="getCurrentActorApplications"></el-button>
                        </template>
                    </ltw-input>
                </el-col>
                <el-col :span="6" v-if="roleSelected">
                    <el-button type="primary" @click="showAssignDialog">应用分配</el-button>
                </el-col>
            </el-row>
        </template>
        <el-table
                :data="currentActorApplications"
                @expand-change="handleExpandChange" row-key="id" ref="tableRef"
        >
            <el-table-column header-align="left" align="left" type="expand" >
                <template #default="scope">
                    <el-row :gutter="10">
                        <el-col :span="18">
                            <el-tree
                                    node-key="id"
                                    :props="menuTreeProps"
                                    :data="scope.row.menuTree"
                                    :default-checked-keys="scope.row.assignedMenus"
                                    show-checkbox
                                    ref="menuTree"
                            >
                            </el-tree>
                        </el-col>
                        <el-col :span="6" style="display: flex;justify-content: flex-end;align-items: flex-end"
                                v-if="scope.row.menuTree && scope.row.menuTree.length>0">
                            <el-button type="primary" @click="menuAssign(scope.row)">保存</el-button>
                        </el-col>
                    </el-row>

                </template>
            </el-table-column>
            <el-table-column header-align="left" align="left" prop="name" label="名称"></el-table-column>
            <el-table-column header-align="left" align="left" prop="code" label="编码"></el-table-column>
            <el-table-column header-align="left" align="left" prop="icon" label="图标">
                <template #default="scope">
                    <div class="icon-container">
                        <ltw-auth-image
                                :auth-src="scope.row.icon ? downloadUrl+scope.row.icon: ''"
                                fit="fill">
                            <template #error>
                                <div class="image-slot">
                                    <el-icon><picture-outline /></el-icon>
                                </div>
                            </template>
                        </ltw-auth-image>
                    </div>
                </template>
            </el-table-column>
            <el-table-column header-align="left" align="left" label="状态" width="100">
                <template #default="scope">
                    <el-switch v-model="scope.row.enabled" disabled=""></el-switch>
                </template>
            </el-table-column>
        </el-table>
    </el-card>
    <el-dialog
            title="权限分配"
            v-model="dialogVisible"
            width="50%"
            @close="handleClose">
        <div class="privilege-container">
            <el-transfer
                    v-model="selectedApplications"
                    :titles="['待分配', '已分配']"
                    :button-texts="['删除', '添加']"
                    :data="applications"
                    :props="transferProps"
            >
                <template #default="{option}">
                    <span>{{ option.name }}</span>
                </template>
            </el-transfer>
        </div>
        <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="applicationAssign">确 定</el-button>
        </template>
    </el-dialog>

</template>

<script>
    import GLB_CONFIG from '@/plugins/glb-constant'
    import {listSysPrivilegeMenu, treeListSysPrivilegeMenu} from '@/apis/system/sys-privilege-menu'
    import {listSysPrivilegeApplication} from '@/apis/system/sys-privilege-application'
    import {assignApplications, assignMenus} from '@/apis/system/sys-role-custom-actor'
    import LtwAuthImage from "@/components/base/LtwAuthImage";

    export default {
        name: "Privilege",
        components: {LtwAuthImage},
        data() {
            return {
                downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
                currentActorApplications: [],
                queryParam: {},
                dialogVisible: false,
                selectedApplications: [],
                transferProps: {
                    key: 'id',
                    label: 'name'
                },
                applications: [],
                roleSelected: false,
                roleId: '',
                menuTreeProps: {
                    label: 'name',
                    children: 'children'
                }
            }
        }
        ,
        props: {},
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList

            }

        },
        methods: {
            load(roleId) {
                this.roleId = roleId
                this.getCurrentActorApplications()
                this.roleSelected = true
            },
            getCurrentActorApplications() {
                this.queryParam.roleId = this.roleId
                listSysPrivilegeApplication(this.queryParam).then(
                    res => {
                        this.currentActorApplications = res.data;
                    }
                )
            },
            handleClose() {
                this.selectedApplications = []
                this.applications = []
            },
            showAssignDialog() {
                this.dialogVisible = true
                if (this.currentActorApplications && this.currentActorApplications.length > 0) {
                    this.currentActorApplications.forEach(
                        ele => {
                            this.selectedApplications.push(ele.id)
                        }
                    )

                }
                this.queryParam.roleId = undefined
                listSysPrivilegeApplication(this.queryParam).then(
                    res => {
                        this.applications = res.data;
                    }
                )
            },
            applicationAssign() {
                assignApplications({
                    roleId: this.roleId,
                    privilegeIdList: this.selectedApplications
                }).then(
                    () => {
                        this.getCurrentActorApplications()
                    }
                )
                this.dialogVisible = false
            },
            handleExpandChange(row, expandedRows) {
                if (expandedRows.indexOf(row) === -1) {
                    return
                }
                listSysPrivilegeMenu({
                    roleId: this.roleId,
                    applicationId: row.id
                }).then(
                    res => {
                        if (res.data && res.data.length > 0) {
                            row.assignedMenus = []
                            res.data.forEach(
                                ele => {
                                    if (ele.asLeaf) {
                                        row.assignedMenus.push(ele.id)
                                    }
                                }
                            )
                        }
                    }
                )
                treeListSysPrivilegeMenu({
                    applicationId: row.id,
                    enabled: true
                }).then(
                    res => {
                        row.menuTree = res.data
                    }
                )

            },
            menuAssign() {
                let keys = this.$refs.menuTree.getCheckedKeys();
                let halfCheckedKeys = this.$refs.menuTree.getHalfCheckedKeys();
                if (halfCheckedKeys && halfCheckedKeys.length > 0) {
                    halfCheckedKeys.forEach(
                        ele => keys.push(ele)
                    )
                }


                assignMenus({
                    roleId: this.roleId,
                    privilegeIdList: keys
                }).then(
                    this.$message.success("保存成功")
                )
            }
        }
    }
</script>

<style scoped lang="scss">
    .title-text-container {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .privilege-container {
        display: flex;
        justify-content: center;
        align-items: center;

        .el-transfer-panel {
            width: 200px;
        }
    }

    .icon-container {
        width: 40px;
        height: 40px;
    }

    /*.ltw-auth-image {*/
    /*    width: 40px;*/
    /*    height: 40px;*/
    /*}*/
</style>
