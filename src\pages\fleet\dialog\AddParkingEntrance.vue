<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    @close="dialogClosed"
    append-to-body
    :draggable="true"
  >
    <el-form :model="form" ref="formRef" label-width="100px">
      <el-form-item :label="$t('编码')" prop="code">
        <ltw-input v-model="form.code" :disabled="formReadonly" textType="code" id="code"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('入口名称')" prop="name">
        <ltw-input v-model="form.name" :disabled="formReadonly" id="name"></ltw-input>
      </el-form-item>
      <el-form-item :label="$t('进入口路线')" prop="route">
        <el-select
          filterable
          collapse-tags
          collapse-tags-tooltip
          v-model="form.route"
          :disabled="formReadonly"
          clearable
        >
          <el-option
            v-for="item in parkingEntranceRouteList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('道闸类型')" prop="barrierType">
        <el-select
          filterable
          collapse-tags
          collapse-tags-tooltip
          v-model="form.barrierType"
          :disabled="formReadonly"
          clearable
        >
          <el-option
            v-for="item in parkingEntranceBarrierList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('入口坡道类型')" prop="rampType">
        <el-select filterable v-model="form.rampType" :disabled="formReadonly" clearable>
          <el-option
            v-for="item in parkingEntranceRampList"
            :key="item.code"
            :label="$t(item.name)"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('是否有闸机')" prop="hasBarrier">
        <el-select filterable v-model="form.hasBarrier" :disabled="formReadonly" clearable>
          <el-option v-for="item in booleanList" :key="item.code" :label="$t(item.name)" :value="item.code" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="dialogStatus === 'view'" id="view-cancel">{{
          $t('关闭')
        }}</el-button>
        <template v-else>
          <el-button @click="dialogVisible = false" id="cancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submit" id="submit">{{ $t('保存') }}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { validatePhoneOrTel } from '@/plugins/util.js'
import { listSysDictionary } from '@/apis/system/sys-dictionary'
import {
  saveFtmParkingEntrance,
  updateFtmParkingEntrance,
  getFtmParkingEntrance
} from '@/apis/fleet/ftm-parking-entrance'
import Codemirror from 'codemirror-editor-vue3'
// language
import 'codemirror/mode/javascript/javascript.js'
// theme
import 'codemirror/theme/dracula.css'

const defaultform = {}
export default {
  name: 'AddParkingEntrance',
  emits: ['reload'],
  components: {
    Codemirror
  },
  data() {
    return {
      booleanList: [
        {
          name: '是',
          code: true
        },
        {
          name: '否',
          code: false
        }
      ],
      parkingLotId: '',
      parkingEntranceRouteList: [],
      parkingEntranceRampList: [],
      parkingEntranceBarrierList: [],
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      formRules: {
        name: [{ required: true, message: this.$t('请输入姓名'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('请输入编号'), trigger: 'blur' }],
        telephoneNum: [
          {
            required: true,
            message: this.$t('请输入电话'),
            trigger: 'blur'
          },
          { validator: validatePhoneOrTel, trigger: 'blur' }
        ]
      },
      cmOptions: {}
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      this.dialogStatus = row.type
      this.parkingLotId = row.parkingLotId
      switch (row.type) {
        case 'add':
          this.dialogTitle = this.$t('新增停车场入口')
          break
        case 'edit':
          this.dialogTitle = this.$t('编辑停车场入口')
          this.getFtmParkingEntrance(row.id)
          break
        case 'view':
          this.dialogTitle = this.$t('停车场入口详情')
          this.getFtmParkingEntrance(row.id)
          break
      }
      this.getparkingEntranceRoute() // 进入口路线
      this.getparkingEntranceRamp() //入口坡道类型
      this.getparkingEntranceBarrier() //入口道闸类型
    },
    getFtmParkingEntrance(id) {
      getFtmParkingEntrance(id).then(res => {
        this.form = res.data
      })
    },

    getparkingEntranceRoute() {
      if (!this.parkingEntranceRouteList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrance_route'
        }).then(res => {
          this.parkingEntranceRouteList = res.data
        })
      }
    },
    getparkingEntranceRamp() {
      if (!this.parkingEntranceRampList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrace_ramp'
        }).then(res => {
          this.parkingEntranceRampList = res.data
        })
      }
    },
    getparkingEntranceBarrier() {
      if (!this.parkingEntranceBarrierList?.length) {
        listSysDictionary({
          typeCode: 'parking_entrace_barrier'
        }).then(res => {
          this.parkingEntranceBarrierList = res.data
        })
      }
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload', val)
    },
    dialogOpened() {},
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.form = { ...defaultform }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        if (this.dialogStatus === 'add') {
          this.form.parkingLotId = this.parkingLotId
          saveFtmParkingEntrance(this.form).then(() => {
            this.cancel()
          })
        }
        if (this.dialogStatus === 'edit') {
          updateFtmParkingEntrance(this.form).then(() => {
            this.cancel()
          })
        }
      })
    },
    onChange() {},
    onBlur() {
      try {
        this.formData[this.activeTab] = JSON.stringify(JSON.parse(this.formData[this.activeTab] || '{}'), null, '\t')
      } catch (error) {
        console.log(error)
      }
    },
    onFocus() {},
    onScroll() {}
  }
}
</script>

<style scoped lang="scss"></style>
