import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmDbcSignalLibrary = (data = {}, params = {}) =>
  httpPost({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys', data, params })
export const updateFtmDbcSignalLibrary = (data = {}, params = {}) =>
  httpPut({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys', data, params })
export const deleteFtmDbcSignalLibrary = (params = {}) =>
  httpDelete({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys', params })
export const listFtmDbcSignalLibrary = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys', params })
export const listFtmDbcSignalLibrarySelection = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys/selections', params })
export const pageFtmDbcSignalLibrary = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys/page', params })
export const getFtmDbcSignalLibrary = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys/' + id })
export const pageFtmDbcSignalLibraryType = (params = {}) =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/ftm_dbc_signal_librarys/pageSignalType', params })
