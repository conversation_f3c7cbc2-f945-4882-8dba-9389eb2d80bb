<template>
  <div class="ltw-page-container">
    <el-card>
      <div class="ltw-toolbar">
        <div class="ltw-search-container ltw-tool-container">
          <el-input :placeholder="$t('请输入关键字')" v-model="queryParam.key" clearable @clear="refresh">
            <template #append>
              <el-button @click="refresh">
                <ltw-icon icon-code="el-icon-search"></ltw-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="ltw-tool-container button-group">
          <el-button
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item)"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-dropdown
            @command="handleCommand"
            class="batch-operate-btn"
            v-if="batchingFunctionList && batchingFunctionList.length > 0"
          >
            <el-button type="primary">
              {{ $t('批量操作') }}
              <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :key="item.id" v-for="item in batchingFunctionList" :command="item.buttonCode">
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="table-content" v-if="loading">
        <el-row v-for="rowIndex in Math.ceil(pageData?.records?.length / splitNum)" :key="rowIndex" :gutter="20">
          <el-col
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
            :xl="4"
            v-for="colIndex in rowIndex === Math.ceil(pageData?.records?.length / splitNum)
              ? pageData?.records?.length % splitNum || splitNum
              : splitNum"
            :key="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)].id"
            style="margin-bottom: 20px"
          >
            <image-sample-card
              ref="ImageSampleCard"
              :item="pageData.records[(rowIndex - 1) * splitNum + (colIndex - 1)]"
              :customFunctionList="customFunctionList"
              :inlineFunctionList="inlineFunctionList"
              type="edit"
              @view="view"
              @edit="edit"
              @singleRemove="singleRemove"
            />
          </el-col>
        </el-row>
      </div>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[12, 24, 36, 48]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
      >
      </el-pagination>
    </el-card>
    <add-image-sample ref="AddImageSample" @reload="reload" />
  </div>
</template>

<script>
import { deleteDmImageSample, pageDmImageSample } from '@/apis/data-collect/dm-image-sample'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showToast, showConfirmToast, debounce } from '@/plugins/util'
import AddImageSample from '@/pages/dataCollect/dialog/AddImageSample'
import ImageSampleCard from '@/pages/dataCollect/components/ImageSampleCard'

export default {
  name: 'DmImageSample',
  components: { ImageSampleCard, AddImageSample },
  data() {
    return {
      batchingFunctionList: [],
      inlineFunctionList: [],
      outlineFunctionList: [],
      customFunctionList: [],
      dataOperatePermission: {},
      pageData: {
        records: [],
        total: 0
      },
      queryParam: {
        current: 1,
        size: 12
      },
      loading: true,
      splitNum: 5
    }
  },
  created() {
    const routerFunctionMap = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]
    if (routerFunctionMap) {
      this.batchingFunctionList = routerFunctionMap.batchingFunctionList
      this.inlineFunctionList = routerFunctionMap.inlineFunctionList
      this.outlineFunctionList = routerFunctionMap.outlineFunctionList
      this.customFunctionList = routerFunctionMap.customFunctionList
      this.dataOperatePermission = routerFunctionMap.dataOperatePermission
    }
    this.query()
    let _this = this
    window.onresize = debounce(_this.changeSize)
    this.changeSize()
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  methods: {
    executeButtonMethod(button, row) {
      this[button.buttonCode](row, button)
    },
    refresh() {
      this.query()
    },
    query() {
      pageDmImageSample(this.queryParam).then(res => {
        res.data.records.forEach(val=>{
          val.sourceTypeName = val.sourceType.toLowerCase().split('_').join(' ')
        })
        this.pageData = res.data
      })
    },
    add() {
      this.$refs.AddImageSample.show({
        type: 'add'
      })
    },
    edit(row) {
      this.$refs.AddImageSample.show({
        type: 'edit',
        id: row.id
      })
    },
    view(row) {
      this.$refs.AddImageSample.show({
        type: 'view',
        id: row.id
      })
    },
    handleCommand(command) {
      if (this.selectedData.length === 0) {
        return showToast(BASE_CONSTANT.BATCH_OPERATION_WARNING, 'warning')
      }
      if (command === 'batchRemove') {
        this.batchRemove()
      }
    },
    singleRemove(row) {
      this.remove({ id: row.id })
    },
    batchRemove() {
      let idList = []
      this.selectedData.forEach(ele => {
        idList.push(ele.id)
      })
      let ids = idList.join(',')
      this.remove({ ids })
    },
    remove(param) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDmImageSample(param).then(() => {
          this.query()
        })
      })
    },
    handleSizeChange(value) {
      this.queryParam.size = value
      this.query()
    },
    handleCurrentChange(value) {
      this.queryParam.current = value
      this.query()
    },
    reload() {
      this.query()
    },
    changeSize() {
      if (window.innerWidth >= 1920) {
        this.splitNum = 6
      } else if (window.innerWidth >= 1200) {
        this.splitNum = 4
      } else if (window.innerWidth >= 992) {
        this.splitNum = 3
      } else if (window.innerWidth >= 768) {
        this.splitNum = 2
      } else {
        this.splitNum = 1
      }
    }
  }
}
</script>

<style scoped lang="scss">
.button-group {
  .el-button {
    margin-right: 10px;
  }
}
</style>
