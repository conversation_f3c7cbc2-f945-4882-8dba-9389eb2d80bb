<template>
  <el-card class="box-card" :class="getStatusType(form.status).type" id="task-form">
    <div class="card-header">
      <div class="title-content">
        <el-tag class="vin">{{ form.vin }}</el-tag>
        <div class="title">
          <el-tooltip effect="dark" :content="form.name">{{ form.name }}</el-tooltip>
        </div>
      </div>
      <el-tag :type="getStatusType(form.status).type">{{ getStatusType(form.status).name }}</el-tag>
    </div>
    <div class="card-content">
      <div class="task-type">
        <div class="name">{{ form.acquisitionTypeName }}</div>
        <ltw-icon v-if="form.acquisitionType === 'parking'" icon-code="svg-parking" />
        <ltw-icon v-else-if="form.acquisitionType === 'driving'" icon-code="svg-driving" />
        <ltw-icon v-else icon-code="svg-parking-driving" />
      </div>
      <div class="task-content">
        <div class="task-item">
          <div class="item-label">编号</div>
          <div class="item-value">{{ form.code }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">合规员</div>
          <div class="item-value">{{ form.complianceOfficer }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">负责人</div>
          <div class="item-value">{{ form.recipient }}</div>
        </div>
        <div class="task-item">
          <div class="item-label">设备</div>
          <div class="item-value">
            {{ form.equipmentCode }}
          </div>
        </div>
        <div class="task-item">
          <div class="item-label">期望日期</div>
          <div class="item-value">
            <span>
              <el-tag>{{ form.expectedStartTime }}</el-tag>
              &nbsp; ~ &nbsp;
              <el-tag>{{ form.expectedEndTime }}</el-tag>
            </span>
          </div>
        </div>
        <!--        <div class="task-item" v-if="form.status === 'finished' || form.status === 'executing'">-->
        <!--          <div class="item-label">执行时间</div>-->
        <!--          <div  class="item-value">-->
        <!--            <span-->
        <!--              ><el-tag>{{ parseTime(form.startTime, '{y}-{m}-{d}') }}</el-tag-->
        <!--              >&nbsp; ~ &nbsp; <el-tag>{{ parseTime(form.endTime, '{y}-{m}-{d}') || '-' }}</el-tag></span-->
        <!--            >-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
      <div class="task-record">
        <div class="record-item">
          <div class="name">采集记录</div>
          <div class="value">
            <!--            <ltw-icon icon-code="el-icon-chat-square" />-->
            <div class="num">
              <el-tooltip effect="dark" :content="(form.recordAmount || 0).toString()">
                <el-link @click="getTaskRecord(form)" type="primary" :underline="false" id="tag">
                  {{ form.recordAmount || 0 }}
                </el-link>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="record-item" v-if="form.type === 'special'">
          <div class="name">需求标签</div>
          <div class="value">
            <!--            <ltw-icon icon-code="el-icon-chat-square" />-->
            <div class="num">
              <el-tooltip effect="dark" :content="(getTagsLength(form?.requirementList) || 0).toString()">
                <el-link @click="getTags(form, 'req')" type="primary" :underline="false" id="tag">
                  {{ getTagsLength(form?.requirementList) || 0 }}
                </el-link>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="record-item">
          <div class="name">分类标签</div>
          <div class="value">
            <!--            <ltw-icon icon-code="el-icon-chat-square" />-->
            <div class="num">
              <el-tooltip effect="dark" :content="(getTagsLength(form?.classificationList) || 0).toString()">
                <el-link @click="getTags(form, 'class')" type="primary" :underline="false" id="tag">
                  {{ getTagsLength(form?.classificationList) || 0 }}
                </el-link>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <el-link
        v-if="form.status === 'draft' &&( empId === form.recipientEmpId|| isAdmin )"
        type="success"
        @click="publishTaskDetail(form.id)"
        :underline="false"
        id="release"
        >{{ $t('发布') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'draft' && (empId === form.recipientEmpId || isAdmin)"
        @click="editDaqReqDetail(form.id)"
        :underline="false"
        id="edit"
        >{{ $t('编辑') }}
      </el-link>
      <el-link
        type="danger"
        v-if="form.status === 'draft' &&( empId === form.recipientEmpId || isAdmin)"
        @click="deleteDaqReqDetail(form.id)"
        :underline="false"
        id="delete"
        >{{ $t('删除') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'published' &&( empId === form.recipientEmpId || isAdmin)"
        @click="start(form.id)"
        :underline="false"
        id="start"
        >{{ $t('开始') }}
      </el-link>
      <el-link
        type="warning"
        v-if="form.status === 'executing' &&( empId === form.recipientEmpId || isAdmin)"
        @click="finish(form.id)"
        :underline="false"
        id="finish"
        >{{ $t('完成') }}
      </el-link>
      <el-link type="primary" @click="getTaskDetail(form.id)" :underline="false" id="detail">{{ $t('详情') }}</el-link>
    </div>
  </el-card>
</template>

<script>
// task20220928141
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { dateUtils, showToast, showConfirmToast } from '@/plugins/util'
import $store from '@/store'
import {
  publishTaskDetail,
  deleteDaqReqDetail,
  startTaskDetail,
  finishTaskDetail
} from '@/apis/data-collect/vt-daq-task'
// import CollectRecordDialog from '@/pages/dataCollect/dialog/CollectRecordDialog.vue'
import LtwIcon from '@/components/base/LtwIcon.vue'

export default {
  name: 'TaskForm',
  emits: ['reload', 'handleFormChecked', 'showTagList', 'opt-add-task', 'show-req-detail', 'show-task-record'],
  data() {
    return {
      // 配置标签
      tagsData: [],
      tagDialogVisible: false,
      continuousUnits: [],
      editTagsFlag: false,
      tagsTitle: '',
      empId: $store.state.permission.currentUser.empId,
      parseTime: dateUtils.parseTime
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    },
    batchingFunctionList: {
      type: Array,
      default: []
    },
    outlineFunctionList: {
      type: Array,
      default: []
    },
    inlineFunctionList: {
      type: Array,
      default: []
    },
    selectable: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default : false
    }
  },
  computed: {
    form() {
      return this.item
    }
  },
  components: {
    LtwIcon
  },

  methods: {
    tagClose() {
      this.tagDialogVisible = false
    },
    getTags(row, type) {
      this.$emit('showTagList', {
        type: 'view',
        data: {
          taskType: type,
          groupList: type === 'req' ? row.requirementList : row.classificationList
        }
      })
    },
    getTaskRecord(row) {
      let postData = {
        type: 'edit',
        // type: editTags ? 'edit' : 'view',
        data: {
          ...row,
          taskCode: row.code,
          tagRecordCount: row?.tagList?.length
        }
      }
      this.$emit('show-task-record', postData)
    },
    getStatusType(status) {
      let type, name
      switch (status) {
        case 'draft':
          type = 'info'
          name = '草稿'
          break
        case 'published':
          type = 'primary'
          name = '已发布'
          break
        case 'executing':
          type = 'warning'
          name = '执行中'
          break
        case 'finished':
          type = 'success'
          name = '已完成'
          break
      }
      return { type, name }
    },
    cancel(val) {
      this.$emit('reload', val)
    },
    getTaskDetail(id) {
      this.$emit('opt-add-task', { type: 'view', id })
    },
    editDaqReqDetail(id) {
      this.$emit('opt-add-task', { type: 'edit', id })
      // this.$refs.AddTask.show({ type: 'edit', id })
    },
    publishTaskDetail(id) {
      publishTaskDetail(id).then(res => {
        showToast('发布成功')
        this.cancel()
      })
      // })
    },
    deleteDaqReqDetail(id) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        deleteDaqReqDetail({ id }).then(() => {
          showToast('删除成功')
          this.cancel()
        })
      })
      // })
    },
    start(id) {
      startTaskDetail(id).then(res => {
        showToast('已开始')
        this.cancel()
      })
    },
    finish(id) {
      finishTaskDetail(id).then(res => {
        showToast('已完成')
        this.cancel()
      })
    },
    getTagsLength(list) {
      return list
        ?.map(val => val.children)
        ?.flat(Infinity)
        ?.map(val => val.tagList)
        ?.flat(Infinity)?.length
    }
  }
}
</script>

<style scoped lang="scss">
.el-card {
  //height: 256px;
  position: relative;

  :deep(.el-card__body) {
    padding: 10px var(--el-card-padding) var(--el-card-padding) var(--el-card-padding);
  }

  &.box-card {
    border-left: 3px solid;
  }

  &.primary {
    border-left-color: #409eff;
  }

  &.info {
    border-left-color: #909399;
  }

  &.warning {
    border-left-color: #e6a23c;
  }

  &.danger {
    border-left-color: #f56c6c;
  }

  &.success {
    border-left-color: #67c23a;
  }

  //margin-bottom: 15px;

  //.el-descriptions,
  //:deep(.el-descriptions__body),
  //tbody {
  //  width: 100%;
  //
  //  .el-descriptions__table.is-bordered .el-descriptions__cell {
  //    font-size: 12px;
  //    font-weight: 400;
  //    padding: 2px 11px;
  //    white-space: nowrap;
  //    overflow: hidden;
  //    text-overflow: ellipsis;
  //
  //    &.el-descriptions__label {
  //      font-weight: 600;
  //      white-space: nowrap;
  //    }
  //  }
  //
  //  .el-descriptions__table {
  //    table-layout: fixed;
  //  }
  //
  //  // :deep(.el-descriptions__table) {
  //  //   :deep(.el-descriptions__cell) {
  //  //     font-size: 12px;
  //  //     font-weight: 400;
  //  //     padding: 0 11px;
  //  //     // white-space: nowrap;
  //  //     // overflow: hidden;
  //  //     // text-overflow: ellipsis;
  //  //     // word-break: break-all;
  //  //   }
  //  // :deep(.el-descriptions__label) {
  //  //   font-weight: 600;
  //  // }
  //  // }
  //}

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px 10px 0;

    .title-content {
      display: flex;
    }

    .vin {
      font-size: 12px;
      padding: 0 4px;
      display: flex;
      align-items: center;
      background: rgb(16, 41, 106);
      border-color: rgb(16, 41, 106);
      color: #fff;
      border-radius: 6px;
      margin-right: 10px;
    }

    .title {
      color: #404245;
      font-weight: 600;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      margin: 0 10px;
    }

    .el-link {
      white-space: nowrap;
    }
  }

  .card-footer {
    //position: relative;
    //bottom: -6px;
    position: absolute;
    right: 20px;
    bottom: 4px;
    text-align: right;

    .el-link:not(:last-child) {
      margin-right: 8px;
    }
  }

  .card-content {
    display: flex;

    .task-type {
      width: 100px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-right: 20px;

      .name {
        font-size: 16px;
        font-weight: 600;
      }

      .ltw-icon {
        font-size: 50px;
      }
    }

    .task-content {
      width: calc(100% - 148px);

      .task-item {
        display: flex;
        font-size: 12px;
        line-height: 24px;
        overflow: hidden;

        .item-label {
          white-space: nowrap;
          width: 70px;
          font-weight: 600;
        }

        .item-value {
          white-space: nowrap;
        }
      }
    }

    .task-record {
      width: 48px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .record-item {
        .name {
          font-weight: 600;
          font-size: 12px;
        }

        .value {
          position: relative;

          .ltw-icon {
            font-size: 48px;
          }

          .num {
            //position: absolute;
            padding: 0 7px;
            text-align: center;
            width: 100%;
            top: 10px;
            //text-overflow: ellipsis;
            //white-space: nowrap;
            //overflow: hidden;
            //cursor: pointer;
            font-size: 18px;
            //font-weight: 600;
          }

          .value {
          }
        }
      }
    }
  }
}
</style>
