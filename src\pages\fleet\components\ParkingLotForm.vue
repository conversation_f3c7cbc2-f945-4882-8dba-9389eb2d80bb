<template>
  <el-card>
    <div v-if="!form.edit">
      <el-divider content-position="left">{{ $t('停车场信息') }}</el-divider>
      <el-descriptions :column="5" border>
        <el-descriptions-item :label="$t('类型')">
          <el-tag v-if="getDicName(form.type, groundLevelList)">{{ $t(getDicName(form.type, groundLevelList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('总车位')"
        ><span style="white-space: nowrap">{{ form.totalParkingSpace }}</span></el-descriptions-item
        >
        <el-descriptions-item :label="$t('地面材质')">
          <el-tag v-for="item in getDicName(form.roadSurfaceMaterialCodeList, parkingLotMaterialList)" :key="item"
            >{{ $t(item) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('是否人防')">
          <el-tag>{{ $t(getDicName(form.civilAirDefense, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('限高(米)')">{{ form.limitedHeight }} </el-descriptions-item>
        <el-descriptions-item :label="$t('层数')">{{ form.floors }} </el-descriptions-item>
        <el-descriptions-item :label="$t('停车数')">{{ form.parkingVolume }} </el-descriptions-item>
        <el-descriptions-item :label="$t('走道是否有箭头标识')">
          <el-tag>{{ $t(getDicName(form.hasArrow, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('是否安装减速带')">
          <el-tag>{{ $t(getDicName(form.hasSpeedBump, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('亮度')">
          <el-tag v-if="getDicName(form.brightness, parkingLotRrightnessList)"
            >{{ $t(getDicName(form.brightness, parkingLotRrightnessList)) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('是否有RTK车位')">
          <el-tag>{{ $t(getDicName(form.hasRtkSlot, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('特殊标签')">
          <span v-if="form.specialTag">{{ $t(form.specialTag) }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left">{{ $t('停车位信息') }}</el-divider>
      <el-descriptions :column="5" border>
        <el-descriptions-item :label="$t('车位类型')">
          <el-tag v-for="item in getDicName(form.parkingSpaceTypeCodeList, parkingSpaceTypeList)" :key="item"
            >{{ $t(item) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('是否有人防工程标识')">
          <el-tag>{{ $t(getDicName(form.hasCivilAirLogo, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('长度(米)')">{{ form.parkingSpaceLength }} </el-descriptions-item>
        <el-descriptions-item :label="$t('车位线颜色')">
          <el-tag v-for="item in getDicName(form.parkingLineColorCodeList, parkingLotInnerLineColorList)" :key="item">{{ $t(item) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('车位线状态')">
          <el-tag v-for="item in getDicName(form.parkingLineStatusCodeList, parkingLotLineStatusList)" :key="item"
            >{{ $t(item) }}
          </el-tag>
          <!-- <el-tag>{{
            getDicName(form.parkingLineStatus, parkingLotLineStatusList)
          }}</el-tag> -->
        </el-descriptions-item>
        <el-descriptions-item :label="$t('是否有升降车位')">
          <el-tag>{{ $t(getDicName(form.hasLiftCarport, booleanList)) }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('配置')">
          <el-tag v-if="getDicName(form.parkingSpaceConfiguration, parkingLotConfig)"
            >{{ $t(getDicName(form.parkingSpaceConfiguration, parkingLotConfig)) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('宽度(米)')">{{ form.parkingSpaceWidth }} </el-descriptions-item>
        <el-descriptions-item :label="$t('车位内部颜色')">
          <el-tag v-for="item in getDicName(form.parkingInnerColorCodeList, parkingInnerColorList)" :key="item"
            >{{ $t(item) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-form v-else size="small" :model="form" :rules="formRules" ref="itemFormRef" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('类型')" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio v-for="item in groundLevelList" :label="item.code" :key="item.code"
              >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-divider content-position="left">{{ $t('停车场信息') }}</el-divider>
        <el-col :span="8">
          <el-form-item :label="$t('总车位')" prop="totalParkingSpace">
            <el-input-number :min="1" v-model="form.totalParkingSpace" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('地面材质')" prop="roadSurfaceMaterialCodeList">
            <el-select
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              v-model="form.roadSurfaceMaterialCodeList"
              :disabled="formReadonly"
              clearable
            >
              <el-option
                v-for="item in parkingLotMaterialList"
                :key="item.code"
                :label="$t(item.name)"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('是否人防')" prop="civilAirDefense">
            <el-switch v-model="form.civilAirDefense" inline-prompt :active-text="$t('是')" :inactive-text="$t('否')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('是否安装减速带')" prop="hasSpeedBump">
            <el-switch v-model="form.hasSpeedBump" inline-prompt :active-text="$t('是')" :inactive-text="$t('否')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('层数')" prop="floors">
            <el-input-number :min="0" v-model="form.floors" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('停车数')" prop="parkingVolume">
            <el-input-number :min="0" v-model="form.parkingVolume" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('走道是否有箭头标识')" prop="hasArrow">
            <el-switch v-model="form.hasArrow" inline-prompt :active-text="$t('是')" :inactive-text="$t('否')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('限高(米)')" prop="limitedHeight">
            <el-input-number :min="1" v-model="form.limitedHeight" :precision="2" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('亮度')" prop="brightness">
            <el-radio-group v-model="form.brightness">
              <el-radio v-for="item in parkingLotRrightnessList" :label="item.code" :key="item.code"
                >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-divider content-position="left">{{ $t('停车位信息') }}</el-divider>
        <el-col :span="8">
          <el-form-item :label="$t('车位类型')" prop="parkingSpaceTypeCodeList">
            <el-checkbox-group v-model="form.parkingSpaceTypeCodeList">
              <el-checkbox v-for="item in parkingSpaceTypeList" :label="item.code" :key="item.code"
                >{{ $t(item.name) }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('是否有人防工程标识')" prop="hasCivilAirLogo">
            <el-switch v-model="form.hasCivilAirLogo" inline-prompt :active-text="$t('是')" :inactive-text="$t('否')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('是否有升降车位')" prop="hasLiftCarport">
            <el-switch v-model="form.hasLiftCarport" inline-prompt :active-text="$t('是')" :inactive-text="$t('否')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('配置')" prop="parkingSpaceConfiguration">
            <el-radio-group v-model="form.parkingSpaceConfiguration">
              <el-radio v-for="item in parkingLotConfig" :label="item.code" :key="item.code"
                >{{ $t(item.name) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('长度') + '(m)'" prop="parkingSpaceLength">
            <el-input-number :min="0" v-model="form.parkingSpaceLength" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('宽度') + '(m)'" prop="parkingSpaceWidth">
            <el-input-number :min="0" v-model="form.parkingSpaceWidth" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('车位线颜色')" prop="parkingLineColorCodeList">
            <el-checkbox-group v-model="form.parkingLineColorCodeList">
              <el-checkbox v-for="item in parkingLotInnerLineColorList" :label="item.code" :key="item.code"
                >{{ $t(item.name) }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('车位内部颜色')" prop="parkingInnerColorCodeList">
            <el-select
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              v-model="form.parkingInnerColorCodeList"
              :disabled="formReadonly"
              clearable
            >
              <el-option
                v-for="item in parkingInnerColorList"
                :key="item.code"
                :label="$t(item.name)"
                :value="item.code"
                id="parkingInnerColorCodeList"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
        </el-col>
      </el-row>
    </el-form>
    <div class="dialog-footer" v-if="!formReadonly">
      <span v-if="form.edit">
        <el-button :id="index + 'cancel'" size="small" @click="cancelParking()">{{ $t('取消') }}</el-button>
        <el-button :id="index + 'save'" size="small" type="primary" @click="submit">{{ $t('保存') }}</el-button>
      </span>
      <span v-else>
        <el-link :id="index + 'edit'" type="warning" @click="editParking()" :underline="false">{{
          $t('编辑')
        }}</el-link>
        <el-link :id="index + 'delete'" type="danger" @click="deleteParking()" :underline="false">{{
          $t('删除')
        }}</el-link>
      </span>
    </div>
  </el-card>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { showConfirmToast, getLocale } from '@/plugins/util'
import {
  updateFtmParkingLotDetail,
  deleteFtmParkingLotDetail
} from '@/apis/fleet/ftm-parking-lot-detail'
import DictionarySelection from '@/components/system/DictionarySelection.vue'

export default {
  name: 'CalibrationForm',
  emits: ['reload'],
  data() {
    return {
      locale: getLocale(),
      form: {},
      formRules: {
        type: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        totalParkingSpace: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        roadSurfaceMaterialCodeList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ],
        parkingSpaceTypeCodeList: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'change'
          }
        ]
      },
      booleanList: [
        {
          name: '是',
          code: true
        },
        {
          name: '否',
          code: false
        }
      ]
    }
  },
  props: {
    item: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    formReadonly: {
      type: Boolean,
      default: false
    },
    groundLevelList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingInnerColorList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingLotMaterialList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingLotRrightnessList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingSpaceTypeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingLotConfig: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingLotInnerLineColorList: {
      type: Array,
      default: () => {
        return []
      }
    },
    parkingLotLineStatusList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    DictionarySelection
  },
  watch: {
    item: {
      handler(val) {
        this.form = JSON.parse(JSON.stringify(val))
        this.backupForm = JSON.parse(JSON.stringify(val))
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    submit() {
      this.$refs.itemFormRef.validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.form.id = this.form.detailId
            updateFtmParkingLotDetail(this.form).then(res => {
              this.saveForm()
            })
          }
          // else {
          //   if (this.form.parkingLotId) {
          //     saveFtmParkingLotDetail(this.form).then(res => {
          //       this.form = res.data
          //       this.saveForm()
          //     })
          //   } else {
          //     this.saveForm()
          //   }
          // }
        }
      })
    },
    saveForm() {
      this.form.edit = false
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'add'
      })
    },
    editParking() {
      this.form.edit = true
      this.$emit('reload', {
        form: this.form,
        index: this.index,
        type: 'add'
      })
    },
    deleteParking() {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        if (this.form.detailId) {
          deleteFtmParkingLotDetail({ id: this.form.detailId }).then(() => {
            this.$emit('reload', {
              form: this.form,
              index: this.index,
              type: 'delete'
            })
            // this.query()
          })
        } else {
          this.$emit('reload', {
            form: this.form,
            index: this.index,
            type: 'delete'
          })
        }
      })
    },
    cancelParking() {
      if (this.form.type) {
        this.backupForm.edit = false
        this.$emit('reload', {
          form: this.backupForm,
          index: this.index,
          type: 'add'
        })
      } else {
        this.$emit('reload', {
          form: this.form,
          index: this.index,
          type: 'delete'
        })
      }
    },
    getDicName(value, list) {
      if (list?.length) {
        if (Array.isArray(value)) {
          let nameList = []
          value.forEach(val => {
            let item = list.find(listVal => {
              return listVal.code === val
            })
            nameList.push(item?.name || '')
          })
          return nameList
        } else {
          let item = list.find(val => {
            return val.code === (value || false)
          })
          return item?.name || ''
        }
      }
    },
    checkValidate() {
      return new Promise((resolve, reject) => {
        this.$refs?.itemFormRef?.validate(valid => {
          this.$emit('reload', {
            form: this.form,
            index: this.index,
            type: 'add'
          })
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
  margin-top: 10px;
}

.el-card {
  margin-bottom: 10px;

  .el-tag,
  .el-link {
    margin-right: 10px;
  }
}
</style>
