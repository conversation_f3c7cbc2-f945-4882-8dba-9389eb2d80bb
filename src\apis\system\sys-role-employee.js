import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysRoleEmployee = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees', data, params})
export const updateSysRoleEmployee = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees', data, params})
export const deleteSysRoleEmployee = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees', params})
export const listSysRoleEmployee = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.systemUrl + '/sys_role_employees', params})

// export const listSysRoleEmployee = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees', params})
export const listSysRoleEmployeeSelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees/selections', params})
export const pageSysRoleEmployee = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees/page', params})
export const getSysRoleEmployee = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees/' + id})
export const pageNotInWorkgroupSysRoleEmployee = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_role_employees/page/workgroups/not_in', params})
export const getDriverList = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/driver/list', params})
