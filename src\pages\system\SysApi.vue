<template>
  <div>
    <div class="main-container">
      <div class="org-tree-container">
        <div class="filter-opt">
          <ltw-input
            v-model="filterText"
            :placeholder="$t('过滤关键字')"
            id="filterText"
          />
          <el-dropdown @command="handleCommand" class="batch-operate-btn">
            <el-button type="primary" id="batch-operate-btn">
              API
              <ltw-icon
                icon-code="el-icon-arrow-down"
                class="el-icon--right"
              ></ltw-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :key="item.id"
                  v-for="item in APIFunctionList"
                  :command="item.buttonCode"
                  :id="item.buttonCode"
                >
                  <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                  {{ $t(item.name) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <el-tree
          @node-click="handleClick"
          ref="treeRef"
          class="filter-tree"
          node-key="id"
          :highlight-current="true"
          :data="apiMenuList"
          :props="defaultProps"
          default-expand-all
          :filter-node-method="filterNode"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span class="tree-opt">
                <el-link
                  :underline="false"
                  v-if="!data.module"
                  @click.stop="addTree(data)"
                >
                  <ltw-icon icon-code="el-icon-plus"></ltw-icon>
                </el-link>
                <el-link :underline="false" @click.stop="editTree(data)">
                  <ltw-icon icon-code="el-icon-Edit"></ltw-icon>
                </el-link>
                <el-link :underline="false" @click.stop="deleteTree(data)">
                  <ltw-icon icon-code="el-icon-delete"></ltw-icon>
                </el-link>
                <!-- <a @click="append(data)"> Append </a>
                <a @click="remove(node, data)"> Delete </a> -->
              </span>
            </span>
          </template></el-tree
        >
      </div>
      <div class="content-container">
        <el-card>
          <el-button
            class="apply-btn"
            :disabled="!formData.id"
            :type="item.buttonStyleType"
            :key="item.id"
            v-for="item in outlineFunctionList"
            @click="executeButtonMethod(item.buttonCode)"
            id="apply-btn"
          >
            <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
            {{ $t(item.name) }}
          </el-button>
          <el-form
            class="api-form"
            :model="formData"
            :rules="formRules"
            ref="formRef"
            label-position="top"
            :disabled="formReadonly"
            id="sys-api"
          >
            <el-row>
              <el-col :span="24">
                <el-form-item
                  :label="$t('模块 / API名称')"
                  prop="module"
                  id="module-item"
                >
                  <ltw-input
                    v-model="formData.name"
                    :placeholder="$t('请输入API名称')"
                    id="module"
                  >
                    <template #prepend>
                      <el-select
                        style="width: 110px"
                        v-model="formData.module"
                        :placeholder="$t('选择模块')"
                        popper-class="module-selector"
                      >
                        <el-option
                          v-for="item in apiMenuList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                          id="apiMenu"
                        />
                      </el-select>
                    </template>
                  </ltw-input>
                </el-form-item>
              </el-col> </el-row
            ><el-row>
              <el-col :span="24">
                <el-form-item :label="$t('API地址')" prop="url" id="url-item">
                  <ltw-input
                    v-model="formData.url"
                    :placeholder="$t('请输入API地址')"
                    id="url"
                    textType="description"
                  >
                    <template #prepend>
                      <el-select
                        v-model="formData.requestType"
                        style="width: 110px"
                        popper-class="requestType-selector"
                      >
                        <el-option
                          v-for="item in requestTypes"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code"
                          id="requestTypes"
                        />
                      </el-select>
                      <el-select
                        v-model="formData.httpType"
                        style="width: 110px"
                        @change="changeHttpType"
                        popper-class="httpType-selector"
                      >
                        <el-option
                          v-for="item in httpTypes"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code"
                          id="httpTypes"
                        />
                      </el-select>
                    </template>
                  </ltw-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('API描述')" prop="description">
                  <ltw-input
                    v-model="formData.description"
                    :placeholder="$t('请输入API描述')"
                    type="textarea"
                    id="description"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-collapse v-model="activeNames">
              <el-collapse-item :title="$t('请求参数')" name="1">
                <el-tabs
                  v-model="formData.paramPaneType"
                  class="demo-tabs"
                  @tab-change="handleTabClick"
                  :disabled="formReadonly"
                  id="paramPaneType"
                >
                  <el-tab-pane
                    v-if="
                      formData.httpType === 'post' ||
                      formData.httpType === 'put'
                    "
                    :label="$t('请求体')"
                    name="body"
                    id="paramPaneType"
                  >
                    <el-radio-group
                      v-model="formData.paramType"
                      id="paramType-radio"
                    >
                      <el-radio
                        v-for="item in paramTypes"
                        :label="item.code"
                        :key="item.code"
                        @change="changeParamType"
                        :id="item.code"
                        >{{ item.name }}</el-radio
                      >
                    </el-radio-group>
                  </el-tab-pane>
                  <el-tab-pane
                    v-if="
                      formData.httpType === 'get' ||
                      formData.httpType === 'delete'
                    "
                    :label="$t('Query参数')"
                    name="query"
                    id="httpType"
                  >
                  </el-tab-pane>
                </el-tabs>
                <el-table
                  :data="formData.paramStructure"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                  border
                  default-expand-all
                >
                  <el-table-column
                    prop="paramName"
                    :label="$t('参数名')"
                    width="180"
                  >
                    <template #default="scope">
                      <ltw-input
                        @input="
                          addItem(
                            formData.paramStructure,
                            scope.row,
                            scope.$index
                          )
                        "
                        v-model="scope.row.paramName"
                        :placeholder="$t('参数名')"
                        id="paramName"
                      ></ltw-input>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="paramType"
                    :label="$t('类型')"
                    width="180"
                  >
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.paramType"
                        :placeholder="$t('类型')"
                        style="width: 110px"
                        popper-class="paramType-selector"
                      >
                        <el-option
                          v-for="item in paramDataTypes"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code"
                          id="paramDataTypes"
                        />
                      </el-select> </template
                  ></el-table-column>
                  <el-table-column
                    prop="require"
                    :label="$t('必填')"
                    width="80"
                  >
                    <template #default="scope">
                      <el-checkbox
                        v-model="scope.row.require"
                        id="require"
                      /> </template
                  ></el-table-column>
                  <el-table-column
                    prop="description"
                    :label="$t('说明')"
                    width="180"
                  >
                    <template #default="scope">
                      <ltw-input
                        v-model="scope.row.description"
                        :placeholder="$t('说明')"
                        id="description"
                      ></ltw-input>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('操作')" width="240">
                    <template #default="scope">
                      <el-link
                        :disabled="formReadonly"
                        v-if="
                          scope.row.paramName && formData.paramType === 'json'
                        "
                        @click="addChildren(formData.paramStructure, scope.row)"
                        type="primary"
                        :underline="false"
                        >{{ $t('添加子字段') }}</el-link
                      >
                      <el-link
                        :disabled="formReadonly"
                        v-if="scope.row.paramName"
                        @click="
                          appendParam(
                            formData.paramStructure,
                            scope.row,
                            scope.$index
                          )
                        "
                        type="primary"
                        :underline="false"
                        >{{ $t('插入') }}</el-link
                      >
                      <el-link
                        :disabled="formReadonly"
                        @click="
                          deleteParam(
                            formData.paramStructure,
                            scope.row,
                            scope.$index,
                            scope
                          )
                        "
                        type="primary"
                        :underline="false"
                        >{{ $t('删除') }}</el-link
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
              <el-collapse-item :title="$t('请求参数示例')" name="2">
                <el-row>
                  <el-col :span="24">
                    <json-editor-vue
                      style="margin-top: 10px"
                      :currentMode="jsonMode"
                      :language="jsonLang"
                      v-model="formData.paramDemo"
                      :modeList="['code']"
                    ></json-editor-vue>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item :title="$t('响应内容')" name="3">
                <el-row>
                  <el-col :span="24">
                    <el-tabs
                      v-model="formData.resultPaneType"
                      class="demo-tabs"
                      @tab-change="handleResultTabClick"
                      :disabled="formReadonly"
                    >
                      <el-tab-pane :label="$t('返回结果')" name="resultBody">
                        <el-radio-group v-model="formData.resultType">
                          <el-radio
                            v-for="item in resultTypes"
                            :label="item.code"
                            :key="item.code"
                            >{{ item.name }}</el-radio
                          >
                        </el-radio-group>
                        <el-table
                          :data="formData.resultStructure"
                          style="width: 100%; margin-bottom: 20px"
                          row-key="id"
                          border
                          default-expand-all
                        >
                          <el-table-column
                            prop="paramName"
                            :label="$t('参数名')"
                            width="180"
                          >
                            <template #default="scope">
                              <ltw-input
                                @input="
                                  addItem(
                                    formData.resultStructure,
                                    scope.row,
                                    scope.$index
                                  )
                                "
                                :disabled="scope.row.disabled"
                                v-model="scope.row.paramName"
                                :placeholder="$t('参数名')"
                              ></ltw-input>
                            </template>
                          </el-table-column>
                          <el-table-column
                            prop="paramType"
                            :label="$t('类型')"
                            width="180"
                          >
                            <template #default="scope">
                              <el-select
                                v-model="scope.row.paramType"
                                :placeholder="$t('类型')"
                                style="width: 110px"
                                :disabled="scope.row.disabled"
                                popper-class="paramTypeSelector"
                              >
                                <el-option
                                  v-for="item in paramDataTypes"
                                  :key="item.code"
                                  :label="item.name"
                                  :value="item.code"
                                  id="paramDataTypes"
                                />
                              </el-select> </template
                          ></el-table-column>
                          <el-table-column
                            prop="require"
                            :label="$t('必填')"
                            width="80"
                          >
                            <template #default="scope">
                              <el-checkbox
                                :disabled="scope.row.disabled"
                                v-model="scope.row.require"
                                id="require"
                              /> </template
                          ></el-table-column>
                          <el-table-column
                            prop="description"
                            :label="$t('说明')"
                            width="180"
                          >
                            <template #default="scope">
                              <ltw-input
                                v-model="scope.row.description"
                                :placeholder="$t('说明')"
                                :disabled="scope.row.disabled"
                                id="description"
                              ></ltw-input>
                            </template>
                          </el-table-column>
                          <el-table-column
                            :label="$t('操作')"
                            width="240"
                            id="opration-button"
                          >
                            <template #default="scope">
                              <el-link
                                v-if="
                                  scope.row.disabled !== true &&
                                  scope.row.paramName &&
                                  formData.resultType === 'json'
                                "
                                @click="
                                  addChildren(
                                    formData.resultStructure,
                                    scope.row
                                  )
                                "
                                type="primary"
                                :underline="false"
                                :disabled="formReadonly"
                                id="add-sub-field"
                                >{{ $t('添加子字段') }}</el-link
                              >
                              <el-link
                                v-if="
                                  scope.row.disabled !== true &&
                                  scope.row.paramName
                                "
                                @click="
                                  appendParam(
                                    formData.resultStructure,
                                    scope.row,
                                    scope.$index
                                  )
                                "
                                type="primary"
                                :underline="false"
                                :disabled="formReadonly"
                                id="update"
                                >{{ $t('插入') }}</el-link
                              >
                              <el-link
                                v-if="scope.row.disabled !== true"
                                @click="
                                  deleteParam(
                                    formData.resultStructure,
                                    scope.row,
                                    scope.$index,
                                    scope
                                  )
                                "
                                type="primary"
                                :underline="false"
                                :disabled="formReadonly"
                                id="delete"
                                >{{ $t('删除') }}</el-link
                              >
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-tab-pane>
                    </el-tabs>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item :title="$t('响应内容示例')" name="4">
                <el-row>
                  <el-col :span="24">
                    <json-editor-vue
                      style="margin: 10px 0"
                      :currentMode="jsonMode"
                      :language="jsonLang"
                      v-model="formData.resultDemo"
                      :modeList="['code']"
                    ></json-editor-vue>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-form>
          <div class="footer" v-if="!formReadonly">
            <el-button @click="getAPIDetail()" id="cancel">{{
              $t('取消')
            }}</el-button>
            <el-button type="primary" @click="saveAPI()" id="save">{{
              $t('保存')
            }}</el-button>
          </div>
        </el-card>
      </div>
    </div>
    <add-moudle ref="addMoudle" @reload="query"></add-moudle>
    <apply-api ref="ApplyApi"></apply-api>
  </div>
</template>

<script>
import {
  getSysApiMenu,
  saveSysApi,
  updateSysApi,
  deleteSysApi,
  getSysApi
} from '@/apis/system/sys-api'
import {
  listSysDictionary,
  deleteSysDictionary
} from '@/apis/system/sys-dictionary'
import {
  showToast,
  showConfirmToast,
  getLocale,
  getUuid,
  debounce
} from '@/plugins/util'
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import JsonEditorVue from 'json-editor-vue3'
import AddMoudle from '@/pages/system/components/AddMoudle.vue'
import ApplyApi from '@/pages/system/components/ApplyApi.vue'

const defaultFormData = {
  module: '',
  name: '',
  description: '',
  url: '/',
  httpType: 'post',
  requestType: 'http',
  paramPaneType: 'body',
  paramType: 'json',
  paramStructure: [],
  paramDemo: {},
  resultPaneType: 'resultBody',
  resultType: 'json',
  resultDemo: {
    data: {},
    status: 1,
    success: true
  },
  resultStructure: [
    {
      description: '数据主体',
      disabled: true,
      id: '1',
      paramName: 'data',
      paramType: 'object',
      require: true,
      children: []
    },
    {
      description: '请求返回状态',
      disabled: true,
      id: '2',
      paramName: 'status',
      paramType: 'number',
      require: true
    },
    {
      description: '请求成功与否',
      disabled: true,
      id: '3',
      paramName: 'success',
      paramType: 'boolean',
      require: true
    }
  ]
}
export default {
  name: 'SysApi',
  data() {
    var validateAPIName = (rule, value, callback) => {
      if (!this.formData.module) {
        callback(new Error(this.$t('请选择模块')))
      } else if (!this.formData.name) {
        callback(new Error(this.$t('请输入API名称')))
      } else {
        callback()
      }
    }
    return {
      formData: Object.assign({}, defaultFormData),
      formRules: {
        module: [
          { required: true, validator: validateAPIName, trigger: 'blur' }
        ],
        url: [
          {
            required: true,
            message: this.$t('请输入API地址'),
            trigger: 'blur'
          }
        ],
        description: [
          {
            required: true,
            message: this.$t('请输入API描述'),
            trigger: 'blur'
          }
        ]
      },
      APIFunctionList: [
        {
          id: 1,
          buttonCode: 'api',
          name: '添加API'
        },
        {
          id: 2,
          buttonCode: 'group',
          name: '添加模块'
        }
      ],
      filterText: '',
      defaultProps: { children: 'sysApis', label: 'name' },
      apiMenuList: [],
      requestTypes: [],
      httpTypes: [],
      paramTypes: [
        {
          code: 'form',
          name: 'Form-data'
        },
        {
          code: 'json',
          name: 'JSON'
        }
      ],
      resultTypes: [
        {
          code: 'json',
          name: 'JSON'
        }
      ],
      paramTableData: [],
      paramQueryData: [],
      paramHeaderData: [],
      paramTableItem: {
        paramName: '',
        paramType: 'string',
        require: false,
        description: ''
      },
      resultTableData: [],
      paramDataTypes: [],
      formReadonly: true,
      jsonLang: getLocale() === 'en' ? 'en' : 'zh-CN',
      jsonMode: 'code',
      activeNames: []
    }
  },
  components: {
    JsonEditorVue,
    AddMoudle,
    ApplyApi
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  created() {
    this.query()
    this.formData.paramStructure = [{ ...this.paramTableItem, id: getUuid() }]
    this.formData.resultStructure[0].children = [
      { ...this.paramTableItem, id: getUuid() }
    ]
    // this.paramHeaderData = [{ ...this.paramTableItem, id: getUuid() }]
    // this.paramTableData = [{ ...this.paramTableItem, id: getUuid() }]
    // this.paramQueryData = [{ ...this.paramTableItem, id: getUuid() }]
    // this.resultTableData = [{ ...this.paramTableItem, id: getUuid() }]
    // this.currentUser = this.$store.state.permission.currentUser
    this.getDictList('request_type').then(res => {
      this.requestTypes = res
    })
    this.getDictList('http_type').then(res => {
      this.httpTypes = res
    })
    this.getDictList('api_data_types').then(res => {
      this.paramDataTypes = res
    })

    if (
      this.$store.state.permission.routerFunctionMap[
        this.$router.currentRoute.value.path
      ]
    ) {
      this.outlineFunctionList =
        this.$store.state.permission.routerFunctionMap[
          this.$router.currentRoute.value.path
        ].outlineFunctionList
    }
  },
  // computed: {
  //   formReadonly() {
  //     return this.dialogStatus === 'view'
  //   }
  // },
  methods: {
    getDictList(code) {
      return new Promise((resolve, reject) => {
        listSysDictionary({ typeCode: code }).then(res => {
          resolve(res.data)
        })
      })
    },
    executeButtonMethod(funcName, row) {
      this[funcName](row)
    },
    refresh() {
      this.$refs.tableRef.clearSelection()
      this.query()
    },
    query(id) {
      getSysApiMenu().then(res => {
        res.data.forEach(val => {
          val.name = val.moduleName
        })
        this.apiMenuList = res.data
        this.$nextTick(() => {
          id && this.$refs.treeRef.setCurrentKey(id)
        })
      })
    },
    handleCommand(command) {
      if (command === 'api') {
        // this.batchRemove()
        // console.log(this.$refs.treeRef.getCurrentNode())
        let data = this.$refs.treeRef.getCurrentNode()
        if (data && data.moduleName) {
          this.initForm(data && data.id)
        } else {
          this.initForm(data && data.module)
        }
        // this.formData = {
        //   ...defaultFormData,
        //   module: data ? data.id : ''
        // }
        // if (!data) {
        // this.formReadonly = true
        // }
        this.formReadonly = false
        this.activeNames = ['1', '2', '3', '4']
        this.jsonMode = 'code'
      }
      if (command === 'group') {
        this.$refs.addMoudle.show({ type: 'add' })
      }
    },
    initForm(id) {
      this.$refs.formRef.resetFields()
      this.formData = { ...defaultFormData, module: id }
      this.formData.paramStructure = [{ ...this.paramTableItem, id: getUuid() }]
      this.formData.resultStructure[0].children = [
        { ...this.paramTableItem, id: getUuid() }
      ]
      // this.resultTableData = [{ ...this.paramTableItem, id: getUuid() }]
      // this.paramHeaderData = [{ ...this.paramTableItem, id: getUuid() }]
      // this.paramTableData = [{ ...this.paramTableItem, id: getUuid() }]
      // this.paramQueryData = [{ ...this.paramTableItem, id: getUuid() }]
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    handleClick(obj) {
      if (obj.module) {
        this.getSysApi(obj.id)
        this.formReadonly = true
        this.activeNames = ['1', '2', '3', '4']
        this.jsonMode = 'view'
      }
    },
    getSysApi(id) {
      getSysApi(id).then(res => {
        /* 格式化请求参数 */
        res.data.paramStructure = JSON.parse(res.data.paramStructure || '[]')
        if (!(res.data.paramStructure && res.data.paramStructure.length)) {
          res.data.paramStructure = [{ ...this.paramTableItem, id: getUuid() }]
        }
        res.data.paramDemo = JSON.parse(res.data.paramDemo || '{}')

        /* 格式化相应参数 */
        let resultStructure = JSON.parse(
          JSON.stringify(defaultFormData.resultStructure)
        )
        /* 保存响应数据未填写data */
        res.data.resultStructure = JSON.parse(res.data.resultStructure || '[]')
        if (!this.formReadonly) {
          if (!(res.data.resultStructure && res.data.resultStructure.length)) {
            res.data.resultStructure = [
              { ...this.paramTableItem, id: getUuid() }
            ]
          }
          this.formatAddData(res.data.resultStructure)
          this.formatAddData(res.data.paramStructure)
        }
        resultStructure[0].children = res.data.resultStructure
        res.data.resultStructure = resultStructure
        res.data.resultPaneType = 'resultBody'
        res.data.resultDemo = JSON.parse(res.data.resultDemo || '{}')
        this.formData = res.data
        this.changeHttpType()
      })
    },
    formatAddData(dataList) {
      for (let i = 0, len = dataList.length; i < len; i++) {
        if (dataList[i].children && dataList[i].children.length) {
          this.formatAddData(dataList[i].children)
        }
      }
      if (
        dataList &&
        dataList[dataList.length - 1] &&
        dataList[dataList.length - 1].paramName
      ) {
        dataList.push({ ...this.paramTableItem, id: getUuid() })
      }
    },
    formatRemoveData(dataList) {
      for (let i = 0, len = dataList.length; i < len; i++) {
        if (dataList[i].children && dataList[i].children.length) {
          this.formatRemoveData(dataList[i].children)
        }
      }
      if (
        dataList &&
        dataList[dataList.length - 1] &&
        !dataList[dataList.length - 1].paramName
      ) {
        dataList.pop()
      }
    },
    handleTabClick(tab, event) {
      // if (this.formData.paramType !== 'body') {
      //   this.formData.paramStructure = 'form'
      // }
    },
    handleResultTabClick(tab, event) {
      // console.log(tab)
    },
    addChildren(tableData, row) {
      if (row.children && row.children.length) {
        row.children.push({ ...this.paramTableItem, id: getUuid() })
      } else {
        row.children = [{ ...this.paramTableItem, id: getUuid() }]
      }
      row.paramType = 'object'
    },
    findParentItem(paramTableData, id) {
      for (let i = 0, len = paramTableData.length; i < len; i++) {
        if (paramTableData[i].id === id) {
          return {
            data: paramTableData,
            childIndex: i
          }
        } else {
          if (paramTableData[i].children && paramTableData[i].children.length) {
            let item = this.findParentItem(paramTableData[i].children, id)
            if (item) {
              return item
            }
          }
        }
      }
    },
    addItem: debounce(function () {
      let parentItem = this.findParentItem(arguments[0], arguments[1].id)
      if (
        parentItem.data &&
        parentItem.data[parentItem.data.length - 1] &&
        parentItem.data[parentItem.data.length - 1].paramName
      ) {
        parentItem.data.splice(parentItem.childIndex + 1, 0, {
          ...this.paramTableItem,
          id: getUuid()
        })
      }
    }),
    // (tableData, row, index) {
    // console.log('1')
    // debounce(() => {
    //   console.log('input')
    //   let parentItem = this.findParentItem(tableData, row.id)
    //   if (
    //     parentItem.data &&
    //     parentItem.data[parentItem.data.length - 1] &&
    //     parentItem.data[parentItem.data.length - 1].paramName
    //   ) {
    //     parentItem.data.splice(parentItem.childIndex + 1, 0, {
    //       ...this.paramTableItem,
    //       id: getUuid()
    //     })
    //   }
    // }, 300, true)
    // if (row.paramName) {
    //   if (!row.dataFlag) {
    // row.dataFlag = true
    //   }
    // } else {
    //     row.dataFlag = false
    // }
    // if (row.paramName) {
    //   let parentItem = this.findParentItem(this.paramTableData, row.id)
    //   parentItem.splice(index + 1, 0, { ...this.paramTableItem, id: getUuid() })
    // }
    // },
    appendParam(tableData, row, index) {
      let parentItem = this.findParentItem(tableData, row.id)
      parentItem.data.splice(parentItem.childIndex, 0, {
        ...this.paramTableItem,
        id: getUuid()
        // dataFlag: true
      })
    },
    deleteParam(resultTableData, row, index) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        let parentItem = this.findParentItem(resultTableData, row.id)
        parentItem.data.splice(parentItem.childIndex, 1)
      })
    },
    saveAPI() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let postData = JSON.parse(JSON.stringify(this.formData))
        this.formatRemoveData(postData.paramStructure)
        this.formatRemoveData(postData.resultStructure[0].children)
        postData.resultStructure = postData.resultStructure[0].children

        // postData.resultStructure = JSON.stringify(postData.resultStructure[0].children)
        // postData.paramStructure = JSON.stringify(postData.paramStructure)
        // if (this.formData.paramType === 'header') {
        //   postData.paramStructure = 'form'
        //   postData.paramDemo = JSON.stringify(this.paramHeaderData)
        // } else if (this.formData.paramType === 'body') {
        //   postData.paramDemo = JSON.stringify(this.paramTableData)
        // } else if (this.formData.paramType === 'query') {
        //   postData.paramStructure = 'form'
        //   postData.paramDemo = JSON.stringify(this.paramQueryData)
        // }
        if (postData.id) {
          updateSysApi(postData).then(res => {
            showToast('更新成功')
            this.query(postData.id)
          })
        } else {
          saveSysApi(postData).then(res => {
            showToast('保存成功')
            this.query()
          })
        }
      })
    },
    getAPIDetail() {
      // console.log(this.$refs.treeRef.getCurrentNode())
    },
    addTree(data) {
      this.initForm(data && data.id)
      this.formReadonly = false
      this.activeNames = ['1', '2', '3', '4']
      this.jsonMode = 'code'
    },
    editTree(data) {
      if (data.module) {
        this.formReadonly = false
        this.activeNames = ['1', '2', '3', '4']
        this.jsonMode = 'code'
        this.getSysApi(data.id)
      } else {
        this.$refs.addMoudle.show({ type: 'edit', id: data.id })
      }
    },
    deleteTree(data) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(res => {
        if (data.module) {
          deleteSysApi({ id: data.id }).then(res => {
            // showToast('更新成功')
            this.query()
          })
        } else {
          deleteSysDictionary({ id: data.id }).then(res => {
            this.query()
          })
        }
      })
      // console.log(data.id)
    },
    changeHttpType() {
      if (
        this.formData.httpType === 'get' ||
        this.formData.httpType === 'delete'
      ) {
        this.formData.paramPaneType = 'query'
      } else if (
        this.formData.httpType === 'post' ||
        this.formData.httpType === 'put'
      ) {
        this.formData.paramPaneType = 'body'
      }
    },
    changeParamType() {
      if (this.formData.paramType === 'form') {
        this.formData.paramStructure.forEach((val, index) => {
          if (val.children && val.children.length) {
            val.children = []
            val.paramType = 'string'
          }
        })
      }
    },
    apply() {
      this.$refs.ApplyApi.show({ type: 'add', apiId: this.formData.id })
    }
    // editParamDemo(val) {
    //   console.log(val)
    //   console.log(this.formData.paramDemo)
    // }
  }
}
</script>

<style scoped lang="scss">
.el-input-group__prepend {
  .el-select {
    margin: 0;
  }
  .el-select:first-child {
    margin-left: -20px;
  }
  .el-select:last-child {
    margin-right: -20px;
  }
}
.actor-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-transfer-panel {
    width: 200px;
  }
}

.main-container {
  display: flex;
  flex-direction: row;

  .org-tree-container {
    width: 280px;
    margin-right: 20px;
    .filter-opt {
      display: flex;
      .ltw-input {
        margin-right: 10px;
      }
    }
  }

  .content-container {
    width: calc(100% - 300px);

    :deep(.jsoneditor-poweredBy) {
      display: none;
    }
    .el-card {
      position: relative;
      .apply-btn {
        z-index: 1;
        position: absolute;
        top: 10px;
        right: 20px;
      }
    }
  }
  .footer {
    text-align: center;
  }
  .custom-tree-node {
    position: relative;
    width: calc(100% - 28px);
    &:hover {
      .tree-opt {
        opacity: 1;
      }
    }
    .tree-opt {
      position: absolute;
      right: 5px;
      opacity: 0;
      transition: all 0.3s;
      padding-left: 10px;
      .el-link {
        margin-right: 5px;
      }
    }
  }
}
.api-form {
  :deep(.ltw-input-group__prepend) {
    padding: 0;
    .el-select {
      margin: 0;
    }
  }

  :deep(.el-collapse-item__header) {
    padding: 10px 20px;
    border-left: 3px solid rgb(0, 120, 90);
    background: rgb(248, 248, 250);
    margin-top: 20px;
  }
  .el-tabs {
    padding-left: 10px;
  }
  .el-table {
    margin-top: 0;
    .el-link {
      font-size: 12px;
      padding: 0 10px;
      &:not(:last-child) {
        border-right: 1px solid rgb(232, 232, 232);
      }
    }
    :deep(.cell) {
      display: flex;
    }
  }
}
</style>
