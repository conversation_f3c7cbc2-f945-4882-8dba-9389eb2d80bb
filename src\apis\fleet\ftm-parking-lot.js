import { httpDelete, httpGet, httpPut, httpPost } from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveFtmParkingLot = (data = {}, params = {}) =>
  httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    data,
    params
  })
export const updateFtmParkingLot = (data = {}, params = {}) =>
  httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    data,
    params
  })
export const deleteFtmParkingLot = (params = {}) =>
  httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    params
  })
export const listFtmParkingLot = (params = {}, unloading = true) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots',
    params,
    unloading
  })
export const listFtmParkingLotSelection = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/selections',
    params
  })
export const pageFtmParkingLot = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/page',
    params
  })
export const getFtmParkingLot = id =>
  httpGet({ url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lots/' + id })
export const getFtmParkingLotFieldItems = (params = {}) =>
  httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/fleet/ftm_parking_lot_field_items',
    params
  })
