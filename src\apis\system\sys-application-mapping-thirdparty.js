import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveSysApplicationMappingThirdparty = (data = {}, params = {}) => httpPost({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys', data, params})
export const updateSysApplicationMappingThirdparty = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys', data, params})
export const deleteSysApplicationMappingThirdparty = (params = {}) => httpDelete({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys', params})
export const listSysApplicationMappingThirdparty = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys', params})
export const listSysApplicationMappingThirdpartySelection = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys/selections', params})
export const pageSysApplicationMappingThirdparty = (params = {}) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys/page', params})
export const getSysApplicationMappingThirdparty = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootSystemUrl + '/sys_application_mapping_thirdpartys/' + id})
