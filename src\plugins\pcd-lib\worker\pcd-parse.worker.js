import { parsePCD } from '../parse-pcd';
import { getTransferList } from './get-transfer-list';

const ctx = self;

ctx.addEventListener('message', async (data) => {
    let transfer = [];
    let result, error;
    try {
        result = parsePCD(data.data.data);
        transfer = getTransferList(result);
    } catch (e) {
        error = e.message;
    }

    ctx.postMessage({ key: data.data.key, data: result, error }, transfer);
});
