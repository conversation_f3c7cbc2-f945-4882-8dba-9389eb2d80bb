<template>
  <el-dialog
    :title="form.vehicleInfo.vin"
    v-model="dialogVisible"
    @close="dialogClosed"
    @open="dialogOpened"
    width="800px"
    append-to-body
    class="vehicle-statistic-detail"
  >
    <div class="vehicle-detail">
      <div class="self-el-descriptions">
        <el-descriptions border :column="2">
          <!-- <el-descriptions-item
            :min-width="80"
            :width="80"
            :label="$t('车型')"
            >{{
              form.vehicleInfo.brand + form.vehicleInfo.model
            }}</el-descriptions-item
          > -->
          <el-descriptions-item :min-width="80" :width="80" :label="$t('车型版本')">
            <template>
              {{ (form.vehicleInfo.name || 'N/A') + '(' + (form.vehicleInfo.version || 'N/A') + ')' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :min-width="80" :width="80" :label="$t('出厂车架号')"
            >{{ form.vehicleInfo.factoryVin }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('车牌')">{{ form.vehicleInfo.license }}</el-descriptions-item>
          <el-descriptions-item :min-width="80" :width="80" :label="$t('责任人')"
            >{{ form.vehicleInfo.keeperEmpName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('车端设备')">
            <el-link
              v-if="form.vehicleInfo.tagEquipBrand && form.vehicleInfo.tagEquipCode"
              type="primary"
              :underline="false"
              >{{ form.vehicleInfo.tagEquipBrand + '-' + form.vehicleInfo.tagEquipCode }}
            </el-link>
          </el-descriptions-item>

          <el-descriptions-item :min-width="80" :width="80" :label="$t('状态')">
            <el-tag :type="form.vehicleInfo.status === 'working' ? 'danger' : 'success'"
              >{{ form.vehicleInfo.statusName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('轴距')">{{ form.vehicleInfo.wheelBase }}</el-descriptions-item>
          <el-descriptions-item v-if="form.vehicleInfo.status === 'free'" :label="$t('当前位置')">
            <el-link
              v-if="!(form.vehicleInfo.longitude && form.vehicleInfo.latitude)"
              disabled
              style="font-size: 20px"
              type="success"
              :underline="false"
            >
              <ltw-icon icon-code="el-icon-add-location"></ltw-icon>
            </el-link>
            <el-popover v-else :width="500" trigger="click">
              <template #reference>
                <el-link
                  style="font-size: 20px"
                  @click="getPosition(form.vehicleInfo)"
                  type="success"
                  :underline="false"
                >
                  <ltw-icon icon-code="el-icon-add-location"></ltw-icon>
                </el-link>
              </template>
              <fleet-position-map-overview style="height: 400px; width: 100%" id="mapContent" ref="mapContent" />
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item v-else :label="$t('当前任务')">
            <el-link @click="getViewTaskDetail(form.currentTaskInfo)" type="primary" :underline="false"
              >{{ form.currentTaskInfo.taskName }}
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
        <div class="self-img">
          <el-image
            :src="
              form.vehicleInfo.photoList && form.vehicleInfo.photoList.length
                ? downloadUrl + form.vehicleInfo.photoList[0].id + token
                : ''
            "
            :preview-src-list="[
              form.vehicleInfo.photoList && form.vehicleInfo.photoList.length
                ? downloadUrl + form.vehicleInfo.photoList[0].id + token
                : ''
            ]"
            fit="scale-down"
          >
            <template #error>
              <div class="image-slot">
                <ltw-icon icon-code="el-icon-picture"></ltw-icon>
              </div>
            </template>
          </el-image>
          <!-- <img :src="form.vehicleInfo.photoList && form.vehicleInfo.photoList.length ? downloadUrl + form.vehicleInfo.photoList[0].id + token : require('@/assets/images/test-vehicle.png')" :alt="$t('测试车辆')" /> -->
        </div>
      </div>
      <el-descriptions border :column="3">
        <el-descriptions-item :min-width="80" :width="80" :span="3" :label="$t('传感器')">
          <el-table max-height="200px" width="100%" :data="form.vehicleMappingModalityVOS">
            <el-table-column header-align="left" align="left" :label="$t('传感器')" prop="modalityName" />
            <el-table-column header-align="left" align="left" :label="$t('传感器类型')" prop="sensorTypeName" />
            <el-table-column header-align="left" align="left" :label="$t('供应商')" prop="supplierName" />
            <el-table-column header-align="left" align="left" :label="$t('型号')" prop="model" />
            <el-table-column header-align="left" align="left" :label="$t('规格')" prop="specification" />
            <el-table-column
              header-align="left"
              align="left"
              :label="$t('安装') + $t('位置')"
              prop="installationPositionName"
            />
            <el-table-column header-align="left" align="left" :label="$t('指导位置x') + 'mm'" prop="positionX" />
            <el-table-column header-align="left" align="left" :label="$t('指导位置y') + 'mm'" prop="positionY" />
            <el-table-column header-align="left" align="left" :label="$t('指导位置z') + 'mm'" prop="positionZ" />
            <el-table-column header-align="left" align="left" :label="$t('距地高度') + 'mm'" prop="height" />
            <el-table-column header-align="left" align="left" :label="$t('图片中坐标')" prop="imageCoordinate" />
            <el-table-column header-align="left" align="left" :label="$t('msop')" prop="msop" />
            <el-table-column header-align="left" align="left" :label="$t('difop')" prop="difop" />
            <!-- <el-table-column
              header-align="left"
              align="left"
              :label="$t('帧同步偏移值' + '(ms)')"
              prop="pcdJpgOffset"
            />
            <el-table-column
              header-align="left"
              align="left"
              :label="$t('间隔差的基准值') + '(ms)'"
              prop="intervalDif"
            /> -->
            <el-table-column header-align="left" align="left" :label="$t('备注')" prop="remark" />
          </el-table>
        </el-descriptions-item>
        <!-- <el-descriptions-item
          :min-width="80"
          :width="80"
          :span="3"
          :label="$t('标定参数')"
        >
          <el-table max-height="200px" :data="modalityData">
            <el-table-column prop="type" :label="$t('参数类型')" />
            <el-table-column prop="version" :label="$t('版本')" />
            <el-table-column prop="activationDate" :label="$t('生效日期')" />
            <el-table-column prop="description" :label="$t('说明')" />
            <el-table-column
              header-align="left"
              align="left"
              :label="$t('操作')"
              width="200"
            >
              <template #default="scope">
                <el-button-group>
                  <el-tooltip
                    effect="dark"
                    :content="$t('下载')"
                    placement="top"
                    :enterable="false"
                  >
                    <el-button
                      type="primary"
                      @click="downloadCalibrationParams(scope.row)"
                    >
                      <ltw-icon icon-code="el-icon-download"></ltw-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item> -->
      </el-descriptions>
      <el-tabs v-model="activeName" type="card" class="tag-tabs" @tab-change="handleClick">
        <el-tab-pane :label="$t('标签统计')" name="tagStatistics">
          <div class="tag-content">
            <div class="tag-form">
              <dictionary-selection
                style="width: 100px"
                v-model="tagStatisticsForm.dateType"
                dictionaryType="date_type_range"
                :placeholder="$t('请选择')"
              />
              <el-date-picker
                clearable
                @change="queryDayTagHistogram"
                value-format="YYYY-MM-DD"
                v-model="tagStatisticsForm.date"
                :type="
                  tagStatisticsForm.dateType === 'day' || tagStatisticsForm.dateType === 'range'
                    ? 'date'
                    : tagStatisticsForm.dateType
                "
                :placeholder="$t('选择日期')"
              />
              <span v-if="tagStatisticsForm.dateType === 'range'">
                To

                <el-date-picker
                  clearable
                  @change="queryDayTagHistogram"
                  value-format="YYYY-MM-DD"
                  v-model="tagStatisticsForm.endTime"
                  :type="
                    tagStatisticsForm.dateType === 'day' || tagStatisticsForm.dateType === 'range'
                      ? 'date'
                      : tagStatisticsForm.dateType
                  "
                  :placeholder="$t('结束日期')"
              /></span>
              <el-link type="primary" @click="chooseTag()">{{ $t('选择标签') }}</el-link>
            </div>
            <div class="tab-chart">
              <el-tabs v-model="tagStatisticsForm.tagType" @tab-change="queryDayTagHistogram" tab-position="left">
                <el-tab-pane
                  v-for="item in eventStatuses"
                  :key="item.code"
                  :label="$t(item.name)"
                  :name="item.code"
                ></el-tab-pane>
              </el-tabs>
              <div id="tag-statistics"></div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('标签趋势')" name="tagTrend">
          <div class="tag-content">
            <div class="tag-form">
              <dictionary-selection
                style="width: 100px"
                v-model="tagTrendForm.dateType"
                dictionaryType="date_type"
                :placeholder="$t('请选择')"
              />
              <el-date-picker
                clearable
                @change="queryTagTrendLineChartAndPieChart"
                value-format="YYYY-MM-DD"
                v-model="tagTrendForm.startTime"
                :type="tagTrendForm.dateType === 'day' ? 'date' : tagTrendForm.dateType"
                :placeholder="$t('选择日期')"
              />
              To

              <el-date-picker
                clearable
                @change="queryTagTrendLineChartAndPieChart"
                value-format="YYYY-MM-DD"
                v-model="tagTrendForm.endTime"
                :type="tagTrendForm.dateType === 'day' ? 'date' : tagTrendForm.dateType"
                :placeholder="$t('结束日期')"
              />
              <el-link type="primary" @click="chooseTag()">{{ $t('选择标签') }}</el-link>
            </div>
            <div class="tab-chart">
              <el-tabs
                v-model="tagTrendForm.tagType"
                @tab-change="queryTagTrendLineChartAndPieChart"
                tab-position="left"
              >
                <el-tab-pane
                  v-for="item in eventStatuses"
                  :key="item.code"
                  :label="$t(item.name)"
                  :name="item.code"
                ></el-tab-pane>
              </el-tabs>
              <div id="tag-trend"></div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <bs-tag-group-drawer
      @drawerClick="confirmDistributeTags"
      :drawerVisible="tagDistributeDrawerVisible"
      :rowTagList="tagList"
    ></bs-tag-group-drawer>
    <disassemble-requirements ref="DisassembleRequirements" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel()" id="cancel">{{ $t('取消') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import util from '@/plugins/util'
import BsTagGroupDrawer from '@/components/basic/BsTagGroupDrawer.vue'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import DisassembleRequirements from '@/pages/dataCollect/dialog/DisassembleRequirements'
import { queryDayTagHistogram, queryTagTrendLineChartAndPieChart } from '@/apis/fleet/fleet-management'
import { dateUtils, showToast } from '@/plugins/util'
import { i18n } from '@/plugins/lang'
import GLB_CONFIG from '@/plugins/glb-constant'
import FleetPositionMapOverview from '@/components/map/FleetPositionMapOverview'
import {
  ElLink,
  ElDescriptions,
  ElDescriptionsItem,
  ElCard,
  ElDialog,
  ElTableColumn,
  ElTable,
  ElButton,
  ElTooltip,
  ElButtonGroup,
  ElDatePicker,
  ElTabPane,
  ElTabs,
  ElSelect,
  ElOption,
  ElPopover,
  ElTag,
  ElImage
} from 'element-plus'
import LtwIcon from '@/components/base/LtwIcon'
import * as $echarts from 'echarts'
import { getVehicleTask } from '@/apis/fleet/bs-vehicle'

const defaultform = {
  vehicleInfo: {},
  currentTaskInfo: {}
}
export default {
  name: 'VehicleStatisticDetail',
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      form: Object.assign({}, defaultform),
      activeName: 'tagStatistics',
      eventStatuses: [
        {
          code: 'continuous',
          name: '持续性(时长)'
        },
        {
          code: 'continuous time',
          name: '持续性(次数)'
        },
        {
          code: 'transient',
          name: '事件'
        }
      ],
      tagStatisticsForm: {
        dateType: 'day',
        date: '',
        endTime: '',
        vehicleIdList: [],
        tagIdList: [],
        tagType: 'continuous'
      },
      tagTrendForm: {
        dateType: 'day',
        startTime: '',
        endTime: '',
        vehicleIdList: [],
        tagIdList: [],
        tagType: 'continuous'
      },
      tagList: [],
      tagDistributeDrawerVisible: false,
      modalityData: [],
      vehicleMappingModalityVOS: [],
      dayTagList: [],
      trendTagList: [],
      $t: i18n.global.t,
      //文件
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      token: '?token=' + util.getToken(),
      vehicleId: '',
      formatSecToDate: dateUtils.formatSecToDate
    }
  },
  components: {
    BsTagGroupDrawer,
    DictionarySelection,
    ElLink,
    ElDescriptions,
    ElDescriptionsItem,
    ElCard,
    ElDialog,
    ElTableColumn,
    ElTable,
    ElButton,
    ElTooltip,
    ElButtonGroup,
    ElDatePicker,
    ElTabPane,
    ElTabs,
    ElSelect,
    ElOption,
    LtwIcon,
    DisassembleRequirements,
    ElPopover,
    ElTag,
    ElImage,
    FleetPositionMapOverview
  },
  methods: {
    show(row) {
      this.dialogVisible = true
      if (row.id) {
        this.tagStatisticsForm.vehicleIdList = [row.id]
        this.tagTrendForm.vehicleIdList = [row.id]
        this.tagStatisticsForm.date =
          dateUtils.parseTime(new Date().getTime() - 60 * 60 * 24 * 1000, '{y}-{m}-{d}') + ' 00:00:00'

        // 本月第一天
        let currentMonthFirstDay = dateUtils.parseTime(new Date().getTime(), '{y}-{m}-{d}').substr(0, 8) + '01'
        // 上月最后一天
        this.tagTrendForm.endTime =
          dateUtils.parseTime(new Date(currentMonthFirstDay).getTime() - 60 * 60 * 24 * 1000, '{y}-{m}-{d}') +
          ' 23:59:59'
        //上月第一天
        this.tagTrendForm.startTime = this.tagTrendForm.endTime.substr(0, 8) + '01' + ' 00:00:00'
        this.handleClick()
        this.getVehicleTask(row.id)
      }
    },
    getVehicleTask(id) {
      getVehicleTask(id).then(res => {
        this.form = res.data
        // this.pageFtmCalibrationParameter(res.data.vehicleInfo.id)
        // this.pageFtmVehicleModalityIssue(res.data.vehicleInfo.id)
        // this.dialogTitle = this.$t('7185C')
        // 格式化响应数据
        // let responseResult = JSON.parse(
        //   JSON.stringify(defaultform.resultStructure)
        // )
        // responseResult[0].children = JSON.parse(res.data.responseResult)
        // res.data.resultPaneType = 'resultBody'
        // res.data.responseResult = responseResult
        // res.data.responseDemo = JSON.parse(res.data.responseDemo || '{}')
        // //请求数据
        // res.data.requestParamDemo = JSON.parse(
        //   res.data.requestParamDemo || '{}'
        // )
        // res.data.requestParam = JSON.parse(res.data.requestParam)
        // this.form = res.data
      })
    },
    // pageFtmCalibrationParameter(id) {
    //   pageFtmCalibrationParameter({ vehicleId: id }).then(res => {
    //     this.modalityData = res.data.records
    //   })
    // },
    // pageFtmVehicleModalityIssue(id) {
    //   let postData = {
    //     current: 1,
    //     size: 999,
    //     vehicleId: id
    //   }
    //   pageFtmVehicleModalityIssue(postData).then(res => {
    //     this.vehicleMappingModalityVOS = res.data.records
    //   })
    // },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    initForm() {
      this.form = Object.assign({}, defaultform)
    },
    handleClick() {
      if (this.activeName === 'tagStatistics') {
        this.$nextTick(() => {
          this.queryDayTagHistogram()
        })
      } else if (this.activeName === 'tagTrend') {
        this.$nextTick(() => {
          this.queryTagTrendLineChartAndPieChart()
        })
      }
    },
    chooseTag() {
      this.tagList = this.activeName === 'tagStatistics' ? this.dayTagList : this.trendTagList
      this.tagDistributeDrawerVisible = true
    },
    confirmDistributeTags(data) {
      if (data && data.tagList && data.tagList.length) {
        this.tagList = []
        data.tagList.forEach(tagGroup => {
          this.saveCheckedTagList(tagGroup)
        })
        if (this.activeName === 'tagStatistics') {
          this.dayTagList = JSON.parse(JSON.stringify(this.tagList))
          this.tagStatisticsForm.tagIdList = this.tagList.map(val => val.id)
          this.queryDayTagHistogram()
        } else if (this.activeName === 'tagTrend') {
          this.trendTagList = JSON.parse(JSON.stringify(this.tagList))
          this.tagTrendForm.tagIdList = this.tagList.map(val => val.id)
          this.queryTagTrendLineChartAndPieChart()
        }
      }
      this.tagDistributeDrawerVisible = false
    },
    saveCheckedTagList(group) {
      if (group.checkedTagIdList && group.checkedTagIdList.length > 0) {
        group.checkedTagIdList.forEach(tagId => {
          this.tagList.push(group.tagMap[tagId])
        })
      }
      if (group.children && group.children.length > 0) {
        group.children.forEach(subGroup => {
          this.saveCheckedTagList(subGroup)
        })
      }
    },
    formatTime(type, time) {
      let startTime, endTime
      if (time) {
        startTime = dateUtils.parseTime(new Date(time), '{y}-{m}-{d}') + ' 00:00:00'

        let year = new Date(time).getFullYear()
        let month = new Date(time).getMonth() + 1
        if (type === 'day') {
          endTime = dateUtils.parseTime(new Date(time), '{y}-{m}-{d}') + ' 23:59:59'
        } else if (type === 'week') {
          endTime = dateUtils.parseTime(new Date(time).getTime() + 7 * 60 * 60 * 24 * 1000, '{y}-{m}-{d}') + ' 23:59:59'
        } else if (type === 'month') {
          let nextMonthTime = new Date(year, month, 1)
          endTime = dateUtils.parseTime(nextMonthTime.getTime() - 60 * 60 * 24 * 1000, '{y}-{m}-{d}') + ' 23:59:59'
        } else if (type === 'year') {
          endTime =
            dateUtils.parseTime(new Date(year + 1, 0, 1).getTime() - 60 * 60 * 24 * 1000, '{y}-{m}-{d}') + ' 23:59:59'
        }
      } else {
      }
      return { startTime, endTime }
    },
    queryDayTagHistogram() {
      const _this = this
      let postData = { ...this.tagStatisticsForm }
      if (this.tagStatisticsForm.dateType === 'range') {
        postData.startTime = postData.date
      } else {
        let time = this.formatTime(postData.dateType, postData.date)
        postData.startTime = time.startTime
        postData.endTime = time.endTime
      }
      delete postData.date
      queryDayTagHistogram(postData).then(res => {
        let legend = [],
          xAxisData = [],
          seriesData = []
        res.data.perDayTagHistogramData.forEach((timeTypeValue, index) => {
          let data = []
          timeTypeValue.data.forEach(val => {
            if (!index) {
              xAxisData.push(val.name)
            }
            data.push(val.value)
          })
          legend.push(timeTypeValue.type)
          seriesData.push({
            data: data,
            name: timeTypeValue.type,
            type: 'bar',
            stack: 'total',
            barMaxWidth: 100,
            emphasis: {
              focus: 'series'
            }
          })
        })
        let option = {
          tooltip: {
            confine: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter(params) {
              let str = params[0].axisValue + '<br />'
              params.forEach((val, index) => {
                if (_this.tagStatisticsForm.tagType === 'continuous') {
                  let times = _this.formatSecToDate(val.value)
                  str += val.marker + val.seriesName + ': ' + times.time + '<br />'
                } else {
                  str += val.marker + val.value + ' ' + i18n.global.t('次') + '<br />'
                }
              })
              return str
            }
          },
          legend: {
            type: 'scroll',
            left: 'center',
            top: 'top',
            data: legend
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: xAxisData
              // data: ['Direct', 'Email', 'Union Ads', 'Video Ads', 'search Ads']
              // data: legend
            }
          ],

          yAxis: [
            {
              type: 'value',
              name:
                seriesData &&
                seriesData.length &&
                i18n.global.t('单位') +
                  '（' +
                  (_this.tagStatisticsForm.tagType === 'continuous' ? 's' : i18n.global.t('次')) +
                  '）',
              nameTextStyle: {
                color: '#aaa'
                // nameLocation: 'end'
              },
              position: 'left'
            }
          ],
          series: seriesData
        }

        let myChart
        if (myChart != null && myChart != '' && myChart != undefined) {
          myChart.dispose()
        }
        myChart = $echarts.init(document.getElementById('tag-statistics'), 'light')
        myChart.setOption(option, true)
      })
      // this.sensorAbnormalTimes = Number(Math.random() * 10)
    },
    queryTagTrendLineChartAndPieChart() {
      const _this = this
      let postData = { ...this.tagTrendForm }
      let startTime = this.formatTime(this.tagTrendForm.dateType, this.tagTrendForm.startTime)
      let endTime = this.formatTime(this.tagTrendForm.dateType, this.tagTrendForm.endTime)
      postData.startTime = startTime.startTime
      postData.endTime = endTime.endTime
      queryTagTrendLineChartAndPieChart(postData).then(res => {
        let legend = [],
          xAxisData = [],
          seriesData = []
        res.data.tagTrendLineChartData.forEach((timeTypeValue, index) => {
          let data = []
          timeTypeValue.data.forEach(val => {
            if (!index) {
              xAxisData.push(val.times)
            }
            data.push(val.value)
          })
          legend.push(timeTypeValue.tagName)
          seriesData.push({
            data: data,
            name: timeTypeValue.tagName,
            type: 'line'
          })
        })
        let option = {
          // title: {
          //   text: 'Stacked Line'
          // },
          tooltip: {
            confine: true,
            trigger: 'axis',
            formatter(params) {
              let str = params[0].axisValue + '<br />'
              params.forEach((val, index) => {
                if (_this.tagTrendForm.tagType === 'continuous') {
                  let times = _this.formatSecToDate(val.value)
                  str += val.marker + val.seriesName + ': ' + times.time + '<br />'
                } else {
                  str += val.marker + val.value + ' ' + i18n.global.t('次') + '<br />'
                }
              })
              return str
            }
          },
          legend: {
            type: 'scroll',
            left: 'center',
            top: 'top',
            data: legend
            // data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
          },
          grid: {
            left: '3%',
            right: '7%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData
            // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          },

          yAxis: [
            {
              type: 'value',
              name:
                seriesData &&
                seriesData.length &&
                i18n.global.t('单位') +
                  '（' +
                  (_this.tagTrendForm.tagType === 'continuous' ? 's' : i18n.global.t('次')) +
                  '）',
              nameTextStyle: {
                color: '#aaa'
                // nameLocation: 'end'
              },
              position: 'left'
            }
          ],
          series: seriesData
        }
        let myChart
        if (myChart != null && myChart != '' && myChart != undefined) {
          myChart.dispose()
        }
        myChart = $echarts.init(document.getElementById('tag-trend'), 'light')
        myChart.setOption(option, true)
      })

      // this.sensorAbnormalTimes = Number(Math.random() * 10)
    },
    //文件
    downloadCalibrationParams(row) {
      window.location.href = this.downloadUrl + row.fileId + '?token=' + util.getToken()
    },
    getViewTaskDetail(row) {
      this.$refs.DisassembleRequirements.show({ type: 'view', id: row.reqId })
    },
    getPosition(row) {
      if (row.longitude && row.latitude) {
        this.$nextTick(() => {
          this.$refs.mapContent.show({
            data: [row],
            disabledScrollWheelZoom: true
          })
        })
      } else {
        showToast('暂无位置信息', 'warning')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.vehicle-detail {
  .el-descriptions {
    width: 100%;

    :deep(.el-descriptions__cell) {
      font-size: 12px;
      font-weight: 400;
      padding: 0px 11px;
      white-space: nowrap;

      .el-table {
        margin: 0;
        width: 664px;
      }
    }

    :deep(.el-descriptions__label) {
      font-weight: 600;
    }

    :deep(.el-descriptions__table) {
      width: 100%;
    }
  }

  .self-el-descriptions {
    display: flex;

    .el-descriptions {
      width: 75%;
    }

    .self-img {
      height: 100px;
      width: 25%;
      display: flex;
      align-items: center;
      flex-grow: 1;

      .el-image {
        height: 100%;
        // height: 74px;
        width: 100%;
        font-size: 30px;
        background: var(--el-fill-color-light);
        color: var(--el-text-color-secondary);

        .image-slot {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
      }
    }
  }

  .tag-tabs {
    margin-top: 20px;

    .tag-content {
      display: flex;
      flex-direction: column;

      .tag-form {
        .el-select,
        .el-date-editor,
        .el-link {
          margin: 0 10px;
        }

        margin-bottom: 10px;
      }

      .tab-chart {
        display: flex;
        flex-direction: row;

        .el-tab {
          width: 90px;
        }

        #tag-statistics,
        #tag-trend {
          width: calc(100% - 90px);
          height: 300px;
        }
      }
    }
  }
}

// .el-tabs {
//   padding-left: 10px;
// }
// .el-table {
//   margin-top: 0;
//   .el-link {
//     font-size: 12px;
//     padding: 0 10px;
//     &:not(:last-child) {
//       border-right: 1px solid rgb(232, 232, 232);
//     }
//   }
//   :deep(.cell) {
//     display: flex;
//   }
// }
</style>
