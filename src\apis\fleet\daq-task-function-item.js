import {httpDelete, httpGet, httpPut, httpPost} from '@/plugins/http'
import GLB_CONFIG from '@/plugins/glb-constant'

export const saveDaqTaskFunctionItem = (data = {}, params = {}) => httpPost({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items',
    data,
    params
})
export const updateDaqTaskFunctionItem = (data = {}, params = {}) => httpPut({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items',
    data,
    params
})
export const deleteDaqTaskFunctionItem = (params = {}) => httpDelete({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items',
    params
})
export const listDaqTaskFunctionItem = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items',
    params
})
export const listDaqTaskFunctionItemSelection = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items/selections',
    params
})
export const pageDaqTaskFunctionItem = (params = {}) => httpGet({
    url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items/page',
    params
})
export const getDaqTaskFunctionItem = (id) => httpGet({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items/' + id})

export const reorderFunctionItem = (data = {}, params = {}) => httpPut({url: GLB_CONFIG.devUrl.serviceSiteRootUrl + '/ftm/daq_task_function_items/reorder', data, params})