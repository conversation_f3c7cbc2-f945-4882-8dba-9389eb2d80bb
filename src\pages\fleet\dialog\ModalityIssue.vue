<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="cancel"
    @open="dialogOpened"
    append-to-body
    draggable
  >
    <el-button type="primary" @click="addCalibrationParams">
      {{ $t('添加') }}
    </el-button>
    <el-table
      :data="calibrationParamsPageData.records"
      stripe
      ref="calibrationParamsRef"
      :row-key="getRowKeys"
    >
      <el-table-column type="expand" width="0">
        <template #default="props">
          <el-form
            :model="calibrationParamsEditFormData"
            :rules="formRules"
            :ref="'calibrationParamsEditFormDataRef' + props.$index"
            label-width="95px"
            v-show="calibrationParamsEditFormVisible == true"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('维修方')" prop="supplierId">
                  <el-select
                    v-model="calibrationParamsEditFormData.supplierId"
                    filterable
                    :placeholder="$t('请选择维修方')"
                    clearable
                    @change="getSupplierName"
                    id="supplierId"
                  >
                    <el-option
                      v-for="item in modalityData"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      id="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item :label="$t('传感器')" prop="modality">
                  <el-select
                    v-model="calibrationParamsEditFormData.modality"
                    filterable
                    :placeholder="$t('请选择传感器')"
                    clearable
                  >
                    <el-option
                      v-for="item in modalityData"
                      :key="item.id"
                      :label="item.name"
                      :value="item.code"
                    >
                      <span style="float: left">{{ item.name }}</span>
                      <span
                        style="
                          float: right;
                          color: var(--el-text-color-secondary);
                          font-size: 10px;
                        "
                        >{{ item.code }}</span
                      >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
              <!-- <el-col :span="12">
                <el-form-item :label="$t('状态')" prop="status">
                  <dictionary-selection
                    v-model="calibrationParamsEditFormData.status"
                    clearable
                    dictionaryType="modality_abnormal"
                    placeholder="please select status"
                    filterable
                  /> </el-form-item
              ></el-col> -->
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('维修开始日期')" prop="occurTime">
                  <el-date-picker
                    v-model="calibrationParamsEditFormData.occurTime"
                    type="date"
                    :disabled-date="disabledOccurTime"
                    :placeholder="$t('选择维修开始日期')"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('维修完成日期')" prop="finishTime">
                  <el-date-picker
                    v-model="calibrationParamsEditFormData.finishTime"
                    type="date"
                    :disabled-date="disabledFinishTime"
                    :placeholder="$t('选择维修完成日期')"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('维修内容')" prop="description">
                  <ltw-input
                    type="textarea"
                    :limit-size="250"
                    v-model="calibrationParamsEditFormData.description"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item :label="$t('备注')" prop="remark">
                  <ltw-input
                    type="textarea"
                    :limit-size="250"
                    v-model="calibrationParamsEditFormData.remark"
                  /> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="occurTime"
        width="120"
        :label="$t('维修开始日期')"
      >
        <template #default="scope">
          <el-tag>
            {{
              dateUtils.parseTime(new Date(scope.row.occurTime), '{y}-{m}-{d}')
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        width="120"
        prop="finishTime"
        :label="$t('维修结束日期')"
      >
        <template #default="scope">
          <el-tag>{{
            scope.row.finishTime
              ? dateUtils.parseTime(
                  new Date(scope.row.finishTime),
                  '{y}-{m}-{d}'
                )
              : '-'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="repairDuration"
        :label="$t('维修时长')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="description"
        show-overflow-tooltip
        :label="$t('维修内容')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="supplierName"
        :label="$t('维修方')"
      >
      </el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        prop="remark"
        show-overflow-tooltip
        :label="$t('备注')"
      ></el-table-column>
      <el-table-column
        header-align="left"
        align="left"
        :label="$t('操作')"
        fixed="right"
        width="120"
      >
        <template #default="scope">
          <el-button-group>
            <el-tooltip
              effect="dark"
              :content="$t('保存')"
              placement="top"
              :enterable="false"
              v-if="!scope.row.editing"
            >
              <el-button
                type="success"
                @click="saveCalibrationParams(scope.row, scope.$index)"
              >
                <ltw-icon icon-code="el-icon-finished"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="$t('编辑')"
              placement="top"
              :enterable="false"
              v-if="scope.row.editing"
            >
              <el-button
                type="warning"
                @click="editCalibrationParams(scope.row)"
              >
                <ltw-icon icon-code="el-icon-edit"></ltw-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="$t('删除')"
              placement="top"
              :enterable="false"
              v-if="!formReadonly"
            >
              <el-button
                type="danger"
                @click="
                  singleRemoveCalibrationParams(
                    scope.row,
                    scope.$index,
                    calibrationParamsPageData.records
                  )
                "
              >
                <ltw-icon icon-code="el-icon-delete"></ltw-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
      <template #append>
        <el-form
          style="padding-top: 10px;"
          :model="calibrationParamsFormData"
          :rules="formRules"
          ref="calibrationParamsFormRef"
          label-width="95px"
          v-show="calibrationParamsFormVisible == true"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('维修方')" prop="supplierId">
                <el-select
                  v-model="calibrationParamsFormData.supplierId"
                  filterable
                  :placeholder="$t('请选择维修方')"
                  clearable
                  @change="getSupplierName"
                  id="supplierId"
                >
                  <el-option
                    v-for="item in modalityData"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    id="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('维修开始日期')" prop="occurTime">
                <el-date-picker
                  v-model="calibrationParamsFormData.occurTime"
                  type="date"
                  :disabled-date="disabledOccurTime"
                  :placeholder="$t('选择维修开始日期')"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('维修完成日期')" prop="finishTime">
                <el-date-picker
                  v-model="calibrationParamsFormData.finishTime"
                  type="date"
                  :disabled-date="disabledFinishTime"
                  :placeholder="$t('选择维修完成日期')"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('维修内容')" prop="description">
                <ltw-input
                  type="textarea"
                  :limit-size="250"
                  v-model="calibrationParamsFormData.description"
                /> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item :label="$t('备注')" prop="remark">
                <ltw-input
                  type="textarea"
                  :limit-size="250"
                  v-model="calibrationParamsFormData.remark"
                /> </el-form-item
            ></el-col>
          </el-row>
          <el-form-item>
            <el-button @click="cancelCalibrationParamsForm">{{
              $t('取消')
            }}</el-button>
            <el-button type="primary" @click="confirm">{{
              $t('保存')
            }}</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-table>
    <el-pagination
      background
      @size-change="handlecalibrationSizeChange"
      @current-change="handlecalibrationCurrentChange"
      :current-page="calibrationParamsQueryParam.current"
      :page-sizes="[5, 10, 20, 30]"
      :page-size="calibrationParamsQueryParam.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="calibrationParamsPageData.total"
    >
    </el-pagination>
    <template #footer>
      <el-button @click="cancel()">{{ $t('关闭') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import BASE_CONSTANT from '@/plugins/constants/base-constant'
import { i18n } from '@/plugins/lang'
import util, { dateUtils, showConfirmToast } from '@/plugins/util.js'
import GLB_CONFIG from '@/plugins/glb-constant'
import LtwInput from '@/components/base/LtwInput'
import DictionarySelection from '@/components/system/DictionarySelection.vue'
import EmployeeSelection from '@/components/system/EmployeeSelection.vue'
import LtwIcon from '@/components/base/LtwIcon'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElRow,
  ElCol,
  ElButtonGroup,
  ElTooltip,
  ElInputNumber,
  ElDatePicker,
  ElUpload,
  ElPagination,
  ElOption,
  ElSelect
} from 'element-plus'
import { getBsVehicle } from '@/apis/fleet/bs-vehicle'
import { listSysRoleOrg } from '@/apis/system/sys-role-org'
import {
  saveFtmVehicleModalityIssue,
  updateFtmVehicleModalityIssue,
  deleteFtmVehicleModalityIssue,
  pageFtmVehicleModalityIssue,
  getFtmVehicleModalityIssue
} from '@/apis/fleet/ftm-vehicle-modality-issue'
const defaultFormData = {}
const defaultCalibrationParameterData = {
  version: 1.0,
  activationDate: dateUtils.formatDateNormal(new Date())
}
const defaultModalityData = {
  occurTime: dateUtils.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
}
export default {
  name: 'checkPwd',
  emits: ['reload'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      dialogTitle: '',
      $t: i18n.global.t,
      //参数标定
      calibrationVisible: false,
      calibrationParamsPageData: {
        total: 0
      },
      calibrationParamsQueryParam: {
        current: 1,
        size: 10
      },
      calibrationParamsFormData: Object.assign(
        {},
        defaultCalibrationParameterData
      ),
      calibrationParamsFormVisible: false,
      vehicleId: '',
      calibrationParamsEditFormData: Object.assign({}, defaultFormData),
      calibrationParamsEditFormVisible: false,
      expandKeys: [],
      //文件
      downloadUrl: GLB_CONFIG.devUrl.fileServer + '/',
      headerObj: {
        token: util.getToken()
      },
      formRules: {
        supplierId: [
          {
            required: true,
            message: i18n.global.t('请选择'),
            trigger: 'change'
          }
        ],
        occurTime: [
          {
            required: true,
            message: i18n.global.t('请选择'),
            trigger: 'change'
          }
        ],
        description: [
          {
            required: true,
            message: i18n.global.t('请输入'),
            trigger: 'blur'
          }
        ]
      },
      filePostData: {
        sourceType: 'calibration_parameter_file'
      },
      fileList: [],
      editFileList: [],
      rowsIndex: '',
      dateUtils,
      modalityData: []
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
    ElDialog,
    ElForm,
    ElFormItem,
    ElButton,
    ElTable,
    ElTableColumn,
    ElRow,
    ElCol,
    ElButtonGroup,
    ElTooltip,
    ElInputNumber,
    ElDatePicker,
    ElUpload,
    ElPagination,
    ElOption,
    ElSelect,
    LtwInput,
    DictionarySelection,
    EmployeeSelection,
    LtwIcon
  },

  methods: {
    show(row) {
      this.dialogVisible = true
      // this.dialogStatus = row.type
      this.dialogTitle = i18n.global.t('传感器维护记录')
      this.calibrationParamsQueryParam.vehicleId = row.vehicleId
      this.calibrationParamsQueryParam.modality = row.modality
      this.calibrationParamsQuery()
      this.listSysRoleOrg()
      // switch (row.type) {
      //   case 'add':
      //     this.dialogTitle = i18n.global.t('新增车辆')
      //     break
      //   case 'edit':
      //     this.dialogTitle = i18n.global.t('编辑车辆')
      //     this.getBsVehicle(row.data)
      //     break
      //   case 'view':
      //     this.dialogTitle = i18n.global.t('车辆详情')
      //     this.getBsVehicle(row.data)
      //     break
      // }
      // this.userName = this.$store.state.permission.currentUser.userName
      // // console.log(row.data)
      // this.applicationId = row.applicationId
      // this.type = row.type
    },
    dialogClosed() {
      this.initForm()
    },
    cancel(val) {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    // initForm(id) {
    //   this.$refs.calibrationParamsFormRef.resetFields()
    //   this.form = { ...defaultform }
    // },
    // submit() {
    //   this.$refs.calibrationParamsEditFormDataRef.validate(valid => {
    //     if (!valid) return
    //     let postData = {
    //       vehicleId: this.calibrationParamsQueryParam.vehicleId,
    //       modality: this.calibrationParamsQueryParam.modality,
    //       ...this.form
    //     }
    //     if (this.dialogStatus === 'add') {
    //       saveBsVehicle(postData).then(() => {
    //         this.cancelCalibrationParamsForm()
    //       })
    //     }
    //     if (this.dialogStatus === 'edit') {
    //       updateBsVehicle(postData).then(() => {
    //         this.cancelCalibrationParamsForm()
    //       })
    //     }
    //   })
    // },
    getBsVehicle(row) {
      getBsVehicle(row.id).then(res => {
        // this.$nextTick(function () {
        this.form = res.data
        // })
        this.dialogVisible = true
      })
    },
    addCalibrationParams() {
      this.calibrationParamsFormVisible = true
    },

    //参数标定
    calibrationParamsQuery() {
      pageFtmVehicleModalityIssue(this.calibrationParamsQueryParam).then(
        res => {
          this.calibrationParamsPageData = res.data
          if (!this.calibrationParamsPageData.records) {
            this.calibrationParamsPageData.records = []
          }
          this.calibrationParamsPageData.records.forEach(item => {
            item.vehicleId = this.calibrationParamsQueryParam.vehicleId
            item.editing = true
            if (item.occurTime && item.finishTime) {
              item.repairDuration = this.getDistanceDays(
                item.occurTime,
                item.finishTime
              )
            }
          })
        }
      )
    },
    getDistanceDays(date1, date2) {
      // date1例如:'2022-03-05',date2例如:'2022-03-06'
      const date1_timeStamp = new Date(date1) - 0
      const date2_timeStamp = new Date(date2) - 0
      let max = '',
        min = ''
      if (date1_timeStamp > date2_timeStamp) {
        max = date1_timeStamp
        min = date2_timeStamp
      } else if (date1_timeStamp === date2_timeStamp) {
        return 1
      } else {
        max = date2_timeStamp
        min = date1_timeStamp
      }
      // 例如返回:'1'
      return (max - min) / (24 * 60 * 60 * 1000)
    },
    initForm() {
      // this.$refs.calibrationParamsEditFormRef.resetFields()
      // this.$refs.calibrationParamsFormRef.resetFields()
      this.calibrationParamsEditFormData = Object.assign({}, defaultFormData)
      this.calibrationParamsFormData = Object.assign(
        {},
        defaultCalibrationParameterData
      )
    },
    getRowKeys(row) {
      return row.id
    },
    saveCalibrationParams(row, index) {
      // this.calibrationParamsEditFormData.vehicleId =
      //   this.calibrationParamsQueryParam.vehicleId
      // // this.calibrationParamsEditFormData.fileId=this.editFileList.id
      // this.calibrationParamsEditFormData.sourceType =
      //   this.filePostData.sourceType

      this.$refs['calibrationParamsEditFormDataRef' + index].validate(valid => {
        if (valid) {
          updateFtmVehicleModalityIssue(
            this.calibrationParamsEditFormData
          ).then(res => {
            this.calibrationParamsEditFormVisible = false
            this.$refs.calibrationParamsRef.toggleRowExpansion(row, false)
            this.calibrationParamsQuery()
          })
        }
      })
      // this.editFileList = []
    },
    editCalibrationParams(row) {
      // this.rowsIndex = row.fileId
      this.calibrationParamsEditFormVisible = true
      row.editing = false
      // this.expandKeys = []
      // this.expandKeys.push(row.id)
      this.$refs.calibrationParamsRef.toggleRowExpansion(row, true)
      getFtmVehicleModalityIssue(row.id).then(res => {
        this.$nextTick(function () {
          this.calibrationParamsEditFormData = res.data
          // getSysFile(this.calibrationParamsEditFormData.fileId).then(r => {
          //   this.editFileList = []
          //   this.editFileList.push({
          //     name: r.data.fileName + '.' + r.data.fileType,
          //     id: this.calibrationParamsEditFormData.fileId,
          //     url: this.downloadUrl + this.calibrationParamsEditFormData.fileId
          //   })
          // })
        })
      })
    },
    singleRemoveCalibrationParams(row, index, rows) {
      showConfirmToast({
        message: BASE_CONSTANT.DELETE_CONFIRM_MSG
      }).then(() => {
        if (row.id) {
          deleteFtmVehicleModalityIssue({
            id: row.id,
            vehicleId: row.vehicleId
          }).then(() => {
            // rows.splice(index, 1)
            this.calibrationParamsFormData = Object.assign(
              {},
              defaultCalibrationParameterData
            )
            this.calibrationParamsQuery()
            // rows.splice(index, 1)
          })
        } else {
          rows.splice(index, 1)
        }
      })
    },
    //文件
    handlePreview() {},
    handleRemove() {
      this.fileList = []
    },
    // beforeEditUpload(file, props) {
    //   let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
    //   console.log(testmsg)
    //   if (!(testmsg === 'xml') && !(testmsg === 'json')) {
    //     this.$message({
    //       message: '上传文件只能是xml或者json格式',
    //       type: 'warning'
    //     })
    //     reject()
    //   }
    // },

    // handleEditUpload(param) {
    //   // if(param.file){
    //   let formData = new FormData()
    //   formData.append('sourceType', this.filePostData.sourceType) // 额外参数
    //   formData.append('file', param.file)
    //   // deleteFile(param)
    //   // this.$refs['uploadEditRef' + this.rowsIndex].clearFiles()
    //   deleteFile(this.rowsIndex).then(res => {})
    //   return uploadFile(formData).then(res => {
    //     this.editFileList = []
    //     this.calibrationParamsEditFormData.fileId = res.data.id
    //     this.editFileList.push({
    //       name: res.data.fileName + '.' + res.data.fileType,
    //       id: this.calibrationParamsEditFormData.fileId,
    //       url: this.downloadUrl + this.calibrationParamsEditFormData.fileId
    //     })
    //   })
    //   // }
    // },
    // beforeUpload(file) {
    //   console.log(file)
    //   let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
    //   console.log(testmsg)
    //   if (!(testmsg === 'xml') && !(testmsg === 'json')) {
    //     this.$message({
    //       message: '上传文件只能是xml或者json格式',
    //       type: 'warning'
    //     })
    //     reject()
    //   }
    // },
    // handleUpload(param) {
    //   if (param.file) {
    //     let formData = new FormData()
    //     formData.append('sourceType', this.filePostData.sourceType) // 额外参数
    //     formData.append('file', param.file)
    //     return uploadFile(formData).then(res => {
    //       this.calibrationParamsFormData.fileId = res.data.id
    //       // this.fileList.push(
    //       //     {
    //       //         name: res.data.fileName+'.'+res.data.fileType,
    //       //         id: this.calibrationParamsFormData.fileId,
    //       //         url: this.downloadUrl + this.calibrationParamsFormData.fileId
    //       //     }
    //       // )
    //     })
    //   }
    //   this.$refs.uploadRef.clearFiles()
    // },
    handleFileChange(file, fileList) {
      // this.fileList = fileList;
      // if (this.fileList.length > 1) {
      //   this.fileList.splice(0, 1);
      // }
    },
    handleEditFileChange(file, fileList) {
      // this.editFileList = fileList;
      // if (this.editFileList.length > 1) {
      //   this.editFileList.splice(0, 1);
      // }
    },
    confirm() {
      this.$refs.calibrationParamsFormRef.validate(valid => {
        if (valid) {
          let postData = {
            vehicleId: this.calibrationParamsQueryParam.vehicleId,
            modality: this.calibrationParamsQueryParam.modality,
            ...this.calibrationParamsFormData
          }
          // this.calibrationParamsFormData.vehicleId =
          //   this.calibrationParamsQueryParam.vehicleId
          // this.calibrationParamsFormData.sourceType = this.filePostData.sourceType
          saveFtmVehicleModalityIssue(postData).then(() => {
            this.calibrationParamsFormVisible = false
            ;(this.calibrationParamsFormData = Object.assign(
              {},
              defaultCalibrationParameterData
            )),
              this.calibrationParamsQuery()
          })
        }
      })
    },
    // downloadCalibrationParams(row) {
    //   window.location.href =
    //     this.downloadUrl +
    //     row.fileId +
    //     '?token=' +
    //     util.getToken()
    // },
    handlecalibrationSizeChange(value) {
      this.calibrationParamsQueryParam.size = value
      this.calibrationParamsQuery()
    },
    handlecalibrationCurrentChange(value) {
      this.calibrationParamsQueryParam.current = value
      this.calibrationParamsQuery()
    },
    cancelCalibrationParamsForm() {
      this.calibrationParamsFormVisible = false
      // this.fileList = []
    },
    disabledOccurTime(val) {
      let nowDay = dateUtils.parseTime(
        new Date().getTime(),
        '{y}-{m}-{d}' + ' 00:00:00'
      )
      if (this.calibrationParamsFormData.modality) {
        let item = this.calibrationParamsPageData.records.find(
          val => val.modality === this.calibrationParamsFormData.modality
        )
        if (item) {
          return (
            new Date(val) > new Date(nowDay).getTime() ||
            new Date(val) <
              new Date(
                dateUtils.parseTime(
                  new Date(item.occurTime).getTime(),
                  '{y}-{m}-{d}' + ' 00:00:00'
                )
              ).getTime()
          )
        }
      }
      return new Date(val) > new Date(nowDay).getTime()
    },
    disabledFinishTime(val) {
      let nowDay = dateUtils.parseTime(
        new Date().getTime(),
        '{y}-{m}-{d}' + ' 00:00:00'
      )
      return (
        new Date(val) > new Date(nowDay).getTime() ||
        new Date(val) <
          new Date(this.calibrationParamsEditFormData.occurTime).getTime()
      )
    },
    listSysRoleOrg() {
      if (!(this.modalityData && this.modalityData?.length)) {
        listSysRoleOrg({
          tagCode: 'org_supplier'
        }).then(res => {
          this.modalityData = res.data
        })
      }
    },
    getSupplierName(val) {
      let supplierItem = this.modalityData.find(item => item.id === val)
      if (this.calibrationParamsEditFormVisible) {
        this.calibrationParamsEditFormData.supplierName = supplierItem.tagName
      } else {
        this.calibrationParamsFormData.supplierName = supplierItem.tagName
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
  .user-name {
    color: #409eff;
  }
}
:deep(.el-table__expand-icon) {
  visibility: hidden;
}
</style>
