<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="50%"
    @close="dialogClosed"
    @open="dialogOpened"
    append-to-body
    :destroy-on-close="true"
  >

    <el-form
        :inline="true"
        class="form-record"
        :model="formDataRecord"
        :rules="formRulesRecord"
        ref="formRef"
        label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('生效日期')" prop="startDate">
            <el-date-picker
                v-model="formDataRecord.startDate"
                type="date"
                :disabled-date="disabledStartDate"
                :placeholder="$t('开始日期')"
                value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('结束日期')" prop="endDate">
            <el-date-picker
                v-model="formDataRecord.endDate"
                type="date"
                :disabled-date="disabledEndDate"
                :placeholder="$t('结束日期')"
                value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" id="view-cancel">{{ $t('关闭') }}</el-button>
        <el-button type="primary" @click="submit" id="view-submit">{{ $t('确认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {dateUtils, downloadFile, showToast} from "@/plugins/util";
import {exportSpareReport} from "@/apis/fleet/ftm-spare-part";

const defaultFormData = {}
export default {
  name: 'ExportParkingLot',
  emits: ['reload', 'cancel'],
  data() {
    return {
      formRulesRecord: {
      },
      //新增车型版本
      formDataRecord: {},
      file:{},
      dialogStatus: '',
      objectValue: {},
      dialogVisible: false,
      dialogTitle: ''
    }
  },
  computed: {
    formReadonly() {
      return this.dialogStatus === 'view'
    }
  },
  components: {
  },
  created() {},
  methods: {
    disabledEndDate(val) {
      if (this.formDataRecord.startDate) {
        return new Date(val) < new Date(this.formDataRecord.startDate).getTime() - 1000 * 60 * 60 * 24
      }
    },
    disabledStartDate(val) {
      if (this.formDataRecord.endDate) {
        return new Date(val) > new Date(this.formDataRecord.endDate).getTime()
      }
    },
    show(row) {
      this.dialogVisible = true
      this.dialogTitle = this.$t('导出对手件')
    },
    dialogClosed() {
      this.initForm()
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('reload')
    },
    dialogOpened() {},
    submit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          exportSpareReport(this.formDataRecord).then(res => {
            if (res.type === 'application/json') {
              this.$message.warning({
                message: '暂无数据可导出!',
                type: 'warning'
              })
              return
            }
            const fileName = "对手件数据" + dateUtils.formatDateNormal(new Date())
            downloadFile(res, fileName)
          })
        }
      })
    },
    initForm() {
      this.form = Object.assign({}, defaultFormData)
    }
  }
}
</script>

