<template>
    <div>
        <el-card>
            <div class="ltw-toolbar">
                <div class="ltw-search-container ltw-tool-container">
                    <ltw-input placeholder="请输入关键字" v-model="queryParam.key" clearable @clear="refresh">
                        <template #append>
                                                        <el-button @click="refresh">
                                <ltw-icon icon-code="el-icon-search"></ltw-icon>
                            </el-button>
                        </template>
                    </ltw-input>
                </div>
                <div class="ltw-tool-container button-group">
                    <el-button
                        :type="item.buttonStyleType"
                        :key="item.id"
                        v-for="item in outlineFunctionList"
                        @click="executeButtonMethod(item.buttonCode)"
                    >
                        <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>

                        {{ item.name }}
                    </el-button>                    <el-dropdown @command="handleCommand" class="batch-operate-btn"
                                 v-if="batchingFunctionList && batchingFunctionList.length>0">
                        <el-button type="primary">
                            批量操作
                            <ltw-icon icon-code="el-icon-arrow-down" class="el-icon--right"></ltw-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    :key="item.id"
                                    v-for="item in batchingFunctionList"
                                    :command="item.buttonCode"
                                >
                                <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon>
                                    {{item.name}}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
            <el-table :data="pageData.records"  @selection-change="handleSelectionChange" row-key="id" ref="tableRef">
                <el-table-column header-align="left" align="left" type="selection" width="55"></el-table-column>
                                <el-table-column header-align="left" align="left" prop="sendType" label="发送类型"></el-table-column>
                <el-table-column header-align="left" align="left" prop="categoryCode" label="分类编码"></el-table-column>
                <el-table-column header-align="left" align="left" prop="title" label="标题"></el-table-column>
                <el-table-column header-align="left" align="left" prop="content" label="内容"></el-table-column>
                <el-table-column header-align="left" align="left" prop="sendUserId" label="推送用户id"></el-table-column>
                <el-table-column header-align="left" align="left" prop="receiveUserId" label="接收用户id"></el-table-column>
                <el-table-column header-align="left" align="left" prop="paramValue" label="携带参数"></el-table-column>
                <el-table-column header-align="left" align="left" prop="hasRead" label="是否已读"></el-table-column>
                <el-table-column header-align="left" align="left" prop="bisId" label="业务id"></el-table-column>
                <el-table-column header-align="left" align="left" prop="sendStatus" label="信息发送状态"></el-table-column>
                <el-table-column header-align="left" align="left" prop="executed" label="是否已经执行"></el-table-column>
                <el-table-column header-align="left" align="left" prop="executeUserId" label="执行用户id"></el-table-column>
                <el-table-column header-align="left" align="left" prop="createUser" label="创建人"></el-table-column>
                <el-table-column header-align="left" align="left" prop="updateUser" label="更新人"></el-table-column>
                <el-table-column header-align="left" align="left" label="操作"  min-width="180">
                    <template #default="scope">
                        <el-button-group>
                            <el-tooltip :key="item.id" v-for="item in inlineFunctionList" effect="dark" :content="item.name" placement="top" :enterable="false"
                            >
                                 <el-button
                                    :type="item.buttonStyleType"
                                   
                                    @click="executeButtonMethod(item.buttonCode, scope.row)"
                                >
                                    <ltw-icon :icon-code="item.buttonIconCode"></ltw-icon></el-button>
                            </el-tooltip>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="queryParam.current"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="queryParam.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageData.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="50%" @close="dialogClosed" @open="dialogOpened">
            <el-form :model="formData" :rules="formRules" ref="formRef"
                     label-width="100px">
                <el-form-item label="发送类型" prop="sendType">
                    <ltw-input v-model="formData.sendType" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="分类编码" prop="categoryCode">
                    <ltw-input v-model="formData.categoryCode" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="标题" prop="title">
                    <ltw-input v-model="formData.title" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="内容" prop="content">
                    <ltw-input v-model="formData.content" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="推送用户id" prop="sendUserId">
                    <ltw-input v-model="formData.sendUserId" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="接收用户id" prop="receiveUserId">
                    <ltw-input v-model="formData.receiveUserId" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="携带参数" prop="paramValue">
                    <ltw-input v-model="formData.paramValue" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否已读" prop="hasRead">
                    <ltw-input v-model="formData.hasRead" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="业务id" prop="bisId">
                    <ltw-input v-model="formData.bisId" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="信息发送状态" prop="sendStatus">
                    <ltw-input v-model="formData.sendStatus" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="是否已经执行" prop="executed">
                    <ltw-input v-model="formData.executed" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="执行用户id" prop="executeUserId">
                    <ltw-input v-model="formData.executeUserId" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="创建人" prop="createUser">
                    <ltw-input v-model="formData.createUser" :disabled="formReadonly"></ltw-input>
                </el-form-item>
                <el-form-item label="更新人" prop="updateUser">
                    <ltw-input v-model="formData.updateUser" :disabled="formReadonly"></ltw-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                  <el-button @click="dialogVisible = false" v-if=" dialogStatus === 'view'">关 闭</el-button>
                    <template v-else>
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">保 存</el-button>
                    </template>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import {
        saveSysNotice,
        updateSysNotice,
        deleteSysNotice,
        pageSysNotice,
        getSysNotice
    } from '@/apis/system/sys-notice'
    

const defaultFormData = {}
    export default {
        name: "SysNotice",
        data() {
            return {
                batchingFunctionList:[],
                inlineFunctionList:[],
                outlineFunctionList:[],
                pageData: {
                total:0
            },
                queryParam: {
                    current: 1,
                    size: 10
                },
                dialogVisible: false,
                formData: Object.assign({},defaultFormData),
                formRules: {
                    sendType: [
                        {required: true, message: '请输入发送类型', trigger: 'blur'}
                    ],
                    categoryCode: [
                        {required: true, message: '请输入分类编码', trigger: 'blur'}
                    ],
                    title: [
                        {required: true, message: '请输入标题', trigger: 'blur'}
                    ],
                    content: [
                        {required: true, message: '请输入内容', trigger: 'blur'}
                    ],
                    sendUserId: [
                        {required: true, message: '请输入推送用户id', trigger: 'blur'}
                    ],
                    receiveUserId: [
                        {required: true, message: '请输入接收用户id', trigger: 'blur'}
                    ],
                    paramValue: [
                        {required: true, message: '请输入携带参数', trigger: 'blur'}
                    ],
                    hasRead: [
                        {required: true, message: '请输入是否已读', trigger: 'blur'}
                    ],
                    bisId: [
                        {required: true, message: '请输入业务id', trigger: 'blur'}
                    ],
                    sendStatus: [
                        {required: true, message: '请输入信息发送状态', trigger: 'blur'}
                    ],
                    executed: [
                        {required: true, message: '请输入是否已经执行', trigger: 'blur'}
                    ],
                    executeUserId: [
                        {required: true, message: '请输入执行用户id', trigger: 'blur'}
                    ],
                    createUser: [
                        {required: true, message: '请输入创建人', trigger: 'blur'}
                    ],
                    updateUser: [
                        {required: true, message: '请输入更新人', trigger: 'blur'}
                    ]
                },
                dialogTitle: '',
                dialogStatus: '',
                selectedData:[]
            }
        },
        created() {
            if(this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path]){
                this.batchingFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].batchingFunctionList
                this.inlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].inlineFunctionList
                this.outlineFunctionList = this.$store.state.permission.routerFunctionMap[this.$router.currentRoute.value.path].outlineFunctionList
            }
            this.query()
        },
        computed: {
            formReadonly() {
                return this.dialogStatus === 'view'
            }
        },
        methods: {
            executeButtonMethod(funcName,row){
                this[funcName](row)
            },
            refresh(){
                this.$refs.tableRef.clearSelection()
                this.query()
            },
            query() {
                pageSysNotice(this.queryParam).then(
                    res => {
                        this.pageData = res.data
                    }
                )
            },
            add() {
                this.dialogTitle = '添加通知'
                this.dialogStatus = 'add'
                this.dialogVisible = true
            },
            save() {
                this.$refs.formRef.validate(
                    valid => {
                        if (!valid) return
                        if (this.dialogStatus === 'add') {
                            saveSysNotice(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                        if (this.dialogStatus === 'edit') {
                            updateSysNotice(this.formData).then(
                                () => {
                                this.dialogVisible = false
                                this.query()
                            }
                        )
                        }
                    }
                )
            },
            edit(row) {
                this.dialogTitle = '修改通知'
                this.dialogStatus = 'edit'
                getSysNotice(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            view(row) {
                this.dialogTitle = '查看通知'
                this.dialogStatus = 'view'
                getSysNotice(row.id).then(
                    res => {
                        this.$nextTick(function () {
                            this.formData = res.data
                        })
                        this.dialogVisible = true
                }
                )
            },
            handleCommand(command){
                if(this.selectedData.length === 0){
                    this.$message.warning({
                        message: '请先选择数据再执行批量操作',
                        type: 'warning'
                    })
                    return
                }
                if(command === 'batchRemove'){
                    this.batchRemove()
                }
            },
            singleRemove(row) {
                this.remove({id:row.id})
            },
            batchRemove(){
                let idList = [];
                this.selectedData.forEach(ele=>{
                    idList.push(ele.id)
                })
                let ids = idList.join(',')
                this.remove({ids})
            },
            remove(param){
                let msg = '此操作将永久删除选中数据，是否继续?'
                this.$confirm(msg, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    deleteSysNotice(param).then(
                        ()=>{
                            this.query()
                        }
                    )
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message:'已取消删除'
                    })
                })
            },
            handleSizeChange(value) {
                this.queryParam.size = value
                this.query()
            },
            handleCurrentChange(value) {
                this.queryParam.current = value
                this.query()
            },
            dialogOpened(){

            },
            dialogClosed() {
                this.initForm()
            },
            handleSelectionChange(value){
                this.selectedData = value
            },
            initForm(){
                this.$refs.formRef.resetFields()
                this.formData = Object.assign({},defaultFormData)
            }

        }
    }
</script>
